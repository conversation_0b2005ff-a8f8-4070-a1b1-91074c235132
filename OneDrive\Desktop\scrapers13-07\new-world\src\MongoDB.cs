using MongoDB.Driver;
using MongoDB.Bson;
using MongoDB.Driver.GridFS;
using Microsoft.Extensions.Configuration;
using static Scraper.Utilities;

namespace Scraper
{
    public static class MongoDB
    {
        private static IMongoClient? _client;
        private static IMongoDatabase? _database;
        private static IGridFSBucket? _gridFS;
        private static ObjectId? _storeId;
        
        // Collections
        private static IMongoCollection<BsonDocument>? _storesCollection;
        private static IMongoCollection<BsonDocument>? _brandsCollection;
        private static IMongoCollection<BsonDocument>? _consolidatedProductsCollection;
        private static IMongoCollection<BsonDocument>? _priceHistoryCollection;
        private static IMongoCollection<BsonDocument>? _categoryHierarchyCollection;

        public static void Initialise(IConfiguration config)
        {
            var connectionString = config.GetSection("MONGODB_CONNECTION_STRING").Get<string>();
            var databaseName = config.GetSection("MONGODB_DATABASE_NAME").Get<string>() ?? "nz-supermarket-scraper";
            
            if (string.IsNullOrWhiteSpace(connectionString))
                throw new Exception("MONGODB_CONNECTION_STRING not configured");

            try
            {
                _client = new MongoClient(connectionString);
                _database = _client.GetDatabase(databaseName);
                
                // Initialize collections
                _storesCollection = _database.GetCollection<BsonDocument>("stores");
                _brandsCollection = _database.GetCollection<BsonDocument>("brands");
                _consolidatedProductsCollection = _database.GetCollection<BsonDocument>("consolidatedProducts");
                _priceHistoryCollection = _database.GetCollection<BsonDocument>("priceHistory");
                _categoryHierarchyCollection = _database.GetCollection<BsonDocument>("categoryHierarchy");
                
                // Initialize GridFS for image storage
                _gridFS = new GridFSBucket(_database, new GridFSBucketOptions
                {
                    BucketName = "productImages"
                });
                
                // Create indexes for performance
                CreateIndexes();
                
                Utilities.Log("✅ MongoDB connection established", ConsoleColor.Green);
            }
            catch (Exception ex)
            {
                LogError($"Failed to connect to MongoDB: {ex.Message}");
                throw;
            }
        }

        private static void CreateIndexes()
        {
            try
            {
                // Consolidated products indexes
                var consolidatedIndexes = new[]
                {
                    new CreateIndexModel<BsonDocument>(
                        Builders<BsonDocument>.IndexKeys.Text("displayName").Text("normalizedName").Text("variants.storeName")
                    ),
                    new CreateIndexModel<BsonDocument>(
                        Builders<BsonDocument>.IndexKeys.Ascending("categoryId")
                    ),
                    new CreateIndexModel<BsonDocument>(
                        Builders<BsonDocument>.IndexKeys.Ascending("brandId")
                    ),
                    new CreateIndexModel<BsonDocument>(
                        Builders<BsonDocument>.IndexKeys.Ascending("variants.storeProductId").Ascending("variants.storeId")
                    )
                };
                _consolidatedProductsCollection?.Indexes.CreateMany(consolidatedIndexes);
                
                // Price history indexes
                var priceIndexes = new[]
                {
                    new CreateIndexModel<BsonDocument>(
                        Builders<BsonDocument>.IndexKeys.Ascending("consolidatedProductId").Descending("recordedAt")
                    ),
                    new CreateIndexModel<BsonDocument>(
                        Builders<BsonDocument>.IndexKeys.Ascending("year").Ascending("month")
                    )
                };
                _priceHistoryCollection?.Indexes.CreateMany(priceIndexes);
                
                Utilities.Log("✅ MongoDB indexes created", ConsoleColor.Blue);
            }
            catch (Exception ex)
            {
                LogError($"Failed to create indexes: {ex.Message}");
            }
        }

        public enum UpsertResponse { NewProduct, PriceUpdated, InfoUpdated, AlreadyUpToDate, Failed }

        public static async Task<ObjectId> EnsureStoreRow(string name)
        {
            if (_storeId != null) return _storeId.Value;
            
            if (_storesCollection == null) throw new Exception("MongoDB not initialised");
            
            try
            {
                var filter = Builders<BsonDocument>.Filter.Eq("storeId", name.ToLower());
                var existingStore = await _storesCollection.Find(filter).FirstOrDefaultAsync();
                
                if (existingStore != null)
                {
                    _storeId = existingStore["_id"].AsObjectId;
                    return _storeId.Value;
                }

                // Insert new store
                var newStore = new BsonDocument
                {
                    { "storeId", name.ToLower() },
                    { "name", name },
                    { "logoUrl", BsonNull.Value },
                    { "createdAt", DateTime.UtcNow },
                    { "updatedAt", DateTime.UtcNow }
                };
                
                await _storesCollection.InsertOneAsync(newStore);
                _storeId = newStore["_id"].AsObjectId;
                return _storeId.Value;
            }
            catch (Exception ex)
            {
                LogError($"Failed to ensure store row: {ex.Message}");
                throw;
            }
        }

        public static async Task<UpsertResponse> UpsertProduct(Program.Product p, ObjectId storeId)
        {
            if (_consolidatedProductsCollection == null || _priceHistoryCollection == null)
                throw new Exception("MongoDB not initialised");

            try
            {
                var now = DateTime.UtcNow;
                
                // Find or create consolidated product
                var filter = Builders<BsonDocument>.Filter.And(
                    Builders<BsonDocument>.Filter.Eq("variants.storeProductId", p.id),
                    Builders<BsonDocument>.Filter.Eq("variants.storeId", storeId)
                );
                
                var existingProduct = await _consolidatedProductsCollection.Find(filter).FirstOrDefaultAsync();
                bool isNewProduct = false;
                ObjectId consolidatedProductId;
                
                if (existingProduct == null)
                {
                    // Try to find matching consolidated product using enhanced algorithm
                    var normalizedName = NormalizeProductName(p.name, p.size);
                    Utilities.Log($"🔍 Processing: {p.name} -> normalized: {normalizedName}", ConsoleColor.Cyan);

                    var matchingProduct = await FindBestMatch(normalizedName, p.size, p.name);

                    if (matchingProduct != null)
                    {
                        // Found a match - add this as a new variant and add alias
                        consolidatedProductId = matchingProduct["_id"].AsObjectId;

                        // Add current product name as alias if different
                        await AddProductAlias(consolidatedProductId, p.name);

                        // Add new variant to existing consolidated product
                        var newVariant = new BsonDocument {
                            { "storeProductId", p.id },
                            { "storeId", storeId },
                            { "storeName", p.name },
                            { "storeSize", string.IsNullOrEmpty(p.size) ? BsonNull.Value : (BsonValue)p.size },
                            { "storeUnitPrice", p.unitPrice },
                            { "storeUnitName", string.IsNullOrEmpty(p.unitName) ? BsonNull.Value : (BsonValue)p.unitName },
                            { "lastSeen", now },
                            { "isActive", true },
                            { "imageUrl", BsonNull.Value }
                        };

                        var variantUpdate = Builders<BsonDocument>.Update
                            .Push("variants", newVariant)
                            .Set("updatedAt", now);

                        await _consolidatedProductsCollection.UpdateOneAsync(
                            Builders<BsonDocument>.Filter.Eq("_id", consolidatedProductId),
                            variantUpdate);

                        Utilities.Log($"✅ Added variant to existing product: {matchingProduct["displayName"]}", ConsoleColor.Green);
                    }
                    else
                    {
                        // Create new consolidated product
                        var newProduct = new BsonDocument
                        {
                            { "normalizedName", normalizedName },
                            { "displayName", p.name },
                            { "primarySize", string.IsNullOrEmpty(p.size) ? BsonNull.Value : (BsonValue)p.size },
                            { "categoryId", BsonNull.Value }, // TODO: Implement category mapping
                            { "brandId", BsonNull.Value }, // TODO: Implement brand extraction
                            { "matchConfidence", 100 },
                            { "manualMatch", false },
                            { "aliases", new BsonArray() }, // Initialize empty aliases array
                            { "variants", new BsonArray {
                                new BsonDocument {
                                    { "storeProductId", p.id },
                                    { "storeId", storeId },
                                    { "storeName", p.name },
                                    { "storeSize", string.IsNullOrEmpty(p.size) ? BsonNull.Value : (BsonValue)p.size },
                                    { "storeUnitPrice", p.unitPrice },
                                    { "storeUnitName", string.IsNullOrEmpty(p.unitName) ? BsonNull.Value : (BsonValue)p.unitName },
                                    { "lastSeen", now },
                                    { "isActive", true },
                                    { "imageUrl", BsonNull.Value }
                                }
                            }},
                            { "sizeVariants", !string.IsNullOrEmpty(p.size) ? new BsonArray {
                                new BsonDocument {
                                    { "sizeName", p.size },
                                    { "sizeWeightGrams", BsonNull.Value },
                                    { "sizeVolumeMl", BsonNull.Value },
                                    { "isPrimarySize", true }
                                }
                            } : new BsonArray()},
                            { "currentPrices", new BsonArray {
                                new BsonDocument {
                                    { "storeId", storeId },
                                    { "price", p.currentPrice },
                                    { "isSpecial", false },
                                    { "wasAvailable", true },
                                    { "recordedAt", now }
                                }
                            }},
                            { "createdAt", now },
                            { "updatedAt", now }
                        };

                        await _consolidatedProductsCollection.InsertOneAsync(newProduct);
                        consolidatedProductId = newProduct["_id"].AsObjectId;
                        isNewProduct = true;

                        Utilities.Log($"🆕 Created new consolidated product: {p.name}", ConsoleColor.Blue);
                    }
                }
                else
                {
                    // Update existing consolidated product
                    consolidatedProductId = existingProduct["_id"].AsObjectId;
                    
                    var variantFilter = Builders<BsonDocument>.Filter.And(
                        Builders<BsonDocument>.Filter.Eq("_id", consolidatedProductId),
                        Builders<BsonDocument>.Filter.ElemMatch("variants", 
                            Builders<BsonDocument>.Filter.Eq("storeProductId", p.id))
                    );
                    
                    var variantUpdate = Builders<BsonDocument>.Update
                        .Set("variants.$.lastSeen", now)
                        .Set("variants.$.storeUnitPrice", p.unitPrice)
                        .Set("variants.$.storeUnitName", string.IsNullOrEmpty(p.unitName) ? BsonNull.Value : (BsonValue)p.unitName)
                        .Set("updatedAt", now);
                    
                    await _consolidatedProductsCollection.UpdateOneAsync(variantFilter, variantUpdate);
                    
                    // Update current price
                    var priceFilter = Builders<BsonDocument>.Filter.And(
                        Builders<BsonDocument>.Filter.Eq("_id", consolidatedProductId),
                        Builders<BsonDocument>.Filter.ElemMatch("currentPrices", 
                            Builders<BsonDocument>.Filter.Eq("storeId", storeId))
                    );
                    
                    var priceUpdate = Builders<BsonDocument>.Update
                        .Set("currentPrices.$.price", p.currentPrice)
                        .Set("currentPrices.$.recordedAt", now);
                    
                    await _consolidatedProductsCollection.UpdateOneAsync(priceFilter, priceUpdate);
                }
                
                // Insert price history record
                var priceHistoryDoc = new BsonDocument
                {
                    { "consolidatedProductId", consolidatedProductId },
                    { "storeId", storeId },
                    { "price", p.currentPrice },
                    { "isSpecial", false },
                    { "wasAvailable", true },
                    { "recordedAt", now },
                    { "year", now.Year },
                    { "month", now.Month }
                };
                
                await _priceHistoryCollection.InsertOneAsync(priceHistoryDoc);
                
                Utilities.Log($"  Upserted: {p.name.Substring(0, Math.Min(45, p.name.Length)).PadRight(45)} | ${p.currentPrice}", ConsoleColor.Green);
                
                return isNewProduct ? UpsertResponse.NewProduct : UpsertResponse.PriceUpdated;
            }
            catch (Exception ex)
            {
                LogError($"MongoDB upsert failed: {ex.Message}");
                return UpsertResponse.Failed;
            }
        }

        // Upload product image to MongoDB GridFS
        public static async Task<bool> UploadImageToGridFS(string imageUrl, Program.Product product, ObjectId storeId)
        {
            if (_gridFS == null || _consolidatedProductsCollection == null)
            {
                LogError("MongoDB GridFS or consolidated products collection not initialised");
                return false;
            }

            try
            {
                // Download image
                using var httpClient = new HttpClient();
                var imageResponse = await httpClient.GetAsync(imageUrl);
                
                if (!imageResponse.IsSuccessStatusCode)
                {
                    LogError($"Failed to download image from {imageUrl}: {imageResponse.StatusCode}");
                    return false;
                }

                var imageBytes = await imageResponse.Content.ReadAsByteArrayAsync();
                
                // Check if image already exists and delete it
                var existingFiles = await _gridFS.FindAsync(
                    Builders<GridFSFileInfo>.Filter.Eq("metadata.productId", product.id));
                
                await existingFiles.ForEachAsync(async file =>
                {
                    await _gridFS.DeleteAsync(file.Id);
                });
                
                // Upload new image
                var filename = $"{product.id}.jpg";
                var metadata = new BsonDocument
                {
                    { "productId", product.id },
                    { "storeId", storeId },
                    { "originalUrl", imageUrl },
                    { "uploadedAt", DateTime.UtcNow }
                };
                
                var options = new GridFSUploadOptions
                {
                    Metadata = metadata
                };
                
                var fileId = await _gridFS.UploadFromBytesAsync(filename, imageBytes, options);
                
                // Update product with image URL reference
                var imageUrlGridFS = $"gridfs://productImages/{fileId}";
                
                var filter = Builders<BsonDocument>.Filter.ElemMatch("variants", 
                    Builders<BsonDocument>.Filter.Eq("storeProductId", product.id));
                var update = Builders<BsonDocument>.Update.Set("variants.$.imageUrl", imageUrlGridFS);
                
                await _consolidatedProductsCollection.UpdateOneAsync(filter, update);
                
                Utilities.Log($"  Image uploaded: {product.id} -> GridFS {fileId}", ConsoleColor.Blue);
                return true;
            }
            catch (Exception ex)
            {
                LogError($"Image upload error: {ex.Message}");
                return false;
            }
        }

        // Enhanced product name normalization with manual mappings
        private static string NormalizeProductName(string name, string? size = null)
        {
            if (string.IsNullOrEmpty(name)) return "";

            var normalized = name.ToLower()
                .Replace("'", "")
                .Replace("\"", "")
                .Replace("-", " ")
                .Replace("  ", " ")
                .Trim();

            // Remove common store descriptors
            var descriptors = new[] { "new world", "countdown", "woolworths", "paknsave", "pak n save",
                                    "select", "premium", "value", "budget", "signature", "essentials",
                                    "pams", "homebrand", "signature range", "fresh choice" };

            foreach (var descriptor in descriptors)
            {
                normalized = normalized.Replace(descriptor, "").Trim();
            }

            // Apply comprehensive NZ brand mappings
            var nzBrandMappings = GetNZBrandMappings();

            // Apply brand mappings
            foreach (var mapping in nzBrandMappings)
            {
                if (normalized.Contains(mapping.Key))
                {
                    normalized = normalized.Replace(mapping.Key, mapping.Key);
                    break; // Use first match to avoid multiple replacements
                }

                // Check variations
                foreach (var variation in mapping.Value)
                {
                    if (normalized.Contains(variation))
                    {
                        normalized = normalized.Replace(variation, mapping.Key);
                        break;
                    }
                }
            }

            // Include size if provided
            if (!string.IsNullOrEmpty(size))
            {
                var normalizedSize = size.ToLower()
                    .Replace("grams", "g")
                    .Replace("gram", "g")
                    .Replace("kilograms", "kg")
                    .Replace("kilogram", "kg")
                    .Replace("litres", "l")
                    .Replace("litre", "l")
                    .Replace("millilitres", "ml")
                    .Replace("millilitre", "ml");
                normalized += $" {normalizedSize}";
            }

            return normalized.Trim();
        }

        // Get comprehensive NZ brand mappings
        private static Dictionary<string, string[]> GetNZBrandMappings()
        {
            return new Dictionary<string, string[]>
            {
                // Supermarket Private Labels
                { "pams", new[] { "pam", "pams brand", "pams select" } },
                { "essentials", new[] { "essentials brand", "countdown essentials" } },
                { "homebrand", new[] { "home brand", "homebrand select" } },
                { "signature", new[] { "signature range", "signature brand" } },

                // Dairy Brands
                { "anchor", new[] { "anchor brand", "anchor dairy", "anchor milk", "anchor butter" } },
                { "mainland", new[] { "mainland cheese", "mainland dairy", "mainland brand" } },
                { "meadowfresh", new[] { "meadow fresh", "meadowfresh milk", "meadow fresh milk" } },
                { "lewis road", new[] { "lewis road creamery", "lewis road milk", "lewis road butter" } },
                { "kapiti", new[] { "kapiti cheese", "kapiti ice cream", "kapiti brand" } },

                // Meat Brands
                { "tegel", new[] { "tegel chicken", "tegel poultry", "tegel brand" } },
                { "inghams", new[] { "ingham", "inghams chicken", "inghams poultry" } },
                { "hellers", new[] { "heller", "hellers bacon", "hellers sausages" } },
                { "beehive", new[] { "beehive bacon", "beehive ham", "beehive smallgoods" } },

                // Bread Brands
                { "tip top", new[] { "tiptop", "tip top bread", "tiptop bread" } },
                { "molenberg", new[] { "molenberg bread", "molenburg", "molenberg wholemeal" } },
                { "vogels", new[] { "vogel", "vogels bread", "vogel bread", "vogels original" } },
                { "freyas", new[] { "freya", "freyas bread", "freya bread" } },

                // Beverage Brands
                { "coke", new[] { "coca cola", "coca-cola", "coke classic", "coca cola classic" } },
                { "coke zero", new[] { "coca cola zero", "coke zero sugar", "coca cola zero sugar" } },
                { "pepsi", new[] { "pepsi cola", "pepsi classic", "pepsi original" } },
                { "l&p", new[] { "lemon paeroa", "lemon and paeroa", "l and p" } },

                // Cereal Brands
                { "sanitarium", new[] { "sanitarium weetbix", "sanitarium so good" } },
                { "weetbix", new[] { "weet bix", "wheat biscuits", "sanitarium weetbix" } },
                { "uncle tobys", new[] { "uncle toby", "uncle tobys oats", "uncle tobys muesli" } },
                { "kelloggs", new[] { "kellogg", "kelloggs cornflakes", "kelloggs special k" } },

                // Pantry Brands
                { "watties", new[] { "wattie", "watties tomatoes", "watties sauce" } },
                { "heinz", new[] { "heinz beans", "heinz tomato sauce", "heinz soup" } },
                { "mccains", new[] { "mccain", "mccains chips", "mccains frozen" } },
                { "maggi", new[] { "maggi noodles", "maggi soup", "maggi instant" } },

                // Snack Brands
                { "cadbury", new[] { "cadburys", "cadbury chocolate" } },
                { "whittakers", new[] { "whittaker", "whittakers chocolate" } },
                { "bluebird", new[] { "bluebird chips", "bluebird snacks" } },
                { "eta", new[] { "eta chips", "eta snacks", "eta peanut butter" } },

                // Cleaning Brands
                { "janola", new[] { "janola bleach", "janola cleaning" } },
                { "finish", new[] { "finish dishwasher", "finish tablets" } },
                { "persil", new[] { "persil washing powder", "persil liquid" } },
                { "sorbent", new[] { "sorbent toilet paper", "sorbent tissues" } },

                // Additional NZ Brands
                { "griffins", new[] { "griffin", "griffins biscuits", "griffin biscuits" } },
                { "edmonds", new[] { "edmonds flour", "edmonds baking" } },
                { "chelsea", new[] { "chelsea sugar", "chelsea baking" } },
                { "pics", new[] { "pic", "pic peanut butter", "pics peanut butter" } }
            };
        }

        // Calculate Levenshtein distance for string similarity
        private static int LevenshteinDistance(string s1, string s2)
        {
            if (string.IsNullOrEmpty(s1)) return string.IsNullOrEmpty(s2) ? 0 : s2.Length;
            if (string.IsNullOrEmpty(s2)) return s1.Length;

            var matrix = new int[s1.Length + 1, s2.Length + 1];

            for (int i = 0; i <= s1.Length; i++) matrix[i, 0] = i;
            for (int j = 0; j <= s2.Length; j++) matrix[0, j] = j;

            for (int i = 1; i <= s1.Length; i++)
            {
                for (int j = 1; j <= s2.Length; j++)
                {
                    int cost = s1[i - 1] == s2[j - 1] ? 0 : 1;
                    matrix[i, j] = Math.Min(Math.Min(matrix[i - 1, j] + 1, matrix[i, j - 1] + 1), matrix[i - 1, j - 1] + cost);
                }
            }

            return matrix[s1.Length, s2.Length];
        }

        // Calculate similarity score between two product names
        private static double CalculateSimilarity(string name1, string name2)
        {
            if (string.IsNullOrEmpty(name1) || string.IsNullOrEmpty(name2)) return 0;
            if (name1 == name2) return 1.0;

            var distance = LevenshteinDistance(name1, name2);
            var maxLength = Math.Max(name1.Length, name2.Length);

            return maxLength == 0 ? 1.0 : 1.0 - (double)distance / maxLength;
        }

        // Find best matching consolidated product using enhanced algorithm
        private static async Task<BsonDocument> FindBestMatch(string normalizedName, string size, string originalName)
        {
            try
            {
                // First try exact match
                var exactMatch = await _consolidatedProductsCollection
                    .Find(Builders<BsonDocument>.Filter.Eq("normalizedName", normalizedName))
                    .FirstOrDefaultAsync();

                if (exactMatch != null)
                {
                    Utilities.Log($"✅ Exact match found: {exactMatch["displayName"]}", ConsoleColor.Green);
                    return exactMatch;
                }

                // Try fuzzy matching
                var allProducts = await _consolidatedProductsCollection
                    .Find(Builders<BsonDocument>.Filter.Empty)
                    .Limit(1000)
                    .ToListAsync();

                BsonDocument bestMatch = null;
                double bestScore = 0;
                const double threshold = 0.8;

                foreach (var product in allProducts)
                {
                    var productNormalizedName = product.GetValue("normalizedName", "").AsString;
                    var score = CalculateSimilarity(normalizedName, productNormalizedName);

                    // Check aliases if they exist
                    if (product.Contains("aliases") && product["aliases"].IsBsonArray)
                    {
                        foreach (var alias in product["aliases"].AsBsonArray)
                        {
                            if (alias.IsString)
                            {
                                var aliasScore = CalculateSimilarity(normalizedName, alias.AsString);
                                score = Math.Max(score, aliasScore);
                            }
                        }
                    }

                    if (score > bestScore && score >= threshold)
                    {
                        bestScore = score;
                        bestMatch = product;
                    }
                }

                if (bestMatch != null)
                {
                    Utilities.Log($"✅ Fuzzy match found: {bestMatch["displayName"]} (confidence: {bestScore:F3})", ConsoleColor.Yellow);
                    return bestMatch;
                }

                Utilities.Log($"❌ No match found for: {originalName}", ConsoleColor.Red);
                return null;
            }
            catch (Exception ex)
            {
                Utilities.Log($"❌ Error finding match: {ex.Message}", ConsoleColor.Red);
                return null;
            }
        }

        // Add alias to consolidated product
        private static async Task AddProductAlias(ObjectId consolidatedProductId, string newAlias)
        {
            try
            {
                var normalizedAlias = NormalizeProductName(newAlias);
                if (string.IsNullOrEmpty(normalizedAlias)) return;

                var product = await _consolidatedProductsCollection
                    .Find(Builders<BsonDocument>.Filter.Eq("_id", consolidatedProductId))
                    .FirstOrDefaultAsync();

                if (product == null) return;

                // Get current aliases or initialize empty array
                var currentAliases = product.Contains("aliases") && product["aliases"].IsBsonArray
                    ? product["aliases"].AsBsonArray.Select(a => a.AsString).ToList()
                    : new List<string>();

                // Don't add if alias already exists or is the same as normalized name
                var normalizedName = product.GetValue("normalizedName", "").AsString;
                if (normalizedAlias == normalizedName || currentAliases.Contains(normalizedAlias)) return;

                // Add new alias
                currentAliases.Add(normalizedAlias);

                var update = Builders<BsonDocument>.Update
                    .Set("aliases", new BsonArray(currentAliases))
                    .Set("updatedAt", DateTime.UtcNow);

                await _consolidatedProductsCollection.UpdateOneAsync(
                    Builders<BsonDocument>.Filter.Eq("_id", consolidatedProductId),
                    update);

                Utilities.Log($"📝 Added alias '{normalizedAlias}' to product {consolidatedProductId}", ConsoleColor.Cyan);
            }
            catch (Exception ex)
            {
                Utilities.Log($"❌ Error adding alias: {ex.Message}", ConsoleColor.Red);
            }
        }

        // Health check
        public static async Task<bool> HealthCheck()
        {
            try
            {
                if (_database == null) return false;
                await _database.RunCommandAsync<BsonDocument>(new BsonDocument("ping", 1));
                return true;
            }
            catch
            {
                return false;
            }
        }

        // Close connection
        public static void Close()
        {
            // MongoDB driver handles connection pooling automatically
            Utilities.Log("MongoDB connection closed", ConsoleColor.Blue);
        }
    }
}