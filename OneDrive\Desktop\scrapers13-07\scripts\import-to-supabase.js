const { createClient } = require("@supabase/supabase-js");
const fs = require("fs").promises;
const path = require("path");

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL || "YOUR_SUPABASE_URL";
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || "YOUR_SERVICE_KEY";
const INPUT_FILE = process.argv[2] || "./cosmos-export/products_all.json";

// Store name mapping
const STORE_MAPPING = {
  "paknsave.co.nz": "paknsave",
  "countdown.co.nz": "woolworths",
  "ishopnewworld.co.nz": "newworld"
};

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function importToSupabase() {
  console.log(`Starting import from ${INPUT_FILE}...`);
  
  try {
    // Read exported data
    const rawData = await fs.readFile(INPUT_FILE, "utf8");
    const cosmosProducts = JSON.parse(rawData);
    console.log(`Found ${cosmosProducts.length} products to import`);
    
    // Ensure stores exist
    const storeIds = {};
    for (const [site, storeName] of Object.entries(STORE_MAPPING)) {
      const { data, error } = await supabase
        .from("stores")
        .upsert({ name: storeName })
        .select("id")
        .single();
      
      if (error) throw error;
      storeIds[site] = data.id;
      console.log(`Store ${storeName} has ID ${data.id}`);
    }
    
    // Process in batches
    const BATCH_SIZE = 100;
    let imported = 0;
    
    for (let i = 0; i < cosmosProducts.length; i += BATCH_SIZE) {
      const batch = cosmosProducts.slice(i, i + BATCH_SIZE);
      
      // Prepare products for upsert
      const products = batch.map(p => ({
        id: p.id,
        name: p.name,
        size: p.size || null,
        unit_price: p.unitPrice || null,
        unit_name: p.unitName || null,
        original_unit_qty: p.originalUnitQuantity || null,
        source_site: p.sourceSite,
        last_updated: p.lastUpdated,
        last_checked: p.lastChecked
      }));
      
      // Upsert products
      const { error: productError } = await supabase
        .from("products")
        .upsert(products, { onConflict: "id" });
      
      if (productError) {
        console.error("Product upsert failed:", productError);
        continue;
      }
      
      // Prepare price history
      const prices = [];
      batch.forEach(p => {
        const storeId = storeIds[p.sourceSite];
        if (!storeId) {
          console.warn(`Unknown store for ${p.sourceSite}`);
          return;
        }
        
        // Add current price
        prices.push({
          product_id: p.id,
          store_id: storeId,
          price: p.currentPrice,
          recorded_at: p.lastUpdated
        });
        
        // Add historical prices
        if (p.priceHistory && Array.isArray(p.priceHistory)) {
          p.priceHistory.forEach(ph => {
            prices.push({
              product_id: p.id,
              store_id: storeId,
              price: ph.price,
              recorded_at: ph.date
            });
          });
        }
      });
      
      // Insert price history
      if (prices.length > 0) {
        const { error: priceError } = await supabase
          .from("prices")
          .insert(prices);
        
        if (priceError) {
          console.error("Price insert failed:", priceError);
        }
      }
      
      imported += batch.length;
      console.log(`Progress: ${imported}/${cosmosProducts.length}`);
    }
    
    console.log(`Import complete! Imported ${imported} products.`);
    
  } catch (error) {
    console.error("Import failed:", error);
    process.exit(1);
  }
}

// Run import
importToSupabase(); 