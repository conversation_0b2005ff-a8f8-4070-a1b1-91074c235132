process.argv = [process.argv[0], ...process.argv.slice(3)];

		import __esrun_url from 'url';

		import { createRequire as __esrun_createRequire } from "module";

		const __esrun_fileUrl = __esrun_url.pathToFileURL("C:\Users\<USER>\OneDrive\Desktop\scrapers13-07\Woolworths\esrun-1753261851737.tmp.mjs");

		const require = __esrun_createRequire(__esrun_fileUrl);
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __require = /* @__PURE__ */ ((x) => typeof require !== "undefined" ? require : typeof Proxy !== "undefined" ? new Proxy(x, {
  get: (a, b) => (typeof require !== "undefined" ? require : a)[b]
}) : x)(function(x) {
  if (typeof require !== "undefined") return require.apply(this, arguments);
  throw Error('Dynamic require of "' + x + '" is not supported');
});
var __commonJS = (cb, mod) => function __require2() {
  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));

// src/typings.js
var require_typings = __commonJS({
  "src/typings.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// src/utilities.js
var require_utilities = __commonJS({
  "src/utilities.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.validCategories = exports.colour = void 0;
    exports.log = log4;
    exports.logError = logError4;
    exports.logProductRow = logProductRow2;
    exports.logTableHeader = logTableHeader2;
    exports.readLinesFromTextFile = readLinesFromTextFile2;
    exports.getTimeElapsedSince = getTimeElapsedSince2;
    exports.toTitleCase = toTitleCase2;
    var fs_1 = __require("fs");
    var tableIDWidth = 6;
    var tableNameWidth = 60;
    var tableSizeWidth = 17;
    exports.colour = {
      red: "\x1B[31m",
      green: "\x1B[32m",
      yellow: "\x1B[33m",
      blue: "\x1B[38;5;117m",
      magenta: "\x1B[35m",
      cyan: "\x1B[36m",
      white: "\x1B[37m",
      crimson: "\x1B[38m",
      grey: "\x1B[90m",
      orange: "\x1B[38;5;214m",
      sky: "\x1B[38;5;153m"
    };
    function log4(colour4, text) {
      const clear = "\x1B[0m";
      console.log(`${colour4}%s${clear}`, text);
    }
    function logError4(text) {
      log4(exports.colour.red, text);
    }
    function logProductRow2(product) {
      var _a;
      const unitPriceString = product.unitPrice ? `$${product.unitPrice.toFixed(2)} /${product.unitName}` : ``;
      log4(getAlternatingRowColour(exports.colour.sky, exports.colour.white), `${product.id.padStart(tableIDWidth)} | ${product.name.slice(0, tableNameWidth).padEnd(tableNameWidth)} | ${(_a = product.size) === null || _a === void 0 ? void 0 : _a.slice(0, tableSizeWidth).padEnd(tableSizeWidth)} | $ ${product.currentPrice.toFixed(2).padStart(4).padEnd(5)} | ` + unitPriceString);
    }
    function logTableHeader2() {
      log4(exports.colour.yellow, `${"ID".padStart(tableIDWidth)} | ${"Name".padEnd(tableNameWidth)} | ${"Size".padEnd(tableSizeWidth)} | ${"Price".padEnd(7)} | Unit Price`);
      let headerLine = "";
      for (let i = 0; i < 111; i++) {
        headerLine += "-";
      }
      log4(exports.colour.yellow, headerLine);
    }
    var alternatingRowColour = false;
    function getAlternatingRowColour(colourA, colourB) {
      alternatingRowColour = alternatingRowColour ? false : true;
      return alternatingRowColour ? colourA : colourB;
    }
    function readLinesFromTextFile2(filename) {
      try {
        const file = (0, fs_1.readFileSync)(filename, "utf-8");
        const result = file.split(/\r?\n/).filter((line) => {
          if (line.trim().length > 0)
            return true;
          else
            return false;
        });
        return result;
      } catch (error) {
        throw "Error reading " + filename;
      }
    }
    function getTimeElapsedSince2(startTime2) {
      let elapsedTimeSeconds = (Date.now() - startTime2) / 1e3;
      let elapsedTimeString = Math.floor(elapsedTimeSeconds).toString();
      if (elapsedTimeSeconds >= 60) {
        return Math.floor(elapsedTimeSeconds / 60) + ":" + Math.floor(elapsedTimeSeconds % 60).toString().padStart(2, "0");
      } else
        return elapsedTimeString + "s";
    }
    exports.validCategories = [
      // freshCategory
      "eggs",
      "fruit",
      "fresh-vegetables",
      "salads-coleslaw",
      "bread",
      "bread-rolls",
      "specialty-bread",
      "bakery-cakes",
      "bakery-desserts",
      // chilledCategory
      "milk",
      "long-life-milk",
      "sour-cream",
      "cream",
      "yoghurt",
      "butter",
      "cheese",
      "cheese-slices",
      "salami",
      "other-deli-foods",
      // meatCategory
      "beef-lamb",
      "chicken",
      "ham",
      "bacon",
      "pork",
      "patties-meatballs",
      "sausages",
      "deli-meats",
      "meat-alternatives",
      "seafood",
      "salmon",
      // frozenCategory
      "ice-cream",
      "ice-blocks",
      "pastries-cheesecake",
      "frozen-chips",
      "frozen-vegetables",
      "frozen-fruit",
      "frozen-seafood",
      "pies-sausage-rolls",
      "pizza",
      "other-savouries",
      // pantryCategory
      "rice",
      "noodles",
      "pasta",
      "beans-spaghetti",
      "canned-fish",
      "canned-meat",
      "soup",
      "cereal",
      "spreads",
      "baking",
      "sauces",
      "oils-vinegars",
      "world-foods",
      // snacksCategory
      "chocolate",
      "boxed-chocolate",
      "chips",
      "crackers",
      "biscuits",
      "muesli-bars",
      "nuts-bulk-mix",
      "sweets-lollies",
      "other-snacks",
      // drinksCategory
      "black-tea",
      "green-tea",
      "herbal-tea",
      "drinking-chocolate",
      "coffee",
      "soft-drinks",
      "energy-drinks",
      "juice",
      // petsCategory
      "cat-food",
      "cat-treats",
      "dog-food",
      "dog-treats"
    ];
    function toTitleCase2(str) {
      return str.replace(/\w\S*/g, function(txt) {
        return txt.charAt(0).toUpperCase() + txt.substring(1).toLowerCase();
      });
    }
  }
});

// src/product-overrides.js
var require_product_overrides = __commonJS({
  "src/product-overrides.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.productOverrides = void 0;
    exports.productOverrides = [
      { id: "206889", size: "180g" },
      { id: "196996", size: "300g" },
      { id: "137967", size: "420g" },
      { id: "125856", size: "450g" },
      { id: "189268", size: "1.13kg" },
      { id: "189150", size: "1.2kg" },
      { id: "190454", size: "2.1kg" },
      { id: "189078", size: "1.3kg" },
      { id: "189136", size: "1.2kg" },
      { id: "755237", size: "931g" },
      { id: "755304", size: "1.1kg" },
      { id: "755246", size: "1020g" },
      { id: "755245", size: "1.2kg" },
      { id: "112273", size: "865ml" },
      { id: "269514", size: "584ml" },
      { id: "269515", size: "584ml" },
      { id: "116518", size: "440ml" },
      { id: "151191", size: "570ml" },
      { id: "279904", size: "575ml" },
      { id: "146149", size: "1000ml" },
      { id: "791925", size: "525g" },
      { id: "774216", size: "525g" },
      { id: "784406", size: "525g" },
      { id: "791916", size: "525g" },
      { id: "306624", size: "185g" },
      { id: "156824", size: "180g" },
      { id: "9023", size: "375g" },
      { id: "266962", category: "sweets-lollies" },
      { id: "171524", size: "230ml", category: "baking" },
      { id: "170021", category: "ice-blocks" },
      { id: "71164", category: "sausages" },
      { id: "71174", category: "sausages" },
      { id: "71168", category: "sausages" },
      { id: "71165", category: "sausages" },
      { id: "331560", category: "specialty-bread" },
      { id: "679412", category: "herbal-tea" },
      { id: "790129", category: "herbal-tea" },
      { id: "267492", category: "herbal-tea" },
      { id: "267485", category: "herbal-tea" },
      { id: "413302", category: "herbal-tea" },
      { id: "267488", category: "herbal-tea" },
      { id: "760872", category: "herbal-tea" },
      { id: "681177", category: "herbal-tea" },
      { id: "95091", category: "herbal-tea" },
      { id: "761093", category: "black-tea" },
      { id: "721661", category: "green-tea" },
      { id: "790129", category: "herbal-tea" },
      { id: "267492", category: "herbal-tea" },
      { id: "267485", category: "herbal-tea" },
      { id: "721034", category: "herbal-tea" },
      { id: "413302", category: "herbal-tea" },
      { id: "267488", category: "herbal-tea" },
      { id: "760872", category: "herbal-tea" },
      { id: "681177", category: "herbal-tea" },
      { id: "95091.", category: "herbal-tea" },
      { id: "184090", category: "herbal-tea" },
      { id: "761093", category: "black-tea" },
      { id: "721661", category: "green-tea" },
      { id: "690093", category: "green-tea" },
      { id: "780922", category: "sauces" },
      { id: "780921", category: "sauces" },
      { id: "72618", category: "black-tea" },
      { id: "6053", category: "black-tea" },
      { id: "72617", category: "black-tea" },
      { id: "168068", category: "black-tea" },
      { id: "6052", category: "black-tea" },
      { id: "761436", category: "black-tea" }
    ];
  }
});

// src/index.ts
import * as dotenv3 from "./node_modules/dotenv/lib/main.js";
import playwright from "./node_modules/playwright/index.mjs";
import * as cheerio from "./node_modules/cheerio/dist/esm/index.js";
import _ from "./node_modules/lodash/lodash.js";
import { setTimeout } from "timers/promises";

// src/mongodb.ts
var import_typings = __toESM(require_typings());
var import_utilities = __toESM(require_utilities());
import { MongoClient, GridFSBucket, ObjectId } from "./node_modules/mongodb/lib/index.js";
import * as dotenv from "./node_modules/dotenv/lib/main.js";
dotenv.config();
dotenv.config({ path: `.env.local`, override: true });
var client;
var db;
var gridFS;
var storeId;
var storesCollection;
var brandsCollection;
var consolidatedProductsCollection;
var priceHistoryCollection;
var categoryHierarchyCollection;
async function establishMongoDB() {
  const connectionString = process.env.MONGODB_CONNECTION_STRING;
  const databaseName = process.env.MONGODB_DATABASE_NAME || "nz-supermarket-scraper";
  if (!connectionString) {
    throw Error("MONGODB_CONNECTION_STRING not set in env");
  }
  try {
    client = new MongoClient(connectionString);
    await client.connect();
    db = client.db(databaseName);
    storesCollection = db.collection("stores");
    brandsCollection = db.collection("brands");
    consolidatedProductsCollection = db.collection("consolidatedProducts");
    priceHistoryCollection = db.collection("priceHistory");
    categoryHierarchyCollection = db.collection("categoryHierarchy");
    gridFS = new GridFSBucket(db, { bucketName: "productImages" });
    (0, import_utilities.log)(import_utilities.colour.green, "\u2705 MongoDB connection established");
    await createIndexes();
  } catch (error) {
    (0, import_utilities.logError)(`Failed to connect to MongoDB: ${error.message}`);
    throw error;
  }
}
async function createIndexes() {
  try {
    await consolidatedProductsCollection.createIndex({
      "displayName": "text",
      "normalizedName": "text",
      "variants.storeName": "text"
    });
    await consolidatedProductsCollection.createIndex({ "categoryId": 1 });
    await consolidatedProductsCollection.createIndex({ "brandId": 1 });
    await consolidatedProductsCollection.createIndex({
      "variants.storeProductId": 1,
      "variants.storeId": 1
    });
    await priceHistoryCollection.createIndex({
      "consolidatedProductId": 1,
      "recordedAt": -1
    });
    await priceHistoryCollection.createIndex({ "year": 1, "month": 1 });
    await categoryHierarchyCollection.createIndex({ "parentId": 1, "sortOrder": 1 });
    await categoryHierarchyCollection.createIndex({ "level": 1, "sortOrder": 1 });
    (0, import_utilities.log)(import_utilities.colour.blue, "\u2705 MongoDB indexes created");
  } catch (error) {
    (0, import_utilities.logError)(`Failed to create indexes: ${error.message}`);
  }
}
async function ensureStoreRow() {
  if (storeId !== void 0) return storeId;
  try {
    const existingStore = await storesCollection.findOne({ storeId: "woolworths" });
    if (existingStore) {
      storeId = existingStore._id;
      return storeId;
    }
    const insertResult = await storesCollection.insertOne({
      storeId: "woolworths",
      name: "Woolworths",
      logoUrl: null,
      createdAt: /* @__PURE__ */ new Date(),
      updatedAt: /* @__PURE__ */ new Date()
    });
    storeId = insertResult.insertedId;
    return storeId;
  } catch (error) {
    (0, import_utilities.logError)(`Failed to ensure store row: ${error.message}`);
    throw error;
  }
}
async function upsertProductToMongoDB(scraped) {
  if (!db) throw Error("MongoDB client not initialised");
  try {
    const sId = await ensureStoreRow();
    const now = /* @__PURE__ */ new Date();
    let consolidatedProduct = await consolidatedProductsCollection.findOne({
      "variants.storeProductId": scraped.id,
      "variants.storeId": sId
    });
    if (!consolidatedProduct) {
      const normalizedName = normalizeProductName(scraped.name, scraped.size);
      (0, import_utilities.log)(import_utilities.colour.cyan, `\u{1F50D} Processing: ${scraped.name} -> normalized: ${normalizedName}`);
      const matchingProduct = await findBestMatch(normalizedName, scraped.size, scraped.name);
      if (matchingProduct) {
        consolidatedProduct = matchingProduct;
        await addProductAlias(consolidatedProduct._id, scraped.name);
        const newVariant = {
          storeProductId: scraped.id,
          storeId: sId,
          storeName: scraped.name,
          storeSize: scraped.size,
          storeUnitPrice: scraped.unitPrice,
          storeUnitName: scraped.unitName,
          lastSeen: now,
          isActive: true,
          imageUrl: null
        };
        await consolidatedProductsCollection.updateOne(
          { _id: consolidatedProduct._id },
          {
            $push: { variants: newVariant },
            $set: { updatedAt: now }
          }
        );
        (0, import_utilities.log)(import_utilities.colour.green, `\u2705 Added variant to existing product: ${matchingProduct.displayName}`);
      } else {
        const newProduct = {
          normalizedName,
          displayName: scraped.name,
          primarySize: scraped.size,
          categoryId: null,
          // TODO: Implement category mapping
          brandId: null,
          // TODO: Implement brand extraction
          matchConfidence: 100,
          manualMatch: false,
          aliases: [],
          // Initialize empty aliases array
          variants: [{
            storeProductId: scraped.id,
            storeId: sId,
            storeName: scraped.name,
            storeSize: scraped.size,
            storeUnitPrice: scraped.unitPrice,
            storeUnitName: scraped.unitName,
            lastSeen: now,
            isActive: true,
            imageUrl: null
          }],
          sizeVariants: scraped.size ? [{
            sizeName: scraped.size,
            sizeWeightGrams: null,
            sizeVolumeMl: null,
            isPrimarySize: true
          }] : [],
          currentPrices: [{
            storeId: sId,
            price: scraped.currentPrice,
            isSpecial: false,
            wasAvailable: true,
            recordedAt: now
          }],
          createdAt: now,
          updatedAt: now
        };
        const insertResult = await consolidatedProductsCollection.insertOne(newProduct);
        consolidatedProduct = { _id: insertResult.insertedId, ...newProduct };
        (0, import_utilities.log)(import_utilities.colour.blue, `\u{1F195} Created new consolidated product: ${scraped.name}`);
      }
    } else {
      await consolidatedProductsCollection.updateOne(
        { _id: consolidatedProduct._id },
        {
          $set: {
            "variants.$[variant].lastSeen": now,
            "variants.$[variant].storeUnitPrice": scraped.unitPrice,
            "variants.$[variant].storeUnitName": scraped.unitName,
            "currentPrices.$[price].price": scraped.currentPrice,
            "currentPrices.$[price].recordedAt": now,
            updatedAt: now
          }
        },
        {
          arrayFilters: [
            { "variant.storeProductId": scraped.id },
            { "price.storeId": sId }
          ]
        }
      );
    }
    await priceHistoryCollection.insertOne({
      consolidatedProductId: consolidatedProduct._id,
      storeId: sId,
      price: scraped.currentPrice,
      isSpecial: false,
      wasAvailable: true,
      recordedAt: now,
      year: now.getFullYear(),
      month: now.getMonth() + 1
    });
    (0, import_utilities.log)(import_utilities.colour.green, `  Upserted: ${scraped.name.slice(0, 45).padEnd(45)} | $${scraped.currentPrice}`);
    return import_typings.UpsertResponse.PriceChanged;
  } catch (error) {
    (0, import_utilities.logError)(`MongoDB upsert failed: ${error.message}`);
    return import_typings.UpsertResponse.Failed;
  }
}
async function uploadImageToMongoDB(imageUrl, product) {
  if (!gridFS) throw Error("MongoDB GridFS not initialised");
  try {
    const sId = await ensureStoreRow();
    const imageResponse = await fetch(imageUrl);
    if (!imageResponse.ok) {
      (0, import_utilities.logError)(`Failed to download image from ${imageUrl}: ${imageResponse.statusText}`);
      return false;
    }
    const imageBuffer = Buffer.from(await imageResponse.arrayBuffer());
    const existingFiles = await gridFS.find({
      "metadata.productId": product.id
    }).toArray();
    for (const file of existingFiles) {
      await gridFS.delete(file._id);
    }
    const filename = `${product.id}.jpg`;
    const uploadStream = gridFS.openUploadStream(filename, {
      contentType: "image/jpeg",
      metadata: {
        productId: product.id,
        storeId: sId,
        originalUrl: imageUrl,
        uploadedAt: /* @__PURE__ */ new Date()
      }
    });
    await new Promise((resolve, reject) => {
      uploadStream.end(imageBuffer, (error) => {
        if (error) reject(error);
        else resolve(uploadStream.id);
      });
    });
    const imageUrl_gridfs = `gridfs://productImages/${uploadStream.id}`;
    await consolidatedProductsCollection.updateOne(
      { "variants.storeProductId": product.id },
      {
        $set: {
          "variants.$[variant].imageUrl": imageUrl_gridfs
        }
      },
      {
        arrayFilters: [{ "variant.storeProductId": product.id }]
      }
    );
    (0, import_utilities.log)(import_utilities.colour.blue, `  Image uploaded: ${product.id} -> GridFS ${uploadStream.id}`);
    return true;
  } catch (err) {
    (0, import_utilities.logError)(`Image upload error: ${err.message}`);
    return false;
  }
}
function normalizeProductName(name, size) {
  if (!name) return "";
  let normalized = name.toLowerCase().replace(/'/g, "").replace(/"/g, "").replace(/-/g, " ").replace(/\s+/g, " ").trim();
  const descriptors = [
    "woolworths",
    "countdown",
    "new world",
    "paknsave",
    "pak n save",
    "select",
    "premium",
    "value",
    "budget",
    "signature",
    "essentials",
    "pams",
    "homebrand",
    "signature range",
    "fresh choice"
  ];
  for (const descriptor of descriptors) {
    normalized = normalized.replace(new RegExp(`\\b${descriptor}\\b`, "g"), "").trim();
  }
  const nzBrandMappings = getNZBrandMappings();
  for (const [canonical, variations] of Object.entries(nzBrandMappings)) {
    if (normalized.includes(canonical)) {
      normalized = normalized.replace(canonical, canonical);
      break;
    }
    for (const variation of variations) {
      if (normalized.includes(variation)) {
        normalized = normalized.replace(variation, canonical);
        break;
      }
    }
  }
  if (size) {
    const normalizedSize = size.toLowerCase().replace(/grams?/g, "g").replace(/kilograms?/g, "kg").replace(/litres?/g, "l").replace(/millilitres?/g, "ml");
    normalized += ` ${normalizedSize}`;
  }
  return normalized.trim();
}
function getNZBrandMappings() {
  return {
    // Supermarket Private Labels
    "pams": ["pam", "pams brand", "pams select"],
    "essentials": ["essentials brand", "countdown essentials"],
    "homebrand": ["home brand", "homebrand select"],
    "signature": ["signature range", "signature brand"],
    // Dairy Brands
    "anchor": ["anchor brand", "anchor dairy", "anchor milk", "anchor butter"],
    "mainland": ["mainland cheese", "mainland dairy", "mainland brand"],
    "meadowfresh": ["meadow fresh", "meadowfresh milk", "meadow fresh milk"],
    "lewis road": ["lewis road creamery", "lewis road milk", "lewis road butter"],
    "kapiti": ["kapiti cheese", "kapiti ice cream", "kapiti brand"],
    // Meat Brands
    "tegel": ["tegel chicken", "tegel poultry", "tegel brand"],
    "inghams": ["ingham", "inghams chicken", "inghams poultry"],
    "hellers": ["heller", "hellers bacon", "hellers sausages"],
    "beehive": ["beehive bacon", "beehive ham", "beehive smallgoods"],
    // Bread Brands
    "tip top": ["tiptop", "tip top bread", "tiptop bread"],
    "molenberg": ["molenberg bread", "molenburg", "molenberg wholemeal"],
    "vogels": ["vogel", "vogels bread", "vogel bread", "vogels original"],
    "freyas": ["freya", "freyas bread", "freya bread"],
    // Beverage Brands
    "coke": ["coca cola", "coca-cola", "coke classic", "coca cola classic"],
    "coke zero": ["coca cola zero", "coke zero sugar", "coca cola zero sugar"],
    "pepsi": ["pepsi cola", "pepsi classic", "pepsi original"],
    "l&p": ["lemon paeroa", "lemon and paeroa", "l and p"],
    // Cereal Brands
    "sanitarium": ["sanitarium weetbix", "sanitarium so good"],
    "weetbix": ["weet bix", "wheat biscuits", "sanitarium weetbix"],
    "uncle tobys": ["uncle toby", "uncle tobys oats", "uncle tobys muesli"],
    "kelloggs": ["kellogg", "kelloggs cornflakes", "kelloggs special k"],
    // Pantry Brands
    "watties": ["wattie", "watties tomatoes", "watties sauce"],
    "heinz": ["heinz beans", "heinz tomato sauce", "heinz soup"],
    "mccains": ["mccain", "mccains chips", "mccains frozen"],
    "maggi": ["maggi noodles", "maggi soup", "maggi instant"],
    // Snack Brands
    "cadbury": ["cadburys", "cadbury chocolate"],
    "whittakers": ["whittaker", "whittakers chocolate"],
    "bluebird": ["bluebird chips", "bluebird snacks"],
    "eta": ["eta chips", "eta snacks", "eta peanut butter"],
    // Cleaning Brands
    "janola": ["janola bleach", "janola cleaning"],
    "finish": ["finish dishwasher", "finish tablets"],
    "persil": ["persil washing powder", "persil liquid"],
    "sorbent": ["sorbent toilet paper", "sorbent tissues"],
    // Additional NZ Brands
    "griffins": ["griffin", "griffins biscuits", "griffin biscuits"],
    "edmonds": ["edmonds flour", "edmonds baking"],
    "chelsea": ["chelsea sugar", "chelsea baking"],
    "pics": ["pic", "pic peanut butter", "pics peanut butter"]
  };
}
function levenshteinDistance(s1, s2) {
  if (!s1) return s2 ? s2.length : 0;
  if (!s2) return s1.length;
  const matrix = [];
  for (let i = 0; i <= s2.length; i++) {
    matrix[i] = [i];
  }
  for (let j = 0; j <= s1.length; j++) {
    matrix[0][j] = j;
  }
  for (let i = 1; i <= s2.length; i++) {
    for (let j = 1; j <= s1.length; j++) {
      const cost = s1[j - 1] === s2[i - 1] ? 0 : 1;
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,
        matrix[i][j - 1] + 1,
        matrix[i - 1][j - 1] + cost
      );
    }
  }
  return matrix[s2.length][s1.length];
}
function calculateSimilarity(name1, name2) {
  if (!name1 || !name2) return 0;
  if (name1 === name2) return 1;
  const distance = levenshteinDistance(name1, name2);
  const maxLength = Math.max(name1.length, name2.length);
  return maxLength === 0 ? 1 : 1 - distance / maxLength;
}
async function findBestMatch(normalizedName, size, originalName) {
  try {
    const exactMatch = await consolidatedProductsCollection.findOne({
      normalizedName
    });
    if (exactMatch) {
      (0, import_utilities.log)(import_utilities.colour.green, `\u2705 Exact match found: ${exactMatch.displayName}`);
      return exactMatch;
    }
    const allProducts = await consolidatedProductsCollection.find({}).limit(1e3).toArray();
    let bestMatch = null;
    let bestScore = 0;
    const threshold = 0.8;
    for (const product of allProducts) {
      let score = calculateSimilarity(normalizedName, product.normalizedName);
      if (product.aliases && Array.isArray(product.aliases)) {
        for (const alias of product.aliases) {
          const aliasScore = calculateSimilarity(normalizedName, alias);
          score = Math.max(score, aliasScore);
        }
      }
      if (score > bestScore && score >= threshold) {
        bestScore = score;
        bestMatch = product;
      }
    }
    if (bestMatch) {
      (0, import_utilities.log)(import_utilities.colour.yellow, `\u2705 Fuzzy match found: ${bestMatch.displayName} (confidence: ${bestScore.toFixed(3)})`);
      return bestMatch;
    }
    (0, import_utilities.log)(import_utilities.colour.red, `\u274C No match found for: ${originalName}`);
    return null;
  } catch (error) {
    (0, import_utilities.logError)(`\u274C Error finding match: ${error.message}`);
    return null;
  }
}
async function addProductAlias(consolidatedProductId, newAlias) {
  try {
    const normalizedAlias = normalizeProductName(newAlias);
    if (!normalizedAlias) return;
    const product = await consolidatedProductsCollection.findOne({ _id: consolidatedProductId });
    if (!product) return;
    const currentAliases = product.aliases || [];
    if (normalizedAlias === product.normalizedName || currentAliases.includes(normalizedAlias)) return;
    currentAliases.push(normalizedAlias);
    await consolidatedProductsCollection.updateOne(
      { _id: consolidatedProductId },
      {
        $set: {
          aliases: currentAliases,
          updatedAt: /* @__PURE__ */ new Date()
        }
      }
    );
    (0, import_utilities.log)(import_utilities.colour.cyan, `\u{1F4DD} Added alias '${normalizedAlias}' to product ${consolidatedProductId}`);
  } catch (error) {
    (0, import_utilities.logError)(`\u274C Error adding alias: ${error.message}`);
  }
}
async function closeMongoDB() {
  if (client) {
    await client.close();
    (0, import_utilities.log)(import_utilities.colour.blue, "MongoDB connection closed");
  }
}

// src/consolidated-products-mongodb.ts
var import_utilities2 = __toESM(require_utilities());
import { MongoClient as MongoClient2, ObjectId as ObjectId2 } from "./node_modules/mongodb/lib/index.js";
import * as dotenv2 from "./node_modules/dotenv/lib/main.js";
dotenv2.config();
var client2;
var db2;
var consolidatedProductsCollection2;
var brandsCollection2;
var categoryHierarchyCollection2;
async function initializeConsolidatedProductsMongoDB() {
  const connectionString = process.env.MONGODB_CONNECTION_STRING;
  const databaseName = process.env.MONGODB_DATABASE_NAME || "nz-supermarket-scraper";
  if (!connectionString) {
    throw Error("MONGODB_CONNECTION_STRING not set in env");
  }
  try {
    client2 = new MongoClient2(connectionString);
    await client2.connect();
    db2 = client2.db(databaseName);
    consolidatedProductsCollection2 = db2.collection("consolidatedProducts");
    brandsCollection2 = db2.collection("brands");
    categoryHierarchyCollection2 = db2.collection("categoryHierarchy");
    (0, import_utilities2.log)(import_utilities2.colour.green, "\u2705 Consolidated products MongoDB initialized");
    await ensureBasicCategories();
  } catch (error) {
    (0, import_utilities2.logError)(`Failed to initialize consolidated products MongoDB: ${error.message}`);
    throw error;
  }
}
async function ensureBasicCategories() {
  try {
    const existingCategories = await categoryHierarchyCollection2.countDocuments();
    if (existingCategories === 0) {
      const mainCategories = [
        { name: "Fresh Foods", parentId: null, level: 0, sortOrder: 1 },
        { name: "Chilled & Frozen", parentId: null, level: 0, sortOrder: 2 },
        { name: "Pantry & Dry Goods", parentId: null, level: 0, sortOrder: 3 },
        { name: "Beverages", parentId: null, level: 0, sortOrder: 4 },
        { name: "Health & Household", parentId: null, level: 0, sortOrder: 5 }
      ];
      const insertResult = await categoryHierarchyCollection2.insertMany(mainCategories);
      (0, import_utilities2.log)(import_utilities2.colour.blue, `\u2705 Created ${insertResult.insertedCount} main categories`);
      const freshFoodsId = Object.values(insertResult.insertedIds).find(async (id) => {
        const cat = await categoryHierarchyCollection2.findOne({ _id: id });
        return cat?.name === "Fresh Foods";
      });
      (0, import_utilities2.log)(import_utilities2.colour.blue, "\u2705 Basic category structure created");
    }
  } catch (error) {
    (0, import_utilities2.logError)(`Failed to ensure basic categories: ${error.message}`);
  }
}
async function processConsolidatedProductMongoDB(scrapedProduct) {
  if (!consolidatedProductsCollection2) {
    (0, import_utilities2.logError)("Consolidated products collection not initialized");
    return null;
  }
  try {
    const normalizedName = normalizeProductName2(scrapedProduct.name, scrapedProduct.size);
    const now = /* @__PURE__ */ new Date();
    const existingProduct = await consolidatedProductsCollection2.findOne({
      "variants.storeProductId": scrapedProduct.id
    });
    if (existingProduct) {
      await consolidatedProductsCollection2.updateOne(
        { _id: existingProduct._id },
        {
          $set: {
            "variants.$[variant].lastSeen": now,
            "variants.$[variant].storeUnitPrice": scrapedProduct.unitPrice,
            "variants.$[variant].storeUnitName": scrapedProduct.unitName,
            updatedAt: now
          }
        },
        {
          arrayFilters: [{ "variant.storeProductId": scrapedProduct.id }]
        }
      );
      (0, import_utilities2.log)(import_utilities2.colour.cyan, `  Updated consolidated product: ${scrapedProduct.name.slice(0, 40)}`);
      return existingProduct._id.toString();
    } else {
      const newProduct = {
        normalizedName,
        displayName: scrapedProduct.name,
        primarySize: scrapedProduct.size,
        categoryId: null,
        // TODO: Implement category mapping
        brandId: null,
        // TODO: Implement brand extraction
        matchConfidence: 100,
        manualMatch: false,
        variants: [{
          storeProductId: scrapedProduct.id,
          storeId: "woolworths",
          // Store identifier
          storeName: scrapedProduct.name,
          storeSize: scrapedProduct.size,
          storeUnitPrice: scrapedProduct.unitPrice,
          storeUnitName: scrapedProduct.unitName,
          lastSeen: now,
          isActive: true,
          imageUrl: null
        }],
        sizeVariants: scrapedProduct.size ? [{
          sizeName: scrapedProduct.size,
          sizeWeightGrams: null,
          sizeVolumeMl: null,
          isPrimarySize: true
        }] : [],
        currentPrices: [{
          storeId: "woolworths",
          price: scrapedProduct.currentPrice,
          isSpecial: false,
          wasAvailable: true,
          recordedAt: now
        }],
        createdAt: now,
        updatedAt: now
      };
      const insertResult = await consolidatedProductsCollection2.insertOne(newProduct);
      (0, import_utilities2.log)(import_utilities2.colour.cyan, `  Created consolidated product: ${scrapedProduct.name.slice(0, 40)}`);
      return insertResult.insertedId.toString();
    }
  } catch (error) {
    (0, import_utilities2.logError)(`Failed to process consolidated product: ${error.message}`);
    return null;
  }
}
function normalizeProductName2(name, size) {
  let normalized = name.toLowerCase().replace(/[^a-z0-9\s]/g, " ").replace(/\s+/g, "_").trim();
  if (size) {
    const normalizedSize = size.toLowerCase().replace(/[^a-z0-9]/g, "").trim();
    normalized += "_" + normalizedSize;
  }
  return normalized;
}
async function closeConsolidatedProductsMongoDB() {
  try {
    if (client2) {
      await client2.close();
    }
    (0, import_utilities2.log)(import_utilities2.colour.blue, "\u2705 Consolidated products MongoDB connections closed");
  } catch (error) {
    (0, import_utilities2.logError)(`Failed to close MongoDB connections: ${error.message}`);
  }
}

// src/index.ts
var import_product_overrides = __toESM(require_product_overrides());
var import_utilities3 = __toESM(require_utilities());
dotenv3.config();
dotenv3.config({ path: `.env.local`, override: true });
var pageLoadDelaySeconds = 7;
var productLogDelayMilliSeconds = 20;
var startTime = Date.now();
var categorisedUrls = loadUrlsFile();
var databaseMode = false;
var uploadImagesMode = false;
var headlessMode = true;
categorisedUrls = await handleArguments(categorisedUrls);
if (databaseMode) {
  await establishMongoDB();
  await initializeConsolidatedProductsMongoDB();
}
var browser;
var page;
browser = await establishPlaywrightPage(headlessMode);
if (process.env.SKIP_STORE_SELECTION !== "true") {
  try {
    await selectStoreByLocationName();
  } catch (error) {
    (0, import_utilities3.logError)(`Store selection failed: ${error}`);
    (0, import_utilities3.log)(import_utilities3.colour.yellow, "Continuing with default store location...");
  }
} else {
  (0, import_utilities3.log)(import_utilities3.colour.yellow, "Store selection skipped (SKIP_STORE_SELECTION=true)");
}
await scrapeAllPageURLs();
await browser.close();
if (databaseMode) {
  await closeConsolidatedProductsMongoDB();
  await closeMongoDB();
}
(0, import_utilities3.log)(
  import_utilities3.colour.sky,
  `
All Pages Completed = Total Time Elapsed ${(0, import_utilities3.getTimeElapsedSince)(startTime)} 
`
);
function loadUrlsFile(filePath = "src/urls.txt") {
  const rawLinesFromFile = (0, import_utilities3.readLinesFromTextFile)(filePath);
  let categorisedUrls2 = [];
  rawLinesFromFile.map((line) => {
    let parsedUrls = parseAndCategoriseURL(line);
    if (parsedUrls !== void 0) {
      categorisedUrls2 = [...categorisedUrls2, ...parsedUrls];
    }
  });
  return categorisedUrls2;
}
async function scrapeAllPageURLs() {
  (0, import_utilities3.log)(
    import_utilities3.colour.yellow,
    `${categorisedUrls.length} pages to be scraped`.padEnd(35) + `${pageLoadDelaySeconds}s delay between scrapes`.padEnd(35) + (databaseMode ? "(Database Mode)" : "(Dry Run Mode)")
  );
  for (let i = 0; i < categorisedUrls.length; i++) {
    const categorisedUrl = categorisedUrls[i];
    let url = categorisedUrls[i].url;
    const shortUrl = url.replace("https://", "");
    (0, import_utilities3.log)(
      import_utilities3.colour.yellow,
      `
[${i + 1}/${categorisedUrls.length}] ${shortUrl}`
    );
    try {
      await page.goto(url);
      for (let pageDown = 0; pageDown < 5; pageDown++) {
        const timeBetweenPgDowns = Math.random() * 1e3 + 500;
        await page.waitForTimeout(timeBetweenPgDowns);
        await page.keyboard.press("PageDown");
      }
      await page.setDefaultTimeout(3e4);
      await page.waitForSelector("product-price h3");
      const html = await page.innerHTML("product-grid");
      const $ = cheerio.load(html);
      const allProductEntries = $("cdx-card product-stamp-grid div.product-entry");
      const advertisementEntries = $("div.carousel-track div cdx-card product-stamp-grid div.product-entry");
      const adHrefs = advertisementEntries.map((index, element) => {
        return $(element).find("a").first().attr("href");
      }).toArray();
      const productEntries = allProductEntries.filter((index, element) => {
        const productHref = $(element).find("a").first().attr("href");
        return !adHrefs.includes(productHref);
      });
      (0, import_utilities3.log)(
        import_utilities3.colour.yellow,
        `${productEntries.length} product entries found`.padEnd(35) + `Time Elapsed: ${(0, import_utilities3.getTimeElapsedSince)(startTime)}`.padEnd(35) + `Category: ${_.startCase(categorisedUrl.categories.join(" - "))}`
      );
      if (!databaseMode) (0, import_utilities3.logTableHeader)();
      let perPageLogStats = {
        newProducts: 0,
        priceChanged: 0,
        infoUpdated: 0,
        alreadyUpToDate: 0
      };
      perPageLogStats = await processFoundProductEntries(categorisedUrl, productEntries, perPageLogStats);
      if (databaseMode) {
        (0, import_utilities3.log)(
          import_utilities3.colour.blue,
          `Supabase: ${perPageLogStats.newProducts} new products, ${perPageLogStats.priceChanged} updated prices, ${perPageLogStats.infoUpdated} updated info, ${perPageLogStats.alreadyUpToDate} already up-to-date`
        );
      }
      await setTimeout(pageLoadDelaySeconds * 1e3);
    } catch (error) {
      if (typeof error === "string") {
        if (error.includes("NS_ERROR_CONNECTION_REFUSED")) {
          (0, import_utilities3.logError)("Connection Failed - Check Firewall\n" + error);
          return;
        }
      }
      (0, import_utilities3.logError)(
        "Page Timeout after 30 seconds - Skipping this page\n" + error
      );
    }
  }
}
async function processFoundProductEntries(categorisedUrl, productEntries, perPageLogStats) {
  for (let i = 0; i < productEntries.length; i++) {
    const productEntryElement = productEntries[i];
    const product = playwrightElementToProduct(
      productEntryElement,
      categorisedUrl.categories
    );
    if (databaseMode && product !== void 0) {
      const response = await upsertProductToMongoDB(product);
      const consolidatedProductId = await processConsolidatedProductMongoDB(product);
      switch (response) {
        case 3 /* AlreadyUpToDate */:
          perPageLogStats.alreadyUpToDate++;
          break;
        case 2 /* InfoChanged */:
          perPageLogStats.infoUpdated++;
          break;
        case 0 /* NewProduct */:
          perPageLogStats.newProducts++;
          break;
        case 1 /* PriceChanged */:
          perPageLogStats.priceChanged++;
          break;
        default:
          break;
      }
      if (uploadImagesMode) {
        const imageUrlBase = "https://assets.woolworths.com.au/images/2010/";
        const imageUrlExtensionAndQueryParams = ".jpg?impolicy=wowcdxwbjbx&w=900&h=900";
        const imageUrl = imageUrlBase + product.id + imageUrlExtensionAndQueryParams;
        await uploadImageToMongoDB(imageUrl, product);
      }
    } else if (!databaseMode && product !== void 0) {
      (0, import_utilities3.logProductRow)(product);
    }
    await setTimeout(productLogDelayMilliSeconds);
  }
  return perPageLogStats;
}
function handleArguments(categorisedUrls2) {
  if (process.argv.length > 2) {
    const userArgs = process.argv.slice(2, process.argv.length);
    let potentialUrl = "";
    userArgs.forEach(async (arg) => {
      if (arg === "db") databaseMode = true;
      else if (arg === "images") uploadImagesMode = true;
      else if (arg === "headless") headlessMode = true;
      else if (arg === "headed") headlessMode = false;
      else if (arg.includes(".co.nz")) potentialUrl += arg;
      else if (arg === "reverse") categorisedUrls2 = categorisedUrls2.reverse();
    });
    const parsedUrl = parseAndCategoriseURL(potentialUrl);
    if (parsedUrl !== void 0) categorisedUrls2 = [parsedUrl];
  }
  return categorisedUrls2;
}
async function establishPlaywrightPage(headless = true) {
  (0, import_utilities3.log)(
    import_utilities3.colour.yellow,
    "Launching Browser.. " + (process.argv.length > 2 ? "(" + (process.argv.length - 2) + " arguments found)" : "")
  );
  browser = await playwright.firefox.launch({
    headless
  });
  page = await browser.newPage();
  await routePlaywrightExclusions();
  return browser;
}
async function selectStoreByLocationName(locationName = "") {
  if (locationName === "") {
    if (process.env.STORE_NAME) locationName = process.env.STORE_NAME;
    else {
      (0, import_utilities3.log)(import_utilities3.colour.yellow, "No store location specified - using default location");
      return;
    }
  }
  (0, import_utilities3.log)(import_utilities3.colour.yellow, `Attempting to select store location: ${locationName}`);
  try {
    await page.setDefaultTimeout(15e3);
    await page.goto("https://www.woolworths.co.nz/shop/browse", {
      waitUntil: "domcontentloaded"
    });
    await page.waitForTimeout(2e3);
    const possibleSelectors = [
      'button[data-testid="store-selector"]',
      'button[aria-label*="store"]',
      'button[aria-label*="location"]',
      '[data-testid="change-store"]',
      'button:has-text("Change store")',
      'button:has-text("Select store")',
      ".store-selector button",
      '[class*="store"] button',
      "fieldset div div p button"
    ];
    let storeButton = null;
    for (const selector of possibleSelectors) {
      try {
        await page.waitForSelector(selector, { timeout: 3e3 });
        storeButton = page.locator(selector).first();
        (0, import_utilities3.log)(import_utilities3.colour.green, `Found store selector: ${selector}`);
        break;
      } catch {
      }
    }
    if (!storeButton) {
      (0, import_utilities3.log)(import_utilities3.colour.yellow, "No store selector found - proceeding with default location");
      return;
    }
    await storeButton.click();
    await page.waitForTimeout(1e3);
    const inputSelectors = [
      'input[placeholder*="suburb"]',
      'input[placeholder*="location"]',
      'input[placeholder*="address"]',
      'input[type="text"]',
      "form-suburb-autocomplete form-input input",
      '[data-testid="location-input"]',
      ".location-input input",
      'input[aria-label*="location"]'
    ];
    let locationInput = null;
    for (const selector of inputSelectors) {
      try {
        await page.waitForSelector(selector, { timeout: 3e3 });
        locationInput = page.locator(selector).first();
        (0, import_utilities3.log)(import_utilities3.colour.green, `Found location input: ${selector}`);
        break;
      } catch {
      }
    }
    if (!locationInput) {
      (0, import_utilities3.log)(import_utilities3.colour.yellow, "No location input found - proceeding with default location");
      return;
    }
    await locationInput.clear();
    await locationInput.fill(locationName);
    await page.waitForTimeout(2e3);
    try {
      const suggestionSelectors = [
        '[role="option"]:first-child',
        ".suggestion:first-child",
        ".autocomplete-item:first-child",
        "li:first-child",
        '[data-testid="suggestion"]:first-child'
      ];
      let suggestionFound = false;
      for (const selector of suggestionSelectors) {
        try {
          await page.waitForSelector(selector, { timeout: 2e3 });
          await page.locator(selector).click();
          suggestionFound = true;
          (0, import_utilities3.log)(import_utilities3.colour.green, `Selected suggestion using: ${selector}`);
          break;
        } catch {
        }
      }
      if (!suggestionFound) {
        await page.keyboard.press("ArrowDown");
        await page.waitForTimeout(300);
        await page.keyboard.press("Enter");
        (0, import_utilities3.log)(import_utilities3.colour.yellow, "Used keyboard navigation to select location");
      }
      await page.waitForTimeout(1e3);
      const saveSelectors = [
        'button:has-text("Save")',
        'button:has-text("Continue")',
        'button:has-text("Confirm")',
        'button:has-text("Select")',
        '[data-testid="save-location"]',
        ".save-button",
        'button[type="submit"]'
      ];
      for (const selector of saveSelectors) {
        try {
          await page.waitForSelector(selector, { timeout: 2e3 });
          await page.locator(selector).click();
          (0, import_utilities3.log)(import_utilities3.colour.green, `Clicked save button: ${selector}`);
          break;
        } catch {
        }
      }
      await page.waitForTimeout(2e3);
      (0, import_utilities3.log)(import_utilities3.colour.green, `Successfully changed location to: ${locationName}`);
    } catch (error) {
      (0, import_utilities3.log)(import_utilities3.colour.yellow, `Could not select location suggestion - using typed location: ${error}`);
    }
  } catch (error) {
    (0, import_utilities3.logError)(`Store location selection failed: ${error}`);
    (0, import_utilities3.log)(import_utilities3.colour.yellow, "Proceeding with default location");
  }
}
function playwrightElementToProduct(element, categories) {
  const $ = cheerio.load(element);
  let idNameSizeH3 = $(element).find("h3").filter((i, element2) => {
    if ($(element2).attr("id")?.includes("-title")) {
      return true;
    } else return false;
  });
  let product = {
    // ID
    // -------
    // Extract product ID from h3 id attribute, and remove non-numbers
    id: idNameSizeH3.attr("id")?.replace(/\D/g, ""),
    // Source Site - set where the source of information came from
    sourceSite: "countdown.co.nz",
    // use countdown for consistency with old data
    // Categories
    category: categories,
    // already obtained from url/text file
    // Store today's date
    lastChecked: /* @__PURE__ */ new Date(),
    lastUpdated: /* @__PURE__ */ new Date(),
    // These values will later be overwritten
    name: "",
    priceHistory: [],
    currentPrice: 0
  };
  let rawNameAndSize = idNameSizeH3.text().trim();
  rawNameAndSize = rawNameAndSize.toLowerCase().replace("  ", " ").replace("fresh fruit", "").replace("fresh vegetable", "").trim();
  let tryMatchSize = rawNameAndSize.match(/(tray\s\d+)|(\d+(\.\d+)?(\-\d+\.\d+)?\s?(g|kg|l|ml|pack))\b/g);
  if (!tryMatchSize) {
    product.name = (0, import_utilities3.toTitleCase)(rawNameAndSize);
    product.size = "";
  } else {
    let indexOfSizeSection = rawNameAndSize.indexOf(tryMatchSize[0]);
    product.name = (0, import_utilities3.toTitleCase)(rawNameAndSize.slice(0, indexOfSizeSection)).trim();
    let cleanedSize = rawNameAndSize.slice(indexOfSizeSection).trim();
    if (cleanedSize.match(/\d+l\b/)) {
      cleanedSize = cleanedSize.replace("l", "L");
    }
    cleanedSize.replace("tray", "Tray");
    product.size = cleanedSize;
  }
  const dollarString = $(element).find("product-price div h3 em").text().trim();
  let centString = $(element).find("product-price div h3 span").text().trim();
  if (centString.includes("kg")) product.size = "per kg";
  centString = centString.replace(/\D/g, "");
  product.currentPrice = Number(dollarString + "." + centString);
  const today = /* @__PURE__ */ new Date();
  today.setMinutes(0);
  today.setSeconds(0);
  const todaysDatedPrice = {
    date: today,
    price: product.currentPrice
  };
  product.priceHistory = [todaysDatedPrice];
  const rawUnitPrice = $(element).find("span.cupPrice").text().trim();
  if (rawUnitPrice) {
    const unitPriceString = rawUnitPrice.split("/")[0].replace("$", "").trim();
    let unitPrice = Number.parseFloat(unitPriceString);
    const amountAndUnit = rawUnitPrice.split("/")[1].trim();
    let amount = Number.parseInt(amountAndUnit.match(/\d+/g)?.[0] || "");
    let unit = amountAndUnit.match(/\w+/g)?.[0] || "";
    if (amountAndUnit == "100g") {
      amount = amount * 10;
      unitPrice = unitPrice * 10;
      unit = "kg";
    } else if (amountAndUnit == "100mL") {
      amount = amount * 10;
      unitPrice = unitPrice * 10;
      unit = "L";
    }
    unit = unit.replace("1kg", "kg");
    unit = unit.replace("1L", "L");
    product.unitPrice = unitPrice;
    product.unitName = unit;
  }
  import_product_overrides.productOverrides.forEach((override) => {
    if (override.id === product.id) {
      if (override.size !== void 0) {
        product.size = override.size;
      }
      if (override.category !== void 0) {
        product.category = [override.category];
      }
    }
  });
  if (validateProduct(product)) return product;
  else {
    try {
      (0, import_utilities3.logError)(
        `  Unable to Scrape: ${product.id.padStart(6)} | ${product.name} | $${product.currentPrice}`
      );
    } catch {
      (0, import_utilities3.logError)("  Unable to Scrape ID from product");
    }
    return void 0;
  }
}
function validateProduct(product) {
  try {
    if (product.name.match(/\$\s\d+/)) return false;
    if (product.name.length < 4 || product.name.length > 100) return false;
    if (product.id.length < 2 || product.id.length > 20) return false;
    if (product.currentPrice <= 0 || product.currentPrice === null || product.currentPrice === void 0 || Number.isNaN(product.currentPrice) || product.currentPrice > 999) {
      return false;
    }
    return true;
  } catch (error) {
    return false;
  }
}
function parseAndCategoriseURL(line) {
  let baseCategorisedURL = { url: "", categories: [] };
  let parsedUrls = [];
  let numPagesPerURL = 1;
  if (!line.includes("woolworths.co.nz")) {
    return void 0;
  } else if (line.includes("?search=")) {
    parsedUrls.push({ url: line, categories: [] });
  } else {
    line.split(" ").forEach((section) => {
      if (section.includes("woolworths.co.nz")) {
        baseCategorisedURL.url = section;
        if (!baseCategorisedURL.url.startsWith("http"))
          baseCategorisedURL.url = "https://" + section;
        if (section.includes("?")) {
          baseCategorisedURL.url = line.substring(0, line.indexOf("?"));
        }
        baseCategorisedURL.url += "?page=1";
      } else if (section.startsWith("categories=")) {
        let splitCategories = [section.replace("categories=", "")];
        if (section.includes(","))
          splitCategories = section.replace("categories=", "").split(",");
        baseCategorisedURL.categories = splitCategories;
      } else if (section.startsWith("pages=")) {
        numPagesPerURL = Number.parseInt(section.split("=")[1]);
      }
      if (baseCategorisedURL.categories.length === 0) {
        const baseUrl = baseCategorisedURL.url.split("?")[0];
        let slashSections = baseUrl.split("/");
        baseCategorisedURL.categories = [slashSections[slashSections.length - 1]];
      }
    });
    for (let i = 1; i <= numPagesPerURL; i++) {
      let pagedUrl = {
        url: baseCategorisedURL.url.replace("page=1", "page=" + i),
        categories: baseCategorisedURL.categories
      };
      parsedUrls.push(pagedUrl);
    }
  }
  return parsedUrls;
}
async function routePlaywrightExclusions() {
  let typeExclusions = ["image", "media", "font"];
  let urlExclusions = [
    "googleoptimize.com",
    "gtm.js",
    "visitoridentification.js",
    "js-agent.newrelic.com",
    "cquotient.com",
    "googletagmanager.com",
    "cloudflareinsights.com",
    "dwanalytics",
    "facebook.net",
    "chatWidget",
    "edge.adobedc.net",
    "\u200B/Content/Banners/",
    "go-mpulse.net"
  ];
  await page.route("**/*", async (route) => {
    const req = route.request();
    let excludeThisRequest = false;
    urlExclusions.forEach((excludedURL) => {
      if (req.url().includes(excludedURL)) excludeThisRequest = true;
    });
    typeExclusions.forEach((excludedType) => {
      if (req.resourceType() === excludedType) excludeThisRequest = true;
    });
    if (excludeThisRequest) {
      await route.abort();
    } else {
      await route.continue();
    }
  });
  return;
}
export {
  databaseMode,
  parseAndCategoriseURL,
  playwrightElementToProduct,
  uploadImagesMode
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsic3JjL3R5cGluZ3MuanMiLCAic3JjL3V0aWxpdGllcy5qcyIsICJzcmMvcHJvZHVjdC1vdmVycmlkZXMuanMiLCAic3JjL2luZGV4LnRzIiwgInNyYy9tb25nb2RiLnRzIiwgInNyYy9jb25zb2xpZGF0ZWQtcHJvZHVjdHMtbW9uZ29kYi50cyJdLAogICJzb3VyY2VSb290IjogIkM6XFxVc2Vyc1xcc2tlYW1cXE9uZURyaXZlXFxEZXNrdG9wXFxzY3JhcGVyczEzLTA3XFxXb29sd29ydGhzIiwKICAic291cmNlc0NvbnRlbnQiOiBbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuIiwgIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy52YWxpZENhdGVnb3JpZXMgPSBleHBvcnRzLmNvbG91ciA9IHZvaWQgMDtcbmV4cG9ydHMubG9nID0gbG9nO1xuZXhwb3J0cy5sb2dFcnJvciA9IGxvZ0Vycm9yO1xuZXhwb3J0cy5sb2dQcm9kdWN0Um93ID0gbG9nUHJvZHVjdFJvdztcbmV4cG9ydHMubG9nVGFibGVIZWFkZXIgPSBsb2dUYWJsZUhlYWRlcjtcbmV4cG9ydHMucmVhZExpbmVzRnJvbVRleHRGaWxlID0gcmVhZExpbmVzRnJvbVRleHRGaWxlO1xuZXhwb3J0cy5nZXRUaW1lRWxhcHNlZFNpbmNlID0gZ2V0VGltZUVsYXBzZWRTaW5jZTtcbmV4cG9ydHMudG9UaXRsZUNhc2UgPSB0b1RpdGxlQ2FzZTtcbmNvbnN0IGZzXzEgPSByZXF1aXJlKFwiZnNcIik7XG4vLyBTZXQgd2lkdGhzIGZvciB0YWJsZSBsb2cgb3V0cHV0XG5jb25zdCB0YWJsZUlEV2lkdGggPSA2O1xuY29uc3QgdGFibGVOYW1lV2lkdGggPSA2MDtcbmNvbnN0IHRhYmxlU2l6ZVdpZHRoID0gMTc7XG5leHBvcnRzLmNvbG91ciA9IHtcbiAgICByZWQ6ICdcXHgxYlszMW0nLFxuICAgIGdyZWVuOiAnXFx4MWJbMzJtJyxcbiAgICB5ZWxsb3c6ICdcXHgxYlszM20nLFxuICAgIGJsdWU6ICdcXHgxYlszODs1OzExN20nLFxuICAgIG1hZ2VudGE6ICdcXHgxYlszNW0nLFxuICAgIGN5YW46ICdcXHgxYlszNm0nLFxuICAgIHdoaXRlOiAnXFx4MWJbMzdtJyxcbiAgICBjcmltc29uOiAnXFx4MWJbMzhtJyxcbiAgICBncmV5OiAnXFx4MWJbOTBtJyxcbiAgICBvcmFuZ2U6ICdcXHgxYlszODs1OzIxNG0nLFxuICAgIHNreTogJ1xceDFiWzM4OzU7MTUzbScsXG59O1xuLy8gbG9nKClcbi8vIC0tLS0tXG4vLyBDb25zb2xlIGxvZyB3aXRoIHNwZWNpZmllZCBjb2xvdXJcbmZ1bmN0aW9uIGxvZyhjb2xvdXIsIHRleHQpIHtcbiAgICBjb25zdCBjbGVhciA9ICdcXHgxYlswbSc7XG4gICAgY29uc29sZS5sb2coYCR7Y29sb3VyfSVzJHtjbGVhcn1gLCB0ZXh0KTtcbn1cbi8vIGxvZ0Vycm9yKClcbi8vIC0tLS0tLS0tLS1cbi8vIFNob3J0aGFuZCBmdW5jdGlvbiBmb3IgbG9nZ2luZyB3aXRoIHJlZCBjb2xvdXJcbmZ1bmN0aW9uIGxvZ0Vycm9yKHRleHQpIHtcbiAgICBsb2coZXhwb3J0cy5jb2xvdXIucmVkLCB0ZXh0KTtcbn1cbi8vIGxvZ1Byb2R1Y3RSb3coKVxuLy8gLS0tLS0tLS0tLS0tLS0tXG4vLyBMb2cgYSBzaW5nbGUgcHJvZHVjdCBpbiBvbmUgcm93LCB1c2luZyBhbHRlcm5hdGluZyBjb2xvdXJzIGZvciByZWFkYWJpbGl0eS5cbmZ1bmN0aW9uIGxvZ1Byb2R1Y3RSb3cocHJvZHVjdCkge1xuICAgIHZhciBfYTtcbiAgICBjb25zdCB1bml0UHJpY2VTdHJpbmcgPSBwcm9kdWN0LnVuaXRQcmljZSA/IGAkJHtwcm9kdWN0LnVuaXRQcmljZS50b0ZpeGVkKDIpfSAvJHtwcm9kdWN0LnVuaXROYW1lfWAgOiBgYDtcbiAgICBsb2coZ2V0QWx0ZXJuYXRpbmdSb3dDb2xvdXIoZXhwb3J0cy5jb2xvdXIuc2t5LCBleHBvcnRzLmNvbG91ci53aGl0ZSksIGAke3Byb2R1Y3QuaWQucGFkU3RhcnQodGFibGVJRFdpZHRoKX0gfCBgICtcbiAgICAgICAgYCR7cHJvZHVjdC5uYW1lLnNsaWNlKDAsIHRhYmxlTmFtZVdpZHRoKS5wYWRFbmQodGFibGVOYW1lV2lkdGgpfSB8IGAgK1xuICAgICAgICBgJHsoX2EgPSBwcm9kdWN0LnNpemUpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5zbGljZSgwLCB0YWJsZVNpemVXaWR0aCkucGFkRW5kKHRhYmxlU2l6ZVdpZHRoKX0gfCBgICtcbiAgICAgICAgYCQgJHtwcm9kdWN0LmN1cnJlbnRQcmljZS50b0ZpeGVkKDIpLnBhZFN0YXJ0KDQpLnBhZEVuZCg1KX0gfCBgICtcbiAgICAgICAgdW5pdFByaWNlU3RyaW5nKTtcbn1cbi8vIGxvZ1RhYmxlSGVhZGVyKClcbi8vIC0tLS0tLS0tLS0tLS0tLS1cbmZ1bmN0aW9uIGxvZ1RhYmxlSGVhZGVyKCkge1xuICAgIGxvZyhleHBvcnRzLmNvbG91ci55ZWxsb3csIGAkeydJRCcucGFkU3RhcnQodGFibGVJRFdpZHRoKX0gfCAkeydOYW1lJy5wYWRFbmQodGFibGVOYW1lV2lkdGgpfSB8IGAgK1xuICAgICAgICBgJHsnU2l6ZScucGFkRW5kKHRhYmxlU2l6ZVdpZHRoKX0gfCBgICtcbiAgICAgICAgYCR7J1ByaWNlJy5wYWRFbmQoNyl9IHwgVW5pdCBQcmljZWApO1xuICAgIGxldCBoZWFkZXJMaW5lID0gXCJcIjtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IDExMTsgaSsrKSB7XG4gICAgICAgIGhlYWRlckxpbmUgKz0gXCItXCI7XG4gICAgfVxuICAgIGxvZyhleHBvcnRzLmNvbG91ci55ZWxsb3csIGhlYWRlckxpbmUpO1xufVxuLy8gZ2V0QWx0ZXJuYXRpbmdSb3dDb2xvdXIoKVxuLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuLy8gVGFrZXMgMiBjb2xvdXJzIGFuZCBmbGlwLWZsb3BzIGJldHdlZW4gdGhlbSBvbiBlYWNoIGZ1bmN0aW9uIGNhbGwuXG4vLyBJcyB1c2VkIGZvciBwcmludGluZyB0YWJsZXMgd2l0aCBiZXR0ZXIgcmVhZGFiaWxpdHkuXG5sZXQgYWx0ZXJuYXRpbmdSb3dDb2xvdXIgPSBmYWxzZTtcbmZ1bmN0aW9uIGdldEFsdGVybmF0aW5nUm93Q29sb3VyKGNvbG91ckEsIGNvbG91ckIpIHtcbiAgICBhbHRlcm5hdGluZ1Jvd0NvbG91ciA9IGFsdGVybmF0aW5nUm93Q29sb3VyID8gZmFsc2UgOiB0cnVlO1xuICAgIHJldHVybiBhbHRlcm5hdGluZ1Jvd0NvbG91ciA/IGNvbG91ckEgOiBjb2xvdXJCO1xufVxuLy8gcmVhZExpbmVzRnJvbVRleHRGaWxlKClcbi8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4vLyBSZWFkIGZyb20gbG9jYWwgdGV4dCBmaWxlIGNvbnRhaW5pbmcgb25lIHVybCBwZXIgbGluZSwgcmV0dXJuIGFzIHN0cmluZyBhcnJheS5cbmZ1bmN0aW9uIHJlYWRMaW5lc0Zyb21UZXh0RmlsZShmaWxlbmFtZSkge1xuICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IGZpbGUgPSAoMCwgZnNfMS5yZWFkRmlsZVN5bmMpKGZpbGVuYW1lLCAndXRmLTgnKTtcbiAgICAgICAgY29uc3QgcmVzdWx0ID0gZmlsZS5zcGxpdCgvXFxyP1xcbi8pLmZpbHRlcigobGluZSkgPT4ge1xuICAgICAgICAgICAgaWYgKGxpbmUudHJpbSgpLmxlbmd0aCA+IDApXG4gICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgICAgICBlbHNlXG4gICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuIHJlc3VsdDtcbiAgICB9XG4gICAgY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIHRocm93ICdFcnJvciByZWFkaW5nICcgKyBmaWxlbmFtZTtcbiAgICB9XG59XG4vLyBnZXRUaW1lRWxhcHNlZFNpbmNlKClcbi8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuLy8gR2V0IHRpbWUgZGlmZmVyZW5jZSBpbiBiZXR3ZWVuIHN0YXJ0VGltZSBhbmQgbm93LiBSZXR1cm5zIGluIDU4cyBvciAxMjozMiBmb3JtYXQuXG5mdW5jdGlvbiBnZXRUaW1lRWxhcHNlZFNpbmNlKHN0YXJ0VGltZSkge1xuICAgIGxldCBlbGFwc2VkVGltZVNlY29uZHMgPSAoRGF0ZS5ub3coKSAtIHN0YXJ0VGltZSkgLyAxMDAwO1xuICAgIGxldCBlbGFwc2VkVGltZVN0cmluZyA9IE1hdGguZmxvb3IoZWxhcHNlZFRpbWVTZWNvbmRzKS50b1N0cmluZygpO1xuICAgIC8vIElmIG92ZXIgNjAgc2VjcywgcHJpbnQgYXMgMToyMyBmb3JtYXRcbiAgICBpZiAoZWxhcHNlZFRpbWVTZWNvbmRzID49IDYwKSB7XG4gICAgICAgIHJldHVybiAoTWF0aC5mbG9vcihlbGFwc2VkVGltZVNlY29uZHMgLyA2MCkgK1xuICAgICAgICAgICAgJzonICtcbiAgICAgICAgICAgIE1hdGguZmxvb3IoZWxhcHNlZFRpbWVTZWNvbmRzICUgNjApXG4gICAgICAgICAgICAgICAgLnRvU3RyaW5nKClcbiAgICAgICAgICAgICAgICAucGFkU3RhcnQoMiwgJzAnKSk7XG4gICAgICAgIC8vIEVsc2UgcHJpbnQgaW4gNDBzIGZvcm1hdFxuICAgIH1cbiAgICBlbHNlXG4gICAgICAgIHJldHVybiBlbGFwc2VkVGltZVN0cmluZyArIFwic1wiO1xufVxuLy8gTGlzdCBvZiB2YWxpZCBjYXRlZ29yeSBuYW1lcyB0aGF0IHNjcmFwZWQgcHJvZHVjdHMgc2hvdWxkIGJlIHB1dCBpblxuZXhwb3J0cy52YWxpZENhdGVnb3JpZXMgPSBbXG4gICAgLy8gZnJlc2hDYXRlZ29yeVxuICAgICdlZ2dzJyxcbiAgICAnZnJ1aXQnLFxuICAgICdmcmVzaC12ZWdldGFibGVzJyxcbiAgICAnc2FsYWRzLWNvbGVzbGF3JyxcbiAgICAnYnJlYWQnLFxuICAgICdicmVhZC1yb2xscycsXG4gICAgJ3NwZWNpYWx0eS1icmVhZCcsXG4gICAgJ2Jha2VyeS1jYWtlcycsXG4gICAgJ2Jha2VyeS1kZXNzZXJ0cycsXG4gICAgLy8gY2hpbGxlZENhdGVnb3J5XG4gICAgJ21pbGsnLFxuICAgICdsb25nLWxpZmUtbWlsaycsXG4gICAgJ3NvdXItY3JlYW0nLFxuICAgICdjcmVhbScsXG4gICAgJ3lvZ2h1cnQnLFxuICAgICdidXR0ZXInLFxuICAgICdjaGVlc2UnLFxuICAgICdjaGVlc2Utc2xpY2VzJyxcbiAgICAnc2FsYW1pJyxcbiAgICAnb3RoZXItZGVsaS1mb29kcycsXG4gICAgLy8gbWVhdENhdGVnb3J5XG4gICAgJ2JlZWYtbGFtYicsXG4gICAgJ2NoaWNrZW4nLFxuICAgICdoYW0nLFxuICAgICdiYWNvbicsXG4gICAgJ3BvcmsnLFxuICAgICdwYXR0aWVzLW1lYXRiYWxscycsXG4gICAgJ3NhdXNhZ2VzJyxcbiAgICAnZGVsaS1tZWF0cycsXG4gICAgJ21lYXQtYWx0ZXJuYXRpdmVzJyxcbiAgICAnc2VhZm9vZCcsXG4gICAgJ3NhbG1vbicsXG4gICAgLy8gZnJvemVuQ2F0ZWdvcnlcbiAgICAnaWNlLWNyZWFtJyxcbiAgICAnaWNlLWJsb2NrcycsXG4gICAgJ3Bhc3RyaWVzLWNoZWVzZWNha2UnLFxuICAgICdmcm96ZW4tY2hpcHMnLFxuICAgICdmcm96ZW4tdmVnZXRhYmxlcycsXG4gICAgJ2Zyb3plbi1mcnVpdCcsXG4gICAgJ2Zyb3plbi1zZWFmb29kJyxcbiAgICAncGllcy1zYXVzYWdlLXJvbGxzJyxcbiAgICAncGl6emEnLFxuICAgICdvdGhlci1zYXZvdXJpZXMnLFxuICAgIC8vIHBhbnRyeUNhdGVnb3J5XG4gICAgJ3JpY2UnLFxuICAgICdub29kbGVzJyxcbiAgICAncGFzdGEnLFxuICAgICdiZWFucy1zcGFnaGV0dGknLFxuICAgICdjYW5uZWQtZmlzaCcsXG4gICAgJ2Nhbm5lZC1tZWF0JyxcbiAgICAnc291cCcsXG4gICAgJ2NlcmVhbCcsXG4gICAgJ3NwcmVhZHMnLFxuICAgICdiYWtpbmcnLFxuICAgICdzYXVjZXMnLFxuICAgICdvaWxzLXZpbmVnYXJzJyxcbiAgICAnd29ybGQtZm9vZHMnLFxuICAgIC8vIHNuYWNrc0NhdGVnb3J5XG4gICAgJ2Nob2NvbGF0ZScsXG4gICAgJ2JveGVkLWNob2NvbGF0ZScsXG4gICAgJ2NoaXBzJyxcbiAgICAnY3JhY2tlcnMnLFxuICAgICdiaXNjdWl0cycsXG4gICAgJ211ZXNsaS1iYXJzJyxcbiAgICAnbnV0cy1idWxrLW1peCcsXG4gICAgJ3N3ZWV0cy1sb2xsaWVzJyxcbiAgICAnb3RoZXItc25hY2tzJyxcbiAgICAvLyBkcmlua3NDYXRlZ29yeVxuICAgICdibGFjay10ZWEnLFxuICAgICdncmVlbi10ZWEnLFxuICAgICdoZXJiYWwtdGVhJyxcbiAgICAnZHJpbmtpbmctY2hvY29sYXRlJyxcbiAgICAnY29mZmVlJyxcbiAgICAnc29mdC1kcmlua3MnLFxuICAgICdlbmVyZ3ktZHJpbmtzJyxcbiAgICAnanVpY2UnLFxuICAgIC8vIHBldHNDYXRlZ29yeVxuICAgICdjYXQtZm9vZCcsXG4gICAgJ2NhdC10cmVhdHMnLFxuICAgICdkb2ctZm9vZCcsXG4gICAgJ2RvZy10cmVhdHMnLFxuXTtcbi8vIHRvVGl0bGVDYXNlKClcbi8vIC0tLS0tLS0tLS0tLS1cbi8vIENvbnZlcnQgYSBzdHJpbmcgdG8gdGl0bGUgY2FzZVxuZnVuY3Rpb24gdG9UaXRsZUNhc2Uoc3RyKSB7XG4gICAgcmV0dXJuIHN0ci5yZXBsYWNlKC9cXHdcXFMqL2csIGZ1bmN0aW9uICh0eHQpIHtcbiAgICAgICAgcmV0dXJuIHR4dC5jaGFyQXQoMCkudG9VcHBlckNhc2UoKSArIHR4dC5zdWJzdHJpbmcoMSkudG9Mb3dlckNhc2UoKTtcbiAgICB9KTtcbn1cbiIsICJcInVzZSBzdHJpY3RcIjtcbi8vIFRoaXMgZmlsZSBpcyBmb3IgbWFudWFsbHkgb3ZlcnJpZGluZyBwcm9kdWN0IHNpemUgYW5kIGNhdGVnb3J5IGRhdGEuXG4vLyBJcyB1c2VkIGZvciBwcm9kdWN0cyB0aGF0IGRvIG5vdCBoYXZlIGxpc3RlZCBzaXplcyB0byBiZSBzY3JhcGVkLCBvciBoYXZlIGluY29ycmVjdCBjYXRlZ29yaWVzLlxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5wcm9kdWN0T3ZlcnJpZGVzID0gdm9pZCAwO1xuZXhwb3J0cy5wcm9kdWN0T3ZlcnJpZGVzID0gW1xuICAgIHsgaWQ6ICcyMDY4ODknLCBzaXplOiAnMTgwZycgfSxcbiAgICB7IGlkOiAnMTk2OTk2Jywgc2l6ZTogJzMwMGcnIH0sXG4gICAgeyBpZDogJzEzNzk2NycsIHNpemU6ICc0MjBnJyB9LFxuICAgIHsgaWQ6ICcxMjU4NTYnLCBzaXplOiAnNDUwZycgfSxcbiAgICB7IGlkOiAnMTg5MjY4Jywgc2l6ZTogJzEuMTNrZycgfSxcbiAgICB7IGlkOiAnMTg5MTUwJywgc2l6ZTogJzEuMmtnJyB9LFxuICAgIHsgaWQ6ICcxOTA0NTQnLCBzaXplOiAnMi4xa2cnIH0sXG4gICAgeyBpZDogJzE4OTA3OCcsIHNpemU6ICcxLjNrZycgfSxcbiAgICB7IGlkOiAnMTg5MTM2Jywgc2l6ZTogJzEuMmtnJyB9LFxuICAgIHsgaWQ6ICc3NTUyMzcnLCBzaXplOiAnOTMxZycgfSxcbiAgICB7IGlkOiAnNzU1MzA0Jywgc2l6ZTogJzEuMWtnJyB9LFxuICAgIHsgaWQ6ICc3NTUyNDYnLCBzaXplOiAnMTAyMGcnIH0sXG4gICAgeyBpZDogJzc1NTI0NScsIHNpemU6ICcxLjJrZycgfSxcbiAgICB7IGlkOiAnMTEyMjczJywgc2l6ZTogJzg2NW1sJyB9LFxuICAgIHsgaWQ6ICcyNjk1MTQnLCBzaXplOiAnNTg0bWwnIH0sXG4gICAgeyBpZDogJzI2OTUxNScsIHNpemU6ICc1ODRtbCcgfSxcbiAgICB7IGlkOiAnMTE2NTE4Jywgc2l6ZTogJzQ0MG1sJyB9LFxuICAgIHsgaWQ6ICcxNTExOTEnLCBzaXplOiAnNTcwbWwnIH0sXG4gICAgeyBpZDogJzI3OTkwNCcsIHNpemU6ICc1NzVtbCcgfSxcbiAgICB7IGlkOiAnMTQ2MTQ5Jywgc2l6ZTogJzEwMDBtbCcgfSxcbiAgICB7IGlkOiAnNzkxOTI1Jywgc2l6ZTogJzUyNWcnIH0sXG4gICAgeyBpZDogJzc3NDIxNicsIHNpemU6ICc1MjVnJyB9LFxuICAgIHsgaWQ6ICc3ODQ0MDYnLCBzaXplOiAnNTI1ZycgfSxcbiAgICB7IGlkOiAnNzkxOTE2Jywgc2l6ZTogJzUyNWcnIH0sXG4gICAgeyBpZDogJzMwNjYyNCcsIHNpemU6ICcxODVnJyB9LFxuICAgIHsgaWQ6ICcxNTY4MjQnLCBzaXplOiAnMTgwZycgfSxcbiAgICB7IGlkOiAnOTAyMycsIHNpemU6ICczNzVnJyB9LFxuICAgIHsgaWQ6ICcyNjY5NjInLCBjYXRlZ29yeTogJ3N3ZWV0cy1sb2xsaWVzJyB9LFxuICAgIHsgaWQ6ICcxNzE1MjQnLCBzaXplOiAnMjMwbWwnLCBjYXRlZ29yeTogJ2Jha2luZycgfSxcbiAgICB7IGlkOiAnMTcwMDIxJywgY2F0ZWdvcnk6ICdpY2UtYmxvY2tzJyB9LFxuICAgIHsgaWQ6ICc3MTE2NCcsIGNhdGVnb3J5OiAnc2F1c2FnZXMnIH0sXG4gICAgeyBpZDogJzcxMTc0JywgY2F0ZWdvcnk6ICdzYXVzYWdlcycgfSxcbiAgICB7IGlkOiAnNzExNjgnLCBjYXRlZ29yeTogJ3NhdXNhZ2VzJyB9LFxuICAgIHsgaWQ6ICc3MTE2NScsIGNhdGVnb3J5OiAnc2F1c2FnZXMnIH0sXG4gICAgeyBpZDogJzMzMTU2MCcsIGNhdGVnb3J5OiAnc3BlY2lhbHR5LWJyZWFkJyB9LFxuICAgIHsgaWQ6ICc2Nzk0MTInLCBjYXRlZ29yeTogJ2hlcmJhbC10ZWEnIH0sXG4gICAgeyBpZDogJzc5MDEyOScsIGNhdGVnb3J5OiAnaGVyYmFsLXRlYScgfSxcbiAgICB7IGlkOiAnMjY3NDkyJywgY2F0ZWdvcnk6ICdoZXJiYWwtdGVhJyB9LFxuICAgIHsgaWQ6ICcyNjc0ODUnLCBjYXRlZ29yeTogJ2hlcmJhbC10ZWEnIH0sXG4gICAgeyBpZDogJzQxMzMwMicsIGNhdGVnb3J5OiAnaGVyYmFsLXRlYScgfSxcbiAgICB7IGlkOiAnMjY3NDg4JywgY2F0ZWdvcnk6ICdoZXJiYWwtdGVhJyB9LFxuICAgIHsgaWQ6ICc3NjA4NzInLCBjYXRlZ29yeTogJ2hlcmJhbC10ZWEnIH0sXG4gICAgeyBpZDogJzY4MTE3NycsIGNhdGVnb3J5OiAnaGVyYmFsLXRlYScgfSxcbiAgICB7IGlkOiAnOTUwOTEnLCBjYXRlZ29yeTogJ2hlcmJhbC10ZWEnIH0sXG4gICAgeyBpZDogJzc2MTA5MycsIGNhdGVnb3J5OiAnYmxhY2stdGVhJyB9LFxuICAgIHsgaWQ6ICc3MjE2NjEnLCBjYXRlZ29yeTogJ2dyZWVuLXRlYScgfSxcbiAgICB7IGlkOiAnNzkwMTI5JywgY2F0ZWdvcnk6ICdoZXJiYWwtdGVhJyB9LFxuICAgIHsgaWQ6ICcyNjc0OTInLCBjYXRlZ29yeTogJ2hlcmJhbC10ZWEnIH0sXG4gICAgeyBpZDogJzI2NzQ4NScsIGNhdGVnb3J5OiAnaGVyYmFsLXRlYScgfSxcbiAgICB7IGlkOiAnNzIxMDM0JywgY2F0ZWdvcnk6ICdoZXJiYWwtdGVhJyB9LFxuICAgIHsgaWQ6ICc0MTMzMDInLCBjYXRlZ29yeTogJ2hlcmJhbC10ZWEnIH0sXG4gICAgeyBpZDogJzI2NzQ4OCcsIGNhdGVnb3J5OiAnaGVyYmFsLXRlYScgfSxcbiAgICB7IGlkOiAnNzYwODcyJywgY2F0ZWdvcnk6ICdoZXJiYWwtdGVhJyB9LFxuICAgIHsgaWQ6ICc2ODExNzcnLCBjYXRlZ29yeTogJ2hlcmJhbC10ZWEnIH0sXG4gICAgeyBpZDogJzk1MDkxLicsIGNhdGVnb3J5OiAnaGVyYmFsLXRlYScgfSxcbiAgICB7IGlkOiAnMTg0MDkwJywgY2F0ZWdvcnk6ICdoZXJiYWwtdGVhJyB9LFxuICAgIHsgaWQ6ICc3NjEwOTMnLCBjYXRlZ29yeTogJ2JsYWNrLXRlYScgfSxcbiAgICB7IGlkOiAnNzIxNjYxJywgY2F0ZWdvcnk6ICdncmVlbi10ZWEnIH0sXG4gICAgeyBpZDogJzY5MDA5MycsIGNhdGVnb3J5OiAnZ3JlZW4tdGVhJyB9LFxuICAgIHsgaWQ6ICc3ODA5MjInLCBjYXRlZ29yeTogJ3NhdWNlcycgfSxcbiAgICB7IGlkOiAnNzgwOTIxJywgY2F0ZWdvcnk6ICdzYXVjZXMnIH0sXG4gICAgeyBpZDogJzcyNjE4JywgY2F0ZWdvcnk6ICdibGFjay10ZWEnIH0sXG4gICAgeyBpZDogJzYwNTMnLCBjYXRlZ29yeTogJ2JsYWNrLXRlYScgfSxcbiAgICB7IGlkOiAnNzI2MTcnLCBjYXRlZ29yeTogJ2JsYWNrLXRlYScgfSxcbiAgICB7IGlkOiAnMTY4MDY4JywgY2F0ZWdvcnk6ICdibGFjay10ZWEnIH0sXG4gICAgeyBpZDogJzYwNTInLCBjYXRlZ29yeTogJ2JsYWNrLXRlYScgfSxcbiAgICB7IGlkOiAnNzYxNDM2JywgY2F0ZWdvcnk6ICdibGFjay10ZWEnIH0sXG5dO1xuIiwgImltcG9ydCAqIGFzIGRvdGVudiBmcm9tIFwiZG90ZW52XCI7XG5kb3RlbnYuY29uZmlnKCk7XG5kb3RlbnYuY29uZmlnKHsgcGF0aDogYC5lbnYubG9jYWxgLCBvdmVycmlkZTogdHJ1ZSB9KTtcblxuaW1wb3J0IHBsYXl3cmlnaHQgZnJvbSBcInBsYXl3cmlnaHRcIjtcbmltcG9ydCAqIGFzIGNoZWVyaW8gZnJvbSBcImNoZWVyaW9cIjtcbmltcG9ydCBfIGZyb20gXCJsb2Rhc2hcIjtcbmltcG9ydCB7IHNldFRpbWVvdXQgfSBmcm9tIFwidGltZXJzL3Byb21pc2VzXCI7XG5cbmltcG9ydCB7IGVzdGFibGlzaE1vbmdvREIsIHVwc2VydFByb2R1Y3RUb01vbmdvREIsIHVwbG9hZEltYWdlVG9Nb25nb0RCLCBjbG9zZU1vbmdvREIgfSBmcm9tIFwiLi9tb25nb2RiLmpzXCI7XG5pbXBvcnQgeyBpbml0aWFsaXplQ29uc29saWRhdGVkUHJvZHVjdHNNb25nb0RCLCBwcm9jZXNzQ29uc29saWRhdGVkUHJvZHVjdE1vbmdvREIsIGNsb3NlQ29uc29saWRhdGVkUHJvZHVjdHNNb25nb0RCIH0gZnJvbSBcIi4vY29uc29saWRhdGVkLXByb2R1Y3RzLW1vbmdvZGIuanNcIjtcbmltcG9ydCB7IHByb2R1Y3RPdmVycmlkZXMgfSBmcm9tIFwiLi9wcm9kdWN0LW92ZXJyaWRlcy5qc1wiO1xuaW1wb3J0IHsgQ2F0ZWdvcmlzZWRVcmwsIERhdGVkUHJpY2UsIFByb2R1Y3QsIFVwc2VydFJlc3BvbnNlIH0gZnJvbSBcIi4vdHlwaW5nc1wiO1xuaW1wb3J0IHtcbiAgbG9nLCBjb2xvdXIsIGxvZ1Byb2R1Y3RSb3csIGxvZ0Vycm9yLCByZWFkTGluZXNGcm9tVGV4dEZpbGUsIGdldFRpbWVFbGFwc2VkU2luY2UsXG4gIGxvZ1RhYmxlSGVhZGVyLCB0b1RpdGxlQ2FzZSxcbn0gZnJvbSBcIi4vdXRpbGl0aWVzLmpzXCI7XG5cbi8vIFdvb2x3b3J0aHMgLyBDb3VudGRvd24gU2NyYXBlclxuLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4vLyBTY3JhcGVzIHByaWNpbmcgYW5kIG90aGVyIGluZm8gZnJvbSBXb29sd29ydGhzIE5aJ3Mgd2Vic2l0ZS5cblxuLy8gU2V0IGEgcmVhc29uYWJsZSBkZWxheSBiZXR3ZWVuIGVhY2ggcGFnZSBsb2FkIHRvIHJlZHVjZSBsb2FkIG9uIHRoZSBzZXJ2ZXIuXG5jb25zdCBwYWdlTG9hZERlbGF5U2Vjb25kcyA9IDc7XG5cbi8vIFNldCBhIGRlbGF5IHdoZW4gbG9nZ2luZyBlYWNoIHByb2R1Y3QgcGVyIHBhZ2UgdG8gdGhlIGNvbnNvbGUuXG5jb25zdCBwcm9kdWN0TG9nRGVsYXlNaWxsaVNlY29uZHMgPSAyMDtcblxuLy8gUmVjb3JkIHN0YXJ0IHRpbWUgZm9yIGxvZ2dpbmcgcHVycG9zZXNcbmNvbnN0IHN0YXJ0VGltZSA9IERhdGUubm93KCk7XG5cbi8vIExvYWQgVVJMcyBmcm9tIHRleHQgZmlsZSAndXJscy50eHQnXG5sZXQgY2F0ZWdvcmlzZWRVcmxzOiBDYXRlZ29yaXNlZFVybFtdID0gbG9hZFVybHNGaWxlKCk7XG5cbi8vIEhhbmRsZSBjb21tYW5kLWxpbmUgYXJndW1lbnRzLCBleDogJ2RiJywgJ2ltYWdlcycsIG9yIHNpbmdsZSB1cmxzXG5leHBvcnQgbGV0IGRhdGFiYXNlTW9kZSA9IGZhbHNlO1xuZXhwb3J0IGxldCB1cGxvYWRJbWFnZXNNb2RlID0gZmFsc2U7XG5sZXQgaGVhZGxlc3NNb2RlID0gdHJ1ZTtcbmNhdGVnb3Jpc2VkVXJscyA9IGF3YWl0IGhhbmRsZUFyZ3VtZW50cyhjYXRlZ29yaXNlZFVybHMpO1xuXG4vLyBFc3RhYmxpc2ggTW9uZ29EQiBpZiBiZWluZyB1c2VkXG5pZiAoZGF0YWJhc2VNb2RlKSB7XG4gIGF3YWl0IGVzdGFibGlzaE1vbmdvREIoKTtcbiAgXG4gIC8vIEluaXRpYWxpemUgY29uc29saWRhdGVkIHByb2R1Y3RzIHN5c3RlbVxuICBhd2FpdCBpbml0aWFsaXplQ29uc29saWRhdGVkUHJvZHVjdHNNb25nb0RCKCk7XG59XG5cbi8vIEVzdGFibGlzaCBwbGF5d3JpZ2h0IGJyb3dzZXJcbmxldCBicm93c2VyOiBwbGF5d3JpZ2h0LkJyb3dzZXI7XG5sZXQgcGFnZTogcGxheXdyaWdodC5QYWdlO1xuYnJvd3NlciA9IGF3YWl0IGVzdGFibGlzaFBsYXl3cmlnaHRQYWdlKGhlYWRsZXNzTW9kZSk7XG5cbi8vIFNlbGVjdCBzdG9yZSBsb2NhdGlvbiAod2l0aCBmYWxsYmFjaylcbmlmIChwcm9jZXNzLmVudi5TS0lQX1NUT1JFX1NFTEVDVElPTiAhPT0gJ3RydWUnKSB7XG4gIHRyeSB7XG4gICAgYXdhaXQgc2VsZWN0U3RvcmVCeUxvY2F0aW9uTmFtZSgpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGxvZ0Vycm9yKGBTdG9yZSBzZWxlY3Rpb24gZmFpbGVkOiAke2Vycm9yfWApO1xuICAgIGxvZyhjb2xvdXIueWVsbG93LCBcIkNvbnRpbnVpbmcgd2l0aCBkZWZhdWx0IHN0b3JlIGxvY2F0aW9uLi4uXCIpO1xuICB9XG59IGVsc2Uge1xuICBsb2coY29sb3VyLnllbGxvdywgXCJTdG9yZSBzZWxlY3Rpb24gc2tpcHBlZCAoU0tJUF9TVE9SRV9TRUxFQ1RJT049dHJ1ZSlcIik7XG59XG5cbi8vIE1haW4gTG9vcCAtIFNjcmFwZSB0aHJvdWdoIGVhY2ggcGFnZVxuYXdhaXQgc2NyYXBlQWxsUGFnZVVSTHMoKTtcblxuLy8gUHJvZ3JhbSBFbmQgYW5kIENsZWFudXBcbmF3YWl0IGJyb3dzZXIuY2xvc2UoKTtcbmlmIChkYXRhYmFzZU1vZGUpIHtcbiAgYXdhaXQgY2xvc2VDb25zb2xpZGF0ZWRQcm9kdWN0c01vbmdvREIoKTtcbiAgYXdhaXQgY2xvc2VNb25nb0RCKCk7XG59XG5sb2coXG4gIGNvbG91ci5za3ksXG4gIGBcXG5BbGwgUGFnZXMgQ29tcGxldGVkID0gVG90YWwgVGltZSBFbGFwc2VkICR7Z2V0VGltZUVsYXBzZWRTaW5jZShzdGFydFRpbWUpfSBcXG5gXG4pO1xuLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cblxuXG4vLyBsb2FkVXJsc0ZpbGVcbi8vIC0tLS0tLS0tLS0tLVxuLy8gTG9hZHMgYW5kIHZhbGlkYXRlcyBVUkxzIGZyb20gYSB0eHQgZmlsZSB0byBiZSBzY3JhcGVkLlxuXG5mdW5jdGlvbiBsb2FkVXJsc0ZpbGUoZmlsZVBhdGg6IHN0cmluZyA9IFwic3JjL3VybHMudHh0XCIpOiBDYXRlZ29yaXNlZFVybFtdIHtcbiAgLy8gVHJ5IHRvIHJlYWQgZmlsZSB1cmxzLnR4dCBvciBvdGhlciBmaWxlIGZvciBhIGxpc3Qgb2YgVVJMc1xuICBjb25zdCByYXdMaW5lc0Zyb21GaWxlOiBzdHJpbmdbXSA9IHJlYWRMaW5lc0Zyb21UZXh0RmlsZShmaWxlUGF0aCk7XG5cbiAgLy8gUGFyc2UgYW5kIG9wdGltaXNlIFVSTHNcbiAgbGV0IGNhdGVnb3Jpc2VkVXJsczogQ2F0ZWdvcmlzZWRVcmxbXSA9IFtdO1xuICByYXdMaW5lc0Zyb21GaWxlLm1hcCgobGluZSkgPT4ge1xuICAgIGxldCBwYXJzZWRVcmxzID0gcGFyc2VBbmRDYXRlZ29yaXNlVVJMKGxpbmUpO1xuICAgIGlmIChwYXJzZWRVcmxzICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIGNhdGVnb3Jpc2VkVXJscyA9IFsuLi5jYXRlZ29yaXNlZFVybHMsIC4uLnBhcnNlZFVybHNdO1xuICAgIH1cbiAgfSk7XG5cbiAgLy8gUmV0dXJuIGFzIGFuIGFycmF5IG9mIENhdGVnb3Jpc2VkVXJsIG9iamVjdHNcbiAgcmV0dXJuIGNhdGVnb3Jpc2VkVXJscztcbn1cblxuLy8gc2NyYXBlQWxsUGFnZVVSTHNcbi8vIC0tLS0tLS0tLS0tLS0tLVxuLy8gTG9vcHMgdGhyb3VnaCBlYWNoIHBhZ2UgVVJMIGFuZCBzY3JhcGVzIHByaWNpbmcgYW5kIG90aGVyIGluZm8uXG4vLyBUaGlzIGlzIHRoZSBtYWluIGZ1bmN0aW9uIHRoYXQgY2FsbHMgdGhlIG90aGVyIGZ1bmN0aW9ucy5cblxuYXN5bmMgZnVuY3Rpb24gc2NyYXBlQWxsUGFnZVVSTHMoKSB7XG5cbiAgLy8gTG9nIGxvb3Agc3RhcnRcbiAgbG9nKFxuICAgIGNvbG91ci55ZWxsb3csXG4gICAgYCR7Y2F0ZWdvcmlzZWRVcmxzLmxlbmd0aH0gcGFnZXMgdG8gYmUgc2NyYXBlZGAucGFkRW5kKDM1KSArXG4gICAgYCR7cGFnZUxvYWREZWxheVNlY29uZHN9cyBkZWxheSBiZXR3ZWVuIHNjcmFwZXNgLnBhZEVuZCgzNSkgK1xuICAgIChkYXRhYmFzZU1vZGUgPyBcIihEYXRhYmFzZSBNb2RlKVwiIDogXCIoRHJ5IFJ1biBNb2RlKVwiKVxuICApO1xuXG4gIC8vIExvb3AgdGhyb3VnaCBlYWNoIHBhZ2UgVVJMIHRvIHNjcmFwZVxuICBmb3IgKGxldCBpID0gMDsgaSA8IGNhdGVnb3Jpc2VkVXJscy5sZW5ndGg7IGkrKykge1xuICAgIGNvbnN0IGNhdGVnb3Jpc2VkVXJsOiBDYXRlZ29yaXNlZFVybCA9IGNhdGVnb3Jpc2VkVXJsc1tpXTtcbiAgICBsZXQgdXJsOiBzdHJpbmcgPSBjYXRlZ29yaXNlZFVybHNbaV0udXJsO1xuXG4gICAgLy8gTG9nIGN1cnJlbnQgc2NyYXBlIHNlcXVlbmNlIGFuZCB0aGUgdG90YWwgbnVtYmVyIG9mIHBhZ2VzIHRvIHNjcmFwZVxuICAgIGNvbnN0IHNob3J0VXJsID0gdXJsLnJlcGxhY2UoXCJodHRwczovL1wiLCBcIlwiKTtcbiAgICBsb2coXG4gICAgICBjb2xvdXIueWVsbG93LFxuICAgICAgYFxcblske2kgKyAxfS8ke2NhdGVnb3Jpc2VkVXJscy5sZW5ndGh9XSAke3Nob3J0VXJsfWBcbiAgICApO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIE9wZW4gcGFnZSB3aXRoIHVybCBvcHRpb25zIG5vdyBzZXRcbiAgICAgIGF3YWl0IHBhZ2UuZ290byh1cmwpO1xuXG4gICAgICAvLyBXYWl0IGFuZCBwYWdlIGRvd24gbXVsdGlwbGUgdGltZXMgdG8gZnVydGhlciB0cmlnZ2VyIGFueSBsYXp5IGxvYWRzXG4gICAgICBmb3IgKGxldCBwYWdlRG93biA9IDA7IHBhZ2VEb3duIDwgNTsgcGFnZURvd24rKykge1xuICAgICAgICAvLyBjcmVhdGUgYSByYW5kb20gbnVtYmVyIGJldHdlZW4gNTAwIGFuZCAxNTAwXG4gICAgICAgIGNvbnN0IHRpbWVCZXR3ZWVuUGdEb3ducyA9IE1hdGgucmFuZG9tKCkgKiAxMDAwICsgNTAwO1xuICAgICAgICBhd2FpdCBwYWdlLndhaXRGb3JUaW1lb3V0KHRpbWVCZXR3ZWVuUGdEb3ducyk7XG4gICAgICAgIGF3YWl0IHBhZ2Uua2V5Ym9hcmQucHJlc3MoXCJQYWdlRG93blwiKTtcbiAgICAgIH1cblxuICAgICAgLy8gU2V0IHBhZ2UgdGltZW91dCB0byAzMCBzZWNvbmRzXG4gICAgICBhd2FpdCBwYWdlLnNldERlZmF1bHRUaW1lb3V0KDMwMDAwKTtcblxuICAgICAgLy8gV2FpdCBmb3IgcHJvZHVjdC1wcmljZSBoMyBodG1sIGVsZW1lbnQgdG8gZHluYW1pY2FsbHkgbG9hZCBpbixcbiAgICAgIC8vICB0aGlzIGlzIHJlcXVpcmVkIHRvIHNlZSBwcm9kdWN0IGRhdGFcbiAgICAgIGF3YWl0IHBhZ2Uud2FpdEZvclNlbGVjdG9yKFwicHJvZHVjdC1wcmljZSBoM1wiKTtcblxuICAgICAgLy8gTG9hZCBodG1sIGludG8gQ2hlZXJpbyBmb3IgRE9NIHNlbGVjdGlvblxuICAgICAgY29uc3QgaHRtbCA9IGF3YWl0IHBhZ2UuaW5uZXJIVE1MKFwicHJvZHVjdC1ncmlkXCIpO1xuICAgICAgY29uc3QgJCA9IGNoZWVyaW8ubG9hZChodG1sKTtcblxuICAgICAgLy8gRmluZCBhbGwgcHJvZHVjdCBlbnRyaWVzXG4gICAgICBjb25zdCBhbGxQcm9kdWN0RW50cmllcyA9ICQoXCJjZHgtY2FyZCBwcm9kdWN0LXN0YW1wLWdyaWQgZGl2LnByb2R1Y3QtZW50cnlcIik7XG5cbiAgICAgIC8vIEZpbmQgYWR2ZXJ0aXNlbWVudCBwcm9kdWN0IGVudHJpZXMgbm90IG5vcm1hbGx5IHBhcnQgb2YgdGhpcyBwcm9kdWN0IGNhdGVnb3J5XG4gICAgICBjb25zdCBhZHZlcnRpc2VtZW50RW50cmllcyA9ICQoXCJkaXYuY2Fyb3VzZWwtdHJhY2sgZGl2IGNkeC1jYXJkIHByb2R1Y3Qtc3RhbXAtZ3JpZCBkaXYucHJvZHVjdC1lbnRyeVwiKVxuICAgICAgY29uc3QgYWRIcmVmczogc3RyaW5nW10gPSBhZHZlcnRpc2VtZW50RW50cmllcy5tYXAoKGluZGV4LCBlbGVtZW50KSA9PiB7XG4gICAgICAgIHJldHVybiAkKGVsZW1lbnQpLmZpbmQoXCJhXCIpLmZpcnN0KCkuYXR0cihcImhyZWZcIik7XG4gICAgICB9KS50b0FycmF5KCk7XG5cbiAgICAgIC8vIEZpbHRlciBvdXQgcHJvZHVjdCBlbnRyaWVzIHRoYXQgbWF0Y2ggdGhlIGZvdW5kIGFkdmVydGlzZW1lbnRzXG4gICAgICBjb25zdCBwcm9kdWN0RW50cmllcyA9IGFsbFByb2R1Y3RFbnRyaWVzLmZpbHRlcigoaW5kZXgsIGVsZW1lbnQpID0+IHtcbiAgICAgICAgY29uc3QgcHJvZHVjdEhyZWYgPSAkKGVsZW1lbnQpLmZpbmQoXCJhXCIpLmZpcnN0KCkuYXR0cihcImhyZWZcIik7XG4gICAgICAgIHJldHVybiAhYWRIcmVmcy5pbmNsdWRlcyhwcm9kdWN0SHJlZiEpO1xuICAgICAgfSlcblxuICAgICAgLy8gTG9nIHRoZSBudW1iZXIgb2YgcHJvZHVjdHMgZm91bmQsIHRpbWUgZWxhcHNlZCwgY2F0ZWdvcnlcbiAgICAgIGxvZyhcbiAgICAgICAgY29sb3VyLnllbGxvdyxcbiAgICAgICAgYCR7cHJvZHVjdEVudHJpZXMubGVuZ3RofSBwcm9kdWN0IGVudHJpZXMgZm91bmRgLnBhZEVuZCgzNSkgK1xuICAgICAgICBgVGltZSBFbGFwc2VkOiAke2dldFRpbWVFbGFwc2VkU2luY2Uoc3RhcnRUaW1lKX1gLnBhZEVuZCgzNSkgK1xuICAgICAgICBgQ2F0ZWdvcnk6ICR7Xy5zdGFydENhc2UoY2F0ZWdvcmlzZWRVcmwuY2F0ZWdvcmllcy5qb2luKFwiIC0gXCIpKX1gXG4gICAgICApO1xuXG4gICAgICAvLyBMb2cgdGFibGUgaGVhZGVyXG4gICAgICBpZiAoIWRhdGFiYXNlTW9kZSkgbG9nVGFibGVIZWFkZXIoKTtcblxuICAgICAgLy8gU3RvcmUgbnVtYmVyIG9mIGl0ZW1zIHByb2Nlc3NlZCBmb3IgbG9nZ2luZyBwdXJwb3Nlc1xuICAgICAgbGV0IHBlclBhZ2VMb2dTdGF0cyA9IHtcbiAgICAgICAgbmV3UHJvZHVjdHM6IDAsXG4gICAgICAgIHByaWNlQ2hhbmdlZDogMCxcbiAgICAgICAgaW5mb1VwZGF0ZWQ6IDAsXG4gICAgICAgIGFscmVhZHlVcFRvRGF0ZTogMCxcbiAgICAgIH1cblxuICAgICAgLy8gU3RhcnQgbmVzdGVkIGxvb3Agd2hpY2ggbG9vcHMgdGhyb3VnaCBlYWNoIHByb2R1Y3QgZW50cnlcbiAgICAgIHBlclBhZ2VMb2dTdGF0cyA9XG4gICAgICAgIGF3YWl0IHByb2Nlc3NGb3VuZFByb2R1Y3RFbnRyaWVzKGNhdGVnb3Jpc2VkVXJsLCBwcm9kdWN0RW50cmllcywgcGVyUGFnZUxvZ1N0YXRzKTtcblxuICAgICAgLy8gQWZ0ZXIgc2NyYXBpbmcgZXZlcnkgaXRlbSBpcyBjb21wbGV0ZSwgbG9nIGhvdyBtYW55IHByb2R1Y3RzIHdlcmUgc2NyYXBlZFxuICAgICAgaWYgKGRhdGFiYXNlTW9kZSkge1xuICAgICAgICBsb2coXG4gICAgICAgICAgY29sb3VyLmJsdWUsXG4gICAgICAgICAgYFN1cGFiYXNlOiAke3BlclBhZ2VMb2dTdGF0cy5uZXdQcm9kdWN0c30gbmV3IHByb2R1Y3RzLCBgICtcbiAgICAgICAgICBgJHtwZXJQYWdlTG9nU3RhdHMucHJpY2VDaGFuZ2VkfSB1cGRhdGVkIHByaWNlcywgYCArXG4gICAgICAgICAgYCR7cGVyUGFnZUxvZ1N0YXRzLmluZm9VcGRhdGVkfSB1cGRhdGVkIGluZm8sIGAgK1xuICAgICAgICAgIGAke3BlclBhZ2VMb2dTdGF0cy5hbHJlYWR5VXBUb0RhdGV9IGFscmVhZHkgdXAtdG8tZGF0ZWBcbiAgICAgICAgKTtcbiAgICAgIH1cblxuICAgICAgLy8gRGVsYXkgYmV0d2VlbiBlYWNoIHBhZ2UgbG9hZFxuICAgICAgYXdhaXQgc2V0VGltZW91dChwYWdlTG9hZERlbGF5U2Vjb25kcyAqIDEwMDApO1xuXG4gICAgfSBjYXRjaCAoZXJyb3I6IHVua25vd24pIHtcbiAgICAgIGlmICh0eXBlb2YgZXJyb3IgPT09ICdzdHJpbmcnKSB7XG4gICAgICAgIGlmIChlcnJvci5pbmNsdWRlcyhcIk5TX0VSUk9SX0NPTk5FQ1RJT05fUkVGVVNFRFwiKSkge1xuICAgICAgICAgIGxvZ0Vycm9yKFwiQ29ubmVjdGlvbiBGYWlsZWQgLSBDaGVjayBGaXJld2FsbFxcblwiICsgZXJyb3IpO1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgbG9nRXJyb3IoXG4gICAgICAgIFwiUGFnZSBUaW1lb3V0IGFmdGVyIDMwIHNlY29uZHMgLSBTa2lwcGluZyB0aGlzIHBhZ2VcXG5cIiArIGVycm9yXG4gICAgICApO1xuICAgIH1cbiAgfVxufVxuXG4vLyBwcm9jZXNzRm91bmRQcm9kdWN0RW50cmllc1xuLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbi8vIExvb3BzIHRocm91Z2ggZWFjaCBwcm9kdWN0IGVudHJ5IGFuZCBzY3JhcGVzIHByaWNpbmcgYW5kIG90aGVyIGluZm8uXG4vLyBUaGlzIGZ1bmN0aW9uIGlzIGNhbGxlZCBieSBzY3JhcGVBbGxQYWdlVVJMcy5cblxuYXN5bmMgZnVuY3Rpb24gcHJvY2Vzc0ZvdW5kUHJvZHVjdEVudHJpZXNcbiAgKFxuICAgIGNhdGVnb3Jpc2VkVXJsOiBDYXRlZ29yaXNlZFVybCxcbiAgICBwcm9kdWN0RW50cmllczogY2hlZXJpby5DaGVlcmlvPGNoZWVyaW8uRWxlbWVudD4sXG4gICAgcGVyUGFnZUxvZ1N0YXRzOiB7XG4gICAgICBuZXdQcm9kdWN0czogbnVtYmVyO1xuICAgICAgcHJpY2VDaGFuZ2VkOiBudW1iZXI7XG4gICAgICBpbmZvVXBkYXRlZDogbnVtYmVyO1xuICAgICAgYWxyZWFkeVVwVG9EYXRlOiBudW1iZXI7XG4gICAgfSkge1xuXG4gIC8vIExvb3AgdGhyb3VnaCBlYWNoIHByb2R1Y3QgZW50cnlcbiAgZm9yIChsZXQgaSA9IDA7IGkgPCBwcm9kdWN0RW50cmllcy5sZW5ndGg7IGkrKykge1xuICAgIGNvbnN0IHByb2R1Y3RFbnRyeUVsZW1lbnQgPSBwcm9kdWN0RW50cmllc1tpXTtcblxuICAgIGNvbnN0IHByb2R1Y3QgPSBwbGF5d3JpZ2h0RWxlbWVudFRvUHJvZHVjdChcbiAgICAgIHByb2R1Y3RFbnRyeUVsZW1lbnQsXG4gICAgICBjYXRlZ29yaXNlZFVybC5jYXRlZ29yaWVzXG4gICAgKTtcblxuICAgIGlmIChkYXRhYmFzZU1vZGUgJiYgcHJvZHVjdCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAvLyBJbnNlcnQgb3IgdXBkYXRlIGl0ZW0gaW50byBNb25nb0RCIChjb25zb2xpZGF0ZWQgc3lzdGVtKVxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB1cHNlcnRQcm9kdWN0VG9Nb25nb0RCKHByb2R1Y3QpO1xuXG4gICAgICAvLyBQcm9jZXNzIGNvbnNvbGlkYXRlZCBwcm9kdWN0IChNb25nb0RCIHN5c3RlbSlcbiAgICAgIGNvbnN0IGNvbnNvbGlkYXRlZFByb2R1Y3RJZCA9IGF3YWl0IHByb2Nlc3NDb25zb2xpZGF0ZWRQcm9kdWN0TW9uZ29EQihwcm9kdWN0KTtcblxuICAgICAgLy8gVXNlIHJlc3BvbnNlIHRvIHVwZGF0ZSBsb2dnaW5nIGNvdW50ZXJzXG4gICAgICBzd2l0Y2ggKHJlc3BvbnNlKSB7XG4gICAgICAgIGNhc2UgVXBzZXJ0UmVzcG9uc2UuQWxyZWFkeVVwVG9EYXRlOlxuICAgICAgICAgIHBlclBhZ2VMb2dTdGF0cy5hbHJlYWR5VXBUb0RhdGUrKztcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSBVcHNlcnRSZXNwb25zZS5JbmZvQ2hhbmdlZDpcbiAgICAgICAgICBwZXJQYWdlTG9nU3RhdHMuaW5mb1VwZGF0ZWQrKztcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSBVcHNlcnRSZXNwb25zZS5OZXdQcm9kdWN0OlxuICAgICAgICAgIHBlclBhZ2VMb2dTdGF0cy5uZXdQcm9kdWN0cysrO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIFVwc2VydFJlc3BvbnNlLlByaWNlQ2hhbmdlZDpcbiAgICAgICAgICBwZXJQYWdlTG9nU3RhdHMucHJpY2VDaGFuZ2VkKys7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgYnJlYWs7XG4gICAgICB9XG5cbiAgICAgIC8vIFVwbG9hZCBpbWFnZSB0byBTdXBhYmFzZSBTdG9yYWdlXG4gICAgICBpZiAodXBsb2FkSW1hZ2VzTW9kZSkge1xuICAgICAgICAvLyBHZXQgaW1hZ2UgdXJsIHVzaW5nIHByb3ZpZGVkIGJhc2UgdXJsLCBwcm9kdWN0IElELCBhbmQgaGktcmVzIHF1ZXJ5IHBhcmFtZXRlcnNcbiAgICAgICAgY29uc3QgaW1hZ2VVcmxCYXNlID1cbiAgICAgICAgICBcImh0dHBzOi8vYXNzZXRzLndvb2x3b3J0aHMuY29tLmF1L2ltYWdlcy8yMDEwL1wiO1xuICAgICAgICBjb25zdCBpbWFnZVVybEV4dGVuc2lvbkFuZFF1ZXJ5UGFyYW1zID1cbiAgICAgICAgICBcIi5qcGc/aW1wb2xpY3k9d293Y2R4d2JqYngmdz05MDAmaD05MDBcIjtcbiAgICAgICAgY29uc3QgaW1hZ2VVcmwgPVxuICAgICAgICAgIGltYWdlVXJsQmFzZSArIHByb2R1Y3QuaWQgKyBpbWFnZVVybEV4dGVuc2lvbkFuZFF1ZXJ5UGFyYW1zO1xuXG4gICAgICAgIGF3YWl0IHVwbG9hZEltYWdlVG9Nb25nb0RCKGltYWdlVXJsISwgcHJvZHVjdCk7XG4gICAgICB9XG4gICAgfSBlbHNlIGlmICghZGF0YWJhc2VNb2RlICYmIHByb2R1Y3QgIT09IHVuZGVmaW5lZCkge1xuICAgICAgLy8gV2hlbiBkb2luZyBhIGRyeSBydW4sIGxvZyBwcm9kdWN0IG5hbWUgLSBzaXplIC0gcHJpY2UgaW4gdGFibGUgZm9ybWF0XG4gICAgICBsb2dQcm9kdWN0Um93KHByb2R1Y3QhKTtcbiAgICB9XG5cbiAgICAvLyBBZGQgYSB0aW55IGRlbGF5IGJldHdlZW4gZWFjaCBwcm9kdWN0IGxvb3AuXG4gICAgLy8gVGhpcyBtYWtlcyBwcmludGluZyB0aGUgbG9nIG1vcmUgcmVhZGFibGVcbiAgICBhd2FpdCBzZXRUaW1lb3V0KHByb2R1Y3RMb2dEZWxheU1pbGxpU2Vjb25kcyk7XG4gIH1cblxuICAvLyBSZXR1cm4gbG9nIHN0YXRzIGZvciBjb21wbGV0ZWQgcGFnZVxuICByZXR1cm4gcGVyUGFnZUxvZ1N0YXRzO1xufVxuXG5cbi8vIGhhbmRsZUFyZ3VtZW50cygpXG4vLyAtLS0tLS0tLS0tLS0tLS0tLVxuLy8gSGFuZGxlIGNvbW1hbmQgbGluZSBhcmd1bWVudHMuIENhbiBiZSByZXZlcnNlIG1vZGUsIGRyeS1ydW4tbW9kZSwgY3VzdG9tIHVybCwgb3IgY2F0ZWdvcmllc1xuXG5mdW5jdGlvbiBoYW5kbGVBcmd1bWVudHMoY2F0ZWdvcmlzZWRVcmxzKTogQ2F0ZWdvcmlzZWRVcmxbXSB7XG4gIGlmIChwcm9jZXNzLmFyZ3YubGVuZ3RoID4gMikge1xuICAgIC8vIFNsaWNlIG91dCB0aGUgZmlyc3QgMiBhcmd1bWVudHMsIGFzIHRoZXkgYXJlIG5vdCB1c2VyLXByb3ZpZGVkXG4gICAgY29uc3QgdXNlckFyZ3MgPSBwcm9jZXNzLmFyZ3Yuc2xpY2UoMiwgcHJvY2Vzcy5hcmd2Lmxlbmd0aCk7XG5cbiAgICAvLyBMb29wIHRocm91Z2ggYWxsIGFyZ3MgYW5kIGZpbmQgYW55IG1hdGNoaW5nIGtleXdvcmRzXG4gICAgbGV0IHBvdGVudGlhbFVybCA9IFwiXCI7XG4gICAgdXNlckFyZ3MuZm9yRWFjaChhc3luYyAoYXJnKSA9PiB7XG4gICAgICBpZiAoYXJnID09PSBcImRiXCIpIGRhdGFiYXNlTW9kZSA9IHRydWU7XG4gICAgICBlbHNlIGlmIChhcmcgPT09IFwiaW1hZ2VzXCIpIHVwbG9hZEltYWdlc01vZGUgPSB0cnVlO1xuICAgICAgZWxzZSBpZiAoYXJnID09PSBcImhlYWRsZXNzXCIpIGhlYWRsZXNzTW9kZSA9IHRydWUgLy8gaXMgYWxyZWFkeSBkZWZhdWx0XG4gICAgICBlbHNlIGlmIChhcmcgPT09IFwiaGVhZGVkXCIpIGhlYWRsZXNzTW9kZSA9IGZhbHNlXG5cbiAgICAgIC8vIEFueSBhcmcgY29udGFpbmluZyAuY28ubnogd2lsbCByZXBsYWNlZCB0aGUgVVJMcyB0ZXh0IGZpbGUgdG8gYmUgc2NyYXBlZC5cbiAgICAgIGVsc2UgaWYgKGFyZy5pbmNsdWRlcyhcIi5jby5uelwiKSkgcG90ZW50aWFsVXJsICs9IGFyZztcblxuICAgICAgLy8gUmV2ZXJzZSB0aGUgb3JkZXIgb2YgdGhlIFVSTHMgdG8gYmUgc2NyYXBlZCwgc3RhcnRpbmcgZnJvbSB0aGUgYm90dG9tXG4gICAgICBlbHNlIGlmIChhcmcgPT09IFwicmV2ZXJzZVwiKSBjYXRlZ29yaXNlZFVybHMgPSBjYXRlZ29yaXNlZFVybHMucmV2ZXJzZSgpO1xuICAgICAgLy8gZWxzZSBpZiAoYXJnID09PSBcImN1c3RvbVwiKSB7XG4gICAgICAvLyAgIGNhdGVnb3Jpc2VkVXJscyA9IFtdO1xuICAgICAgLy8gICBhd2FpdCBjdXN0b21RdWVyeSgpO1xuICAgICAgLy8gICBwcm9jZXNzLmV4aXQoKTtcbiAgICAgIC8vIH1cbiAgICB9KTtcblxuICAgIC8vIFRyeSB0byBwYXJzZSB0aGUgcG90ZW50aWFsIG5ldyB1cmxcbiAgICBjb25zdCBwYXJzZWRVcmwgPSBwYXJzZUFuZENhdGVnb3Jpc2VVUkwocG90ZW50aWFsVXJsKTtcbiAgICBpZiAocGFyc2VkVXJsICE9PSB1bmRlZmluZWQpIGNhdGVnb3Jpc2VkVXJscyA9IFtwYXJzZWRVcmxdO1xuICB9XG4gIHJldHVybiBjYXRlZ29yaXNlZFVybHM7XG59XG5cbi8vIGVzdGFibGlzaFBsYXl3cmlnaHRQYWdlKClcbi8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbi8vIENyZWF0ZSBhIHBsYXl3cmlnaHQgYnJvd3NlclxuXG5hc3luYyBmdW5jdGlvbiBlc3RhYmxpc2hQbGF5d3JpZ2h0UGFnZShoZWFkbGVzcyA9IHRydWUpIHtcbiAgbG9nKFxuICAgIGNvbG91ci55ZWxsb3csXG4gICAgXCJMYXVuY2hpbmcgQnJvd3Nlci4uIFwiICtcbiAgICAocHJvY2Vzcy5hcmd2Lmxlbmd0aCA+IDJcbiAgICAgID8gXCIoXCIgKyAocHJvY2Vzcy5hcmd2Lmxlbmd0aCAtIDIpICsgXCIgYXJndW1lbnRzIGZvdW5kKVwiXG4gICAgICA6IFwiXCIpXG4gICk7XG4gIGJyb3dzZXIgPSBhd2FpdCBwbGF5d3JpZ2h0LmZpcmVmb3gubGF1bmNoKHtcbiAgICBoZWFkbGVzczogaGVhZGxlc3MsXG4gIH0pO1xuICBwYWdlID0gYXdhaXQgYnJvd3Nlci5uZXdQYWdlKCk7XG5cbiAgLy8gUmVqZWN0IHVubmVjZXNzYXJ5IGFkL3RyYWNraW5nIHVybHNcbiAgYXdhaXQgcm91dGVQbGF5d3JpZ2h0RXhjbHVzaW9ucygpO1xuXG4gIHJldHVybiBicm93c2VyO1xufVxuXG4vLyBzZWxlY3RTdG9yZUJ5TG9jYXRpb25OYW1lKClcbi8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuLy8gU2VsZWN0cyBhIHN0b3JlIGxvY2F0aW9uIGJ5IHR5cGluZyBpbiB0aGUgc3BlY2lmaWVkIGxvY2F0aW9uIGFkZHJlc3NcblxuYXN5bmMgZnVuY3Rpb24gc2VsZWN0U3RvcmVCeUxvY2F0aW9uTmFtZShsb2NhdGlvbk5hbWU6IHN0cmluZyA9IFwiXCIpIHtcbiAgLy8gSWYgbm8gbG9jYXRpb24gd2FzIHBhc3NlZCBpbiwgYWxzbyBjaGVjayAuZW52IGZvciBTVE9SRV9OQU1FXG4gIGlmIChsb2NhdGlvbk5hbWUgPT09IFwiXCIpIHtcbiAgICBpZiAocHJvY2Vzcy5lbnYuU1RPUkVfTkFNRSkgbG9jYXRpb25OYW1lID0gcHJvY2Vzcy5lbnYuU1RPUkVfTkFNRTtcbiAgICAvLyBJZiBTVE9SRV9OQU1FIGlzIGFsc28gbm90IHByZXNlbnQsIHNraXAgc3RvcmUgbG9jYXRpb24gc2VsZWN0aW9uXG4gICAgZWxzZSB7XG4gICAgICBsb2coY29sb3VyLnllbGxvdywgXCJObyBzdG9yZSBsb2NhdGlvbiBzcGVjaWZpZWQgLSB1c2luZyBkZWZhdWx0IGxvY2F0aW9uXCIpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgfVxuXG4gIGxvZyhjb2xvdXIueWVsbG93LCBgQXR0ZW1wdGluZyB0byBzZWxlY3Qgc3RvcmUgbG9jYXRpb246ICR7bG9jYXRpb25OYW1lfWApO1xuXG4gIHRyeSB7XG4gICAgYXdhaXQgcGFnZS5zZXREZWZhdWx0VGltZW91dCgxNTAwMCk7XG5cbiAgICAvLyBUcnkgdGhlIG1haW4gc2hvcHBpbmcgcGFnZSBmaXJzdFxuICAgIGF3YWl0IHBhZ2UuZ290byhcImh0dHBzOi8vd3d3Lndvb2x3b3J0aHMuY28ubnovc2hvcC9icm93c2VcIiwge1xuICAgICAgd2FpdFVudGlsOiBcImRvbWNvbnRlbnRsb2FkZWRcIixcbiAgICB9KTtcblxuICAgIC8vIFdhaXQgYSBtb21lbnQgZm9yIHRoZSBwYWdlIHRvIGZ1bGx5IGxvYWRcbiAgICBhd2FpdCBwYWdlLndhaXRGb3JUaW1lb3V0KDIwMDApO1xuXG4gICAgLy8gTG9vayBmb3IgdmFyaW91cyBwb3NzaWJsZSBzdG9yZSBsb2NhdGlvbiBzZWxlY3RvcnNcbiAgICBjb25zdCBwb3NzaWJsZVNlbGVjdG9ycyA9IFtcbiAgICAgICdidXR0b25bZGF0YS10ZXN0aWQ9XCJzdG9yZS1zZWxlY3RvclwiXScsXG4gICAgICAnYnV0dG9uW2FyaWEtbGFiZWwqPVwic3RvcmVcIl0nLFxuICAgICAgJ2J1dHRvblthcmlhLWxhYmVsKj1cImxvY2F0aW9uXCJdJyxcbiAgICAgICdbZGF0YS10ZXN0aWQ9XCJjaGFuZ2Utc3RvcmVcIl0nLFxuICAgICAgJ2J1dHRvbjpoYXMtdGV4dChcIkNoYW5nZSBzdG9yZVwiKScsXG4gICAgICAnYnV0dG9uOmhhcy10ZXh0KFwiU2VsZWN0IHN0b3JlXCIpJyxcbiAgICAgICcuc3RvcmUtc2VsZWN0b3IgYnV0dG9uJyxcbiAgICAgICdbY2xhc3MqPVwic3RvcmVcIl0gYnV0dG9uJyxcbiAgICAgICdmaWVsZHNldCBkaXYgZGl2IHAgYnV0dG9uJ1xuICAgIF07XG5cbiAgICBsZXQgc3RvcmVCdXR0b24gPSBudWxsO1xuICAgIGZvciAoY29uc3Qgc2VsZWN0b3Igb2YgcG9zc2libGVTZWxlY3RvcnMpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGF3YWl0IHBhZ2Uud2FpdEZvclNlbGVjdG9yKHNlbGVjdG9yLCB7IHRpbWVvdXQ6IDMwMDAgfSk7XG4gICAgICAgIHN0b3JlQnV0dG9uID0gcGFnZS5sb2NhdG9yKHNlbGVjdG9yKS5maXJzdCgpO1xuICAgICAgICBsb2coY29sb3VyLmdyZWVuLCBgRm91bmQgc3RvcmUgc2VsZWN0b3I6ICR7c2VsZWN0b3J9YCk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgfSBjYXRjaCB7XG4gICAgICAgIC8vIENvbnRpbnVlIHRvIG5leHQgc2VsZWN0b3JcbiAgICAgIH1cbiAgICB9XG5cbiAgICBpZiAoIXN0b3JlQnV0dG9uKSB7XG4gICAgICBsb2coY29sb3VyLnllbGxvdywgXCJObyBzdG9yZSBzZWxlY3RvciBmb3VuZCAtIHByb2NlZWRpbmcgd2l0aCBkZWZhdWx0IGxvY2F0aW9uXCIpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIC8vIENsaWNrIHRoZSBzdG9yZSBzZWxlY3RvciBidXR0b25cbiAgICBhd2FpdCBzdG9yZUJ1dHRvbi5jbGljaygpO1xuICAgIGF3YWl0IHBhZ2Uud2FpdEZvclRpbWVvdXQoMTAwMCk7XG5cbiAgICAvLyBMb29rIGZvciBpbnB1dCBmaWVsZCB3aXRoIHZhcmlvdXMgcG9zc2libGUgc2VsZWN0b3JzXG4gICAgY29uc3QgaW5wdXRTZWxlY3RvcnMgPSBbXG4gICAgICAnaW5wdXRbcGxhY2Vob2xkZXIqPVwic3VidXJiXCJdJyxcbiAgICAgICdpbnB1dFtwbGFjZWhvbGRlcio9XCJsb2NhdGlvblwiXScsXG4gICAgICAnaW5wdXRbcGxhY2Vob2xkZXIqPVwiYWRkcmVzc1wiXScsXG4gICAgICAnaW5wdXRbdHlwZT1cInRleHRcIl0nLFxuICAgICAgJ2Zvcm0tc3VidXJiLWF1dG9jb21wbGV0ZSBmb3JtLWlucHV0IGlucHV0JyxcbiAgICAgICdbZGF0YS10ZXN0aWQ9XCJsb2NhdGlvbi1pbnB1dFwiXScsXG4gICAgICAnLmxvY2F0aW9uLWlucHV0IGlucHV0JyxcbiAgICAgICdpbnB1dFthcmlhLWxhYmVsKj1cImxvY2F0aW9uXCJdJ1xuICAgIF07XG5cbiAgICBsZXQgbG9jYXRpb25JbnB1dCA9IG51bGw7XG4gICAgZm9yIChjb25zdCBzZWxlY3RvciBvZiBpbnB1dFNlbGVjdG9ycykge1xuICAgICAgdHJ5IHtcbiAgICAgICAgYXdhaXQgcGFnZS53YWl0Rm9yU2VsZWN0b3Ioc2VsZWN0b3IsIHsgdGltZW91dDogMzAwMCB9KTtcbiAgICAgICAgbG9jYXRpb25JbnB1dCA9IHBhZ2UubG9jYXRvcihzZWxlY3RvcikuZmlyc3QoKTtcbiAgICAgICAgbG9nKGNvbG91ci5ncmVlbiwgYEZvdW5kIGxvY2F0aW9uIGlucHV0OiAke3NlbGVjdG9yfWApO1xuICAgICAgICBicmVhaztcbiAgICAgIH0gY2F0Y2gge1xuICAgICAgICAvLyBDb250aW51ZSB0byBuZXh0IHNlbGVjdG9yXG4gICAgICB9XG4gICAgfVxuXG4gICAgaWYgKCFsb2NhdGlvbklucHV0KSB7XG4gICAgICBsb2coY29sb3VyLnllbGxvdywgXCJObyBsb2NhdGlvbiBpbnB1dCBmb3VuZCAtIHByb2NlZWRpbmcgd2l0aCBkZWZhdWx0IGxvY2F0aW9uXCIpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIC8vIENsZWFyIGFueSBleGlzdGluZyB0ZXh0IGFuZCB0eXBlIHRoZSBsb2NhdGlvblxuICAgIGF3YWl0IGxvY2F0aW9uSW5wdXQuY2xlYXIoKTtcbiAgICBhd2FpdCBsb2NhdGlvbklucHV0LmZpbGwobG9jYXRpb25OYW1lKTtcbiAgICBhd2FpdCBwYWdlLndhaXRGb3JUaW1lb3V0KDIwMDApOyAvLyBXYWl0IGZvciBhdXRvY29tcGxldGVcblxuICAgIC8vIFRyeSB0byBzZWxlY3QgdGhlIGZpcnN0IHN1Z2dlc3Rpb25cbiAgICB0cnkge1xuICAgICAgLy8gTG9vayBmb3IgZHJvcGRvd24gc3VnZ2VzdGlvbnNcbiAgICAgIGNvbnN0IHN1Z2dlc3Rpb25TZWxlY3RvcnMgPSBbXG4gICAgICAgICdbcm9sZT1cIm9wdGlvblwiXTpmaXJzdC1jaGlsZCcsXG4gICAgICAgICcuc3VnZ2VzdGlvbjpmaXJzdC1jaGlsZCcsXG4gICAgICAgICcuYXV0b2NvbXBsZXRlLWl0ZW06Zmlyc3QtY2hpbGQnLFxuICAgICAgICAnbGk6Zmlyc3QtY2hpbGQnLFxuICAgICAgICAnW2RhdGEtdGVzdGlkPVwic3VnZ2VzdGlvblwiXTpmaXJzdC1jaGlsZCdcbiAgICAgIF07XG5cbiAgICAgIGxldCBzdWdnZXN0aW9uRm91bmQgPSBmYWxzZTtcbiAgICAgIGZvciAoY29uc3Qgc2VsZWN0b3Igb2Ygc3VnZ2VzdGlvblNlbGVjdG9ycykge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGF3YWl0IHBhZ2Uud2FpdEZvclNlbGVjdG9yKHNlbGVjdG9yLCB7IHRpbWVvdXQ6IDIwMDAgfSk7XG4gICAgICAgICAgYXdhaXQgcGFnZS5sb2NhdG9yKHNlbGVjdG9yKS5jbGljaygpO1xuICAgICAgICAgIHN1Z2dlc3Rpb25Gb3VuZCA9IHRydWU7XG4gICAgICAgICAgbG9nKGNvbG91ci5ncmVlbiwgYFNlbGVjdGVkIHN1Z2dlc3Rpb24gdXNpbmc6ICR7c2VsZWN0b3J9YCk7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIH0gY2F0Y2gge1xuICAgICAgICAgIC8vIENvbnRpbnVlIHRvIG5leHQgc2VsZWN0b3JcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBpZiAoIXN1Z2dlc3Rpb25Gb3VuZCkge1xuICAgICAgICAvLyBUcnkga2V5Ym9hcmQgbmF2aWdhdGlvbiBhcyBmYWxsYmFja1xuICAgICAgICBhd2FpdCBwYWdlLmtleWJvYXJkLnByZXNzKFwiQXJyb3dEb3duXCIpO1xuICAgICAgICBhd2FpdCBwYWdlLndhaXRGb3JUaW1lb3V0KDMwMCk7XG4gICAgICAgIGF3YWl0IHBhZ2Uua2V5Ym9hcmQucHJlc3MoXCJFbnRlclwiKTtcbiAgICAgICAgbG9nKGNvbG91ci55ZWxsb3csIFwiVXNlZCBrZXlib2FyZCBuYXZpZ2F0aW9uIHRvIHNlbGVjdCBsb2NhdGlvblwiKTtcbiAgICAgIH1cblxuICAgICAgYXdhaXQgcGFnZS53YWl0Rm9yVGltZW91dCgxMDAwKTtcblxuICAgICAgLy8gTG9vayBmb3Igc2F2ZS9jb25maXJtIGJ1dHRvblxuICAgICAgY29uc3Qgc2F2ZVNlbGVjdG9ycyA9IFtcbiAgICAgICAgJ2J1dHRvbjpoYXMtdGV4dChcIlNhdmVcIiknLFxuICAgICAgICAnYnV0dG9uOmhhcy10ZXh0KFwiQ29udGludWVcIiknLFxuICAgICAgICAnYnV0dG9uOmhhcy10ZXh0KFwiQ29uZmlybVwiKScsXG4gICAgICAgICdidXR0b246aGFzLXRleHQoXCJTZWxlY3RcIiknLFxuICAgICAgICAnW2RhdGEtdGVzdGlkPVwic2F2ZS1sb2NhdGlvblwiXScsXG4gICAgICAgICcuc2F2ZS1idXR0b24nLFxuICAgICAgICAnYnV0dG9uW3R5cGU9XCJzdWJtaXRcIl0nXG4gICAgICBdO1xuXG4gICAgICBmb3IgKGNvbnN0IHNlbGVjdG9yIG9mIHNhdmVTZWxlY3RvcnMpIHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBhd2FpdCBwYWdlLndhaXRGb3JTZWxlY3RvcihzZWxlY3RvciwgeyB0aW1lb3V0OiAyMDAwIH0pO1xuICAgICAgICAgIGF3YWl0IHBhZ2UubG9jYXRvcihzZWxlY3RvcikuY2xpY2soKTtcbiAgICAgICAgICBsb2coY29sb3VyLmdyZWVuLCBgQ2xpY2tlZCBzYXZlIGJ1dHRvbjogJHtzZWxlY3Rvcn1gKTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgfSBjYXRjaCB7XG4gICAgICAgICAgLy8gQ29udGludWUgdG8gbmV4dCBzZWxlY3RvclxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGF3YWl0IHBhZ2Uud2FpdEZvclRpbWVvdXQoMjAwMCk7XG4gICAgICBsb2coY29sb3VyLmdyZWVuLCBgU3VjY2Vzc2Z1bGx5IGNoYW5nZWQgbG9jYXRpb24gdG86ICR7bG9jYXRpb25OYW1lfWApO1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGxvZyhjb2xvdXIueWVsbG93LCBgQ291bGQgbm90IHNlbGVjdCBsb2NhdGlvbiBzdWdnZXN0aW9uIC0gdXNpbmcgdHlwZWQgbG9jYXRpb246ICR7ZXJyb3J9YCk7XG4gICAgfVxuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgbG9nRXJyb3IoYFN0b3JlIGxvY2F0aW9uIHNlbGVjdGlvbiBmYWlsZWQ6ICR7ZXJyb3J9YCk7XG4gICAgbG9nKGNvbG91ci55ZWxsb3csIFwiUHJvY2VlZGluZyB3aXRoIGRlZmF1bHQgbG9jYXRpb25cIik7XG4gIH1cbn1cblxuLy8gcGxheXdyaWdodEVsZW1lbnRUb1Byb2R1Y3QoKVxuLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuLy8gVGFrZXMgYSBwbGF5d3JpZ2h0IGh0bWwgZWxlbWVudCBmb3IgJ2EucHJvZHVjdC1lbnRyeScsIGJ1aWxkcyBhbmQgcmV0dXJucyBhIFByb2R1Y3RcblxuZXhwb3J0IGZ1bmN0aW9uIHBsYXl3cmlnaHRFbGVtZW50VG9Qcm9kdWN0KFxuICBlbGVtZW50OiBjaGVlcmlvLkVsZW1lbnQsXG4gIGNhdGVnb3JpZXM6IHN0cmluZ1tdXG4pOiBQcm9kdWN0IHwgdW5kZWZpbmVkIHtcbiAgY29uc3QgJCA9IGNoZWVyaW8ubG9hZChlbGVtZW50KTtcblxuICAvLyBGaW5kIHRoZSA8aDM+IHRhZyB3aXRoIGFuIGlkIGNvbnRhaW5pbmcgXCItdGl0bGVcIlxuICAvLyBUaGlzIGhvbGRzIHRoZSBwcm9kdWN0IElELCBuYW1lIGFuZCBzaXplXG4gIGxldCBpZE5hbWVTaXplSDMgPSAkKGVsZW1lbnQpLmZpbmQoXCJoM1wiKS5maWx0ZXIoKGksIGVsZW1lbnQpID0+IHtcbiAgICBpZiAoJChlbGVtZW50KS5hdHRyKFwiaWRcIik/LmluY2x1ZGVzKFwiLXRpdGxlXCIpKSB7XG4gICAgICByZXR1cm4gdHJ1ZVxuICAgIH0gZWxzZSByZXR1cm4gZmFsc2U7XG4gIH0pO1xuXG4gIGxldCBwcm9kdWN0OiBQcm9kdWN0ID0ge1xuXG4gICAgLy8gSURcbiAgICAvLyAtLS0tLS0tXG4gICAgLy8gRXh0cmFjdCBwcm9kdWN0IElEIGZyb20gaDMgaWQgYXR0cmlidXRlLCBhbmQgcmVtb3ZlIG5vbi1udW1iZXJzXG4gICAgaWQ6IGlkTmFtZVNpemVIMy5hdHRyKFwiaWRcIik/LnJlcGxhY2UoL1xcRC9nLCBcIlwiKSBhcyBzdHJpbmcsXG5cbiAgICAvLyBTb3VyY2UgU2l0ZSAtIHNldCB3aGVyZSB0aGUgc291cmNlIG9mIGluZm9ybWF0aW9uIGNhbWUgZnJvbVxuICAgIHNvdXJjZVNpdGU6IFwiY291bnRkb3duLmNvLm56XCIsIC8vIHVzZSBjb3VudGRvd24gZm9yIGNvbnNpc3RlbmN5IHdpdGggb2xkIGRhdGFcblxuICAgIC8vIENhdGVnb3JpZXNcbiAgICBjYXRlZ29yeTogY2F0ZWdvcmllcywgIC8vIGFscmVhZHkgb2J0YWluZWQgZnJvbSB1cmwvdGV4dCBmaWxlXG5cbiAgICAvLyBTdG9yZSB0b2RheSdzIGRhdGVcbiAgICBsYXN0Q2hlY2tlZDogbmV3IERhdGUoKSxcbiAgICBsYXN0VXBkYXRlZDogbmV3IERhdGUoKSxcblxuICAgIC8vIFRoZXNlIHZhbHVlcyB3aWxsIGxhdGVyIGJlIG92ZXJ3cml0dGVuXG4gICAgbmFtZTogXCJcIixcbiAgICBwcmljZUhpc3Rvcnk6IFtdLFxuICAgIGN1cnJlbnRQcmljZTogMCxcbiAgfTtcblxuICAvLyBOYW1lICYgU2l6ZVxuICAvLyAtLS0tLS0tLS0tLS1cbiAgLy8gVHJ5IHRvIGV4dHJhY3QgY29tYmluZWQgbmFtZSBhbmQgc2l6ZSBmcm9tIGgzIHRhZyBpbm5lciB0ZXh0XG4gIGxldCByYXdOYW1lQW5kU2l6ZSA9IGlkTmFtZVNpemVIMy50ZXh0KCkudHJpbSgpO1xuXG4gIC8vIENsZWFuIHVubmVjZXNzYXJ5IHdvcmRzIGZyb20gdGl0bGVzXG4gIHJhd05hbWVBbmRTaXplID0gcmF3TmFtZUFuZFNpemVcbiAgICAudG9Mb3dlckNhc2UoKVxuICAgIC5yZXBsYWNlKFwiICBcIiwgXCIgXCIpXG4gICAgLnJlcGxhY2UoXCJmcmVzaCBmcnVpdFwiLCBcIlwiKVxuICAgIC5yZXBsYWNlKFwiZnJlc2ggdmVnZXRhYmxlXCIsIFwiXCIpXG4gICAgLnRyaW0oKVxuICAgIDtcblxuICAvLyBUcnkgdG8gcmVnZXggbWF0Y2ggYSBzaXplIHNlY3Rpb24gc3VjaCBhczpcbiAgLy8gMTAwZywgMTUwbWwsIDE2cGFjaywgMC41LTEuNWtnLCB0cmF5IDFrZywgZXRjXG4gIGxldCB0cnlNYXRjaFNpemUgPVxuICAgIHJhd05hbWVBbmRTaXplLm1hdGNoKC8odHJheVxcc1xcZCspfChcXGQrKFxcLlxcZCspPyhcXC1cXGQrXFwuXFxkKyk/XFxzPyhnfGtnfGx8bWx8cGFjaykpXFxiL2cpO1xuXG4gIGlmICghdHJ5TWF0Y2hTaXplKSB7XG4gICAgLy8gQ2FwaXRhbGlzZSBhbmQgc2V0IG5hbWVcbiAgICBwcm9kdWN0Lm5hbWUgPSB0b1RpdGxlQ2FzZShyYXdOYW1lQW5kU2l6ZSk7XG5cbiAgICAvLyBObyBzaXplIHdhcyBmb3VuZCBpbiBuYW1lLCBzaXplIGNhbiBiZSBkZXJpdmVkIGZyb20gdW5pdCBwcmljZSBsYXRlclxuICAgIHByb2R1Y3Quc2l6ZSA9IFwiXCI7XG4gIH0gZWxzZSB7XG4gICAgLy8gSWYgYSBzaXplIHdhcyBmb3VuZCwgZ2V0IHRoZSBpbmRleCB0byBzcGxpdCB0aGUgc3RyaW5nIGludG8gbmFtZSBhbmQgc2l6ZVxuICAgIGxldCBpbmRleE9mU2l6ZVNlY3Rpb24gPSByYXdOYW1lQW5kU2l6ZS5pbmRleE9mKHRyeU1hdGNoU2l6ZVswXSk7XG5cbiAgICAvLyBDYXBpdGFsaXNlIGFuZCBzZXQgbmFtZVxuICAgIHByb2R1Y3QubmFtZSA9IHRvVGl0bGVDYXNlKHJhd05hbWVBbmRTaXplLnNsaWNlKDAsIGluZGV4T2ZTaXplU2VjdGlvbikpLnRyaW0oKTtcblxuICAgIC8vIENsZWFuIHVwIGFuZCBzZXQgc2l6ZVxuICAgIGxldCBjbGVhbmVkU2l6ZSA9IHJhd05hbWVBbmRTaXplLnNsaWNlKGluZGV4T2ZTaXplU2VjdGlvbikudHJpbSgpO1xuICAgIGlmIChjbGVhbmVkU2l6ZS5tYXRjaCgvXFxkK2xcXGIvKSkge1xuICAgICAgLy8gQ2FwaXRhbGlzZSBMIGZvciBsaXRyZXNcbiAgICAgIGNsZWFuZWRTaXplID0gY2xlYW5lZFNpemUucmVwbGFjZShcImxcIiwgXCJMXCIpO1xuICAgIH1cbiAgICBjbGVhbmVkU2l6ZS5yZXBsYWNlKFwidHJheVwiLCBcIlRyYXlcIik7XG4gICAgcHJvZHVjdC5zaXplID0gY2xlYW5lZFNpemU7XG4gIH1cblxuICAvLyBQcmljZVxuICAvLyAtLS0tLS1cbiAgLy8gSXMgb3JpZ2luYWxseSBkaXNwbGF5ZWQgd2l0aCBkb2xsYXJzIGluIGFuIDxlbT4sIGNlbnRzIGluIGEgPHNwYW4+LFxuICAvLyBhbmQgcG90ZW50aWFsbHkgYSBrZyB1bml0IG5hbWUgaW5zaWRlIHRoZSA8c3Bhbj4gZm9yIHNvbWUgbWVhdCBwcm9kdWN0cy5cbiAgLy8gVGhlIDIgbnVtYmVycyBhcmUgam9pbmVkLCBwYXJzZWQsIGFuZCBub24tbnVtYmVyIGNoYXJzIGFyZSByZW1vdmVkLlxuICBjb25zdCBkb2xsYXJTdHJpbmc6IHN0cmluZyA9ICQoZWxlbWVudClcbiAgICAuZmluZChcInByb2R1Y3QtcHJpY2UgZGl2IGgzIGVtXCIpXG4gICAgLnRleHQoKVxuICAgIC50cmltKCk7XG4gIGxldCBjZW50U3RyaW5nOiBzdHJpbmcgPSAkKGVsZW1lbnQpXG4gICAgLmZpbmQoXCJwcm9kdWN0LXByaWNlIGRpdiBoMyBzcGFuXCIpXG4gICAgLnRleHQoKVxuICAgIC50cmltKCk7XG4gIGlmIChjZW50U3RyaW5nLmluY2x1ZGVzKFwia2dcIikpIHByb2R1Y3Quc2l6ZSA9IFwicGVyIGtnXCI7XG4gIGNlbnRTdHJpbmcgPSBjZW50U3RyaW5nLnJlcGxhY2UoL1xcRC9nLCBcIlwiKTtcbiAgcHJvZHVjdC5jdXJyZW50UHJpY2UgPSBOdW1iZXIoZG9sbGFyU3RyaW5nICsgXCIuXCIgKyBjZW50U3RyaW5nKTtcblxuICAvLyBDcmVhdGUgYSBkYXRlIG9iamVjdCBmb3Igbm93LCBidXQgd2l0aCBtaW51dGVzIGFuZCBzZWNvbmRzIHNldCB0byAwXG4gIGNvbnN0IHRvZGF5ID0gbmV3IERhdGUoKTtcbiAgdG9kYXkuc2V0TWludXRlcygwKTtcbiAgdG9kYXkuc2V0U2Vjb25kcygwKTtcblxuICAvLyBDcmVhdGUgYSBEYXRlZFByaWNlIG9iamVjdCwgd2hpY2ggbWF5IGJlIGFkZGVkIGludG8gdGhlIHByb2R1Y3QgaWYgbmVlZGVkXG4gIGNvbnN0IHRvZGF5c0RhdGVkUHJpY2U6IERhdGVkUHJpY2UgPSB7XG4gICAgZGF0ZTogdG9kYXksXG4gICAgcHJpY2U6IHByb2R1Y3QuY3VycmVudFByaWNlLFxuICB9O1xuICBwcm9kdWN0LnByaWNlSGlzdG9yeSA9IFt0b2RheXNEYXRlZFByaWNlXTtcblxuICAvLyBVbml0IFByaWNlXG4gIC8vIC0tLS0tLS0tLS0tXG4gIC8vIFRyeSB0byBleHRyYWN0IGZyb20gc3Bhbi5jdXBQcmljZSwgZXg6ICQyLjUyIC8gMTAwbUxcbiAgY29uc3QgcmF3VW5pdFByaWNlID0gJChlbGVtZW50KS5maW5kKFwic3Bhbi5jdXBQcmljZVwiKS50ZXh0KCkudHJpbSgpO1xuXG4gIGlmIChyYXdVbml0UHJpY2UpIHtcbiAgICAvLyBFeHRyYWN0IGFuZCBwYXJzZSB1bml0IHByaWNlLCBleDogMi41MlxuICAgIGNvbnN0IHVuaXRQcmljZVN0cmluZyA9IHJhd1VuaXRQcmljZS5zcGxpdChcIi9cIilbMF0ucmVwbGFjZShcIiRcIiwgXCJcIikudHJpbSgpO1xuICAgIGxldCB1bml0UHJpY2UgPSBOdW1iZXIucGFyc2VGbG9hdCh1bml0UHJpY2VTdHJpbmcpO1xuXG4gICAgLy8gRXh0cmFjdCBhbW91bnQgYW5kIHVuaXQsIGV4OiAxMDBtTFxuICAgIGNvbnN0IGFtb3VudEFuZFVuaXQgPSByYXdVbml0UHJpY2Uuc3BsaXQoXCIvXCIpWzFdLnRyaW0oKTtcblxuICAgIC8vIFBhcnNlIGFtb3VudCwgZXg6IDEwMFxuICAgIGxldCBhbW91bnQgPSBOdW1iZXIucGFyc2VJbnQoYW1vdW50QW5kVW5pdC5tYXRjaCgvXFxkKy9nKT8uWzBdIHx8IFwiXCIpO1xuXG4gICAgLy8gRXh0cmFjdCB1bml0LCBleDogbUxcbiAgICBsZXQgdW5pdCA9IGFtb3VudEFuZFVuaXQubWF0Y2goL1xcdysvZyk/LlswXSB8fCBcIlwiXG5cbiAgICAvLyBOb3JtYWxpemUgdW5pdHMgdG8ga2cgb3IgTFxuICAgIGlmIChhbW91bnRBbmRVbml0ID09IFwiMTAwZ1wiKSB7XG4gICAgICBhbW91bnQgPSBhbW91bnQgKiAxMDtcbiAgICAgIHVuaXRQcmljZSA9IHVuaXRQcmljZSAqIDEwO1xuICAgICAgdW5pdCA9IFwia2dcIjtcbiAgICB9XG4gICAgZWxzZSBpZiAoYW1vdW50QW5kVW5pdCA9PSBcIjEwMG1MXCIpIHtcbiAgICAgIGFtb3VudCA9IGFtb3VudCAqIDEwO1xuICAgICAgdW5pdFByaWNlID0gdW5pdFByaWNlICogMTA7XG4gICAgICB1bml0ID0gXCJMXCI7XG4gICAgfVxuXG4gICAgLy8gQ2xlYW51cCAxa2cgdG8ganVzdCBrZ1xuICAgIHVuaXQgPSB1bml0LnJlcGxhY2UoXCIxa2dcIiwgXCJrZ1wiKTtcbiAgICB1bml0ID0gdW5pdC5yZXBsYWNlKFwiMUxcIiwgXCJMXCIpO1xuXG4gICAgLy8gU2V0IGZpbmFsaXNlZCB1bml0IHByaWNlIGFuZCBuYW1lXG4gICAgcHJvZHVjdC51bml0UHJpY2UgPSB1bml0UHJpY2U7XG4gICAgcHJvZHVjdC51bml0TmFtZSA9IHVuaXQ7XG4gIH1cblxuICAvLyBPdmVycmlkZXNcbiAgLy8gLS0tLS0tLS0tLVxuICAvLyBDaGVjayAudHMgZmlsZSBmb3IgbWFudWFsbHkgb3ZlcnJpZGRlbiBwcm9kdWN0IGRhdGFcbiAgcHJvZHVjdE92ZXJyaWRlcy5mb3JFYWNoKChvdmVycmlkZSkgPT4ge1xuICAgIC8vIEZpcnN0IGNoZWNrIGlmIHByb2R1Y3QgSUQgaGFzIGFueSBvdmVycmlkZXNcbiAgICBpZiAob3ZlcnJpZGUuaWQgPT09IHByb2R1Y3QuaWQpIHtcbiAgICAgIC8vIENoZWNrIGZvciBzaXplIG92ZXJyaWRlXG4gICAgICBpZiAob3ZlcnJpZGUuc2l6ZSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIHByb2R1Y3Quc2l6ZSA9IG92ZXJyaWRlLnNpemU7XG4gICAgICB9XG5cbiAgICAgIC8vIENoZWNrIGZvciBjYXRlZ29yeSBvdmVycmlkZVxuICAgICAgaWYgKG92ZXJyaWRlLmNhdGVnb3J5ICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgcHJvZHVjdC5jYXRlZ29yeSA9IFtvdmVycmlkZS5jYXRlZ29yeV07XG4gICAgICB9XG4gICAgfVxuICB9KTtcblxuICAvLyBWYWxpZGF0aW9uXG4gIC8vIC0tLS0tLS0tLS1cbiAgLy8gSWYgcHJvZHVjdCB2YWx1ZXMgcGFzcyB2YWxpZGF0aW9uLCByZXR1cm4gcHJvZHVjdFxuICBpZiAodmFsaWRhdGVQcm9kdWN0KHByb2R1Y3QpKSByZXR1cm4gcHJvZHVjdDtcbiAgZWxzZSB7XG4gICAgdHJ5IHtcbiAgICAgIGxvZ0Vycm9yKFxuICAgICAgICBgICBVbmFibGUgdG8gU2NyYXBlOiAke3Byb2R1Y3QuaWQucGFkU3RhcnQoNil9IHwgJHtwcm9kdWN0Lm5hbWV9IHwgYCArXG4gICAgICAgIGAkJHtwcm9kdWN0LmN1cnJlbnRQcmljZX1gXG4gICAgICApO1xuICAgIH0gY2F0Y2gge1xuICAgICAgbG9nRXJyb3IoXCIgIFVuYWJsZSB0byBTY3JhcGUgSUQgZnJvbSBwcm9kdWN0XCIpO1xuICAgIH1cbiAgICByZXR1cm4gdW5kZWZpbmVkO1xuICB9XG59XG5cbi8vIHZhbGlkYXRlUHJvZHVjdCgpXG4vLyAtLS0tLS0tLS0tLS0tLS0tLVxuLy8gQ2hlY2tzIHNjcmFwZWQgcHJvZHVjdCB2YWx1ZXMgYXJlIHdpdGhpbiByZWFzb25hYmxlIHJhbmdlc1xuXG5mdW5jdGlvbiB2YWxpZGF0ZVByb2R1Y3QocHJvZHVjdDogUHJvZHVjdCk6IGJvb2xlYW4ge1xuICB0cnkge1xuICAgIGlmIChwcm9kdWN0Lm5hbWUubWF0Y2goL1xcJFxcc1xcZCsvKSkgcmV0dXJuIGZhbHNlO1xuICAgIGlmIChwcm9kdWN0Lm5hbWUubGVuZ3RoIDwgNCB8fCBwcm9kdWN0Lm5hbWUubGVuZ3RoID4gMTAwKSByZXR1cm4gZmFsc2U7XG4gICAgaWYgKHByb2R1Y3QuaWQubGVuZ3RoIDwgMiB8fCBwcm9kdWN0LmlkLmxlbmd0aCA+IDIwKSByZXR1cm4gZmFsc2U7XG4gICAgaWYgKFxuICAgICAgcHJvZHVjdC5jdXJyZW50UHJpY2UgPD0gMCB8fFxuICAgICAgcHJvZHVjdC5jdXJyZW50UHJpY2UgPT09IG51bGwgfHxcbiAgICAgIHByb2R1Y3QuY3VycmVudFByaWNlID09PSB1bmRlZmluZWQgfHxcbiAgICAgIE51bWJlci5pc05hTihwcm9kdWN0LmN1cnJlbnRQcmljZSkgfHxcbiAgICAgIHByb2R1Y3QuY3VycmVudFByaWNlID4gOTk5XG4gICAgKSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIHJldHVybiB0cnVlO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxufVxuXG4vLyBwYXJzZUFuZENhdGVnb3Jpc2VVUkwoKVxuLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbi8vIFBhcnNlcyBhIFVSTCBzdHJpbmcsIGFuIG9wdGlvbmFsIGNhdGVnb3J5LCBvcHRpb25hbCBudW1iZXIgb2YgcGFnZXMgdG8gc2NyYXBlXG4vLyAgZnJvbSBhIHNpbmdsZSBsaW5lIG9mIHRleHQuXG4vLyBSZXR1cm5zIHVuZGVmaW5lZCBpZiBub3QgYSB2YWxpZCBVUkxcbi8vIEV4YW1wbGUgSW5wdXQ6XG4vLyAgICB3b29sd29ydGhzLmNvLm56L3Nob3AvYnJvd3NlL2Zyb3plbi9pY2UtY3JlYW0tc29yYmV0L3R1YnMgY2F0ZWdvcnk9aWNlLWNyZWFtIHBhZ2VzPTJcbi8vIEV4YW1wbGUgUmV0dXJuOlxuLy8gIFtcbi8vICAgIHtcbi8vICAgICAgICB1cmw6IFwiaHR0cHM6Ly93b29sd29ydGhzLmNvLm56L3Nob3AvYnJvd3NlL2Zyb3plbi9pY2UtY3JlYW0tc29yYmV0L3R1YnM/cGFnZT0xJmluU3RvY2tQcm9kdWN0c09ubHk9dHJ1ZVwiXG4vLyAgICAgICAgY2F0ZWdvcnk6IFwiaWNlLWNyZWFtXCJcbi8vICAgIH0sXG4vLyAgICB7XG4vLyAgICAgICAgdXJsOiBcImh0dHBzOi8vd29vbHdvcnRocy5jby5uei9zaG9wL2Jyb3dzZS9mcm96ZW4vaWNlLWNyZWFtLXNvcmJldC90dWJzP3BhZ2U9MiZpblN0b2NrUHJvZHVjdHNPbmx5PXRydWVcIlxuLy8gICAgICAgIGNhdGVnb3J5OiBcImljZS1jcmVhbVwiXG4vLyAgICB9XG4vLyAgXVxuXG5leHBvcnQgZnVuY3Rpb24gcGFyc2VBbmRDYXRlZ29yaXNlVVJMKFxuICBsaW5lOiBzdHJpbmdcbik6IENhdGVnb3Jpc2VkVXJsW10gfCB1bmRlZmluZWQge1xuICBsZXQgYmFzZUNhdGVnb3Jpc2VkVVJMOiBDYXRlZ29yaXNlZFVybCA9IHsgdXJsOiBcIlwiLCBjYXRlZ29yaWVzOiBbXSB9O1xuICBsZXQgcGFyc2VkVXJsczogQ2F0ZWdvcmlzZWRVcmxbXSA9IFtdO1xuICBsZXQgbnVtUGFnZXNQZXJVUkwgPSAxO1xuXG4gIC8vIElmIGxpbmUgZG9lc24ndCBjb250YWluIGRlc2lyZWQgdXJsIHNlY3Rpb24sIHJldHVybiB1bmRlZmluZWRcbiAgaWYgKCFsaW5lLmluY2x1ZGVzKFwid29vbHdvcnRocy5jby5uelwiKSkge1xuICAgIHJldHVybiB1bmRlZmluZWQ7XG5cbiAgICAvLyBJZiBsaW5lIGlzIGEgc2VhcmNoIHVybCwgcmV0dXJuIGFzLWlzXG4gIH0gZWxzZSBpZiAobGluZS5pbmNsdWRlcyhcIj9zZWFyY2g9XCIpKSB7XG4gICAgcGFyc2VkVXJscy5wdXNoKHsgdXJsOiBsaW5lLCBjYXRlZ29yaWVzOiBbXSB9KVxuXG4gICAgLy8gRWxzZSBvcHRpbWl6ZSBhbmQgY2xlYW51cCBVUkxcbiAgfSBlbHNlIHtcbiAgICAvLyBTcGxpdCBsaW5lIGJ5IGVtcHR5IHNwYWNlLCBsb29rIGZvciB1cmwsIG9wdGlvbmFsIHBhZ2UgYW1vdW50ICYgY2F0ZWdvcnlcbiAgICBsaW5lLnNwbGl0KFwiIFwiKS5mb3JFYWNoKChzZWN0aW9uKSA9PiB7XG5cbiAgICAgIC8vIFBhcnNlIFVSTFxuICAgICAgaWYgKHNlY3Rpb24uaW5jbHVkZXMoXCJ3b29sd29ydGhzLmNvLm56XCIpKSB7XG4gICAgICAgIGJhc2VDYXRlZ29yaXNlZFVSTC51cmwgPSBzZWN0aW9uO1xuXG4gICAgICAgIC8vIEVuc3VyZSBVUkwgaGFzIGh0dHA6Ly8gb3IgaHR0cHM6Ly9cbiAgICAgICAgaWYgKCFiYXNlQ2F0ZWdvcmlzZWRVUkwudXJsLnN0YXJ0c1dpdGgoXCJodHRwXCIpKVxuICAgICAgICAgIGJhc2VDYXRlZ29yaXNlZFVSTC51cmwgPSBcImh0dHBzOi8vXCIgKyBzZWN0aW9uO1xuXG4gICAgICAgIC8vIElmIHVybCBjb250YWlucyA/IGl0IGhhcyBxdWVyeSBvcHRpb25zIGFscmVhZHkgc2V0XG4gICAgICAgIGlmIChzZWN0aW9uLmluY2x1ZGVzKFwiP1wiKSkge1xuICAgICAgICAgIC8vIFN0cmlwIGFueSBleGlzdGluZyBxdWVyeSBvcHRpb25zIG9mZiBvZiBVUkxcbiAgICAgICAgICBiYXNlQ2F0ZWdvcmlzZWRVUkwudXJsID0gbGluZS5zdWJzdHJpbmcoMCwgbGluZS5pbmRleE9mKFwiP1wiKSk7XG4gICAgICAgIH1cbiAgICAgICAgLy8gUmVwbGFjZSBxdWVyeSBwYXJhbWV0ZXJzIHdpdGggb3B0aW1pc2VkIG9uZXNcbiAgICAgICAgYmFzZUNhdGVnb3Jpc2VkVVJMLnVybCArPSAnP3BhZ2U9MSc7XG5cbiAgICAgICAgLy8gUGFyc2UgQ2F0ZWdvcnlcbiAgICAgIH0gZWxzZSBpZiAoc2VjdGlvbi5zdGFydHNXaXRoKFwiY2F0ZWdvcmllcz1cIikpIHtcbiAgICAgICAgbGV0IHNwbGl0Q2F0ZWdvcmllcyA9IFtzZWN0aW9uLnJlcGxhY2UoXCJjYXRlZ29yaWVzPVwiLCBcIlwiKV07XG4gICAgICAgIGlmIChzZWN0aW9uLmluY2x1ZGVzKFwiLFwiKSlcbiAgICAgICAgICBzcGxpdENhdGVnb3JpZXMgPSBzZWN0aW9uLnJlcGxhY2UoXCJjYXRlZ29yaWVzPVwiLCBcIlwiKS5zcGxpdChcIixcIik7XG4gICAgICAgIGJhc2VDYXRlZ29yaXNlZFVSTC5jYXRlZ29yaWVzID0gc3BsaXRDYXRlZ29yaWVzO1xuXG4gICAgICAgIC8vIFBhcnNlIG51bWJlciBvZiBwYWdlc1xuICAgICAgfSBlbHNlIGlmIChzZWN0aW9uLnN0YXJ0c1dpdGgoXCJwYWdlcz1cIikpIHtcbiAgICAgICAgbnVtUGFnZXNQZXJVUkwgPSBOdW1iZXIucGFyc2VJbnQoc2VjdGlvbi5zcGxpdChcIj1cIilbMV0pO1xuICAgICAgfVxuXG4gICAgICAvLyBJZiBubyBjYXRlZ29yeSB3YXMgc3BlY2lmaWVkLCBkZXJpdmUgb25lIGZyb20gdGhlIGxhc3QgdXJsIC9zZWN0aW9uXG4gICAgICBpZiAoYmFzZUNhdGVnb3Jpc2VkVVJMLmNhdGVnb3JpZXMubGVuZ3RoID09PSAwKSB7XG4gICAgICAgIC8vIEV4dHJhY3QgL3NsYXNoU2VjdGlvbnMvIGZyb20gdXJsLCB3aGlsZSBleGNsdWRpbmcgY29udGVudCBhZnRlciAnPydcbiAgICAgICAgY29uc3QgYmFzZVVybCA9IGJhc2VDYXRlZ29yaXNlZFVSTC51cmwuc3BsaXQoXCI/XCIpWzBdO1xuICAgICAgICBsZXQgc2xhc2hTZWN0aW9ucyA9IGJhc2VVcmwuc3BsaXQoXCIvXCIpO1xuXG4gICAgICAgIC8vIFNldCBjYXRlZ29yeSB0byBsYXN0IHVybCAvc2VjdGlvbi9cbiAgICAgICAgYmFzZUNhdGVnb3Jpc2VkVVJMLmNhdGVnb3JpZXMgPSBbc2xhc2hTZWN0aW9uc1tzbGFzaFNlY3Rpb25zLmxlbmd0aCAtIDFdXTtcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIC8vIEZvciBtdWx0aXBsZSBwYWdlcywgZHVwbGljYXRlIHRoZSB1cmwgYW5kIGVkaXQgdGhlID9wYWdlPTEgcXVlcnkgcGFyYW1ldGVyXG4gICAgZm9yIChsZXQgaSA9IDE7IGkgPD0gbnVtUGFnZXNQZXJVUkw7IGkrKykge1xuICAgICAgbGV0IHBhZ2VkVXJsID0ge1xuICAgICAgICB1cmw6IGJhc2VDYXRlZ29yaXNlZFVSTC51cmwucmVwbGFjZShcInBhZ2U9MVwiLCBcInBhZ2U9XCIgKyBpKSxcbiAgICAgICAgY2F0ZWdvcmllczogYmFzZUNhdGVnb3Jpc2VkVVJMLmNhdGVnb3JpZXMsXG4gICAgICB9XG4gICAgICBwYXJzZWRVcmxzLnB1c2gocGFnZWRVcmwpO1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiBwYXJzZWRVcmxzO1xufVxuXG4vLyByb3V0ZVBsYXl3cmlnaHRFeGNsdXNpb25zKClcbi8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuLy8gRXhjbHVkZXMgYWRzLCB0cmFja2luZywgYW5kIGJhbmR3aWR0aCBpbnRlbnNpdmUgcmVzb3VyY2VzIGZyb20gYmVpbmcgZG93bmxvYWRlZCBieSBQbGF5d3JpZ2h0XG5cbmFzeW5jIGZ1bmN0aW9uIHJvdXRlUGxheXdyaWdodEV4Y2x1c2lvbnMoKSB7XG4gIGxldCB0eXBlRXhjbHVzaW9ucyA9IFtcImltYWdlXCIsIFwibWVkaWFcIiwgXCJmb250XCJdO1xuICBsZXQgdXJsRXhjbHVzaW9ucyA9IFtcbiAgICBcImdvb2dsZW9wdGltaXplLmNvbVwiLFxuICAgIFwiZ3RtLmpzXCIsXG4gICAgXCJ2aXNpdG9yaWRlbnRpZmljYXRpb24uanNcIixcbiAgICBcImpzLWFnZW50Lm5ld3JlbGljLmNvbVwiLFxuICAgIFwiY3F1b3RpZW50LmNvbVwiLFxuICAgIFwiZ29vZ2xldGFnbWFuYWdlci5jb21cIixcbiAgICBcImNsb3VkZmxhcmVpbnNpZ2h0cy5jb21cIixcbiAgICBcImR3YW5hbHl0aWNzXCIsXG4gICAgXCJmYWNlYm9vay5uZXRcIixcbiAgICBcImNoYXRXaWRnZXRcIixcbiAgICBcImVkZ2UuYWRvYmVkYy5uZXRcIixcbiAgICBcIlx1MjAwQi9Db250ZW50L0Jhbm5lcnMvXCIsXG4gICAgXCJnby1tcHVsc2UubmV0XCJcbiAgXTtcblxuICAvLyBSb3V0ZSB3aXRoIGV4Y2x1c2lvbnMgcHJvY2Vzc2VkXG4gIGF3YWl0IHBhZ2Uucm91dGUoXCIqKi8qXCIsIGFzeW5jIChyb3V0ZSkgPT4ge1xuICAgIGNvbnN0IHJlcSA9IHJvdXRlLnJlcXVlc3QoKTtcbiAgICBsZXQgZXhjbHVkZVRoaXNSZXF1ZXN0ID0gZmFsc2U7XG4gICAgLy9sZXQgdHJpbW1lZFVybCA9IHJlcS51cmwoKS5sZW5ndGggPiAxMjAgPyByZXEudXJsKCkuc3Vic3RyaW5nKDAsIDEyMCkgKyAnLi4uJyA6IHJlcS51cmwoKTtcblxuICAgIHVybEV4Y2x1c2lvbnMuZm9yRWFjaCgoZXhjbHVkZWRVUkwpID0+IHtcbiAgICAgIGlmIChyZXEudXJsKCkuaW5jbHVkZXMoZXhjbHVkZWRVUkwpKSBleGNsdWRlVGhpc1JlcXVlc3QgPSB0cnVlO1xuICAgIH0pO1xuXG4gICAgdHlwZUV4Y2x1c2lvbnMuZm9yRWFjaCgoZXhjbHVkZWRUeXBlKSA9PiB7XG4gICAgICBpZiAocmVxLnJlc291cmNlVHlwZSgpID09PSBleGNsdWRlZFR5cGUpIGV4Y2x1ZGVUaGlzUmVxdWVzdCA9IHRydWU7XG4gICAgfSk7XG5cbiAgICBpZiAoZXhjbHVkZVRoaXNSZXF1ZXN0KSB7XG4gICAgICAvL2xvZ0Vycm9yKGAke3JlcS5tZXRob2QoKX0gJHtyZXEucmVzb3VyY2VUeXBlKCl9IC0gJHt0cmltbWVkVXJsfWApO1xuICAgICAgYXdhaXQgcm91dGUuYWJvcnQoKTtcbiAgICB9IGVsc2Uge1xuICAgICAgLy9sb2coY29sb3VyLndoaXRlLCBgJHtyZXEubWV0aG9kKCl9ICR7cmVxLnJlc291cmNlVHlwZSgpfSAtICR7dHJpbW1lZFVybH1gKTtcbiAgICAgIGF3YWl0IHJvdXRlLmNvbnRpbnVlKCk7XG4gICAgfVxuICB9KTtcblxuICByZXR1cm47XG59XG5cbiIsICJpbXBvcnQgeyBNb25nb0NsaWVudCwgRGIsIENvbGxlY3Rpb24sIEdyaWRGU0J1Y2tldCwgT2JqZWN0SWQgfSBmcm9tIFwibW9uZ29kYlwiO1xyXG5pbXBvcnQgKiBhcyBkb3RlbnYgZnJvbSBcImRvdGVudlwiO1xyXG5kb3RlbnYuY29uZmlnKCk7XHJcbmRvdGVudi5jb25maWcoeyBwYXRoOiBgLmVudi5sb2NhbGAsIG92ZXJyaWRlOiB0cnVlIH0pO1xyXG5cclxuaW1wb3J0IHsgUHJvZHVjdCwgVXBzZXJ0UmVzcG9uc2UgfSBmcm9tIFwiLi90eXBpbmdzLmpzXCI7XHJcbmltcG9ydCB7IGNvbG91ciwgbG9nLCBsb2dFcnJvciB9IGZyb20gXCIuL3V0aWxpdGllcy5qc1wiO1xyXG5cclxubGV0IGNsaWVudDogTW9uZ29DbGllbnQ7XHJcbmxldCBkYjogRGI7XHJcbmxldCBncmlkRlM6IEdyaWRGU0J1Y2tldDtcclxubGV0IHN0b3JlSWQ6IE9iamVjdElkIHwgdW5kZWZpbmVkOyAvLyBXb29sd29ydGhzIHN0b3JlIE9iamVjdElkXHJcblxyXG4vLyBDb2xsZWN0aW9uc1xyXG5sZXQgc3RvcmVzQ29sbGVjdGlvbjogQ29sbGVjdGlvbjtcclxubGV0IGJyYW5kc0NvbGxlY3Rpb246IENvbGxlY3Rpb247XHJcbmxldCBjb25zb2xpZGF0ZWRQcm9kdWN0c0NvbGxlY3Rpb246IENvbGxlY3Rpb247XHJcbmxldCBwcmljZUhpc3RvcnlDb2xsZWN0aW9uOiBDb2xsZWN0aW9uO1xyXG5sZXQgY2F0ZWdvcnlIaWVyYXJjaHlDb2xsZWN0aW9uOiBDb2xsZWN0aW9uO1xyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGVzdGFibGlzaE1vbmdvREIoKSB7XHJcbiAgY29uc3QgY29ubmVjdGlvblN0cmluZyA9IHByb2Nlc3MuZW52Lk1PTkdPREJfQ09OTkVDVElPTl9TVFJJTkc7XHJcbiAgY29uc3QgZGF0YWJhc2VOYW1lID0gcHJvY2Vzcy5lbnYuTU9OR09EQl9EQVRBQkFTRV9OQU1FIHx8IFwibnotc3VwZXJtYXJrZXQtc2NyYXBlclwiO1xyXG4gIFxyXG4gIGlmICghY29ubmVjdGlvblN0cmluZykge1xyXG4gICAgdGhyb3cgRXJyb3IoXCJNT05HT0RCX0NPTk5FQ1RJT05fU1RSSU5HIG5vdCBzZXQgaW4gZW52XCIpO1xyXG4gIH1cclxuXHJcbiAgdHJ5IHtcclxuICAgIGNsaWVudCA9IG5ldyBNb25nb0NsaWVudChjb25uZWN0aW9uU3RyaW5nKTtcclxuICAgIGF3YWl0IGNsaWVudC5jb25uZWN0KCk7XHJcbiAgICBkYiA9IGNsaWVudC5kYihkYXRhYmFzZU5hbWUpO1xyXG4gICAgXHJcbiAgICAvLyBJbml0aWFsaXplIGNvbGxlY3Rpb25zXHJcbiAgICBzdG9yZXNDb2xsZWN0aW9uID0gZGIuY29sbGVjdGlvbihcInN0b3Jlc1wiKTtcclxuICAgIGJyYW5kc0NvbGxlY3Rpb24gPSBkYi5jb2xsZWN0aW9uKFwiYnJhbmRzXCIpO1xyXG4gICAgY29uc29saWRhdGVkUHJvZHVjdHNDb2xsZWN0aW9uID0gZGIuY29sbGVjdGlvbihcImNvbnNvbGlkYXRlZFByb2R1Y3RzXCIpO1xyXG4gICAgcHJpY2VIaXN0b3J5Q29sbGVjdGlvbiA9IGRiLmNvbGxlY3Rpb24oXCJwcmljZUhpc3RvcnlcIik7XHJcbiAgICBjYXRlZ29yeUhpZXJhcmNoeUNvbGxlY3Rpb24gPSBkYi5jb2xsZWN0aW9uKFwiY2F0ZWdvcnlIaWVyYXJjaHlcIik7XHJcbiAgICBcclxuICAgIC8vIEluaXRpYWxpemUgR3JpZEZTIGZvciBpbWFnZSBzdG9yYWdlXHJcbiAgICBncmlkRlMgPSBuZXcgR3JpZEZTQnVja2V0KGRiLCB7IGJ1Y2tldE5hbWU6IFwicHJvZHVjdEltYWdlc1wiIH0pO1xyXG4gICAgXHJcbiAgICBsb2coY29sb3VyLmdyZWVuLCBcIlx1MjcwNSBNb25nb0RCIGNvbm5lY3Rpb24gZXN0YWJsaXNoZWRcIik7XHJcbiAgICBcclxuICAgIC8vIENyZWF0ZSBpbmRleGVzIGZvciBwZXJmb3JtYW5jZVxyXG4gICAgYXdhaXQgY3JlYXRlSW5kZXhlcygpO1xyXG4gICAgXHJcbiAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xyXG4gICAgbG9nRXJyb3IoYEZhaWxlZCB0byBjb25uZWN0IHRvIE1vbmdvREI6ICR7ZXJyb3IubWVzc2FnZX1gKTtcclxuICAgIHRocm93IGVycm9yO1xyXG4gIH1cclxufVxyXG5cclxuYXN5bmMgZnVuY3Rpb24gY3JlYXRlSW5kZXhlcygpIHtcclxuICB0cnkge1xyXG4gICAgLy8gQ29uc29saWRhdGVkIHByb2R1Y3RzIGluZGV4ZXNcclxuICAgIGF3YWl0IGNvbnNvbGlkYXRlZFByb2R1Y3RzQ29sbGVjdGlvbi5jcmVhdGVJbmRleCh7IFxyXG4gICAgICBcImRpc3BsYXlOYW1lXCI6IFwidGV4dFwiLCBcclxuICAgICAgXCJub3JtYWxpemVkTmFtZVwiOiBcInRleHRcIiwgXHJcbiAgICAgIFwidmFyaWFudHMuc3RvcmVOYW1lXCI6IFwidGV4dFwiIFxyXG4gICAgfSk7XHJcbiAgICBhd2FpdCBjb25zb2xpZGF0ZWRQcm9kdWN0c0NvbGxlY3Rpb24uY3JlYXRlSW5kZXgoeyBcImNhdGVnb3J5SWRcIjogMSB9KTtcclxuICAgIGF3YWl0IGNvbnNvbGlkYXRlZFByb2R1Y3RzQ29sbGVjdGlvbi5jcmVhdGVJbmRleCh7IFwiYnJhbmRJZFwiOiAxIH0pO1xyXG4gICAgYXdhaXQgY29uc29saWRhdGVkUHJvZHVjdHNDb2xsZWN0aW9uLmNyZWF0ZUluZGV4KHsgXHJcbiAgICAgIFwidmFyaWFudHMuc3RvcmVQcm9kdWN0SWRcIjogMSwgXHJcbiAgICAgIFwidmFyaWFudHMuc3RvcmVJZFwiOiAxIFxyXG4gICAgfSk7XHJcbiAgICBcclxuICAgIC8vIFByaWNlIGhpc3RvcnkgaW5kZXhlc1xyXG4gICAgYXdhaXQgcHJpY2VIaXN0b3J5Q29sbGVjdGlvbi5jcmVhdGVJbmRleCh7IFxyXG4gICAgICBcImNvbnNvbGlkYXRlZFByb2R1Y3RJZFwiOiAxLCBcclxuICAgICAgXCJyZWNvcmRlZEF0XCI6IC0xIFxyXG4gICAgfSk7XHJcbiAgICBhd2FpdCBwcmljZUhpc3RvcnlDb2xsZWN0aW9uLmNyZWF0ZUluZGV4KHsgXCJ5ZWFyXCI6IDEsIFwibW9udGhcIjogMSB9KTtcclxuICAgIFxyXG4gICAgLy8gQ2F0ZWdvcnkgaGllcmFyY2h5IGluZGV4ZXNcclxuICAgIGF3YWl0IGNhdGVnb3J5SGllcmFyY2h5Q29sbGVjdGlvbi5jcmVhdGVJbmRleCh7IFwicGFyZW50SWRcIjogMSwgXCJzb3J0T3JkZXJcIjogMSB9KTtcclxuICAgIGF3YWl0IGNhdGVnb3J5SGllcmFyY2h5Q29sbGVjdGlvbi5jcmVhdGVJbmRleCh7IFwibGV2ZWxcIjogMSwgXCJzb3J0T3JkZXJcIjogMSB9KTtcclxuICAgIFxyXG4gICAgbG9nKGNvbG91ci5ibHVlLCBcIlx1MjcwNSBNb25nb0RCIGluZGV4ZXMgY3JlYXRlZFwiKTtcclxuICB9IGNhdGNoIChlcnJvcjogYW55KSB7XHJcbiAgICBsb2dFcnJvcihgRmFpbGVkIHRvIGNyZWF0ZSBpbmRleGVzOiAke2Vycm9yLm1lc3NhZ2V9YCk7XHJcbiAgfVxyXG59XHJcblxyXG5hc3luYyBmdW5jdGlvbiBlbnN1cmVTdG9yZVJvdygpOiBQcm9taXNlPE9iamVjdElkPiB7XHJcbiAgaWYgKHN0b3JlSWQgIT09IHVuZGVmaW5lZCkgcmV0dXJuIHN0b3JlSWQ7XHJcbiAgXHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IGV4aXN0aW5nU3RvcmUgPSBhd2FpdCBzdG9yZXNDb2xsZWN0aW9uLmZpbmRPbmUoeyBzdG9yZUlkOiBcIndvb2x3b3J0aHNcIiB9KTtcclxuICAgIFxyXG4gICAgaWYgKGV4aXN0aW5nU3RvcmUpIHtcclxuICAgICAgc3RvcmVJZCA9IGV4aXN0aW5nU3RvcmUuX2lkO1xyXG4gICAgICByZXR1cm4gc3RvcmVJZDtcclxuICAgIH1cclxuXHJcbiAgICAvLyBJbnNlcnQgc3RvcmUgaWYgbm90IGV4aXN0c1xyXG4gICAgY29uc3QgaW5zZXJ0UmVzdWx0ID0gYXdhaXQgc3RvcmVzQ29sbGVjdGlvbi5pbnNlcnRPbmUoe1xyXG4gICAgICBzdG9yZUlkOiBcIndvb2x3b3J0aHNcIixcclxuICAgICAgbmFtZTogXCJXb29sd29ydGhzXCIsXHJcbiAgICAgIGxvZ29Vcmw6IG51bGwsXHJcbiAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKSxcclxuICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpXHJcbiAgICB9KTtcclxuICAgIFxyXG4gICAgc3RvcmVJZCA9IGluc2VydFJlc3VsdC5pbnNlcnRlZElkO1xyXG4gICAgcmV0dXJuIHN0b3JlSWQ7XHJcbiAgICBcclxuICB9IGNhdGNoIChlcnJvcjogYW55KSB7XHJcbiAgICBsb2dFcnJvcihgRmFpbGVkIHRvIGVuc3VyZSBzdG9yZSByb3c6ICR7ZXJyb3IubWVzc2FnZX1gKTtcclxuICAgIHRocm93IGVycm9yO1xyXG4gIH1cclxufVxyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHVwc2VydFByb2R1Y3RUb01vbmdvREIoc2NyYXBlZDogUHJvZHVjdCk6IFByb21pc2U8VXBzZXJ0UmVzcG9uc2U+IHtcclxuICBpZiAoIWRiKSB0aHJvdyBFcnJvcihcIk1vbmdvREIgY2xpZW50IG5vdCBpbml0aWFsaXNlZFwiKTtcclxuICBcclxuICB0cnkge1xyXG4gICAgY29uc3Qgc0lkID0gYXdhaXQgZW5zdXJlU3RvcmVSb3coKTtcclxuICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7XHJcbiAgICBcclxuICAgIC8vIEZpbmQgb3IgY3JlYXRlIGNvbnNvbGlkYXRlZCBwcm9kdWN0XHJcbiAgICBsZXQgY29uc29saWRhdGVkUHJvZHVjdCA9IGF3YWl0IGNvbnNvbGlkYXRlZFByb2R1Y3RzQ29sbGVjdGlvbi5maW5kT25lKHtcclxuICAgICAgXCJ2YXJpYW50cy5zdG9yZVByb2R1Y3RJZFwiOiBzY3JhcGVkLmlkLFxyXG4gICAgICBcInZhcmlhbnRzLnN0b3JlSWRcIjogc0lkXHJcbiAgICB9KTtcclxuICAgIFxyXG4gICAgaWYgKCFjb25zb2xpZGF0ZWRQcm9kdWN0KSB7XHJcbiAgICAgIC8vIFRyeSB0byBmaW5kIG1hdGNoaW5nIGNvbnNvbGlkYXRlZCBwcm9kdWN0IHVzaW5nIGVuaGFuY2VkIGFsZ29yaXRobVxyXG4gICAgICBjb25zdCBub3JtYWxpemVkTmFtZSA9IG5vcm1hbGl6ZVByb2R1Y3ROYW1lKHNjcmFwZWQubmFtZSwgc2NyYXBlZC5zaXplKTtcclxuICAgICAgbG9nKGNvbG91ci5jeWFuLCBgXHVEODNEXHVERDBEIFByb2Nlc3Npbmc6ICR7c2NyYXBlZC5uYW1lfSAtPiBub3JtYWxpemVkOiAke25vcm1hbGl6ZWROYW1lfWApO1xyXG5cclxuICAgICAgY29uc3QgbWF0Y2hpbmdQcm9kdWN0ID0gYXdhaXQgZmluZEJlc3RNYXRjaChub3JtYWxpemVkTmFtZSwgc2NyYXBlZC5zaXplLCBzY3JhcGVkLm5hbWUpO1xyXG5cclxuICAgICAgaWYgKG1hdGNoaW5nUHJvZHVjdCkge1xyXG4gICAgICAgIC8vIEZvdW5kIGEgbWF0Y2ggLSBhZGQgdGhpcyBhcyBhIG5ldyB2YXJpYW50IGFuZCBhZGQgYWxpYXNcclxuICAgICAgICBjb25zb2xpZGF0ZWRQcm9kdWN0ID0gbWF0Y2hpbmdQcm9kdWN0O1xyXG5cclxuICAgICAgICAvLyBBZGQgY3VycmVudCBwcm9kdWN0IG5hbWUgYXMgYWxpYXMgaWYgZGlmZmVyZW50XHJcbiAgICAgICAgYXdhaXQgYWRkUHJvZHVjdEFsaWFzKGNvbnNvbGlkYXRlZFByb2R1Y3QuX2lkLCBzY3JhcGVkLm5hbWUpO1xyXG5cclxuICAgICAgICAvLyBBZGQgbmV3IHZhcmlhbnQgdG8gZXhpc3RpbmcgY29uc29saWRhdGVkIHByb2R1Y3RcclxuICAgICAgICBjb25zdCBuZXdWYXJpYW50ID0ge1xyXG4gICAgICAgICAgc3RvcmVQcm9kdWN0SWQ6IHNjcmFwZWQuaWQsXHJcbiAgICAgICAgICBzdG9yZUlkOiBzSWQsXHJcbiAgICAgICAgICBzdG9yZU5hbWU6IHNjcmFwZWQubmFtZSxcclxuICAgICAgICAgIHN0b3JlU2l6ZTogc2NyYXBlZC5zaXplLFxyXG4gICAgICAgICAgc3RvcmVVbml0UHJpY2U6IHNjcmFwZWQudW5pdFByaWNlLFxyXG4gICAgICAgICAgc3RvcmVVbml0TmFtZTogc2NyYXBlZC51bml0TmFtZSxcclxuICAgICAgICAgIGxhc3RTZWVuOiBub3csXHJcbiAgICAgICAgICBpc0FjdGl2ZTogdHJ1ZSxcclxuICAgICAgICAgIGltYWdlVXJsOiBudWxsXHJcbiAgICAgICAgfTtcclxuXHJcbiAgICAgICAgYXdhaXQgY29uc29saWRhdGVkUHJvZHVjdHNDb2xsZWN0aW9uLnVwZGF0ZU9uZShcclxuICAgICAgICAgIHsgX2lkOiBjb25zb2xpZGF0ZWRQcm9kdWN0Ll9pZCB9LFxyXG4gICAgICAgICAge1xyXG4gICAgICAgICAgICAkcHVzaDogeyB2YXJpYW50czogbmV3VmFyaWFudCB9LFxyXG4gICAgICAgICAgICAkc2V0OiB7IHVwZGF0ZWRBdDogbm93IH1cclxuICAgICAgICAgIH1cclxuICAgICAgICApO1xyXG5cclxuICAgICAgICBsb2coY29sb3VyLmdyZWVuLCBgXHUyNzA1IEFkZGVkIHZhcmlhbnQgdG8gZXhpc3RpbmcgcHJvZHVjdDogJHttYXRjaGluZ1Byb2R1Y3QuZGlzcGxheU5hbWV9YCk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgLy8gQ3JlYXRlIG5ldyBjb25zb2xpZGF0ZWQgcHJvZHVjdFxyXG4gICAgICAgIGNvbnN0IG5ld1Byb2R1Y3QgPSB7XHJcbiAgICAgICAgICBub3JtYWxpemVkTmFtZSxcclxuICAgICAgICAgIGRpc3BsYXlOYW1lOiBzY3JhcGVkLm5hbWUsXHJcbiAgICAgICAgICBwcmltYXJ5U2l6ZTogc2NyYXBlZC5zaXplLFxyXG4gICAgICAgICAgY2F0ZWdvcnlJZDogbnVsbCwgLy8gVE9ETzogSW1wbGVtZW50IGNhdGVnb3J5IG1hcHBpbmdcclxuICAgICAgICAgIGJyYW5kSWQ6IG51bGwsIC8vIFRPRE86IEltcGxlbWVudCBicmFuZCBleHRyYWN0aW9uXHJcbiAgICAgICAgICBtYXRjaENvbmZpZGVuY2U6IDEwMCxcclxuICAgICAgICAgIG1hbnVhbE1hdGNoOiBmYWxzZSxcclxuICAgICAgICAgIGFsaWFzZXM6IFtdLCAvLyBJbml0aWFsaXplIGVtcHR5IGFsaWFzZXMgYXJyYXlcclxuICAgICAgICAgIHZhcmlhbnRzOiBbe1xyXG4gICAgICAgICAgICBzdG9yZVByb2R1Y3RJZDogc2NyYXBlZC5pZCxcclxuICAgICAgICAgICAgc3RvcmVJZDogc0lkLFxyXG4gICAgICAgICAgICBzdG9yZU5hbWU6IHNjcmFwZWQubmFtZSxcclxuICAgICAgICAgICAgc3RvcmVTaXplOiBzY3JhcGVkLnNpemUsXHJcbiAgICAgICAgICAgIHN0b3JlVW5pdFByaWNlOiBzY3JhcGVkLnVuaXRQcmljZSxcclxuICAgICAgICAgICAgc3RvcmVVbml0TmFtZTogc2NyYXBlZC51bml0TmFtZSxcclxuICAgICAgICAgICAgbGFzdFNlZW46IG5vdyxcclxuICAgICAgICAgICAgaXNBY3RpdmU6IHRydWUsXHJcbiAgICAgICAgICAgIGltYWdlVXJsOiBudWxsXHJcbiAgICAgICAgICB9XSxcclxuICAgICAgICAgIHNpemVWYXJpYW50czogc2NyYXBlZC5zaXplID8gW3tcclxuICAgICAgICAgICAgc2l6ZU5hbWU6IHNjcmFwZWQuc2l6ZSxcclxuICAgICAgICAgICAgc2l6ZVdlaWdodEdyYW1zOiBudWxsLFxyXG4gICAgICAgICAgICBzaXplVm9sdW1lTWw6IG51bGwsXHJcbiAgICAgICAgICAgIGlzUHJpbWFyeVNpemU6IHRydWVcclxuICAgICAgICAgIH1dIDogW10sXHJcbiAgICAgICAgICBjdXJyZW50UHJpY2VzOiBbe1xyXG4gICAgICAgICAgICBzdG9yZUlkOiBzSWQsXHJcbiAgICAgICAgICAgIHByaWNlOiBzY3JhcGVkLmN1cnJlbnRQcmljZSxcclxuICAgICAgICAgICAgaXNTcGVjaWFsOiBmYWxzZSxcclxuICAgICAgICAgICAgd2FzQXZhaWxhYmxlOiB0cnVlLFxyXG4gICAgICAgICAgICByZWNvcmRlZEF0OiBub3dcclxuICAgICAgICAgIH1dLFxyXG4gICAgICAgICAgY3JlYXRlZEF0OiBub3csXHJcbiAgICAgICAgICB1cGRhdGVkQXQ6IG5vd1xyXG4gICAgICAgIH07XHJcblxyXG4gICAgICAgIGNvbnN0IGluc2VydFJlc3VsdCA9IGF3YWl0IGNvbnNvbGlkYXRlZFByb2R1Y3RzQ29sbGVjdGlvbi5pbnNlcnRPbmUobmV3UHJvZHVjdCk7XHJcbiAgICAgICAgY29uc29saWRhdGVkUHJvZHVjdCA9IHsgX2lkOiBpbnNlcnRSZXN1bHQuaW5zZXJ0ZWRJZCwgLi4ubmV3UHJvZHVjdCB9O1xyXG5cclxuICAgICAgICBsb2coY29sb3VyLmJsdWUsIGBcdUQ4M0NcdUREOTUgQ3JlYXRlZCBuZXcgY29uc29saWRhdGVkIHByb2R1Y3Q6ICR7c2NyYXBlZC5uYW1lfWApO1xyXG4gICAgICB9XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICAvLyBVcGRhdGUgZXhpc3RpbmcgY29uc29saWRhdGVkIHByb2R1Y3RcclxuICAgICAgYXdhaXQgY29uc29saWRhdGVkUHJvZHVjdHNDb2xsZWN0aW9uLnVwZGF0ZU9uZShcclxuICAgICAgICB7IF9pZDogY29uc29saWRhdGVkUHJvZHVjdC5faWQgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICAkc2V0OiB7XHJcbiAgICAgICAgICAgIFwidmFyaWFudHMuJFt2YXJpYW50XS5sYXN0U2VlblwiOiBub3csXHJcbiAgICAgICAgICAgIFwidmFyaWFudHMuJFt2YXJpYW50XS5zdG9yZVVuaXRQcmljZVwiOiBzY3JhcGVkLnVuaXRQcmljZSxcclxuICAgICAgICAgICAgXCJ2YXJpYW50cy4kW3ZhcmlhbnRdLnN0b3JlVW5pdE5hbWVcIjogc2NyYXBlZC51bml0TmFtZSxcclxuICAgICAgICAgICAgXCJjdXJyZW50UHJpY2VzLiRbcHJpY2VdLnByaWNlXCI6IHNjcmFwZWQuY3VycmVudFByaWNlLFxyXG4gICAgICAgICAgICBcImN1cnJlbnRQcmljZXMuJFtwcmljZV0ucmVjb3JkZWRBdFwiOiBub3csXHJcbiAgICAgICAgICAgIHVwZGF0ZWRBdDogbm93XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICBhcnJheUZpbHRlcnM6IFtcclxuICAgICAgICAgICAgeyBcInZhcmlhbnQuc3RvcmVQcm9kdWN0SWRcIjogc2NyYXBlZC5pZCB9LFxyXG4gICAgICAgICAgICB7IFwicHJpY2Uuc3RvcmVJZFwiOiBzSWQgfVxyXG4gICAgICAgICAgXVxyXG4gICAgICAgIH1cclxuICAgICAgKTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgLy8gSW5zZXJ0IHByaWNlIGhpc3RvcnkgcmVjb3JkXHJcbiAgICBhd2FpdCBwcmljZUhpc3RvcnlDb2xsZWN0aW9uLmluc2VydE9uZSh7XHJcbiAgICAgIGNvbnNvbGlkYXRlZFByb2R1Y3RJZDogY29uc29saWRhdGVkUHJvZHVjdC5faWQsXHJcbiAgICAgIHN0b3JlSWQ6IHNJZCxcclxuICAgICAgcHJpY2U6IHNjcmFwZWQuY3VycmVudFByaWNlLFxyXG4gICAgICBpc1NwZWNpYWw6IGZhbHNlLFxyXG4gICAgICB3YXNBdmFpbGFibGU6IHRydWUsXHJcbiAgICAgIHJlY29yZGVkQXQ6IG5vdyxcclxuICAgICAgeWVhcjogbm93LmdldEZ1bGxZZWFyKCksXHJcbiAgICAgIG1vbnRoOiBub3cuZ2V0TW9udGgoKSArIDFcclxuICAgIH0pO1xyXG4gICAgXHJcbiAgICBsb2coY29sb3VyLmdyZWVuLCBgICBVcHNlcnRlZDogJHtzY3JhcGVkLm5hbWUuc2xpY2UoMCwgNDUpLnBhZEVuZCg0NSl9IHwgJCR7c2NyYXBlZC5jdXJyZW50UHJpY2V9YCk7XHJcbiAgICByZXR1cm4gVXBzZXJ0UmVzcG9uc2UuUHJpY2VDaGFuZ2VkO1xyXG4gICAgXHJcbiAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xyXG4gICAgbG9nRXJyb3IoYE1vbmdvREIgdXBzZXJ0IGZhaWxlZDogJHtlcnJvci5tZXNzYWdlfWApO1xyXG4gICAgcmV0dXJuIFVwc2VydFJlc3BvbnNlLkZhaWxlZDtcclxuICB9XHJcbn1cclxuXHJcbi8vIFVwbG9hZCBwcm9kdWN0IGltYWdlIHRvIE1vbmdvREIgR3JpZEZTXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB1cGxvYWRJbWFnZVRvTW9uZ29EQihpbWFnZVVybDogc3RyaW5nLCBwcm9kdWN0OiBQcm9kdWN0KTogUHJvbWlzZTxib29sZWFuPiB7XHJcbiAgaWYgKCFncmlkRlMpIHRocm93IEVycm9yKFwiTW9uZ29EQiBHcmlkRlMgbm90IGluaXRpYWxpc2VkXCIpO1xyXG5cclxuICB0cnkge1xyXG4gICAgY29uc3Qgc0lkID0gYXdhaXQgZW5zdXJlU3RvcmVSb3coKTtcclxuICAgIFxyXG4gICAgLy8gRG93bmxvYWQgaW1hZ2UgZnJvbSBXb29sd29ydGhzXHJcbiAgICBjb25zdCBpbWFnZVJlc3BvbnNlID0gYXdhaXQgZmV0Y2goaW1hZ2VVcmwpO1xyXG4gICAgaWYgKCFpbWFnZVJlc3BvbnNlLm9rKSB7XHJcbiAgICAgIGxvZ0Vycm9yKGBGYWlsZWQgdG8gZG93bmxvYWQgaW1hZ2UgZnJvbSAke2ltYWdlVXJsfTogJHtpbWFnZVJlc3BvbnNlLnN0YXR1c1RleHR9YCk7XHJcbiAgICAgIHJldHVybiBmYWxzZTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBpbWFnZUJ1ZmZlciA9IEJ1ZmZlci5mcm9tKGF3YWl0IGltYWdlUmVzcG9uc2UuYXJyYXlCdWZmZXIoKSk7XHJcbiAgICBcclxuICAgIC8vIENoZWNrIGlmIGltYWdlIGFscmVhZHkgZXhpc3RzIGFuZCBkZWxldGUgaXRcclxuICAgIGNvbnN0IGV4aXN0aW5nRmlsZXMgPSBhd2FpdCBncmlkRlMuZmluZCh7IFxyXG4gICAgICBcIm1ldGFkYXRhLnByb2R1Y3RJZFwiOiBwcm9kdWN0LmlkIFxyXG4gICAgfSkudG9BcnJheSgpO1xyXG4gICAgXHJcbiAgICBmb3IgKGNvbnN0IGZpbGUgb2YgZXhpc3RpbmdGaWxlcykge1xyXG4gICAgICBhd2FpdCBncmlkRlMuZGVsZXRlKGZpbGUuX2lkKTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgLy8gQ3JlYXRlIHVwbG9hZCBzdHJlYW1cclxuICAgIGNvbnN0IGZpbGVuYW1lID0gYCR7cHJvZHVjdC5pZH0uanBnYDtcclxuICAgIGNvbnN0IHVwbG9hZFN0cmVhbSA9IGdyaWRGUy5vcGVuVXBsb2FkU3RyZWFtKGZpbGVuYW1lLCB7XHJcbiAgICAgIGNvbnRlbnRUeXBlOiAnaW1hZ2UvanBlZycsXHJcbiAgICAgIG1ldGFkYXRhOiB7XHJcbiAgICAgICAgcHJvZHVjdElkOiBwcm9kdWN0LmlkLFxyXG4gICAgICAgIHN0b3JlSWQ6IHNJZCxcclxuICAgICAgICBvcmlnaW5hbFVybDogaW1hZ2VVcmwsXHJcbiAgICAgICAgdXBsb2FkZWRBdDogbmV3IERhdGUoKVxyXG4gICAgICB9XHJcbiAgICB9KTtcclxuICAgIFxyXG4gICAgLy8gVXBsb2FkIHRoZSBpbWFnZVxyXG4gICAgYXdhaXQgbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xyXG4gICAgICB1cGxvYWRTdHJlYW0uZW5kKGltYWdlQnVmZmVyLCAoZXJyb3IpID0+IHtcclxuICAgICAgICBpZiAoZXJyb3IpIHJlamVjdChlcnJvcik7XHJcbiAgICAgICAgZWxzZSByZXNvbHZlKHVwbG9hZFN0cmVhbS5pZCk7XHJcbiAgICAgIH0pO1xyXG4gICAgfSk7XHJcbiAgICBcclxuICAgIC8vIFVwZGF0ZSBwcm9kdWN0IHdpdGggaW1hZ2UgVVJMIHJlZmVyZW5jZVxyXG4gICAgY29uc3QgaW1hZ2VVcmxfZ3JpZGZzID0gYGdyaWRmczovL3Byb2R1Y3RJbWFnZXMvJHt1cGxvYWRTdHJlYW0uaWR9YDtcclxuICAgIFxyXG4gICAgYXdhaXQgY29uc29saWRhdGVkUHJvZHVjdHNDb2xsZWN0aW9uLnVwZGF0ZU9uZShcclxuICAgICAgeyBcInZhcmlhbnRzLnN0b3JlUHJvZHVjdElkXCI6IHByb2R1Y3QuaWQgfSxcclxuICAgICAge1xyXG4gICAgICAgICRzZXQ6IHtcclxuICAgICAgICAgIFwidmFyaWFudHMuJFt2YXJpYW50XS5pbWFnZVVybFwiOiBpbWFnZVVybF9ncmlkZnNcclxuICAgICAgICB9XHJcbiAgICAgIH0sXHJcbiAgICAgIHtcclxuICAgICAgICBhcnJheUZpbHRlcnM6IFt7IFwidmFyaWFudC5zdG9yZVByb2R1Y3RJZFwiOiBwcm9kdWN0LmlkIH1dXHJcbiAgICAgIH1cclxuICAgICk7XHJcbiAgICBcclxuICAgIGxvZyhjb2xvdXIuYmx1ZSwgYCAgSW1hZ2UgdXBsb2FkZWQ6ICR7cHJvZHVjdC5pZH0gLT4gR3JpZEZTICR7dXBsb2FkU3RyZWFtLmlkfWApO1xyXG4gICAgcmV0dXJuIHRydWU7XHJcblxyXG4gIH0gY2F0Y2ggKGVycjogYW55KSB7XHJcbiAgICBsb2dFcnJvcihgSW1hZ2UgdXBsb2FkIGVycm9yOiAke2Vyci5tZXNzYWdlfWApO1xyXG4gICAgcmV0dXJuIGZhbHNlO1xyXG4gIH1cclxufVxyXG5cclxuLy8gRW5oYW5jZWQgcHJvZHVjdCBuYW1lIG5vcm1hbGl6YXRpb24gd2l0aCBtYW51YWwgbWFwcGluZ3NcclxuZnVuY3Rpb24gbm9ybWFsaXplUHJvZHVjdE5hbWUobmFtZTogc3RyaW5nLCBzaXplPzogc3RyaW5nKTogc3RyaW5nIHtcclxuICBpZiAoIW5hbWUpIHJldHVybiAnJztcclxuXHJcbiAgbGV0IG5vcm1hbGl6ZWQgPSBuYW1lLnRvTG93ZXJDYXNlKClcclxuICAgIC5yZXBsYWNlKC8nL2csICcnKVxyXG4gICAgLnJlcGxhY2UoL1wiL2csICcnKVxyXG4gICAgLnJlcGxhY2UoLy0vZywgJyAnKVxyXG4gICAgLnJlcGxhY2UoL1xccysvZywgJyAnKVxyXG4gICAgLnRyaW0oKTtcclxuXHJcbiAgLy8gUmVtb3ZlIGNvbW1vbiBzdG9yZSBkZXNjcmlwdG9yc1xyXG4gIGNvbnN0IGRlc2NyaXB0b3JzID0gWyd3b29sd29ydGhzJywgJ2NvdW50ZG93bicsICduZXcgd29ybGQnLCAncGFrbnNhdmUnLCAncGFrIG4gc2F2ZScsXHJcbiAgICAgICAgICAgICAgICAgICAgICAnc2VsZWN0JywgJ3ByZW1pdW0nLCAndmFsdWUnLCAnYnVkZ2V0JywgJ3NpZ25hdHVyZScsICdlc3NlbnRpYWxzJyxcclxuICAgICAgICAgICAgICAgICAgICAgICdwYW1zJywgJ2hvbWVicmFuZCcsICdzaWduYXR1cmUgcmFuZ2UnLCAnZnJlc2ggY2hvaWNlJ107XHJcblxyXG4gIGZvciAoY29uc3QgZGVzY3JpcHRvciBvZiBkZXNjcmlwdG9ycykge1xyXG4gICAgbm9ybWFsaXplZCA9IG5vcm1hbGl6ZWQucmVwbGFjZShuZXcgUmVnRXhwKGBcXFxcYiR7ZGVzY3JpcHRvcn1cXFxcYmAsICdnJyksICcnKS50cmltKCk7XHJcbiAgfVxyXG5cclxuICAvLyBBcHBseSBjb21wcmVoZW5zaXZlIE5aIGJyYW5kIG1hcHBpbmdzXHJcbiAgY29uc3QgbnpCcmFuZE1hcHBpbmdzID0gZ2V0TlpCcmFuZE1hcHBpbmdzKCk7XHJcblxyXG4gIC8vIEFwcGx5IGJyYW5kIG1hcHBpbmdzXHJcbiAgZm9yIChjb25zdCBbY2Fub25pY2FsLCB2YXJpYXRpb25zXSBvZiBPYmplY3QuZW50cmllcyhuekJyYW5kTWFwcGluZ3MpKSB7XHJcbiAgICBpZiAobm9ybWFsaXplZC5pbmNsdWRlcyhjYW5vbmljYWwpKSB7XHJcbiAgICAgIG5vcm1hbGl6ZWQgPSBub3JtYWxpemVkLnJlcGxhY2UoY2Fub25pY2FsLCBjYW5vbmljYWwpO1xyXG4gICAgICBicmVhazsgLy8gVXNlIGZpcnN0IG1hdGNoIHRvIGF2b2lkIG11bHRpcGxlIHJlcGxhY2VtZW50c1xyXG4gICAgfVxyXG5cclxuICAgIC8vIENoZWNrIHZhcmlhdGlvbnNcclxuICAgIGZvciAoY29uc3QgdmFyaWF0aW9uIG9mIHZhcmlhdGlvbnMpIHtcclxuICAgICAgaWYgKG5vcm1hbGl6ZWQuaW5jbHVkZXModmFyaWF0aW9uKSkge1xyXG4gICAgICAgIG5vcm1hbGl6ZWQgPSBub3JtYWxpemVkLnJlcGxhY2UodmFyaWF0aW9uLCBjYW5vbmljYWwpO1xyXG4gICAgICAgIGJyZWFrO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvLyBJbmNsdWRlIHNpemUgaWYgcHJvdmlkZWRcclxuICBpZiAoc2l6ZSkge1xyXG4gICAgY29uc3Qgbm9ybWFsaXplZFNpemUgPSBzaXplLnRvTG93ZXJDYXNlKClcclxuICAgICAgLnJlcGxhY2UoL2dyYW1zPy9nLCAnZycpXHJcbiAgICAgIC5yZXBsYWNlKC9raWxvZ3JhbXM/L2csICdrZycpXHJcbiAgICAgIC5yZXBsYWNlKC9saXRyZXM/L2csICdsJylcclxuICAgICAgLnJlcGxhY2UoL21pbGxpbGl0cmVzPy9nLCAnbWwnKTtcclxuICAgIG5vcm1hbGl6ZWQgKz0gYCAke25vcm1hbGl6ZWRTaXplfWA7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gbm9ybWFsaXplZC50cmltKCk7XHJcbn1cclxuXHJcbi8vIEdldCBjb21wcmVoZW5zaXZlIE5aIGJyYW5kIG1hcHBpbmdzXHJcbmZ1bmN0aW9uIGdldE5aQnJhbmRNYXBwaW5ncygpOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmdbXT4ge1xyXG4gIHJldHVybiB7XHJcbiAgICAvLyBTdXBlcm1hcmtldCBQcml2YXRlIExhYmVsc1xyXG4gICAgJ3BhbXMnOiBbJ3BhbScsICdwYW1zIGJyYW5kJywgJ3BhbXMgc2VsZWN0J10sXHJcbiAgICAnZXNzZW50aWFscyc6IFsnZXNzZW50aWFscyBicmFuZCcsICdjb3VudGRvd24gZXNzZW50aWFscyddLFxyXG4gICAgJ2hvbWVicmFuZCc6IFsnaG9tZSBicmFuZCcsICdob21lYnJhbmQgc2VsZWN0J10sXHJcbiAgICAnc2lnbmF0dXJlJzogWydzaWduYXR1cmUgcmFuZ2UnLCAnc2lnbmF0dXJlIGJyYW5kJ10sXHJcblxyXG4gICAgLy8gRGFpcnkgQnJhbmRzXHJcbiAgICAnYW5jaG9yJzogWydhbmNob3IgYnJhbmQnLCAnYW5jaG9yIGRhaXJ5JywgJ2FuY2hvciBtaWxrJywgJ2FuY2hvciBidXR0ZXInXSxcclxuICAgICdtYWlubGFuZCc6IFsnbWFpbmxhbmQgY2hlZXNlJywgJ21haW5sYW5kIGRhaXJ5JywgJ21haW5sYW5kIGJyYW5kJ10sXHJcbiAgICAnbWVhZG93ZnJlc2gnOiBbJ21lYWRvdyBmcmVzaCcsICdtZWFkb3dmcmVzaCBtaWxrJywgJ21lYWRvdyBmcmVzaCBtaWxrJ10sXHJcbiAgICAnbGV3aXMgcm9hZCc6IFsnbGV3aXMgcm9hZCBjcmVhbWVyeScsICdsZXdpcyByb2FkIG1pbGsnLCAnbGV3aXMgcm9hZCBidXR0ZXInXSxcclxuICAgICdrYXBpdGknOiBbJ2thcGl0aSBjaGVlc2UnLCAna2FwaXRpIGljZSBjcmVhbScsICdrYXBpdGkgYnJhbmQnXSxcclxuXHJcbiAgICAvLyBNZWF0IEJyYW5kc1xyXG4gICAgJ3RlZ2VsJzogWyd0ZWdlbCBjaGlja2VuJywgJ3RlZ2VsIHBvdWx0cnknLCAndGVnZWwgYnJhbmQnXSxcclxuICAgICdpbmdoYW1zJzogWydpbmdoYW0nLCAnaW5naGFtcyBjaGlja2VuJywgJ2luZ2hhbXMgcG91bHRyeSddLFxyXG4gICAgJ2hlbGxlcnMnOiBbJ2hlbGxlcicsICdoZWxsZXJzIGJhY29uJywgJ2hlbGxlcnMgc2F1c2FnZXMnXSxcclxuICAgICdiZWVoaXZlJzogWydiZWVoaXZlIGJhY29uJywgJ2JlZWhpdmUgaGFtJywgJ2JlZWhpdmUgc21hbGxnb29kcyddLFxyXG5cclxuICAgIC8vIEJyZWFkIEJyYW5kc1xyXG4gICAgJ3RpcCB0b3AnOiBbJ3RpcHRvcCcsICd0aXAgdG9wIGJyZWFkJywgJ3RpcHRvcCBicmVhZCddLFxyXG4gICAgJ21vbGVuYmVyZyc6IFsnbW9sZW5iZXJnIGJyZWFkJywgJ21vbGVuYnVyZycsICdtb2xlbmJlcmcgd2hvbGVtZWFsJ10sXHJcbiAgICAndm9nZWxzJzogWyd2b2dlbCcsICd2b2dlbHMgYnJlYWQnLCAndm9nZWwgYnJlYWQnLCAndm9nZWxzIG9yaWdpbmFsJ10sXHJcbiAgICAnZnJleWFzJzogWydmcmV5YScsICdmcmV5YXMgYnJlYWQnLCAnZnJleWEgYnJlYWQnXSxcclxuXHJcbiAgICAvLyBCZXZlcmFnZSBCcmFuZHNcclxuICAgICdjb2tlJzogWydjb2NhIGNvbGEnLCAnY29jYS1jb2xhJywgJ2Nva2UgY2xhc3NpYycsICdjb2NhIGNvbGEgY2xhc3NpYyddLFxyXG4gICAgJ2Nva2UgemVybyc6IFsnY29jYSBjb2xhIHplcm8nLCAnY29rZSB6ZXJvIHN1Z2FyJywgJ2NvY2EgY29sYSB6ZXJvIHN1Z2FyJ10sXHJcbiAgICAncGVwc2knOiBbJ3BlcHNpIGNvbGEnLCAncGVwc2kgY2xhc3NpYycsICdwZXBzaSBvcmlnaW5hbCddLFxyXG4gICAgJ2wmcCc6IFsnbGVtb24gcGFlcm9hJywgJ2xlbW9uIGFuZCBwYWVyb2EnLCAnbCBhbmQgcCddLFxyXG5cclxuICAgIC8vIENlcmVhbCBCcmFuZHNcclxuICAgICdzYW5pdGFyaXVtJzogWydzYW5pdGFyaXVtIHdlZXRiaXgnLCAnc2FuaXRhcml1bSBzbyBnb29kJ10sXHJcbiAgICAnd2VldGJpeCc6IFsnd2VldCBiaXgnLCAnd2hlYXQgYmlzY3VpdHMnLCAnc2FuaXRhcml1bSB3ZWV0Yml4J10sXHJcbiAgICAndW5jbGUgdG9ieXMnOiBbJ3VuY2xlIHRvYnknLCAndW5jbGUgdG9ieXMgb2F0cycsICd1bmNsZSB0b2J5cyBtdWVzbGknXSxcclxuICAgICdrZWxsb2dncyc6IFsna2VsbG9nZycsICdrZWxsb2dncyBjb3JuZmxha2VzJywgJ2tlbGxvZ2dzIHNwZWNpYWwgayddLFxyXG5cclxuICAgIC8vIFBhbnRyeSBCcmFuZHNcclxuICAgICd3YXR0aWVzJzogWyd3YXR0aWUnLCAnd2F0dGllcyB0b21hdG9lcycsICd3YXR0aWVzIHNhdWNlJ10sXHJcbiAgICAnaGVpbnonOiBbJ2hlaW56IGJlYW5zJywgJ2hlaW56IHRvbWF0byBzYXVjZScsICdoZWlueiBzb3VwJ10sXHJcbiAgICAnbWNjYWlucyc6IFsnbWNjYWluJywgJ21jY2FpbnMgY2hpcHMnLCAnbWNjYWlucyBmcm96ZW4nXSxcclxuICAgICdtYWdnaSc6IFsnbWFnZ2kgbm9vZGxlcycsICdtYWdnaSBzb3VwJywgJ21hZ2dpIGluc3RhbnQnXSxcclxuXHJcbiAgICAvLyBTbmFjayBCcmFuZHNcclxuICAgICdjYWRidXJ5JzogWydjYWRidXJ5cycsICdjYWRidXJ5IGNob2NvbGF0ZSddLFxyXG4gICAgJ3doaXR0YWtlcnMnOiBbJ3doaXR0YWtlcicsICd3aGl0dGFrZXJzIGNob2NvbGF0ZSddLFxyXG4gICAgJ2JsdWViaXJkJzogWydibHVlYmlyZCBjaGlwcycsICdibHVlYmlyZCBzbmFja3MnXSxcclxuICAgICdldGEnOiBbJ2V0YSBjaGlwcycsICdldGEgc25hY2tzJywgJ2V0YSBwZWFudXQgYnV0dGVyJ10sXHJcblxyXG4gICAgLy8gQ2xlYW5pbmcgQnJhbmRzXHJcbiAgICAnamFub2xhJzogWydqYW5vbGEgYmxlYWNoJywgJ2phbm9sYSBjbGVhbmluZyddLFxyXG4gICAgJ2ZpbmlzaCc6IFsnZmluaXNoIGRpc2h3YXNoZXInLCAnZmluaXNoIHRhYmxldHMnXSxcclxuICAgICdwZXJzaWwnOiBbJ3BlcnNpbCB3YXNoaW5nIHBvd2RlcicsICdwZXJzaWwgbGlxdWlkJ10sXHJcbiAgICAnc29yYmVudCc6IFsnc29yYmVudCB0b2lsZXQgcGFwZXInLCAnc29yYmVudCB0aXNzdWVzJ10sXHJcblxyXG4gICAgLy8gQWRkaXRpb25hbCBOWiBCcmFuZHNcclxuICAgICdncmlmZmlucyc6IFsnZ3JpZmZpbicsICdncmlmZmlucyBiaXNjdWl0cycsICdncmlmZmluIGJpc2N1aXRzJ10sXHJcbiAgICAnZWRtb25kcyc6IFsnZWRtb25kcyBmbG91cicsICdlZG1vbmRzIGJha2luZyddLFxyXG4gICAgJ2NoZWxzZWEnOiBbJ2NoZWxzZWEgc3VnYXInLCAnY2hlbHNlYSBiYWtpbmcnXSxcclxuICAgICdwaWNzJzogWydwaWMnLCAncGljIHBlYW51dCBidXR0ZXInLCAncGljcyBwZWFudXQgYnV0dGVyJ11cclxuICB9O1xyXG59XHJcblxyXG4vLyBDYWxjdWxhdGUgTGV2ZW5zaHRlaW4gZGlzdGFuY2UgZm9yIHN0cmluZyBzaW1pbGFyaXR5XHJcbmZ1bmN0aW9uIGxldmVuc2h0ZWluRGlzdGFuY2UoczE6IHN0cmluZywgczI6IHN0cmluZyk6IG51bWJlciB7XHJcbiAgaWYgKCFzMSkgcmV0dXJuIHMyID8gczIubGVuZ3RoIDogMDtcclxuICBpZiAoIXMyKSByZXR1cm4gczEubGVuZ3RoO1xyXG5cclxuICBjb25zdCBtYXRyaXg6IG51bWJlcltdW10gPSBbXTtcclxuXHJcbiAgZm9yIChsZXQgaSA9IDA7IGkgPD0gczIubGVuZ3RoOyBpKyspIHtcclxuICAgIG1hdHJpeFtpXSA9IFtpXTtcclxuICB9XHJcblxyXG4gIGZvciAobGV0IGogPSAwOyBqIDw9IHMxLmxlbmd0aDsgaisrKSB7XHJcbiAgICBtYXRyaXhbMF1bal0gPSBqO1xyXG4gIH1cclxuXHJcbiAgZm9yIChsZXQgaSA9IDE7IGkgPD0gczIubGVuZ3RoOyBpKyspIHtcclxuICAgIGZvciAobGV0IGogPSAxOyBqIDw9IHMxLmxlbmd0aDsgaisrKSB7XHJcbiAgICAgIGNvbnN0IGNvc3QgPSBzMVtqIC0gMV0gPT09IHMyW2kgLSAxXSA/IDAgOiAxO1xyXG4gICAgICBtYXRyaXhbaV1bal0gPSBNYXRoLm1pbihcclxuICAgICAgICBtYXRyaXhbaSAtIDFdW2pdICsgMSxcclxuICAgICAgICBtYXRyaXhbaV1baiAtIDFdICsgMSxcclxuICAgICAgICBtYXRyaXhbaSAtIDFdW2ogLSAxXSArIGNvc3RcclxuICAgICAgKTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIHJldHVybiBtYXRyaXhbczIubGVuZ3RoXVtzMS5sZW5ndGhdO1xyXG59XHJcblxyXG4vLyBDYWxjdWxhdGUgc2ltaWxhcml0eSBzY29yZSBiZXR3ZWVuIHR3byBwcm9kdWN0IG5hbWVzXHJcbmZ1bmN0aW9uIGNhbGN1bGF0ZVNpbWlsYXJpdHkobmFtZTE6IHN0cmluZywgbmFtZTI6IHN0cmluZyk6IG51bWJlciB7XHJcbiAgaWYgKCFuYW1lMSB8fCAhbmFtZTIpIHJldHVybiAwO1xyXG4gIGlmIChuYW1lMSA9PT0gbmFtZTIpIHJldHVybiAxLjA7XHJcblxyXG4gIGNvbnN0IGRpc3RhbmNlID0gbGV2ZW5zaHRlaW5EaXN0YW5jZShuYW1lMSwgbmFtZTIpO1xyXG4gIGNvbnN0IG1heExlbmd0aCA9IE1hdGgubWF4KG5hbWUxLmxlbmd0aCwgbmFtZTIubGVuZ3RoKTtcclxuXHJcbiAgcmV0dXJuIG1heExlbmd0aCA9PT0gMCA/IDEuMCA6IDEuMCAtIGRpc3RhbmNlIC8gbWF4TGVuZ3RoO1xyXG59XHJcblxyXG4vLyBGaW5kIGJlc3QgbWF0Y2hpbmcgY29uc29saWRhdGVkIHByb2R1Y3QgdXNpbmcgZW5oYW5jZWQgYWxnb3JpdGhtXHJcbmFzeW5jIGZ1bmN0aW9uIGZpbmRCZXN0TWF0Y2gobm9ybWFsaXplZE5hbWU6IHN0cmluZywgc2l6ZTogc3RyaW5nIHwgdW5kZWZpbmVkLCBvcmlnaW5hbE5hbWU6IHN0cmluZyk6IFByb21pc2U8YW55PiB7XHJcbiAgdHJ5IHtcclxuICAgIC8vIEZpcnN0IHRyeSBleGFjdCBtYXRjaFxyXG4gICAgY29uc3QgZXhhY3RNYXRjaCA9IGF3YWl0IGNvbnNvbGlkYXRlZFByb2R1Y3RzQ29sbGVjdGlvbi5maW5kT25lKHtcclxuICAgICAgbm9ybWFsaXplZE5hbWU6IG5vcm1hbGl6ZWROYW1lXHJcbiAgICB9KTtcclxuXHJcbiAgICBpZiAoZXhhY3RNYXRjaCkge1xyXG4gICAgICBsb2coY29sb3VyLmdyZWVuLCBgXHUyNzA1IEV4YWN0IG1hdGNoIGZvdW5kOiAke2V4YWN0TWF0Y2guZGlzcGxheU5hbWV9YCk7XHJcbiAgICAgIHJldHVybiBleGFjdE1hdGNoO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIFRyeSBmdXp6eSBtYXRjaGluZ1xyXG4gICAgY29uc3QgYWxsUHJvZHVjdHMgPSBhd2FpdCBjb25zb2xpZGF0ZWRQcm9kdWN0c0NvbGxlY3Rpb24uZmluZCh7fSkubGltaXQoMTAwMCkudG9BcnJheSgpO1xyXG5cclxuICAgIGxldCBiZXN0TWF0Y2g6IGFueSA9IG51bGw7XHJcbiAgICBsZXQgYmVzdFNjb3JlID0gMDtcclxuICAgIGNvbnN0IHRocmVzaG9sZCA9IDAuODtcclxuXHJcbiAgICBmb3IgKGNvbnN0IHByb2R1Y3Qgb2YgYWxsUHJvZHVjdHMpIHtcclxuICAgICAgbGV0IHNjb3JlID0gY2FsY3VsYXRlU2ltaWxhcml0eShub3JtYWxpemVkTmFtZSwgcHJvZHVjdC5ub3JtYWxpemVkTmFtZSk7XHJcblxyXG4gICAgICAvLyBDaGVjayBhbGlhc2VzIGlmIHRoZXkgZXhpc3RcclxuICAgICAgaWYgKHByb2R1Y3QuYWxpYXNlcyAmJiBBcnJheS5pc0FycmF5KHByb2R1Y3QuYWxpYXNlcykpIHtcclxuICAgICAgICBmb3IgKGNvbnN0IGFsaWFzIG9mIHByb2R1Y3QuYWxpYXNlcykge1xyXG4gICAgICAgICAgY29uc3QgYWxpYXNTY29yZSA9IGNhbGN1bGF0ZVNpbWlsYXJpdHkobm9ybWFsaXplZE5hbWUsIGFsaWFzKTtcclxuICAgICAgICAgIHNjb3JlID0gTWF0aC5tYXgoc2NvcmUsIGFsaWFzU2NvcmUpO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgaWYgKHNjb3JlID4gYmVzdFNjb3JlICYmIHNjb3JlID49IHRocmVzaG9sZCkge1xyXG4gICAgICAgIGJlc3RTY29yZSA9IHNjb3JlO1xyXG4gICAgICAgIGJlc3RNYXRjaCA9IHByb2R1Y3Q7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBpZiAoYmVzdE1hdGNoKSB7XHJcbiAgICAgIGxvZyhjb2xvdXIueWVsbG93LCBgXHUyNzA1IEZ1enp5IG1hdGNoIGZvdW5kOiAke2Jlc3RNYXRjaC5kaXNwbGF5TmFtZX0gKGNvbmZpZGVuY2U6ICR7YmVzdFNjb3JlLnRvRml4ZWQoMyl9KWApO1xyXG4gICAgICByZXR1cm4gYmVzdE1hdGNoO1xyXG4gICAgfVxyXG5cclxuICAgIGxvZyhjb2xvdXIucmVkLCBgXHUyNzRDIE5vIG1hdGNoIGZvdW5kIGZvcjogJHtvcmlnaW5hbE5hbWV9YCk7XHJcbiAgICByZXR1cm4gbnVsbDtcclxuICB9IGNhdGNoIChlcnJvcjogYW55KSB7XHJcbiAgICBsb2dFcnJvcihgXHUyNzRDIEVycm9yIGZpbmRpbmcgbWF0Y2g6ICR7ZXJyb3IubWVzc2FnZX1gKTtcclxuICAgIHJldHVybiBudWxsO1xyXG4gIH1cclxufVxyXG5cclxuLy8gQWRkIGFsaWFzIHRvIGNvbnNvbGlkYXRlZCBwcm9kdWN0XHJcbmFzeW5jIGZ1bmN0aW9uIGFkZFByb2R1Y3RBbGlhcyhjb25zb2xpZGF0ZWRQcm9kdWN0SWQ6IGFueSwgbmV3QWxpYXM6IHN0cmluZyk6IFByb21pc2U8dm9pZD4ge1xyXG4gIHRyeSB7XHJcbiAgICBjb25zdCBub3JtYWxpemVkQWxpYXMgPSBub3JtYWxpemVQcm9kdWN0TmFtZShuZXdBbGlhcyk7XHJcbiAgICBpZiAoIW5vcm1hbGl6ZWRBbGlhcykgcmV0dXJuO1xyXG5cclxuICAgIGNvbnN0IHByb2R1Y3QgPSBhd2FpdCBjb25zb2xpZGF0ZWRQcm9kdWN0c0NvbGxlY3Rpb24uZmluZE9uZSh7IF9pZDogY29uc29saWRhdGVkUHJvZHVjdElkIH0pO1xyXG4gICAgaWYgKCFwcm9kdWN0KSByZXR1cm47XHJcblxyXG4gICAgLy8gR2V0IGN1cnJlbnQgYWxpYXNlcyBvciBpbml0aWFsaXplIGVtcHR5IGFycmF5XHJcbiAgICBjb25zdCBjdXJyZW50QWxpYXNlcyA9IHByb2R1Y3QuYWxpYXNlcyB8fCBbXTtcclxuXHJcbiAgICAvLyBEb24ndCBhZGQgaWYgYWxpYXMgYWxyZWFkeSBleGlzdHMgb3IgaXMgdGhlIHNhbWUgYXMgbm9ybWFsaXplZCBuYW1lXHJcbiAgICBpZiAobm9ybWFsaXplZEFsaWFzID09PSBwcm9kdWN0Lm5vcm1hbGl6ZWROYW1lIHx8IGN1cnJlbnRBbGlhc2VzLmluY2x1ZGVzKG5vcm1hbGl6ZWRBbGlhcykpIHJldHVybjtcclxuXHJcbiAgICAvLyBBZGQgbmV3IGFsaWFzXHJcbiAgICBjdXJyZW50QWxpYXNlcy5wdXNoKG5vcm1hbGl6ZWRBbGlhcyk7XHJcblxyXG4gICAgYXdhaXQgY29uc29saWRhdGVkUHJvZHVjdHNDb2xsZWN0aW9uLnVwZGF0ZU9uZShcclxuICAgICAgeyBfaWQ6IGNvbnNvbGlkYXRlZFByb2R1Y3RJZCB9LFxyXG4gICAgICB7XHJcbiAgICAgICAgJHNldDoge1xyXG4gICAgICAgICAgYWxpYXNlczogY3VycmVudEFsaWFzZXMsXHJcbiAgICAgICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKClcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgICk7XHJcblxyXG4gICAgbG9nKGNvbG91ci5jeWFuLCBgXHVEODNEXHVEQ0REIEFkZGVkIGFsaWFzICcke25vcm1hbGl6ZWRBbGlhc30nIHRvIHByb2R1Y3QgJHtjb25zb2xpZGF0ZWRQcm9kdWN0SWR9YCk7XHJcbiAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xyXG4gICAgbG9nRXJyb3IoYFx1Mjc0QyBFcnJvciBhZGRpbmcgYWxpYXM6ICR7ZXJyb3IubWVzc2FnZX1gKTtcclxuICB9XHJcbn1cclxuXHJcbi8vIEdldCBpbWFnZSBzdHJlYW0gZnJvbSBHcmlkRlNcclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldEltYWdlRnJvbUdyaWRGUyhmaWxlSWQ6IHN0cmluZyk6IFByb21pc2U8Tm9kZUpTLlJlYWRhYmxlU3RyZWFtIHwgbnVsbD4ge1xyXG4gIGlmICghZ3JpZEZTKSByZXR1cm4gbnVsbDtcclxuXHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IG9iamVjdElkID0gbmV3IE9iamVjdElkKGZpbGVJZCk7XHJcbiAgICByZXR1cm4gZ3JpZEZTLm9wZW5Eb3dubG9hZFN0cmVhbShvYmplY3RJZCk7XHJcbiAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xyXG4gICAgbG9nRXJyb3IoYEZhaWxlZCB0byBnZXQgaW1hZ2UgZnJvbSBHcmlkRlM6ICR7ZXJyb3IubWVzc2FnZX1gKTtcclxuICAgIHJldHVybiBudWxsO1xyXG4gIH1cclxufVxyXG5cclxuLy8gQ2xvc2UgTW9uZ29EQiBjb25uZWN0aW9uXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBjbG9zZU1vbmdvREIoKSB7XHJcbiAgaWYgKGNsaWVudCkge1xyXG4gICAgYXdhaXQgY2xpZW50LmNsb3NlKCk7XHJcbiAgICBsb2coY29sb3VyLmJsdWUsIFwiTW9uZ29EQiBjb25uZWN0aW9uIGNsb3NlZFwiKTtcclxuICB9XHJcbn1cclxuXHJcbi8vIEhlYWx0aCBjaGVja1xyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gbW9uZ29IZWFsdGhDaGVjaygpOiBQcm9taXNlPGJvb2xlYW4+IHtcclxuICB0cnkge1xyXG4gICAgYXdhaXQgZGIuYWRtaW4oKS5waW5nKCk7XHJcbiAgICByZXR1cm4gdHJ1ZTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgcmV0dXJuIGZhbHNlO1xyXG4gIH1cclxufSIsICJpbXBvcnQgeyBNb25nb0NsaWVudCwgRGIsIENvbGxlY3Rpb24sIE9iamVjdElkIH0gZnJvbSBcIm1vbmdvZGJcIjtcclxuaW1wb3J0ICogYXMgZG90ZW52IGZyb20gXCJkb3RlbnZcIjtcclxuZG90ZW52LmNvbmZpZygpO1xyXG5cclxuaW1wb3J0IHsgUHJvZHVjdCB9IGZyb20gXCIuL3R5cGluZ3MuanNcIjtcclxuaW1wb3J0IHsgY29sb3VyLCBsb2csIGxvZ0Vycm9yIH0gZnJvbSBcIi4vdXRpbGl0aWVzLmpzXCI7XHJcblxyXG5sZXQgY2xpZW50OiBNb25nb0NsaWVudDtcclxubGV0IGRiOiBEYjtcclxubGV0IGNvbnNvbGlkYXRlZFByb2R1Y3RzQ29sbGVjdGlvbjogQ29sbGVjdGlvbjtcclxubGV0IGJyYW5kc0NvbGxlY3Rpb246IENvbGxlY3Rpb247XHJcbmxldCBjYXRlZ29yeUhpZXJhcmNoeUNvbGxlY3Rpb246IENvbGxlY3Rpb247XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gaW5pdGlhbGl6ZUNvbnNvbGlkYXRlZFByb2R1Y3RzTW9uZ29EQigpIHtcclxuICBjb25zdCBjb25uZWN0aW9uU3RyaW5nID0gcHJvY2Vzcy5lbnYuTU9OR09EQl9DT05ORUNUSU9OX1NUUklORztcclxuICBjb25zdCBkYXRhYmFzZU5hbWUgPSBwcm9jZXNzLmVudi5NT05HT0RCX0RBVEFCQVNFX05BTUUgfHwgXCJuei1zdXBlcm1hcmtldC1zY3JhcGVyXCI7XHJcbiAgXHJcbiAgaWYgKCFjb25uZWN0aW9uU3RyaW5nKSB7XHJcbiAgICB0aHJvdyBFcnJvcihcIk1PTkdPREJfQ09OTkVDVElPTl9TVFJJTkcgbm90IHNldCBpbiBlbnZcIik7XHJcbiAgfVxyXG5cclxuICB0cnkge1xyXG4gICAgY2xpZW50ID0gbmV3IE1vbmdvQ2xpZW50KGNvbm5lY3Rpb25TdHJpbmcpO1xyXG4gICAgYXdhaXQgY2xpZW50LmNvbm5lY3QoKTtcclxuICAgIGRiID0gY2xpZW50LmRiKGRhdGFiYXNlTmFtZSk7XHJcbiAgICBcclxuICAgIGNvbnNvbGlkYXRlZFByb2R1Y3RzQ29sbGVjdGlvbiA9IGRiLmNvbGxlY3Rpb24oXCJjb25zb2xpZGF0ZWRQcm9kdWN0c1wiKTtcclxuICAgIGJyYW5kc0NvbGxlY3Rpb24gPSBkYi5jb2xsZWN0aW9uKFwiYnJhbmRzXCIpO1xyXG4gICAgY2F0ZWdvcnlIaWVyYXJjaHlDb2xsZWN0aW9uID0gZGIuY29sbGVjdGlvbihcImNhdGVnb3J5SGllcmFyY2h5XCIpO1xyXG4gICAgXHJcbiAgICBsb2coY29sb3VyLmdyZWVuLCBcIlx1MjcwNSBDb25zb2xpZGF0ZWQgcHJvZHVjdHMgTW9uZ29EQiBpbml0aWFsaXplZFwiKTtcclxuICAgIFxyXG4gICAgLy8gSW5pdGlhbGl6ZSBiYXNpYyBjYXRlZ29yaWVzIGlmIHRoZXkgZG9uJ3QgZXhpc3RcclxuICAgIGF3YWl0IGVuc3VyZUJhc2ljQ2F0ZWdvcmllcygpO1xyXG4gICAgXHJcbiAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xyXG4gICAgbG9nRXJyb3IoYEZhaWxlZCB0byBpbml0aWFsaXplIGNvbnNvbGlkYXRlZCBwcm9kdWN0cyBNb25nb0RCOiAke2Vycm9yLm1lc3NhZ2V9YCk7XHJcbiAgICB0aHJvdyBlcnJvcjtcclxuICB9XHJcbn1cclxuXHJcbmFzeW5jIGZ1bmN0aW9uIGVuc3VyZUJhc2ljQ2F0ZWdvcmllcygpIHtcclxuICB0cnkge1xyXG4gICAgY29uc3QgZXhpc3RpbmdDYXRlZ29yaWVzID0gYXdhaXQgY2F0ZWdvcnlIaWVyYXJjaHlDb2xsZWN0aW9uLmNvdW50RG9jdW1lbnRzKCk7XHJcbiAgICBcclxuICAgIGlmIChleGlzdGluZ0NhdGVnb3JpZXMgPT09IDApIHtcclxuICAgICAgLy8gSW5zZXJ0IG1haW4gY2F0ZWdvcmllc1xyXG4gICAgICBjb25zdCBtYWluQ2F0ZWdvcmllcyA9IFtcclxuICAgICAgICB7IG5hbWU6IFwiRnJlc2ggRm9vZHNcIiwgcGFyZW50SWQ6IG51bGwsIGxldmVsOiAwLCBzb3J0T3JkZXI6IDEgfSxcclxuICAgICAgICB7IG5hbWU6IFwiQ2hpbGxlZCAmIEZyb3plblwiLCBwYXJlbnRJZDogbnVsbCwgbGV2ZWw6IDAsIHNvcnRPcmRlcjogMiB9LFxyXG4gICAgICAgIHsgbmFtZTogXCJQYW50cnkgJiBEcnkgR29vZHNcIiwgcGFyZW50SWQ6IG51bGwsIGxldmVsOiAwLCBzb3J0T3JkZXI6IDMgfSxcclxuICAgICAgICB7IG5hbWU6IFwiQmV2ZXJhZ2VzXCIsIHBhcmVudElkOiBudWxsLCBsZXZlbDogMCwgc29ydE9yZGVyOiA0IH0sXHJcbiAgICAgICAgeyBuYW1lOiBcIkhlYWx0aCAmIEhvdXNlaG9sZFwiLCBwYXJlbnRJZDogbnVsbCwgbGV2ZWw6IDAsIHNvcnRPcmRlcjogNSB9XHJcbiAgICAgIF07XHJcbiAgICAgIFxyXG4gICAgICBjb25zdCBpbnNlcnRSZXN1bHQgPSBhd2FpdCBjYXRlZ29yeUhpZXJhcmNoeUNvbGxlY3Rpb24uaW5zZXJ0TWFueShtYWluQ2F0ZWdvcmllcyk7XHJcbiAgICAgIGxvZyhjb2xvdXIuYmx1ZSwgYFx1MjcwNSBDcmVhdGVkICR7aW5zZXJ0UmVzdWx0Lmluc2VydGVkQ291bnR9IG1haW4gY2F0ZWdvcmllc2ApO1xyXG4gICAgICBcclxuICAgICAgLy8gSW5zZXJ0IHN1YmNhdGVnb3JpZXNcclxuICAgICAgY29uc3QgZnJlc2hGb29kc0lkID0gT2JqZWN0LnZhbHVlcyhpbnNlcnRSZXN1bHQuaW5zZXJ0ZWRJZHMpLmZpbmQoYXN5bmMgKGlkKSA9PiB7XHJcbiAgICAgICAgY29uc3QgY2F0ID0gYXdhaXQgY2F0ZWdvcnlIaWVyYXJjaHlDb2xsZWN0aW9uLmZpbmRPbmUoeyBfaWQ6IGlkIH0pO1xyXG4gICAgICAgIHJldHVybiBjYXQ/Lm5hbWUgPT09IFwiRnJlc2ggRm9vZHNcIjtcclxuICAgICAgfSk7XHJcbiAgICAgIFxyXG4gICAgICAvLyBGb3Igbm93LCBqdXN0IGNyZWF0ZSBiYXNpYyBzdHJ1Y3R1cmUgLSBjYW4gYmUgZXhwYW5kZWQgbGF0ZXJcclxuICAgICAgbG9nKGNvbG91ci5ibHVlLCBcIlx1MjcwNSBCYXNpYyBjYXRlZ29yeSBzdHJ1Y3R1cmUgY3JlYXRlZFwiKTtcclxuICAgIH1cclxuICB9IGNhdGNoIChlcnJvcjogYW55KSB7XHJcbiAgICBsb2dFcnJvcihgRmFpbGVkIHRvIGVuc3VyZSBiYXNpYyBjYXRlZ29yaWVzOiAke2Vycm9yLm1lc3NhZ2V9YCk7XHJcbiAgfVxyXG59XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gcHJvY2Vzc0NvbnNvbGlkYXRlZFByb2R1Y3RNb25nb0RCKHNjcmFwZWRQcm9kdWN0OiBQcm9kdWN0KTogUHJvbWlzZTxzdHJpbmcgfCBudWxsPiB7XHJcbiAgaWYgKCFjb25zb2xpZGF0ZWRQcm9kdWN0c0NvbGxlY3Rpb24pIHtcclxuICAgIGxvZ0Vycm9yKFwiQ29uc29saWRhdGVkIHByb2R1Y3RzIGNvbGxlY3Rpb24gbm90IGluaXRpYWxpemVkXCIpO1xyXG4gICAgcmV0dXJuIG51bGw7XHJcbiAgfVxyXG5cclxuICB0cnkge1xyXG4gICAgLy8gRm9yIG5vdywgY3JlYXRlIGEgc2ltcGxlIGNvbnNvbGlkYXRlZCBwcm9kdWN0IGZvciBlYWNoIHNjcmFwZWQgcHJvZHVjdFxyXG4gICAgLy8gVGhpcyBjYW4gYmUgZW5oYW5jZWQgbGF0ZXIgd2l0aCBwcm9wZXIgcHJvZHVjdCBtYXRjaGluZ1xyXG4gICAgY29uc3Qgbm9ybWFsaXplZE5hbWUgPSBub3JtYWxpemVQcm9kdWN0TmFtZShzY3JhcGVkUHJvZHVjdC5uYW1lLCBzY3JhcGVkUHJvZHVjdC5zaXplKTtcclxuICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7XHJcbiAgICBcclxuICAgIC8vIENoZWNrIGlmIGEgY29uc29saWRhdGVkIHByb2R1Y3QgYWxyZWFkeSBleGlzdHMgZm9yIHRoaXMgc3RvcmUgcHJvZHVjdFxyXG4gICAgY29uc3QgZXhpc3RpbmdQcm9kdWN0ID0gYXdhaXQgY29uc29saWRhdGVkUHJvZHVjdHNDb2xsZWN0aW9uLmZpbmRPbmUoe1xyXG4gICAgICBcInZhcmlhbnRzLnN0b3JlUHJvZHVjdElkXCI6IHNjcmFwZWRQcm9kdWN0LmlkXHJcbiAgICB9KTtcclxuICAgIFxyXG4gICAgaWYgKGV4aXN0aW5nUHJvZHVjdCkge1xyXG4gICAgICAvLyBVcGRhdGUgZXhpc3RpbmcgY29uc29saWRhdGVkIHByb2R1Y3RcclxuICAgICAgYXdhaXQgY29uc29saWRhdGVkUHJvZHVjdHNDb2xsZWN0aW9uLnVwZGF0ZU9uZShcclxuICAgICAgICB7IF9pZDogZXhpc3RpbmdQcm9kdWN0Ll9pZCB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgICRzZXQ6IHtcclxuICAgICAgICAgICAgXCJ2YXJpYW50cy4kW3ZhcmlhbnRdLmxhc3RTZWVuXCI6IG5vdyxcclxuICAgICAgICAgICAgXCJ2YXJpYW50cy4kW3ZhcmlhbnRdLnN0b3JlVW5pdFByaWNlXCI6IHNjcmFwZWRQcm9kdWN0LnVuaXRQcmljZSxcclxuICAgICAgICAgICAgXCJ2YXJpYW50cy4kW3ZhcmlhbnRdLnN0b3JlVW5pdE5hbWVcIjogc2NyYXBlZFByb2R1Y3QudW5pdE5hbWUsXHJcbiAgICAgICAgICAgIHVwZGF0ZWRBdDogbm93XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICBhcnJheUZpbHRlcnM6IFt7IFwidmFyaWFudC5zdG9yZVByb2R1Y3RJZFwiOiBzY3JhcGVkUHJvZHVjdC5pZCB9XVxyXG4gICAgICAgIH1cclxuICAgICAgKTtcclxuICAgICAgXHJcbiAgICAgIGxvZyhjb2xvdXIuY3lhbiwgYCAgVXBkYXRlZCBjb25zb2xpZGF0ZWQgcHJvZHVjdDogJHtzY3JhcGVkUHJvZHVjdC5uYW1lLnNsaWNlKDAsIDQwKX1gKTtcclxuICAgICAgcmV0dXJuIGV4aXN0aW5nUHJvZHVjdC5faWQudG9TdHJpbmcoKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIC8vIENyZWF0ZSBuZXcgY29uc29saWRhdGVkIHByb2R1Y3RcclxuICAgICAgY29uc3QgbmV3UHJvZHVjdCA9IHtcclxuICAgICAgICBub3JtYWxpemVkTmFtZSxcclxuICAgICAgICBkaXNwbGF5TmFtZTogc2NyYXBlZFByb2R1Y3QubmFtZSxcclxuICAgICAgICBwcmltYXJ5U2l6ZTogc2NyYXBlZFByb2R1Y3Quc2l6ZSxcclxuICAgICAgICBjYXRlZ29yeUlkOiBudWxsLCAvLyBUT0RPOiBJbXBsZW1lbnQgY2F0ZWdvcnkgbWFwcGluZ1xyXG4gICAgICAgIGJyYW5kSWQ6IG51bGwsIC8vIFRPRE86IEltcGxlbWVudCBicmFuZCBleHRyYWN0aW9uXHJcbiAgICAgICAgbWF0Y2hDb25maWRlbmNlOiAxMDAsXHJcbiAgICAgICAgbWFudWFsTWF0Y2g6IGZhbHNlLFxyXG4gICAgICAgIHZhcmlhbnRzOiBbe1xyXG4gICAgICAgICAgc3RvcmVQcm9kdWN0SWQ6IHNjcmFwZWRQcm9kdWN0LmlkLFxyXG4gICAgICAgICAgc3RvcmVJZDogXCJ3b29sd29ydGhzXCIsIC8vIFN0b3JlIGlkZW50aWZpZXJcclxuICAgICAgICAgIHN0b3JlTmFtZTogc2NyYXBlZFByb2R1Y3QubmFtZSxcclxuICAgICAgICAgIHN0b3JlU2l6ZTogc2NyYXBlZFByb2R1Y3Quc2l6ZSxcclxuICAgICAgICAgIHN0b3JlVW5pdFByaWNlOiBzY3JhcGVkUHJvZHVjdC51bml0UHJpY2UsXHJcbiAgICAgICAgICBzdG9yZVVuaXROYW1lOiBzY3JhcGVkUHJvZHVjdC51bml0TmFtZSxcclxuICAgICAgICAgIGxhc3RTZWVuOiBub3csXHJcbiAgICAgICAgICBpc0FjdGl2ZTogdHJ1ZSxcclxuICAgICAgICAgIGltYWdlVXJsOiBudWxsXHJcbiAgICAgICAgfV0sXHJcbiAgICAgICAgc2l6ZVZhcmlhbnRzOiBzY3JhcGVkUHJvZHVjdC5zaXplID8gW3tcclxuICAgICAgICAgIHNpemVOYW1lOiBzY3JhcGVkUHJvZHVjdC5zaXplLFxyXG4gICAgICAgICAgc2l6ZVdlaWdodEdyYW1zOiBudWxsLFxyXG4gICAgICAgICAgc2l6ZVZvbHVtZU1sOiBudWxsLFxyXG4gICAgICAgICAgaXNQcmltYXJ5U2l6ZTogdHJ1ZVxyXG4gICAgICAgIH1dIDogW10sXHJcbiAgICAgICAgY3VycmVudFByaWNlczogW3tcclxuICAgICAgICAgIHN0b3JlSWQ6IFwid29vbHdvcnRoc1wiLFxyXG4gICAgICAgICAgcHJpY2U6IHNjcmFwZWRQcm9kdWN0LmN1cnJlbnRQcmljZSxcclxuICAgICAgICAgIGlzU3BlY2lhbDogZmFsc2UsXHJcbiAgICAgICAgICB3YXNBdmFpbGFibGU6IHRydWUsXHJcbiAgICAgICAgICByZWNvcmRlZEF0OiBub3dcclxuICAgICAgICB9XSxcclxuICAgICAgICBjcmVhdGVkQXQ6IG5vdyxcclxuICAgICAgICB1cGRhdGVkQXQ6IG5vd1xyXG4gICAgICB9O1xyXG4gICAgICBcclxuICAgICAgY29uc3QgaW5zZXJ0UmVzdWx0ID0gYXdhaXQgY29uc29saWRhdGVkUHJvZHVjdHNDb2xsZWN0aW9uLmluc2VydE9uZShuZXdQcm9kdWN0KTtcclxuICAgICAgbG9nKGNvbG91ci5jeWFuLCBgICBDcmVhdGVkIGNvbnNvbGlkYXRlZCBwcm9kdWN0OiAke3NjcmFwZWRQcm9kdWN0Lm5hbWUuc2xpY2UoMCwgNDApfWApO1xyXG4gICAgICByZXR1cm4gaW5zZXJ0UmVzdWx0Lmluc2VydGVkSWQudG9TdHJpbmcoKTtcclxuICAgIH1cclxuICAgIFxyXG4gIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcclxuICAgIGxvZ0Vycm9yKGBGYWlsZWQgdG8gcHJvY2VzcyBjb25zb2xpZGF0ZWQgcHJvZHVjdDogJHtlcnJvci5tZXNzYWdlfWApO1xyXG4gICAgcmV0dXJuIG51bGw7XHJcbiAgfVxyXG59XHJcblxyXG4vLyBIZWxwZXIgZnVuY3Rpb24gdG8gbm9ybWFsaXplIHByb2R1Y3QgbmFtZXNcclxuZnVuY3Rpb24gbm9ybWFsaXplUHJvZHVjdE5hbWUobmFtZTogc3RyaW5nLCBzaXplPzogc3RyaW5nKTogc3RyaW5nIHtcclxuICBsZXQgbm9ybWFsaXplZCA9IG5hbWUudG9Mb3dlckNhc2UoKVxyXG4gICAgLnJlcGxhY2UoL1teYS16MC05XFxzXS9nLCAnICcpXHJcbiAgICAucmVwbGFjZSgvXFxzKy9nLCAnXycpXHJcbiAgICAudHJpbSgpO1xyXG4gIFxyXG4gIGlmIChzaXplKSB7XHJcbiAgICBjb25zdCBub3JtYWxpemVkU2l6ZSA9IHNpemUudG9Mb3dlckNhc2UoKVxyXG4gICAgICAucmVwbGFjZSgvW15hLXowLTldL2csICcnKVxyXG4gICAgICAudHJpbSgpO1xyXG4gICAgbm9ybWFsaXplZCArPSAnXycgKyBub3JtYWxpemVkU2l6ZTtcclxuICB9XHJcbiAgXHJcbiAgcmV0dXJuIG5vcm1hbGl6ZWQ7XHJcbn1cclxuXHJcbi8vIENsZWFuIHVwIGZ1bmN0aW9uIC0gY2xvc2UgY29ubmVjdGlvbnNcclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNsb3NlQ29uc29saWRhdGVkUHJvZHVjdHNNb25nb0RCKCkge1xyXG4gIHRyeSB7XHJcbiAgICBpZiAoY2xpZW50KSB7XHJcbiAgICAgIGF3YWl0IGNsaWVudC5jbG9zZSgpO1xyXG4gICAgfVxyXG4gICAgbG9nKGNvbG91ci5ibHVlLCBcIlx1MjcwNSBDb25zb2xpZGF0ZWQgcHJvZHVjdHMgTW9uZ29EQiBjb25uZWN0aW9ucyBjbG9zZWRcIik7XHJcbiAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xyXG4gICAgbG9nRXJyb3IoYEZhaWxlZCB0byBjbG9zZSBNb25nb0RCIGNvbm5lY3Rpb25zOiAke2Vycm9yLm1lc3NhZ2V9YCk7XHJcbiAgfVxyXG59XHJcblxyXG4vLyBTZWFyY2ggY29uc29saWRhdGVkIHByb2R1Y3RzXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBzZWFyY2hDb25zb2xpZGF0ZWRQcm9kdWN0cyhxdWVyeTogc3RyaW5nLCBsaW1pdDogbnVtYmVyID0gMjApOiBQcm9taXNlPGFueVtdPiB7XHJcbiAgaWYgKCFjb25zb2xpZGF0ZWRQcm9kdWN0c0NvbGxlY3Rpb24pIHJldHVybiBbXTtcclxuICBcclxuICB0cnkge1xyXG4gICAgY29uc3QgcmVzdWx0cyA9IGF3YWl0IGNvbnNvbGlkYXRlZFByb2R1Y3RzQ29sbGVjdGlvbi5maW5kKHtcclxuICAgICAgJHRleHQ6IHsgJHNlYXJjaDogcXVlcnkgfVxyXG4gICAgfSlcclxuICAgIC5zb3J0KHsgc2NvcmU6IHsgJG1ldGE6IFwidGV4dFNjb3JlXCIgfSB9KVxyXG4gICAgLmxpbWl0KGxpbWl0KVxyXG4gICAgLnRvQXJyYXkoKTtcclxuICAgIFxyXG4gICAgcmV0dXJuIHJlc3VsdHM7XHJcbiAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xyXG4gICAgbG9nRXJyb3IoYFNlYXJjaCBmYWlsZWQ6ICR7ZXJyb3IubWVzc2FnZX1gKTtcclxuICAgIHJldHVybiBbXTtcclxuICB9XHJcbn1cclxuXHJcbi8vIEdldCBjb25zb2xpZGF0ZWQgcHJvZHVjdCB3aXRoIGFsbCBzdG9yZSBwcmljZXNcclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldENvbnNvbGlkYXRlZFByb2R1Y3RXaXRoUHJpY2VzKHByb2R1Y3RJZDogc3RyaW5nKTogUHJvbWlzZTxhbnkgfCBudWxsPiB7XHJcbiAgaWYgKCFjb25zb2xpZGF0ZWRQcm9kdWN0c0NvbGxlY3Rpb24pIHJldHVybiBudWxsO1xyXG4gIFxyXG4gIHRyeSB7XHJcbiAgICBjb25zdCBvYmplY3RJZCA9IG5ldyBPYmplY3RJZChwcm9kdWN0SWQpO1xyXG4gICAgY29uc3QgcHJvZHVjdCA9IGF3YWl0IGNvbnNvbGlkYXRlZFByb2R1Y3RzQ29sbGVjdGlvbi5maW5kT25lKHsgX2lkOiBvYmplY3RJZCB9KTtcclxuICAgIFxyXG4gICAgaWYgKCFwcm9kdWN0KSByZXR1cm4gbnVsbDtcclxuICAgIFxyXG4gICAgLy8gVE9ETzogQWRkIHByaWNlIGhpc3RvcnkgYWdncmVnYXRpb24gaWYgbmVlZGVkXHJcbiAgICBcclxuICAgIHJldHVybiBwcm9kdWN0O1xyXG4gIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcclxuICAgIGxvZ0Vycm9yKGBGYWlsZWQgdG8gZ2V0IHByb2R1Y3Qgd2l0aCBwcmljZXM6ICR7ZXJyb3IubWVzc2FnZX1gKTtcclxuICAgIHJldHVybiBudWxsO1xyXG4gIH1cclxufSJdLAogICJtYXBwaW5ncyI6ICI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQUE7QUFBQTtBQUNBLFdBQU8sZUFBZSxTQUFTLGNBQWMsRUFBRSxPQUFPLEtBQUssQ0FBQztBQUFBO0FBQUE7OztBQ0Q1RDtBQUFBO0FBQUE7QUFDQSxXQUFPLGVBQWUsU0FBUyxjQUFjLEVBQUUsT0FBTyxLQUFLLENBQUM7QUFDNUQsWUFBUSxrQkFBa0IsUUFBUSxTQUFTO0FBQzNDLFlBQVEsTUFBTUE7QUFDZCxZQUFRLFdBQVdDO0FBQ25CLFlBQVEsZ0JBQWdCQztBQUN4QixZQUFRLGlCQUFpQkM7QUFDekIsWUFBUSx3QkFBd0JDO0FBQ2hDLFlBQVEsc0JBQXNCQztBQUM5QixZQUFRLGNBQWNDO0FBQ3RCLFFBQU0sT0FBTyxVQUFRLElBQUk7QUFFekIsUUFBTSxlQUFlO0FBQ3JCLFFBQU0saUJBQWlCO0FBQ3ZCLFFBQU0saUJBQWlCO0FBQ3ZCLFlBQVEsU0FBUztBQUFBLE1BQ2IsS0FBSztBQUFBLE1BQ0wsT0FBTztBQUFBLE1BQ1AsUUFBUTtBQUFBLE1BQ1IsTUFBTTtBQUFBLE1BQ04sU0FBUztBQUFBLE1BQ1QsTUFBTTtBQUFBLE1BQ04sT0FBTztBQUFBLE1BQ1AsU0FBUztBQUFBLE1BQ1QsTUFBTTtBQUFBLE1BQ04sUUFBUTtBQUFBLE1BQ1IsS0FBSztBQUFBLElBQ1Q7QUFJQSxhQUFTTixLQUFJTyxTQUFRLE1BQU07QUFDdkIsWUFBTSxRQUFRO0FBQ2QsY0FBUSxJQUFJLEdBQUdBLE9BQU0sS0FBSyxLQUFLLElBQUksSUFBSTtBQUFBLElBQzNDO0FBSUEsYUFBU04sVUFBUyxNQUFNO0FBQ3BCLE1BQUFELEtBQUksUUFBUSxPQUFPLEtBQUssSUFBSTtBQUFBLElBQ2hDO0FBSUEsYUFBU0UsZUFBYyxTQUFTO0FBQzVCLFVBQUk7QUFDSixZQUFNLGtCQUFrQixRQUFRLFlBQVksSUFBSSxRQUFRLFVBQVUsUUFBUSxDQUFDLENBQUMsS0FBSyxRQUFRLFFBQVEsS0FBSztBQUN0RyxNQUFBRixLQUFJLHdCQUF3QixRQUFRLE9BQU8sS0FBSyxRQUFRLE9BQU8sS0FBSyxHQUFHLEdBQUcsUUFBUSxHQUFHLFNBQVMsWUFBWSxDQUFDLE1BQ3BHLFFBQVEsS0FBSyxNQUFNLEdBQUcsY0FBYyxFQUFFLE9BQU8sY0FBYyxDQUFDLE9BQzNELEtBQUssUUFBUSxVQUFVLFFBQVEsT0FBTyxTQUFTLFNBQVMsR0FBRyxNQUFNLEdBQUcsY0FBYyxFQUFFLE9BQU8sY0FBYyxDQUFDLFFBQ3pHLFFBQVEsYUFBYSxRQUFRLENBQUMsRUFBRSxTQUFTLENBQUMsRUFBRSxPQUFPLENBQUMsQ0FBQyxRQUMxRCxlQUFlO0FBQUEsSUFDdkI7QUFHQSxhQUFTRyxrQkFBaUI7QUFDdEIsTUFBQUgsS0FBSSxRQUFRLE9BQU8sUUFBUSxHQUFHLEtBQUssU0FBUyxZQUFZLENBQUMsTUFBTSxPQUFPLE9BQU8sY0FBYyxDQUFDLE1BQ3JGLE9BQU8sT0FBTyxjQUFjLENBQUMsTUFDN0IsUUFBUSxPQUFPLENBQUMsQ0FBQyxlQUFlO0FBQ3ZDLFVBQUksYUFBYTtBQUNqQixlQUFTLElBQUksR0FBRyxJQUFJLEtBQUssS0FBSztBQUMxQixzQkFBYztBQUFBLE1BQ2xCO0FBQ0EsTUFBQUEsS0FBSSxRQUFRLE9BQU8sUUFBUSxVQUFVO0FBQUEsSUFDekM7QUFLQSxRQUFJLHVCQUF1QjtBQUMzQixhQUFTLHdCQUF3QixTQUFTLFNBQVM7QUFDL0MsNkJBQXVCLHVCQUF1QixRQUFRO0FBQ3RELGFBQU8sdUJBQXVCLFVBQVU7QUFBQSxJQUM1QztBQUlBLGFBQVNJLHVCQUFzQixVQUFVO0FBQ3JDLFVBQUk7QUFDQSxjQUFNLFFBQVEsR0FBRyxLQUFLLGNBQWMsVUFBVSxPQUFPO0FBQ3JELGNBQU0sU0FBUyxLQUFLLE1BQU0sT0FBTyxFQUFFLE9BQU8sQ0FBQyxTQUFTO0FBQ2hELGNBQUksS0FBSyxLQUFLLEVBQUUsU0FBUztBQUNyQixtQkFBTztBQUFBO0FBRVAsbUJBQU87QUFBQSxRQUNmLENBQUM7QUFDRCxlQUFPO0FBQUEsTUFDWCxTQUNPLE9BQU87QUFDVixjQUFNLG1CQUFtQjtBQUFBLE1BQzdCO0FBQUEsSUFDSjtBQUlBLGFBQVNDLHFCQUFvQkcsWUFBVztBQUNwQyxVQUFJLHNCQUFzQixLQUFLLElBQUksSUFBSUEsY0FBYTtBQUNwRCxVQUFJLG9CQUFvQixLQUFLLE1BQU0sa0JBQWtCLEVBQUUsU0FBUztBQUVoRSxVQUFJLHNCQUFzQixJQUFJO0FBQzFCLGVBQVEsS0FBSyxNQUFNLHFCQUFxQixFQUFFLElBQ3RDLE1BQ0EsS0FBSyxNQUFNLHFCQUFxQixFQUFFLEVBQzdCLFNBQVMsRUFDVCxTQUFTLEdBQUcsR0FBRztBQUFBLE1BRTVCO0FBRUksZUFBTyxvQkFBb0I7QUFBQSxJQUNuQztBQUVBLFlBQVEsa0JBQWtCO0FBQUE7QUFBQSxNQUV0QjtBQUFBLE1BQ0E7QUFBQSxNQUNBO0FBQUEsTUFDQTtBQUFBLE1BQ0E7QUFBQSxNQUNBO0FBQUEsTUFDQTtBQUFBLE1BQ0E7QUFBQSxNQUNBO0FBQUE7QUFBQSxNQUVBO0FBQUEsTUFDQTtBQUFBLE1BQ0E7QUFBQSxNQUNBO0FBQUEsTUFDQTtBQUFBLE1BQ0E7QUFBQSxNQUNBO0FBQUEsTUFDQTtBQUFBLE1BQ0E7QUFBQSxNQUNBO0FBQUE7QUFBQSxNQUVBO0FBQUEsTUFDQTtBQUFBLE1BQ0E7QUFBQSxNQUNBO0FBQUEsTUFDQTtBQUFBLE1BQ0E7QUFBQSxNQUNBO0FBQUEsTUFDQTtBQUFBLE1BQ0E7QUFBQSxNQUNBO0FBQUEsTUFDQTtBQUFBO0FBQUEsTUFFQTtBQUFBLE1BQ0E7QUFBQSxNQUNBO0FBQUEsTUFDQTtBQUFBLE1BQ0E7QUFBQSxNQUNBO0FBQUEsTUFDQTtBQUFBLE1BQ0E7QUFBQSxNQUNBO0FBQUEsTUFDQTtBQUFBO0FBQUEsTUFFQTtBQUFBLE1BQ0E7QUFBQSxNQUNBO0FBQUEsTUFDQTtBQUFBLE1BQ0E7QUFBQSxNQUNBO0FBQUEsTUFDQTtBQUFBLE1BQ0E7QUFBQSxNQUNBO0FBQUEsTUFDQTtBQUFBLE1BQ0E7QUFBQSxNQUNBO0FBQUEsTUFDQTtBQUFBO0FBQUEsTUFFQTtBQUFBLE1BQ0E7QUFBQSxNQUNBO0FBQUEsTUFDQTtBQUFBLE1BQ0E7QUFBQSxNQUNBO0FBQUEsTUFDQTtBQUFBLE1BQ0E7QUFBQSxNQUNBO0FBQUE7QUFBQSxNQUVBO0FBQUEsTUFDQTtBQUFBLE1BQ0E7QUFBQSxNQUNBO0FBQUEsTUFDQTtBQUFBLE1BQ0E7QUFBQSxNQUNBO0FBQUEsTUFDQTtBQUFBO0FBQUEsTUFFQTtBQUFBLE1BQ0E7QUFBQSxNQUNBO0FBQUEsTUFDQTtBQUFBLElBQ0o7QUFJQSxhQUFTRixhQUFZLEtBQUs7QUFDdEIsYUFBTyxJQUFJLFFBQVEsVUFBVSxTQUFVLEtBQUs7QUFDeEMsZUFBTyxJQUFJLE9BQU8sQ0FBQyxFQUFFLFlBQVksSUFBSSxJQUFJLFVBQVUsQ0FBQyxFQUFFLFlBQVk7QUFBQSxNQUN0RSxDQUFDO0FBQUEsSUFDTDtBQUFBO0FBQUE7OztBQzFNQTtBQUFBO0FBQUE7QUFHQSxXQUFPLGVBQWUsU0FBUyxjQUFjLEVBQUUsT0FBTyxLQUFLLENBQUM7QUFDNUQsWUFBUSxtQkFBbUI7QUFDM0IsWUFBUSxtQkFBbUI7QUFBQSxNQUN2QixFQUFFLElBQUksVUFBVSxNQUFNLE9BQU87QUFBQSxNQUM3QixFQUFFLElBQUksVUFBVSxNQUFNLE9BQU87QUFBQSxNQUM3QixFQUFFLElBQUksVUFBVSxNQUFNLE9BQU87QUFBQSxNQUM3QixFQUFFLElBQUksVUFBVSxNQUFNLE9BQU87QUFBQSxNQUM3QixFQUFFLElBQUksVUFBVSxNQUFNLFNBQVM7QUFBQSxNQUMvQixFQUFFLElBQUksVUFBVSxNQUFNLFFBQVE7QUFBQSxNQUM5QixFQUFFLElBQUksVUFBVSxNQUFNLFFBQVE7QUFBQSxNQUM5QixFQUFFLElBQUksVUFBVSxNQUFNLFFBQVE7QUFBQSxNQUM5QixFQUFFLElBQUksVUFBVSxNQUFNLFFBQVE7QUFBQSxNQUM5QixFQUFFLElBQUksVUFBVSxNQUFNLE9BQU87QUFBQSxNQUM3QixFQUFFLElBQUksVUFBVSxNQUFNLFFBQVE7QUFBQSxNQUM5QixFQUFFLElBQUksVUFBVSxNQUFNLFFBQVE7QUFBQSxNQUM5QixFQUFFLElBQUksVUFBVSxNQUFNLFFBQVE7QUFBQSxNQUM5QixFQUFFLElBQUksVUFBVSxNQUFNLFFBQVE7QUFBQSxNQUM5QixFQUFFLElBQUksVUFBVSxNQUFNLFFBQVE7QUFBQSxNQUM5QixFQUFFLElBQUksVUFBVSxNQUFNLFFBQVE7QUFBQSxNQUM5QixFQUFFLElBQUksVUFBVSxNQUFNLFFBQVE7QUFBQSxNQUM5QixFQUFFLElBQUksVUFBVSxNQUFNLFFBQVE7QUFBQSxNQUM5QixFQUFFLElBQUksVUFBVSxNQUFNLFFBQVE7QUFBQSxNQUM5QixFQUFFLElBQUksVUFBVSxNQUFNLFNBQVM7QUFBQSxNQUMvQixFQUFFLElBQUksVUFBVSxNQUFNLE9BQU87QUFBQSxNQUM3QixFQUFFLElBQUksVUFBVSxNQUFNLE9BQU87QUFBQSxNQUM3QixFQUFFLElBQUksVUFBVSxNQUFNLE9BQU87QUFBQSxNQUM3QixFQUFFLElBQUksVUFBVSxNQUFNLE9BQU87QUFBQSxNQUM3QixFQUFFLElBQUksVUFBVSxNQUFNLE9BQU87QUFBQSxNQUM3QixFQUFFLElBQUksVUFBVSxNQUFNLE9BQU87QUFBQSxNQUM3QixFQUFFLElBQUksUUFBUSxNQUFNLE9BQU87QUFBQSxNQUMzQixFQUFFLElBQUksVUFBVSxVQUFVLGlCQUFpQjtBQUFBLE1BQzNDLEVBQUUsSUFBSSxVQUFVLE1BQU0sU0FBUyxVQUFVLFNBQVM7QUFBQSxNQUNsRCxFQUFFLElBQUksVUFBVSxVQUFVLGFBQWE7QUFBQSxNQUN2QyxFQUFFLElBQUksU0FBUyxVQUFVLFdBQVc7QUFBQSxNQUNwQyxFQUFFLElBQUksU0FBUyxVQUFVLFdBQVc7QUFBQSxNQUNwQyxFQUFFLElBQUksU0FBUyxVQUFVLFdBQVc7QUFBQSxNQUNwQyxFQUFFLElBQUksU0FBUyxVQUFVLFdBQVc7QUFBQSxNQUNwQyxFQUFFLElBQUksVUFBVSxVQUFVLGtCQUFrQjtBQUFBLE1BQzVDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLE1BQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLE1BQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLE1BQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLE1BQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLE1BQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLE1BQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLE1BQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLE1BQ3ZDLEVBQUUsSUFBSSxTQUFTLFVBQVUsYUFBYTtBQUFBLE1BQ3RDLEVBQUUsSUFBSSxVQUFVLFVBQVUsWUFBWTtBQUFBLE1BQ3RDLEVBQUUsSUFBSSxVQUFVLFVBQVUsWUFBWTtBQUFBLE1BQ3RDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLE1BQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLE1BQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLE1BQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLE1BQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLE1BQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLE1BQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLE1BQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLE1BQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLE1BQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLE1BQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsWUFBWTtBQUFBLE1BQ3RDLEVBQUUsSUFBSSxVQUFVLFVBQVUsWUFBWTtBQUFBLE1BQ3RDLEVBQUUsSUFBSSxVQUFVLFVBQVUsWUFBWTtBQUFBLE1BQ3RDLEVBQUUsSUFBSSxVQUFVLFVBQVUsU0FBUztBQUFBLE1BQ25DLEVBQUUsSUFBSSxVQUFVLFVBQVUsU0FBUztBQUFBLE1BQ25DLEVBQUUsSUFBSSxTQUFTLFVBQVUsWUFBWTtBQUFBLE1BQ3JDLEVBQUUsSUFBSSxRQUFRLFVBQVUsWUFBWTtBQUFBLE1BQ3BDLEVBQUUsSUFBSSxTQUFTLFVBQVUsWUFBWTtBQUFBLE1BQ3JDLEVBQUUsSUFBSSxVQUFVLFVBQVUsWUFBWTtBQUFBLE1BQ3RDLEVBQUUsSUFBSSxRQUFRLFVBQVUsWUFBWTtBQUFBLE1BQ3BDLEVBQUUsSUFBSSxVQUFVLFVBQVUsWUFBWTtBQUFBLElBQzFDO0FBQUE7QUFBQTs7O0FDekVBLFlBQVlHLGFBQVk7QUFJeEIsT0FBTyxnQkFBZ0I7QUFDdkIsWUFBWSxhQUFhO0FBQ3pCLE9BQU8sT0FBTztBQUNkLFNBQVMsa0JBQWtCOzs7QUNGM0IscUJBQXdDO0FBQ3hDLHVCQUFzQztBQU50QyxTQUFTLGFBQTZCLGNBQWMsZ0JBQWdCO0FBQ3BFLFlBQVksWUFBWTtBQUNqQixjQUFPO0FBQ1AsY0FBTyxFQUFFLE1BQU0sY0FBYyxVQUFVLEtBQUssQ0FBQztBQUtwRCxJQUFJO0FBQ0osSUFBSTtBQUNKLElBQUk7QUFDSixJQUFJO0FBR0osSUFBSTtBQUNKLElBQUk7QUFDSixJQUFJO0FBQ0osSUFBSTtBQUNKLElBQUk7QUFFSixlQUFzQixtQkFBbUI7QUFDdkMsUUFBTSxtQkFBbUIsUUFBUSxJQUFJO0FBQ3JDLFFBQU0sZUFBZSxRQUFRLElBQUkseUJBQXlCO0FBRTFELE1BQUksQ0FBQyxrQkFBa0I7QUFDckIsVUFBTSxNQUFNLDBDQUEwQztBQUFBLEVBQ3hEO0FBRUEsTUFBSTtBQUNGLGFBQVMsSUFBSSxZQUFZLGdCQUFnQjtBQUN6QyxVQUFNLE9BQU8sUUFBUTtBQUNyQixTQUFLLE9BQU8sR0FBRyxZQUFZO0FBRzNCLHVCQUFtQixHQUFHLFdBQVcsUUFBUTtBQUN6Qyx1QkFBbUIsR0FBRyxXQUFXLFFBQVE7QUFDekMscUNBQWlDLEdBQUcsV0FBVyxzQkFBc0I7QUFDckUsNkJBQXlCLEdBQUcsV0FBVyxjQUFjO0FBQ3JELGtDQUE4QixHQUFHLFdBQVcsbUJBQW1CO0FBRy9ELGFBQVMsSUFBSSxhQUFhLElBQUksRUFBRSxZQUFZLGdCQUFnQixDQUFDO0FBRTdELDhCQUFJLHdCQUFPLE9BQU8sdUNBQWtDO0FBR3BELFVBQU0sY0FBYztBQUFBLEVBRXRCLFNBQVMsT0FBWTtBQUNuQixtQ0FBUyxpQ0FBaUMsTUFBTSxPQUFPLEVBQUU7QUFDekQsVUFBTTtBQUFBLEVBQ1I7QUFDRjtBQUVBLGVBQWUsZ0JBQWdCO0FBQzdCLE1BQUk7QUFFRixVQUFNLCtCQUErQixZQUFZO0FBQUEsTUFDL0MsZUFBZTtBQUFBLE1BQ2Ysa0JBQWtCO0FBQUEsTUFDbEIsc0JBQXNCO0FBQUEsSUFDeEIsQ0FBQztBQUNELFVBQU0sK0JBQStCLFlBQVksRUFBRSxjQUFjLEVBQUUsQ0FBQztBQUNwRSxVQUFNLCtCQUErQixZQUFZLEVBQUUsV0FBVyxFQUFFLENBQUM7QUFDakUsVUFBTSwrQkFBK0IsWUFBWTtBQUFBLE1BQy9DLDJCQUEyQjtBQUFBLE1BQzNCLG9CQUFvQjtBQUFBLElBQ3RCLENBQUM7QUFHRCxVQUFNLHVCQUF1QixZQUFZO0FBQUEsTUFDdkMseUJBQXlCO0FBQUEsTUFDekIsY0FBYztBQUFBLElBQ2hCLENBQUM7QUFDRCxVQUFNLHVCQUF1QixZQUFZLEVBQUUsUUFBUSxHQUFHLFNBQVMsRUFBRSxDQUFDO0FBR2xFLFVBQU0sNEJBQTRCLFlBQVksRUFBRSxZQUFZLEdBQUcsYUFBYSxFQUFFLENBQUM7QUFDL0UsVUFBTSw0QkFBNEIsWUFBWSxFQUFFLFNBQVMsR0FBRyxhQUFhLEVBQUUsQ0FBQztBQUU1RSw4QkFBSSx3QkFBTyxNQUFNLGdDQUEyQjtBQUFBLEVBQzlDLFNBQVMsT0FBWTtBQUNuQixtQ0FBUyw2QkFBNkIsTUFBTSxPQUFPLEVBQUU7QUFBQSxFQUN2RDtBQUNGO0FBRUEsZUFBZSxpQkFBb0M7QUFDakQsTUFBSSxZQUFZLE9BQVcsUUFBTztBQUVsQyxNQUFJO0FBQ0YsVUFBTSxnQkFBZ0IsTUFBTSxpQkFBaUIsUUFBUSxFQUFFLFNBQVMsYUFBYSxDQUFDO0FBRTlFLFFBQUksZUFBZTtBQUNqQixnQkFBVSxjQUFjO0FBQ3hCLGFBQU87QUFBQSxJQUNUO0FBR0EsVUFBTSxlQUFlLE1BQU0saUJBQWlCLFVBQVU7QUFBQSxNQUNwRCxTQUFTO0FBQUEsTUFDVCxNQUFNO0FBQUEsTUFDTixTQUFTO0FBQUEsTUFDVCxXQUFXLG9CQUFJLEtBQUs7QUFBQSxNQUNwQixXQUFXLG9CQUFJLEtBQUs7QUFBQSxJQUN0QixDQUFDO0FBRUQsY0FBVSxhQUFhO0FBQ3ZCLFdBQU87QUFBQSxFQUVULFNBQVMsT0FBWTtBQUNuQixtQ0FBUywrQkFBK0IsTUFBTSxPQUFPLEVBQUU7QUFDdkQsVUFBTTtBQUFBLEVBQ1I7QUFDRjtBQUVBLGVBQXNCLHVCQUF1QixTQUEyQztBQUN0RixNQUFJLENBQUMsR0FBSSxPQUFNLE1BQU0sZ0NBQWdDO0FBRXJELE1BQUk7QUFDRixVQUFNLE1BQU0sTUFBTSxlQUFlO0FBQ2pDLFVBQU0sTUFBTSxvQkFBSSxLQUFLO0FBR3JCLFFBQUksc0JBQXNCLE1BQU0sK0JBQStCLFFBQVE7QUFBQSxNQUNyRSwyQkFBMkIsUUFBUTtBQUFBLE1BQ25DLG9CQUFvQjtBQUFBLElBQ3RCLENBQUM7QUFFRCxRQUFJLENBQUMscUJBQXFCO0FBRXhCLFlBQU0saUJBQWlCLHFCQUFxQixRQUFRLE1BQU0sUUFBUSxJQUFJO0FBQ3RFLGdDQUFJLHdCQUFPLE1BQU0seUJBQWtCLFFBQVEsSUFBSSxtQkFBbUIsY0FBYyxFQUFFO0FBRWxGLFlBQU0sa0JBQWtCLE1BQU0sY0FBYyxnQkFBZ0IsUUFBUSxNQUFNLFFBQVEsSUFBSTtBQUV0RixVQUFJLGlCQUFpQjtBQUVuQiw4QkFBc0I7QUFHdEIsY0FBTSxnQkFBZ0Isb0JBQW9CLEtBQUssUUFBUSxJQUFJO0FBRzNELGNBQU0sYUFBYTtBQUFBLFVBQ2pCLGdCQUFnQixRQUFRO0FBQUEsVUFDeEIsU0FBUztBQUFBLFVBQ1QsV0FBVyxRQUFRO0FBQUEsVUFDbkIsV0FBVyxRQUFRO0FBQUEsVUFDbkIsZ0JBQWdCLFFBQVE7QUFBQSxVQUN4QixlQUFlLFFBQVE7QUFBQSxVQUN2QixVQUFVO0FBQUEsVUFDVixVQUFVO0FBQUEsVUFDVixVQUFVO0FBQUEsUUFDWjtBQUVBLGNBQU0sK0JBQStCO0FBQUEsVUFDbkMsRUFBRSxLQUFLLG9CQUFvQixJQUFJO0FBQUEsVUFDL0I7QUFBQSxZQUNFLE9BQU8sRUFBRSxVQUFVLFdBQVc7QUFBQSxZQUM5QixNQUFNLEVBQUUsV0FBVyxJQUFJO0FBQUEsVUFDekI7QUFBQSxRQUNGO0FBRUEsa0NBQUksd0JBQU8sT0FBTyw2Q0FBd0MsZ0JBQWdCLFdBQVcsRUFBRTtBQUFBLE1BQ3pGLE9BQU87QUFFTCxjQUFNLGFBQWE7QUFBQSxVQUNqQjtBQUFBLFVBQ0EsYUFBYSxRQUFRO0FBQUEsVUFDckIsYUFBYSxRQUFRO0FBQUEsVUFDckIsWUFBWTtBQUFBO0FBQUEsVUFDWixTQUFTO0FBQUE7QUFBQSxVQUNULGlCQUFpQjtBQUFBLFVBQ2pCLGFBQWE7QUFBQSxVQUNiLFNBQVMsQ0FBQztBQUFBO0FBQUEsVUFDVixVQUFVLENBQUM7QUFBQSxZQUNULGdCQUFnQixRQUFRO0FBQUEsWUFDeEIsU0FBUztBQUFBLFlBQ1QsV0FBVyxRQUFRO0FBQUEsWUFDbkIsV0FBVyxRQUFRO0FBQUEsWUFDbkIsZ0JBQWdCLFFBQVE7QUFBQSxZQUN4QixlQUFlLFFBQVE7QUFBQSxZQUN2QixVQUFVO0FBQUEsWUFDVixVQUFVO0FBQUEsWUFDVixVQUFVO0FBQUEsVUFDWixDQUFDO0FBQUEsVUFDRCxjQUFjLFFBQVEsT0FBTyxDQUFDO0FBQUEsWUFDNUIsVUFBVSxRQUFRO0FBQUEsWUFDbEIsaUJBQWlCO0FBQUEsWUFDakIsY0FBYztBQUFBLFlBQ2QsZUFBZTtBQUFBLFVBQ2pCLENBQUMsSUFBSSxDQUFDO0FBQUEsVUFDTixlQUFlLENBQUM7QUFBQSxZQUNkLFNBQVM7QUFBQSxZQUNULE9BQU8sUUFBUTtBQUFBLFlBQ2YsV0FBVztBQUFBLFlBQ1gsY0FBYztBQUFBLFlBQ2QsWUFBWTtBQUFBLFVBQ2QsQ0FBQztBQUFBLFVBQ0QsV0FBVztBQUFBLFVBQ1gsV0FBVztBQUFBLFFBQ2I7QUFFQSxjQUFNLGVBQWUsTUFBTSwrQkFBK0IsVUFBVSxVQUFVO0FBQzlFLDhCQUFzQixFQUFFLEtBQUssYUFBYSxZQUFZLEdBQUcsV0FBVztBQUVwRSxrQ0FBSSx3QkFBTyxNQUFNLCtDQUF3QyxRQUFRLElBQUksRUFBRTtBQUFBLE1BQ3pFO0FBQUEsSUFDRixPQUFPO0FBRUwsWUFBTSwrQkFBK0I7QUFBQSxRQUNuQyxFQUFFLEtBQUssb0JBQW9CLElBQUk7QUFBQSxRQUMvQjtBQUFBLFVBQ0UsTUFBTTtBQUFBLFlBQ0osZ0NBQWdDO0FBQUEsWUFDaEMsc0NBQXNDLFFBQVE7QUFBQSxZQUM5QyxxQ0FBcUMsUUFBUTtBQUFBLFlBQzdDLGdDQUFnQyxRQUFRO0FBQUEsWUFDeEMscUNBQXFDO0FBQUEsWUFDckMsV0FBVztBQUFBLFVBQ2I7QUFBQSxRQUNGO0FBQUEsUUFDQTtBQUFBLFVBQ0UsY0FBYztBQUFBLFlBQ1osRUFBRSwwQkFBMEIsUUFBUSxHQUFHO0FBQUEsWUFDdkMsRUFBRSxpQkFBaUIsSUFBSTtBQUFBLFVBQ3pCO0FBQUEsUUFDRjtBQUFBLE1BQ0Y7QUFBQSxJQUNGO0FBR0EsVUFBTSx1QkFBdUIsVUFBVTtBQUFBLE1BQ3JDLHVCQUF1QixvQkFBb0I7QUFBQSxNQUMzQyxTQUFTO0FBQUEsTUFDVCxPQUFPLFFBQVE7QUFBQSxNQUNmLFdBQVc7QUFBQSxNQUNYLGNBQWM7QUFBQSxNQUNkLFlBQVk7QUFBQSxNQUNaLE1BQU0sSUFBSSxZQUFZO0FBQUEsTUFDdEIsT0FBTyxJQUFJLFNBQVMsSUFBSTtBQUFBLElBQzFCLENBQUM7QUFFRCw4QkFBSSx3QkFBTyxPQUFPLGVBQWUsUUFBUSxLQUFLLE1BQU0sR0FBRyxFQUFFLEVBQUUsT0FBTyxFQUFFLENBQUMsT0FBTyxRQUFRLFlBQVksRUFBRTtBQUNsRyxXQUFPLDhCQUFlO0FBQUEsRUFFeEIsU0FBUyxPQUFZO0FBQ25CLG1DQUFTLDBCQUEwQixNQUFNLE9BQU8sRUFBRTtBQUNsRCxXQUFPLDhCQUFlO0FBQUEsRUFDeEI7QUFDRjtBQUdBLGVBQXNCLHFCQUFxQixVQUFrQixTQUFvQztBQUMvRixNQUFJLENBQUMsT0FBUSxPQUFNLE1BQU0sZ0NBQWdDO0FBRXpELE1BQUk7QUFDRixVQUFNLE1BQU0sTUFBTSxlQUFlO0FBR2pDLFVBQU0sZ0JBQWdCLE1BQU0sTUFBTSxRQUFRO0FBQzFDLFFBQUksQ0FBQyxjQUFjLElBQUk7QUFDckIscUNBQVMsaUNBQWlDLFFBQVEsS0FBSyxjQUFjLFVBQVUsRUFBRTtBQUNqRixhQUFPO0FBQUEsSUFDVDtBQUVBLFVBQU0sY0FBYyxPQUFPLEtBQUssTUFBTSxjQUFjLFlBQVksQ0FBQztBQUdqRSxVQUFNLGdCQUFnQixNQUFNLE9BQU8sS0FBSztBQUFBLE1BQ3RDLHNCQUFzQixRQUFRO0FBQUEsSUFDaEMsQ0FBQyxFQUFFLFFBQVE7QUFFWCxlQUFXLFFBQVEsZUFBZTtBQUNoQyxZQUFNLE9BQU8sT0FBTyxLQUFLLEdBQUc7QUFBQSxJQUM5QjtBQUdBLFVBQU0sV0FBVyxHQUFHLFFBQVEsRUFBRTtBQUM5QixVQUFNLGVBQWUsT0FBTyxpQkFBaUIsVUFBVTtBQUFBLE1BQ3JELGFBQWE7QUFBQSxNQUNiLFVBQVU7QUFBQSxRQUNSLFdBQVcsUUFBUTtBQUFBLFFBQ25CLFNBQVM7QUFBQSxRQUNULGFBQWE7QUFBQSxRQUNiLFlBQVksb0JBQUksS0FBSztBQUFBLE1BQ3ZCO0FBQUEsSUFDRixDQUFDO0FBR0QsVUFBTSxJQUFJLFFBQVEsQ0FBQyxTQUFTLFdBQVc7QUFDckMsbUJBQWEsSUFBSSxhQUFhLENBQUMsVUFBVTtBQUN2QyxZQUFJLE1BQU8sUUFBTyxLQUFLO0FBQUEsWUFDbEIsU0FBUSxhQUFhLEVBQUU7QUFBQSxNQUM5QixDQUFDO0FBQUEsSUFDSCxDQUFDO0FBR0QsVUFBTSxrQkFBa0IsMEJBQTBCLGFBQWEsRUFBRTtBQUVqRSxVQUFNLCtCQUErQjtBQUFBLE1BQ25DLEVBQUUsMkJBQTJCLFFBQVEsR0FBRztBQUFBLE1BQ3hDO0FBQUEsUUFDRSxNQUFNO0FBQUEsVUFDSixnQ0FBZ0M7QUFBQSxRQUNsQztBQUFBLE1BQ0Y7QUFBQSxNQUNBO0FBQUEsUUFDRSxjQUFjLENBQUMsRUFBRSwwQkFBMEIsUUFBUSxHQUFHLENBQUM7QUFBQSxNQUN6RDtBQUFBLElBQ0Y7QUFFQSw4QkFBSSx3QkFBTyxNQUFNLHFCQUFxQixRQUFRLEVBQUUsY0FBYyxhQUFhLEVBQUUsRUFBRTtBQUMvRSxXQUFPO0FBQUEsRUFFVCxTQUFTLEtBQVU7QUFDakIsbUNBQVMsdUJBQXVCLElBQUksT0FBTyxFQUFFO0FBQzdDLFdBQU87QUFBQSxFQUNUO0FBQ0Y7QUFHQSxTQUFTLHFCQUFxQixNQUFjLE1BQXVCO0FBQ2pFLE1BQUksQ0FBQyxLQUFNLFFBQU87QUFFbEIsTUFBSSxhQUFhLEtBQUssWUFBWSxFQUMvQixRQUFRLE1BQU0sRUFBRSxFQUNoQixRQUFRLE1BQU0sRUFBRSxFQUNoQixRQUFRLE1BQU0sR0FBRyxFQUNqQixRQUFRLFFBQVEsR0FBRyxFQUNuQixLQUFLO0FBR1IsUUFBTSxjQUFjO0FBQUEsSUFBQztBQUFBLElBQWM7QUFBQSxJQUFhO0FBQUEsSUFBYTtBQUFBLElBQVk7QUFBQSxJQUNyRDtBQUFBLElBQVU7QUFBQSxJQUFXO0FBQUEsSUFBUztBQUFBLElBQVU7QUFBQSxJQUFhO0FBQUEsSUFDckQ7QUFBQSxJQUFRO0FBQUEsSUFBYTtBQUFBLElBQW1CO0FBQUEsRUFBYztBQUUxRSxhQUFXLGNBQWMsYUFBYTtBQUNwQyxpQkFBYSxXQUFXLFFBQVEsSUFBSSxPQUFPLE1BQU0sVUFBVSxPQUFPLEdBQUcsR0FBRyxFQUFFLEVBQUUsS0FBSztBQUFBLEVBQ25GO0FBR0EsUUFBTSxrQkFBa0IsbUJBQW1CO0FBRzNDLGFBQVcsQ0FBQyxXQUFXLFVBQVUsS0FBSyxPQUFPLFFBQVEsZUFBZSxHQUFHO0FBQ3JFLFFBQUksV0FBVyxTQUFTLFNBQVMsR0FBRztBQUNsQyxtQkFBYSxXQUFXLFFBQVEsV0FBVyxTQUFTO0FBQ3BEO0FBQUEsSUFDRjtBQUdBLGVBQVcsYUFBYSxZQUFZO0FBQ2xDLFVBQUksV0FBVyxTQUFTLFNBQVMsR0FBRztBQUNsQyxxQkFBYSxXQUFXLFFBQVEsV0FBVyxTQUFTO0FBQ3BEO0FBQUEsTUFDRjtBQUFBLElBQ0Y7QUFBQSxFQUNGO0FBR0EsTUFBSSxNQUFNO0FBQ1IsVUFBTSxpQkFBaUIsS0FBSyxZQUFZLEVBQ3JDLFFBQVEsV0FBVyxHQUFHLEVBQ3RCLFFBQVEsZUFBZSxJQUFJLEVBQzNCLFFBQVEsWUFBWSxHQUFHLEVBQ3ZCLFFBQVEsaUJBQWlCLElBQUk7QUFDaEMsa0JBQWMsSUFBSSxjQUFjO0FBQUEsRUFDbEM7QUFFQSxTQUFPLFdBQVcsS0FBSztBQUN6QjtBQUdBLFNBQVMscUJBQStDO0FBQ3RELFNBQU87QUFBQTtBQUFBLElBRUwsUUFBUSxDQUFDLE9BQU8sY0FBYyxhQUFhO0FBQUEsSUFDM0MsY0FBYyxDQUFDLG9CQUFvQixzQkFBc0I7QUFBQSxJQUN6RCxhQUFhLENBQUMsY0FBYyxrQkFBa0I7QUFBQSxJQUM5QyxhQUFhLENBQUMsbUJBQW1CLGlCQUFpQjtBQUFBO0FBQUEsSUFHbEQsVUFBVSxDQUFDLGdCQUFnQixnQkFBZ0IsZUFBZSxlQUFlO0FBQUEsSUFDekUsWUFBWSxDQUFDLG1CQUFtQixrQkFBa0IsZ0JBQWdCO0FBQUEsSUFDbEUsZUFBZSxDQUFDLGdCQUFnQixvQkFBb0IsbUJBQW1CO0FBQUEsSUFDdkUsY0FBYyxDQUFDLHVCQUF1QixtQkFBbUIsbUJBQW1CO0FBQUEsSUFDNUUsVUFBVSxDQUFDLGlCQUFpQixvQkFBb0IsY0FBYztBQUFBO0FBQUEsSUFHOUQsU0FBUyxDQUFDLGlCQUFpQixpQkFBaUIsYUFBYTtBQUFBLElBQ3pELFdBQVcsQ0FBQyxVQUFVLG1CQUFtQixpQkFBaUI7QUFBQSxJQUMxRCxXQUFXLENBQUMsVUFBVSxpQkFBaUIsa0JBQWtCO0FBQUEsSUFDekQsV0FBVyxDQUFDLGlCQUFpQixlQUFlLG9CQUFvQjtBQUFBO0FBQUEsSUFHaEUsV0FBVyxDQUFDLFVBQVUsaUJBQWlCLGNBQWM7QUFBQSxJQUNyRCxhQUFhLENBQUMsbUJBQW1CLGFBQWEscUJBQXFCO0FBQUEsSUFDbkUsVUFBVSxDQUFDLFNBQVMsZ0JBQWdCLGVBQWUsaUJBQWlCO0FBQUEsSUFDcEUsVUFBVSxDQUFDLFNBQVMsZ0JBQWdCLGFBQWE7QUFBQTtBQUFBLElBR2pELFFBQVEsQ0FBQyxhQUFhLGFBQWEsZ0JBQWdCLG1CQUFtQjtBQUFBLElBQ3RFLGFBQWEsQ0FBQyxrQkFBa0IsbUJBQW1CLHNCQUFzQjtBQUFBLElBQ3pFLFNBQVMsQ0FBQyxjQUFjLGlCQUFpQixnQkFBZ0I7QUFBQSxJQUN6RCxPQUFPLENBQUMsZ0JBQWdCLG9CQUFvQixTQUFTO0FBQUE7QUFBQSxJQUdyRCxjQUFjLENBQUMsc0JBQXNCLG9CQUFvQjtBQUFBLElBQ3pELFdBQVcsQ0FBQyxZQUFZLGtCQUFrQixvQkFBb0I7QUFBQSxJQUM5RCxlQUFlLENBQUMsY0FBYyxvQkFBb0Isb0JBQW9CO0FBQUEsSUFDdEUsWUFBWSxDQUFDLFdBQVcsdUJBQXVCLG9CQUFvQjtBQUFBO0FBQUEsSUFHbkUsV0FBVyxDQUFDLFVBQVUsb0JBQW9CLGVBQWU7QUFBQSxJQUN6RCxTQUFTLENBQUMsZUFBZSxzQkFBc0IsWUFBWTtBQUFBLElBQzNELFdBQVcsQ0FBQyxVQUFVLGlCQUFpQixnQkFBZ0I7QUFBQSxJQUN2RCxTQUFTLENBQUMsaUJBQWlCLGNBQWMsZUFBZTtBQUFBO0FBQUEsSUFHeEQsV0FBVyxDQUFDLFlBQVksbUJBQW1CO0FBQUEsSUFDM0MsY0FBYyxDQUFDLGFBQWEsc0JBQXNCO0FBQUEsSUFDbEQsWUFBWSxDQUFDLGtCQUFrQixpQkFBaUI7QUFBQSxJQUNoRCxPQUFPLENBQUMsYUFBYSxjQUFjLG1CQUFtQjtBQUFBO0FBQUEsSUFHdEQsVUFBVSxDQUFDLGlCQUFpQixpQkFBaUI7QUFBQSxJQUM3QyxVQUFVLENBQUMscUJBQXFCLGdCQUFnQjtBQUFBLElBQ2hELFVBQVUsQ0FBQyx5QkFBeUIsZUFBZTtBQUFBLElBQ25ELFdBQVcsQ0FBQyx3QkFBd0IsaUJBQWlCO0FBQUE7QUFBQSxJQUdyRCxZQUFZLENBQUMsV0FBVyxxQkFBcUIsa0JBQWtCO0FBQUEsSUFDL0QsV0FBVyxDQUFDLGlCQUFpQixnQkFBZ0I7QUFBQSxJQUM3QyxXQUFXLENBQUMsaUJBQWlCLGdCQUFnQjtBQUFBLElBQzdDLFFBQVEsQ0FBQyxPQUFPLHFCQUFxQixvQkFBb0I7QUFBQSxFQUMzRDtBQUNGO0FBR0EsU0FBUyxvQkFBb0IsSUFBWSxJQUFvQjtBQUMzRCxNQUFJLENBQUMsR0FBSSxRQUFPLEtBQUssR0FBRyxTQUFTO0FBQ2pDLE1BQUksQ0FBQyxHQUFJLFFBQU8sR0FBRztBQUVuQixRQUFNLFNBQXFCLENBQUM7QUFFNUIsV0FBUyxJQUFJLEdBQUcsS0FBSyxHQUFHLFFBQVEsS0FBSztBQUNuQyxXQUFPLENBQUMsSUFBSSxDQUFDLENBQUM7QUFBQSxFQUNoQjtBQUVBLFdBQVMsSUFBSSxHQUFHLEtBQUssR0FBRyxRQUFRLEtBQUs7QUFDbkMsV0FBTyxDQUFDLEVBQUUsQ0FBQyxJQUFJO0FBQUEsRUFDakI7QUFFQSxXQUFTLElBQUksR0FBRyxLQUFLLEdBQUcsUUFBUSxLQUFLO0FBQ25DLGFBQVMsSUFBSSxHQUFHLEtBQUssR0FBRyxRQUFRLEtBQUs7QUFDbkMsWUFBTSxPQUFPLEdBQUcsSUFBSSxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUMsSUFBSSxJQUFJO0FBQzNDLGFBQU8sQ0FBQyxFQUFFLENBQUMsSUFBSSxLQUFLO0FBQUEsUUFDbEIsT0FBTyxJQUFJLENBQUMsRUFBRSxDQUFDLElBQUk7QUFBQSxRQUNuQixPQUFPLENBQUMsRUFBRSxJQUFJLENBQUMsSUFBSTtBQUFBLFFBQ25CLE9BQU8sSUFBSSxDQUFDLEVBQUUsSUFBSSxDQUFDLElBQUk7QUFBQSxNQUN6QjtBQUFBLElBQ0Y7QUFBQSxFQUNGO0FBRUEsU0FBTyxPQUFPLEdBQUcsTUFBTSxFQUFFLEdBQUcsTUFBTTtBQUNwQztBQUdBLFNBQVMsb0JBQW9CLE9BQWUsT0FBdUI7QUFDakUsTUFBSSxDQUFDLFNBQVMsQ0FBQyxNQUFPLFFBQU87QUFDN0IsTUFBSSxVQUFVLE1BQU8sUUFBTztBQUU1QixRQUFNLFdBQVcsb0JBQW9CLE9BQU8sS0FBSztBQUNqRCxRQUFNLFlBQVksS0FBSyxJQUFJLE1BQU0sUUFBUSxNQUFNLE1BQU07QUFFckQsU0FBTyxjQUFjLElBQUksSUFBTSxJQUFNLFdBQVc7QUFDbEQ7QUFHQSxlQUFlLGNBQWMsZ0JBQXdCLE1BQTBCLGNBQW9DO0FBQ2pILE1BQUk7QUFFRixVQUFNLGFBQWEsTUFBTSwrQkFBK0IsUUFBUTtBQUFBLE1BQzlEO0FBQUEsSUFDRixDQUFDO0FBRUQsUUFBSSxZQUFZO0FBQ2QsZ0NBQUksd0JBQU8sT0FBTyw2QkFBd0IsV0FBVyxXQUFXLEVBQUU7QUFDbEUsYUFBTztBQUFBLElBQ1Q7QUFHQSxVQUFNLGNBQWMsTUFBTSwrQkFBK0IsS0FBSyxDQUFDLENBQUMsRUFBRSxNQUFNLEdBQUksRUFBRSxRQUFRO0FBRXRGLFFBQUksWUFBaUI7QUFDckIsUUFBSSxZQUFZO0FBQ2hCLFVBQU0sWUFBWTtBQUVsQixlQUFXLFdBQVcsYUFBYTtBQUNqQyxVQUFJLFFBQVEsb0JBQW9CLGdCQUFnQixRQUFRLGNBQWM7QUFHdEUsVUFBSSxRQUFRLFdBQVcsTUFBTSxRQUFRLFFBQVEsT0FBTyxHQUFHO0FBQ3JELG1CQUFXLFNBQVMsUUFBUSxTQUFTO0FBQ25DLGdCQUFNLGFBQWEsb0JBQW9CLGdCQUFnQixLQUFLO0FBQzVELGtCQUFRLEtBQUssSUFBSSxPQUFPLFVBQVU7QUFBQSxRQUNwQztBQUFBLE1BQ0Y7QUFFQSxVQUFJLFFBQVEsYUFBYSxTQUFTLFdBQVc7QUFDM0Msb0JBQVk7QUFDWixvQkFBWTtBQUFBLE1BQ2Q7QUFBQSxJQUNGO0FBRUEsUUFBSSxXQUFXO0FBQ2IsZ0NBQUksd0JBQU8sUUFBUSw2QkFBd0IsVUFBVSxXQUFXLGlCQUFpQixVQUFVLFFBQVEsQ0FBQyxDQUFDLEdBQUc7QUFDeEcsYUFBTztBQUFBLElBQ1Q7QUFFQSw4QkFBSSx3QkFBTyxLQUFLLDhCQUF5QixZQUFZLEVBQUU7QUFDdkQsV0FBTztBQUFBLEVBQ1QsU0FBUyxPQUFZO0FBQ25CLG1DQUFTLCtCQUEwQixNQUFNLE9BQU8sRUFBRTtBQUNsRCxXQUFPO0FBQUEsRUFDVDtBQUNGO0FBR0EsZUFBZSxnQkFBZ0IsdUJBQTRCLFVBQWlDO0FBQzFGLE1BQUk7QUFDRixVQUFNLGtCQUFrQixxQkFBcUIsUUFBUTtBQUNyRCxRQUFJLENBQUMsZ0JBQWlCO0FBRXRCLFVBQU0sVUFBVSxNQUFNLCtCQUErQixRQUFRLEVBQUUsS0FBSyxzQkFBc0IsQ0FBQztBQUMzRixRQUFJLENBQUMsUUFBUztBQUdkLFVBQU0saUJBQWlCLFFBQVEsV0FBVyxDQUFDO0FBRzNDLFFBQUksb0JBQW9CLFFBQVEsa0JBQWtCLGVBQWUsU0FBUyxlQUFlLEVBQUc7QUFHNUYsbUJBQWUsS0FBSyxlQUFlO0FBRW5DLFVBQU0sK0JBQStCO0FBQUEsTUFDbkMsRUFBRSxLQUFLLHNCQUFzQjtBQUFBLE1BQzdCO0FBQUEsUUFDRSxNQUFNO0FBQUEsVUFDSixTQUFTO0FBQUEsVUFDVCxXQUFXLG9CQUFJLEtBQUs7QUFBQSxRQUN0QjtBQUFBLE1BQ0Y7QUFBQSxJQUNGO0FBRUEsOEJBQUksd0JBQU8sTUFBTSwwQkFBbUIsZUFBZSxnQkFBZ0IscUJBQXFCLEVBQUU7QUFBQSxFQUM1RixTQUFTLE9BQVk7QUFDbkIsbUNBQVMsOEJBQXlCLE1BQU0sT0FBTyxFQUFFO0FBQUEsRUFDbkQ7QUFDRjtBQWdCQSxlQUFzQixlQUFlO0FBQ25DLE1BQUksUUFBUTtBQUNWLFVBQU0sT0FBTyxNQUFNO0FBQ25CLDhCQUFJLHdCQUFPLE1BQU0sMkJBQTJCO0FBQUEsRUFDOUM7QUFDRjs7O0FDamtCQSxJQUFBQyxvQkFBc0M7QUFMdEMsU0FBUyxlQUFBQyxjQUE2QixZQUFBQyxpQkFBZ0I7QUFDdEQsWUFBWUMsYUFBWTtBQUNqQixlQUFPO0FBS2QsSUFBSUM7QUFDSixJQUFJQztBQUNKLElBQUlDO0FBQ0osSUFBSUM7QUFDSixJQUFJQztBQUVKLGVBQXNCLHdDQUF3QztBQUM1RCxRQUFNLG1CQUFtQixRQUFRLElBQUk7QUFDckMsUUFBTSxlQUFlLFFBQVEsSUFBSSx5QkFBeUI7QUFFMUQsTUFBSSxDQUFDLGtCQUFrQjtBQUNyQixVQUFNLE1BQU0sMENBQTBDO0FBQUEsRUFDeEQ7QUFFQSxNQUFJO0FBQ0YsSUFBQUosVUFBUyxJQUFJSCxhQUFZLGdCQUFnQjtBQUN6QyxVQUFNRyxRQUFPLFFBQVE7QUFDckIsSUFBQUMsTUFBS0QsUUFBTyxHQUFHLFlBQVk7QUFFM0IsSUFBQUUsa0NBQWlDRCxJQUFHLFdBQVcsc0JBQXNCO0FBQ3JFLElBQUFFLG9CQUFtQkYsSUFBRyxXQUFXLFFBQVE7QUFDekMsSUFBQUcsK0JBQThCSCxJQUFHLFdBQVcsbUJBQW1CO0FBRS9ELCtCQUFJLHlCQUFPLE9BQU8sa0RBQTZDO0FBRy9ELFVBQU0sc0JBQXNCO0FBQUEsRUFFOUIsU0FBUyxPQUFZO0FBQ25CLG9DQUFTLHVEQUF1RCxNQUFNLE9BQU8sRUFBRTtBQUMvRSxVQUFNO0FBQUEsRUFDUjtBQUNGO0FBRUEsZUFBZSx3QkFBd0I7QUFDckMsTUFBSTtBQUNGLFVBQU0scUJBQXFCLE1BQU1HLDZCQUE0QixlQUFlO0FBRTVFLFFBQUksdUJBQXVCLEdBQUc7QUFFNUIsWUFBTSxpQkFBaUI7QUFBQSxRQUNyQixFQUFFLE1BQU0sZUFBZSxVQUFVLE1BQU0sT0FBTyxHQUFHLFdBQVcsRUFBRTtBQUFBLFFBQzlELEVBQUUsTUFBTSxvQkFBb0IsVUFBVSxNQUFNLE9BQU8sR0FBRyxXQUFXLEVBQUU7QUFBQSxRQUNuRSxFQUFFLE1BQU0sc0JBQXNCLFVBQVUsTUFBTSxPQUFPLEdBQUcsV0FBVyxFQUFFO0FBQUEsUUFDckUsRUFBRSxNQUFNLGFBQWEsVUFBVSxNQUFNLE9BQU8sR0FBRyxXQUFXLEVBQUU7QUFBQSxRQUM1RCxFQUFFLE1BQU0sc0JBQXNCLFVBQVUsTUFBTSxPQUFPLEdBQUcsV0FBVyxFQUFFO0FBQUEsTUFDdkU7QUFFQSxZQUFNLGVBQWUsTUFBTUEsNkJBQTRCLFdBQVcsY0FBYztBQUNoRixpQ0FBSSx5QkFBTyxNQUFNLGtCQUFhLGFBQWEsYUFBYSxrQkFBa0I7QUFHMUUsWUFBTSxlQUFlLE9BQU8sT0FBTyxhQUFhLFdBQVcsRUFBRSxLQUFLLE9BQU8sT0FBTztBQUM5RSxjQUFNLE1BQU0sTUFBTUEsNkJBQTRCLFFBQVEsRUFBRSxLQUFLLEdBQUcsQ0FBQztBQUNqRSxlQUFPLEtBQUssU0FBUztBQUFBLE1BQ3ZCLENBQUM7QUFHRCxpQ0FBSSx5QkFBTyxNQUFNLHlDQUFvQztBQUFBLElBQ3ZEO0FBQUEsRUFDRixTQUFTLE9BQVk7QUFDbkIsb0NBQVMsc0NBQXNDLE1BQU0sT0FBTyxFQUFFO0FBQUEsRUFDaEU7QUFDRjtBQUVBLGVBQXNCLGtDQUFrQyxnQkFBaUQ7QUFDdkcsTUFBSSxDQUFDRixpQ0FBZ0M7QUFDbkMsb0NBQVMsa0RBQWtEO0FBQzNELFdBQU87QUFBQSxFQUNUO0FBRUEsTUFBSTtBQUdGLFVBQU0saUJBQWlCRyxzQkFBcUIsZUFBZSxNQUFNLGVBQWUsSUFBSTtBQUNwRixVQUFNLE1BQU0sb0JBQUksS0FBSztBQUdyQixVQUFNLGtCQUFrQixNQUFNSCxnQ0FBK0IsUUFBUTtBQUFBLE1BQ25FLDJCQUEyQixlQUFlO0FBQUEsSUFDNUMsQ0FBQztBQUVELFFBQUksaUJBQWlCO0FBRW5CLFlBQU1BLGdDQUErQjtBQUFBLFFBQ25DLEVBQUUsS0FBSyxnQkFBZ0IsSUFBSTtBQUFBLFFBQzNCO0FBQUEsVUFDRSxNQUFNO0FBQUEsWUFDSixnQ0FBZ0M7QUFBQSxZQUNoQyxzQ0FBc0MsZUFBZTtBQUFBLFlBQ3JELHFDQUFxQyxlQUFlO0FBQUEsWUFDcEQsV0FBVztBQUFBLFVBQ2I7QUFBQSxRQUNGO0FBQUEsUUFDQTtBQUFBLFVBQ0UsY0FBYyxDQUFDLEVBQUUsMEJBQTBCLGVBQWUsR0FBRyxDQUFDO0FBQUEsUUFDaEU7QUFBQSxNQUNGO0FBRUEsaUNBQUkseUJBQU8sTUFBTSxtQ0FBbUMsZUFBZSxLQUFLLE1BQU0sR0FBRyxFQUFFLENBQUMsRUFBRTtBQUN0RixhQUFPLGdCQUFnQixJQUFJLFNBQVM7QUFBQSxJQUN0QyxPQUFPO0FBRUwsWUFBTSxhQUFhO0FBQUEsUUFDakI7QUFBQSxRQUNBLGFBQWEsZUFBZTtBQUFBLFFBQzVCLGFBQWEsZUFBZTtBQUFBLFFBQzVCLFlBQVk7QUFBQTtBQUFBLFFBQ1osU0FBUztBQUFBO0FBQUEsUUFDVCxpQkFBaUI7QUFBQSxRQUNqQixhQUFhO0FBQUEsUUFDYixVQUFVLENBQUM7QUFBQSxVQUNULGdCQUFnQixlQUFlO0FBQUEsVUFDL0IsU0FBUztBQUFBO0FBQUEsVUFDVCxXQUFXLGVBQWU7QUFBQSxVQUMxQixXQUFXLGVBQWU7QUFBQSxVQUMxQixnQkFBZ0IsZUFBZTtBQUFBLFVBQy9CLGVBQWUsZUFBZTtBQUFBLFVBQzlCLFVBQVU7QUFBQSxVQUNWLFVBQVU7QUFBQSxVQUNWLFVBQVU7QUFBQSxRQUNaLENBQUM7QUFBQSxRQUNELGNBQWMsZUFBZSxPQUFPLENBQUM7QUFBQSxVQUNuQyxVQUFVLGVBQWU7QUFBQSxVQUN6QixpQkFBaUI7QUFBQSxVQUNqQixjQUFjO0FBQUEsVUFDZCxlQUFlO0FBQUEsUUFDakIsQ0FBQyxJQUFJLENBQUM7QUFBQSxRQUNOLGVBQWUsQ0FBQztBQUFBLFVBQ2QsU0FBUztBQUFBLFVBQ1QsT0FBTyxlQUFlO0FBQUEsVUFDdEIsV0FBVztBQUFBLFVBQ1gsY0FBYztBQUFBLFVBQ2QsWUFBWTtBQUFBLFFBQ2QsQ0FBQztBQUFBLFFBQ0QsV0FBVztBQUFBLFFBQ1gsV0FBVztBQUFBLE1BQ2I7QUFFQSxZQUFNLGVBQWUsTUFBTUEsZ0NBQStCLFVBQVUsVUFBVTtBQUM5RSxpQ0FBSSx5QkFBTyxNQUFNLG1DQUFtQyxlQUFlLEtBQUssTUFBTSxHQUFHLEVBQUUsQ0FBQyxFQUFFO0FBQ3RGLGFBQU8sYUFBYSxXQUFXLFNBQVM7QUFBQSxJQUMxQztBQUFBLEVBRUYsU0FBUyxPQUFZO0FBQ25CLG9DQUFTLDJDQUEyQyxNQUFNLE9BQU8sRUFBRTtBQUNuRSxXQUFPO0FBQUEsRUFDVDtBQUNGO0FBR0EsU0FBU0csc0JBQXFCLE1BQWMsTUFBdUI7QUFDakUsTUFBSSxhQUFhLEtBQUssWUFBWSxFQUMvQixRQUFRLGdCQUFnQixHQUFHLEVBQzNCLFFBQVEsUUFBUSxHQUFHLEVBQ25CLEtBQUs7QUFFUixNQUFJLE1BQU07QUFDUixVQUFNLGlCQUFpQixLQUFLLFlBQVksRUFDckMsUUFBUSxjQUFjLEVBQUUsRUFDeEIsS0FBSztBQUNSLGtCQUFjLE1BQU07QUFBQSxFQUN0QjtBQUVBLFNBQU87QUFDVDtBQUdBLGVBQXNCLG1DQUFtQztBQUN2RCxNQUFJO0FBQ0YsUUFBSUwsU0FBUTtBQUNWLFlBQU1BLFFBQU8sTUFBTTtBQUFBLElBQ3JCO0FBQ0EsK0JBQUkseUJBQU8sTUFBTSx5REFBb0Q7QUFBQSxFQUN2RSxTQUFTLE9BQVk7QUFDbkIsb0NBQVMsd0NBQXdDLE1BQU0sT0FBTyxFQUFFO0FBQUEsRUFDbEU7QUFDRjs7O0FGN0tBLCtCQUFpQztBQUVqQyxJQUFBTSxvQkFHTztBQWZBLGVBQU87QUFDUCxlQUFPLEVBQUUsTUFBTSxjQUFjLFVBQVUsS0FBSyxDQUFDO0FBcUJwRCxJQUFNLHVCQUF1QjtBQUc3QixJQUFNLDhCQUE4QjtBQUdwQyxJQUFNLFlBQVksS0FBSyxJQUFJO0FBRzNCLElBQUksa0JBQW9DLGFBQWE7QUFHOUMsSUFBSSxlQUFlO0FBQ25CLElBQUksbUJBQW1CO0FBQzlCLElBQUksZUFBZTtBQUNuQixrQkFBa0IsTUFBTSxnQkFBZ0IsZUFBZTtBQUd2RCxJQUFJLGNBQWM7QUFDaEIsUUFBTSxpQkFBaUI7QUFHdkIsUUFBTSxzQ0FBc0M7QUFDOUM7QUFHQSxJQUFJO0FBQ0osSUFBSTtBQUNKLFVBQVUsTUFBTSx3QkFBd0IsWUFBWTtBQUdwRCxJQUFJLFFBQVEsSUFBSSx5QkFBeUIsUUFBUTtBQUMvQyxNQUFJO0FBQ0YsVUFBTSwwQkFBMEI7QUFBQSxFQUNsQyxTQUFTLE9BQU87QUFDZCxvQ0FBUywyQkFBMkIsS0FBSyxFQUFFO0FBQzNDLCtCQUFJLHlCQUFPLFFBQVEsMkNBQTJDO0FBQUEsRUFDaEU7QUFDRixPQUFPO0FBQ0wsNkJBQUkseUJBQU8sUUFBUSxxREFBcUQ7QUFDMUU7QUFHQSxNQUFNLGtCQUFrQjtBQUd4QixNQUFNLFFBQVEsTUFBTTtBQUNwQixJQUFJLGNBQWM7QUFDaEIsUUFBTSxpQ0FBaUM7QUFDdkMsUUFBTSxhQUFhO0FBQ3JCO0FBQUEsSUFDQTtBQUFBLEVBQ0UseUJBQU87QUFBQSxFQUNQO0FBQUEsK0NBQThDLHVDQUFvQixTQUFTLENBQUM7QUFBQTtBQUM5RTtBQVFBLFNBQVMsYUFBYSxXQUFtQixnQkFBa0M7QUFFekUsUUFBTSx1QkFBNkIseUNBQXNCLFFBQVE7QUFHakUsTUFBSUMsbUJBQW9DLENBQUM7QUFDekMsbUJBQWlCLElBQUksQ0FBQyxTQUFTO0FBQzdCLFFBQUksYUFBYSxzQkFBc0IsSUFBSTtBQUMzQyxRQUFJLGVBQWUsUUFBVztBQUM1QixNQUFBQSxtQkFBa0IsQ0FBQyxHQUFHQSxrQkFBaUIsR0FBRyxVQUFVO0FBQUEsSUFDdEQ7QUFBQSxFQUNGLENBQUM7QUFHRCxTQUFPQTtBQUNUO0FBT0EsZUFBZSxvQkFBb0I7QUFHakM7QUFBQSxJQUNFLHlCQUFPO0FBQUEsSUFDUCxHQUFHLGdCQUFnQixNQUFNLHVCQUF1QixPQUFPLEVBQUUsSUFDekQsR0FBRyxvQkFBb0IsMEJBQTBCLE9BQU8sRUFBRSxLQUN6RCxlQUFlLG9CQUFvQjtBQUFBLEVBQ3RDO0FBR0EsV0FBUyxJQUFJLEdBQUcsSUFBSSxnQkFBZ0IsUUFBUSxLQUFLO0FBQy9DLFVBQU0saUJBQWlDLGdCQUFnQixDQUFDO0FBQ3hELFFBQUksTUFBYyxnQkFBZ0IsQ0FBQyxFQUFFO0FBR3JDLFVBQU0sV0FBVyxJQUFJLFFBQVEsWUFBWSxFQUFFO0FBQzNDO0FBQUEsTUFDRSx5QkFBTztBQUFBLE1BQ1A7QUFBQSxHQUFNLElBQUksQ0FBQyxJQUFJLGdCQUFnQixNQUFNLEtBQUssUUFBUTtBQUFBLElBQ3BEO0FBRUEsUUFBSTtBQUVGLFlBQU0sS0FBSyxLQUFLLEdBQUc7QUFHbkIsZUFBUyxXQUFXLEdBQUcsV0FBVyxHQUFHLFlBQVk7QUFFL0MsY0FBTSxxQkFBcUIsS0FBSyxPQUFPLElBQUksTUFBTztBQUNsRCxjQUFNLEtBQUssZUFBZSxrQkFBa0I7QUFDNUMsY0FBTSxLQUFLLFNBQVMsTUFBTSxVQUFVO0FBQUEsTUFDdEM7QUFHQSxZQUFNLEtBQUssa0JBQWtCLEdBQUs7QUFJbEMsWUFBTSxLQUFLLGdCQUFnQixrQkFBa0I7QUFHN0MsWUFBTSxPQUFPLE1BQU0sS0FBSyxVQUFVLGNBQWM7QUFDaEQsWUFBTSxJQUFZLGFBQUssSUFBSTtBQUczQixZQUFNLG9CQUFvQixFQUFFLCtDQUErQztBQUczRSxZQUFNLHVCQUF1QixFQUFFLHNFQUFzRTtBQUNyRyxZQUFNLFVBQW9CLHFCQUFxQixJQUFJLENBQUMsT0FBTyxZQUFZO0FBQ3JFLGVBQU8sRUFBRSxPQUFPLEVBQUUsS0FBSyxHQUFHLEVBQUUsTUFBTSxFQUFFLEtBQUssTUFBTTtBQUFBLE1BQ2pELENBQUMsRUFBRSxRQUFRO0FBR1gsWUFBTSxpQkFBaUIsa0JBQWtCLE9BQU8sQ0FBQyxPQUFPLFlBQVk7QUFDbEUsY0FBTSxjQUFjLEVBQUUsT0FBTyxFQUFFLEtBQUssR0FBRyxFQUFFLE1BQU0sRUFBRSxLQUFLLE1BQU07QUFDNUQsZUFBTyxDQUFDLFFBQVEsU0FBUyxXQUFZO0FBQUEsTUFDdkMsQ0FBQztBQUdEO0FBQUEsUUFDRSx5QkFBTztBQUFBLFFBQ1AsR0FBRyxlQUFlLE1BQU0seUJBQXlCLE9BQU8sRUFBRSxJQUMxRCxxQkFBaUIsdUNBQW9CLFNBQVMsQ0FBQyxHQUFHLE9BQU8sRUFBRSxJQUMzRCxhQUFhLEVBQUUsVUFBVSxlQUFlLFdBQVcsS0FBSyxLQUFLLENBQUMsQ0FBQztBQUFBLE1BQ2pFO0FBR0EsVUFBSSxDQUFDLGFBQWMsdUNBQWU7QUFHbEMsVUFBSSxrQkFBa0I7QUFBQSxRQUNwQixhQUFhO0FBQUEsUUFDYixjQUFjO0FBQUEsUUFDZCxhQUFhO0FBQUEsUUFDYixpQkFBaUI7QUFBQSxNQUNuQjtBQUdBLHdCQUNFLE1BQU0sMkJBQTJCLGdCQUFnQixnQkFBZ0IsZUFBZTtBQUdsRixVQUFJLGNBQWM7QUFDaEI7QUFBQSxVQUNFLHlCQUFPO0FBQUEsVUFDUCxhQUFhLGdCQUFnQixXQUFXLGtCQUNyQyxnQkFBZ0IsWUFBWSxvQkFDNUIsZ0JBQWdCLFdBQVcsa0JBQzNCLGdCQUFnQixlQUFlO0FBQUEsUUFDcEM7QUFBQSxNQUNGO0FBR0EsWUFBTSxXQUFXLHVCQUF1QixHQUFJO0FBQUEsSUFFOUMsU0FBUyxPQUFnQjtBQUN2QixVQUFJLE9BQU8sVUFBVSxVQUFVO0FBQzdCLFlBQUksTUFBTSxTQUFTLDZCQUE2QixHQUFHO0FBQ2pELDBDQUFTLHlDQUF5QyxLQUFLO0FBQ3ZEO0FBQUEsUUFDRjtBQUFBLE1BQ0Y7QUFDQTtBQUFBLFFBQ0UseURBQXlEO0FBQUEsTUFDM0Q7QUFBQSxJQUNGO0FBQUEsRUFDRjtBQUNGO0FBT0EsZUFBZSwyQkFFWCxnQkFDQSxnQkFDQSxpQkFLRztBQUdMLFdBQVMsSUFBSSxHQUFHLElBQUksZUFBZSxRQUFRLEtBQUs7QUFDOUMsVUFBTSxzQkFBc0IsZUFBZSxDQUFDO0FBRTVDLFVBQU0sVUFBVTtBQUFBLE1BQ2Q7QUFBQSxNQUNBLGVBQWU7QUFBQSxJQUNqQjtBQUVBLFFBQUksZ0JBQWdCLFlBQVksUUFBVztBQUV6QyxZQUFNLFdBQVcsTUFBTSx1QkFBdUIsT0FBTztBQUdyRCxZQUFNLHdCQUF3QixNQUFNLGtDQUFrQyxPQUFPO0FBRzdFLGNBQVEsVUFBVTtBQUFBLFFBQ2hCO0FBQ0UsMEJBQWdCO0FBQ2hCO0FBQUEsUUFDRjtBQUNFLDBCQUFnQjtBQUNoQjtBQUFBLFFBQ0Y7QUFDRSwwQkFBZ0I7QUFDaEI7QUFBQSxRQUNGO0FBQ0UsMEJBQWdCO0FBQ2hCO0FBQUEsUUFDRjtBQUNFO0FBQUEsTUFDSjtBQUdBLFVBQUksa0JBQWtCO0FBRXBCLGNBQU0sZUFDSjtBQUNGLGNBQU0sa0NBQ0o7QUFDRixjQUFNLFdBQ0osZUFBZSxRQUFRLEtBQUs7QUFFOUIsY0FBTSxxQkFBcUIsVUFBVyxPQUFPO0FBQUEsTUFDL0M7QUFBQSxJQUNGLFdBQVcsQ0FBQyxnQkFBZ0IsWUFBWSxRQUFXO0FBRWpELDJDQUFjLE9BQVE7QUFBQSxJQUN4QjtBQUlBLFVBQU0sV0FBVywyQkFBMkI7QUFBQSxFQUM5QztBQUdBLFNBQU87QUFDVDtBQU9BLFNBQVMsZ0JBQWdCQSxrQkFBbUM7QUFDMUQsTUFBSSxRQUFRLEtBQUssU0FBUyxHQUFHO0FBRTNCLFVBQU0sV0FBVyxRQUFRLEtBQUssTUFBTSxHQUFHLFFBQVEsS0FBSyxNQUFNO0FBRzFELFFBQUksZUFBZTtBQUNuQixhQUFTLFFBQVEsT0FBTyxRQUFRO0FBQzlCLFVBQUksUUFBUSxLQUFNLGdCQUFlO0FBQUEsZUFDeEIsUUFBUSxTQUFVLG9CQUFtQjtBQUFBLGVBQ3JDLFFBQVEsV0FBWSxnQkFBZTtBQUFBLGVBQ25DLFFBQVEsU0FBVSxnQkFBZTtBQUFBLGVBR2pDLElBQUksU0FBUyxRQUFRLEVBQUcsaUJBQWdCO0FBQUEsZUFHeEMsUUFBUSxVQUFXLENBQUFBLG1CQUFrQkEsaUJBQWdCLFFBQVE7QUFBQSxJQU14RSxDQUFDO0FBR0QsVUFBTSxZQUFZLHNCQUFzQixZQUFZO0FBQ3BELFFBQUksY0FBYyxPQUFXLENBQUFBLG1CQUFrQixDQUFDLFNBQVM7QUFBQSxFQUMzRDtBQUNBLFNBQU9BO0FBQ1Q7QUFNQSxlQUFlLHdCQUF3QixXQUFXLE1BQU07QUFDdEQ7QUFBQSxJQUNFLHlCQUFPO0FBQUEsSUFDUCwwQkFDQyxRQUFRLEtBQUssU0FBUyxJQUNuQixPQUFPLFFBQVEsS0FBSyxTQUFTLEtBQUssc0JBQ2xDO0FBQUEsRUFDTjtBQUNBLFlBQVUsTUFBTSxXQUFXLFFBQVEsT0FBTztBQUFBLElBQ3hDO0FBQUEsRUFDRixDQUFDO0FBQ0QsU0FBTyxNQUFNLFFBQVEsUUFBUTtBQUc3QixRQUFNLDBCQUEwQjtBQUVoQyxTQUFPO0FBQ1Q7QUFNQSxlQUFlLDBCQUEwQixlQUF1QixJQUFJO0FBRWxFLE1BQUksaUJBQWlCLElBQUk7QUFDdkIsUUFBSSxRQUFRLElBQUksV0FBWSxnQkFBZSxRQUFRLElBQUk7QUFBQSxTQUVsRDtBQUNILGlDQUFJLHlCQUFPLFFBQVEsc0RBQXNEO0FBQ3pFO0FBQUEsSUFDRjtBQUFBLEVBQ0Y7QUFFQSw2QkFBSSx5QkFBTyxRQUFRLHdDQUF3QyxZQUFZLEVBQUU7QUFFekUsTUFBSTtBQUNGLFVBQU0sS0FBSyxrQkFBa0IsSUFBSztBQUdsQyxVQUFNLEtBQUssS0FBSyw0Q0FBNEM7QUFBQSxNQUMxRCxXQUFXO0FBQUEsSUFDYixDQUFDO0FBR0QsVUFBTSxLQUFLLGVBQWUsR0FBSTtBQUc5QixVQUFNLG9CQUFvQjtBQUFBLE1BQ3hCO0FBQUEsTUFDQTtBQUFBLE1BQ0E7QUFBQSxNQUNBO0FBQUEsTUFDQTtBQUFBLE1BQ0E7QUFBQSxNQUNBO0FBQUEsTUFDQTtBQUFBLE1BQ0E7QUFBQSxJQUNGO0FBRUEsUUFBSSxjQUFjO0FBQ2xCLGVBQVcsWUFBWSxtQkFBbUI7QUFDeEMsVUFBSTtBQUNGLGNBQU0sS0FBSyxnQkFBZ0IsVUFBVSxFQUFFLFNBQVMsSUFBSyxDQUFDO0FBQ3RELHNCQUFjLEtBQUssUUFBUSxRQUFRLEVBQUUsTUFBTTtBQUMzQyxtQ0FBSSx5QkFBTyxPQUFPLHlCQUF5QixRQUFRLEVBQUU7QUFDckQ7QUFBQSxNQUNGLFFBQVE7QUFBQSxNQUVSO0FBQUEsSUFDRjtBQUVBLFFBQUksQ0FBQyxhQUFhO0FBQ2hCLGlDQUFJLHlCQUFPLFFBQVEsNERBQTREO0FBQy9FO0FBQUEsSUFDRjtBQUdBLFVBQU0sWUFBWSxNQUFNO0FBQ3hCLFVBQU0sS0FBSyxlQUFlLEdBQUk7QUFHOUIsVUFBTSxpQkFBaUI7QUFBQSxNQUNyQjtBQUFBLE1BQ0E7QUFBQSxNQUNBO0FBQUEsTUFDQTtBQUFBLE1BQ0E7QUFBQSxNQUNBO0FBQUEsTUFDQTtBQUFBLE1BQ0E7QUFBQSxJQUNGO0FBRUEsUUFBSSxnQkFBZ0I7QUFDcEIsZUFBVyxZQUFZLGdCQUFnQjtBQUNyQyxVQUFJO0FBQ0YsY0FBTSxLQUFLLGdCQUFnQixVQUFVLEVBQUUsU0FBUyxJQUFLLENBQUM7QUFDdEQsd0JBQWdCLEtBQUssUUFBUSxRQUFRLEVBQUUsTUFBTTtBQUM3QyxtQ0FBSSx5QkFBTyxPQUFPLHlCQUF5QixRQUFRLEVBQUU7QUFDckQ7QUFBQSxNQUNGLFFBQVE7QUFBQSxNQUVSO0FBQUEsSUFDRjtBQUVBLFFBQUksQ0FBQyxlQUFlO0FBQ2xCLGlDQUFJLHlCQUFPLFFBQVEsNERBQTREO0FBQy9FO0FBQUEsSUFDRjtBQUdBLFVBQU0sY0FBYyxNQUFNO0FBQzFCLFVBQU0sY0FBYyxLQUFLLFlBQVk7QUFDckMsVUFBTSxLQUFLLGVBQWUsR0FBSTtBQUc5QixRQUFJO0FBRUYsWUFBTSxzQkFBc0I7QUFBQSxRQUMxQjtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxNQUNGO0FBRUEsVUFBSSxrQkFBa0I7QUFDdEIsaUJBQVcsWUFBWSxxQkFBcUI7QUFDMUMsWUFBSTtBQUNGLGdCQUFNLEtBQUssZ0JBQWdCLFVBQVUsRUFBRSxTQUFTLElBQUssQ0FBQztBQUN0RCxnQkFBTSxLQUFLLFFBQVEsUUFBUSxFQUFFLE1BQU07QUFDbkMsNEJBQWtCO0FBQ2xCLHFDQUFJLHlCQUFPLE9BQU8sOEJBQThCLFFBQVEsRUFBRTtBQUMxRDtBQUFBLFFBQ0YsUUFBUTtBQUFBLFFBRVI7QUFBQSxNQUNGO0FBRUEsVUFBSSxDQUFDLGlCQUFpQjtBQUVwQixjQUFNLEtBQUssU0FBUyxNQUFNLFdBQVc7QUFDckMsY0FBTSxLQUFLLGVBQWUsR0FBRztBQUM3QixjQUFNLEtBQUssU0FBUyxNQUFNLE9BQU87QUFDakMsbUNBQUkseUJBQU8sUUFBUSw2Q0FBNkM7QUFBQSxNQUNsRTtBQUVBLFlBQU0sS0FBSyxlQUFlLEdBQUk7QUFHOUIsWUFBTSxnQkFBZ0I7QUFBQSxRQUNwQjtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLE1BQ0Y7QUFFQSxpQkFBVyxZQUFZLGVBQWU7QUFDcEMsWUFBSTtBQUNGLGdCQUFNLEtBQUssZ0JBQWdCLFVBQVUsRUFBRSxTQUFTLElBQUssQ0FBQztBQUN0RCxnQkFBTSxLQUFLLFFBQVEsUUFBUSxFQUFFLE1BQU07QUFDbkMscUNBQUkseUJBQU8sT0FBTyx3QkFBd0IsUUFBUSxFQUFFO0FBQ3BEO0FBQUEsUUFDRixRQUFRO0FBQUEsUUFFUjtBQUFBLE1BQ0Y7QUFFQSxZQUFNLEtBQUssZUFBZSxHQUFJO0FBQzlCLGlDQUFJLHlCQUFPLE9BQU8scUNBQXFDLFlBQVksRUFBRTtBQUFBLElBRXZFLFNBQVMsT0FBTztBQUNkLGlDQUFJLHlCQUFPLFFBQVEsZ0VBQWdFLEtBQUssRUFBRTtBQUFBLElBQzVGO0FBQUEsRUFFRixTQUFTLE9BQU87QUFDZCxvQ0FBUyxvQ0FBb0MsS0FBSyxFQUFFO0FBQ3BELCtCQUFJLHlCQUFPLFFBQVEsa0NBQWtDO0FBQUEsRUFDdkQ7QUFDRjtBQU1PLFNBQVMsMkJBQ2QsU0FDQSxZQUNxQjtBQUNyQixRQUFNLElBQVksYUFBSyxPQUFPO0FBSTlCLE1BQUksZUFBZSxFQUFFLE9BQU8sRUFBRSxLQUFLLElBQUksRUFBRSxPQUFPLENBQUMsR0FBR0MsYUFBWTtBQUM5RCxRQUFJLEVBQUVBLFFBQU8sRUFBRSxLQUFLLElBQUksR0FBRyxTQUFTLFFBQVEsR0FBRztBQUM3QyxhQUFPO0FBQUEsSUFDVCxNQUFPLFFBQU87QUFBQSxFQUNoQixDQUFDO0FBRUQsTUFBSSxVQUFtQjtBQUFBO0FBQUE7QUFBQTtBQUFBLElBS3JCLElBQUksYUFBYSxLQUFLLElBQUksR0FBRyxRQUFRLE9BQU8sRUFBRTtBQUFBO0FBQUEsSUFHOUMsWUFBWTtBQUFBO0FBQUE7QUFBQSxJQUdaLFVBQVU7QUFBQTtBQUFBO0FBQUEsSUFHVixhQUFhLG9CQUFJLEtBQUs7QUFBQSxJQUN0QixhQUFhLG9CQUFJLEtBQUs7QUFBQTtBQUFBLElBR3RCLE1BQU07QUFBQSxJQUNOLGNBQWMsQ0FBQztBQUFBLElBQ2YsY0FBYztBQUFBLEVBQ2hCO0FBS0EsTUFBSSxpQkFBaUIsYUFBYSxLQUFLLEVBQUUsS0FBSztBQUc5QyxtQkFBaUIsZUFDZCxZQUFZLEVBQ1osUUFBUSxNQUFNLEdBQUcsRUFDakIsUUFBUSxlQUFlLEVBQUUsRUFDekIsUUFBUSxtQkFBbUIsRUFBRSxFQUM3QixLQUFLO0FBS1IsTUFBSSxlQUNGLGVBQWUsTUFBTSw4REFBOEQ7QUFFckYsTUFBSSxDQUFDLGNBQWM7QUFFakIsWUFBUSxXQUFPLCtCQUFZLGNBQWM7QUFHekMsWUFBUSxPQUFPO0FBQUEsRUFDakIsT0FBTztBQUVMLFFBQUkscUJBQXFCLGVBQWUsUUFBUSxhQUFhLENBQUMsQ0FBQztBQUcvRCxZQUFRLFdBQU8sK0JBQVksZUFBZSxNQUFNLEdBQUcsa0JBQWtCLENBQUMsRUFBRSxLQUFLO0FBRzdFLFFBQUksY0FBYyxlQUFlLE1BQU0sa0JBQWtCLEVBQUUsS0FBSztBQUNoRSxRQUFJLFlBQVksTUFBTSxRQUFRLEdBQUc7QUFFL0Isb0JBQWMsWUFBWSxRQUFRLEtBQUssR0FBRztBQUFBLElBQzVDO0FBQ0EsZ0JBQVksUUFBUSxRQUFRLE1BQU07QUFDbEMsWUFBUSxPQUFPO0FBQUEsRUFDakI7QUFPQSxRQUFNLGVBQXVCLEVBQUUsT0FBTyxFQUNuQyxLQUFLLHlCQUF5QixFQUM5QixLQUFLLEVBQ0wsS0FBSztBQUNSLE1BQUksYUFBcUIsRUFBRSxPQUFPLEVBQy9CLEtBQUssMkJBQTJCLEVBQ2hDLEtBQUssRUFDTCxLQUFLO0FBQ1IsTUFBSSxXQUFXLFNBQVMsSUFBSSxFQUFHLFNBQVEsT0FBTztBQUM5QyxlQUFhLFdBQVcsUUFBUSxPQUFPLEVBQUU7QUFDekMsVUFBUSxlQUFlLE9BQU8sZUFBZSxNQUFNLFVBQVU7QUFHN0QsUUFBTSxRQUFRLG9CQUFJLEtBQUs7QUFDdkIsUUFBTSxXQUFXLENBQUM7QUFDbEIsUUFBTSxXQUFXLENBQUM7QUFHbEIsUUFBTSxtQkFBK0I7QUFBQSxJQUNuQyxNQUFNO0FBQUEsSUFDTixPQUFPLFFBQVE7QUFBQSxFQUNqQjtBQUNBLFVBQVEsZUFBZSxDQUFDLGdCQUFnQjtBQUt4QyxRQUFNLGVBQWUsRUFBRSxPQUFPLEVBQUUsS0FBSyxlQUFlLEVBQUUsS0FBSyxFQUFFLEtBQUs7QUFFbEUsTUFBSSxjQUFjO0FBRWhCLFVBQU0sa0JBQWtCLGFBQWEsTUFBTSxHQUFHLEVBQUUsQ0FBQyxFQUFFLFFBQVEsS0FBSyxFQUFFLEVBQUUsS0FBSztBQUN6RSxRQUFJLFlBQVksT0FBTyxXQUFXLGVBQWU7QUFHakQsVUFBTSxnQkFBZ0IsYUFBYSxNQUFNLEdBQUcsRUFBRSxDQUFDLEVBQUUsS0FBSztBQUd0RCxRQUFJLFNBQVMsT0FBTyxTQUFTLGNBQWMsTUFBTSxNQUFNLElBQUksQ0FBQyxLQUFLLEVBQUU7QUFHbkUsUUFBSSxPQUFPLGNBQWMsTUFBTSxNQUFNLElBQUksQ0FBQyxLQUFLO0FBRy9DLFFBQUksaUJBQWlCLFFBQVE7QUFDM0IsZUFBUyxTQUFTO0FBQ2xCLGtCQUFZLFlBQVk7QUFDeEIsYUFBTztBQUFBLElBQ1QsV0FDUyxpQkFBaUIsU0FBUztBQUNqQyxlQUFTLFNBQVM7QUFDbEIsa0JBQVksWUFBWTtBQUN4QixhQUFPO0FBQUEsSUFDVDtBQUdBLFdBQU8sS0FBSyxRQUFRLE9BQU8sSUFBSTtBQUMvQixXQUFPLEtBQUssUUFBUSxNQUFNLEdBQUc7QUFHN0IsWUFBUSxZQUFZO0FBQ3BCLFlBQVEsV0FBVztBQUFBLEVBQ3JCO0FBS0EsNENBQWlCLFFBQVEsQ0FBQyxhQUFhO0FBRXJDLFFBQUksU0FBUyxPQUFPLFFBQVEsSUFBSTtBQUU5QixVQUFJLFNBQVMsU0FBUyxRQUFXO0FBQy9CLGdCQUFRLE9BQU8sU0FBUztBQUFBLE1BQzFCO0FBR0EsVUFBSSxTQUFTLGFBQWEsUUFBVztBQUNuQyxnQkFBUSxXQUFXLENBQUMsU0FBUyxRQUFRO0FBQUEsTUFDdkM7QUFBQSxJQUNGO0FBQUEsRUFDRixDQUFDO0FBS0QsTUFBSSxnQkFBZ0IsT0FBTyxFQUFHLFFBQU87QUFBQSxPQUNoQztBQUNILFFBQUk7QUFDRjtBQUFBLFFBQ0UsdUJBQXVCLFFBQVEsR0FBRyxTQUFTLENBQUMsQ0FBQyxNQUFNLFFBQVEsSUFBSSxPQUMzRCxRQUFRLFlBQVk7QUFBQSxNQUMxQjtBQUFBLElBQ0YsUUFBUTtBQUNOLHNDQUFTLG9DQUFvQztBQUFBLElBQy9DO0FBQ0EsV0FBTztBQUFBLEVBQ1Q7QUFDRjtBQU1BLFNBQVMsZ0JBQWdCLFNBQTJCO0FBQ2xELE1BQUk7QUFDRixRQUFJLFFBQVEsS0FBSyxNQUFNLFNBQVMsRUFBRyxRQUFPO0FBQzFDLFFBQUksUUFBUSxLQUFLLFNBQVMsS0FBSyxRQUFRLEtBQUssU0FBUyxJQUFLLFFBQU87QUFDakUsUUFBSSxRQUFRLEdBQUcsU0FBUyxLQUFLLFFBQVEsR0FBRyxTQUFTLEdBQUksUUFBTztBQUM1RCxRQUNFLFFBQVEsZ0JBQWdCLEtBQ3hCLFFBQVEsaUJBQWlCLFFBQ3pCLFFBQVEsaUJBQWlCLFVBQ3pCLE9BQU8sTUFBTSxRQUFRLFlBQVksS0FDakMsUUFBUSxlQUFlLEtBQ3ZCO0FBQ0EsYUFBTztBQUFBLElBQ1Q7QUFDQSxXQUFPO0FBQUEsRUFDVCxTQUFTLE9BQU87QUFDZCxXQUFPO0FBQUEsRUFDVDtBQUNGO0FBcUJPLFNBQVMsc0JBQ2QsTUFDOEI7QUFDOUIsTUFBSSxxQkFBcUMsRUFBRSxLQUFLLElBQUksWUFBWSxDQUFDLEVBQUU7QUFDbkUsTUFBSSxhQUErQixDQUFDO0FBQ3BDLE1BQUksaUJBQWlCO0FBR3JCLE1BQUksQ0FBQyxLQUFLLFNBQVMsa0JBQWtCLEdBQUc7QUFDdEMsV0FBTztBQUFBLEVBR1QsV0FBVyxLQUFLLFNBQVMsVUFBVSxHQUFHO0FBQ3BDLGVBQVcsS0FBSyxFQUFFLEtBQUssTUFBTSxZQUFZLENBQUMsRUFBRSxDQUFDO0FBQUEsRUFHL0MsT0FBTztBQUVMLFNBQUssTUFBTSxHQUFHLEVBQUUsUUFBUSxDQUFDLFlBQVk7QUFHbkMsVUFBSSxRQUFRLFNBQVMsa0JBQWtCLEdBQUc7QUFDeEMsMkJBQW1CLE1BQU07QUFHekIsWUFBSSxDQUFDLG1CQUFtQixJQUFJLFdBQVcsTUFBTTtBQUMzQyw2QkFBbUIsTUFBTSxhQUFhO0FBR3hDLFlBQUksUUFBUSxTQUFTLEdBQUcsR0FBRztBQUV6Qiw2QkFBbUIsTUFBTSxLQUFLLFVBQVUsR0FBRyxLQUFLLFFBQVEsR0FBRyxDQUFDO0FBQUEsUUFDOUQ7QUFFQSwyQkFBbUIsT0FBTztBQUFBLE1BRzVCLFdBQVcsUUFBUSxXQUFXLGFBQWEsR0FBRztBQUM1QyxZQUFJLGtCQUFrQixDQUFDLFFBQVEsUUFBUSxlQUFlLEVBQUUsQ0FBQztBQUN6RCxZQUFJLFFBQVEsU0FBUyxHQUFHO0FBQ3RCLDRCQUFrQixRQUFRLFFBQVEsZUFBZSxFQUFFLEVBQUUsTUFBTSxHQUFHO0FBQ2hFLDJCQUFtQixhQUFhO0FBQUEsTUFHbEMsV0FBVyxRQUFRLFdBQVcsUUFBUSxHQUFHO0FBQ3ZDLHlCQUFpQixPQUFPLFNBQVMsUUFBUSxNQUFNLEdBQUcsRUFBRSxDQUFDLENBQUM7QUFBQSxNQUN4RDtBQUdBLFVBQUksbUJBQW1CLFdBQVcsV0FBVyxHQUFHO0FBRTlDLGNBQU0sVUFBVSxtQkFBbUIsSUFBSSxNQUFNLEdBQUcsRUFBRSxDQUFDO0FBQ25ELFlBQUksZ0JBQWdCLFFBQVEsTUFBTSxHQUFHO0FBR3JDLDJCQUFtQixhQUFhLENBQUMsY0FBYyxjQUFjLFNBQVMsQ0FBQyxDQUFDO0FBQUEsTUFDMUU7QUFBQSxJQUNGLENBQUM7QUFHRCxhQUFTLElBQUksR0FBRyxLQUFLLGdCQUFnQixLQUFLO0FBQ3hDLFVBQUksV0FBVztBQUFBLFFBQ2IsS0FBSyxtQkFBbUIsSUFBSSxRQUFRLFVBQVUsVUFBVSxDQUFDO0FBQUEsUUFDekQsWUFBWSxtQkFBbUI7QUFBQSxNQUNqQztBQUNBLGlCQUFXLEtBQUssUUFBUTtBQUFBLElBQzFCO0FBQUEsRUFDRjtBQUVBLFNBQU87QUFDVDtBQU1BLGVBQWUsNEJBQTRCO0FBQ3pDLE1BQUksaUJBQWlCLENBQUMsU0FBUyxTQUFTLE1BQU07QUFDOUMsTUFBSSxnQkFBZ0I7QUFBQSxJQUNsQjtBQUFBLElBQ0E7QUFBQSxJQUNBO0FBQUEsSUFDQTtBQUFBLElBQ0E7QUFBQSxJQUNBO0FBQUEsSUFDQTtBQUFBLElBQ0E7QUFBQSxJQUNBO0FBQUEsSUFDQTtBQUFBLElBQ0E7QUFBQSxJQUNBO0FBQUEsSUFDQTtBQUFBLEVBQ0Y7QUFHQSxRQUFNLEtBQUssTUFBTSxRQUFRLE9BQU8sVUFBVTtBQUN4QyxVQUFNLE1BQU0sTUFBTSxRQUFRO0FBQzFCLFFBQUkscUJBQXFCO0FBR3pCLGtCQUFjLFFBQVEsQ0FBQyxnQkFBZ0I7QUFDckMsVUFBSSxJQUFJLElBQUksRUFBRSxTQUFTLFdBQVcsRUFBRyxzQkFBcUI7QUFBQSxJQUM1RCxDQUFDO0FBRUQsbUJBQWUsUUFBUSxDQUFDLGlCQUFpQjtBQUN2QyxVQUFJLElBQUksYUFBYSxNQUFNLGFBQWMsc0JBQXFCO0FBQUEsSUFDaEUsQ0FBQztBQUVELFFBQUksb0JBQW9CO0FBRXRCLFlBQU0sTUFBTSxNQUFNO0FBQUEsSUFDcEIsT0FBTztBQUVMLFlBQU0sTUFBTSxTQUFTO0FBQUEsSUFDdkI7QUFBQSxFQUNGLENBQUM7QUFFRDtBQUNGOyIsCiAgIm5hbWVzIjogWyJsb2ciLCAibG9nRXJyb3IiLCAibG9nUHJvZHVjdFJvdyIsICJsb2dUYWJsZUhlYWRlciIsICJyZWFkTGluZXNGcm9tVGV4dEZpbGUiLCAiZ2V0VGltZUVsYXBzZWRTaW5jZSIsICJ0b1RpdGxlQ2FzZSIsICJjb2xvdXIiLCAic3RhcnRUaW1lIiwgImRvdGVudiIsICJpbXBvcnRfdXRpbGl0aWVzIiwgIk1vbmdvQ2xpZW50IiwgIk9iamVjdElkIiwgImRvdGVudiIsICJjbGllbnQiLCAiZGIiLCAiY29uc29saWRhdGVkUHJvZHVjdHNDb2xsZWN0aW9uIiwgImJyYW5kc0NvbGxlY3Rpb24iLCAiY2F0ZWdvcnlIaWVyYXJjaHlDb2xsZWN0aW9uIiwgIm5vcm1hbGl6ZVByb2R1Y3ROYW1lIiwgImltcG9ydF91dGlsaXRpZXMiLCAiY2F0ZWdvcmlzZWRVcmxzIiwgImVsZW1lbnQiXQp9Cg==

	