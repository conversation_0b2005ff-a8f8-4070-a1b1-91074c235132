
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/png" sizes="32x32" href="./icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="./icon-16x16.png">
    <link rel="manifest" href="./manifest.webmanifest">
    <title>Playwright Trace Viewer</title>
    <script type="module" crossorigin src="./index.0ab19804.js"></script>
    <link rel="stylesheet" href="./index.dbbacc35.css">
  </head>
  <body>
    <div id="root"></div>
    
    <dialog id="fallback-error">
      <p>The Playwright Trace Viewer must be loaded over the <code>http://</code> or <code>https://</code> protocols.</p>
      <p>For more information, please see the <a href="https://aka.ms/playwright/trace-viewer-file-protocol">docs</a>.</p>
    </dialog>
    <script>
      if (!/^https?:/.test(window.location.protocol))
        document.getElementById("fallback-error").show();
    </script>
  </body>
</html>
