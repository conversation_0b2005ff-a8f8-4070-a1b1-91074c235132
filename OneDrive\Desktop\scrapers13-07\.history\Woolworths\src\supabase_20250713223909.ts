import { createClient, SupabaseClient } from "@supabase/supabase-js";
import * as dotenv from "dotenv";
dotenv.config();
dotenv.config({ path: `.env.local`, override: true });

import { Product, UpsertResponse, ProductResponse, DatedPrice } from "./typings.js";
import { colour, log, logError } from "./utilities.js";

let supabase: SupabaseClient;
let storeId: number | undefined; // Woolworths store row id

export function establishSupabase() {
  const url = process.env.SUPABASE_URL;
  const key = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;
  if (!url || !key) {
    throw Error("SUPABASE_URL or SUPABASE_ANON_KEY not set in env");
  }
  supabase = createClient(url, key, { auth: { persistSession: false } });
}

async function ensureStoreRow(): Promise<number> {
  if (storeId !== undefined) return storeId;
  const { data, error } = await supabase
    .from("stores")
    .select("id")
    .eq("name", "woolworths")
    .single();

  if (error && error.code !== "PGRST116") {
    throw error;
  }

  if (data?.id) {
    storeId = data.id;
    return storeId!;
  }

  // Insert store row if not exists
  const { data: insertData, error: insertErr } = await supabase
    .from("stores")
    .insert({ name: "woolworths" })
    .select("id")
    .single();
  if (insertErr) throw insertErr;
  storeId = insertData!.id;
  return storeId!;
}

export async function upsertProductToSupabase(scraped: Product): Promise<UpsertResponse> {
  if (!supabase) throw Error("Supabase client not initialised");
  const sId = await ensureStoreRow();

  // Upsert into products
  const productPayload: any = {
    id: scraped.id,
    name: scraped.name,
    size: scraped.size,
    unit_price: scraped.unitPrice,
    unit_name: scraped.unitName,
    original_unit_qty: scraped.originalUnitQuantity,
    source_site: scraped.sourceSite,
    last_checked: scraped.lastChecked.toISOString(),
    last_updated: scraped.lastUpdated.toISOString()
  };

  const { error: prodErr } = await supabase.from("products").upsert(productPayload, { onConflict: "id" });
  if (prodErr) {
    logError("Supabase upsert product failed: " + prodErr.message);
    return UpsertResponse.Failed;
  }

  // Insert price row (always) – logic to omit duplicates can be added later
  const pricePayload = {
    product_id: scraped.id,
    store_id: sId,
    price: scraped.currentPrice,
    recorded_at: scraped.lastUpdated.toISOString()
  };
  const { error: priceErr } = await supabase.from("prices").insert(pricePayload);
  if (priceErr) {
    logError("Supabase insert price failed: " + priceErr.message);
    return UpsertResponse.Failed;
  }

  log(colour.green, `  Upserted: ${scraped.name.slice(0, 45).padEnd(45)} | $${scraped.currentPrice}`);
  return UpsertResponse.PriceChanged; // coarse; can refine later
} 