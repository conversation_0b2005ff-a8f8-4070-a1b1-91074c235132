# PowerShell version of .NET fix script
Write-Host "🔧 .NET Scraper Fix Script" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan

Write-Host ""
Write-Host "🔍 Step 1: Clearing NuGet caches" -ForegroundColor Yellow
dotnet nuget locals all --clear

Write-Host ""
Write-Host "🔍 Step 2: Listing current package sources" -ForegroundColor Yellow
dotnet nuget list source

Write-Host ""
Write-Host "🔍 Step 3: Removing problematic local sources" -ForegroundColor Yellow
try {
    dotnet nuget remove source "Microsoft Visual Studio Offline Packages" 2>$null
    dotnet nuget remove source "Microsoft SDKs" 2>$null
    dotnet nuget remove source "C:\Program Files (x86)\Microsoft SDKs\NuGetPackages\" 2>$null
} catch {
    # Ignore errors for non-existent sources
}

Write-Host ""
Write-Host "🔍 Step 4: Ensuring nuget.org source is available" -ForegroundColor Yellow
try {
    dotnet nuget add source https://api.nuget.org/v3/index.json -n nuget.org 2>$null
} catch {
    # Source might already exist
}

Write-Host ""
Write-Host "🔍 Step 5: Testing PakNSave scraper" -ForegroundColor Yellow
Set-Location "paknsave\src"
Write-Host "Current directory: $(Get-Location)"

Write-Host "  - Running dotnet restore..." -ForegroundColor Gray
$restoreResult = dotnet restore
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ PakNSave restore failed" -ForegroundColor Red
} else {
    Write-Host "✅ PakNSave restore successful" -ForegroundColor Green
    
    Write-Host "  - Running dotnet build..." -ForegroundColor Gray
    $buildResult = dotnet build
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ PakNSave build failed" -ForegroundColor Red
    } else {
        Write-Host "✅ PakNSave build successful" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "🔍 Step 6: Testing New World scraper" -ForegroundColor Yellow
Set-Location "..\..\new-world\src"
Write-Host "Current directory: $(Get-Location)"

Write-Host "  - Running dotnet restore..." -ForegroundColor Gray
$restoreResult = dotnet restore
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ New World restore failed" -ForegroundColor Red
} else {
    Write-Host "✅ New World restore successful" -ForegroundColor Green
    
    Write-Host "  - Running dotnet build..." -ForegroundColor Gray
    $buildResult = dotnet build
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ New World build failed" -ForegroundColor Red
    } else {
        Write-Host "✅ New World build successful" -ForegroundColor Green
    }
}

Set-Location "..\..\"

Write-Host ""
Write-Host "🎉 .NET fix script completed!" -ForegroundColor Green
Write-Host ""
Write-Host "📝 If issues persist, try:" -ForegroundColor Yellow
Write-Host "  1. Restart Visual Studio / VS Code"
Write-Host "  2. Run: dotnet --info"
Write-Host "  3. Check Windows firewall/antivirus blocking NuGet"
Write-Host "  4. Run as Administrator"
Write-Host ""
Read-Host "Press Enter to continue..."