#!/bin/bash
echo "Starting MongoDB for the scraper system..."
echo

# Check if MongoDB is installed
if ! command -v mongod &> /dev/null; then
    echo "MongoDB is not installed."
    echo
    echo "Install options:"
    echo "1. Using Docker (recommended):"
    echo "   docker run -d -p 27017:27017 --name mongodb mongo:latest"
    echo
    echo "2. Install MongoDB Community Server:"
    echo "   Ubuntu/Debian: sudo apt-get install -y mongodb-org"
    echo "   macOS: brew install mongodb/brew/mongodb-community"
    echo
    exit 1
fi

# Create data directory if it doesn't exist
sudo mkdir -p /data/db
sudo chown -R $(whoami) /data/db

# Start MongoDB
echo "Starting MongoDB on port 27017..."
mongod --dbpath /data/db --port 27017 --fork --logpath /tmp/mongodb.log

echo
echo "MongoDB should now be running on localhost:27017"
echo "You can now run the scrapers with:"
echo "  - Woolworths: cd Woolworths && npm run db"
echo "  - New World: cd new-world/src && dotnet run db"  
echo "  - PakNSave: cd paknsave/src && dotnet run db"
echo