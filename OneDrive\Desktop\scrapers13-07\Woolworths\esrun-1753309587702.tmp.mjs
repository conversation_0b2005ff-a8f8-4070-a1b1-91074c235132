process.argv = [process.argv[0], ...process.argv.slice(3)];

		import __esrun_url from 'url';

		import { createRequire as __esrun_createRequire } from "module";

		const __esrun_fileUrl = __esrun_url.pathToFileURL("C:\Users\<USER>\OneDrive\Desktop\scrapers13-07\Woolworths\esrun-1753309587702.tmp.mjs");

		const require = __esrun_createRequire(__esrun_fileUrl);
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __require = /* @__PURE__ */ ((x) => typeof require !== "undefined" ? require : typeof Proxy !== "undefined" ? new Proxy(x, {
  get: (a, b) => (typeof require !== "undefined" ? require : a)[b]
}) : x)(function(x) {
  if (typeof require !== "undefined") return require.apply(this, arguments);
  throw Error('Dynamic require of "' + x + '" is not supported');
});
var __commonJS = (cb, mod) => function __require2() {
  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));

// src/typings.js
var require_typings = __commonJS({
  "src/typings.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// src/utilities.js
var require_utilities = __commonJS({
  "src/utilities.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.validCategories = exports.colour = void 0;
    exports.log = log4;
    exports.logError = logError4;
    exports.logProductRow = logProductRow2;
    exports.logTableHeader = logTableHeader2;
    exports.readLinesFromTextFile = readLinesFromTextFile2;
    exports.getTimeElapsedSince = getTimeElapsedSince2;
    exports.toTitleCase = toTitleCase2;
    var fs_1 = __require("fs");
    var tableIDWidth = 6;
    var tableNameWidth = 60;
    var tableSizeWidth = 17;
    exports.colour = {
      red: "\x1B[31m",
      green: "\x1B[32m",
      yellow: "\x1B[33m",
      blue: "\x1B[38;5;117m",
      magenta: "\x1B[35m",
      cyan: "\x1B[36m",
      white: "\x1B[37m",
      crimson: "\x1B[38m",
      grey: "\x1B[90m",
      orange: "\x1B[38;5;214m",
      sky: "\x1B[38;5;153m"
    };
    function log4(colour4, text) {
      const clear = "\x1B[0m";
      console.log(`${colour4}%s${clear}`, text);
    }
    function logError4(text) {
      log4(exports.colour.red, text);
    }
    function logProductRow2(product) {
      var _a;
      const unitPriceString = product.unitPrice ? `$${product.unitPrice.toFixed(2)} /${product.unitName}` : ``;
      log4(getAlternatingRowColour(exports.colour.sky, exports.colour.white), `${product.id.padStart(tableIDWidth)} | ${product.name.slice(0, tableNameWidth).padEnd(tableNameWidth)} | ${(_a = product.size) === null || _a === void 0 ? void 0 : _a.slice(0, tableSizeWidth).padEnd(tableSizeWidth)} | $ ${product.currentPrice.toFixed(2).padStart(4).padEnd(5)} | ` + unitPriceString);
    }
    function logTableHeader2() {
      log4(exports.colour.yellow, `${"ID".padStart(tableIDWidth)} | ${"Name".padEnd(tableNameWidth)} | ${"Size".padEnd(tableSizeWidth)} | ${"Price".padEnd(7)} | Unit Price`);
      let headerLine = "";
      for (let i = 0; i < 111; i++) {
        headerLine += "-";
      }
      log4(exports.colour.yellow, headerLine);
    }
    var alternatingRowColour = false;
    function getAlternatingRowColour(colourA, colourB) {
      alternatingRowColour = alternatingRowColour ? false : true;
      return alternatingRowColour ? colourA : colourB;
    }
    function readLinesFromTextFile2(filename) {
      try {
        const file = (0, fs_1.readFileSync)(filename, "utf-8");
        const result = file.split(/\r?\n/).filter((line) => {
          if (line.trim().length > 0)
            return true;
          else
            return false;
        });
        return result;
      } catch (error) {
        throw "Error reading " + filename;
      }
    }
    function getTimeElapsedSince2(startTime2) {
      let elapsedTimeSeconds = (Date.now() - startTime2) / 1e3;
      let elapsedTimeString = Math.floor(elapsedTimeSeconds).toString();
      if (elapsedTimeSeconds >= 60) {
        return Math.floor(elapsedTimeSeconds / 60) + ":" + Math.floor(elapsedTimeSeconds % 60).toString().padStart(2, "0");
      } else
        return elapsedTimeString + "s";
    }
    exports.validCategories = [
      // freshCategory
      "eggs",
      "fruit",
      "fresh-vegetables",
      "salads-coleslaw",
      "bread",
      "bread-rolls",
      "specialty-bread",
      "bakery-cakes",
      "bakery-desserts",
      // chilledCategory
      "milk",
      "long-life-milk",
      "sour-cream",
      "cream",
      "yoghurt",
      "butter",
      "cheese",
      "cheese-slices",
      "salami",
      "other-deli-foods",
      // meatCategory
      "beef-lamb",
      "chicken",
      "ham",
      "bacon",
      "pork",
      "patties-meatballs",
      "sausages",
      "deli-meats",
      "meat-alternatives",
      "seafood",
      "salmon",
      // frozenCategory
      "ice-cream",
      "ice-blocks",
      "pastries-cheesecake",
      "frozen-chips",
      "frozen-vegetables",
      "frozen-fruit",
      "frozen-seafood",
      "pies-sausage-rolls",
      "pizza",
      "other-savouries",
      // pantryCategory
      "rice",
      "noodles",
      "pasta",
      "beans-spaghetti",
      "canned-fish",
      "canned-meat",
      "soup",
      "cereal",
      "spreads",
      "baking",
      "sauces",
      "oils-vinegars",
      "world-foods",
      // snacksCategory
      "chocolate",
      "boxed-chocolate",
      "chips",
      "crackers",
      "biscuits",
      "muesli-bars",
      "nuts-bulk-mix",
      "sweets-lollies",
      "other-snacks",
      // drinksCategory
      "black-tea",
      "green-tea",
      "herbal-tea",
      "drinking-chocolate",
      "coffee",
      "soft-drinks",
      "energy-drinks",
      "juice",
      // petsCategory
      "cat-food",
      "cat-treats",
      "dog-food",
      "dog-treats"
    ];
    function toTitleCase2(str) {
      return str.replace(/\w\S*/g, function(txt) {
        return txt.charAt(0).toUpperCase() + txt.substring(1).toLowerCase();
      });
    }
  }
});

// src/product-overrides.js
var require_product_overrides = __commonJS({
  "src/product-overrides.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.productOverrides = void 0;
    exports.productOverrides = [
      { id: "206889", size: "180g" },
      { id: "196996", size: "300g" },
      { id: "137967", size: "420g" },
      { id: "125856", size: "450g" },
      { id: "189268", size: "1.13kg" },
      { id: "189150", size: "1.2kg" },
      { id: "190454", size: "2.1kg" },
      { id: "189078", size: "1.3kg" },
      { id: "189136", size: "1.2kg" },
      { id: "755237", size: "931g" },
      { id: "755304", size: "1.1kg" },
      { id: "755246", size: "1020g" },
      { id: "755245", size: "1.2kg" },
      { id: "112273", size: "865ml" },
      { id: "269514", size: "584ml" },
      { id: "269515", size: "584ml" },
      { id: "116518", size: "440ml" },
      { id: "151191", size: "570ml" },
      { id: "279904", size: "575ml" },
      { id: "146149", size: "1000ml" },
      { id: "791925", size: "525g" },
      { id: "774216", size: "525g" },
      { id: "784406", size: "525g" },
      { id: "791916", size: "525g" },
      { id: "306624", size: "185g" },
      { id: "156824", size: "180g" },
      { id: "9023", size: "375g" },
      { id: "266962", category: "sweets-lollies" },
      { id: "171524", size: "230ml", category: "baking" },
      { id: "170021", category: "ice-blocks" },
      { id: "71164", category: "sausages" },
      { id: "71174", category: "sausages" },
      { id: "71168", category: "sausages" },
      { id: "71165", category: "sausages" },
      { id: "331560", category: "specialty-bread" },
      { id: "679412", category: "herbal-tea" },
      { id: "790129", category: "herbal-tea" },
      { id: "267492", category: "herbal-tea" },
      { id: "267485", category: "herbal-tea" },
      { id: "413302", category: "herbal-tea" },
      { id: "267488", category: "herbal-tea" },
      { id: "760872", category: "herbal-tea" },
      { id: "681177", category: "herbal-tea" },
      { id: "95091", category: "herbal-tea" },
      { id: "761093", category: "black-tea" },
      { id: "721661", category: "green-tea" },
      { id: "790129", category: "herbal-tea" },
      { id: "267492", category: "herbal-tea" },
      { id: "267485", category: "herbal-tea" },
      { id: "721034", category: "herbal-tea" },
      { id: "413302", category: "herbal-tea" },
      { id: "267488", category: "herbal-tea" },
      { id: "760872", category: "herbal-tea" },
      { id: "681177", category: "herbal-tea" },
      { id: "95091.", category: "herbal-tea" },
      { id: "184090", category: "herbal-tea" },
      { id: "761093", category: "black-tea" },
      { id: "721661", category: "green-tea" },
      { id: "690093", category: "green-tea" },
      { id: "780922", category: "sauces" },
      { id: "780921", category: "sauces" },
      { id: "72618", category: "black-tea" },
      { id: "6053", category: "black-tea" },
      { id: "72617", category: "black-tea" },
      { id: "168068", category: "black-tea" },
      { id: "6052", category: "black-tea" },
      { id: "761436", category: "black-tea" }
    ];
  }
});

// src/index.ts
import * as dotenv3 from "./node_modules/dotenv/lib/main.js";
import playwright from "./node_modules/playwright/index.mjs";
import * as cheerio from "./node_modules/cheerio/dist/esm/index.js";
import _ from "./node_modules/lodash/lodash.js";
import { setTimeout as setTimeout2 } from "timers/promises";

// src/mongodb.ts
var import_typings = __toESM(require_typings());
var import_utilities = __toESM(require_utilities());
import { MongoClient, GridFSBucket, ObjectId } from "./node_modules/mongodb/lib/index.js";
import * as dotenv from "./node_modules/dotenv/lib/main.js";
dotenv.config();
dotenv.config({ path: `.env.local`, override: true });
var client;
var db;
var gridFS;
var storeId;
var storesCollection;
var brandsCollection;
var consolidatedProductsCollection;
var priceHistoryCollection;
var categoryHierarchyCollection;
async function establishMongoDB() {
  const connectionString = process.env.MONGODB_CONNECTION_STRING;
  const databaseName = process.env.MONGODB_DATABASE_NAME || "nz-supermarket-scraper";
  if (!connectionString) {
    throw Error("MONGODB_CONNECTION_STRING not set in env");
  }
  const maxRetries = 5;
  const retryDelay = 2e3;
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      (0, import_utilities.log)(import_utilities.colour.yellow, `\u{1F504} MongoDB connection attempt ${attempt}/${maxRetries}...`);
      client = new MongoClient(connectionString, {
        serverSelectionTimeoutMS: 1e4,
        // 10 second timeout
        connectTimeoutMS: 1e4,
        socketTimeoutMS: 0,
        maxPoolSize: 10,
        retryWrites: true,
        retryReads: true
      });
      await client.connect();
      await client.db("admin").command({ ping: 1 });
      db = client.db(databaseName);
      storesCollection = db.collection("stores");
      brandsCollection = db.collection("brands");
      consolidatedProductsCollection = db.collection("consolidatedProducts");
      priceHistoryCollection = db.collection("priceHistory");
      categoryHierarchyCollection = db.collection("categoryHierarchy");
      gridFS = new GridFSBucket(db, { bucketName: "productImages" });
      (0, import_utilities.log)(import_utilities.colour.green, "\u2705 MongoDB connection established");
      await createIndexes();
      return;
    } catch (error) {
      (0, import_utilities.logError)(`\u274C MongoDB connection attempt ${attempt} failed: ${error.message}`);
      if (client) {
        try {
          await client.close();
        } catch (closeError) {
        }
        client = null;
      }
      if (attempt === maxRetries) {
        (0, import_utilities.logError)(`\u274C MongoDB connection failed after ${maxRetries} attempts`);
        (0, import_utilities.logError)("\u{1F4A1} Make sure MongoDB is running: docker start mongodb");
        throw error;
      }
      (0, import_utilities.log)(import_utilities.colour.yellow, `\u23F3 Retrying in ${retryDelay / 1e3} seconds...`);
      await new Promise((resolve) => setTimeout(resolve, retryDelay));
    }
  }
}
async function createIndexes() {
  try {
    await consolidatedProductsCollection.createIndex({
      "displayName": "text",
      "normalizedName": "text",
      "variants.storeName": "text"
    });
    await consolidatedProductsCollection.createIndex({ "categoryId": 1 });
    await consolidatedProductsCollection.createIndex({ "brandId": 1 });
    await consolidatedProductsCollection.createIndex({
      "variants.storeProductId": 1,
      "variants.storeId": 1
    });
    await priceHistoryCollection.createIndex({
      "consolidatedProductId": 1,
      "recordedAt": -1
    });
    await priceHistoryCollection.createIndex({ "year": 1, "month": 1 });
    await categoryHierarchyCollection.createIndex({ "parentId": 1, "sortOrder": 1 });
    await categoryHierarchyCollection.createIndex({ "level": 1, "sortOrder": 1 });
    (0, import_utilities.log)(import_utilities.colour.blue, "\u2705 MongoDB indexes created");
  } catch (error) {
    (0, import_utilities.logError)(`Failed to create indexes: ${error.message}`);
  }
}
async function ensureStoreRow() {
  if (storeId !== void 0) return storeId;
  try {
    const existingStore = await storesCollection.findOne({ storeId: "woolworths" });
    if (existingStore) {
      storeId = existingStore._id;
      return storeId;
    }
    const insertResult = await storesCollection.insertOne({
      storeId: "woolworths",
      name: "Woolworths",
      logoUrl: null,
      createdAt: /* @__PURE__ */ new Date(),
      updatedAt: /* @__PURE__ */ new Date()
    });
    storeId = insertResult.insertedId;
    return storeId;
  } catch (error) {
    (0, import_utilities.logError)(`Failed to ensure store row: ${error.message}`);
    throw error;
  }
}
async function upsertProductToMongoDB(scraped) {
  if (!db) throw Error("MongoDB client not initialised");
  try {
    const sId = await ensureStoreRow();
    const now = /* @__PURE__ */ new Date();
    let consolidatedProduct = await consolidatedProductsCollection.findOne({
      "variants.storeProductId": scraped.id,
      "variants.storeId": sId
    });
    if (!consolidatedProduct) {
      const normalizedName = normalizeProductName(scraped.name, scraped.size);
      (0, import_utilities.log)(import_utilities.colour.cyan, `\u{1F50D} Processing: ${scraped.name} -> normalized: ${normalizedName}`);
      const matchResult = await findBestMatch(normalizedName, scraped.size, scraped.name);
      if (matchResult && matchResult.confidence >= 0.85 && matchResult.product) {
        consolidatedProduct = matchResult.product;
        if (matchResult.matchType !== "exact" && consolidatedProduct) {
          await addProductAlias(consolidatedProduct._id, scraped.name);
        }
        if (consolidatedProduct) {
          const newVariant = {
            storeProductId: scraped.id,
            storeId: sId,
            storeName: scraped.name,
            storeSize: scraped.size || null,
            storeUnitPrice: scraped.unitPrice || null,
            storeUnitName: scraped.unitName || null,
            lastSeen: now,
            isActive: true,
            imageUrl: null
          };
          await consolidatedProductsCollection.updateOne(
            { _id: consolidatedProduct._id },
            {
              $push: { variants: newVariant },
              $set: { updatedAt: now }
            }
          );
          (0, import_utilities.log)(import_utilities.colour.green, `\u2705 Added variant to existing product: ${consolidatedProduct.displayName} (confidence: ${matchResult.confidence.toFixed(3)}, type: ${matchResult.matchType})`);
        }
      } else {
        const newProduct = {
          normalizedName,
          displayName: scraped.name,
          primarySize: scraped.size,
          categoryId: null,
          // TODO: Implement category mapping
          brandId: null,
          // TODO: Implement brand extraction
          matchConfidence: matchResult ? Math.round(matchResult.confidence * 100) : 100,
          manualMatch: false,
          aliases: [],
          // Initialize empty aliases array
          variants: [{
            storeProductId: scraped.id,
            storeId: sId,
            storeName: scraped.name,
            storeSize: scraped.size || null,
            storeUnitPrice: scraped.unitPrice || null,
            storeUnitName: scraped.unitName || null,
            lastSeen: now,
            isActive: true,
            imageUrl: null
          }],
          sizeVariants: scraped.size ? [{
            sizeName: scraped.size,
            sizeWeightGrams: null,
            sizeVolumeMl: null,
            isPrimarySize: true
          }] : [],
          currentPrices: [{
            storeId: sId,
            price: scraped.currentPrice,
            isSpecial: false,
            wasAvailable: true,
            recordedAt: now
          }],
          createdAt: now,
          updatedAt: now
        };
        const insertResult = await consolidatedProductsCollection.insertOne(newProduct);
        consolidatedProduct = { _id: insertResult.insertedId, ...newProduct };
        if (matchResult && matchResult.confidence >= 0.7) {
          (0, import_utilities.log)(import_utilities.colour.blue, `\u{1F195} Created new product (matches below threshold): ${scraped.name} (Best match: ${matchResult.confidence.toFixed(3)})`);
        } else {
          (0, import_utilities.log)(import_utilities.colour.blue, `\u{1F195} Created new consolidated product: ${scraped.name}`);
        }
      }
    } else {
      await consolidatedProductsCollection.updateOne(
        { _id: consolidatedProduct._id },
        {
          $set: {
            "variants.$[variant].lastSeen": now,
            "variants.$[variant].storeUnitPrice": scraped.unitPrice,
            "variants.$[variant].storeUnitName": scraped.unitName,
            "currentPrices.$[price].price": scraped.currentPrice,
            "currentPrices.$[price].recordedAt": now,
            updatedAt: now
          }
        },
        {
          arrayFilters: [
            { "variant.storeProductId": scraped.id },
            { "price.storeId": sId }
          ]
        }
      );
    }
    if (consolidatedProduct) {
      await priceHistoryCollection.insertOne({
        consolidatedProductId: consolidatedProduct._id,
        storeId: sId,
        price: scraped.currentPrice,
        isSpecial: false,
        wasAvailable: true,
        recordedAt: now,
        year: now.getFullYear(),
        month: now.getMonth() + 1
      });
    }
    (0, import_utilities.log)(import_utilities.colour.green, `  Upserted: ${scraped.name.slice(0, 45).padEnd(45)} | $${scraped.currentPrice}`);
    return import_typings.UpsertResponse.PriceChanged;
  } catch (error) {
    (0, import_utilities.logError)(`MongoDB upsert failed: ${error.message}`);
    return import_typings.UpsertResponse.Failed;
  }
}
async function uploadImageToMongoDB(imageUrl, product) {
  if (!gridFS) throw Error("MongoDB GridFS not initialised");
  try {
    const sId = await ensureStoreRow();
    const imageResponse = await fetch(imageUrl);
    if (!imageResponse.ok) {
      (0, import_utilities.logError)(`Failed to download image from ${imageUrl}: ${imageResponse.statusText}`);
      return false;
    }
    const imageBuffer = Buffer.from(await imageResponse.arrayBuffer());
    const existingFiles = await gridFS.find({
      "metadata.productId": product.id
    }).toArray();
    for (const file of existingFiles) {
      await gridFS.delete(file._id);
    }
    const filename = `${product.id}.jpg`;
    const uploadStream = gridFS.openUploadStream(filename, {
      contentType: "image/jpeg",
      metadata: {
        productId: product.id,
        storeId: sId,
        originalUrl: imageUrl,
        uploadedAt: /* @__PURE__ */ new Date()
      }
    });
    await new Promise((resolve, reject) => {
      uploadStream.end(imageBuffer, () => {
        resolve(uploadStream.id);
      });
      uploadStream.on("error", (error) => {
        reject(error);
      });
    });
    const imageUrl_gridfs = `gridfs://productImages/${uploadStream.id}`;
    await consolidatedProductsCollection.updateOne(
      { "variants.storeProductId": product.id },
      {
        $set: {
          "variants.$[variant].imageUrl": imageUrl_gridfs
        }
      },
      {
        arrayFilters: [{ "variant.storeProductId": product.id }]
      }
    );
    (0, import_utilities.log)(import_utilities.colour.blue, `  Image uploaded: ${product.id} -> GridFS ${uploadStream.id}`);
    return true;
  } catch (err) {
    (0, import_utilities.logError)(`Image upload error: ${err.message}`);
    return false;
  }
}
var storeDescriptors = [
  "woolworths",
  "countdown",
  "new world",
  "paknsave",
  "pak n save",
  "select",
  "premium",
  "value",
  "budget",
  "signature",
  "essentials",
  "pams",
  "homebrand",
  "signature range",
  "fresh choice",
  "macro",
  "organic",
  "free range",
  "grass fed",
  "natural",
  "artisan",
  "gourmet",
  "deluxe",
  "finest",
  "choice",
  "quality",
  "fresh",
  "pure",
  "real",
  "authentic",
  "traditional",
  "classic"
];
var sizeNormalizations = {
  "grams": "g",
  "gram": "g",
  "kilograms": "kg",
  "kilogram": "kg",
  "kg": "kg",
  "litres": "l",
  "litre": "l",
  "ltr": "l",
  "millilitres": "ml",
  "millilitre": "ml",
  "pieces": "pc",
  "piece": "pc",
  "each": "ea",
  "pack": "pk",
  "packet": "pk"
};
function removeStoreDescriptors(productName) {
  let cleaned = productName.toLowerCase();
  for (const descriptor of storeDescriptors) {
    const regex = new RegExp(`\\b${descriptor}\\b`, "gi");
    cleaned = cleaned.replace(regex, "").trim();
  }
  return cleaned.replace(/\s+/g, " ").trim();
}
function normalizeSize(size) {
  if (!size) return "";
  let normalized = size.toLowerCase().replace(/[^\w\d]/g, "");
  for (const [original, replacement] of Object.entries(sizeNormalizations)) {
    const regex = new RegExp(original, "gi");
    normalized = normalized.replace(regex, replacement);
  }
  return normalized;
}
function calculateSizeSimilarity(size1, size2) {
  if (!size1 || !size2) return 0;
  const normalized1 = normalizeSize(size1);
  const normalized2 = normalizeSize(size2);
  if (normalized1 === normalized2) return 1;
  const num1 = parseFloat(normalized1.replace(/[^\d.]/g, ""));
  const num2 = parseFloat(normalized2.replace(/[^\d.]/g, ""));
  if (!isNaN(num1) && !isNaN(num2)) {
    const ratio = Math.min(num1, num2) / Math.max(num1, num2);
    return ratio > 0.8 ? ratio : 0;
  }
  return calculateSimilarity(normalized1, normalized2);
}
function normalizeProductName(name, size) {
  if (!name) return "";
  let normalized = name.toLowerCase().replace(/'/g, "").replace(/"/g, "").replace(/-/g, " ").replace(/\s+/g, " ").trim();
  normalized = removeStoreDescriptors(normalized);
  const nzBrandMappings = getNZBrandMappings();
  for (const [canonical, variations] of Object.entries(nzBrandMappings)) {
    if (normalized.includes(canonical)) {
      normalized = normalized.replace(canonical, canonical);
      break;
    }
    for (const variation of variations) {
      if (normalized.includes(variation)) {
        normalized = normalized.replace(variation, canonical);
        break;
      }
    }
  }
  if (size) {
    const normalizedSize = normalizeSize(size);
    if (normalizedSize) {
      normalized += ` ${normalizedSize}`;
    }
  }
  return normalized.trim();
}
function getNZBrandMappings() {
  return {
    // === SUPERMARKET PRIVATE LABELS ===
    "pams": ["pam", "pams brand", "pams select"],
    "essentials": ["essentials brand", "countdown essentials"],
    "macro": ["macro brand", "macro organic"],
    "woolworths": ["woolworths brand", "woolworths select"],
    "countdown": ["countdown brand", "countdown select"],
    "homebrand": ["home brand", "homebrand select"],
    "signature": ["signature range", "signature brand"],
    "value": ["value brand", "budget"],
    "fresh choice": ["freshchoice", "fresh choice brand"],
    // === DAIRY BRANDS ===
    "anchor": ["anchor brand", "anchor dairy", "anchor milk", "anchor butter", "anchor cheese"],
    "mainland": ["mainland cheese", "mainland dairy", "mainland brand"],
    "meadowfresh": ["meadow fresh", "meadowfresh milk", "meadow fresh milk"],
    "lewis road": ["lewis road creamery", "lewis road milk", "lewis road butter"],
    "kapiti": ["kapiti cheese", "kapiti ice cream", "kapiti brand"],
    "fernleaf": ["fernleaf milk", "fernleaf powder"],
    "tararua": ["tararua cheese", "tararua dairy"],
    "rolling meadow": ["rolling meadow cheese", "rolling meadow dairy"],
    "whitestone": ["whitestone cheese", "whitestone dairy"],
    "mercer": ["mercer cheese", "mercer dairy"],
    "epicure": ["epicure cheese", "epicure dairy"],
    "kikorangi": ["kikorangi cheese", "kikorangi blue"],
    // === MEAT BRANDS ===
    "tegel": ["tegel chicken", "tegel poultry", "tegel brand"],
    "inghams": ["ingham", "inghams chicken", "inghams poultry"],
    "turks": ["turks poultry", "turks chicken"],
    "brinks": ["brinks chicken", "brinks poultry"],
    "hellers": ["heller", "hellers bacon", "hellers sausages", "hellers smallgoods"],
    "beehive": ["beehive bacon", "beehive ham", "beehive smallgoods"],
    "farmland": ["farmland bacon", "farmland ham"],
    "primo": ["primo smallgoods", "primo bacon"],
    "hans": ["hans smallgoods", "hans continental"],
    "continental deli": ["continental smallgoods", "continental deli"],
    // === BREAD & BAKERY BRANDS ===
    "tip top": ["tiptop", "tip top bread", "tiptop bread"],
    "molenberg": ["molenberg bread", "molenburg", "molenberg wholemeal"],
    "vogels": ["vogel", "vogels bread", "vogel bread", "vogels original"],
    "freyas": ["freya", "freyas bread", "freya bread"],
    "natures fresh": ["nature fresh", "natures fresh bread"],
    "burgen": ["burgen bread", "burgen soy lin"],
    "ploughmans": ["ploughman", "ploughmans bread"],
    "golden": ["golden bread", "golden bakery"],
    "bakers delight": ["bakersdelight", "bakers delight bread"],
    // === BEVERAGE BRANDS ===
    "coke": ["coca cola", "coca-cola", "coke classic", "coca cola classic"],
    "coke zero": ["coca cola zero", "coke zero sugar", "coca cola zero sugar"],
    "diet coke": ["coca cola diet", "diet coca cola"],
    "pepsi": ["pepsi cola", "pepsi classic", "pepsi original"],
    "pepsi max": ["pepsi maximum taste", "pepsi max no sugar"],
    "fanta": ["fanta orange", "fanta grape"],
    "sprite": ["sprite lemon", "sprite lime"],
    "l&p": ["lemon paeroa", "lemon and paeroa", "l and p"],
    "just juice": ["justjuice", "just juice brand"],
    "fresh up": ["freshup", "fresh up juice"],
    "keri": ["keri juice", "keri fresh"],
    "charlies": ["charlie", "charlies juice", "charlies honest"],
    "phoenix": ["phoenix organic", "phoenix juice"],
    "pump": ["pump water", "pump brand"],
    "powerade": ["powerade sports drink"],
    "gatorade": ["gatorade sports drink"],
    // === CEREAL & BREAKFAST BRANDS ===
    "sanitarium": ["sanitarium weetbix", "sanitarium so good"],
    "weetbix": ["weet bix", "wheat biscuits", "sanitarium weetbix"],
    "uncle tobys": ["uncle toby", "uncle tobys oats", "uncle tobys muesli"],
    "kelloggs": ["kellogg", "kelloggs cornflakes", "kelloggs special k"],
    "cornflakes": ["corn flakes", "kelloggs cornflakes"],
    "nutrigrain": ["nutri grain", "kelloggs nutrigrain"],
    "special k": ["specialk", "kelloggs special k"],
    "hubbards": ["hubbards cereal", "hubbards muesli"],
    // === SNACK & CONFECTIONERY BRANDS ===
    "cadbury": ["cadburys", "cadbury chocolate"],
    "whittakers": ["whittaker", "whittakers chocolate"],
    "nestle": ["nestl\xE9", "nestle chocolate"],
    "mars": ["mars bar", "mars chocolate"],
    "snickers": ["snickers bar", "snickers chocolate"],
    "kit kat": ["kitkat", "kit-kat"],
    "twix": ["twix bar", "twix chocolate"],
    "moro": ["moro bar", "moro chocolate"],
    "picnic": ["picnic bar", "picnic chocolate"],
    "crunchie": ["crunchie bar", "crunchie chocolate"],
    "bluebird": ["bluebird chips", "bluebird snacks"],
    "eta": ["eta chips", "eta snacks", "eta peanut butter"],
    "proper": ["proper chips", "proper crisps"],
    "heartland": ["heartland chips", "heartland snacks"],
    "griffins": ["griffin", "griffins biscuits", "griffin biscuits"],
    "arnott": ["arnotts", "arnott biscuits", "arnotts biscuits"],
    "toffee pops": ["toffeepops", "griffins toffee pops"],
    "mallowpuffs": ["mallow puffs", "griffins mallowpuffs"],
    "pics": ["pic", "pic peanut butter", "pics peanut butter"],
    "cottees": ["cottee", "cottees jam", "cottee jam"],
    // === PANTRY & COOKING BRANDS ===
    "watties": ["wattie", "watties tomatoes", "watties sauce"],
    "heinz": ["heinz beans", "heinz tomato sauce", "heinz soup"],
    "maggi": ["maggi noodles", "maggi soup", "maggi instant"],
    "continental soup": ["continental soup", "continental pasta"],
    "mccains": ["mccain", "mccains chips", "mccains frozen"],
    "edgell": ["edgell vegetables", "edgell canned"],
    "greggs": ["greggs coffee", "greggs instant"],
    "nescafe": ["nescaf\xE9", "nescafe coffee"],
    "moccona": ["moccona coffee", "moccona instant"],
    "robert harris": ["robertharris", "robert harris coffee"],
    "bell tea": ["bell tea bags", "bell black tea"],
    "dilmah": ["dilmah tea", "dilmah ceylon"],
    "olivani": ["olivani oil", "olivani olive oil"],
    "bertolli": ["bertolli oil", "bertolli olive oil"],
    "praise": ["praise mayonnaise", "praise dressing"],
    "best foods": ["bestfoods", "best foods mayo"],
    "masterfoods": ["master foods", "masterfoods sauce"],
    "fountain": ["fountain sauce", "fountain tomato sauce"],
    // === FROZEN FOOD BRANDS ===
    "birds eye": ["birdseye", "birds eye vegetables", "birds eye fish"],
    "talley": ["talleys", "talley vegetables", "talley frozen"],
    // === CLEANING & HOUSEHOLD BRANDS ===
    "janola": ["janola bleach", "janola cleaning"],
    "earthwise": ["earthwise cleaning", "earthwise eco"],
    "finish": ["finish dishwasher", "finish tablets"],
    "ajax": ["ajax spray", "ajax cleaning"],
    "jif": ["jif cream cleanser", "jif bathroom"],
    "domestos": ["domestos bleach", "domestos toilet"],
    "toilet duck": ["toiletduck", "toilet duck cleaner"],
    "mr muscle": ["mrmuscle", "mr muscle bathroom"],
    "windex": ["windex glass", "windex cleaner"],
    "persil": ["persil washing powder", "persil liquid"],
    "surf": ["surf washing powder", "surf liquid"],
    "omo": ["omo washing powder", "omo liquid"],
    "cold power": ["coldpower", "cold power liquid"],
    "dynamo": ["dynamo washing liquid"],
    "sorbent": ["sorbent toilet paper", "sorbent tissues"],
    "kleenex": ["kleenex tissues", "kleenex toilet paper"],
    "quilton": ["quilton toilet paper", "quilton tissues"],
    "treasures": ["treasures toilet paper", "treasures tissues"],
    // === HEALTH & BEAUTY BRANDS ===
    "colgate": ["colgate toothpaste", "colgate toothbrush"],
    "oral b": ["oral-b", "oralb", "oral b toothbrush"],
    "sensodyne": ["sensodyne toothpaste"],
    "macleans": ["macleans toothpaste"],
    "head shoulders": ["head and shoulders", "head & shoulders"],
    "pantene": ["pantene shampoo", "pantene conditioner"],
    "herbal essences": ["herbal essence", "herbal essences shampoo"],
    "dove": ["dove soap", "dove body wash"],
    "nivea": ["nivea cream", "nivea body"],
    "vaseline": ["vaseline petroleum jelly"],
    // === BABY & PERSONAL CARE ===
    "huggies": ["huggies nappies", "huggies diapers"],
    "pampers": ["pampers nappies", "pampers diapers"],
    "johnson": ["johnsons", "johnson baby", "johnsons baby"],
    "bepanthen": ["bepanthen cream", "bepanthen nappy"],
    // === PET FOOD BRANDS ===
    "pedigree": ["pedigree dog food", "pedigree dry"],
    "whiskas": ["whiskas cat food", "whiskas wet"],
    "fancy feast": ["fancyfeast", "fancy feast cat"],
    "royal canin": ["royalcanin", "royal canin dog"],
    "hills": ["hills pet food", "hills science diet"],
    "eukanuba": ["eukanuba dog food"],
    "iams": ["iams pet food", "iams dog"],
    "optimum": ["optimum dog food", "optimum pet"],
    "tux": ["tux cat food", "tux pet"],
    "champ": ["champ dog food", "champ pet"],
    // === ADDITIONAL NZ SPECIFIC BRANDS ===
    "tip top ice cream": ["tiptop ice cream", "tip top icecream"],
    "new zealand natural": ["nz natural", "new zealand natural ice cream"],
    "deep south": ["deep south ice cream", "deep south icecream"],
    "barkers": ["barkers jam", "barkers preserves"],
    "san remo": ["sanremo", "san remo pasta"],
    "latina": ["latina pasta", "latina fresh"],
    "uncle bens": ["uncle ben", "uncle bens rice"],
    "sunrice": ["sun rice", "sunrice brand"],
    "campbells": ["campbell", "campbells soup", "campbell soup"],
    "delmaine": ["delmaine vegetables", "delmaine canned"],
    "john west": ["johnwest", "john west tuna", "johnwest tuna"],
    "sirena": ["sirena tuna", "sirena seafood"],
    "edmonds": ["edmonds flour", "edmonds baking"],
    "champion": ["champion flour", "champion baking"],
    "chelsea": ["chelsea sugar", "chelsea baking"]
  };
}
function levenshteinDistance(s1, s2) {
  if (!s1) return s2 ? s2.length : 0;
  if (!s2) return s1.length;
  const matrix = [];
  for (let i = 0; i <= s2.length; i++) {
    matrix[i] = [i];
  }
  for (let j = 0; j <= s1.length; j++) {
    matrix[0][j] = j;
  }
  for (let i = 1; i <= s2.length; i++) {
    for (let j = 1; j <= s1.length; j++) {
      const cost = s1[j - 1] === s2[i - 1] ? 0 : 1;
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,
        matrix[i][j - 1] + 1,
        matrix[i - 1][j - 1] + cost
      );
    }
  }
  return matrix[s2.length][s1.length];
}
function jaccardSimilarity(str1, str2) {
  const set1 = new Set(str1.toLowerCase().split(/\s+/).filter((w) => w.length > 1));
  const set2 = new Set(str2.toLowerCase().split(/\s+/).filter((w) => w.length > 1));
  const intersection = new Set([...set1].filter((x) => set2.has(x)));
  const union = /* @__PURE__ */ new Set([...set1, ...set2]);
  return union.size === 0 ? 0 : intersection.size / union.size;
}
function calculateSimilarity(name1, name2, brand1, brand2) {
  if (!name1 || !name2) return 0;
  if (name1 === name2) return 1;
  const normalized1 = normalizeProductName(name1, void 0);
  const normalized2 = normalizeProductName(name2, void 0);
  if (normalized1 === normalized2) return 1;
  const nzBrandMappings = getNZBrandMappings();
  let mapping1 = null;
  let mapping2 = null;
  for (const [canonical, variations] of Object.entries(nzBrandMappings)) {
    if (normalized1.includes(canonical) || variations.some((v) => normalized1.includes(v))) {
      mapping1 = canonical;
    }
    if (normalized2.includes(canonical) || variations.some((v) => normalized2.includes(v))) {
      mapping2 = canonical;
    }
  }
  if (mapping1 && mapping2 && mapping1 === mapping2) return 0.95;
  const distance = levenshteinDistance(normalized1, normalized2);
  const maxLength = Math.max(normalized1.length, normalized2.length);
  const levenshteinSim = maxLength === 0 ? 1 : Math.max(0, 1 - distance / maxLength);
  const jaccardSim = jaccardSimilarity(normalized1, normalized2);
  let combinedSimilarity = levenshteinSim * 0.6 + jaccardSim * 0.4;
  const words1 = normalized1.split(" ").filter((w) => w.length > 2);
  const words2 = normalized2.split(" ").filter((w) => w.length > 2);
  const commonWords = words1.filter((w) => words2.includes(w));
  if (commonWords.length > 0) {
    const wordBoost = commonWords.length / Math.max(words1.length, words2.length) * 0.2;
    combinedSimilarity = Math.min(1, combinedSimilarity + wordBoost);
  }
  const lengthRatio = Math.min(normalized1.length, normalized2.length) / Math.max(normalized1.length, normalized2.length);
  if (lengthRatio < 0.5) {
    combinedSimilarity *= 0.8;
  }
  return combinedSimilarity;
}
async function findBestMatch(normalizedName, size, originalName) {
  try {
    (0, import_utilities.log)(import_utilities.colour.cyan, `\u{1F50D} Finding match for: "${originalName}" -> normalized: "${normalizedName}"`);
    const exactMatch = await consolidatedProductsCollection.findOne({
      normalizedName
    });
    if (exactMatch) {
      (0, import_utilities.log)(import_utilities.colour.green, `\u2705 Exact match found: ${exactMatch.displayName}`);
      return { product: exactMatch, confidence: 1, matchType: "exact" };
    }
    const nzBrandMappings = getNZBrandMappings();
    for (const [canonical, variations] of Object.entries(nzBrandMappings)) {
      if (normalizedName.includes(canonical) || variations.some((v) => normalizedName.includes(v))) {
        const manualMatch = await consolidatedProductsCollection.findOne({
          normalizedName: canonical
        });
        if (manualMatch) {
          (0, import_utilities.log)(import_utilities.colour.green, `\u2705 Manual mapping match found: ${manualMatch.displayName}`);
          return { product: manualMatch, confidence: 0.95, matchType: "manual" };
        }
      }
    }
    const allProducts = await consolidatedProductsCollection.find({}).limit(1e3).toArray();
    let bestMatch = null;
    let bestScore = 0;
    let matchType = "fuzzy";
    const threshold = 0.8;
    for (const product of allProducts) {
      let score = calculateSimilarity(normalizedName, product.normalizedName);
      if (product.aliases && Array.isArray(product.aliases)) {
        for (const alias of product.aliases) {
          const aliasScore = calculateSimilarity(normalizedName, alias);
          if (aliasScore > score) {
            score = aliasScore;
            matchType = "alias";
          }
        }
      }
      if (size && product.primarySize) {
        const sizeScore = calculateSizeSimilarity(size, product.primarySize);
        if (sizeScore > 0.8) {
          score += 0.05;
        }
      }
      if (score > bestScore && score >= threshold) {
        bestScore = score;
        bestMatch = product;
      }
    }
    if (bestMatch) {
      (0, import_utilities.log)(import_utilities.colour.yellow, `\u2705 Fuzzy match found: ${bestMatch.displayName} (confidence: ${bestScore.toFixed(3)}, type: ${matchType})`);
      return {
        product: bestMatch,
        confidence: Math.min(1, bestScore),
        matchType,
        existingConfidence: bestMatch.matchConfidence
      };
    }
    (0, import_utilities.log)(import_utilities.colour.red, `\u274C No match found for: ${originalName}`);
    return null;
  } catch (error) {
    (0, import_utilities.logError)(`\u274C Error finding match: ${error.message}`);
    return null;
  }
}
async function addProductAlias(consolidatedProductId, newAlias) {
  try {
    const normalizedAlias = normalizeProductName(newAlias);
    if (!normalizedAlias || normalizedAlias.trim() === "") return;
    (0, import_utilities.log)(import_utilities.colour.cyan, `\u{1F4DD} Attempting to add alias: "${newAlias}" -> normalized: "${normalizedAlias}"`);
    const product = await consolidatedProductsCollection.findOne({ _id: consolidatedProductId });
    if (!product) {
      (0, import_utilities.log)(import_utilities.colour.red, `\u274C Product not found for alias addition: ${consolidatedProductId}`);
      return;
    }
    const currentAliases = product.aliases || [];
    if (normalizedAlias === product.normalizedName) {
      (0, import_utilities.log)(import_utilities.colour.yellow, `\u26A0\uFE0F Alias matches normalized name, skipping: ${normalizedAlias}`);
      return;
    }
    if (currentAliases.includes(normalizedAlias)) {
      (0, import_utilities.log)(import_utilities.colour.yellow, `\u26A0\uFE0F Alias already exists, skipping: ${normalizedAlias}`);
      return;
    }
    currentAliases.push(normalizedAlias);
    await consolidatedProductsCollection.updateOne(
      { _id: consolidatedProductId },
      {
        $set: {
          aliases: currentAliases,
          updatedAt: /* @__PURE__ */ new Date()
        }
      }
    );
    (0, import_utilities.log)(import_utilities.colour.cyan, `\u2705 Added alias '${normalizedAlias}' to product: ${product.displayName}`);
  } catch (error) {
    (0, import_utilities.logError)(`\u274C Error adding alias: ${error.message}`);
  }
}
async function closeMongoDB() {
  if (client) {
    await client.close();
    (0, import_utilities.log)(import_utilities.colour.blue, "MongoDB connection closed");
  }
}

// src/consolidated-products-mongodb.ts
var import_utilities2 = __toESM(require_utilities());
import { MongoClient as MongoClient2, ObjectId as ObjectId2 } from "./node_modules/mongodb/lib/index.js";
import * as dotenv2 from "./node_modules/dotenv/lib/main.js";
dotenv2.config();
var client2;
var db2;
var consolidatedProductsCollection2;
var brandsCollection2;
var categoryHierarchyCollection2;
async function initializeConsolidatedProductsMongoDB() {
  const connectionString = process.env.MONGODB_CONNECTION_STRING;
  const databaseName = process.env.MONGODB_DATABASE_NAME || "nz-supermarket-scraper";
  if (!connectionString) {
    throw Error("MONGODB_CONNECTION_STRING not set in env");
  }
  try {
    client2 = new MongoClient2(connectionString);
    await client2.connect();
    db2 = client2.db(databaseName);
    consolidatedProductsCollection2 = db2.collection("consolidatedProducts");
    brandsCollection2 = db2.collection("brands");
    categoryHierarchyCollection2 = db2.collection("categoryHierarchy");
    (0, import_utilities2.log)(import_utilities2.colour.green, "\u2705 Consolidated products MongoDB initialized");
    await ensureBasicCategories();
  } catch (error) {
    (0, import_utilities2.logError)(`Failed to initialize consolidated products MongoDB: ${error.message}`);
    throw error;
  }
}
async function ensureBasicCategories() {
  try {
    const existingCategories = await categoryHierarchyCollection2.countDocuments();
    if (existingCategories === 0) {
      const mainCategories = [
        { name: "Fresh Foods", parentId: null, level: 0, sortOrder: 1 },
        { name: "Chilled & Frozen", parentId: null, level: 0, sortOrder: 2 },
        { name: "Pantry & Dry Goods", parentId: null, level: 0, sortOrder: 3 },
        { name: "Beverages", parentId: null, level: 0, sortOrder: 4 },
        { name: "Health & Household", parentId: null, level: 0, sortOrder: 5 }
      ];
      const insertResult = await categoryHierarchyCollection2.insertMany(mainCategories);
      (0, import_utilities2.log)(import_utilities2.colour.blue, `\u2705 Created ${insertResult.insertedCount} main categories`);
      const freshFoodsId = Object.values(insertResult.insertedIds).find(async (id) => {
        const cat = await categoryHierarchyCollection2.findOne({ _id: id });
        return cat?.name === "Fresh Foods";
      });
      (0, import_utilities2.log)(import_utilities2.colour.blue, "\u2705 Basic category structure created");
    }
  } catch (error) {
    (0, import_utilities2.logError)(`Failed to ensure basic categories: ${error.message}`);
  }
}
async function processConsolidatedProductMongoDB(scrapedProduct) {
  if (!consolidatedProductsCollection2) {
    (0, import_utilities2.logError)("Consolidated products collection not initialized");
    return null;
  }
  try {
    const normalizedName = normalizeProductName2(scrapedProduct.name, scrapedProduct.size);
    const now = /* @__PURE__ */ new Date();
    const existingProduct = await consolidatedProductsCollection2.findOne({
      "variants.storeProductId": scrapedProduct.id
    });
    if (existingProduct) {
      await consolidatedProductsCollection2.updateOne(
        { _id: existingProduct._id },
        {
          $set: {
            "variants.$[variant].lastSeen": now,
            "variants.$[variant].storeUnitPrice": scrapedProduct.unitPrice,
            "variants.$[variant].storeUnitName": scrapedProduct.unitName,
            updatedAt: now
          }
        },
        {
          arrayFilters: [{ "variant.storeProductId": scrapedProduct.id }]
        }
      );
      (0, import_utilities2.log)(import_utilities2.colour.cyan, `  Updated consolidated product: ${scrapedProduct.name.slice(0, 40)}`);
      return existingProduct._id.toString();
    } else {
      const newProduct = {
        normalizedName,
        displayName: scrapedProduct.name,
        primarySize: scrapedProduct.size,
        categoryId: null,
        // TODO: Implement category mapping
        brandId: null,
        // TODO: Implement brand extraction
        matchConfidence: 100,
        manualMatch: false,
        variants: [{
          storeProductId: scrapedProduct.id,
          storeId: "woolworths",
          // Store identifier
          storeName: scrapedProduct.name,
          storeSize: scrapedProduct.size,
          storeUnitPrice: scrapedProduct.unitPrice,
          storeUnitName: scrapedProduct.unitName,
          lastSeen: now,
          isActive: true,
          imageUrl: null
        }],
        sizeVariants: scrapedProduct.size ? [{
          sizeName: scrapedProduct.size,
          sizeWeightGrams: null,
          sizeVolumeMl: null,
          isPrimarySize: true
        }] : [],
        currentPrices: [{
          storeId: "woolworths",
          price: scrapedProduct.currentPrice,
          isSpecial: false,
          wasAvailable: true,
          recordedAt: now
        }],
        createdAt: now,
        updatedAt: now
      };
      const insertResult = await consolidatedProductsCollection2.insertOne(newProduct);
      (0, import_utilities2.log)(import_utilities2.colour.cyan, `  Created consolidated product: ${scrapedProduct.name.slice(0, 40)}`);
      return insertResult.insertedId.toString();
    }
  } catch (error) {
    (0, import_utilities2.logError)(`Failed to process consolidated product: ${error.message}`);
    return null;
  }
}
function normalizeProductName2(name, size) {
  let normalized = name.toLowerCase().replace(/[^a-z0-9\s]/g, " ").replace(/\s+/g, "_").trim();
  if (size) {
    const normalizedSize = size.toLowerCase().replace(/[^a-z0-9]/g, "").trim();
    normalized += "_" + normalizedSize;
  }
  return normalized;
}
async function closeConsolidatedProductsMongoDB() {
  try {
    if (client2) {
      await client2.close();
    }
    (0, import_utilities2.log)(import_utilities2.colour.blue, "\u2705 Consolidated products MongoDB connections closed");
  } catch (error) {
    (0, import_utilities2.logError)(`Failed to close MongoDB connections: ${error.message}`);
  }
}

// src/index.ts
var import_product_overrides = __toESM(require_product_overrides());
var import_utilities3 = __toESM(require_utilities());
dotenv3.config();
dotenv3.config({ path: `.env.local`, override: true });
var pageLoadDelaySeconds = 7;
var productLogDelayMilliSeconds = 20;
var startTime = Date.now();
var categorisedUrls = loadUrlsFile();
var databaseMode = false;
var uploadImagesMode = false;
var headlessMode = true;
categorisedUrls = await handleArguments(categorisedUrls);
if (databaseMode) {
  await establishMongoDB();
  await initializeConsolidatedProductsMongoDB();
}
var browser;
var page;
browser = await establishPlaywrightPage(headlessMode);
if (process.env.SKIP_STORE_SELECTION !== "true") {
  try {
    await selectStoreByLocationName();
  } catch (error) {
    (0, import_utilities3.logError)(`Store selection failed: ${error}`);
    (0, import_utilities3.log)(import_utilities3.colour.yellow, "Continuing with default store location...");
  }
} else {
  (0, import_utilities3.log)(import_utilities3.colour.yellow, "Store selection skipped (SKIP_STORE_SELECTION=true)");
}
await scrapeAllPageURLs();
await browser.close();
if (databaseMode) {
  await closeConsolidatedProductsMongoDB();
  await closeMongoDB();
}
(0, import_utilities3.log)(
  import_utilities3.colour.sky,
  `
All Pages Completed = Total Time Elapsed ${(0, import_utilities3.getTimeElapsedSince)(startTime)} 
`
);
function loadUrlsFile(filePath = "src/urls.txt") {
  const rawLinesFromFile = (0, import_utilities3.readLinesFromTextFile)(filePath);
  let categorisedUrls2 = [];
  rawLinesFromFile.map((line) => {
    let parsedUrls = parseAndCategoriseURL(line);
    if (parsedUrls !== void 0) {
      categorisedUrls2 = [...categorisedUrls2, ...parsedUrls];
    }
  });
  return categorisedUrls2;
}
async function scrapeAllPageURLs() {
  (0, import_utilities3.log)(
    import_utilities3.colour.yellow,
    `${categorisedUrls.length} pages to be scraped`.padEnd(35) + `${pageLoadDelaySeconds}s delay between scrapes`.padEnd(35) + (databaseMode ? "(Database Mode)" : "(Dry Run Mode)")
  );
  for (let i = 0; i < categorisedUrls.length; i++) {
    const categorisedUrl = categorisedUrls[i];
    let url = categorisedUrls[i].url;
    const shortUrl = url.replace("https://", "");
    (0, import_utilities3.log)(
      import_utilities3.colour.yellow,
      `
[${i + 1}/${categorisedUrls.length}] ${shortUrl}`
    );
    try {
      await page.goto(url);
      for (let pageDown = 0; pageDown < 5; pageDown++) {
        const timeBetweenPgDowns = Math.random() * 1e3 + 500;
        await page.waitForTimeout(timeBetweenPgDowns);
        await page.keyboard.press("PageDown");
      }
      await page.setDefaultTimeout(3e4);
      await page.waitForSelector("product-price h3");
      const html = await page.innerHTML("product-grid");
      const $ = cheerio.load(html);
      const allProductEntries = $("cdx-card product-stamp-grid div.product-entry");
      const advertisementEntries = $("div.carousel-track div cdx-card product-stamp-grid div.product-entry");
      const adHrefs = advertisementEntries.map((index, element) => {
        return $(element).find("a").first().attr("href");
      }).toArray();
      const productEntries = allProductEntries.filter((index, element) => {
        const productHref = $(element).find("a").first().attr("href");
        return !adHrefs.includes(productHref);
      });
      (0, import_utilities3.log)(
        import_utilities3.colour.yellow,
        `${productEntries.length} product entries found`.padEnd(35) + `Time Elapsed: ${(0, import_utilities3.getTimeElapsedSince)(startTime)}`.padEnd(35) + `Category: ${_.startCase(categorisedUrl.categories.join(" - "))}`
      );
      if (!databaseMode) (0, import_utilities3.logTableHeader)();
      let perPageLogStats = {
        newProducts: 0,
        priceChanged: 0,
        infoUpdated: 0,
        alreadyUpToDate: 0
      };
      perPageLogStats = await processFoundProductEntries(categorisedUrl, productEntries, perPageLogStats);
      if (databaseMode) {
        (0, import_utilities3.log)(
          import_utilities3.colour.blue,
          `Supabase: ${perPageLogStats.newProducts} new products, ${perPageLogStats.priceChanged} updated prices, ${perPageLogStats.infoUpdated} updated info, ${perPageLogStats.alreadyUpToDate} already up-to-date`
        );
      }
      await setTimeout2(pageLoadDelaySeconds * 1e3);
    } catch (error) {
      if (typeof error === "string") {
        if (error.includes("NS_ERROR_CONNECTION_REFUSED")) {
          (0, import_utilities3.logError)("Connection Failed - Check Firewall\n" + error);
          return;
        }
      }
      (0, import_utilities3.logError)(
        "Page Timeout after 30 seconds - Skipping this page\n" + error
      );
    }
  }
}
async function processFoundProductEntries(categorisedUrl, productEntries, perPageLogStats) {
  for (let i = 0; i < productEntries.length; i++) {
    const productEntryElement = productEntries[i];
    const product = playwrightElementToProduct(
      productEntryElement,
      categorisedUrl.categories
    );
    if (databaseMode && product !== void 0) {
      const response = await upsertProductToMongoDB(product);
      const consolidatedProductId = await processConsolidatedProductMongoDB(product);
      switch (response) {
        case 3 /* AlreadyUpToDate */:
          perPageLogStats.alreadyUpToDate++;
          break;
        case 2 /* InfoChanged */:
          perPageLogStats.infoUpdated++;
          break;
        case 0 /* NewProduct */:
          perPageLogStats.newProducts++;
          break;
        case 1 /* PriceChanged */:
          perPageLogStats.priceChanged++;
          break;
        default:
          break;
      }
      if (uploadImagesMode) {
        const imageUrlBase = "https://assets.woolworths.com.au/images/2010/";
        const imageUrlExtensionAndQueryParams = ".jpg?impolicy=wowcdxwbjbx&w=900&h=900";
        const imageUrl = imageUrlBase + product.id + imageUrlExtensionAndQueryParams;
        await uploadImageToMongoDB(imageUrl, product);
      }
    } else if (!databaseMode && product !== void 0) {
      (0, import_utilities3.logProductRow)(product);
    }
    await setTimeout2(productLogDelayMilliSeconds);
  }
  return perPageLogStats;
}
function handleArguments(categorisedUrls2) {
  if (process.argv.length > 2) {
    const userArgs = process.argv.slice(2, process.argv.length);
    let potentialUrl = "";
    userArgs.forEach(async (arg) => {
      if (arg === "db") databaseMode = true;
      else if (arg === "images") uploadImagesMode = true;
      else if (arg === "headless") headlessMode = true;
      else if (arg === "headed") headlessMode = false;
      else if (arg.includes(".co.nz")) potentialUrl += arg;
      else if (arg === "reverse") categorisedUrls2 = categorisedUrls2.reverse();
    });
    const parsedUrl = parseAndCategoriseURL(potentialUrl);
    if (parsedUrl !== void 0) categorisedUrls2 = [parsedUrl];
  }
  return categorisedUrls2;
}
async function establishPlaywrightPage(headless = true) {
  (0, import_utilities3.log)(
    import_utilities3.colour.yellow,
    "Launching Browser.. " + (process.argv.length > 2 ? "(" + (process.argv.length - 2) + " arguments found)" : "")
  );
  browser = await playwright.firefox.launch({
    headless
  });
  page = await browser.newPage();
  await routePlaywrightExclusions();
  return browser;
}
async function selectStoreByLocationName(locationName = "") {
  if (locationName === "") {
    if (process.env.STORE_NAME) locationName = process.env.STORE_NAME;
    else {
      (0, import_utilities3.log)(import_utilities3.colour.yellow, "No store location specified - using default location");
      return;
    }
  }
  (0, import_utilities3.log)(import_utilities3.colour.yellow, `Attempting to select store location: ${locationName}`);
  try {
    await page.setDefaultTimeout(15e3);
    await page.goto("https://www.woolworths.co.nz/shop/browse", {
      waitUntil: "domcontentloaded"
    });
    await page.waitForTimeout(2e3);
    const possibleSelectors = [
      'button[data-testid="store-selector"]',
      'button[aria-label*="store"]',
      'button[aria-label*="location"]',
      '[data-testid="change-store"]',
      'button:has-text("Change store")',
      'button:has-text("Select store")',
      ".store-selector button",
      '[class*="store"] button',
      "fieldset div div p button"
    ];
    let storeButton = null;
    for (const selector of possibleSelectors) {
      try {
        await page.waitForSelector(selector, { timeout: 3e3 });
        storeButton = page.locator(selector).first();
        (0, import_utilities3.log)(import_utilities3.colour.green, `Found store selector: ${selector}`);
        break;
      } catch {
      }
    }
    if (!storeButton) {
      (0, import_utilities3.log)(import_utilities3.colour.yellow, "No store selector found - proceeding with default location");
      return;
    }
    await storeButton.click();
    await page.waitForTimeout(1e3);
    const inputSelectors = [
      'input[placeholder*="suburb"]',
      'input[placeholder*="location"]',
      'input[placeholder*="address"]',
      'input[type="text"]',
      "form-suburb-autocomplete form-input input",
      '[data-testid="location-input"]',
      ".location-input input",
      'input[aria-label*="location"]'
    ];
    let locationInput = null;
    for (const selector of inputSelectors) {
      try {
        await page.waitForSelector(selector, { timeout: 3e3 });
        locationInput = page.locator(selector).first();
        (0, import_utilities3.log)(import_utilities3.colour.green, `Found location input: ${selector}`);
        break;
      } catch {
      }
    }
    if (!locationInput) {
      (0, import_utilities3.log)(import_utilities3.colour.yellow, "No location input found - proceeding with default location");
      return;
    }
    await locationInput.clear();
    await locationInput.fill(locationName);
    await page.waitForTimeout(2e3);
    try {
      const suggestionSelectors = [
        '[role="option"]:first-child',
        ".suggestion:first-child",
        ".autocomplete-item:first-child",
        "li:first-child",
        '[data-testid="suggestion"]:first-child'
      ];
      let suggestionFound = false;
      for (const selector of suggestionSelectors) {
        try {
          await page.waitForSelector(selector, { timeout: 2e3 });
          await page.locator(selector).click();
          suggestionFound = true;
          (0, import_utilities3.log)(import_utilities3.colour.green, `Selected suggestion using: ${selector}`);
          break;
        } catch {
        }
      }
      if (!suggestionFound) {
        await page.keyboard.press("ArrowDown");
        await page.waitForTimeout(300);
        await page.keyboard.press("Enter");
        (0, import_utilities3.log)(import_utilities3.colour.yellow, "Used keyboard navigation to select location");
      }
      await page.waitForTimeout(1e3);
      const saveSelectors = [
        'button:has-text("Save")',
        'button:has-text("Continue")',
        'button:has-text("Confirm")',
        'button:has-text("Select")',
        '[data-testid="save-location"]',
        ".save-button",
        'button[type="submit"]'
      ];
      for (const selector of saveSelectors) {
        try {
          await page.waitForSelector(selector, { timeout: 2e3 });
          await page.locator(selector).click();
          (0, import_utilities3.log)(import_utilities3.colour.green, `Clicked save button: ${selector}`);
          break;
        } catch {
        }
      }
      await page.waitForTimeout(2e3);
      (0, import_utilities3.log)(import_utilities3.colour.green, `Successfully changed location to: ${locationName}`);
    } catch (error) {
      (0, import_utilities3.log)(import_utilities3.colour.yellow, `Could not select location suggestion - using typed location: ${error}`);
    }
  } catch (error) {
    (0, import_utilities3.logError)(`Store location selection failed: ${error}`);
    (0, import_utilities3.log)(import_utilities3.colour.yellow, "Proceeding with default location");
  }
}
function playwrightElementToProduct(element, categories) {
  const $ = cheerio.load(element);
  let idNameSizeH3 = $(element).find("h3").filter((i, element2) => {
    if ($(element2).attr("id")?.includes("-title")) {
      return true;
    } else return false;
  });
  let product = {
    // ID
    // -------
    // Extract product ID from h3 id attribute, and remove non-numbers
    id: idNameSizeH3.attr("id")?.replace(/\D/g, ""),
    // Source Site - set where the source of information came from
    sourceSite: "countdown.co.nz",
    // use countdown for consistency with old data
    // Categories
    category: categories,
    // already obtained from url/text file
    // Store today's date
    lastChecked: /* @__PURE__ */ new Date(),
    lastUpdated: /* @__PURE__ */ new Date(),
    // These values will later be overwritten
    name: "",
    priceHistory: [],
    currentPrice: 0
  };
  let rawNameAndSize = idNameSizeH3.text().trim();
  rawNameAndSize = rawNameAndSize.toLowerCase().replace("  ", " ").replace("fresh fruit", "").replace("fresh vegetable", "").trim();
  let tryMatchSize = rawNameAndSize.match(/(tray\s\d+)|(\d+(\.\d+)?(\-\d+\.\d+)?\s?(g|kg|l|ml|pack))\b/g);
  if (!tryMatchSize) {
    product.name = (0, import_utilities3.toTitleCase)(rawNameAndSize);
    product.size = "";
  } else {
    let indexOfSizeSection = rawNameAndSize.indexOf(tryMatchSize[0]);
    product.name = (0, import_utilities3.toTitleCase)(rawNameAndSize.slice(0, indexOfSizeSection)).trim();
    let cleanedSize = rawNameAndSize.slice(indexOfSizeSection).trim();
    if (cleanedSize.match(/\d+l\b/)) {
      cleanedSize = cleanedSize.replace("l", "L");
    }
    cleanedSize.replace("tray", "Tray");
    product.size = cleanedSize;
  }
  const dollarString = $(element).find("product-price div h3 em").text().trim();
  let centString = $(element).find("product-price div h3 span").text().trim();
  if (centString.includes("kg")) product.size = "per kg";
  centString = centString.replace(/\D/g, "");
  product.currentPrice = Number(dollarString + "." + centString);
  const today = /* @__PURE__ */ new Date();
  today.setMinutes(0);
  today.setSeconds(0);
  const todaysDatedPrice = {
    date: today,
    price: product.currentPrice
  };
  product.priceHistory = [todaysDatedPrice];
  const rawUnitPrice = $(element).find("span.cupPrice").text().trim();
  if (rawUnitPrice) {
    const unitPriceString = rawUnitPrice.split("/")[0].replace("$", "").trim();
    let unitPrice = Number.parseFloat(unitPriceString);
    const amountAndUnit = rawUnitPrice.split("/")[1].trim();
    let amount = Number.parseInt(amountAndUnit.match(/\d+/g)?.[0] || "");
    let unit = amountAndUnit.match(/\w+/g)?.[0] || "";
    if (amountAndUnit == "100g") {
      amount = amount * 10;
      unitPrice = unitPrice * 10;
      unit = "kg";
    } else if (amountAndUnit == "100mL") {
      amount = amount * 10;
      unitPrice = unitPrice * 10;
      unit = "L";
    }
    unit = unit.replace("1kg", "kg");
    unit = unit.replace("1L", "L");
    product.unitPrice = unitPrice;
    product.unitName = unit;
  }
  import_product_overrides.productOverrides.forEach((override) => {
    if (override.id === product.id) {
      if (override.size !== void 0) {
        product.size = override.size;
      }
      if (override.category !== void 0) {
        product.category = [override.category];
      }
    }
  });
  if (validateProduct(product)) return product;
  else {
    try {
      (0, import_utilities3.logError)(
        `  Unable to Scrape: ${product.id.padStart(6)} | ${product.name} | $${product.currentPrice}`
      );
    } catch {
      (0, import_utilities3.logError)("  Unable to Scrape ID from product");
    }
    return void 0;
  }
}
function validateProduct(product) {
  try {
    if (product.name.match(/\$\s\d+/)) return false;
    if (product.name.length < 4 || product.name.length > 100) return false;
    if (product.id.length < 2 || product.id.length > 20) return false;
    if (product.currentPrice <= 0 || product.currentPrice === null || product.currentPrice === void 0 || Number.isNaN(product.currentPrice) || product.currentPrice > 999) {
      return false;
    }
    return true;
  } catch (error) {
    return false;
  }
}
function parseAndCategoriseURL(line) {
  let baseCategorisedURL = { url: "", categories: [] };
  let parsedUrls = [];
  let numPagesPerURL = 1;
  if (!line.includes("woolworths.co.nz")) {
    return void 0;
  } else if (line.includes("?search=")) {
    parsedUrls.push({ url: line, categories: [] });
  } else {
    line.split(" ").forEach((section) => {
      if (section.includes("woolworths.co.nz")) {
        baseCategorisedURL.url = section;
        if (!baseCategorisedURL.url.startsWith("http"))
          baseCategorisedURL.url = "https://" + section;
        if (section.includes("?")) {
          baseCategorisedURL.url = line.substring(0, line.indexOf("?"));
        }
        baseCategorisedURL.url += "?page=1";
      } else if (section.startsWith("categories=")) {
        let splitCategories = [section.replace("categories=", "")];
        if (section.includes(","))
          splitCategories = section.replace("categories=", "").split(",");
        baseCategorisedURL.categories = splitCategories;
      } else if (section.startsWith("pages=")) {
        numPagesPerURL = Number.parseInt(section.split("=")[1]);
      }
      if (baseCategorisedURL.categories.length === 0) {
        const baseUrl = baseCategorisedURL.url.split("?")[0];
        let slashSections = baseUrl.split("/");
        baseCategorisedURL.categories = [slashSections[slashSections.length - 1]];
      }
    });
    for (let i = 1; i <= numPagesPerURL; i++) {
      let pagedUrl = {
        url: baseCategorisedURL.url.replace("page=1", "page=" + i),
        categories: baseCategorisedURL.categories
      };
      parsedUrls.push(pagedUrl);
    }
  }
  return parsedUrls;
}
async function routePlaywrightExclusions() {
  let typeExclusions = ["image", "media", "font"];
  let urlExclusions = [
    "googleoptimize.com",
    "gtm.js",
    "visitoridentification.js",
    "js-agent.newrelic.com",
    "cquotient.com",
    "googletagmanager.com",
    "cloudflareinsights.com",
    "dwanalytics",
    "facebook.net",
    "chatWidget",
    "edge.adobedc.net",
    "\u200B/Content/Banners/",
    "go-mpulse.net"
  ];
  await page.route("**/*", async (route) => {
    const req = route.request();
    let excludeThisRequest = false;
    urlExclusions.forEach((excludedURL) => {
      if (req.url().includes(excludedURL)) excludeThisRequest = true;
    });
    typeExclusions.forEach((excludedType) => {
      if (req.resourceType() === excludedType) excludeThisRequest = true;
    });
    if (excludeThisRequest) {
      await route.abort();
    } else {
      await route.continue();
    }
  });
  return;
}
export {
  databaseMode,
  parseAndCategoriseURL,
  playwrightElementToProduct,
  uploadImagesMode
};
//# sourceMappingURL=data:application/json;base64,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

	