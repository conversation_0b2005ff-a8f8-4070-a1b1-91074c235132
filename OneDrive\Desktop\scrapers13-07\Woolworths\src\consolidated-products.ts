/**
 * Consolidated Products Integration for Woolworths Scraper
 * This module integrates the product matching algorithm with the Woolworths scraper
 */

import { createClient, SupabaseClient } from "@supabase/supabase-js";
import { Product } from "./typings.js";
import * as ProductMatcher from "../../shared/product-matcher.js";

let supabase: SupabaseClient;
let woolworthsStoreId: number;

export function initializeConsolidatedProducts(supabaseUrl: string, supabaseKey: string) {
    supabase = createClient(supabaseUrl, supabaseKey, { auth: { persistSession: false } });
    ProductMatcher.initializeSupabase(supabaseUrl, supabaseKey);
}

export async function ensureWoolworthsStoreId(): Promise<number> {
    if (woolworthsStoreId) return woolworthsStoreId;
    
    const { data, error } = await supabase
        .from("stores")
        .select("id")
        .eq("name", "woolworths")
        .single();

    if (error && error.code !== "PGRST116") {
        throw error;
    }

    if (data?.id) {
        woolworthsStoreId = data.id;
        return woolworthsStoreId;
    }

    // Insert store row if not exists
    const { data: insertData, error: insertErr } = await supabase
        .from("stores")
        .insert({ name: "woolworths" })
        .select("id")
        .single();
    if (insertErr) throw insertErr;
    
    woolworthsStoreId = insertData!.id;
    return woolworthsStoreId;
}

/**
 * Process a scraped Woolworths product and integrate with consolidated products
 */
export async function processConsolidatedProduct(scrapedProduct: Product): Promise<string | null> {
    try {
        // Ensure we have the Woolworths store ID
        const storeId = await ensureWoolworthsStoreId();
        
        // Convert scraped product to format expected by matcher
        const productForMatching = {
            id: scrapedProduct.id,
            name: scrapedProduct.name,
            size: scrapedProduct.size,
            category: scrapedProduct.category,
            brand: extractBrandFromName(scrapedProduct.name), // Extract brand if possible
            unitPrice: scrapedProduct.unitPrice,
            unitName: scrapedProduct.unitName
        };
        
        // Find or create consolidated product
        const consolidatedProductId = await ProductMatcher.consolidateProduct(productForMatching, storeId);
        
        if (consolidatedProductId) {
            // Store the current price
            await ProductMatcher.storeConsolidatedPrice(
                consolidatedProductId,
                storeId,
                scrapedProduct.currentPrice,
                false // isSpecial - could be determined from product data
            );
            
            // Handle multiple sizes if applicable
            await handleProductSizeVariants(consolidatedProductId, scrapedProduct);
        }
        
        return consolidatedProductId;
    } catch (error) {
        console.error(`Error processing consolidated product for ${scrapedProduct.name}:`, error);
        return null;
    }
}

/**
 * Extract brand name from product name using common patterns
 */
function extractBrandFromName(productName: string): string | undefined {
    // Common brand patterns in Woolworths products
    const brandPatterns = [
        /^([A-Z][a-z]+(?:\s+[A-Z][a-z]*)*)\s+/,  // Capitalize Case at start
        /\b(Select|Countdown|Essentials|Free Range|Signature)\b/i // Store brands
    ];
    
    for (const pattern of brandPatterns) {
        const match = productName.match(pattern);
        if (match) {
            return match[1].trim();
        }
    }
    
    return undefined;
}

/**
 * Handle different size variants of the same product
 */
async function handleProductSizeVariants(consolidatedProductId: string, product: Product) {
    if (!product.size) return;
    
    try {
        // Check if this size variant already exists
        const { data: existingVariant } = await supabase
            .from('product_size_variants')
            .select('id')
            .eq('consolidated_product_id', consolidatedProductId)
            .eq('size_name', product.size)
            .single();
            
        if (existingVariant) return; // Size variant already exists
        
        // Calculate normalized weight/volume for comparison
        const weightGrams = calculateWeightInGrams(product.size);
        const volumeML = calculateVolumeInML(product.size);
        
        // Insert new size variant
        await supabase
            .from('product_size_variants')
            .insert({
                consolidated_product_id: consolidatedProductId,
                size_name: product.size,
                size_weight_grams: weightGrams,
                size_volume_ml: volumeML,
                is_primary_size: false // Could be determined by most common size
            });
            
    } catch (error) {
        console.error(`Error handling size variants for ${product.name}:`, error);
    }
}

/**
 * Convert size string to weight in grams for comparison
 */
function calculateWeightInGrams(size: string): number | null {
    if (!size) return null;
    
    const sizeNormalized = size.toLowerCase();
    
    // Match patterns like "500g", "1.5kg", "2kg"
    const gramMatch = sizeNormalized.match(/(\d+(?:\.\d+)?)\s*g\b/);
    if (gramMatch) {
        return parseInt(gramMatch[1]);
    }
    
    const kgMatch = sizeNormalized.match(/(\d+(?:\.\d+)?)\s*kg\b/);
    if (kgMatch) {
        return Math.round(parseFloat(kgMatch[1]) * 1000);
    }
    
    return null;
}

/**
 * Convert size string to volume in ML for comparison
 */
function calculateVolumeInML(size: string): number | null {
    if (!size) return null;
    
    const sizeNormalized = size.toLowerCase();
    
    // Match patterns like "500ml", "1.5l", "2l"
    const mlMatch = sizeNormalized.match(/(\d+(?:\.\d+)?)\s*ml\b/);
    if (mlMatch) {
        return parseInt(mlMatch[1]);
    }
    
    const lMatch = sizeNormalized.match(/(\d+(?:\.\d+)?)\s*l\b/);
    if (lMatch) {
        return Math.round(parseFloat(lMatch[1]) * 1000);
    }
    
    return null;
}

/**
 * Get consolidated product details with all store prices
 */
export async function getConsolidatedProductWithPrices(consolidatedProductId: string) {
    const { data, error } = await supabase
        .from('consolidated_product_details')
        .select('*')
        .eq('id', consolidatedProductId)
        .single();
        
    if (error) {
        console.error('Error fetching consolidated product details:', error);
        return null;
    }
    
    return data;
}

/**
 * Search consolidated products for the frontend
 */
export async function searchConsolidatedProducts(searchTerm: string, categoryId?: number, limit = 50) {
    let query = supabase
        .from('consolidated_product_details')
        .select('*')
        .limit(limit);
        
    if (searchTerm) {
        query = query.textSearch('display_name', searchTerm);
    }
    
    if (categoryId) {
        query = query.eq('category_id', categoryId);
    }
    
    const { data, error } = await query;
    
    if (error) {
        console.error('Error searching consolidated products:', error);
        return [];
    }
    
    return data || [];
}