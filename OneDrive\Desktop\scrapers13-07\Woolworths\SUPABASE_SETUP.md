# Supabase Setup Guide for Woolworths Scraper

## Database Setup

The database schema should already be created via the migration files in the main project. If not, run:

```sql
-- From supabase/migrations/20240713_001_init.sql
-- And supabase/migrations/20250122_001_add_missing_column.sql
```

## Storage Setup for Images

To enable image uploads, you need to create a Storage bucket in your Supabase project:

### 1. Create Storage Bucket

1. Go to your [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project
3. Navigate to **Storage** in the left sidebar
4. Click **"Create a bucket"**
5. Set the following:
   - **Name**: `product-images`
   - **Public bucket**: ✅ **Yes** (required for public image URLs)
   - **File size limit**: 50MB (optional, but recommended)
   - **Allowed MIME types**: `image/*` (optional, but recommended)

### 2. Configure Bucket Policies (Optional)

For additional security, you can set up RLS policies:

```sql
-- Allow public read access to images
CREATE POLICY "Public read access to product images"
ON storage.objects FOR SELECT
USING (bucket_id = 'product-images');

-- Allow authenticated uploads (via Service Role Key)
CREATE POLICY "Service role can upload product images"
ON storage.objects FOR INSERT
WITH CHECK (bucket_id = 'product-images' AND auth.role() = 'service_role');
```

### 3. Test Storage Access

You can test that your storage is working correctly by running:

```bash
npm run "db images"
```

Images will be stored at:
- **Bucket**: `product-images`
- **Path**: `products/{productId}.jpg`
- **Public URL**: `https://your-project.supabase.co/storage/v1/object/public/product-images/products/{productId}.jpg`

### 4. Troubleshooting

**Error: "bucket not found"**
- Make sure you created the `product-images` bucket
- Check the bucket name is exactly `product-images`

**Error: "insufficient permissions"**  
- Ensure you're using the Service Role Key, not the Anon Key
- Check that the Service Role has storage permissions

**Error: "object already exists"**
- This is expected behavior - images are updated with `upsert: true`
- The scraper will overwrite existing images with new versions

### 5. Storage Management

- **View images**: Go to Storage → product-images in Supabase Dashboard
- **Delete images**: You can manually delete images through the dashboard
- **Storage usage**: Monitor usage in your project settings

---

*This setup only needs to be done once per Supabase project.*