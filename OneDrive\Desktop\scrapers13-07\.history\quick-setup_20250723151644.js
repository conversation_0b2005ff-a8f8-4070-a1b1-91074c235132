#!/usr/bin/env node

/**
 * Quick Setup for Consolidated Products System
 * A simplified version that just checks your setup and provides guidance
 */

import dotenv from 'dotenv';
import fs from 'fs';

// Load environment variables
dotenv.config();
dotenv.config({ path: '.env.local', override: true });

const COLORS = {
    reset: '\x1b[0m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    red: '\x1b[31m',
    cyan: '\x1b[36m'
};

function log(color, message) {
    console.log(`${color}${message}${COLORS.reset}`);
}

function checkEnvironment() {
    log(COLORS.cyan, '\n=== Environment Check ===');
    
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;
    
    if (supabaseUrl && supabaseKey) {
        log(COLORS.green, '✅ Supabase environment variables found');
        return true;
    } else {
        log(COLORS.red, '❌ Missing Supabase environment variables');
        console.log('Please set in your .env file:');
        console.log('  SUPABASE_URL=https://emjwniuqwhvohjassnrg.supabase.co');
        console.log('  SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVtanduaXVxd2h2b2hqYXNzbnJnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI0MDE2MDUsImV4cCI6MjA2Nzk3NzYwNX0.t4jObaNMtVPwTw_z5f2qygxN_QRyzBpr26kYaSuqsOU');
        return false;
    }
}

function checkFiles() {
    log(COLORS.cyan, '\n=== File Check ===');
    
    const requiredFiles = [
        'supabase/migrations/20240724_002_consolidated_products.sql',
        'shared/product-matcher.js',
        'Woolworths/src/consolidated-products.ts'
    ];
    
    let allFilesExist = true;
    
    for (const file of requiredFiles) {
        if (fs.existsSync(file)) {
            log(COLORS.green, `✅ ${file}`);
        } else {
            log(COLORS.red, `❌ ${file}`);
            allFilesExist = false;
        }
    }
    
    return allFilesExist;
}

function showNextSteps() {
    log(COLORS.cyan, '\n=== Next Steps ===');
    
    console.log(`
${COLORS.yellow}1. Database Migration:${COLORS.reset}
   • Go to your Supabase dashboard
   • Navigate to SQL Editor
   • Run the migration file: supabase/migrations/20240724_002_consolidated_products.sql

${COLORS.yellow}2. Install Dependencies:${COLORS.reset}
   cd Woolworths
   npm install

${COLORS.yellow}3. Test the System:${COLORS.reset}
   cd Woolworths
   npm run db

${COLORS.yellow}4. Check Results:${COLORS.reset}
   • Check your Supabase dashboard for new tables:
     - consolidated_products
     - product_variants  
     - consolidated_prices
     - category_hierarchy

${COLORS.green}5. Success Indicators:${COLORS.reset}
   ✅ Products get matched across stores
   ✅ consolidated_products table gets populated
   ✅ Price data appears in consolidated_prices
   ✅ Single products show multiple store prices
`);
}

async function main() {
    log(COLORS.cyan, '🚀 Consolidated Products Quick Setup');
    
    const envOk = checkEnvironment();
    const filesOk = checkFiles();
    
    if (envOk && filesOk) {
        log(COLORS.green, '\n🎉 All checks passed! Your system is ready.');
        showNextSteps();
    } else {
        log(COLORS.red, '\n❌ Setup issues found. Please fix the above problems first.');
    }
}

main().catch(console.error);