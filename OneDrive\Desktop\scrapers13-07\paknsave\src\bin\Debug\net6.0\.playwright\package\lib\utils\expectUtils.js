"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.serializeExpectedTextValues = serializeExpectedTextValues;
var _rtti = require("./rtti");
/**
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

function serializeExpectedTextValues(items, options = {}) {
  return items.map(i => ({
    string: (0, _rtti.isString)(i) ? i : undefined,
    regexSource: (0, _rtti.isRegExp)(i) ? i.source : undefined,
    regexFlags: (0, _rtti.isRegExp)(i) ? i.flags : undefined,
    matchSubstring: options.matchSubstring,
    ignoreCase: options.ignoreCase,
    normalizeWhiteSpace: options.normalizeWhiteSpace
  }));
}