process.argv = [process.argv[0], ...process.argv.slice(3)];

		import __esrun_url from 'url';

		import { createRequire as __esrun_createRequire } from "module";

		const __esrun_fileUrl = __esrun_url.pathToFileURL("C:\Users\<USER>\OneDrive\Desktop\scrapers13-07\Woolworths\esrun-1753308963160.tmp.mjs");

		const require = __esrun_createRequire(__esrun_fileUrl);
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __require = /* @__PURE__ */ ((x) => typeof require !== "undefined" ? require : typeof Proxy !== "undefined" ? new Proxy(x, {
  get: (a, b) => (typeof require !== "undefined" ? require : a)[b]
}) : x)(function(x) {
  if (typeof require !== "undefined") return require.apply(this, arguments);
  throw Error('Dynamic require of "' + x + '" is not supported');
});
var __commonJS = (cb, mod) => function __require2() {
  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));

// src/typings.js
var require_typings = __commonJS({
  "src/typings.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// src/utilities.js
var require_utilities = __commonJS({
  "src/utilities.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.validCategories = exports.colour = void 0;
    exports.log = log4;
    exports.logError = logError4;
    exports.logProductRow = logProductRow2;
    exports.logTableHeader = logTableHeader2;
    exports.readLinesFromTextFile = readLinesFromTextFile2;
    exports.getTimeElapsedSince = getTimeElapsedSince2;
    exports.toTitleCase = toTitleCase2;
    var fs_1 = __require("fs");
    var tableIDWidth = 6;
    var tableNameWidth = 60;
    var tableSizeWidth = 17;
    exports.colour = {
      red: "\x1B[31m",
      green: "\x1B[32m",
      yellow: "\x1B[33m",
      blue: "\x1B[38;5;117m",
      magenta: "\x1B[35m",
      cyan: "\x1B[36m",
      white: "\x1B[37m",
      crimson: "\x1B[38m",
      grey: "\x1B[90m",
      orange: "\x1B[38;5;214m",
      sky: "\x1B[38;5;153m"
    };
    function log4(colour4, text) {
      const clear = "\x1B[0m";
      console.log(`${colour4}%s${clear}`, text);
    }
    function logError4(text) {
      log4(exports.colour.red, text);
    }
    function logProductRow2(product) {
      var _a;
      const unitPriceString = product.unitPrice ? `$${product.unitPrice.toFixed(2)} /${product.unitName}` : ``;
      log4(getAlternatingRowColour(exports.colour.sky, exports.colour.white), `${product.id.padStart(tableIDWidth)} | ${product.name.slice(0, tableNameWidth).padEnd(tableNameWidth)} | ${(_a = product.size) === null || _a === void 0 ? void 0 : _a.slice(0, tableSizeWidth).padEnd(tableSizeWidth)} | $ ${product.currentPrice.toFixed(2).padStart(4).padEnd(5)} | ` + unitPriceString);
    }
    function logTableHeader2() {
      log4(exports.colour.yellow, `${"ID".padStart(tableIDWidth)} | ${"Name".padEnd(tableNameWidth)} | ${"Size".padEnd(tableSizeWidth)} | ${"Price".padEnd(7)} | Unit Price`);
      let headerLine = "";
      for (let i = 0; i < 111; i++) {
        headerLine += "-";
      }
      log4(exports.colour.yellow, headerLine);
    }
    var alternatingRowColour = false;
    function getAlternatingRowColour(colourA, colourB) {
      alternatingRowColour = alternatingRowColour ? false : true;
      return alternatingRowColour ? colourA : colourB;
    }
    function readLinesFromTextFile2(filename) {
      try {
        const file = (0, fs_1.readFileSync)(filename, "utf-8");
        const result = file.split(/\r?\n/).filter((line) => {
          if (line.trim().length > 0)
            return true;
          else
            return false;
        });
        return result;
      } catch (error) {
        throw "Error reading " + filename;
      }
    }
    function getTimeElapsedSince2(startTime2) {
      let elapsedTimeSeconds = (Date.now() - startTime2) / 1e3;
      let elapsedTimeString = Math.floor(elapsedTimeSeconds).toString();
      if (elapsedTimeSeconds >= 60) {
        return Math.floor(elapsedTimeSeconds / 60) + ":" + Math.floor(elapsedTimeSeconds % 60).toString().padStart(2, "0");
      } else
        return elapsedTimeString + "s";
    }
    exports.validCategories = [
      // freshCategory
      "eggs",
      "fruit",
      "fresh-vegetables",
      "salads-coleslaw",
      "bread",
      "bread-rolls",
      "specialty-bread",
      "bakery-cakes",
      "bakery-desserts",
      // chilledCategory
      "milk",
      "long-life-milk",
      "sour-cream",
      "cream",
      "yoghurt",
      "butter",
      "cheese",
      "cheese-slices",
      "salami",
      "other-deli-foods",
      // meatCategory
      "beef-lamb",
      "chicken",
      "ham",
      "bacon",
      "pork",
      "patties-meatballs",
      "sausages",
      "deli-meats",
      "meat-alternatives",
      "seafood",
      "salmon",
      // frozenCategory
      "ice-cream",
      "ice-blocks",
      "pastries-cheesecake",
      "frozen-chips",
      "frozen-vegetables",
      "frozen-fruit",
      "frozen-seafood",
      "pies-sausage-rolls",
      "pizza",
      "other-savouries",
      // pantryCategory
      "rice",
      "noodles",
      "pasta",
      "beans-spaghetti",
      "canned-fish",
      "canned-meat",
      "soup",
      "cereal",
      "spreads",
      "baking",
      "sauces",
      "oils-vinegars",
      "world-foods",
      // snacksCategory
      "chocolate",
      "boxed-chocolate",
      "chips",
      "crackers",
      "biscuits",
      "muesli-bars",
      "nuts-bulk-mix",
      "sweets-lollies",
      "other-snacks",
      // drinksCategory
      "black-tea",
      "green-tea",
      "herbal-tea",
      "drinking-chocolate",
      "coffee",
      "soft-drinks",
      "energy-drinks",
      "juice",
      // petsCategory
      "cat-food",
      "cat-treats",
      "dog-food",
      "dog-treats"
    ];
    function toTitleCase2(str) {
      return str.replace(/\w\S*/g, function(txt) {
        return txt.charAt(0).toUpperCase() + txt.substring(1).toLowerCase();
      });
    }
  }
});

// src/product-overrides.js
var require_product_overrides = __commonJS({
  "src/product-overrides.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.productOverrides = void 0;
    exports.productOverrides = [
      { id: "206889", size: "180g" },
      { id: "196996", size: "300g" },
      { id: "137967", size: "420g" },
      { id: "125856", size: "450g" },
      { id: "189268", size: "1.13kg" },
      { id: "189150", size: "1.2kg" },
      { id: "190454", size: "2.1kg" },
      { id: "189078", size: "1.3kg" },
      { id: "189136", size: "1.2kg" },
      { id: "755237", size: "931g" },
      { id: "755304", size: "1.1kg" },
      { id: "755246", size: "1020g" },
      { id: "755245", size: "1.2kg" },
      { id: "112273", size: "865ml" },
      { id: "269514", size: "584ml" },
      { id: "269515", size: "584ml" },
      { id: "116518", size: "440ml" },
      { id: "151191", size: "570ml" },
      { id: "279904", size: "575ml" },
      { id: "146149", size: "1000ml" },
      { id: "791925", size: "525g" },
      { id: "774216", size: "525g" },
      { id: "784406", size: "525g" },
      { id: "791916", size: "525g" },
      { id: "306624", size: "185g" },
      { id: "156824", size: "180g" },
      { id: "9023", size: "375g" },
      { id: "266962", category: "sweets-lollies" },
      { id: "171524", size: "230ml", category: "baking" },
      { id: "170021", category: "ice-blocks" },
      { id: "71164", category: "sausages" },
      { id: "71174", category: "sausages" },
      { id: "71168", category: "sausages" },
      { id: "71165", category: "sausages" },
      { id: "331560", category: "specialty-bread" },
      { id: "679412", category: "herbal-tea" },
      { id: "790129", category: "herbal-tea" },
      { id: "267492", category: "herbal-tea" },
      { id: "267485", category: "herbal-tea" },
      { id: "413302", category: "herbal-tea" },
      { id: "267488", category: "herbal-tea" },
      { id: "760872", category: "herbal-tea" },
      { id: "681177", category: "herbal-tea" },
      { id: "95091", category: "herbal-tea" },
      { id: "761093", category: "black-tea" },
      { id: "721661", category: "green-tea" },
      { id: "790129", category: "herbal-tea" },
      { id: "267492", category: "herbal-tea" },
      { id: "267485", category: "herbal-tea" },
      { id: "721034", category: "herbal-tea" },
      { id: "413302", category: "herbal-tea" },
      { id: "267488", category: "herbal-tea" },
      { id: "760872", category: "herbal-tea" },
      { id: "681177", category: "herbal-tea" },
      { id: "95091.", category: "herbal-tea" },
      { id: "184090", category: "herbal-tea" },
      { id: "761093", category: "black-tea" },
      { id: "721661", category: "green-tea" },
      { id: "690093", category: "green-tea" },
      { id: "780922", category: "sauces" },
      { id: "780921", category: "sauces" },
      { id: "72618", category: "black-tea" },
      { id: "6053", category: "black-tea" },
      { id: "72617", category: "black-tea" },
      { id: "168068", category: "black-tea" },
      { id: "6052", category: "black-tea" },
      { id: "761436", category: "black-tea" }
    ];
  }
});

// src/index.ts
import * as dotenv3 from "./node_modules/dotenv/lib/main.js";
import playwright from "./node_modules/playwright/index.mjs";
import * as cheerio from "./node_modules/cheerio/dist/esm/index.js";
import _ from "./node_modules/lodash/lodash.js";
import { setTimeout as setTimeout2 } from "timers/promises";

// src/mongodb.ts
var import_typings = __toESM(require_typings());
var import_utilities = __toESM(require_utilities());
import { MongoClient, GridFSBucket, ObjectId } from "./node_modules/mongodb/lib/index.js";
import * as dotenv from "./node_modules/dotenv/lib/main.js";
dotenv.config();
dotenv.config({ path: `.env.local`, override: true });
var client;
var db;
var gridFS;
var storeId;
var storesCollection;
var brandsCollection;
var consolidatedProductsCollection;
var priceHistoryCollection;
var categoryHierarchyCollection;
async function establishMongoDB() {
  const connectionString = process.env.MONGODB_CONNECTION_STRING;
  const databaseName = process.env.MONGODB_DATABASE_NAME || "nz-supermarket-scraper";
  if (!connectionString) {
    throw Error("MONGODB_CONNECTION_STRING not set in env");
  }
  const maxRetries = 5;
  const retryDelay = 2e3;
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      (0, import_utilities.log)(import_utilities.colour.yellow, `\u{1F504} MongoDB connection attempt ${attempt}/${maxRetries}...`);
      client = new MongoClient(connectionString, {
        serverSelectionTimeoutMS: 1e4,
        // 10 second timeout
        connectTimeoutMS: 1e4,
        socketTimeoutMS: 0,
        maxPoolSize: 10,
        retryWrites: true,
        retryReads: true
      });
      await client.connect();
      await client.db("admin").command({ ping: 1 });
      db = client.db(databaseName);
      storesCollection = db.collection("stores");
      brandsCollection = db.collection("brands");
      consolidatedProductsCollection = db.collection("consolidatedProducts");
      priceHistoryCollection = db.collection("priceHistory");
      categoryHierarchyCollection = db.collection("categoryHierarchy");
      gridFS = new GridFSBucket(db, { bucketName: "productImages" });
      (0, import_utilities.log)(import_utilities.colour.green, "\u2705 MongoDB connection established");
      await createIndexes();
      return;
    } catch (error) {
      (0, import_utilities.logError)(`\u274C MongoDB connection attempt ${attempt} failed: ${error.message}`);
      if (client) {
        try {
          await client.close();
        } catch (closeError) {
        }
        client = null;
      }
      if (attempt === maxRetries) {
        (0, import_utilities.logError)(`\u274C MongoDB connection failed after ${maxRetries} attempts`);
        (0, import_utilities.logError)("\u{1F4A1} Make sure MongoDB is running: docker start mongodb");
        throw error;
      }
      (0, import_utilities.log)(import_utilities.colour.yellow, `\u23F3 Retrying in ${retryDelay / 1e3} seconds...`);
      await new Promise((resolve) => setTimeout(resolve, retryDelay));
    }
  }
}
async function createIndexes() {
  try {
    await consolidatedProductsCollection.createIndex({
      "displayName": "text",
      "normalizedName": "text",
      "variants.storeName": "text"
    });
    await consolidatedProductsCollection.createIndex({ "categoryId": 1 });
    await consolidatedProductsCollection.createIndex({ "brandId": 1 });
    await consolidatedProductsCollection.createIndex({
      "variants.storeProductId": 1,
      "variants.storeId": 1
    });
    await priceHistoryCollection.createIndex({
      "consolidatedProductId": 1,
      "recordedAt": -1
    });
    await priceHistoryCollection.createIndex({ "year": 1, "month": 1 });
    await categoryHierarchyCollection.createIndex({ "parentId": 1, "sortOrder": 1 });
    await categoryHierarchyCollection.createIndex({ "level": 1, "sortOrder": 1 });
    (0, import_utilities.log)(import_utilities.colour.blue, "\u2705 MongoDB indexes created");
  } catch (error) {
    (0, import_utilities.logError)(`Failed to create indexes: ${error.message}`);
  }
}
async function ensureStoreRow() {
  if (storeId !== void 0) return storeId;
  try {
    const existingStore = await storesCollection.findOne({ storeId: "woolworths" });
    if (existingStore) {
      storeId = existingStore._id;
      return storeId;
    }
    const insertResult = await storesCollection.insertOne({
      storeId: "woolworths",
      name: "Woolworths",
      logoUrl: null,
      createdAt: /* @__PURE__ */ new Date(),
      updatedAt: /* @__PURE__ */ new Date()
    });
    storeId = insertResult.insertedId;
    return storeId;
  } catch (error) {
    (0, import_utilities.logError)(`Failed to ensure store row: ${error.message}`);
    throw error;
  }
}
async function upsertProductToMongoDB(scraped) {
  if (!db) throw Error("MongoDB client not initialised");
  try {
    const sId = await ensureStoreRow();
    const now = /* @__PURE__ */ new Date();
    let consolidatedProduct = await consolidatedProductsCollection.findOne({
      "variants.storeProductId": scraped.id,
      "variants.storeId": sId
    });
    if (!consolidatedProduct) {
      const normalizedName = normalizeProductName(scraped.name, scraped.size);
      (0, import_utilities.log)(import_utilities.colour.cyan, `\u{1F50D} Processing: ${scraped.name} -> normalized: ${normalizedName}`);
      const matchResult = await findBestMatch(normalizedName, scraped.size, scraped.name);
      if (matchResult && matchResult.confidence >= 0.85 && matchResult.product) {
        consolidatedProduct = matchResult.product;
        if (matchResult.matchType !== "exact" && consolidatedProduct) {
          await addProductAlias(consolidatedProduct._id, scraped.name);
        }
        if (consolidatedProduct) {
          const newVariant = {
            storeProductId: scraped.id,
            storeId: sId,
            storeName: scraped.name,
            storeSize: scraped.size || null,
            storeUnitPrice: scraped.unitPrice || null,
            storeUnitName: scraped.unitName || null,
            lastSeen: now,
            isActive: true,
            imageUrl: null
          };
          await consolidatedProductsCollection.updateOne(
            { _id: consolidatedProduct._id },
            {
              $push: { variants: newVariant },
              $set: { updatedAt: now }
            }
          );
          (0, import_utilities.log)(import_utilities.colour.green, `\u2705 Added variant to existing product: ${consolidatedProduct.displayName} (confidence: ${matchResult.confidence.toFixed(3)}, type: ${matchResult.matchType})`);
        }
      } else {
        const newProduct = {
          normalizedName,
          displayName: scraped.name,
          primarySize: scraped.size,
          categoryId: null,
          // TODO: Implement category mapping
          brandId: null,
          // TODO: Implement brand extraction
          matchConfidence: matchResult ? Math.round(matchResult.confidence * 100) : 100,
          manualMatch: false,
          aliases: [],
          // Initialize empty aliases array
          variants: [{
            storeProductId: scraped.id,
            storeId: sId,
            storeName: scraped.name,
            storeSize: scraped.size || null,
            storeUnitPrice: scraped.unitPrice || null,
            storeUnitName: scraped.unitName || null,
            lastSeen: now,
            isActive: true,
            imageUrl: null
          }],
          sizeVariants: scraped.size ? [{
            sizeName: scraped.size,
            sizeWeightGrams: null,
            sizeVolumeMl: null,
            isPrimarySize: true
          }] : [],
          currentPrices: [{
            storeId: sId,
            price: scraped.currentPrice,
            isSpecial: false,
            wasAvailable: true,
            recordedAt: now
          }],
          createdAt: now,
          updatedAt: now
        };
        const insertResult = await consolidatedProductsCollection.insertOne(newProduct);
        consolidatedProduct = { _id: insertResult.insertedId, ...newProduct };
        if (matchResult && matchResult.confidence >= 0.7) {
          (0, import_utilities.log)(import_utilities.colour.blue, `\u{1F195} Created new product (matches below threshold): ${scraped.name} (Best match: ${matchResult.confidence.toFixed(3)})`);
        } else {
          (0, import_utilities.log)(import_utilities.colour.blue, `\u{1F195} Created new consolidated product: ${scraped.name}`);
        }
      }
    } else {
      await consolidatedProductsCollection.updateOne(
        { _id: consolidatedProduct._id },
        {
          $set: {
            "variants.$[variant].lastSeen": now,
            "variants.$[variant].storeUnitPrice": scraped.unitPrice,
            "variants.$[variant].storeUnitName": scraped.unitName,
            "currentPrices.$[price].price": scraped.currentPrice,
            "currentPrices.$[price].recordedAt": now,
            updatedAt: now
          }
        },
        {
          arrayFilters: [
            { "variant.storeProductId": scraped.id },
            { "price.storeId": sId }
          ]
        }
      );
    }
    if (consolidatedProduct) {
      await priceHistoryCollection.insertOne({
        consolidatedProductId: consolidatedProduct._id,
        storeId: sId,
        price: scraped.currentPrice,
        isSpecial: false,
        wasAvailable: true,
        recordedAt: now,
        year: now.getFullYear(),
        month: now.getMonth() + 1
      });
    }
    (0, import_utilities.log)(import_utilities.colour.green, `  Upserted: ${scraped.name.slice(0, 45).padEnd(45)} | $${scraped.currentPrice}`);
    return import_typings.UpsertResponse.PriceChanged;
  } catch (error) {
    (0, import_utilities.logError)(`MongoDB upsert failed: ${error.message}`);
    return import_typings.UpsertResponse.Failed;
  }
}
async function uploadImageToMongoDB(imageUrl, product) {
  if (!gridFS) throw Error("MongoDB GridFS not initialised");
  try {
    const sId = await ensureStoreRow();
    const imageResponse = await fetch(imageUrl);
    if (!imageResponse.ok) {
      (0, import_utilities.logError)(`Failed to download image from ${imageUrl}: ${imageResponse.statusText}`);
      return false;
    }
    const imageBuffer = Buffer.from(await imageResponse.arrayBuffer());
    const existingFiles = await gridFS.find({
      "metadata.productId": product.id
    }).toArray();
    for (const file of existingFiles) {
      await gridFS.delete(file._id);
    }
    const filename = `${product.id}.jpg`;
    const uploadStream = gridFS.openUploadStream(filename, {
      contentType: "image/jpeg",
      metadata: {
        productId: product.id,
        storeId: sId,
        originalUrl: imageUrl,
        uploadedAt: /* @__PURE__ */ new Date()
      }
    });
    await new Promise((resolve, reject) => {
      uploadStream.end(imageBuffer, (error) => {
        if (error) reject(error);
        else resolve(uploadStream.id);
      });
    });
    const imageUrl_gridfs = `gridfs://productImages/${uploadStream.id}`;
    await consolidatedProductsCollection.updateOne(
      { "variants.storeProductId": product.id },
      {
        $set: {
          "variants.$[variant].imageUrl": imageUrl_gridfs
        }
      },
      {
        arrayFilters: [{ "variant.storeProductId": product.id }]
      }
    );
    (0, import_utilities.log)(import_utilities.colour.blue, `  Image uploaded: ${product.id} -> GridFS ${uploadStream.id}`);
    return true;
  } catch (err) {
    (0, import_utilities.logError)(`Image upload error: ${err.message}`);
    return false;
  }
}
var storeDescriptors = [
  "woolworths",
  "countdown",
  "new world",
  "paknsave",
  "pak n save",
  "select",
  "premium",
  "value",
  "budget",
  "signature",
  "essentials",
  "pams",
  "homebrand",
  "signature range",
  "fresh choice",
  "macro",
  "organic",
  "free range",
  "grass fed",
  "natural",
  "artisan",
  "gourmet",
  "deluxe",
  "finest",
  "choice",
  "quality",
  "fresh",
  "pure",
  "real",
  "authentic",
  "traditional",
  "classic"
];
var sizeNormalizations = {
  "grams": "g",
  "gram": "g",
  "kilograms": "kg",
  "kilogram": "kg",
  "kg": "kg",
  "litres": "l",
  "litre": "l",
  "ltr": "l",
  "millilitres": "ml",
  "millilitre": "ml",
  "pieces": "pc",
  "piece": "pc",
  "each": "ea",
  "pack": "pk",
  "packet": "pk"
};
function removeStoreDescriptors(productName) {
  let cleaned = productName.toLowerCase();
  for (const descriptor of storeDescriptors) {
    const regex = new RegExp(`\\b${descriptor}\\b`, "gi");
    cleaned = cleaned.replace(regex, "").trim();
  }
  return cleaned.replace(/\s+/g, " ").trim();
}
function normalizeSize(size) {
  if (!size) return "";
  let normalized = size.toLowerCase().replace(/[^\w\d]/g, "");
  for (const [original, replacement] of Object.entries(sizeNormalizations)) {
    const regex = new RegExp(original, "gi");
    normalized = normalized.replace(regex, replacement);
  }
  return normalized;
}
function calculateSizeSimilarity(size1, size2) {
  if (!size1 || !size2) return 0;
  const normalized1 = normalizeSize(size1);
  const normalized2 = normalizeSize(size2);
  if (normalized1 === normalized2) return 1;
  const num1 = parseFloat(normalized1.replace(/[^\d.]/g, ""));
  const num2 = parseFloat(normalized2.replace(/[^\d.]/g, ""));
  if (!isNaN(num1) && !isNaN(num2)) {
    const ratio = Math.min(num1, num2) / Math.max(num1, num2);
    return ratio > 0.8 ? ratio : 0;
  }
  return calculateSimilarity(normalized1, normalized2);
}
function normalizeProductName(name, size) {
  if (!name) return "";
  let normalized = name.toLowerCase().replace(/'/g, "").replace(/"/g, "").replace(/-/g, " ").replace(/\s+/g, " ").trim();
  normalized = removeStoreDescriptors(normalized);
  const nzBrandMappings = getNZBrandMappings();
  for (const [canonical, variations] of Object.entries(nzBrandMappings)) {
    if (normalized.includes(canonical)) {
      normalized = normalized.replace(canonical, canonical);
      break;
    }
    for (const variation of variations) {
      if (normalized.includes(variation)) {
        normalized = normalized.replace(variation, canonical);
        break;
      }
    }
  }
  if (size) {
    const normalizedSize = normalizeSize(size);
    if (normalizedSize) {
      normalized += ` ${normalizedSize}`;
    }
  }
  return normalized.trim();
}
function getNZBrandMappings() {
  return {
    // === SUPERMARKET PRIVATE LABELS ===
    "pams": ["pam", "pams brand", "pams select"],
    "essentials": ["essentials brand", "countdown essentials"],
    "macro": ["macro brand", "macro organic"],
    "woolworths": ["woolworths brand", "woolworths select"],
    "countdown": ["countdown brand", "countdown select"],
    "homebrand": ["home brand", "homebrand select"],
    "signature": ["signature range", "signature brand"],
    "value": ["value brand", "budget"],
    "fresh choice": ["freshchoice", "fresh choice brand"],
    // === DAIRY BRANDS ===
    "anchor": ["anchor brand", "anchor dairy", "anchor milk", "anchor butter", "anchor cheese"],
    "mainland": ["mainland cheese", "mainland dairy", "mainland brand"],
    "meadowfresh": ["meadow fresh", "meadowfresh milk", "meadow fresh milk"],
    "lewis road": ["lewis road creamery", "lewis road milk", "lewis road butter"],
    "kapiti": ["kapiti cheese", "kapiti ice cream", "kapiti brand"],
    "fernleaf": ["fernleaf milk", "fernleaf powder"],
    "tararua": ["tararua cheese", "tararua dairy"],
    "rolling meadow": ["rolling meadow cheese", "rolling meadow dairy"],
    "whitestone": ["whitestone cheese", "whitestone dairy"],
    "mercer": ["mercer cheese", "mercer dairy"],
    "epicure": ["epicure cheese", "epicure dairy"],
    "kikorangi": ["kikorangi cheese", "kikorangi blue"],
    // === MEAT BRANDS ===
    "tegel": ["tegel chicken", "tegel poultry", "tegel brand"],
    "inghams": ["ingham", "inghams chicken", "inghams poultry"],
    "turks": ["turks poultry", "turks chicken"],
    "brinks": ["brinks chicken", "brinks poultry"],
    "hellers": ["heller", "hellers bacon", "hellers sausages", "hellers smallgoods"],
    "beehive": ["beehive bacon", "beehive ham", "beehive smallgoods"],
    "farmland": ["farmland bacon", "farmland ham"],
    "primo": ["primo smallgoods", "primo bacon"],
    "hans": ["hans smallgoods", "hans continental"],
    "continental deli": ["continental smallgoods", "continental deli"],
    // === BREAD & BAKERY BRANDS ===
    "tip top": ["tiptop", "tip top bread", "tiptop bread"],
    "molenberg": ["molenberg bread", "molenburg", "molenberg wholemeal"],
    "vogels": ["vogel", "vogels bread", "vogel bread", "vogels original"],
    "freyas": ["freya", "freyas bread", "freya bread"],
    "natures fresh": ["nature fresh", "natures fresh bread"],
    "burgen": ["burgen bread", "burgen soy lin"],
    "ploughmans": ["ploughman", "ploughmans bread"],
    "golden": ["golden bread", "golden bakery"],
    "bakers delight": ["bakersdelight", "bakers delight bread"],
    // === BEVERAGE BRANDS ===
    "coke": ["coca cola", "coca-cola", "coke classic", "coca cola classic"],
    "coke zero": ["coca cola zero", "coke zero sugar", "coca cola zero sugar"],
    "diet coke": ["coca cola diet", "diet coca cola"],
    "pepsi": ["pepsi cola", "pepsi classic", "pepsi original"],
    "pepsi max": ["pepsi maximum taste", "pepsi max no sugar"],
    "fanta": ["fanta orange", "fanta grape"],
    "sprite": ["sprite lemon", "sprite lime"],
    "l&p": ["lemon paeroa", "lemon and paeroa", "l and p"],
    "just juice": ["justjuice", "just juice brand"],
    "fresh up": ["freshup", "fresh up juice"],
    "keri": ["keri juice", "keri fresh"],
    "charlies": ["charlie", "charlies juice", "charlies honest"],
    "phoenix": ["phoenix organic", "phoenix juice"],
    "pump": ["pump water", "pump brand"],
    "powerade": ["powerade sports drink"],
    "gatorade": ["gatorade sports drink"],
    // === CEREAL & BREAKFAST BRANDS ===
    "sanitarium": ["sanitarium weetbix", "sanitarium so good"],
    "weetbix": ["weet bix", "wheat biscuits", "sanitarium weetbix"],
    "uncle tobys": ["uncle toby", "uncle tobys oats", "uncle tobys muesli"],
    "kelloggs": ["kellogg", "kelloggs cornflakes", "kelloggs special k"],
    "cornflakes": ["corn flakes", "kelloggs cornflakes"],
    "nutrigrain": ["nutri grain", "kelloggs nutrigrain"],
    "special k": ["specialk", "kelloggs special k"],
    "hubbards": ["hubbards cereal", "hubbards muesli"],
    // === SNACK & CONFECTIONERY BRANDS ===
    "cadbury": ["cadburys", "cadbury chocolate"],
    "whittakers": ["whittaker", "whittakers chocolate"],
    "nestle": ["nestl\xE9", "nestle chocolate"],
    "mars": ["mars bar", "mars chocolate"],
    "snickers": ["snickers bar", "snickers chocolate"],
    "kit kat": ["kitkat", "kit-kat"],
    "twix": ["twix bar", "twix chocolate"],
    "moro": ["moro bar", "moro chocolate"],
    "picnic": ["picnic bar", "picnic chocolate"],
    "crunchie": ["crunchie bar", "crunchie chocolate"],
    "bluebird": ["bluebird chips", "bluebird snacks"],
    "eta": ["eta chips", "eta snacks", "eta peanut butter"],
    "proper": ["proper chips", "proper crisps"],
    "heartland": ["heartland chips", "heartland snacks"],
    "griffins": ["griffin", "griffins biscuits", "griffin biscuits"],
    "arnott": ["arnotts", "arnott biscuits", "arnotts biscuits"],
    "toffee pops": ["toffeepops", "griffins toffee pops"],
    "mallowpuffs": ["mallow puffs", "griffins mallowpuffs"],
    "pics": ["pic", "pic peanut butter", "pics peanut butter"],
    "cottees": ["cottee", "cottees jam", "cottee jam"],
    // === PANTRY & COOKING BRANDS ===
    "watties": ["wattie", "watties tomatoes", "watties sauce"],
    "heinz": ["heinz beans", "heinz tomato sauce", "heinz soup"],
    "maggi": ["maggi noodles", "maggi soup", "maggi instant"],
    "continental soup": ["continental soup", "continental pasta"],
    "mccains": ["mccain", "mccains chips", "mccains frozen"],
    "edgell": ["edgell vegetables", "edgell canned"],
    "greggs": ["greggs coffee", "greggs instant"],
    "nescafe": ["nescaf\xE9", "nescafe coffee"],
    "moccona": ["moccona coffee", "moccona instant"],
    "robert harris": ["robertharris", "robert harris coffee"],
    "bell tea": ["bell tea bags", "bell black tea"],
    "dilmah": ["dilmah tea", "dilmah ceylon"],
    "olivani": ["olivani oil", "olivani olive oil"],
    "bertolli": ["bertolli oil", "bertolli olive oil"],
    "praise": ["praise mayonnaise", "praise dressing"],
    "best foods": ["bestfoods", "best foods mayo"],
    "masterfoods": ["master foods", "masterfoods sauce"],
    "fountain": ["fountain sauce", "fountain tomato sauce"],
    // === FROZEN FOOD BRANDS ===
    "birds eye": ["birdseye", "birds eye vegetables", "birds eye fish"],
    "talley": ["talleys", "talley vegetables", "talley frozen"],
    // === CLEANING & HOUSEHOLD BRANDS ===
    "janola": ["janola bleach", "janola cleaning"],
    "earthwise": ["earthwise cleaning", "earthwise eco"],
    "finish": ["finish dishwasher", "finish tablets"],
    "ajax": ["ajax spray", "ajax cleaning"],
    "jif": ["jif cream cleanser", "jif bathroom"],
    "domestos": ["domestos bleach", "domestos toilet"],
    "toilet duck": ["toiletduck", "toilet duck cleaner"],
    "mr muscle": ["mrmuscle", "mr muscle bathroom"],
    "windex": ["windex glass", "windex cleaner"],
    "persil": ["persil washing powder", "persil liquid"],
    "surf": ["surf washing powder", "surf liquid"],
    "omo": ["omo washing powder", "omo liquid"],
    "cold power": ["coldpower", "cold power liquid"],
    "dynamo": ["dynamo washing liquid"],
    "sorbent": ["sorbent toilet paper", "sorbent tissues"],
    "kleenex": ["kleenex tissues", "kleenex toilet paper"],
    "quilton": ["quilton toilet paper", "quilton tissues"],
    "treasures": ["treasures toilet paper", "treasures tissues"],
    // === HEALTH & BEAUTY BRANDS ===
    "colgate": ["colgate toothpaste", "colgate toothbrush"],
    "oral b": ["oral-b", "oralb", "oral b toothbrush"],
    "sensodyne": ["sensodyne toothpaste"],
    "macleans": ["macleans toothpaste"],
    "head shoulders": ["head and shoulders", "head & shoulders"],
    "pantene": ["pantene shampoo", "pantene conditioner"],
    "herbal essences": ["herbal essence", "herbal essences shampoo"],
    "dove": ["dove soap", "dove body wash"],
    "nivea": ["nivea cream", "nivea body"],
    "vaseline": ["vaseline petroleum jelly"],
    // === BABY & PERSONAL CARE ===
    "huggies": ["huggies nappies", "huggies diapers"],
    "pampers": ["pampers nappies", "pampers diapers"],
    "johnson": ["johnsons", "johnson baby", "johnsons baby"],
    "bepanthen": ["bepanthen cream", "bepanthen nappy"],
    // === PET FOOD BRANDS ===
    "pedigree": ["pedigree dog food", "pedigree dry"],
    "whiskas": ["whiskas cat food", "whiskas wet"],
    "fancy feast": ["fancyfeast", "fancy feast cat"],
    "royal canin": ["royalcanin", "royal canin dog"],
    "hills": ["hills pet food", "hills science diet"],
    "eukanuba": ["eukanuba dog food"],
    "iams": ["iams pet food", "iams dog"],
    "optimum": ["optimum dog food", "optimum pet"],
    "tux": ["tux cat food", "tux pet"],
    "champ": ["champ dog food", "champ pet"],
    // === ADDITIONAL NZ SPECIFIC BRANDS ===
    "tip top ice cream": ["tiptop ice cream", "tip top icecream"],
    "new zealand natural": ["nz natural", "new zealand natural ice cream"],
    "deep south": ["deep south ice cream", "deep south icecream"],
    "barkers": ["barkers jam", "barkers preserves"],
    "san remo": ["sanremo", "san remo pasta"],
    "latina": ["latina pasta", "latina fresh"],
    "uncle bens": ["uncle ben", "uncle bens rice"],
    "sunrice": ["sun rice", "sunrice brand"],
    "campbells": ["campbell", "campbells soup", "campbell soup"],
    "delmaine": ["delmaine vegetables", "delmaine canned"],
    "john west": ["johnwest", "john west tuna", "johnwest tuna"],
    "sirena": ["sirena tuna", "sirena seafood"],
    "edmonds": ["edmonds flour", "edmonds baking"],
    "champion": ["champion flour", "champion baking"],
    "chelsea": ["chelsea sugar", "chelsea baking"]
  };
}
function levenshteinDistance(s1, s2) {
  if (!s1) return s2 ? s2.length : 0;
  if (!s2) return s1.length;
  const matrix = [];
  for (let i = 0; i <= s2.length; i++) {
    matrix[i] = [i];
  }
  for (let j = 0; j <= s1.length; j++) {
    matrix[0][j] = j;
  }
  for (let i = 1; i <= s2.length; i++) {
    for (let j = 1; j <= s1.length; j++) {
      const cost = s1[j - 1] === s2[i - 1] ? 0 : 1;
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,
        matrix[i][j - 1] + 1,
        matrix[i - 1][j - 1] + cost
      );
    }
  }
  return matrix[s2.length][s1.length];
}
function jaccardSimilarity(str1, str2) {
  const set1 = new Set(str1.toLowerCase().split(/\s+/).filter((w) => w.length > 1));
  const set2 = new Set(str2.toLowerCase().split(/\s+/).filter((w) => w.length > 1));
  const intersection = new Set([...set1].filter((x) => set2.has(x)));
  const union = /* @__PURE__ */ new Set([...set1, ...set2]);
  return union.size === 0 ? 0 : intersection.size / union.size;
}
function calculateSimilarity(name1, name2, brand1, brand2) {
  if (!name1 || !name2) return 0;
  if (name1 === name2) return 1;
  const normalized1 = normalizeProductName(name1, void 0);
  const normalized2 = normalizeProductName(name2, void 0);
  if (normalized1 === normalized2) return 1;
  const nzBrandMappings = getNZBrandMappings();
  let mapping1 = null;
  let mapping2 = null;
  for (const [canonical, variations] of Object.entries(nzBrandMappings)) {
    if (normalized1.includes(canonical) || variations.some((v) => normalized1.includes(v))) {
      mapping1 = canonical;
    }
    if (normalized2.includes(canonical) || variations.some((v) => normalized2.includes(v))) {
      mapping2 = canonical;
    }
  }
  if (mapping1 && mapping2 && mapping1 === mapping2) return 0.95;
  const distance = levenshteinDistance(normalized1, normalized2);
  const maxLength = Math.max(normalized1.length, normalized2.length);
  const levenshteinSim = maxLength === 0 ? 1 : Math.max(0, 1 - distance / maxLength);
  const jaccardSim = jaccardSimilarity(normalized1, normalized2);
  let combinedSimilarity = levenshteinSim * 0.6 + jaccardSim * 0.4;
  const words1 = normalized1.split(" ").filter((w) => w.length > 2);
  const words2 = normalized2.split(" ").filter((w) => w.length > 2);
  const commonWords = words1.filter((w) => words2.includes(w));
  if (commonWords.length > 0) {
    const wordBoost = commonWords.length / Math.max(words1.length, words2.length) * 0.2;
    combinedSimilarity = Math.min(1, combinedSimilarity + wordBoost);
  }
  const lengthRatio = Math.min(normalized1.length, normalized2.length) / Math.max(normalized1.length, normalized2.length);
  if (lengthRatio < 0.5) {
    combinedSimilarity *= 0.8;
  }
  return combinedSimilarity;
}
async function findBestMatch(normalizedName, size, originalName) {
  try {
    (0, import_utilities.log)(import_utilities.colour.cyan, `\u{1F50D} Finding match for: "${originalName}" -> normalized: "${normalizedName}"`);
    const exactMatch = await consolidatedProductsCollection.findOne({
      normalizedName
    });
    if (exactMatch) {
      (0, import_utilities.log)(import_utilities.colour.green, `\u2705 Exact match found: ${exactMatch.displayName}`);
      return { product: exactMatch, confidence: 1, matchType: "exact" };
    }
    const nzBrandMappings = getNZBrandMappings();
    for (const [canonical, variations] of Object.entries(nzBrandMappings)) {
      if (normalizedName.includes(canonical) || variations.some((v) => normalizedName.includes(v))) {
        const manualMatch = await consolidatedProductsCollection.findOne({
          normalizedName: canonical
        });
        if (manualMatch) {
          (0, import_utilities.log)(import_utilities.colour.green, `\u2705 Manual mapping match found: ${manualMatch.displayName}`);
          return { product: manualMatch, confidence: 0.95, matchType: "manual" };
        }
      }
    }
    const allProducts = await consolidatedProductsCollection.find({}).limit(1e3).toArray();
    let bestMatch = null;
    let bestScore = 0;
    let matchType = "fuzzy";
    const threshold = 0.8;
    for (const product of allProducts) {
      let score = calculateSimilarity(normalizedName, product.normalizedName);
      if (product.aliases && Array.isArray(product.aliases)) {
        for (const alias of product.aliases) {
          const aliasScore = calculateSimilarity(normalizedName, alias);
          if (aliasScore > score) {
            score = aliasScore;
            matchType = "alias";
          }
        }
      }
      if (size && product.primarySize) {
        const sizeScore = calculateSizeSimilarity(size, product.primarySize);
        if (sizeScore > 0.8) {
          score += 0.05;
        }
      }
      if (score > bestScore && score >= threshold) {
        bestScore = score;
        bestMatch = product;
      }
    }
    if (bestMatch) {
      (0, import_utilities.log)(import_utilities.colour.yellow, `\u2705 Fuzzy match found: ${bestMatch.displayName} (confidence: ${bestScore.toFixed(3)}, type: ${matchType})`);
      return {
        product: bestMatch,
        confidence: Math.min(1, bestScore),
        matchType,
        existingConfidence: bestMatch.matchConfidence
      };
    }
    (0, import_utilities.log)(import_utilities.colour.red, `\u274C No match found for: ${originalName}`);
    return null;
  } catch (error) {
    (0, import_utilities.logError)(`\u274C Error finding match: ${error.message}`);
    return null;
  }
}
async function addProductAlias(consolidatedProductId, newAlias) {
  try {
    const normalizedAlias = normalizeProductName(newAlias);
    if (!normalizedAlias || normalizedAlias.trim() === "") return;
    (0, import_utilities.log)(import_utilities.colour.cyan, `\u{1F4DD} Attempting to add alias: "${newAlias}" -> normalized: "${normalizedAlias}"`);
    const product = await consolidatedProductsCollection.findOne({ _id: consolidatedProductId });
    if (!product) {
      (0, import_utilities.log)(import_utilities.colour.red, `\u274C Product not found for alias addition: ${consolidatedProductId}`);
      return;
    }
    const currentAliases = product.aliases || [];
    if (normalizedAlias === product.normalizedName) {
      (0, import_utilities.log)(import_utilities.colour.yellow, `\u26A0\uFE0F Alias matches normalized name, skipping: ${normalizedAlias}`);
      return;
    }
    if (currentAliases.includes(normalizedAlias)) {
      (0, import_utilities.log)(import_utilities.colour.yellow, `\u26A0\uFE0F Alias already exists, skipping: ${normalizedAlias}`);
      return;
    }
    currentAliases.push(normalizedAlias);
    await consolidatedProductsCollection.updateOne(
      { _id: consolidatedProductId },
      {
        $set: {
          aliases: currentAliases,
          updatedAt: /* @__PURE__ */ new Date()
        }
      }
    );
    (0, import_utilities.log)(import_utilities.colour.cyan, `\u2705 Added alias '${normalizedAlias}' to product: ${product.displayName}`);
  } catch (error) {
    (0, import_utilities.logError)(`\u274C Error adding alias: ${error.message}`);
  }
}
async function closeMongoDB() {
  if (client) {
    await client.close();
    (0, import_utilities.log)(import_utilities.colour.blue, "MongoDB connection closed");
  }
}

// src/consolidated-products-mongodb.ts
var import_utilities2 = __toESM(require_utilities());
import { MongoClient as MongoClient2, ObjectId as ObjectId2 } from "./node_modules/mongodb/lib/index.js";
import * as dotenv2 from "./node_modules/dotenv/lib/main.js";
dotenv2.config();
var client2;
var db2;
var consolidatedProductsCollection2;
var brandsCollection2;
var categoryHierarchyCollection2;
async function initializeConsolidatedProductsMongoDB() {
  const connectionString = process.env.MONGODB_CONNECTION_STRING;
  const databaseName = process.env.MONGODB_DATABASE_NAME || "nz-supermarket-scraper";
  if (!connectionString) {
    throw Error("MONGODB_CONNECTION_STRING not set in env");
  }
  try {
    client2 = new MongoClient2(connectionString);
    await client2.connect();
    db2 = client2.db(databaseName);
    consolidatedProductsCollection2 = db2.collection("consolidatedProducts");
    brandsCollection2 = db2.collection("brands");
    categoryHierarchyCollection2 = db2.collection("categoryHierarchy");
    (0, import_utilities2.log)(import_utilities2.colour.green, "\u2705 Consolidated products MongoDB initialized");
    await ensureBasicCategories();
  } catch (error) {
    (0, import_utilities2.logError)(`Failed to initialize consolidated products MongoDB: ${error.message}`);
    throw error;
  }
}
async function ensureBasicCategories() {
  try {
    const existingCategories = await categoryHierarchyCollection2.countDocuments();
    if (existingCategories === 0) {
      const mainCategories = [
        { name: "Fresh Foods", parentId: null, level: 0, sortOrder: 1 },
        { name: "Chilled & Frozen", parentId: null, level: 0, sortOrder: 2 },
        { name: "Pantry & Dry Goods", parentId: null, level: 0, sortOrder: 3 },
        { name: "Beverages", parentId: null, level: 0, sortOrder: 4 },
        { name: "Health & Household", parentId: null, level: 0, sortOrder: 5 }
      ];
      const insertResult = await categoryHierarchyCollection2.insertMany(mainCategories);
      (0, import_utilities2.log)(import_utilities2.colour.blue, `\u2705 Created ${insertResult.insertedCount} main categories`);
      const freshFoodsId = Object.values(insertResult.insertedIds).find(async (id) => {
        const cat = await categoryHierarchyCollection2.findOne({ _id: id });
        return cat?.name === "Fresh Foods";
      });
      (0, import_utilities2.log)(import_utilities2.colour.blue, "\u2705 Basic category structure created");
    }
  } catch (error) {
    (0, import_utilities2.logError)(`Failed to ensure basic categories: ${error.message}`);
  }
}
async function processConsolidatedProductMongoDB(scrapedProduct) {
  if (!consolidatedProductsCollection2) {
    (0, import_utilities2.logError)("Consolidated products collection not initialized");
    return null;
  }
  try {
    const normalizedName = normalizeProductName2(scrapedProduct.name, scrapedProduct.size);
    const now = /* @__PURE__ */ new Date();
    const existingProduct = await consolidatedProductsCollection2.findOne({
      "variants.storeProductId": scrapedProduct.id
    });
    if (existingProduct) {
      await consolidatedProductsCollection2.updateOne(
        { _id: existingProduct._id },
        {
          $set: {
            "variants.$[variant].lastSeen": now,
            "variants.$[variant].storeUnitPrice": scrapedProduct.unitPrice,
            "variants.$[variant].storeUnitName": scrapedProduct.unitName,
            updatedAt: now
          }
        },
        {
          arrayFilters: [{ "variant.storeProductId": scrapedProduct.id }]
        }
      );
      (0, import_utilities2.log)(import_utilities2.colour.cyan, `  Updated consolidated product: ${scrapedProduct.name.slice(0, 40)}`);
      return existingProduct._id.toString();
    } else {
      const newProduct = {
        normalizedName,
        displayName: scrapedProduct.name,
        primarySize: scrapedProduct.size,
        categoryId: null,
        // TODO: Implement category mapping
        brandId: null,
        // TODO: Implement brand extraction
        matchConfidence: 100,
        manualMatch: false,
        variants: [{
          storeProductId: scrapedProduct.id,
          storeId: "woolworths",
          // Store identifier
          storeName: scrapedProduct.name,
          storeSize: scrapedProduct.size,
          storeUnitPrice: scrapedProduct.unitPrice,
          storeUnitName: scrapedProduct.unitName,
          lastSeen: now,
          isActive: true,
          imageUrl: null
        }],
        sizeVariants: scrapedProduct.size ? [{
          sizeName: scrapedProduct.size,
          sizeWeightGrams: null,
          sizeVolumeMl: null,
          isPrimarySize: true
        }] : [],
        currentPrices: [{
          storeId: "woolworths",
          price: scrapedProduct.currentPrice,
          isSpecial: false,
          wasAvailable: true,
          recordedAt: now
        }],
        createdAt: now,
        updatedAt: now
      };
      const insertResult = await consolidatedProductsCollection2.insertOne(newProduct);
      (0, import_utilities2.log)(import_utilities2.colour.cyan, `  Created consolidated product: ${scrapedProduct.name.slice(0, 40)}`);
      return insertResult.insertedId.toString();
    }
  } catch (error) {
    (0, import_utilities2.logError)(`Failed to process consolidated product: ${error.message}`);
    return null;
  }
}
function normalizeProductName2(name, size) {
  let normalized = name.toLowerCase().replace(/[^a-z0-9\s]/g, " ").replace(/\s+/g, "_").trim();
  if (size) {
    const normalizedSize = size.toLowerCase().replace(/[^a-z0-9]/g, "").trim();
    normalized += "_" + normalizedSize;
  }
  return normalized;
}
async function closeConsolidatedProductsMongoDB() {
  try {
    if (client2) {
      await client2.close();
    }
    (0, import_utilities2.log)(import_utilities2.colour.blue, "\u2705 Consolidated products MongoDB connections closed");
  } catch (error) {
    (0, import_utilities2.logError)(`Failed to close MongoDB connections: ${error.message}`);
  }
}

// src/index.ts
var import_product_overrides = __toESM(require_product_overrides());
var import_utilities3 = __toESM(require_utilities());
dotenv3.config();
dotenv3.config({ path: `.env.local`, override: true });
var pageLoadDelaySeconds = 7;
var productLogDelayMilliSeconds = 20;
var startTime = Date.now();
var categorisedUrls = loadUrlsFile();
var databaseMode = false;
var uploadImagesMode = false;
var headlessMode = true;
categorisedUrls = await handleArguments(categorisedUrls);
if (databaseMode) {
  await establishMongoDB();
  await initializeConsolidatedProductsMongoDB();
}
var browser;
var page;
browser = await establishPlaywrightPage(headlessMode);
if (process.env.SKIP_STORE_SELECTION !== "true") {
  try {
    await selectStoreByLocationName();
  } catch (error) {
    (0, import_utilities3.logError)(`Store selection failed: ${error}`);
    (0, import_utilities3.log)(import_utilities3.colour.yellow, "Continuing with default store location...");
  }
} else {
  (0, import_utilities3.log)(import_utilities3.colour.yellow, "Store selection skipped (SKIP_STORE_SELECTION=true)");
}
await scrapeAllPageURLs();
await browser.close();
if (databaseMode) {
  await closeConsolidatedProductsMongoDB();
  await closeMongoDB();
}
(0, import_utilities3.log)(
  import_utilities3.colour.sky,
  `
All Pages Completed = Total Time Elapsed ${(0, import_utilities3.getTimeElapsedSince)(startTime)} 
`
);
function loadUrlsFile(filePath = "src/urls.txt") {
  const rawLinesFromFile = (0, import_utilities3.readLinesFromTextFile)(filePath);
  let categorisedUrls2 = [];
  rawLinesFromFile.map((line) => {
    let parsedUrls = parseAndCategoriseURL(line);
    if (parsedUrls !== void 0) {
      categorisedUrls2 = [...categorisedUrls2, ...parsedUrls];
    }
  });
  return categorisedUrls2;
}
async function scrapeAllPageURLs() {
  (0, import_utilities3.log)(
    import_utilities3.colour.yellow,
    `${categorisedUrls.length} pages to be scraped`.padEnd(35) + `${pageLoadDelaySeconds}s delay between scrapes`.padEnd(35) + (databaseMode ? "(Database Mode)" : "(Dry Run Mode)")
  );
  for (let i = 0; i < categorisedUrls.length; i++) {
    const categorisedUrl = categorisedUrls[i];
    let url = categorisedUrls[i].url;
    const shortUrl = url.replace("https://", "");
    (0, import_utilities3.log)(
      import_utilities3.colour.yellow,
      `
[${i + 1}/${categorisedUrls.length}] ${shortUrl}`
    );
    try {
      await page.goto(url);
      for (let pageDown = 0; pageDown < 5; pageDown++) {
        const timeBetweenPgDowns = Math.random() * 1e3 + 500;
        await page.waitForTimeout(timeBetweenPgDowns);
        await page.keyboard.press("PageDown");
      }
      await page.setDefaultTimeout(3e4);
      await page.waitForSelector("product-price h3");
      const html = await page.innerHTML("product-grid");
      const $ = cheerio.load(html);
      const allProductEntries = $("cdx-card product-stamp-grid div.product-entry");
      const advertisementEntries = $("div.carousel-track div cdx-card product-stamp-grid div.product-entry");
      const adHrefs = advertisementEntries.map((index, element) => {
        return $(element).find("a").first().attr("href");
      }).toArray();
      const productEntries = allProductEntries.filter((index, element) => {
        const productHref = $(element).find("a").first().attr("href");
        return !adHrefs.includes(productHref);
      });
      (0, import_utilities3.log)(
        import_utilities3.colour.yellow,
        `${productEntries.length} product entries found`.padEnd(35) + `Time Elapsed: ${(0, import_utilities3.getTimeElapsedSince)(startTime)}`.padEnd(35) + `Category: ${_.startCase(categorisedUrl.categories.join(" - "))}`
      );
      if (!databaseMode) (0, import_utilities3.logTableHeader)();
      let perPageLogStats = {
        newProducts: 0,
        priceChanged: 0,
        infoUpdated: 0,
        alreadyUpToDate: 0
      };
      perPageLogStats = await processFoundProductEntries(categorisedUrl, productEntries, perPageLogStats);
      if (databaseMode) {
        (0, import_utilities3.log)(
          import_utilities3.colour.blue,
          `Supabase: ${perPageLogStats.newProducts} new products, ${perPageLogStats.priceChanged} updated prices, ${perPageLogStats.infoUpdated} updated info, ${perPageLogStats.alreadyUpToDate} already up-to-date`
        );
      }
      await setTimeout2(pageLoadDelaySeconds * 1e3);
    } catch (error) {
      if (typeof error === "string") {
        if (error.includes("NS_ERROR_CONNECTION_REFUSED")) {
          (0, import_utilities3.logError)("Connection Failed - Check Firewall\n" + error);
          return;
        }
      }
      (0, import_utilities3.logError)(
        "Page Timeout after 30 seconds - Skipping this page\n" + error
      );
    }
  }
}
async function processFoundProductEntries(categorisedUrl, productEntries, perPageLogStats) {
  for (let i = 0; i < productEntries.length; i++) {
    const productEntryElement = productEntries[i];
    const product = playwrightElementToProduct(
      productEntryElement,
      categorisedUrl.categories
    );
    if (databaseMode && product !== void 0) {
      const response = await upsertProductToMongoDB(product);
      const consolidatedProductId = await processConsolidatedProductMongoDB(product);
      switch (response) {
        case 3 /* AlreadyUpToDate */:
          perPageLogStats.alreadyUpToDate++;
          break;
        case 2 /* InfoChanged */:
          perPageLogStats.infoUpdated++;
          break;
        case 0 /* NewProduct */:
          perPageLogStats.newProducts++;
          break;
        case 1 /* PriceChanged */:
          perPageLogStats.priceChanged++;
          break;
        default:
          break;
      }
      if (uploadImagesMode) {
        const imageUrlBase = "https://assets.woolworths.com.au/images/2010/";
        const imageUrlExtensionAndQueryParams = ".jpg?impolicy=wowcdxwbjbx&w=900&h=900";
        const imageUrl = imageUrlBase + product.id + imageUrlExtensionAndQueryParams;
        await uploadImageToMongoDB(imageUrl, product);
      }
    } else if (!databaseMode && product !== void 0) {
      (0, import_utilities3.logProductRow)(product);
    }
    await setTimeout2(productLogDelayMilliSeconds);
  }
  return perPageLogStats;
}
function handleArguments(categorisedUrls2) {
  if (process.argv.length > 2) {
    const userArgs = process.argv.slice(2, process.argv.length);
    let potentialUrl = "";
    userArgs.forEach(async (arg) => {
      if (arg === "db") databaseMode = true;
      else if (arg === "images") uploadImagesMode = true;
      else if (arg === "headless") headlessMode = true;
      else if (arg === "headed") headlessMode = false;
      else if (arg.includes(".co.nz")) potentialUrl += arg;
      else if (arg === "reverse") categorisedUrls2 = categorisedUrls2.reverse();
    });
    const parsedUrl = parseAndCategoriseURL(potentialUrl);
    if (parsedUrl !== void 0) categorisedUrls2 = [parsedUrl];
  }
  return categorisedUrls2;
}
async function establishPlaywrightPage(headless = true) {
  (0, import_utilities3.log)(
    import_utilities3.colour.yellow,
    "Launching Browser.. " + (process.argv.length > 2 ? "(" + (process.argv.length - 2) + " arguments found)" : "")
  );
  browser = await playwright.firefox.launch({
    headless
  });
  page = await browser.newPage();
  await routePlaywrightExclusions();
  return browser;
}
async function selectStoreByLocationName(locationName = "") {
  if (locationName === "") {
    if (process.env.STORE_NAME) locationName = process.env.STORE_NAME;
    else {
      (0, import_utilities3.log)(import_utilities3.colour.yellow, "No store location specified - using default location");
      return;
    }
  }
  (0, import_utilities3.log)(import_utilities3.colour.yellow, `Attempting to select store location: ${locationName}`);
  try {
    await page.setDefaultTimeout(15e3);
    await page.goto("https://www.woolworths.co.nz/shop/browse", {
      waitUntil: "domcontentloaded"
    });
    await page.waitForTimeout(2e3);
    const possibleSelectors = [
      'button[data-testid="store-selector"]',
      'button[aria-label*="store"]',
      'button[aria-label*="location"]',
      '[data-testid="change-store"]',
      'button:has-text("Change store")',
      'button:has-text("Select store")',
      ".store-selector button",
      '[class*="store"] button',
      "fieldset div div p button"
    ];
    let storeButton = null;
    for (const selector of possibleSelectors) {
      try {
        await page.waitForSelector(selector, { timeout: 3e3 });
        storeButton = page.locator(selector).first();
        (0, import_utilities3.log)(import_utilities3.colour.green, `Found store selector: ${selector}`);
        break;
      } catch {
      }
    }
    if (!storeButton) {
      (0, import_utilities3.log)(import_utilities3.colour.yellow, "No store selector found - proceeding with default location");
      return;
    }
    await storeButton.click();
    await page.waitForTimeout(1e3);
    const inputSelectors = [
      'input[placeholder*="suburb"]',
      'input[placeholder*="location"]',
      'input[placeholder*="address"]',
      'input[type="text"]',
      "form-suburb-autocomplete form-input input",
      '[data-testid="location-input"]',
      ".location-input input",
      'input[aria-label*="location"]'
    ];
    let locationInput = null;
    for (const selector of inputSelectors) {
      try {
        await page.waitForSelector(selector, { timeout: 3e3 });
        locationInput = page.locator(selector).first();
        (0, import_utilities3.log)(import_utilities3.colour.green, `Found location input: ${selector}`);
        break;
      } catch {
      }
    }
    if (!locationInput) {
      (0, import_utilities3.log)(import_utilities3.colour.yellow, "No location input found - proceeding with default location");
      return;
    }
    await locationInput.clear();
    await locationInput.fill(locationName);
    await page.waitForTimeout(2e3);
    try {
      const suggestionSelectors = [
        '[role="option"]:first-child',
        ".suggestion:first-child",
        ".autocomplete-item:first-child",
        "li:first-child",
        '[data-testid="suggestion"]:first-child'
      ];
      let suggestionFound = false;
      for (const selector of suggestionSelectors) {
        try {
          await page.waitForSelector(selector, { timeout: 2e3 });
          await page.locator(selector).click();
          suggestionFound = true;
          (0, import_utilities3.log)(import_utilities3.colour.green, `Selected suggestion using: ${selector}`);
          break;
        } catch {
        }
      }
      if (!suggestionFound) {
        await page.keyboard.press("ArrowDown");
        await page.waitForTimeout(300);
        await page.keyboard.press("Enter");
        (0, import_utilities3.log)(import_utilities3.colour.yellow, "Used keyboard navigation to select location");
      }
      await page.waitForTimeout(1e3);
      const saveSelectors = [
        'button:has-text("Save")',
        'button:has-text("Continue")',
        'button:has-text("Confirm")',
        'button:has-text("Select")',
        '[data-testid="save-location"]',
        ".save-button",
        'button[type="submit"]'
      ];
      for (const selector of saveSelectors) {
        try {
          await page.waitForSelector(selector, { timeout: 2e3 });
          await page.locator(selector).click();
          (0, import_utilities3.log)(import_utilities3.colour.green, `Clicked save button: ${selector}`);
          break;
        } catch {
        }
      }
      await page.waitForTimeout(2e3);
      (0, import_utilities3.log)(import_utilities3.colour.green, `Successfully changed location to: ${locationName}`);
    } catch (error) {
      (0, import_utilities3.log)(import_utilities3.colour.yellow, `Could not select location suggestion - using typed location: ${error}`);
    }
  } catch (error) {
    (0, import_utilities3.logError)(`Store location selection failed: ${error}`);
    (0, import_utilities3.log)(import_utilities3.colour.yellow, "Proceeding with default location");
  }
}
function playwrightElementToProduct(element, categories) {
  const $ = cheerio.load(element);
  let idNameSizeH3 = $(element).find("h3").filter((i, element2) => {
    if ($(element2).attr("id")?.includes("-title")) {
      return true;
    } else return false;
  });
  let product = {
    // ID
    // -------
    // Extract product ID from h3 id attribute, and remove non-numbers
    id: idNameSizeH3.attr("id")?.replace(/\D/g, ""),
    // Source Site - set where the source of information came from
    sourceSite: "countdown.co.nz",
    // use countdown for consistency with old data
    // Categories
    category: categories,
    // already obtained from url/text file
    // Store today's date
    lastChecked: /* @__PURE__ */ new Date(),
    lastUpdated: /* @__PURE__ */ new Date(),
    // These values will later be overwritten
    name: "",
    priceHistory: [],
    currentPrice: 0
  };
  let rawNameAndSize = idNameSizeH3.text().trim();
  rawNameAndSize = rawNameAndSize.toLowerCase().replace("  ", " ").replace("fresh fruit", "").replace("fresh vegetable", "").trim();
  let tryMatchSize = rawNameAndSize.match(/(tray\s\d+)|(\d+(\.\d+)?(\-\d+\.\d+)?\s?(g|kg|l|ml|pack))\b/g);
  if (!tryMatchSize) {
    product.name = (0, import_utilities3.toTitleCase)(rawNameAndSize);
    product.size = "";
  } else {
    let indexOfSizeSection = rawNameAndSize.indexOf(tryMatchSize[0]);
    product.name = (0, import_utilities3.toTitleCase)(rawNameAndSize.slice(0, indexOfSizeSection)).trim();
    let cleanedSize = rawNameAndSize.slice(indexOfSizeSection).trim();
    if (cleanedSize.match(/\d+l\b/)) {
      cleanedSize = cleanedSize.replace("l", "L");
    }
    cleanedSize.replace("tray", "Tray");
    product.size = cleanedSize;
  }
  const dollarString = $(element).find("product-price div h3 em").text().trim();
  let centString = $(element).find("product-price div h3 span").text().trim();
  if (centString.includes("kg")) product.size = "per kg";
  centString = centString.replace(/\D/g, "");
  product.currentPrice = Number(dollarString + "." + centString);
  const today = /* @__PURE__ */ new Date();
  today.setMinutes(0);
  today.setSeconds(0);
  const todaysDatedPrice = {
    date: today,
    price: product.currentPrice
  };
  product.priceHistory = [todaysDatedPrice];
  const rawUnitPrice = $(element).find("span.cupPrice").text().trim();
  if (rawUnitPrice) {
    const unitPriceString = rawUnitPrice.split("/")[0].replace("$", "").trim();
    let unitPrice = Number.parseFloat(unitPriceString);
    const amountAndUnit = rawUnitPrice.split("/")[1].trim();
    let amount = Number.parseInt(amountAndUnit.match(/\d+/g)?.[0] || "");
    let unit = amountAndUnit.match(/\w+/g)?.[0] || "";
    if (amountAndUnit == "100g") {
      amount = amount * 10;
      unitPrice = unitPrice * 10;
      unit = "kg";
    } else if (amountAndUnit == "100mL") {
      amount = amount * 10;
      unitPrice = unitPrice * 10;
      unit = "L";
    }
    unit = unit.replace("1kg", "kg");
    unit = unit.replace("1L", "L");
    product.unitPrice = unitPrice;
    product.unitName = unit;
  }
  import_product_overrides.productOverrides.forEach((override) => {
    if (override.id === product.id) {
      if (override.size !== void 0) {
        product.size = override.size;
      }
      if (override.category !== void 0) {
        product.category = [override.category];
      }
    }
  });
  if (validateProduct(product)) return product;
  else {
    try {
      (0, import_utilities3.logError)(
        `  Unable to Scrape: ${product.id.padStart(6)} | ${product.name} | $${product.currentPrice}`
      );
    } catch {
      (0, import_utilities3.logError)("  Unable to Scrape ID from product");
    }
    return void 0;
  }
}
function validateProduct(product) {
  try {
    if (product.name.match(/\$\s\d+/)) return false;
    if (product.name.length < 4 || product.name.length > 100) return false;
    if (product.id.length < 2 || product.id.length > 20) return false;
    if (product.currentPrice <= 0 || product.currentPrice === null || product.currentPrice === void 0 || Number.isNaN(product.currentPrice) || product.currentPrice > 999) {
      return false;
    }
    return true;
  } catch (error) {
    return false;
  }
}
function parseAndCategoriseURL(line) {
  let baseCategorisedURL = { url: "", categories: [] };
  let parsedUrls = [];
  let numPagesPerURL = 1;
  if (!line.includes("woolworths.co.nz")) {
    return void 0;
  } else if (line.includes("?search=")) {
    parsedUrls.push({ url: line, categories: [] });
  } else {
    line.split(" ").forEach((section) => {
      if (section.includes("woolworths.co.nz")) {
        baseCategorisedURL.url = section;
        if (!baseCategorisedURL.url.startsWith("http"))
          baseCategorisedURL.url = "https://" + section;
        if (section.includes("?")) {
          baseCategorisedURL.url = line.substring(0, line.indexOf("?"));
        }
        baseCategorisedURL.url += "?page=1";
      } else if (section.startsWith("categories=")) {
        let splitCategories = [section.replace("categories=", "")];
        if (section.includes(","))
          splitCategories = section.replace("categories=", "").split(",");
        baseCategorisedURL.categories = splitCategories;
      } else if (section.startsWith("pages=")) {
        numPagesPerURL = Number.parseInt(section.split("=")[1]);
      }
      if (baseCategorisedURL.categories.length === 0) {
        const baseUrl = baseCategorisedURL.url.split("?")[0];
        let slashSections = baseUrl.split("/");
        baseCategorisedURL.categories = [slashSections[slashSections.length - 1]];
      }
    });
    for (let i = 1; i <= numPagesPerURL; i++) {
      let pagedUrl = {
        url: baseCategorisedURL.url.replace("page=1", "page=" + i),
        categories: baseCategorisedURL.categories
      };
      parsedUrls.push(pagedUrl);
    }
  }
  return parsedUrls;
}
async function routePlaywrightExclusions() {
  let typeExclusions = ["image", "media", "font"];
  let urlExclusions = [
    "googleoptimize.com",
    "gtm.js",
    "visitoridentification.js",
    "js-agent.newrelic.com",
    "cquotient.com",
    "googletagmanager.com",
    "cloudflareinsights.com",
    "dwanalytics",
    "facebook.net",
    "chatWidget",
    "edge.adobedc.net",
    "\u200B/Content/Banners/",
    "go-mpulse.net"
  ];
  await page.route("**/*", async (route) => {
    const req = route.request();
    let excludeThisRequest = false;
    urlExclusions.forEach((excludedURL) => {
      if (req.url().includes(excludedURL)) excludeThisRequest = true;
    });
    typeExclusions.forEach((excludedType) => {
      if (req.resourceType() === excludedType) excludeThisRequest = true;
    });
    if (excludeThisRequest) {
      await route.abort();
    } else {
      await route.continue();
    }
  });
  return;
}
export {
  databaseMode,
  parseAndCategoriseURL,
  playwrightElementToProduct,
  uploadImagesMode
};
//# sourceMappingURL=data:application/json;base64,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

	