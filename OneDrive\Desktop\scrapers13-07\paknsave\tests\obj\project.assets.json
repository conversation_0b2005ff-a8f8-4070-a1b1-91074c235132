{"version": 3, "targets": {"net6.0": {"AWSSDK.Core/**********": {"type": "package", "compile": {"lib/netcoreapp3.1/AWSSDK.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/AWSSDK.Core.dll": {"related": ".pdb;.xml"}}}, "AWSSDK.SecurityToken/**********": {"type": "package", "dependencies": {"AWSSDK.Core": "[**********, 4.0.0)"}, "compile": {"lib/netcoreapp3.1/AWSSDK.SecurityToken.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/AWSSDK.SecurityToken.dll": {"related": ".pdb;.xml"}}}, "coverlet.collector/3.2.0": {"type": "package", "build": {"build/netstandard1.0/coverlet.collector.targets": {}}}, "Dapper/2.1.66": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.1", "System.Reflection.Emit.Lightweight": "4.7.0"}, "compile": {"lib/netstandard2.0/Dapper.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Dapper.dll": {"related": ".xml"}}}, "DnsClient/1.6.1": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "5.0.0"}, "compile": {"lib/net5.0/DnsClient.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/DnsClient.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/9.0.1": {"type": "package", "compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Bcl.AsyncInterfaces.targets": {}}}, "Microsoft.CodeCoverage/17.5.0": {"type": "package", "compile": {"lib/netcoreapp3.1/Microsoft.VisualStudio.CodeCoverage.Shim.dll": {}}, "runtime": {"lib/netcoreapp3.1/Microsoft.VisualStudio.CodeCoverage.Shim.dll": {}}, "build": {"build/netstandard2.0/Microsoft.CodeCoverage.props": {}, "build/netstandard2.0/Microsoft.CodeCoverage.targets": {}}}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets": {}}}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "System.Text.Json": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Configuration.UserSecrets.props": {}, "buildTransitive/net6.0/Microsoft.Extensions.Configuration.UserSecrets.targets": {}}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Diagnostics/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Diagnostics.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Diagnostics.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Hosting/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.Configuration.CommandLine": "8.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "Microsoft.Extensions.Logging.Console": "8.0.0", "Microsoft.Extensions.Logging.Debug": "8.0.0", "Microsoft.Extensions.Logging.EventLog": "8.0.0", "Microsoft.Extensions.Logging.EventSource": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Hosting.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Logging.Configuration/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.Console/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Json": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Logging.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Console.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.Debug/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.EventLog/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.EventLog": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Logging.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.EventSource/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Json": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Logging.EventSource.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.EventSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.NET.Test.Sdk/17.5.0": {"type": "package", "dependencies": {"Microsoft.CodeCoverage": "17.5.0", "Microsoft.TestPlatform.TestHost": "17.5.0"}, "compile": {"lib/netcoreapp3.1/_._": {}}, "runtime": {"lib/netcoreapp3.1/_._": {}}, "build": {"build/netcoreapp3.1/Microsoft.NET.Test.Sdk.props": {}, "build/netcoreapp3.1/Microsoft.NET.Test.Sdk.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/Microsoft.NET.Test.Sdk.props": {}}}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.Playwright/1.47.0": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.ComponentModel.Annotations": "5.0.0", "System.Text.Json": "6.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Playwright.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Playwright.dll": {"related": ".xml"}}, "build": {"buildTransitive/Microsoft.Playwright.targets": {}}}, "Microsoft.TestPlatform.ObjectModel/17.5.0": {"type": "package", "dependencies": {"NuGet.Frameworks": "5.11.0", "System.Reflection.Metadata": "1.6.0"}, "compile": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll": {}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {}}, "runtime": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll": {}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {}}, "resource": {"lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.TestPlatform.TestHost/17.5.0": {"type": "package", "dependencies": {"Microsoft.TestPlatform.ObjectModel": "17.5.0", "Newtonsoft.Json": "13.0.1"}, "compile": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CommunicationUtilities.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.CrossPlatEngine.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.Utilities.dll": {}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.Common.dll": {}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {}, "lib/netcoreapp3.1/testhost.dll": {"related": ".deps.json"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CommunicationUtilities.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.CrossPlatEngine.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.Utilities.dll": {}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.Common.dll": {}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {}, "lib/netcoreapp3.1/testhost.dll": {"related": ".deps.json"}}, "resource": {"lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Han<PERSON>"}}, "build": {"build/netcoreapp3.1/Microsoft.TestPlatform.TestHost.props": {}}}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "MongoDB.Bson/2.23.1": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "compile": {"lib/netstandard2.1/MongoDB.Bson.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/MongoDB.Bson.dll": {"related": ".xml"}}}, "MongoDB.Driver/2.23.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "2.0.0", "MongoDB.Bson": "2.23.1", "MongoDB.Driver.Core": "2.23.1", "MongoDB.Libmongocrypt": "1.8.0"}, "compile": {"lib/netstandard2.1/MongoDB.Driver.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.dll": {"related": ".xml"}}}, "MongoDB.Driver.Core/2.23.1": {"type": "package", "dependencies": {"AWSSDK.SecurityToken": "**********", "DnsClient": "1.6.1", "Microsoft.Extensions.Logging.Abstractions": "2.0.0", "MongoDB.Bson": "2.23.1", "MongoDB.Libmongocrypt": "1.8.0", "SharpCompress": "0.30.1", "Snappier": "1.0.0", "System.Buffers": "4.5.1", "ZstdSharp.Port": "0.7.3"}, "compile": {"lib/netstandard2.1/MongoDB.Driver.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.Core.dll": {"related": ".xml"}}}, "MongoDB.Driver.GridFS/2.23.1": {"type": "package", "dependencies": {"MongoDB.Bson": "2.23.1", "MongoDB.Driver": "2.23.1", "MongoDB.Driver.Core": "2.23.1"}, "compile": {"lib/netstandard2.1/MongoDB.Driver.GridFS.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.GridFS.dll": {"related": ".xml"}}}, "MongoDB.Libmongocrypt/1.8.0": {"type": "package", "compile": {"lib/netstandard2.1/MongoDB.Libmongocrypt.dll": {}}, "runtime": {"lib/netstandard2.1/MongoDB.Libmongocrypt.dll": {}}, "contentFiles": {"contentFiles/any/any/_._": {"buildAction": "None", "codeLanguage": "any", "copyToOutput": false}}, "build": {"build/_._": {}}, "runtimeTargets": {"runtimes/linux/native/libmongocrypt.so": {"assetType": "native", "rid": "linux"}, "runtimes/osx/native/libmongocrypt.dylib": {"assetType": "native", "rid": "osx"}, "runtimes/win/native/mongocrypt.dll": {"assetType": "native", "rid": "win"}}}, "MSTest.TestAdapter/3.0.2": {"type": "package", "build": {"build/net6.0/MSTest.TestAdapter.props": {}, "build/net6.0/MSTest.TestAdapter.targets": {}}}, "MSTest.TestFramework/3.0.2": {"type": "package", "compile": {"lib/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll": {"related": ".xml"}}, "resource": {"lib/net6.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "cs"}, "lib/net6.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "de"}, "lib/net6.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "es"}, "lib/net6.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "fr"}, "lib/net6.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "it"}, "lib/net6.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "zh-Han<PERSON>"}}, "build": {"build/net6.0/MSTest.TestFramework.targets": {}}}, "Newtonsoft.Json/13.0.1": {"type": "package", "compile": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Npgsql/7.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "6.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/Npgsql.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Npgsql.dll": {"related": ".xml"}}}, "NuGet.Frameworks/5.11.0": {"type": "package", "compile": {"lib/netstandard2.0/NuGet.Frameworks.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NuGet.Frameworks.dll": {"related": ".xml"}}}, "PlaywrightExtraSharp2/*******": {"type": "package", "dependencies": {"Microsoft.Playwright": "1.41.2", "RestSharp": "110.2.0"}, "compile": {"lib/netstandard2.0/PlaywrightExtraSharp.dll": {}}, "runtime": {"lib/netstandard2.0/PlaywrightExtraSharp.dll": {}}}, "RestSharp/110.2.0": {"type": "package", "dependencies": {"System.Text.Json": "7.0.2"}, "compile": {"lib/net6.0/RestSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/RestSharp.dll": {"related": ".xml"}}}, "SharpCompress/0.30.1": {"type": "package", "compile": {"lib/net5.0/SharpCompress.dll": {}}, "runtime": {"lib/net5.0/SharpCompress.dll": {}}}, "Snappier/1.0.0": {"type": "package", "compile": {"lib/net5.0/Snappier.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Snappier.dll": {"related": ".xml"}}}, "System.Buffers/4.5.1": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "compile": {"ref/netstandard2.1/System.ComponentModel.Annotations.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/System.ComponentModel.Annotations.dll": {"related": ".xml"}}}, "System.Diagnostics.DiagnosticSource/8.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Diagnostics.EventLog/8.0.0": {"type": "package", "compile": {"lib/net6.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll": {"assetType": "runtime", "rid": "win"}, "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Memory/4.5.5": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Reflection.Emit.Lightweight/4.7.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Reflection.Metadata/1.6.0": {"type": "package", "compile": {"lib/netstandard2.0/System.Reflection.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Reflection.Metadata.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Security.AccessControl/5.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "compile": {"ref/netcoreapp3.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll": {"assetType": "runtime", "rid": "browser"}}}, "System.Text.Json/8.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "8.0.0"}, "compile": {"lib/net6.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/System.Text.Json.targets": {}}}, "ZstdSharp.Port/0.7.3": {"type": "package", "compile": {"lib/net6.0/ZstdSharp.dll": {}}, "runtime": {"lib/net6.0/ZstdSharp.dll": {}}}, "PakScraper/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"Dapper": "2.1.45", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Playwright": "1.47.0", "MongoDB.Driver": "2.23.1", "MongoDB.Driver.GridFS": "2.23.1", "Npgsql": "7.0.5", "PlaywrightExtraSharp2": "*******"}, "compile": {"bin/placeholder/PakScraper.dll": {}}, "runtime": {"bin/placeholder/PakScraper.dll": {}}}}}, "libraries": {"AWSSDK.Core/**********": {"sha512": "gnEgxBlk4PFEfdPE8Lkf4+D16MZFYSaW7/o6Wwe5e035QWUkTJX0Dn4LfTCdV5QSEL/fOFxu+yCAm55eIIBgog==", "type": "package", "path": "awssdk.core/**********", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "awssdk.core.**********.nupkg.sha512", "awssdk.core.nuspec", "lib/net35/AWSSDK.Core.dll", "lib/net35/AWSSDK.Core.pdb", "lib/net35/AWSSDK.Core.xml", "lib/net45/AWSSDK.Core.dll", "lib/net45/AWSSDK.Core.pdb", "lib/net45/AWSSDK.Core.xml", "lib/netcoreapp3.1/AWSSDK.Core.dll", "lib/netcoreapp3.1/AWSSDK.Core.pdb", "lib/netcoreapp3.1/AWSSDK.Core.xml", "lib/netstandard2.0/AWSSDK.Core.dll", "lib/netstandard2.0/AWSSDK.Core.pdb", "lib/netstandard2.0/AWSSDK.Core.xml", "tools/account-management.ps1"]}, "AWSSDK.SecurityToken/**********": {"sha512": "dGCVuVo0CFUKWW85W8YENO+aREf8sCBDjvGbnNvxJuNW4Ss+brEU9ltHhq2KfZze2VUNK1/wygbPG1bmbpyXEw==", "type": "package", "path": "awssdk.securitytoken/**********", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/AWSSDK.SecurityToken.CodeAnalysis.dll", "awssdk.securitytoken.**********.nupkg.sha512", "awssdk.securitytoken.nuspec", "lib/net35/AWSSDK.SecurityToken.dll", "lib/net35/AWSSDK.SecurityToken.pdb", "lib/net35/AWSSDK.SecurityToken.xml", "lib/net45/AWSSDK.SecurityToken.dll", "lib/net45/AWSSDK.SecurityToken.pdb", "lib/net45/AWSSDK.SecurityToken.xml", "lib/netcoreapp3.1/AWSSDK.SecurityToken.dll", "lib/netcoreapp3.1/AWSSDK.SecurityToken.pdb", "lib/netcoreapp3.1/AWSSDK.SecurityToken.xml", "lib/netstandard2.0/AWSSDK.SecurityToken.dll", "lib/netstandard2.0/AWSSDK.SecurityToken.pdb", "lib/netstandard2.0/AWSSDK.SecurityToken.xml", "tools/install.ps1", "tools/uninstall.ps1"]}, "coverlet.collector/3.2.0": {"sha512": "xjY8xBigSeWIYs4I7DgUHqSNoGqnHi7Fv7/7RZD02rvZyG3hlsjnQKiVKVWKgr9kRKgmV+dEfu8KScvysiC0Wg==", "type": "package", "path": "coverlet.collector/3.2.0", "files": [".nupkg.metadata", ".signature.p7s", "build/netstandard1.0/Microsoft.Bcl.AsyncInterfaces.dll", "build/netstandard1.0/Microsoft.CSharp.dll", "build/netstandard1.0/Microsoft.DotNet.PlatformAbstractions.dll", "build/netstandard1.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "build/netstandard1.0/Microsoft.Extensions.DependencyInjection.dll", "build/netstandard1.0/Microsoft.Extensions.DependencyModel.dll", "build/netstandard1.0/Microsoft.Extensions.FileSystemGlobbing.dll", "build/netstandard1.0/Microsoft.TestPlatform.CoreUtilities.dll", "build/netstandard1.0/Microsoft.TestPlatform.PlatformAbstractions.dll", "build/netstandard1.0/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll", "build/netstandard1.0/Mono.Cecil.Mdb.dll", "build/netstandard1.0/Mono.Cecil.Pdb.dll", "build/netstandard1.0/Mono.Cecil.Rocks.dll", "build/netstandard1.0/Mono.Cecil.dll", "build/netstandard1.0/Newtonsoft.Json.dll", "build/netstandard1.0/NuGet.Frameworks.dll", "build/netstandard1.0/System.AppContext.dll", "build/netstandard1.0/System.Collections.Immutable.dll", "build/netstandard1.0/System.Dynamic.Runtime.dll", "build/netstandard1.0/System.IO.FileSystem.Primitives.dll", "build/netstandard1.0/System.Linq.Expressions.dll", "build/netstandard1.0/System.Linq.dll", "build/netstandard1.0/System.ObjectModel.dll", "build/netstandard1.0/System.Reflection.Emit.ILGeneration.dll", "build/netstandard1.0/System.Reflection.Emit.Lightweight.dll", "build/netstandard1.0/System.Reflection.Emit.dll", "build/netstandard1.0/System.Reflection.Metadata.dll", "build/netstandard1.0/System.Reflection.TypeExtensions.dll", "build/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "build/netstandard1.0/System.Runtime.Serialization.Primitives.dll", "build/netstandard1.0/System.Text.RegularExpressions.dll", "build/netstandard1.0/System.Threading.Tasks.Extensions.dll", "build/netstandard1.0/System.Threading.dll", "build/netstandard1.0/System.Xml.ReaderWriter.dll", "build/netstandard1.0/System.Xml.XDocument.dll", "build/netstandard1.0/coverlet.collector.deps.json", "build/netstandard1.0/coverlet.collector.dll", "build/netstandard1.0/coverlet.collector.pdb", "build/netstandard1.0/coverlet.collector.targets", "build/netstandard1.0/coverlet.core.dll", "build/netstandard1.0/coverlet.core.pdb", "coverlet-icon.png", "coverlet.collector.3.2.0.nupkg.sha512", "coverlet.collector.nuspec"]}, "Dapper/2.1.66": {"sha512": "/q77jUgDOS+bzkmk3Vy9SiWMaetTw+NOoPAV0xPBsGVAyljd5S6P+4RUW7R3ZUGGr9lDRyPKgAMj2UAOwvqZYw==", "type": "package", "path": "dapper/2.1.66", "files": [".nupkg.metadata", ".signature.p7s", "Dapper.png", "dapper.2.1.66.nupkg.sha512", "dapper.nuspec", "lib/net461/Dapper.dll", "lib/net461/Dapper.xml", "lib/net8.0/Dapper.dll", "lib/net8.0/Dapper.xml", "lib/netstandard2.0/Dapper.dll", "lib/netstandard2.0/Dapper.xml", "readme.md"]}, "DnsClient/1.6.1": {"sha512": "4H/f2uYJOZ+YObZjpY9ABrKZI+JNw3uizp6oMzTXwDw6F+2qIPhpRl/1t68O/6e98+vqNiYGu+lswmwdYUy3gg==", "type": "package", "path": "dnsclient/1.6.1", "files": [".nupkg.metadata", ".signature.p7s", "dnsclient.1.6.1.nupkg.sha512", "dnsclient.nuspec", "icon.png", "lib/net45/DnsClient.dll", "lib/net45/DnsClient.xml", "lib/net471/DnsClient.dll", "lib/net471/DnsClient.xml", "lib/net5.0/DnsClient.dll", "lib/net5.0/DnsClient.xml", "lib/netstandard1.3/DnsClient.dll", "lib/netstandard1.3/DnsClient.xml", "lib/netstandard2.0/DnsClient.dll", "lib/netstandard2.0/DnsClient.xml", "lib/netstandard2.1/DnsClient.dll", "lib/netstandard2.1/DnsClient.xml"]}, "Microsoft.Bcl.AsyncInterfaces/9.0.1": {"sha512": "IVkmUqf+KzbuXKrxi2tyQlg11RArYk26t2eU5cHekff+7Ao09vH8vt8idC0BJSMnpiRV2OK66zM2EwJU6Tm5Cw==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/9.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Bcl.AsyncInterfaces.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Bcl.AsyncInterfaces.targets", "lib/net462/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net462/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.9.0.1.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.CodeCoverage/17.5.0": {"sha512": "6FQo0O6LKDqbCiIgVQhJAf810HSjFlOj7FunWaeOGDKxy8DAbpHzPk4SfBTXz9ytaaceuIIeR6hZgplt09m+ig==", "type": "package", "path": "microsoft.codecoverage/17.5.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE_NET.txt", "ThirdPartyNotices.txt", "build/netstandard2.0/CodeCoverage/CodeCoverage.config", "build/netstandard2.0/CodeCoverage/CodeCoverage.exe", "build/netstandard2.0/CodeCoverage/VanguardInstrumentationProfiler_x86.config", "build/netstandard2.0/CodeCoverage/amd64/CodeCoverage.exe", "build/netstandard2.0/CodeCoverage/amd64/VanguardInstrumentationProfiler_x64.config", "build/netstandard2.0/CodeCoverage/amd64/covrun64.dll", "build/netstandard2.0/CodeCoverage/amd64/msdia140.dll", "build/netstandard2.0/CodeCoverage/arm64/VanguardInstrumentationProfiler_arm64.config", "build/netstandard2.0/CodeCoverage/arm64/covrunarm64.dll", "build/netstandard2.0/CodeCoverage/arm64/msdia140.dll", "build/netstandard2.0/CodeCoverage/codecoveragemessages.dll", "build/netstandard2.0/CodeCoverage/coreclr/Microsoft.VisualStudio.CodeCoverage.Shim.dll", "build/netstandard2.0/CodeCoverage/covrun32.dll", "build/netstandard2.0/CodeCoverage/msdia140.dll", "build/netstandard2.0/InstrumentationEngine/alpine/x64/VanguardInstrumentationProfiler_x64.config", "build/netstandard2.0/InstrumentationEngine/alpine/x64/libCoverageInstrumentationMethod.so", "build/netstandard2.0/InstrumentationEngine/alpine/x64/libInstrumentationEngine.so", "build/netstandard2.0/InstrumentationEngine/arm64/MicrosoftInstrumentationEngine_arm64.dll", "build/netstandard2.0/InstrumentationEngine/macos/x64/VanguardInstrumentationProfiler_x64.config", "build/netstandard2.0/InstrumentationEngine/macos/x64/libCoverageInstrumentationMethod.dylib", "build/netstandard2.0/InstrumentationEngine/macos/x64/libInstrumentationEngine.dylib", "build/netstandard2.0/InstrumentationEngine/ubuntu/x64/VanguardInstrumentationProfiler_x64.config", "build/netstandard2.0/InstrumentationEngine/ubuntu/x64/libCoverageInstrumentationMethod.so", "build/netstandard2.0/InstrumentationEngine/ubuntu/x64/libInstrumentationEngine.so", "build/netstandard2.0/InstrumentationEngine/x64/MicrosoftInstrumentationEngine_x64.dll", "build/netstandard2.0/InstrumentationEngine/x86/MicrosoftInstrumentationEngine_x86.dll", "build/netstandard2.0/Microsoft.CodeCoverage.Core.dll", "build/netstandard2.0/Microsoft.CodeCoverage.Instrumentation.dll", "build/netstandard2.0/Microsoft.CodeCoverage.Interprocess.dll", "build/netstandard2.0/Microsoft.CodeCoverage.props", "build/netstandard2.0/Microsoft.CodeCoverage.targets", "build/netstandard2.0/Microsoft.VisualStudio.TraceDataCollector.dll", "build/netstandard2.0/Mono.Cecil.Pdb.dll", "build/netstandard2.0/Mono.Cecil.Rocks.dll", "build/netstandard2.0/Mono.Cecil.dll", "build/netstandard2.0/ThirdPartyNotices.txt", "build/netstandard2.0/cs/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/de/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/es/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/fr/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/it/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/ja/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/ko/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/pl/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/pt-BR/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/ru/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/tr/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/zh-<PERSON>/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard2.0/zh-Hant/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "lib/net462/Microsoft.VisualStudio.CodeCoverage.Shim.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.CodeCoverage.Shim.dll", "microsoft.codecoverage.17.5.0.nupkg.sha512", "microsoft.codecoverage.nuspec"]}, "Microsoft.Extensions.Configuration/8.0.0": {"sha512": "0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "type": "package", "path": "microsoft.extensions.configuration/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets", "lib/net462/Microsoft.Extensions.Configuration.dll", "lib/net462/Microsoft.Extensions.Configuration.xml", "lib/net6.0/Microsoft.Extensions.Configuration.dll", "lib/net6.0/Microsoft.Extensions.Configuration.xml", "lib/net7.0/Microsoft.Extensions.Configuration.dll", "lib/net7.0/Microsoft.Extensions.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"sha512": "3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"sha512": "mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "type": "package", "path": "microsoft.extensions.configuration.binder/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.dll", "analyzers/dotnet/cs/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/de/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/es/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/it/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets", "lib/net462/Microsoft.Extensions.Configuration.Binder.dll", "lib/net462/Microsoft.Extensions.Configuration.Binder.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"sha512": "NZuZMz3Q8Z780nKX3ifV1fE7lS+6pynDHK71OfU4OZ1ItgvDOhyOC7E6z+JMZrAj63zRpwbdldYFk499t3+1dQ==", "type": "package", "path": "microsoft.extensions.configuration.commandline/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.CommandLine.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.CommandLine.targets", "lib/net462/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net462/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net6.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net6.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net7.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net7.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.xml", "microsoft.extensions.configuration.commandline.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.commandline.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"sha512": "plvZ0ZIpq+97gdPNNvhwvrEZ92kNml9hd1pe3idMA7svR0PztdzVLkoWLcRFgySYXUJc3kSM3Xw3mNFMo/bxRA==", "type": "package", "path": "microsoft.extensions.configuration.environmentvariables/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.EnvironmentVariables.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.targets", "lib/net462/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net462/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net6.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net6.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net7.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net7.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "microsoft.extensions.configuration.environmentvariables.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.environmentvariables.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"sha512": "McP+Lz/EKwvtCv48z0YImw+L1gi1gy5rHhNaNIY2CrjloV+XY8gydT8DjMR6zWeL13AFK+DioVpppwAuO1Gi1w==", "type": "package", "path": "microsoft.extensions.configuration.fileextensions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.FileExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.FileExtensions.targets", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net7.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net7.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.fileextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"sha512": "C2wqUoh9OmRL1akaCcKSTmRU8z0kckfImG7zLNI8uyi47Lp+zd5LWAD17waPQEqCz3ioWOCrFUo+JJuoeZLOBw==", "type": "package", "path": "microsoft.extensions.configuration.json/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Json.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Json.targets", "lib/net462/Microsoft.Extensions.Configuration.Json.dll", "lib/net462/Microsoft.Extensions.Configuration.Json.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.xml", "microsoft.extensions.configuration.json.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.json.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"sha512": "ihDHu2dJYQird9pl2CbdwuNDfvCZdOS0S7SPlNfhPt0B81UTT+yyZKz2pimFZGUp3AfuBRnqUCxB2SjsZKHVUw==", "type": "package", "path": "microsoft.extensions.configuration.usersecrets/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/net462/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/net462/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/net6.0/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/net6.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "lib/net462/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net462/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net6.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net6.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net7.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net7.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "microsoft.extensions.configuration.usersecrets.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.usersecrets.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"sha512": "V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "type": "package", "path": "microsoft.extensions.dependencyinjection/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"sha512": "cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics/8.0.0": {"sha512": "3PZp/YSkIXrF7QK7PfC1bkyRYwqOHpWFad8Qx+4wkuumAeXo1NHaxpS9LboNA9OvNSAu+QOVlXbMyoY+pHSqcw==", "type": "package", "path": "microsoft.extensions.diagnostics/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Diagnostics.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.targets", "lib/net462/Microsoft.Extensions.Diagnostics.dll", "lib/net462/Microsoft.Extensions.Diagnostics.xml", "lib/net6.0/Microsoft.Extensions.Diagnostics.dll", "lib/net6.0/Microsoft.Extensions.Diagnostics.xml", "lib/net7.0/Microsoft.Extensions.Diagnostics.dll", "lib/net7.0/Microsoft.Extensions.Diagnostics.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.xml", "microsoft.extensions.diagnostics.8.0.0.nupkg.sha512", "microsoft.extensions.diagnostics.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"sha512": "JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "type": "package", "path": "microsoft.extensions.diagnostics.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Diagnostics.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.Abstractions.targets", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.diagnostics.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"sha512": "ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"sha512": "UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "type": "package", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Physical.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Physical.targets", "lib/net462/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net462/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net7.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net7.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml", "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.physical.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"sha512": "OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileSystemGlobbing.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileSystemGlobbing.targets", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting/8.0.0": {"sha512": "ItYHpdqVp5/oFLT5QqbopnkKlyFG9EW/9nhM6/yfObeKt6Su0wkBio6AizgRHGNwhJuAtlE5VIjow5JOTrip6w==", "type": "package", "path": "microsoft.extensions.hosting/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Hosting.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.targets", "lib/net462/Microsoft.Extensions.Hosting.dll", "lib/net462/Microsoft.Extensions.Hosting.xml", "lib/net6.0/Microsoft.Extensions.Hosting.dll", "lib/net6.0/Microsoft.Extensions.Hosting.xml", "lib/net7.0/Microsoft.Extensions.Hosting.dll", "lib/net7.0/Microsoft.Extensions.Hosting.xml", "lib/net8.0/Microsoft.Extensions.Hosting.dll", "lib/net8.0/Microsoft.Extensions.Hosting.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.xml", "microsoft.extensions.hosting.8.0.0.nupkg.sha512", "microsoft.extensions.hosting.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"sha512": "AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "type": "package", "path": "microsoft.extensions.hosting.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Hosting.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.Abstractions.targets", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.xml", "microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.hosting.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging/8.0.0": {"sha512": "tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "type": "package", "path": "microsoft.extensions.logging/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net6.0/Microsoft.Extensions.Logging.dll", "lib/net6.0/Microsoft.Extensions.Logging.xml", "lib/net7.0/Microsoft.Extensions.Logging.dll", "lib/net7.0/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.8.0.0.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"sha512": "arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "type": "package", "path": "microsoft.extensions.logging.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Configuration/8.0.0": {"sha512": "ixXXV0G/12g6MXK65TLngYN9V5hQQRuV+fZi882WIoVJT7h5JvoYoxTEwCgdqwLjSneqh1O+66gM8sMr9z/rsQ==", "type": "package", "path": "microsoft.extensions.logging.configuration/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Configuration.targets", "lib/net462/Microsoft.Extensions.Logging.Configuration.dll", "lib/net462/Microsoft.Extensions.Logging.Configuration.xml", "lib/net6.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net6.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/net7.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net7.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.xml", "microsoft.extensions.logging.configuration.8.0.0.nupkg.sha512", "microsoft.extensions.logging.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Console/8.0.0": {"sha512": "e+48o7DztoYog+PY430lPxrM4mm3PbA6qucvQtUDDwVo4MO+ejMw7YGc/o2rnxbxj4isPxdfKFzTxvXMwAz83A==", "type": "package", "path": "microsoft.extensions.logging.console/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Console.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Console.targets", "lib/net462/Microsoft.Extensions.Logging.Console.dll", "lib/net462/Microsoft.Extensions.Logging.Console.xml", "lib/net6.0/Microsoft.Extensions.Logging.Console.dll", "lib/net6.0/Microsoft.Extensions.Logging.Console.xml", "lib/net7.0/Microsoft.Extensions.Logging.Console.dll", "lib/net7.0/Microsoft.Extensions.Logging.Console.xml", "lib/net8.0/Microsoft.Extensions.Logging.Console.dll", "lib/net8.0/Microsoft.Extensions.Logging.Console.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Console.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Console.xml", "microsoft.extensions.logging.console.8.0.0.nupkg.sha512", "microsoft.extensions.logging.console.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Debug/8.0.0": {"sha512": "dt0x21qBdudHLW/bjMJpkixv858RRr8eSomgVbU8qljOyfrfDGi1JQvpF9w8S7ziRPtRKisuWaOwFxJM82GxeA==", "type": "package", "path": "microsoft.extensions.logging.debug/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Debug.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Debug.targets", "lib/net462/Microsoft.Extensions.Logging.Debug.dll", "lib/net462/Microsoft.Extensions.Logging.Debug.xml", "lib/net6.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net6.0/Microsoft.Extensions.Logging.Debug.xml", "lib/net7.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net7.0/Microsoft.Extensions.Logging.Debug.xml", "lib/net8.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net8.0/Microsoft.Extensions.Logging.Debug.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.xml", "microsoft.extensions.logging.debug.8.0.0.nupkg.sha512", "microsoft.extensions.logging.debug.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.EventLog/8.0.0": {"sha512": "3X9D3sl7EmOu7vQp5MJrmIJBl5XSdOhZPYXUeFfYa6Nnm9+tok8x3t3IVPLhm7UJtPOU61ohFchw8rNm9tIYOQ==", "type": "package", "path": "microsoft.extensions.logging.eventlog/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.EventLog.targets", "lib/net462/Microsoft.Extensions.Logging.EventLog.dll", "lib/net462/Microsoft.Extensions.Logging.EventLog.xml", "lib/net6.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/net6.0/Microsoft.Extensions.Logging.EventLog.xml", "lib/net7.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/net7.0/Microsoft.Extensions.Logging.EventLog.xml", "lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/net8.0/Microsoft.Extensions.Logging.EventLog.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.xml", "microsoft.extensions.logging.eventlog.8.0.0.nupkg.sha512", "microsoft.extensions.logging.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.EventSource/8.0.0": {"sha512": "oKcPMrw+luz2DUAKhwFXrmFikZWnyc8l2RKoQwqU3KIZZjcfoJE0zRHAnqATfhRZhtcbjl/QkiY2Xjxp0xu+6w==", "type": "package", "path": "microsoft.extensions.logging.eventsource/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.EventSource.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.EventSource.targets", "lib/net462/Microsoft.Extensions.Logging.EventSource.dll", "lib/net462/Microsoft.Extensions.Logging.EventSource.xml", "lib/net6.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/net6.0/Microsoft.Extensions.Logging.EventSource.xml", "lib/net7.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/net7.0/Microsoft.Extensions.Logging.EventSource.xml", "lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/net8.0/Microsoft.Extensions.Logging.EventSource.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.xml", "microsoft.extensions.logging.eventsource.8.0.0.nupkg.sha512", "microsoft.extensions.logging.eventsource.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/8.0.0": {"sha512": "JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "type": "package", "path": "microsoft.extensions.options/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net6.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net6.0/Microsoft.Extensions.Options.dll", "lib/net6.0/Microsoft.Extensions.Options.xml", "lib/net7.0/Microsoft.Extensions.Options.dll", "lib/net7.0/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.8.0.0.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"sha512": "0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "type": "package", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net6.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net6.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net7.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net7.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512", "microsoft.extensions.options.configurationextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/8.0.0": {"sha512": "bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "type": "package", "path": "microsoft.extensions.primitives/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net6.0/Microsoft.Extensions.Primitives.dll", "lib/net6.0/Microsoft.Extensions.Primitives.xml", "lib/net7.0/Microsoft.Extensions.Primitives.dll", "lib/net7.0/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.8.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.NET.Test.Sdk/17.5.0": {"sha512": "IJ4eSPcsRbwbAZehh1M9KgejSy0u3d0wAdkJytfCh67zOaCl5U3ltruUEe15MqirdRqGmm/ngbjeaVeGapSZxg==", "type": "package", "path": "microsoft.net.test.sdk/17.5.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE_NET.txt", "build/net462/Microsoft.NET.Test.Sdk.props", "build/net462/Microsoft.NET.Test.Sdk.targets", "build/netcoreapp3.1/Microsoft.NET.Test.Sdk.Program.cs", "build/netcoreapp3.1/Microsoft.NET.Test.Sdk.Program.fs", "build/netcoreapp3.1/Microsoft.NET.Test.Sdk.Program.vb", "build/netcoreapp3.1/Microsoft.NET.Test.Sdk.props", "build/netcoreapp3.1/Microsoft.NET.Test.Sdk.targets", "buildMultiTargeting/Microsoft.NET.Test.Sdk.props", "lib/net462/_._", "lib/netcoreapp3.1/_._", "microsoft.net.test.sdk.17.5.0.nupkg.sha512", "microsoft.net.test.sdk.nuspec"]}, "Microsoft.NETCore.Platforms/5.0.0": {"sha512": "VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "type": "package", "path": "microsoft.netcore.platforms/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.5.0.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Playwright/1.47.0": {"sha512": "D9verOkoSO1vqqAe36jmuQlceEHd2leoYlLOXQkMuVDFTbhvblVk7LOm9LeS50u+5xNcIcJi1+vA2rZxN5tW4A==", "type": "package", "path": "microsoft.playwright/1.47.0", "files": [".nupkg.metadata", ".playwright/node/LICENSE", ".playwright/node/darwin-arm64/node", ".playwright/node/darwin-x64/node", ".playwright/node/linux-arm64/node", ".playwright/node/linux-x64/node", ".playwright/node/win32_x64/node.exe", ".playwright/package/LICENSE", ".playwright/package/NOTICE", ".playwright/package/README.md", ".playwright/package/ThirdPartyNotices.txt", ".playwright/package/api.json", ".playwright/package/bin/PrintDeps.exe", ".playwright/package/bin/README.md", ".playwright/package/bin/install_media_pack.ps1", ".playwright/package/bin/reinstall_chrome_beta_linux.sh", ".playwright/package/bin/reinstall_chrome_beta_mac.sh", ".playwright/package/bin/reinstall_chrome_beta_win.ps1", ".playwright/package/bin/reinstall_chrome_stable_linux.sh", ".playwright/package/bin/reinstall_chrome_stable_mac.sh", ".playwright/package/bin/reinstall_chrome_stable_win.ps1", ".playwright/package/bin/reinstall_msedge_beta_linux.sh", ".playwright/package/bin/reinstall_msedge_beta_mac.sh", ".playwright/package/bin/reinstall_msedge_beta_win.ps1", ".playwright/package/bin/reinstall_msedge_dev_linux.sh", ".playwright/package/bin/reinstall_msedge_dev_mac.sh", ".playwright/package/bin/reinstall_msedge_dev_win.ps1", ".playwright/package/bin/reinstall_msedge_stable_linux.sh", ".playwright/package/bin/reinstall_msedge_stable_mac.sh", ".playwright/package/bin/reinstall_msedge_stable_win.ps1", ".playwright/package/browsers.json", ".playwright/package/cli.js", ".playwright/package/index.d.ts", ".playwright/package/index.js", ".playwright/package/index.mjs", ".playwright/package/lib/androidServerImpl.js", ".playwright/package/lib/browserServerImpl.js", ".playwright/package/lib/cli/driver.js", ".playwright/package/lib/cli/program.js", ".playwright/package/lib/cli/programWithTestStub.js", ".playwright/package/lib/client/accessibility.js", ".playwright/package/lib/client/android.js", ".playwright/package/lib/client/api.js", ".playwright/package/lib/client/artifact.js", ".playwright/package/lib/client/browser.js", ".playwright/package/lib/client/browserContext.js", ".playwright/package/lib/client/browserType.js", ".playwright/package/lib/client/cdpSession.js", ".playwright/package/lib/client/channelOwner.js", ".playwright/package/lib/client/clientHelper.js", ".playwright/package/lib/client/clientInstrumentation.js", ".playwright/package/lib/client/clock.js", ".playwright/package/lib/client/connection.js", ".playwright/package/lib/client/consoleMessage.js", ".playwright/package/lib/client/coverage.js", ".playwright/package/lib/client/dialog.js", ".playwright/package/lib/client/download.js", ".playwright/package/lib/client/electron.js", ".playwright/package/lib/client/elementHandle.js", ".playwright/package/lib/client/errors.js", ".playwright/package/lib/client/eventEmitter.js", ".playwright/package/lib/client/events.js", ".playwright/package/lib/client/fetch.js", ".playwright/package/lib/client/fileChooser.js", ".playwright/package/lib/client/frame.js", ".playwright/package/lib/client/harRouter.js", ".playwright/package/lib/client/input.js", ".playwright/package/lib/client/jsHandle.js", ".playwright/package/lib/client/jsonPipe.js", ".playwright/package/lib/client/localUtils.js", ".playwright/package/lib/client/locator.js", ".playwright/package/lib/client/network.js", ".playwright/package/lib/client/page.js", ".playwright/package/lib/client/playwright.js", ".playwright/package/lib/client/selectors.js", ".playwright/package/lib/client/stream.js", ".playwright/package/lib/client/tracing.js", ".playwright/package/lib/client/types.js", ".playwright/package/lib/client/video.js", ".playwright/package/lib/client/waiter.js", ".playwright/package/lib/client/webError.js", ".playwright/package/lib/client/worker.js", ".playwright/package/lib/client/writableStream.js", ".playwright/package/lib/common/socksProxy.js", ".playwright/package/lib/common/timeoutSettings.js", ".playwright/package/lib/common/types.js", ".playwright/package/lib/generated/clockSource.js", ".playwright/package/lib/generated/consoleApiSource.js", ".playwright/package/lib/generated/injectedScriptSource.js", ".playwright/package/lib/generated/recorderSource.js", ".playwright/package/lib/generated/utilityScriptSource.js", ".playwright/package/lib/image_tools/colorUtils.js", ".playwright/package/lib/image_tools/compare.js", ".playwright/package/lib/image_tools/imageChannel.js", ".playwright/package/lib/image_tools/stats.js", ".playwright/package/lib/inProcessFactory.js", ".playwright/package/lib/inprocess.js", ".playwright/package/lib/outofprocess.js", ".playwright/package/lib/protocol/debug.js", ".playwright/package/lib/protocol/serializers.js", ".playwright/package/lib/protocol/transport.js", ".playwright/package/lib/protocol/validator.js", ".playwright/package/lib/protocol/validatorPrimitives.js", ".playwright/package/lib/remote/playwrightConnection.js", ".playwright/package/lib/remote/playwrightServer.js", ".playwright/package/lib/server/accessibility.js", ".playwright/package/lib/server/android/android.js", ".playwright/package/lib/server/android/backendAdb.js", ".playwright/package/lib/server/artifact.js", ".playwright/package/lib/server/bidi/bidiBrowser.js", ".playwright/package/lib/server/bidi/bidiConnection.js", ".playwright/package/lib/server/bidi/bidiExecutionContext.js", ".playwright/package/lib/server/bidi/bidiFirefox.js", ".playwright/package/lib/server/bidi/bidiInput.js", ".playwright/package/lib/server/bidi/bidiNetworkManager.js", ".playwright/package/lib/server/bidi/bidiPage.js", ".playwright/package/lib/server/bidi/third_party/bidiDeserializer.js", ".playwright/package/lib/server/bidi/third_party/bidiKeyboard.js", ".playwright/package/lib/server/bidi/third_party/bidiProtocol.js", ".playwright/package/lib/server/bidi/third_party/bidiSerializer.js", ".playwright/package/lib/server/browser.js", ".playwright/package/lib/server/browserContext.js", ".playwright/package/lib/server/browserType.js", ".playwright/package/lib/server/chromium/appIcon.png", ".playwright/package/lib/server/chromium/chromium.js", ".playwright/package/lib/server/chromium/chromiumSwitches.js", ".playwright/package/lib/server/chromium/crAccessibility.js", ".playwright/package/lib/server/chromium/crBrowser.js", ".playwright/package/lib/server/chromium/crConnection.js", ".playwright/package/lib/server/chromium/crCoverage.js", ".playwright/package/lib/server/chromium/crDevTools.js", ".playwright/package/lib/server/chromium/crDragDrop.js", ".playwright/package/lib/server/chromium/crExecutionContext.js", ".playwright/package/lib/server/chromium/crInput.js", ".playwright/package/lib/server/chromium/crNetworkManager.js", ".playwright/package/lib/server/chromium/crPage.js", ".playwright/package/lib/server/chromium/crPdf.js", ".playwright/package/lib/server/chromium/crProtocolHelper.js", ".playwright/package/lib/server/chromium/crServiceWorker.js", ".playwright/package/lib/server/chromium/defaultFontFamilies.js", ".playwright/package/lib/server/chromium/videoRecorder.js", ".playwright/package/lib/server/clock.js", ".playwright/package/lib/server/codegen/csharp.js", ".playwright/package/lib/server/codegen/java.js", ".playwright/package/lib/server/codegen/javascript.js", ".playwright/package/lib/server/codegen/jsonl.js", ".playwright/package/lib/server/codegen/language.js", ".playwright/package/lib/server/codegen/languages.js", ".playwright/package/lib/server/codegen/python.js", ".playwright/package/lib/server/codegen/types.js", ".playwright/package/lib/server/console.js", ".playwright/package/lib/server/cookieStore.js", ".playwright/package/lib/server/debugController.js", ".playwright/package/lib/server/debugger.js", ".playwright/package/lib/server/deviceDescriptors.js", ".playwright/package/lib/server/deviceDescriptorsSource.json", ".playwright/package/lib/server/dialog.js", ".playwright/package/lib/server/dispatchers/androidDispatcher.js", ".playwright/package/lib/server/dispatchers/artifactDispatcher.js", ".playwright/package/lib/server/dispatchers/browserContextDispatcher.js", ".playwright/package/lib/server/dispatchers/browserDispatcher.js", ".playwright/package/lib/server/dispatchers/browserTypeDispatcher.js", ".playwright/package/lib/server/dispatchers/cdpSessionDispatcher.js", ".playwright/package/lib/server/dispatchers/debugControllerDispatcher.js", ".playwright/package/lib/server/dispatchers/dialogDispatcher.js", ".playwright/package/lib/server/dispatchers/dispatcher.js", ".playwright/package/lib/server/dispatchers/electronDispatcher.js", ".playwright/package/lib/server/dispatchers/elementHandlerDispatcher.js", ".playwright/package/lib/server/dispatchers/frameDispatcher.js", ".playwright/package/lib/server/dispatchers/jsHandleDispatcher.js", ".playwright/package/lib/server/dispatchers/jsonPipeDispatcher.js", ".playwright/package/lib/server/dispatchers/localUtilsDispatcher.js", ".playwright/package/lib/server/dispatchers/networkDispatchers.js", ".playwright/package/lib/server/dispatchers/pageDispatcher.js", ".playwright/package/lib/server/dispatchers/playwrightDispatcher.js", ".playwright/package/lib/server/dispatchers/selectorsDispatcher.js", ".playwright/package/lib/server/dispatchers/streamDispatcher.js", ".playwright/package/lib/server/dispatchers/tracingDispatcher.js", ".playwright/package/lib/server/dispatchers/writableStreamDispatcher.js", ".playwright/package/lib/server/dom.js", ".playwright/package/lib/server/download.js", ".playwright/package/lib/server/electron/electron.js", ".playwright/package/lib/server/electron/loader.js", ".playwright/package/lib/server/errors.js", ".playwright/package/lib/server/fetch.js", ".playwright/package/lib/server/fileChooser.js", ".playwright/package/lib/server/fileUploadUtils.js", ".playwright/package/lib/server/firefox/ffAccessibility.js", ".playwright/package/lib/server/firefox/ffBrowser.js", ".playwright/package/lib/server/firefox/ffConnection.js", ".playwright/package/lib/server/firefox/ffExecutionContext.js", ".playwright/package/lib/server/firefox/ffInput.js", ".playwright/package/lib/server/firefox/ffNetworkManager.js", ".playwright/package/lib/server/firefox/ffPage.js", ".playwright/package/lib/server/firefox/firefox.js", ".playwright/package/lib/server/formData.js", ".playwright/package/lib/server/frameSelectors.js", ".playwright/package/lib/server/frames.js", ".playwright/package/lib/server/har/harRecorder.js", ".playwright/package/lib/server/har/harTracer.js", ".playwright/package/lib/server/helper.js", ".playwright/package/lib/server/index.js", ".playwright/package/lib/server/input.js", ".playwright/package/lib/server/instrumentation.js", ".playwright/package/lib/server/isomorphic/utilityScriptSerializers.js", ".playwright/package/lib/server/javascript.js", ".playwright/package/lib/server/launchApp.js", ".playwright/package/lib/server/macEditingCommands.js", ".playwright/package/lib/server/network.js", ".playwright/package/lib/server/page.js", ".playwright/package/lib/server/pipeTransport.js", ".playwright/package/lib/server/playwright.js", ".playwright/package/lib/server/progress.js", ".playwright/package/lib/server/protocolError.js", ".playwright/package/lib/server/recorder.js", ".playwright/package/lib/server/recorder/contextRecorder.js", ".playwright/package/lib/server/recorder/recorderActions.js", ".playwright/package/lib/server/recorder/recorderApp.js", ".playwright/package/lib/server/recorder/recorderCollection.js", ".playwright/package/lib/server/recorder/recorderRunner.js", ".playwright/package/lib/server/recorder/recorderUtils.js", ".playwright/package/lib/server/recorder/throttledFile.js", ".playwright/package/lib/server/registry/browserFetcher.js", ".playwright/package/lib/server/registry/dependencies.js", ".playwright/package/lib/server/registry/index.js", ".playwright/package/lib/server/registry/nativeDeps.js", ".playwright/package/lib/server/registry/oopDownloadBrowserMain.js", ".playwright/package/lib/server/screenshotter.js", ".playwright/package/lib/server/selectors.js", ".playwright/package/lib/server/socksClientCertificatesInterceptor.js", ".playwright/package/lib/server/socksInterceptor.js", ".playwright/package/lib/server/trace/recorder/snapshotter.js", ".playwright/package/lib/server/trace/recorder/snapshotterInjected.js", ".playwright/package/lib/server/trace/recorder/tracing.js", ".playwright/package/lib/server/trace/test/inMemorySnapshotter.js", ".playwright/package/lib/server/trace/viewer/traceViewer.js", ".playwright/package/lib/server/transport.js", ".playwright/package/lib/server/types.js", ".playwright/package/lib/server/usKeyboardLayout.js", ".playwright/package/lib/server/webkit/webkit.js", ".playwright/package/lib/server/webkit/wkAccessibility.js", ".playwright/package/lib/server/webkit/wkBrowser.js", ".playwright/package/lib/server/webkit/wkConnection.js", ".playwright/package/lib/server/webkit/wkExecutionContext.js", ".playwright/package/lib/server/webkit/wkInput.js", ".playwright/package/lib/server/webkit/wkInterceptableRequest.js", ".playwright/package/lib/server/webkit/wkPage.js", ".playwright/package/lib/server/webkit/wkProvisionalPage.js", ".playwright/package/lib/server/webkit/wkWorkers.js", ".playwright/package/lib/third_party/diff_match_patch.js", ".playwright/package/lib/third_party/pixelmatch.js", ".playwright/package/lib/utils/ascii.js", ".playwright/package/lib/utils/comparators.js", ".playwright/package/lib/utils/crypto.js", ".playwright/package/lib/utils/debug.js", ".playwright/package/lib/utils/debugLogger.js", ".playwright/package/lib/utils/env.js", ".playwright/package/lib/utils/eventsHelper.js", ".playwright/package/lib/utils/expectUtils.js", ".playwright/package/lib/utils/fileUtils.js", ".playwright/package/lib/utils/happy-eyeballs.js", ".playwright/package/lib/utils/headers.js", ".playwright/package/lib/utils/hostPlatform.js", ".playwright/package/lib/utils/httpServer.js", ".playwright/package/lib/utils/index.js", ".playwright/package/lib/utils/isomorphic/cssParser.js", ".playwright/package/lib/utils/isomorphic/cssTokenizer.js", ".playwright/package/lib/utils/isomorphic/locatorGenerators.js", ".playwright/package/lib/utils/isomorphic/locatorParser.js", ".playwright/package/lib/utils/isomorphic/locatorUtils.js", ".playwright/package/lib/utils/isomorphic/mimeType.js", ".playwright/package/lib/utils/isomorphic/selectorParser.js", ".playwright/package/lib/utils/isomorphic/stringUtils.js", ".playwright/package/lib/utils/isomorphic/traceUtils.js", ".playwright/package/lib/utils/isomorphic/urlMatch.js", ".playwright/package/lib/utils/linuxUtils.js", ".playwright/package/lib/utils/manualPromise.js", ".playwright/package/lib/utils/multimap.js", ".playwright/package/lib/utils/network.js", ".playwright/package/lib/utils/processLauncher.js", ".playwright/package/lib/utils/profiler.js", ".playwright/package/lib/utils/rtti.js", ".playwright/package/lib/utils/semaphore.js", ".playwright/package/lib/utils/spawnAsync.js", ".playwright/package/lib/utils/stackTrace.js", ".playwright/package/lib/utils/task.js", ".playwright/package/lib/utils/time.js", ".playwright/package/lib/utils/timeoutRunner.js", ".playwright/package/lib/utils/traceUtils.js", ".playwright/package/lib/utils/userAgent.js", ".playwright/package/lib/utils/wsServer.js", ".playwright/package/lib/utils/zipFile.js", ".playwright/package/lib/utils/zones.js", ".playwright/package/lib/utilsBundle.js", ".playwright/package/lib/utilsBundleImpl/index.js", ".playwright/package/lib/utilsBundleImpl/xdg-open", ".playwright/package/lib/vite/htmlReport/index.html", ".playwright/package/lib/vite/recorder/assets/codeMirrorModule-BN0yUF4I.js", ".playwright/package/lib/vite/recorder/assets/codeMirrorModule-ez37Vkbh.css", ".playwright/package/lib/vite/recorder/assets/codicon-DCmgc-ay.ttf", ".playwright/package/lib/vite/recorder/assets/index-B-MT5gKo.css", ".playwright/package/lib/vite/recorder/assets/index-DVt3E1Ef.js", ".playwright/package/lib/vite/recorder/index.html", ".playwright/package/lib/vite/recorder/playwright-logo.svg", ".playwright/package/lib/vite/traceViewer/assets/codeMirrorModule-Dg-JD506.js", ".playwright/package/lib/vite/traceViewer/assets/testServerConnection-DeE2kSzz.js", ".playwright/package/lib/vite/traceViewer/assets/workbench-D5oSwIMK.js", ".playwright/package/lib/vite/traceViewer/assets/xtermModule-BeNbaIVa.js", ".playwright/package/lib/vite/traceViewer/codeMirrorModule.ez37Vkbh.css", ".playwright/package/lib/vite/traceViewer/codicon.DCmgc-ay.ttf", ".playwright/package/lib/vite/traceViewer/embedded.Bo_JtCyn.js", ".playwright/package/lib/vite/traceViewer/embedded.html", ".playwright/package/lib/vite/traceViewer/embedded.w7WN2u1R.css", ".playwright/package/lib/vite/traceViewer/index.BRZXsPsq.js", ".playwright/package/lib/vite/traceViewer/index.CrbWWHbf.css", ".playwright/package/lib/vite/traceViewer/index.html", ".playwright/package/lib/vite/traceViewer/playwright-logo.svg", ".playwright/package/lib/vite/traceViewer/snapshot.html", ".playwright/package/lib/vite/traceViewer/sw.bundle.js", ".playwright/package/lib/vite/traceViewer/uiMode.D3cNFP6u.css", ".playwright/package/lib/vite/traceViewer/uiMode.DdEQ0QhM.js", ".playwright/package/lib/vite/traceViewer/uiMode.html", ".playwright/package/lib/vite/traceViewer/workbench.DjbIuxix.css", ".playwright/package/lib/vite/traceViewer/xtermModule.DSXBckUd.css", ".playwright/package/lib/zipBundle.js", ".playwright/package/lib/zipBundleImpl.js", ".playwright/package/package.json", ".playwright/package/protocol.yml", ".playwright/package/types/protocol.d.ts", ".playwright/package/types/structs.d.ts", ".playwright/package/types/types.d.ts", ".signature.p7s", "build/Microsoft.Playwright.targets", "build/playwright.ps1", "buildTransitive/Microsoft.Playwright.targets", "buildTransitive/playwright.ps1", "icon.png", "lib/netstandard2.0/Microsoft.Playwright.dll", "lib/netstandard2.0/Microsoft.Playwright.xml", "microsoft.playwright.1.47.0.nupkg.sha512", "microsoft.playwright.nuspec"]}, "Microsoft.TestPlatform.ObjectModel/17.5.0": {"sha512": "QwiBJcC/oEA1kojOaB0uPWOIo4i6BYuTBBYJVhUvmXkyYqZ2Ut/VZfgi+enf8LF8J4sjO98oRRFt39MiRorcIw==", "type": "package", "path": "microsoft.testplatform.objectmodel/17.5.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE_NET.txt", "lib/net462/Microsoft.TestPlatform.CoreUtilities.dll", "lib/net462/Microsoft.TestPlatform.PlatformAbstractions.dll", "lib/net462/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll", "lib/net462/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/de/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/es/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/it/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/zh-<PERSON>/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/net462/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/net462/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll", "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll", "lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/Microsoft.TestPlatform.CoreUtilities.dll", "lib/netstandard2.0/Microsoft.TestPlatform.PlatformAbstractions.dll", "lib/netstandard2.0/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll", "lib/netstandard2.0/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/de/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/es/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/it/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "microsoft.testplatform.objectmodel.17.5.0.nupkg.sha512", "microsoft.testplatform.objectmodel.nuspec"]}, "Microsoft.TestPlatform.TestHost/17.5.0": {"sha512": "X86aikwp9d4SDcBChwzQYZihTPGEtMdDk+9t64emAl7N0Tq+OmlLAoW+Rs+2FB2k6QdUicSlT4QLO2xABRokaw==", "type": "package", "path": "microsoft.testplatform.testhost/17.5.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE_NET.txt", "ThirdPartyNotices.txt", "build/netcoreapp3.1/Microsoft.TestPlatform.TestHost.props", "build/netcoreapp3.1/x64/testhost.dll", "build/netcoreapp3.1/x64/testhost.exe", "build/netcoreapp3.1/x86/testhost.x86.dll", "build/netcoreapp3.1/x86/testhost.x86.exe", "lib/net462/_._", "lib/netcoreapp3.1/Microsoft.TestPlatform.CommunicationUtilities.dll", "lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll", "lib/netcoreapp3.1/Microsoft.TestPlatform.CrossPlatEngine.dll", "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll", "lib/netcoreapp3.1/Microsoft.TestPlatform.Utilities.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.Common.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll", "lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/testhost.deps.json", "lib/netcoreapp3.1/testhost.dll", "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/x64/msdia140.dll", "lib/netcoreapp3.1/x86/msdia140.dll", "lib/netcoreapp3.1/zh-<PERSON>/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CommunicationUtilities.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CrossPlatEngine.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.Common.resources.dll", "microsoft.testplatform.testhost.17.5.0.nupkg.sha512", "microsoft.testplatform.testhost.nuspec"]}, "Microsoft.Win32.Registry/5.0.0": {"sha512": "dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "type": "package", "path": "microsoft.win32.registry/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.xml", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.xml", "microsoft.win32.registry.5.0.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.xml", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "useSharedDesignerContext.txt", "version.txt"]}, "MongoDB.Bson/2.23.1": {"sha512": "IX9tycM35xK5hFwnU+rzharPJOtKYtON6E6Lp2nwOVjh40TUcS/HYToEEWZkLgqKNMCfYPK3Fz3QUCxzhkQRGA==", "type": "package", "path": "mongodb.bson/2.23.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/netstandard2.0/MongoDB.Bson.dll", "lib/netstandard2.0/MongoDB.Bson.xml", "lib/netstandard2.1/MongoDB.Bson.dll", "lib/netstandard2.1/MongoDB.Bson.xml", "mongodb.bson.2.23.1.nupkg.sha512", "mongodb.bson.nuspec", "packageIcon.png"]}, "MongoDB.Driver/2.23.1": {"sha512": "kidqCwGBuLBx2IcW4os3J6zsp9yaUWm7Sp8G08Nm2RVRSAf0cJXfsynl2wRWpHh0HgfIzzwkevP/qhfsKfu8bQ==", "type": "package", "path": "mongodb.driver/2.23.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/netstandard2.0/MongoDB.Driver.dll", "lib/netstandard2.0/MongoDB.Driver.xml", "lib/netstandard2.1/MongoDB.Driver.dll", "lib/netstandard2.1/MongoDB.Driver.xml", "mongodb.driver.2.23.1.nupkg.sha512", "mongodb.driver.nuspec", "packageIcon.png"]}, "MongoDB.Driver.Core/2.23.1": {"sha512": "K8LMdnVgT82vdbSllv8VzjPOLa9k5rLcCBd1fG45z+QGJNPWzAFW5lLgLJQ7xXuJgQIwvP1DBx6X6ecWBtox7g==", "type": "package", "path": "mongodb.driver.core/2.23.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "THIRD-PARTY-NOTICES", "lib/netstandard2.0/MongoDB.Driver.Core.dll", "lib/netstandard2.0/MongoDB.Driver.Core.xml", "lib/netstandard2.1/MongoDB.Driver.Core.dll", "lib/netstandard2.1/MongoDB.Driver.Core.xml", "mongodb.driver.core.2.23.1.nupkg.sha512", "mongodb.driver.core.nuspec", "packageIcon.png"]}, "MongoDB.Driver.GridFS/2.23.1": {"sha512": "rCSIOvjYF1tr3srZGchG+ra1v9rUBhWrD8AhNDVn07B8Z+UTzhjw7MIqlis8Va8NTfzHeIpP1wEudKrngH4+4w==", "type": "package", "path": "mongodb.driver.gridfs/2.23.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/netstandard2.0/MongoDB.Driver.GridFS.dll", "lib/netstandard2.0/MongoDB.Driver.GridFS.xml", "lib/netstandard2.1/MongoDB.Driver.GridFS.dll", "lib/netstandard2.1/MongoDB.Driver.GridFS.xml", "mongodb.driver.gridfs.2.23.1.nupkg.sha512", "mongodb.driver.gridfs.nuspec", "packageIcon.png"]}, "MongoDB.Libmongocrypt/1.8.0": {"sha512": "fgNw8Dxpkq7mpoaAYes8cfnPRzvFIoB8oL9GPXwi3op/rONftl0WAeg4akRLcxfoVuUvuUO2wGoVBr3JzJ7Svw==", "type": "package", "path": "mongodb.libmongocrypt/1.8.0", "files": [".nupkg.metadata", ".signature.p7s", "License.txt", "build/MongoDB.Libmongocrypt.targets", "content/libmongocrypt.dylib", "content/libmongocrypt.so", "content/mongocrypt.dll", "contentFiles/any/net472/libmongocrypt.dylib", "contentFiles/any/net472/libmongocrypt.so", "contentFiles/any/net472/mongocrypt.dll", "contentFiles/any/netstandard2.0/libmongocrypt.dylib", "contentFiles/any/netstandard2.0/libmongocrypt.so", "contentFiles/any/netstandard2.0/mongocrypt.dll", "contentFiles/any/netstandard2.1/libmongocrypt.dylib", "contentFiles/any/netstandard2.1/libmongocrypt.so", "contentFiles/any/netstandard2.1/mongocrypt.dll", "lib/net472/MongoDB.Libmongocrypt.dll", "lib/netstandard2.0/MongoDB.Libmongocrypt.dll", "lib/netstandard2.1/MongoDB.Libmongocrypt.dll", "mongodb.libmongocrypt.1.8.0.nupkg.sha512", "mongodb.libmongocrypt.nuspec", "runtimes/linux/native/libmongocrypt.so", "runtimes/osx/native/libmongocrypt.dylib", "runtimes/win/native/mongocrypt.dll"]}, "MSTest.TestAdapter/3.0.2": {"sha512": "K1hhtevCzbogylmAnCluV/M1/Ajr5EKfxWq3nv+iYxt+gHLN4gSqT6B3tPnNqUbE4aWT1aUHagcRMnARt45NzQ==", "type": "package", "path": "mstest.testadapter/3.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/_localization/cs/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_localization/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_localization/cs/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/cs/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_localization/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_localization/de/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_localization/de/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_localization/de/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/de/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_localization/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_localization/es/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_localization/es/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_localization/es/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/es/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_localization/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_localization/fr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_localization/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_localization/fr/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/fr/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_localization/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_localization/it/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_localization/it/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_localization/it/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/it/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_localization/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_localization/ja/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_localization/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_localization/ja/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/ja/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_localization/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_localization/ko/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_localization/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_localization/ko/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/ko/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_localization/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_localization/pl/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_localization/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_localization/pl/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/pl/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_localization/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_localization/pt-BR/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_localization/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_localization/pt-BR/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/pt-BR/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_localization/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_localization/ru/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_localization/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_localization/ru/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/ru/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_localization/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_localization/tr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_localization/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_localization/tr/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/tr/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_localization/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_localization/zh-Hans/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_localization/zh-Hans/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_localization/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/zh-Hans/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_localization/zh-Hans/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_localization/zh-Hant/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/_localization/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll", "build/_localization/zh-Hant/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_localization/zh-Hant/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_localization/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll", "build/_localization/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/net462/MSTest.TestAdapter.props", "build/net462/MSTest.TestAdapter.targets", "build/net462/Microsoft.TestPlatform.AdapterUtilities.dll", "build/net462/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "build/net462/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/net462/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "build/net462/cs/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net462/de/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net462/es/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net462/fr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net462/it/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net462/ja/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net462/ko/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net462/pl/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net462/pt-BR/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net462/ru/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net462/tr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net462/zh-<PERSON>/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net462/zh-Hant/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net6.0/MSTest.TestAdapter.props", "build/net6.0/MSTest.TestAdapter.targets", "build/net6.0/Microsoft.TestPlatform.AdapterUtilities.dll", "build/net6.0/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "build/net6.0/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "build/net6.0/cs/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net6.0/de/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net6.0/es/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net6.0/fr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net6.0/it/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net6.0/ja/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net6.0/ko/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net6.0/pl/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net6.0/pt-BR/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net6.0/ru/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net6.0/tr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net6.0/winui/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/net6.0/zh-<PERSON>/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/net6.0/zh-Hant/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netcoreapp3.1/MSTest.TestAdapter.props", "build/netcoreapp3.1/Microsoft.TestPlatform.AdapterUtilities.dll", "build/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "build/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "build/netcoreapp3.1/cs/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netcoreapp3.1/de/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netcoreapp3.1/es/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netcoreapp3.1/fr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netcoreapp3.1/it/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netcoreapp3.1/ja/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netcoreapp3.1/ko/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netcoreapp3.1/pl/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netcoreapp3.1/ru/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netcoreapp3.1/tr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netstandard2.0/MSTest.TestAdapter.props", "build/netstandard2.0/Microsoft.TestPlatform.AdapterUtilities.dll", "build/netstandard2.0/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "build/netstandard2.0/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/netstandard2.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "build/netstandard2.0/cs/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netstandard2.0/de/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netstandard2.0/es/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netstandard2.0/fr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netstandard2.0/it/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netstandard2.0/ja/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netstandard2.0/ko/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netstandard2.0/pl/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netstandard2.0/pt-BR/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netstandard2.0/ru/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netstandard2.0/tr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netstandard2.0/zh-<PERSON>/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/netstandard2.0/zh-Hant/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/uap10.0/MSTest.TestAdapter.props", "build/uap10.0/MSTest.TestAdapter.targets", "build/uap10.0/Microsoft.TestPlatform.AdapterUtilities.dll", "build/uap10.0/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "build/uap10.0/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "build/uap10.0/cs/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/uap10.0/de/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/uap10.0/es/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/uap10.0/fr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/uap10.0/it/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/uap10.0/ja/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/uap10.0/ko/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/uap10.0/pl/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/uap10.0/pt-BR/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/uap10.0/ru/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/uap10.0/tr/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/uap10.0/zh-<PERSON>/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "build/uap10.0/zh-Hant/Microsoft.TestPlatform.AdapterUtilities.resources.dll", "mstest.testadapter.3.0.2.nupkg.sha512", "mstest.testadapter.nuspec"]}, "MSTest.TestFramework/3.0.2": {"sha512": "cuCE5PXK0mX0FIfY3yzDNKFqCf2FTxZhdH/Bxg4662MAWZg4TnG3rYsFzNpPECrvn8Ry6lG8TaT2Ltg1NZkC6Q==", "type": "package", "path": "mstest.testframework/3.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/net6.0/MSTest.TestFramework.targets", "build/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "build/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "build/net6.0/winui/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "build/net6.0/winui/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net462/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "lib/net462/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net462/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/net462/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net462/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net462/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/net6.0/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net6.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/net6.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "lib/netstandard2.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard2.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/netstandard2.0/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard2.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "lib/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "lib/uap10.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "mstest.testframework.3.0.2.nupkg.sha512", "mstest.testframework.nuspec"]}, "Newtonsoft.Json/13.0.1": {"sha512": "ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "type": "package", "path": "newtonsoft.json/13.0.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.1.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "Npgsql/7.0.6": {"sha512": "TAqvwRnm3NJ0QvN7cvu6geJkbI0XPzGVRElVY5hF4gsgA+BnE12x6GM1TLhdeq+7ZKvvo3BD8jXKnXmr3tvdEw==", "type": "package", "path": "npgsql/7.0.6", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net5.0/Npgsql.dll", "lib/net5.0/Npgsql.xml", "lib/net6.0/Npgsql.dll", "lib/net6.0/Npgsql.xml", "lib/net7.0/Npgsql.dll", "lib/net7.0/Npgsql.xml", "lib/netcoreapp3.1/Npgsql.dll", "lib/netcoreapp3.1/Npgsql.xml", "lib/netstandard2.0/Npgsql.dll", "lib/netstandard2.0/Npgsql.xml", "lib/netstandard2.1/Npgsql.dll", "lib/netstandard2.1/Npgsql.xml", "npgsql.7.0.6.nupkg.sha512", "npgsql.nuspec", "postgresql.png"]}, "NuGet.Frameworks/5.11.0": {"sha512": "eaiXkUjC4NPcquGWzAGMXjuxvLwc6XGKMptSyOGQeT0X70BUZObuybJFZLA0OfTdueLd3US23NBPTBb6iF3V1Q==", "type": "package", "path": "nuget.frameworks/5.11.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net40/NuGet.Frameworks.dll", "lib/net40/NuGet.Frameworks.xml", "lib/net472/NuGet.Frameworks.dll", "lib/net472/NuGet.Frameworks.xml", "lib/netstandard2.0/NuGet.Frameworks.dll", "lib/netstandard2.0/NuGet.Frameworks.xml", "nuget.frameworks.5.11.0.nupkg.sha512", "nuget.frameworks.nuspec"]}, "PlaywrightExtraSharp2/*******": {"sha512": "UGmBTEq53qP9kXXdD4BwFuInPdbLhUnppqPxIVlh5R0vR4ML9qIOepAWS7fGXm3LlfZn4E0grE5/w5p+LHcs3Q==", "type": "package", "path": "playwrightextrasharp2/*******", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net7.0/PlaywrightExtraSharp.dll", "lib/netstandard2.0/PlaywrightExtraSharp.dll", "playwrightextrasharp2.*******.nupkg.sha512", "playwrightextrasharp2.nuspec"]}, "RestSharp/110.2.0": {"sha512": "FXGw0IMcqY7yO/hzS9QrD3iNswNgb9UxJnxWmfOxmGs4kRlZWqdtBoGPLuhlbgsDzX1RFo4WKui8TGGKXWKalw==", "type": "package", "path": "restsharp/110.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net471/RestSharp.dll", "lib/net471/RestSharp.xml", "lib/net6.0/RestSharp.dll", "lib/net6.0/RestSharp.xml", "lib/net7.0/RestSharp.dll", "lib/net7.0/RestSharp.xml", "lib/netstandard2.0/RestSharp.dll", "lib/netstandard2.0/RestSharp.xml", "restsharp.110.2.0.nupkg.sha512", "restsharp.nuspec", "restsharp.png"]}, "SharpCompress/0.30.1": {"sha512": "XqD4TpfyYGa7QTPzaGlMVbcecKnXy4YmYLDWrU+JIj7IuRNl7DH2END+Ll7ekWIY8o3dAMWLFDE1xdhfIWD1nw==", "type": "package", "path": "sharpcompress/0.30.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/SharpCompress.dll", "lib/net5.0/SharpCompress.dll", "lib/netcoreapp3.1/SharpCompress.dll", "lib/netstandard2.0/SharpCompress.dll", "lib/netstandard2.1/SharpCompress.dll", "sharpcompress.0.30.1.nupkg.sha512", "sharpcompress.nuspec"]}, "Snappier/1.0.0": {"sha512": "rFtK2KEI9hIe8gtx3a0YDXdHOpedIf9wYCEYtBEmtlyiWVX3XlCNV03JrmmAi/Cdfn7dxK+k0sjjcLv4fpHnqA==", "type": "package", "path": "snappier/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "COPYING.txt", "lib/net5.0/Snappier.dll", "lib/net5.0/Snappier.xml", "lib/netcoreapp3.0/Snappier.dll", "lib/netcoreapp3.0/Snappier.xml", "lib/netstandard2.0/Snappier.dll", "lib/netstandard2.0/Snappier.xml", "lib/netstandard2.1/Snappier.dll", "lib/netstandard2.1/Snappier.xml", "snappier.1.0.0.nupkg.sha512", "snappier.nuspec"]}, "System.Buffers/4.5.1": {"sha512": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "type": "package", "path": "system.buffers/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Buffers.dll", "lib/net461/System.Buffers.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.1.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ComponentModel.Annotations/5.0.0": {"sha512": "dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "type": "package", "path": "system.componentmodel.annotations/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net461/System.ComponentModel.Annotations.dll", "lib/netcore50/System.ComponentModel.Annotations.dll", "lib/netstandard1.4/System.ComponentModel.Annotations.dll", "lib/netstandard2.0/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.xml", "lib/portable-net45+win8/_._", "lib/win8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net461/System.ComponentModel.Annotations.dll", "ref/net461/System.ComponentModel.Annotations.xml", "ref/netcore50/System.ComponentModel.Annotations.dll", "ref/netcore50/System.ComponentModel.Annotations.xml", "ref/netcore50/de/System.ComponentModel.Annotations.xml", "ref/netcore50/es/System.ComponentModel.Annotations.xml", "ref/netcore50/fr/System.ComponentModel.Annotations.xml", "ref/netcore50/it/System.ComponentModel.Annotations.xml", "ref/netcore50/ja/System.ComponentModel.Annotations.xml", "ref/netcore50/ko/System.ComponentModel.Annotations.xml", "ref/netcore50/ru/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hans/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/System.ComponentModel.Annotations.dll", "ref/netstandard1.1/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/System.ComponentModel.Annotations.dll", "ref/netstandard1.3/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/System.ComponentModel.Annotations.dll", "ref/netstandard1.4/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard2.0/System.ComponentModel.Annotations.dll", "ref/netstandard2.0/System.ComponentModel.Annotations.xml", "ref/netstandard2.1/System.ComponentModel.Annotations.dll", "ref/netstandard2.1/System.ComponentModel.Annotations.xml", "ref/portable-net45+win8/_._", "ref/win8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.annotations.5.0.0.nupkg.sha512", "system.componentmodel.annotations.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Diagnostics.DiagnosticSource/8.0.0": {"sha512": "c9xLpVz6PL9lp/djOWtk5KPDZq3cSYpmXoJQY524EOtuFl5z9ZtsotpsyrDW40U1DRnQSYvcPKEUV0X//u6gkQ==", "type": "package", "path": "system.diagnostics.diagnosticsource/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "lib/net462/System.Diagnostics.DiagnosticSource.dll", "lib/net462/System.Diagnostics.DiagnosticSource.xml", "lib/net6.0/System.Diagnostics.DiagnosticSource.dll", "lib/net6.0/System.Diagnostics.DiagnosticSource.xml", "lib/net7.0/System.Diagnostics.DiagnosticSource.dll", "lib/net7.0/System.Diagnostics.DiagnosticSource.xml", "lib/net8.0/System.Diagnostics.DiagnosticSource.dll", "lib/net8.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.EventLog/8.0.0": {"sha512": "fdYxcRjQqTTacKId/2IECojlDSFvp7LP5N78+0z/xH7v/Tuw5ZAxu23Y6PTCRinqyu2ePx+Gn1098NC6jM6d+A==", "type": "package", "path": "system.diagnostics.eventlog/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.EventLog.targets", "lib/net462/System.Diagnostics.EventLog.dll", "lib/net462/System.Diagnostics.EventLog.xml", "lib/net6.0/System.Diagnostics.EventLog.dll", "lib/net6.0/System.Diagnostics.EventLog.xml", "lib/net7.0/System.Diagnostics.EventLog.dll", "lib/net7.0/System.Diagnostics.EventLog.xml", "lib/net8.0/System.Diagnostics.EventLog.dll", "lib/net8.0/System.Diagnostics.EventLog.xml", "lib/netstandard2.0/System.Diagnostics.EventLog.dll", "lib/netstandard2.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.xml", "system.diagnostics.eventlog.8.0.0.nupkg.sha512", "system.diagnostics.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "System.Memory/4.5.5": {"sha512": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "type": "package", "path": "system.memory/4.5.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.5.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Emit.Lightweight/4.7.0": {"sha512": "a4OLB4IITxAXJeV74MDx49Oq2+PsF6Sml54XAFv+2RyWwtDBcabzoxiiJRhdhx+gaohLh4hEGCLQyBozXoQPqA==", "type": "package", "path": "system.reflection.emit.lightweight/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.Lightweight.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Reflection.Emit.Lightweight.dll", "lib/netstandard1.0/System.Reflection.Emit.Lightweight.xml", "lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll", "lib/netstandard2.0/System.Reflection.Emit.Lightweight.dll", "lib/netstandard2.0/System.Reflection.Emit.Lightweight.xml", "lib/netstandard2.1/_._", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.dll", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/de/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/es/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/it/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.Lightweight.xml", "ref/netstandard2.0/System.Reflection.Emit.Lightweight.dll", "ref/netstandard2.0/System.Reflection.Emit.Lightweight.xml", "ref/netstandard2.1/_._", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.Emit.Lightweight.dll", "runtimes/aot/lib/netcore50/System.Reflection.Emit.Lightweight.xml", "system.reflection.emit.lightweight.4.7.0.nupkg.sha512", "system.reflection.emit.lightweight.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Metadata/1.6.0": {"sha512": "COC1aiAJjCoA5GBF+QKL2uLqEBew4JsCkQmoHKbN3TlOZKa2fKLz5CpiRQKDz0RsAOEGsVKqOD5bomsXq/4STQ==", "type": "package", "path": "system.reflection.metadata/1.6.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.1/System.Reflection.Metadata.dll", "lib/netstandard1.1/System.Reflection.Metadata.xml", "lib/netstandard2.0/System.Reflection.Metadata.dll", "lib/netstandard2.0/System.Reflection.Metadata.xml", "lib/portable-net45+win8/System.Reflection.Metadata.dll", "lib/portable-net45+win8/System.Reflection.Metadata.xml", "system.reflection.metadata.1.6.0.nupkg.sha512", "system.reflection.metadata.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.AccessControl/5.0.0": {"sha512": "dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "type": "package", "path": "system.security.accesscontrol/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/netstandard1.3/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.xml", "ref/netstandard1.3/System.Security.AccessControl.dll", "ref/netstandard1.3/System.Security.AccessControl.xml", "ref/netstandard1.3/de/System.Security.AccessControl.xml", "ref/netstandard1.3/es/System.Security.AccessControl.xml", "ref/netstandard1.3/fr/System.Security.AccessControl.xml", "ref/netstandard1.3/it/System.Security.AccessControl.xml", "ref/netstandard1.3/ja/System.Security.AccessControl.xml", "ref/netstandard1.3/ko/System.Security.AccessControl.xml", "ref/netstandard1.3/ru/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.Security.AccessControl.xml", "ref/netstandard2.0/System.Security.AccessControl.dll", "ref/netstandard2.0/System.Security.AccessControl.xml", "ref/uap10.0.16299/_._", "runtimes/win/lib/net46/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard1.3/System.Security.AccessControl.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.accesscontrol.5.0.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Principal.Windows/5.0.0": {"sha512": "t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "type": "package", "path": "system.security.principal.windows/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.5.0.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encodings.Web/8.0.0": {"sha512": "yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "type": "package", "path": "system.text.encodings.web/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Text.Encodings.Web.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Text.Encodings.Web.targets", "lib/net462/System.Text.Encodings.Web.dll", "lib/net462/System.Text.Encodings.Web.xml", "lib/net6.0/System.Text.Encodings.Web.dll", "lib/net6.0/System.Text.Encodings.Web.xml", "lib/net7.0/System.Text.Encodings.Web.dll", "lib/net7.0/System.Text.Encodings.Web.xml", "lib/net8.0/System.Text.Encodings.Web.dll", "lib/net8.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.8.0.0.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/8.0.0": {"sha512": "OdrZO2WjkiEG6ajEFRABTRCi/wuXQPxeV6g8xvUJqdxMvvuCCEk86zPla8UiIQJz3durtUEbNyY/3lIhS0yZvQ==", "type": "package", "path": "system.text.json/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net6.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net6.0/System.Text.Json.dll", "lib/net6.0/System.Text.Json.xml", "lib/net7.0/System.Text.Json.dll", "lib/net7.0/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.8.0.0.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "ZstdSharp.Port/0.7.3": {"sha512": "U9Ix4l4cl58Kzz1rJzj5hoVTjmbx1qGMwzAcbv1j/d3NzrFaESIurQyg+ow4mivCgkE3S413y+U9k4WdnEIkRA==", "type": "package", "path": "zstdsharp.port/0.7.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/ZstdSharp.dll", "lib/net5.0/ZstdSharp.dll", "lib/net6.0/ZstdSharp.dll", "lib/net7.0/ZstdSharp.dll", "lib/netcoreapp3.1/ZstdSharp.dll", "lib/netstandard2.0/ZstdSharp.dll", "lib/netstandard2.1/ZstdSharp.dll", "zstdsharp.port.0.7.3.nupkg.sha512", "zstdsharp.port.nuspec"]}, "PakScraper/1.0.0": {"type": "project", "path": "../src/PakScraper.csproj", "msbuildProject": "../src/PakScraper.csproj"}}, "projectFileDependencyGroups": {"net6.0": ["MSTest.TestAdapter >= 3.0.2", "MSTest.TestFramework >= 3.0.2", "Microsoft.NET.Test.Sdk >= 17.5.0", "PakScraper >= 1.0.0", "coverlet.collector >= 3.2.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\scrapers13-07\\paknsave\\tests\\PakScraperTests.csproj", "projectName": "PakScraperTests", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\scrapers13-07\\paknsave\\tests\\PakScraperTests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\scrapers13-07\\paknsave\\tests\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\scrapers13-07\\paknsave\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\scrapers13-07\\paknsave\\src\\PakScraper.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\scrapers13-07\\paknsave\\src\\PakScraper.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"MSTest.TestAdapter": {"target": "Package", "version": "[3.0.2, )"}, "MSTest.TestFramework": {"target": "Package", "version": "[3.0.2, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.5.0, )"}, "coverlet.collector": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[3.2.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.303\\RuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1603", "level": "Warning", "warningLevel": 1, "message": "PakScraper depends on <PERSON><PERSON> (>= 2.1.45) but <PERSON>pper 2.1.45 was not found. Dapper 2.1.66 was resolved instead.", "libraryId": "<PERSON><PERSON>", "targetGraphs": ["net6.0"]}, {"code": "NU1603", "level": "Warning", "warningLevel": 1, "message": "PakScraper depends on Npgsql (>= 7.0.5) but Npgsql 7.0.5 was not found. Npgsql 7.0.6 was resolved instead.", "libraryId": "Npgsql", "targetGraphs": ["net6.0"]}]}