{"comment": "Do not edit this file, use utils/roll_browser.js", "browsers": [{"name": "chromium", "revision": "1048", "installByDefault": true, "revisionOverrides": {"win64": "1050"}, "browserVersion": "111.0.5563.19"}, {"name": "chromium-with-symbols", "revision": "1048", "installByDefault": false, "browserVersion": "111.0.5563.19"}, {"name": "chromium-tip-of-tree", "revision": "1085", "installByDefault": false, "browserVersion": "112.0.5585.0"}, {"name": "firefox", "revision": "1378", "installByDefault": true, "browserVersion": "109.0"}, {"name": "firefox-beta", "revision": "1380", "installByDefault": false, "browserVersion": "110.0b7"}, {"name": "webkit", "revision": "1792", "installByDefault": true, "revisionOverrides": {"mac10.14": "1446", "mac10.15": "1616", "ubuntu18.04": "1728"}, "browserVersion": "16.4"}, {"name": "ffmpeg", "revision": "1008", "installByDefault": true}, {"name": "android", "revision": "1000", "installByDefault": false}]}