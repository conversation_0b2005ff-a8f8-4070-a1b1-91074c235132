@echo off
echo 🔧 .NET Scraper Fix Script
echo ========================

echo.
echo 🔍 Step 1: Clearing NuGet caches
dotnet nuget locals all --clear

echo.
echo 🔍 Step 2: Listing current package sources
dotnet nuget list source

echo.
echo 🔍 Step 3: Removing problematic local sources
dotnet nuget remove source "Microsoft Visual Studio Offline Packages" 2>nul
dotnet nuget remove source "Microsoft SDKs" 2>nul
dotnet nuget remove source "C:\Program Files (x86)\Microsoft SDKs\NuGetPackages\" 2>nul

echo.
echo 🔍 Step 4: Ensuring nuget.org source is available
dotnet nuget add source https://api.nuget.org/v3/index.json -n nuget.org 2>nul

echo.
echo 🔍 Step 5: Testing PakNSave scraper restore
cd paknsave\src
echo Current directory: %CD%
dotnet restore
if %ERRORLEVEL% NEQ 0 (
    echo ❌ PakNSave restore failed
    goto :new_world
) else (
    echo ✅ PakNSave restore successful
)

echo.
echo 🔍 Step 6: Testing PakNSave build
dotnet build
if %ERRORLEVEL% NEQ 0 (
    echo ❌ PakNSave build failed
) else (
    echo ✅ PakNSave build successful
)

:new_world
cd ..\..\new-world\src
echo.
echo 🔍 Step 7: Testing New World scraper restore
echo Current directory: %CD%
dotnet restore
if %ERRORLEVEL% NEQ 0 (
    echo ❌ New World restore failed
    goto :end
) else (
    echo ✅ New World restore successful
)

echo.
echo 🔍 Step 8: Testing New World build
dotnet build
if %ERRORLEVEL% NEQ 0 (
    echo ❌ New World build failed
) else (
    echo ✅ New World build successful
)

:end
cd ..\..
echo.
echo 🎉 .NET fix script completed!
echo.
echo 📝 If issues persist, try:
echo   1. Restart Visual Studio / VS Code
echo   2. Run: dotnet --info
echo   3. Check Windows firewall/antivirus blocking NuGet
echo   4. Run as Administrator
pause