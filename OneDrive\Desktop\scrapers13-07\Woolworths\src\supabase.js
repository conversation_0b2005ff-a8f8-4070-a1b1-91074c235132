"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.establishSupabase = establishSupabase;
exports.upsertProductToSupabase = upsertProductToSupabase;
exports.uploadImageToSupabase = uploadImageToSupabase;
const supabase_js_1 = require("@supabase/supabase-js");
const dotenv = __importStar(require("dotenv"));
dotenv.config();
dotenv.config({ path: `.env.local`, override: true });
const utilities_js_1 = require("./utilities.js");
let supabase;
let storeId; // Woolworths store row id
function establishSupabase() {
    const url = process.env.SUPABASE_URL;
    const key = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;
    if (!url || !key) {
        throw Error("SUPABASE_URL or SUPABASE_ANON_KEY not set in env");
    }
    supabase = (0, supabase_js_1.createClient)(url, key, { auth: { persistSession: false } });
}
function ensureStoreRow() {
    return __awaiter(this, void 0, void 0, function* () {
        if (storeId !== undefined)
            return storeId;
        const { data, error } = yield supabase
            .from("stores")
            .select("id")
            .eq("name", "woolworths")
            .single();
        if (error && error.code !== "PGRST116") {
            throw error;
        }
        if (data === null || data === void 0 ? void 0 : data.id) {
            storeId = data.id;
            return storeId;
        }
        // Insert store row if not exists
        const { data: insertData, error: insertErr } = yield supabase
            .from("stores")
            .insert({ name: "woolworths" })
            .select("id")
            .single();
        if (insertErr)
            throw insertErr;
        storeId = insertData.id;
        return storeId;
    });
}
function upsertProductToSupabase(scraped) {
    return __awaiter(this, void 0, void 0, function* () {
        if (!supabase)
            throw Error("Supabase client not initialised");
        const sId = yield ensureStoreRow();
        // Upsert into products
        const productPayload = {
            id: scraped.id,
            name: scraped.name,
            size: scraped.size,
            unit_price: scraped.unitPrice,
            unit_name: scraped.unitName,
            original_unit_qty: scraped.originalUnitQuantity,
            source_site: scraped.sourceSite,
            last_checked: scraped.lastChecked.toISOString(),
            last_updated: scraped.lastUpdated.toISOString()
        };
        const { error: prodErr } = yield supabase.from("products").upsert(productPayload, { onConflict: "id" });
        if (prodErr) {
            (0, utilities_js_1.logError)("Supabase upsert product failed: " + prodErr.message);
            return 4 /* UpsertResponse.Failed */;
        }
        // Insert price row (always) – logic to omit duplicates can be added later
        const pricePayload = {
            product_id: scraped.id,
            store_id: sId,
            price: scraped.currentPrice,
            recorded_at: scraped.lastUpdated.toISOString()
        };
        const { error: priceErr } = yield supabase.from("prices").insert(pricePayload);
        if (priceErr) {
            (0, utilities_js_1.logError)("Supabase insert price failed: " + priceErr.message);
            return 4 /* UpsertResponse.Failed */;
        }
        (0, utilities_js_1.log)(utilities_js_1.colour.green, `  Upserted: ${scraped.name.slice(0, 45).padEnd(45)} | $${scraped.currentPrice}`);
        return 1 /* UpsertResponse.PriceChanged */; // coarse; can refine later
    });
}
// Upload product image to Supabase Storage
function uploadImageToSupabase(imageUrl, product) {
    return __awaiter(this, void 0, void 0, function* () {
        if (!supabase)
            throw Error("Supabase client not initialised");
        try {
            // Download image from Woolworths
            const imageResponse = yield fetch(imageUrl);
            if (!imageResponse.ok) {
                (0, utilities_js_1.logError)(`Failed to download image from ${imageUrl}: ${imageResponse.statusText}`);
                return false;
            }
            const imageBuffer = yield imageResponse.arrayBuffer();
            const imageBlob = new Blob([imageBuffer], { type: 'image/jpeg' });
            // Generate file path: products/{productId}.jpg
            const filePath = `products/${product.id}.jpg`;
            // Upload to Supabase Storage
            const { data, error } = yield supabase.storage
                .from('product-images')
                .upload(filePath, imageBlob, {
                contentType: 'image/jpeg',
                upsert: true // Overwrite if exists
            });
            if (error) {
                (0, utilities_js_1.logError)(`Supabase storage upload failed: ${error.message}`);
                return false;
            }
            // Get public URL for the uploaded image
            const { data: publicUrlData } = supabase.storage
                .from('product-images')
                .getPublicUrl(filePath);
            (0, utilities_js_1.log)(utilities_js_1.colour.blue, `  Image uploaded: ${product.id} -> ${publicUrlData.publicUrl}`);
            return true;
        }
        catch (err) {
            (0, utilities_js_1.logError)(`Image upload error: ${err.message}`);
            return false;
        }
    });
}
