import { MongoClient, Db, Collection, ObjectId } from "mongodb";
import * as dotenv from "dotenv";
dotenv.config();

import { Product } from "./typings.js";
import { colour, log, logError } from "./utilities.js";

let client: MongoClient;
let db: Db;
let consolidatedProductsCollection: Collection;
let brandsCollection: Collection;
let categoryHierarchyCollection: Collection;

export async function initializeConsolidatedProductsMongoDB() {
  const connectionString = process.env.MONGODB_CONNECTION_STRING;
  const databaseName = process.env.MONGODB_DATABASE_NAME || "nz-supermarket-scraper";
  
  if (!connectionString) {
    throw Error("MONGODB_CONNECTION_STRING not set in env");
  }

  try {
    client = new MongoClient(connectionString);
    await client.connect();
    db = client.db(databaseName);
    
    consolidatedProductsCollection = db.collection("consolidatedProducts");
    brandsCollection = db.collection("brands");
    categoryHierarchyCollection = db.collection("categoryHierarchy");
    
    log(colour.green, "✅ Consolidated products MongoDB initialized");
    
    // Initialize basic categories if they don't exist
    await ensureBasicCategories();
    
  } catch (error: any) {
    logError(`Failed to initialize consolidated products MongoDB: ${error.message}`);
    throw error;
  }
}

async function ensureBasicCategories() {
  try {
    const existingCategories = await categoryHierarchyCollection.countDocuments();
    
    if (existingCategories === 0) {
      // Insert main categories
      const mainCategories = [
        { name: "Fresh Foods", parentId: null, level: 0, sortOrder: 1 },
        { name: "Chilled & Frozen", parentId: null, level: 0, sortOrder: 2 },
        { name: "Pantry & Dry Goods", parentId: null, level: 0, sortOrder: 3 },
        { name: "Beverages", parentId: null, level: 0, sortOrder: 4 },
        { name: "Health & Household", parentId: null, level: 0, sortOrder: 5 }
      ];
      
      const insertResult = await categoryHierarchyCollection.insertMany(mainCategories);
      log(colour.blue, `✅ Created ${insertResult.insertedCount} main categories`);
      
      // Insert subcategories
      const freshFoodsId = Object.values(insertResult.insertedIds).find(async (id) => {
        const cat = await categoryHierarchyCollection.findOne({ _id: id });
        return cat?.name === "Fresh Foods";
      });
      
      // For now, just create basic structure - can be expanded later
      log(colour.blue, "✅ Basic category structure created");
    }
  } catch (error: any) {
    logError(`Failed to ensure basic categories: ${error.message}`);
  }
}

export async function processConsolidatedProductMongoDB(scrapedProduct: Product): Promise<string | null> {
  if (!consolidatedProductsCollection) {
    logError("Consolidated products collection not initialized");
    return null;
  }

  try {
    // For now, create a simple consolidated product for each scraped product
    // This can be enhanced later with proper product matching
    const normalizedName = normalizeProductName(scrapedProduct.name, scrapedProduct.size);
    const now = new Date();
    
    // Check if a consolidated product already exists for this store product
    const existingProduct = await consolidatedProductsCollection.findOne({
      "variants.storeProductId": scrapedProduct.id
    });
    
    if (existingProduct) {
      // Update existing consolidated product
      await consolidatedProductsCollection.updateOne(
        { _id: existingProduct._id },
        {
          $set: {
            "variants.$[variant].lastSeen": now,
            "variants.$[variant].storeUnitPrice": scrapedProduct.unitPrice,
            "variants.$[variant].storeUnitName": scrapedProduct.unitName,
            updatedAt: now
          }
        },
        {
          arrayFilters: [{ "variant.storeProductId": scrapedProduct.id }]
        }
      );
      
      log(colour.cyan, `  Updated consolidated product: ${scrapedProduct.name.slice(0, 40)}`);
      return existingProduct._id.toString();
    } else {
      // Create new consolidated product
      const newProduct = {
        normalizedName,
        displayName: scrapedProduct.name,
        primarySize: scrapedProduct.size,
        categoryId: null, // TODO: Implement category mapping
        brandId: null, // TODO: Implement brand extraction
        matchConfidence: 100,
        manualMatch: false,
        variants: [{
          storeProductId: scrapedProduct.id,
          storeId: "woolworths", // Store identifier
          storeName: scrapedProduct.name,
          storeSize: scrapedProduct.size,
          storeUnitPrice: scrapedProduct.unitPrice,
          storeUnitName: scrapedProduct.unitName,
          lastSeen: now,
          isActive: true,
          imageUrl: null
        }],
        sizeVariants: scrapedProduct.size ? [{
          sizeName: scrapedProduct.size,
          sizeWeightGrams: null,
          sizeVolumeMl: null,
          isPrimarySize: true
        }] : [],
        currentPrices: [{
          storeId: "woolworths",
          price: scrapedProduct.currentPrice,
          isSpecial: false,
          wasAvailable: true,
          recordedAt: now
        }],
        createdAt: now,
        updatedAt: now
      };
      
      const insertResult = await consolidatedProductsCollection.insertOne(newProduct);
      log(colour.cyan, `  Created consolidated product: ${scrapedProduct.name.slice(0, 40)}`);
      return insertResult.insertedId.toString();
    }
    
  } catch (error: any) {
    logError(`Failed to process consolidated product: ${error.message}`);
    return null;
  }
}

// Helper function to normalize product names
function normalizeProductName(name: string, size?: string): string {
  let normalized = name.toLowerCase()
    .replace(/[^a-z0-9\s]/g, ' ')
    .replace(/\s+/g, '_')
    .trim();
  
  if (size) {
    const normalizedSize = size.toLowerCase()
      .replace(/[^a-z0-9]/g, '')
      .trim();
    normalized += '_' + normalizedSize;
  }
  
  return normalized;
}

// Clean up function - close connections
export async function closeConsolidatedProductsMongoDB() {
  try {
    if (client) {
      await client.close();
    }
    log(colour.blue, "✅ Consolidated products MongoDB connections closed");
  } catch (error: any) {
    logError(`Failed to close MongoDB connections: ${error.message}`);
  }
}

// Search consolidated products
export async function searchConsolidatedProducts(query: string, limit: number = 20): Promise<any[]> {
  if (!consolidatedProductsCollection) return [];
  
  try {
    const results = await consolidatedProductsCollection.find({
      $text: { $search: query }
    })
    .sort({ score: { $meta: "textScore" } })
    .limit(limit)
    .toArray();
    
    return results;
  } catch (error: any) {
    logError(`Search failed: ${error.message}`);
    return [];
  }
}

// Get consolidated product with all store prices
export async function getConsolidatedProductWithPrices(productId: string): Promise<any | null> {
  if (!consolidatedProductsCollection) return null;
  
  try {
    const objectId = new ObjectId(productId);
    const product = await consolidatedProductsCollection.findOne({ _id: objectId });
    
    if (!product) return null;
    
    // TODO: Add price history aggregation if needed
    
    return product;
  } catch (error: any) {
    logError(`Failed to get product with prices: ${error.message}`);
    return null;
  }
}