# Troubleshooting Guide

## Database Schema Issues

### Error: "column 'original_unit_qty' of relation 'products' does not exist"

**Problem**: The database schema is missing the `original_unit_qty` column that all scrapers expect to exist.

**Solution**: 
1. Run the schema check/fix command:
   ```bash
   node start-scrapers.js check-schema
   ```

2. Or manually apply the migration in your Supabase SQL editor:
   ```sql
   DO $$
   BEGIN
       IF NOT EXISTS (
           SELECT 1 FROM information_schema.columns 
           WHERE table_name = 'products' 
           AND column_name = 'original_unit_qty'
       ) THEN
           ALTER TABLE products ADD COLUMN original_unit_qty numeric(10,2);
           RAISE NOTICE 'Added original_unit_qty column to products table';
       ELSE
           RAISE NOTICE 'Column original_unit_qty already exists';
       END IF;
   END
   $$;
   ```

### Error: "null value in column 'store' violates not-null constraint"

**Problem**: The database has extra columns (like 'store') with NOT NULL constraints that don't match our expected schema. This happens when the database structure doesn't align with the migration files.

**Solution**:
1. Run the comprehensive schema fix:
   ```bash
   node start-scrapers.js check-schema
   ```

2. Or manually run the comprehensive fix in your Supabase SQL editor (see migration file `20250122_002_fix_products_schema.sql`)

3. **Alternative - Reset the products table** (⚠️ This will delete all data):
   ```sql
   -- Drop and recreate with correct schema
   DROP TABLE IF EXISTS products CASCADE;
   CREATE TABLE products (
     id                  text primary key,
     name                text         not null,
     size                text,
     brand_id            bigint       references brands(id),
     category_id         bigint       references categories(id),
     subcategory         text,
     unit_price          numeric(10,2),
     unit_name           text,
     original_unit_qty   numeric(10,2),
     source_site         text,
     last_updated        timestamptz  not null,
     last_checked        timestamptz  not null,
     created_at          timestamptz  default now()
   );
   
   CREATE INDEX ON products(category_id);
   CREATE INDEX ON products(brand_id);
   ```

### Error: "SUPABASE_URL or SUPABASE_ANON_KEY not set"

**Problem**: Environment variables are not configured.

**Solution**: Create a `.env` file in the root directory with:
```env
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

### Error: "POSTGRES_CONNECTION connection string not found"

**Problem**: C# scrapers can't find PostgreSQL connection string.

**Solution**: Update `appsettings.json` in the scraper directories:
```json
{
  "POSTGRES_CONNECTION": "Host=db.your-project.supabase.co;Port=5432;Database=postgres;Username=postgres;Password=your-password"
}
```

## .NET Scraper Issues (New World & PakNSave)

### Error: "The local source 'C:\Program Files (x86)\Microsoft SDKs\NuGetPackages\' doesn't exist"

**Problem**: NuGet is configured to use a non-existent local package source on Windows.

**Solution**: 
1. **Quick Fix - Run the automated script**:
   ```cmd
   # In Command Prompt
   fix-dotnet-issues.bat
   
   # Or in PowerShell
   .\fix-dotnet-issues.ps1
   ```

2. **Manual Fix**:
   ```cmd
   # Clear NuGet caches
   dotnet nuget locals all --clear
   
   # Remove problematic sources
   dotnet nuget remove source "C:\Program Files (x86)\Microsoft SDKs\NuGetPackages\"
   
   # Ensure nuget.org is available
   dotnet nuget add source https://api.nuget.org/v3/index.json -n nuget.org
   
   # Test restore
   cd paknsave\src
   dotnet restore
   ```

3. **Alternative - Use provided NuGet.Config**: 
   - NuGet.Config files have been added to both C# scrapers
   - These override global settings and force use of nuget.org only

### Error: "dotnet restore" hangs or times out

**Problem**: Network connectivity or firewall blocking NuGet.

**Solution**:
- Check Windows Firewall settings
- Temporarily disable antivirus
- Try running Command Prompt as Administrator
- Check corporate proxy settings

### Error: Build succeeds but runtime errors occur

**Problem**: Missing Playwright browsers or configuration issues.

**Solution**:
```cmd
# Install Playwright browsers
cd paknsave\src
pwsh bin\Debug\net6.0\playwright.ps1 install chromium

cd ..\..\new-world\src  
pwsh bin\Debug\net6.0\playwright.ps1 install chromium
```

## Scraper-Specific Issues

### Playwright Browser Issues

**Problem**: "Browser not found" or Playwright installation errors.

**Solution**:
```bash
# For Node.js (Woolworths)
cd Woolworths
npx playwright install

# For C# (New World/PakNSave)
cd new-world/src  # or paknsave/src
pwsh bin/Debug/net6.0/playwright.ps1 install chromium
```

### Rate Limiting / Getting Blocked

**Problem**: Target websites blocking requests due to too-fast scraping.

**Solution**: The scrapers have built-in rate limiting:
- Woolworths: 7 second delay
- New World: 11 second delay
- PakNSave: 11 second delay

If still blocked, increase delays in the scraper code or use different IP/proxy.

### Memory Issues

**Problem**: Scrapers consuming too much memory during large operations.

**Solution**: 
- Run scrapers individually instead of all at once
- Use `dev` mode for testing (no database writes)
- Monitor system resources and adjust concurrency

## Getting Help

1. Check console output for detailed error messages
2. Run in `dev` mode first to test without database writes
3. Ensure all dependencies are installed (`npm install`, `dotnet restore`)
4. Verify database connectivity and permissions
5. Check that URLs in `urls.txt` files are still valid

## Emergency Reset

If everything breaks and you need to start fresh:

1. **Reset database schema**:
   - Drop and recreate tables in Supabase
   - Re-run initial migration: `supabase/migrations/20240713_001_init.sql`
   - Apply fix migration: `supabase/migrations/20250122_001_add_missing_column.sql`

2. **Reset scrapers**:
   ```bash
   # Clean Node.js scraper
   cd Woolworths && rm -rf node_modules && npm install
   
   # Clean C# scrapers  
   cd ../new-world/src && dotnet clean && dotnet restore
   cd ../../paknsave/src && dotnet clean && dotnet restore
   ```

3. **Test in dev mode**:
   ```bash
   node start-scrapers.js dev
   ```