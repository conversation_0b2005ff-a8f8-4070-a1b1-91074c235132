---
alwaysApply: true
---
Please help me migrate my existing application from CosmosDB to PostgreSQL (using Supabase). I have 3 folders in my project:

paknsave/
woolworths/
new-world/

Each folder contains code that currently connects to and interacts with CosmosDB for storing product data, prices, and brand information for each respective supermarket.
What I need you to do:

Analyze the current CosmosDB implementation in all 3 folders
Remove all CosmosDB dependencies (connection strings, SDK imports, queries, etc.)
Replace with PostgreSQL/Supabase implementation with proper relational database design
Design proper database schema with tables for products, brands, stores, prices, and categories
Create SQL migration scripts to set up the new database structure
Provide data migration scripts to transfer existing data from CosmosDB to PostgreSQL
Update all code to use Supabase client and SQL queries instead of CosmosDB
Ensure consistent PostgreSQL setup across all 3 folders

Current setup details:

Using Node.js/JavaScript for backend scraping
React Native Expo for mobile app
Each folder handles product scraping and price updates for that specific supermarket
Data includes: product names, brands, prices, categories, subcategories, store information
Currently using CosmosDB SQL API
Using Algolia for search (keep this as-is)

PostgreSQL/Supabase requirements:

Use Supabase (PostgreSQL) as the database
Design proper relational schema with foreign keys
Maintain data integrity with proper constraints
Use Supabase client for all database operations
Include proper indexing for price comparison queries
Set up Row Level Security if needed
Enable real-time subscriptions for price updates

Database schema should include:

products table (id, name, category, subcategory)
brands table (id, name)
stores table (id, name, icon/logo)
prices table (product_id, brand_id, store_id, price, updated_at)
Proper foreign key relationships

Please provide:

Complete database schema (SQL DDL)
Updated code files for all 3 folders
Supabase connection setup and configuration
Data migration strategy from CosmosDB to PostgreSQL
SQL queries for common operations (price comparisons, product lookups)
Real-time subscription setup for price updates
Environment variable configuration
Installation/setup instructions

Migration priorities:

Preserve all existing product and price data
Maintain the same API functionality
Optimize for price comparison queries across stores
Set up for real-time price updates in the mobile app
Keep integration with existing Algolia search

Additional context: This is for a shopping list mobile app with price comparison across the 3 supermarkets. Users need to see the cheapest prices and compare brands across stores in real-time.