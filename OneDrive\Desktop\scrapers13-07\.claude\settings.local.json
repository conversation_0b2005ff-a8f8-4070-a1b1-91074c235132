{"permissions": {"allow": ["Bash(npm run dev:*)", "<PERSON><PERSON>(timeout 60 npm run db)", "Bash(rm:*)", "Bash(node:*)", "<PERSON><PERSON>(timeout 5s npm run dev)", "Bash(ls:*)", "Bash(npm install:*)", "Bash(find:*)", "<PERSON><PERSON>(timeout 60s npm run db:*)", "Bash(npm run db:*)", "<PERSON><PERSON>(timeout 45s npm run db)", "Bash(npx tsc:*)", "Bash(mongod:*)", "<PERSON><PERSON>(sudo apt:*)", "Bash(sudo apt install:*)", "<PERSON><PERSON>(docker:*)", "Bash(ss:*)", "Bash(sudo service:*)", "<PERSON><PERSON>(curl:*)", "Bash(tar:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(./bin/mongod:*)", "<PERSON><PERSON>(chmod:*)", "Bash(cmd.exe:*)", "Bash(timeout 30 npm run dev)", "Bash(dotnet run)", "Bash(timeout 30 npm run dev-tsx)", "<PERSON><PERSON>(timeout 60 node:*)", "<PERSON><PERSON>(timeout:*)"], "deny": []}}