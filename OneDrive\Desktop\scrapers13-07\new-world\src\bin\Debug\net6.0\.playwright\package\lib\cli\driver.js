"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.launchBrowserServer = launchBrowserServer;
exports.printApiJson = printApiJson;
exports.runDriver = runDriver;
exports.runServer = runServer;
var _fs = _interopRequireDefault(require("fs"));
var playwright = _interopRequireWildcard(require("../.."));
var _server = require("../server");
var _transport = require("../protocol/transport");
var _playwrightServer = require("../remote/playwrightServer");
var _processLauncher = require("../utils/processLauncher");
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
/**
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the 'License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable no-console */

function printApiJson() {
  // Note: this file is generated by build-playwright-driver.sh
  console.log(JSON.stringify(require('../../api.json')));
}
function runDriver() {
  const dispatcherConnection = new _server.DispatcherConnection();
  new _server.RootDispatcher(dispatcherConnection, async (rootScope, {
    sdkLanguage
  }) => {
    const playwright = (0, _server.createPlaywright)(sdkLanguage);
    return new _server.PlaywrightDispatcher(rootScope, playwright);
  });
  const transport = process.send ? new _transport.IpcTransport(process) : new _transport.PipeTransport(process.stdout, process.stdin);
  transport.onmessage = message => dispatcherConnection.dispatch(JSON.parse(message));
  dispatcherConnection.onmessage = message => transport.send(JSON.stringify(message));
  transport.onclose = () => {
    // Drop any messages during shutdown on the floor.
    dispatcherConnection.onmessage = () => {};
    selfDestruct();
  };
}
async function runServer(options) {
  const {
    port,
    path = '/',
    maxConnections = Infinity
  } = options;
  const server = new _playwrightServer.PlaywrightServer({
    path,
    maxConnections
  });
  const wsEndpoint = await server.listen(port);
  process.on('exit', () => server.close().catch(console.error));
  console.log('Listening on ' + wsEndpoint); // eslint-disable-line no-console
  process.stdin.on('close', () => selfDestruct());
}
async function launchBrowserServer(browserName, configFile) {
  let options = {};
  if (configFile) options = JSON.parse(_fs.default.readFileSync(configFile).toString());
  const browserType = playwright[browserName];
  const server = await browserType.launchServer(options);
  console.log(server.wsEndpoint());
}
function selfDestruct() {
  // Force exit after 30 seconds.
  setTimeout(() => process.exit(0), 30000);
  // Meanwhile, try to gracefully close all browsers.
  (0, _processLauncher.gracefullyCloseAll)().then(() => {
    process.exit(0);
  });
}