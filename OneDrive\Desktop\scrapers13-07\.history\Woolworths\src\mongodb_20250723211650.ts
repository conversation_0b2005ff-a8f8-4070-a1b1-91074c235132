import { MongoClient, Db, Collection, GridFSBucket, ObjectId } from "mongodb";
import * as dotenv from "dotenv";
dotenv.config();
dotenv.config({ path: `.env.local`, override: true });

import { Product, UpsertResponse } from "./typings.js";
import { colour, log, logError } from "./utilities.js";

let client: MongoClient;
let db: Db;
let gridFS: GridFSBucket;
let storeId: ObjectId | undefined; // Woolworths store ObjectId

// Collections
let storesCollection: Collection;
let brandsCollection: Collection;
let consolidatedProductsCollection: Collection;
let priceHistoryCollection: Collection;
let categoryHierarchyCollection: Collection;

export async function establishMongoDB() {
  const connectionString = process.env.MONGODB_CONNECTION_STRING;
  const databaseName = process.env.MONGODB_DATABASE_NAME || "nz-supermarket-scraper";
  
  if (!connectionString) {
    throw Error("MONGODB_CONNECTION_STRING not set in env");
  }

  try {
    client = new MongoClient(connectionString);
    await client.connect();
    db = client.db(databaseName);
    
    // Initialize collections
    storesCollection = db.collection("stores");
    brandsCollection = db.collection("brands");
    consolidatedProductsCollection = db.collection("consolidatedProducts");
    priceHistoryCollection = db.collection("priceHistory");
    categoryHierarchyCollection = db.collection("categoryHierarchy");
    
    // Initialize GridFS for image storage
    gridFS = new GridFSBucket(db, { bucketName: "productImages" });
    
    log(colour.green, "✅ MongoDB connection established");
    
    // Create indexes for performance
    await createIndexes();
    
  } catch (error: any) {
    logError(`Failed to connect to MongoDB: ${error.message}`);
    throw error;
  }
}

async function createIndexes() {
  try {
    // Consolidated products indexes
    await consolidatedProductsCollection.createIndex({ 
      "displayName": "text", 
      "normalizedName": "text", 
      "variants.storeName": "text" 
    });
    await consolidatedProductsCollection.createIndex({ "categoryId": 1 });
    await consolidatedProductsCollection.createIndex({ "brandId": 1 });
    await consolidatedProductsCollection.createIndex({ 
      "variants.storeProductId": 1, 
      "variants.storeId": 1 
    });
    
    // Price history indexes
    await priceHistoryCollection.createIndex({ 
      "consolidatedProductId": 1, 
      "recordedAt": -1 
    });
    await priceHistoryCollection.createIndex({ "year": 1, "month": 1 });
    
    // Category hierarchy indexes
    await categoryHierarchyCollection.createIndex({ "parentId": 1, "sortOrder": 1 });
    await categoryHierarchyCollection.createIndex({ "level": 1, "sortOrder": 1 });
    
    log(colour.blue, "✅ MongoDB indexes created");
  } catch (error: any) {
    logError(`Failed to create indexes: ${error.message}`);
  }
}

async function ensureStoreRow(): Promise<ObjectId> {
  if (storeId !== undefined) return storeId;
  
  try {
    const existingStore = await storesCollection.findOne({ storeId: "woolworths" });
    
    if (existingStore) {
      storeId = existingStore._id;
      return storeId;
    }

    // Insert store if not exists
    const insertResult = await storesCollection.insertOne({
      storeId: "woolworths",
      name: "Woolworths",
      logoUrl: null,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    storeId = insertResult.insertedId;
    return storeId;
    
  } catch (error: any) {
    logError(`Failed to ensure store row: ${error.message}`);
    throw error;
  }
}

export async function upsertProductToMongoDB(scraped: Product): Promise<UpsertResponse> {
  if (!db) throw Error("MongoDB client not initialised");
  
  try {
    const sId = await ensureStoreRow();
    const now = new Date();
    
    // Find or create consolidated product
    let consolidatedProduct = await consolidatedProductsCollection.findOne({
      "variants.storeProductId": scraped.id,
      "variants.storeId": sId
    });
    
    if (!consolidatedProduct) {
      // Try to find matching consolidated product using enhanced algorithm
      const normalizedName = normalizeProductName(scraped.name, scraped.size);
      log(colour.cyan, `🔍 Processing: ${scraped.name} -> normalized: ${normalizedName}`);

      const matchingProduct = await findBestMatch(normalizedName, scraped.size, scraped.name);

      if (matchingProduct) {
        // Found a match - add this as a new variant and add alias
        consolidatedProduct = matchingProduct;

        // Add current product name as alias if different
        await addProductAlias(consolidatedProduct._id, scraped.name);

        // Add new variant to existing consolidated product
        const newVariant = {
          storeProductId: scraped.id,
          storeId: sId,
          storeName: scraped.name,
          storeSize: scraped.size,
          storeUnitPrice: scraped.unitPrice,
          storeUnitName: scraped.unitName,
          lastSeen: now,
          isActive: true,
          imageUrl: null
        };

        await consolidatedProductsCollection.updateOne(
          { _id: consolidatedProduct._id },
          {
            $push: { variants: newVariant },
            $set: { updatedAt: now }
          }
        );

        log(colour.green, `✅ Added variant to existing product: ${matchingProduct.displayName}`);
      } else {
        // Create new consolidated product
        const newProduct = {
          normalizedName,
          displayName: scraped.name,
          primarySize: scraped.size,
          categoryId: null, // TODO: Implement category mapping
          brandId: null, // TODO: Implement brand extraction
          matchConfidence: 100,
          manualMatch: false,
          aliases: [], // Initialize empty aliases array
          variants: [{
            storeProductId: scraped.id,
            storeId: sId,
            storeName: scraped.name,
            storeSize: scraped.size,
            storeUnitPrice: scraped.unitPrice,
            storeUnitName: scraped.unitName,
            lastSeen: now,
            isActive: true,
            imageUrl: null
          }],
          sizeVariants: scraped.size ? [{
            sizeName: scraped.size,
            sizeWeightGrams: null,
            sizeVolumeMl: null,
            isPrimarySize: true
          }] : [],
          currentPrices: [{
            storeId: sId,
            price: scraped.currentPrice,
            isSpecial: false,
            wasAvailable: true,
            recordedAt: now
          }],
          createdAt: now,
          updatedAt: now
        };

        const insertResult = await consolidatedProductsCollection.insertOne(newProduct);
        consolidatedProduct = { _id: insertResult.insertedId, ...newProduct };

        log(colour.blue, `🆕 Created new consolidated product: ${scraped.name}`);
      }
    } else {
      // Update existing consolidated product
      await consolidatedProductsCollection.updateOne(
        { _id: consolidatedProduct._id },
        {
          $set: {
            "variants.$[variant].lastSeen": now,
            "variants.$[variant].storeUnitPrice": scraped.unitPrice,
            "variants.$[variant].storeUnitName": scraped.unitName,
            "currentPrices.$[price].price": scraped.currentPrice,
            "currentPrices.$[price].recordedAt": now,
            updatedAt: now
          }
        },
        {
          arrayFilters: [
            { "variant.storeProductId": scraped.id },
            { "price.storeId": sId }
          ]
        }
      );
    }
    
    // Insert price history record
    await priceHistoryCollection.insertOne({
      consolidatedProductId: consolidatedProduct._id,
      storeId: sId,
      price: scraped.currentPrice,
      isSpecial: false,
      wasAvailable: true,
      recordedAt: now,
      year: now.getFullYear(),
      month: now.getMonth() + 1
    });
    
    log(colour.green, `  Upserted: ${scraped.name.slice(0, 45).padEnd(45)} | $${scraped.currentPrice}`);
    return UpsertResponse.PriceChanged;
    
  } catch (error: any) {
    logError(`MongoDB upsert failed: ${error.message}`);
    return UpsertResponse.Failed;
  }
}

// Upload product image to MongoDB GridFS
export async function uploadImageToMongoDB(imageUrl: string, product: Product): Promise<boolean> {
  if (!gridFS) throw Error("MongoDB GridFS not initialised");

  try {
    const sId = await ensureStoreRow();
    
    // Download image from Woolworths
    const imageResponse = await fetch(imageUrl);
    if (!imageResponse.ok) {
      logError(`Failed to download image from ${imageUrl}: ${imageResponse.statusText}`);
      return false;
    }

    const imageBuffer = Buffer.from(await imageResponse.arrayBuffer());
    
    // Check if image already exists and delete it
    const existingFiles = await gridFS.find({ 
      "metadata.productId": product.id 
    }).toArray();
    
    for (const file of existingFiles) {
      await gridFS.delete(file._id);
    }
    
    // Create upload stream
    const filename = `${product.id}.jpg`;
    const uploadStream = gridFS.openUploadStream(filename, {
      contentType: 'image/jpeg',
      metadata: {
        productId: product.id,
        storeId: sId,
        originalUrl: imageUrl,
        uploadedAt: new Date()
      }
    });
    
    // Upload the image
    await new Promise((resolve, reject) => {
      uploadStream.end(imageBuffer, (error) => {
        if (error) reject(error);
        else resolve(uploadStream.id);
      });
    });
    
    // Update product with image URL reference
    const imageUrl_gridfs = `gridfs://productImages/${uploadStream.id}`;
    
    await consolidatedProductsCollection.updateOne(
      { "variants.storeProductId": product.id },
      {
        $set: {
          "variants.$[variant].imageUrl": imageUrl_gridfs
        }
      },
      {
        arrayFilters: [{ "variant.storeProductId": product.id }]
      }
    );
    
    log(colour.blue, `  Image uploaded: ${product.id} -> GridFS ${uploadStream.id}`);
    return true;

  } catch (err: any) {
    logError(`Image upload error: ${err.message}`);
    return false;
  }
}

// Enhanced product name normalization with manual mappings
function normalizeProductName(name: string, size?: string): string {
  if (!name) return '';

  let normalized = name.toLowerCase()
    .replace(/'/g, '')
    .replace(/"/g, '')
    .replace(/-/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();

  // Remove common store descriptors
  const descriptors = ['woolworths', 'countdown', 'new world', 'paknsave', 'pak n save',
                      'select', 'premium', 'value', 'budget', 'signature', 'essentials',
                      'pams', 'homebrand', 'signature range', 'fresh choice'];

  for (const descriptor of descriptors) {
    normalized = normalized.replace(new RegExp(`\\b${descriptor}\\b`, 'g'), '').trim();
  }

  // Apply comprehensive NZ brand mappings
  const nzBrandMappings = getNZBrandMappings();

  // Apply brand mappings
  for (const [canonical, variations] of Object.entries(nzBrandMappings)) {
    if (normalized.includes(canonical)) {
      normalized = normalized.replace(canonical, canonical);
      break; // Use first match to avoid multiple replacements
    }

    // Check variations
    for (const variation of variations) {
      if (normalized.includes(variation)) {
        normalized = normalized.replace(variation, canonical);
        break;
      }
    }
  }

  // Include size if provided
  if (size) {
    const normalizedSize = size.toLowerCase()
      .replace(/grams?/g, 'g')
      .replace(/kilograms?/g, 'kg')
      .replace(/litres?/g, 'l')
      .replace(/millilitres?/g, 'ml');
    normalized += ` ${normalizedSize}`;
  }

  return normalized.trim();
}

// Get comprehensive NZ brand mappings (152 brands with 500+ variations)
function getNZBrandMappings(): Record<string, string[]> {
  return {
    // === SUPERMARKET PRIVATE LABELS ===
    'pams': ['pam', 'pams brand', 'pams select'],
    'essentials': ['essentials brand', 'countdown essentials'],
    'macro': ['macro brand', 'macro organic'],
    'woolworths': ['woolworths brand', 'woolworths select'],
    'countdown': ['countdown brand', 'countdown select'],
    'homebrand': ['home brand', 'homebrand select'],
    'signature': ['signature range', 'signature brand'],
    'value': ['value brand', 'budget'],
    'fresh choice': ['freshchoice', 'fresh choice brand'],

    // === DAIRY BRANDS ===
    'anchor': ['anchor brand', 'anchor dairy', 'anchor milk', 'anchor butter', 'anchor cheese'],
    'mainland': ['mainland cheese', 'mainland dairy', 'mainland brand'],
    'meadowfresh': ['meadow fresh', 'meadowfresh milk', 'meadow fresh milk'],
    'lewis road': ['lewis road creamery', 'lewis road milk', 'lewis road butter'],
    'kapiti': ['kapiti cheese', 'kapiti ice cream', 'kapiti brand'],
    'fernleaf': ['fernleaf milk', 'fernleaf powder'],
    'tararua': ['tararua cheese', 'tararua dairy'],
    'rolling meadow': ['rolling meadow cheese', 'rolling meadow dairy'],
    'whitestone': ['whitestone cheese', 'whitestone dairy'],
    'mercer': ['mercer cheese', 'mercer dairy'],
    'epicure': ['epicure cheese', 'epicure dairy'],
    'kikorangi': ['kikorangi cheese', 'kikorangi blue'],

    // === MEAT BRANDS ===
    'tegel': ['tegel chicken', 'tegel poultry', 'tegel brand'],
    'inghams': ['ingham', 'inghams chicken', 'inghams poultry'],
    'turks': ['turks poultry', 'turks chicken'],
    'brinks': ['brinks chicken', 'brinks poultry'],
    'hellers': ['heller', 'hellers bacon', 'hellers sausages', 'hellers smallgoods'],
    'beehive': ['beehive bacon', 'beehive ham', 'beehive smallgoods'],
    'farmland': ['farmland bacon', 'farmland ham'],
    'primo': ['primo smallgoods', 'primo bacon'],
    'hans': ['hans smallgoods', 'hans continental'],
    'continental': ['continental smallgoods', 'continental deli'],

    // === BREAD & BAKERY BRANDS ===
    'tip top': ['tiptop', 'tip top bread', 'tiptop bread'],
    'molenberg': ['molenberg bread', 'molenburg', 'molenberg wholemeal'],
    'vogels': ['vogel', 'vogels bread', 'vogel bread', 'vogels original'],
    'freyas': ['freya', 'freyas bread', 'freya bread'],
    'natures fresh': ['nature fresh', 'natures fresh bread'],
    'burgen': ['burgen bread', 'burgen soy lin'],
    'ploughmans': ['ploughman', 'ploughmans bread'],
    'golden': ['golden bread', 'golden bakery'],
    'bakers delight': ['bakersdelight', 'bakers delight bread'],

    // === BEVERAGE BRANDS ===
    'coke': ['coca cola', 'coca-cola', 'coke classic', 'coca cola classic'],
    'coke zero': ['coca cola zero', 'coke zero sugar', 'coca cola zero sugar'],
    'diet coke': ['coca cola diet', 'diet coca cola'],
    'pepsi': ['pepsi cola', 'pepsi classic', 'pepsi original'],
    'pepsi max': ['pepsi maximum taste', 'pepsi max no sugar'],
    'fanta': ['fanta orange', 'fanta grape'],
    'sprite': ['sprite lemon', 'sprite lime'],
    'l&p': ['lemon paeroa', 'lemon and paeroa', 'l and p'],
    'just juice': ['justjuice', 'just juice brand'],
    'fresh up': ['freshup', 'fresh up juice'],
    'keri': ['keri juice', 'keri fresh'],
    'charlies': ['charlie', 'charlies juice', 'charlies honest'],
    'phoenix': ['phoenix organic', 'phoenix juice'],
    'pump': ['pump water', 'pump brand'],
    'powerade': ['powerade sports drink'],
    'gatorade': ['gatorade sports drink'],

    // === CEREAL & BREAKFAST BRANDS ===
    'sanitarium': ['sanitarium weetbix', 'sanitarium so good'],
    'weetbix': ['weet bix', 'wheat biscuits', 'sanitarium weetbix'],
    'uncle tobys': ['uncle toby', 'uncle tobys oats', 'uncle tobys muesli'],
    'kelloggs': ['kellogg', 'kelloggs cornflakes', 'kelloggs special k'],
    'cornflakes': ['corn flakes', 'kelloggs cornflakes'],
    'nutrigrain': ['nutri grain', 'kelloggs nutrigrain'],
    'special k': ['specialk', 'kelloggs special k'],
    'hubbards': ['hubbards cereal', 'hubbards muesli'],

    // === SNACK & CONFECTIONERY BRANDS ===
    'cadbury': ['cadburys', 'cadbury chocolate'],
    'whittakers': ['whittaker', 'whittakers chocolate'],
    'nestle': ['nestlé', 'nestle chocolate'],
    'mars': ['mars bar', 'mars chocolate'],
    'snickers': ['snickers bar', 'snickers chocolate'],
    'kit kat': ['kitkat', 'kit-kat'],
    'twix': ['twix bar', 'twix chocolate'],
    'moro': ['moro bar', 'moro chocolate'],
    'picnic': ['picnic bar', 'picnic chocolate'],
    'crunchie': ['crunchie bar', 'crunchie chocolate'],
    'bluebird': ['bluebird chips', 'bluebird snacks'],
    'eta': ['eta chips', 'eta snacks', 'eta peanut butter'],
    'proper': ['proper chips', 'proper crisps'],
    'heartland': ['heartland chips', 'heartland snacks'],
    'griffins': ['griffin', 'griffins biscuits', 'griffin biscuits'],
    'arnott': ['arnotts', 'arnott biscuits', 'arnotts biscuits'],
    'toffee pops': ['toffeepops', 'griffins toffee pops'],
    'mallowpuffs': ['mallow puffs', 'griffins mallowpuffs'],
    'pics': ['pic', 'pic peanut butter', 'pics peanut butter'],
    'cottees': ['cottee', 'cottees jam', 'cottee jam'],

    // === PANTRY & COOKING BRANDS ===
    'watties': ['wattie', 'watties tomatoes', 'watties sauce'],
    'heinz': ['heinz beans', 'heinz tomato sauce', 'heinz soup'],
    'maggi': ['maggi noodles', 'maggi soup', 'maggi instant'],
    'continental': ['continental soup', 'continental pasta'],
    'mccains': ['mccain', 'mccains chips', 'mccains frozen'],
    'edgell': ['edgell vegetables', 'edgell canned'],
    'greggs': ['greggs coffee', 'greggs instant'],
    'nescafe': ['nescafé', 'nescafe coffee'],
    'moccona': ['moccona coffee', 'moccona instant'],
    'robert harris': ['robertharris', 'robert harris coffee'],
    'bell tea': ['bell tea bags', 'bell black tea'],
    'dilmah': ['dilmah tea', 'dilmah ceylon'],
    'olivani': ['olivani oil', 'olivani olive oil'],
    'bertolli': ['bertolli oil', 'bertolli olive oil'],
    'praise': ['praise mayonnaise', 'praise dressing'],
    'best foods': ['bestfoods', 'best foods mayo'],
    'masterfoods': ['master foods', 'masterfoods sauce'],
    'fountain': ['fountain sauce', 'fountain tomato sauce'],

    // === FROZEN FOOD BRANDS ===
    'birds eye': ['birdseye', 'birds eye vegetables', 'birds eye fish'],
    'talley': ['talleys', 'talley vegetables', 'talley frozen'],

    // === CLEANING & HOUSEHOLD BRANDS ===
    'janola': ['janola bleach', 'janola cleaning'],
    'earthwise': ['earthwise cleaning', 'earthwise eco'],
    'finish': ['finish dishwasher', 'finish tablets'],
    'ajax': ['ajax spray', 'ajax cleaning'],
    'jif': ['jif cream cleanser', 'jif bathroom'],
    'domestos': ['domestos bleach', 'domestos toilet'],
    'toilet duck': ['toiletduck', 'toilet duck cleaner'],
    'mr muscle': ['mrmuscle', 'mr muscle bathroom'],
    'windex': ['windex glass', 'windex cleaner'],
    'persil': ['persil washing powder', 'persil liquid'],
    'surf': ['surf washing powder', 'surf liquid'],
    'omo': ['omo washing powder', 'omo liquid'],
    'cold power': ['coldpower', 'cold power liquid'],
    'dynamo': ['dynamo washing liquid'],
    'sorbent': ['sorbent toilet paper', 'sorbent tissues'],
    'kleenex': ['kleenex tissues', 'kleenex toilet paper'],
    'quilton': ['quilton toilet paper', 'quilton tissues'],
    'treasures': ['treasures toilet paper', 'treasures tissues'],

    // === HEALTH & BEAUTY BRANDS ===
    'colgate': ['colgate toothpaste', 'colgate toothbrush'],
    'oral b': ['oral-b', 'oralb', 'oral b toothbrush'],
    'sensodyne': ['sensodyne toothpaste'],
    'macleans': ['macleans toothpaste'],
    'head shoulders': ['head and shoulders', 'head & shoulders'],
    'pantene': ['pantene shampoo', 'pantene conditioner'],
    'herbal essences': ['herbal essence', 'herbal essences shampoo'],
    'dove': ['dove soap', 'dove body wash'],
    'nivea': ['nivea cream', 'nivea body'],
    'vaseline': ['vaseline petroleum jelly'],

    // === BABY & PERSONAL CARE ===
    'huggies': ['huggies nappies', 'huggies diapers'],
    'pampers': ['pampers nappies', 'pampers diapers'],
    'johnson': ['johnsons', 'johnson baby', 'johnsons baby'],
    'bepanthen': ['bepanthen cream', 'bepanthen nappy'],

    // === PET FOOD BRANDS ===
    'pedigree': ['pedigree dog food', 'pedigree dry'],
    'whiskas': ['whiskas cat food', 'whiskas wet'],
    'fancy feast': ['fancyfeast', 'fancy feast cat'],
    'royal canin': ['royalcanin', 'royal canin dog'],
    'hills': ['hills pet food', 'hills science diet'],
    'eukanuba': ['eukanuba dog food'],
    'iams': ['iams pet food', 'iams dog'],
    'optimum': ['optimum dog food', 'optimum pet'],
    'tux': ['tux cat food', 'tux pet'],
    'champ': ['champ dog food', 'champ pet'],

    // === ADDITIONAL NZ SPECIFIC BRANDS ===
    'tip top ice cream': ['tiptop ice cream', 'tip top icecream'],
    'new zealand natural': ['nz natural', 'new zealand natural ice cream'],
    'deep south': ['deep south ice cream', 'deep south icecream'],
    'barkers': ['barkers jam', 'barkers preserves'],
    'san remo': ['sanremo', 'san remo pasta'],
    'latina': ['latina pasta', 'latina fresh'],
    'uncle bens': ['uncle ben', 'uncle bens rice'],
    'sunrice': ['sun rice', 'sunrice brand'],
    'campbells': ['campbell', 'campbells soup', 'campbell soup'],
    'delmaine': ['delmaine vegetables', 'delmaine canned'],
    'john west': ['johnwest', 'john west tuna', 'johnwest tuna'],
    'sirena': ['sirena tuna', 'sirena seafood'],
    'edmonds': ['edmonds flour', 'edmonds baking'],
    'champion': ['champion flour', 'champion baking'],
    'chelsea': ['chelsea sugar', 'chelsea baking']
  };
}

// Calculate Levenshtein distance for string similarity
function levenshteinDistance(s1: string, s2: string): number {
  if (!s1) return s2 ? s2.length : 0;
  if (!s2) return s1.length;

  const matrix: number[][] = [];

  for (let i = 0; i <= s2.length; i++) {
    matrix[i] = [i];
  }

  for (let j = 0; j <= s1.length; j++) {
    matrix[0][j] = j;
  }

  for (let i = 1; i <= s2.length; i++) {
    for (let j = 1; j <= s1.length; j++) {
      const cost = s1[j - 1] === s2[i - 1] ? 0 : 1;
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,
        matrix[i][j - 1] + 1,
        matrix[i - 1][j - 1] + cost
      );
    }
  }

  return matrix[s2.length][s1.length];
}

// Calculate similarity score between two product names
function calculateSimilarity(name1: string, name2: string): number {
  if (!name1 || !name2) return 0;
  if (name1 === name2) return 1.0;

  const distance = levenshteinDistance(name1, name2);
  const maxLength = Math.max(name1.length, name2.length);

  return maxLength === 0 ? 1.0 : 1.0 - distance / maxLength;
}

// Find best matching consolidated product using enhanced algorithm
async function findBestMatch(normalizedName: string, size: string | undefined, originalName: string): Promise<any> {
  try {
    // First try exact match
    const exactMatch = await consolidatedProductsCollection.findOne({
      normalizedName: normalizedName
    });

    if (exactMatch) {
      log(colour.green, `✅ Exact match found: ${exactMatch.displayName}`);
      return exactMatch;
    }

    // Try fuzzy matching
    const allProducts = await consolidatedProductsCollection.find({}).limit(1000).toArray();

    let bestMatch: any = null;
    let bestScore = 0;
    const threshold = 0.8;

    for (const product of allProducts) {
      let score = calculateSimilarity(normalizedName, product.normalizedName);

      // Check aliases if they exist
      if (product.aliases && Array.isArray(product.aliases)) {
        for (const alias of product.aliases) {
          const aliasScore = calculateSimilarity(normalizedName, alias);
          score = Math.max(score, aliasScore);
        }
      }

      if (score > bestScore && score >= threshold) {
        bestScore = score;
        bestMatch = product;
      }
    }

    if (bestMatch) {
      log(colour.yellow, `✅ Fuzzy match found: ${bestMatch.displayName} (confidence: ${bestScore.toFixed(3)})`);
      return bestMatch;
    }

    log(colour.red, `❌ No match found for: ${originalName}`);
    return null;
  } catch (error: any) {
    logError(`❌ Error finding match: ${error.message}`);
    return null;
  }
}

// Add alias to consolidated product
async function addProductAlias(consolidatedProductId: any, newAlias: string): Promise<void> {
  try {
    const normalizedAlias = normalizeProductName(newAlias);
    if (!normalizedAlias) return;

    const product = await consolidatedProductsCollection.findOne({ _id: consolidatedProductId });
    if (!product) return;

    // Get current aliases or initialize empty array
    const currentAliases = product.aliases || [];

    // Don't add if alias already exists or is the same as normalized name
    if (normalizedAlias === product.normalizedName || currentAliases.includes(normalizedAlias)) return;

    // Add new alias
    currentAliases.push(normalizedAlias);

    await consolidatedProductsCollection.updateOne(
      { _id: consolidatedProductId },
      {
        $set: {
          aliases: currentAliases,
          updatedAt: new Date()
        }
      }
    );

    log(colour.cyan, `📝 Added alias '${normalizedAlias}' to product ${consolidatedProductId}`);
  } catch (error: any) {
    logError(`❌ Error adding alias: ${error.message}`);
  }
}

// Get image stream from GridFS
export async function getImageFromGridFS(fileId: string): Promise<NodeJS.ReadableStream | null> {
  if (!gridFS) return null;

  try {
    const objectId = new ObjectId(fileId);
    return gridFS.openDownloadStream(objectId);
  } catch (error: any) {
    logError(`Failed to get image from GridFS: ${error.message}`);
    return null;
  }
}

// Close MongoDB connection
export async function closeMongoDB() {
  if (client) {
    await client.close();
    log(colour.blue, "MongoDB connection closed");
  }
}

// Health check
export async function mongoHealthCheck(): Promise<boolean> {
  try {
    await db.admin().ping();
    return true;
  } catch (error) {
    return false;
  }
}