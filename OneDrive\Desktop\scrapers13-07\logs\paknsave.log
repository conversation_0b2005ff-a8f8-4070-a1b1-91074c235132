C:\Users\<USER>\OneDrive\Desktop\scrapers13-07\paknsave\src\PakScraper.csproj : warning NU1603: PakScraper depends on <PERSON><PERSON> (>= 2.1.45) but Dapper 2.1.45 was not found. Dapper 2.1.66 was resolved instead.
C:\Users\<USER>\OneDrive\Desktop\scrapers13-07\paknsave\src\PakScraper.csproj : warning NU1603: PakScraper depends on Npgsql (>= 7.0.5) but Npgsql 7.0.5 was not found. Npgsql 7.0.6 was resolved instead.
C:\Users\<USER>\OneDrive\Desktop\scrapers13-07\paknsave\src\PakScraper.csproj : warning NU1903: Package 'Npgsql' 7.0.6 has a known high severity vulnerability, https://github.com/advisories/GHSA-x9vc-6hfv-hg8c
C:\Users\<USER>\.nuget\packages\microsoft.bcl.asyncinterfaces\9.0.1\buildTransitive\netcoreapp2.0\Microsoft.Bcl.AsyncInterfaces.targets(4,5): warning : Microsoft.Bcl.AsyncInterfaces 9.0.1 doesn't support net6.0 and has not been tested with it. Consider upgrading your TargetFramework to net8.0 or later. You may also set <SuppressTfmSupportBuildWarnings>true</SuppressTfmSupportBuildWarnings> in the project file to ignore this warning and attempt to run in this unsupported configuration at your own risk. [C:\Users\<USER>\OneDrive\Desktop\scrapers13-07\paknsave\src\PakScraper.csproj]
C:\Users\<USER>\OneDrive\Desktop\scrapers13-07\paknsave\src\PakScraper.csproj : warning NU1603: PakScraper depends on Dapper (>= 2.1.45) but Dapper 2.1.45 was not found. Dapper 2.1.66 was resolved instead.
C:\Users\<USER>\OneDrive\Desktop\scrapers13-07\paknsave\src\PakScraper.csproj : warning NU1603: PakScraper depends on Npgsql (>= 7.0.5) but Npgsql 7.0.5 was not found. Npgsql 7.0.6 was resolved instead.
C:\Users\<USER>\OneDrive\Desktop\scrapers13-07\paknsave\src\PakScraper.csproj : warning NU1903: Package 'Npgsql' 7.0.6 has a known high severity vulnerability, https://github.com/advisories/GHSA-x9vc-6hfv-hg8c
__________________________________________________
Project "C:\Users\<USER>\OneDrive\Desktop\scrapers13-07\paknsave\src\PakScraper.csproj" (ComputeRunArguments target(s)):

C:\Users\<USER>\.nuget\packages\microsoft.bcl.asyncinterfaces\9.0.1\buildTransitive\netcoreapp2.0\Microsoft.Bcl.AsyncInterfaces.targets(4,5): warning : Microsoft.Bcl.AsyncInterfaces 9.0.1 doesn't support net6.0 and has not been tested with it. Consider upgrading your TargetFramework to net8.0 or later. You may also set <SuppressTfmSupportBuildWarnings>true</SuppressTfmSupportBuildWarnings> in the project file to ignore this warning and attempt to run in this unsupported configuration at your own risk.
Done building project "PakScraper.csproj".
