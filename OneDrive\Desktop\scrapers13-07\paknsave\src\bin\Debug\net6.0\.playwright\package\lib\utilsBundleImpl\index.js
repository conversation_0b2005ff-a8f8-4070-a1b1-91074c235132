"use strict";var hp=Object.create;var $r=Object.defineProperty;var pp=Object.getOwnPropertyDescriptor;var dp=Object.getOwnPropertyNames;var mp=Object.getPrototypeOf,gp=Object.prototype.hasOwnProperty;var E=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),vp=(t,e)=>{for(var i in e)$r(t,i,{get:e[i],enumerable:!0})},sa=(t,e,i,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of dp(e))!gp.call(t,n)&&n!==i&&$r(t,n,{get:()=>e[n],enumerable:!(r=pp(e,n))||r.enumerable});return t};var De=(t,e,i)=>(i=t!=null?hp(mp(t)):{},sa(e||!t||!t.__esModule?$r(i,"default",{value:t,enumerable:!0}):i,t)),_p=t=>sa($r({},"__esModule",{value:!0}),t);var ca=E((G_,la)=>{var aa={};la.exports=aa;var oa={reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],inverse:[7,27],hidden:[8,28],strikethrough:[9,29],black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],gray:[90,39],grey:[90,39],brightRed:[91,39],brightGreen:[92,39],brightYellow:[93,39],brightBlue:[94,39],brightMagenta:[95,39],brightCyan:[96,39],brightWhite:[97,39],bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgGray:[100,49],bgGrey:[100,49],bgBrightRed:[101,49],bgBrightGreen:[102,49],bgBrightYellow:[103,49],bgBrightBlue:[104,49],bgBrightMagenta:[105,49],bgBrightCyan:[106,49],bgBrightWhite:[107,49],blackBG:[40,49],redBG:[41,49],greenBG:[42,49],yellowBG:[43,49],blueBG:[44,49],magentaBG:[45,49],cyanBG:[46,49],whiteBG:[47,49]};Object.keys(oa).forEach(function(t){var e=oa[t],i=aa[t]=[];i.open="\x1B["+e[0]+"m",i.close="\x1B["+e[1]+"m"})});var fa=E((z_,ua)=>{"use strict";ua.exports=function(t,e){e=e||process.argv;var i=e.indexOf("--"),r=/^-{1,2}/.test(t)?"":"--",n=e.indexOf(r+t);return n!==-1&&(i===-1?!0:n<i)}});var pa=E((W_,ha)=>{"use strict";var xp=require("os"),kt=fa(),tt=process.env,Ni=void 0;kt("no-color")||kt("no-colors")||kt("color=false")?Ni=!1:(kt("color")||kt("colors")||kt("color=true")||kt("color=always"))&&(Ni=!0);"FORCE_COLOR"in tt&&(Ni=tt.FORCE_COLOR.length===0||parseInt(tt.FORCE_COLOR,10)!==0);function yp(t){return t===0?!1:{level:t,hasBasic:!0,has256:t>=2,has16m:t>=3}}function bp(t){if(Ni===!1)return 0;if(kt("color=16m")||kt("color=full")||kt("color=truecolor"))return 3;if(kt("color=256"))return 2;if(t&&!t.isTTY&&Ni!==!0)return 0;var e=Ni?1:0;if(process.platform==="win32"){var i=xp.release().split(".");return Number(process.versions.node.split(".")[0])>=8&&Number(i[0])>=10&&Number(i[2])>=10586?Number(i[2])>=14931?3:2:1}if("CI"in tt)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI"].some(function(n){return n in tt})||tt.CI_NAME==="codeship"?1:e;if("TEAMCITY_VERSION"in tt)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(tt.TEAMCITY_VERSION)?1:0;if("TERM_PROGRAM"in tt){var r=parseInt((tt.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(tt.TERM_PROGRAM){case"iTerm.app":return r>=3?3:2;case"Hyper":return 3;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(tt.TERM)?2:/^screen|^xterm|^vt100|^rxvt|color|ansi|cygwin|linux/i.test(tt.TERM)||"COLORTERM"in tt?1:(tt.TERM==="dumb",e)}function Gn(t){var e=bp(t);return yp(e)}ha.exports={supportsColor:Gn,stdout:Gn(process.stdout),stderr:Gn(process.stderr)}});var ma=E((Y_,da)=>{da.exports=function(e,i){var r="";e=e||"Run the trap, drop the bass",e=e.split("");var n={a:["@","\u0104","\u023A","\u0245","\u0394","\u039B","\u0414"],b:["\xDF","\u0181","\u0243","\u026E","\u03B2","\u0E3F"],c:["\xA9","\u023B","\u03FE"],d:["\xD0","\u018A","\u0500","\u0501","\u0502","\u0503"],e:["\xCB","\u0115","\u018E","\u0258","\u03A3","\u03BE","\u04BC","\u0A6C"],f:["\u04FA"],g:["\u0262"],h:["\u0126","\u0195","\u04A2","\u04BA","\u04C7","\u050A"],i:["\u0F0F"],j:["\u0134"],k:["\u0138","\u04A0","\u04C3","\u051E"],l:["\u0139"],m:["\u028D","\u04CD","\u04CE","\u0520","\u0521","\u0D69"],n:["\xD1","\u014B","\u019D","\u0376","\u03A0","\u048A"],o:["\xD8","\xF5","\xF8","\u01FE","\u0298","\u047A","\u05DD","\u06DD","\u0E4F"],p:["\u01F7","\u048E"],q:["\u09CD"],r:["\xAE","\u01A6","\u0210","\u024C","\u0280","\u042F"],s:["\xA7","\u03DE","\u03DF","\u03E8"],t:["\u0141","\u0166","\u0373"],u:["\u01B1","\u054D"],v:["\u05D8"],w:["\u0428","\u0460","\u047C","\u0D70"],x:["\u04B2","\u04FE","\u04FC","\u04FD"],y:["\xA5","\u04B0","\u04CB"],z:["\u01B5","\u0240"]};return e.forEach(function(s){s=s.toLowerCase();var o=n[s]||[" "],c=Math.floor(Math.random()*o.length);typeof n[s]!="undefined"?r+=n[s][c]:r+=s}),r}});var va=E((K_,ga)=>{ga.exports=function(e,i){e=e||"   he is here   ";var r={up:["\u030D","\u030E","\u0304","\u0305","\u033F","\u0311","\u0306","\u0310","\u0352","\u0357","\u0351","\u0307","\u0308","\u030A","\u0342","\u0313","\u0308","\u034A","\u034B","\u034C","\u0303","\u0302","\u030C","\u0350","\u0300","\u0301","\u030B","\u030F","\u0312","\u0313","\u0314","\u033D","\u0309","\u0363","\u0364","\u0365","\u0366","\u0367","\u0368","\u0369","\u036A","\u036B","\u036C","\u036D","\u036E","\u036F","\u033E","\u035B","\u0346","\u031A"],down:["\u0316","\u0317","\u0318","\u0319","\u031C","\u031D","\u031E","\u031F","\u0320","\u0324","\u0325","\u0326","\u0329","\u032A","\u032B","\u032C","\u032D","\u032E","\u032F","\u0330","\u0331","\u0332","\u0333","\u0339","\u033A","\u033B","\u033C","\u0345","\u0347","\u0348","\u0349","\u034D","\u034E","\u0353","\u0354","\u0355","\u0356","\u0359","\u035A","\u0323"],mid:["\u0315","\u031B","\u0300","\u0301","\u0358","\u0321","\u0322","\u0327","\u0328","\u0334","\u0335","\u0336","\u035C","\u035D","\u035E","\u035F","\u0360","\u0362","\u0338","\u0337","\u0361"," \u0489"]},n=[].concat(r.up,r.down,r.mid);function s(u){var h=Math.floor(Math.random()*u);return h}function o(u){var h=!1;return n.filter(function(l){h=l===u}),h}function c(u,h){var l="",d,m;h=h||{},h.up=typeof h.up!="undefined"?h.up:!0,h.mid=typeof h.mid!="undefined"?h.mid:!0,h.down=typeof h.down!="undefined"?h.down:!0,h.size=typeof h.size!="undefined"?h.size:"maxi",u=u.split("");for(m in u)if(!o(m)){switch(l=l+u[m],d={up:0,down:0,mid:0},h.size){case"mini":d.up=s(8),d.mid=s(2),d.down=s(8);break;case"maxi":d.up=s(16)+3,d.mid=s(4)+1,d.down=s(64)+3;break;default:d.up=s(8)+1,d.mid=s(6)/2,d.down=s(8)+1;break}var v=["up","mid","down"];for(var g in v)for(var x=v[g],y=0;y<=d[x];y++)h[x]&&(l=l+r[x][s(r[x].length)])}return l}return c(e,i)}});var xa=E((Z_,_a)=>{_a.exports=function(t){return function(e,i,r){if(e===" ")return e;switch(i%3){case 0:return t.red(e);case 1:return t.white(e);case 2:return t.blue(e)}}}});var ba=E((X_,ya)=>{ya.exports=function(t){return function(e,i,r){return i%2===0?e:t.inverse(e)}}});var Ea=E((J_,wa)=>{wa.exports=function(t){var e=["red","yellow","green","blue","magenta"];return function(i,r,n){return i===" "?i:t[e[r++%e.length]](i)}}});var ka=E((Q_,Sa)=>{Sa.exports=function(t){var e=["underline","inverse","grey","yellow","red","green","blue","white","cyan","magenta","brightYellow","brightRed","brightGreen","brightBlue","brightWhite","brightCyan","brightMagenta"];return function(i,r,n){return i===" "?i:t[e[Math.round(Math.random()*(e.length-2))]](i)}}});var Ba=E((tx,Aa)=>{var ve={};Aa.exports=ve;ve.themes={};var wp=require("util"),pi=ve.styles=ca(),Ca=Object.defineProperties,Ep=new RegExp(/[\r\n]+/g);ve.supportsColor=pa().supportsColor;typeof ve.enabled=="undefined"&&(ve.enabled=ve.supportsColor()!==!1);ve.enable=function(){ve.enabled=!0};ve.disable=function(){ve.enabled=!1};ve.stripColors=ve.strip=function(t){return(""+t).replace(/\x1B\[\d+m/g,"")};var ex=ve.stylize=function(e,i){if(!ve.enabled)return e+"";var r=pi[i];return!r&&i in ve?ve[i](e):r.open+e+r.close},Sp=/[|\\{}()[\]^$+*?.]/g,kp=function(t){if(typeof t!="string")throw new TypeError("Expected a string");return t.replace(Sp,"\\$&")};function Ta(t){var e=function i(){return Cp.apply(i,arguments)};return e._styles=t,e.__proto__=Op,e}var Ia=function(){var t={};return pi.grey=pi.gray,Object.keys(pi).forEach(function(e){pi[e].closeRe=new RegExp(kp(pi[e].close),"g"),t[e]={get:function(){return Ta(this._styles.concat(e))}}}),t}(),Op=Ca(function(){},Ia);function Cp(){var t=Array.prototype.slice.call(arguments),e=t.map(function(o){return o!=null&&o.constructor===String?o:wp.inspect(o)}).join(" ");if(!ve.enabled||!e)return e;for(var i=e.indexOf(`
`)!=-1,r=this._styles,n=r.length;n--;){var s=pi[r[n]];e=s.open+e.replace(s.closeRe,s.open)+s.close,i&&(e=e.replace(Ep,function(o){return s.close+o+s.open}))}return e}ve.setTheme=function(t){if(typeof t=="string"){console.log("colors.setTheme now only accepts an object, not a string.  If you are trying to set a theme from a file, it is now your (the caller's) responsibility to require the file.  The old syntax looked like colors.setTheme(__dirname + '/../themes/generic-logging.js'); The new syntax looks like colors.setTheme(require(__dirname + '/../themes/generic-logging.js'));");return}for(var e in t)(function(i){ve[i]=function(r){if(typeof t[i]=="object"){var n=r;for(var s in t[i])n=ve[t[i][s]](n);return n}return ve[t[i]](r)}})(e)};function Tp(){var t={};return Object.keys(Ia).forEach(function(e){t[e]={get:function(){return Ta([e])}}}),t}var Ip=function(e,i){var r=i.split("");return r=r.map(e),r.join("")};ve.trap=ma();ve.zalgo=va();ve.maps={};ve.maps.america=xa()(ve);ve.maps.zebra=ba()(ve);ve.maps.rainbow=Ea()(ve);ve.maps.random=ka()(ve);for(Oa in ve.maps)(function(t){ve[t]=function(e){return Ip(ve.maps[t],e)}})(Oa);var Oa;Ca(ve,Tp())});var Pa=E((ix,Ra)=>{var Ap=Ba();Ra.exports=Ap});var La=E((rx,Na)=>{var Li=1e3,Fi=Li*60,Mi=Fi*60,di=Mi*24,Bp=di*7,Rp=di*365.25;Na.exports=function(t,e){e=e||{};var i=typeof t;if(i==="string"&&t.length>0)return Pp(t);if(i==="number"&&isFinite(t))return e.long?Lp(t):Np(t);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(t))};function Pp(t){if(t=String(t),!(t.length>100)){var e=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(t);if(e){var i=parseFloat(e[1]),r=(e[2]||"ms").toLowerCase();switch(r){case"years":case"year":case"yrs":case"yr":case"y":return i*Rp;case"weeks":case"week":case"w":return i*Bp;case"days":case"day":case"d":return i*di;case"hours":case"hour":case"hrs":case"hr":case"h":return i*Mi;case"minutes":case"minute":case"mins":case"min":case"m":return i*Fi;case"seconds":case"second":case"secs":case"sec":case"s":return i*Li;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return i;default:return}}}}function Np(t){var e=Math.abs(t);return e>=di?Math.round(t/di)+"d":e>=Mi?Math.round(t/Mi)+"h":e>=Fi?Math.round(t/Fi)+"m":e>=Li?Math.round(t/Li)+"s":t+"ms"}function Lp(t){var e=Math.abs(t);return e>=di?Gr(t,e,di,"day"):e>=Mi?Gr(t,e,Mi,"hour"):e>=Fi?Gr(t,e,Fi,"minute"):e>=Li?Gr(t,e,Li,"second"):t+" ms"}function Gr(t,e,i,r){var n=e>=i*1.5;return Math.round(t/i)+" "+r+(n?"s":"")}});var zn=E((nx,Fa)=>{function Fp(t){i.debug=i,i.default=i,i.coerce=u,i.disable=s,i.enable=n,i.enabled=o,i.humanize=La(),i.destroy=h,Object.keys(t).forEach(l=>{i[l]=t[l]}),i.names=[],i.skips=[],i.formatters={};function e(l){let d=0;for(let m=0;m<l.length;m++)d=(d<<5)-d+l.charCodeAt(m),d|=0;return i.colors[Math.abs(d)%i.colors.length]}i.selectColor=e;function i(l){let d,m=null,v,g;function x(...y){if(!x.enabled)return;let O=x,B=Number(new Date),C=B-(d||B);O.diff=C,O.prev=d,O.curr=B,d=B,y[0]=i.coerce(y[0]),typeof y[0]!="string"&&y.unshift("%O");let P=0;y[0]=y[0].replace(/%([a-zA-Z%])/g,(J,A)=>{if(J==="%%")return"%";P++;let z=i.formatters[A];if(typeof z=="function"){let k=y[P];J=z.call(O,k),y.splice(P,1),P--}return J}),i.formatArgs.call(O,y),(O.log||i.log).apply(O,y)}return x.namespace=l,x.useColors=i.useColors(),x.color=i.selectColor(l),x.extend=r,x.destroy=i.destroy,Object.defineProperty(x,"enabled",{enumerable:!0,configurable:!1,get:()=>m!==null?m:(v!==i.namespaces&&(v=i.namespaces,g=i.enabled(l)),g),set:y=>{m=y}}),typeof i.init=="function"&&i.init(x),x}function r(l,d){let m=i(this.namespace+(typeof d=="undefined"?":":d)+l);return m.log=this.log,m}function n(l){i.save(l),i.namespaces=l,i.names=[],i.skips=[];let d,m=(typeof l=="string"?l:"").split(/[\s,]+/),v=m.length;for(d=0;d<v;d++)m[d]&&(l=m[d].replace(/\*/g,".*?"),l[0]==="-"?i.skips.push(new RegExp("^"+l.slice(1)+"$")):i.names.push(new RegExp("^"+l+"$")))}function s(){let l=[...i.names.map(c),...i.skips.map(c).map(d=>"-"+d)].join(",");return i.enable(""),l}function o(l){if(l[l.length-1]==="*")return!0;let d,m;for(d=0,m=i.skips.length;d<m;d++)if(i.skips[d].test(l))return!1;for(d=0,m=i.names.length;d<m;d++)if(i.names[d].test(l))return!0;return!1}function c(l){return l.toString().substring(2,l.toString().length-2).replace(/\.\*\?$/,"*")}function u(l){return l instanceof Error?l.stack||l.message:l}function h(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return i.enable(i.load()),i}Fa.exports=Fp});var Ma=E((ht,zr)=>{ht.formatArgs=Dp;ht.save=Up;ht.load=jp;ht.useColors=Mp;ht.storage=qp();ht.destroy=(()=>{let t=!1;return()=>{t||(t=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})();ht.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function Mp(){return typeof window!="undefined"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs)?!0:typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/)?!1:typeof document!="undefined"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window!="undefined"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function Dp(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+zr.exports.humanize(this.diff),!this.useColors)return;let e="color: "+this.color;t.splice(1,0,e,"color: inherit");let i=0,r=0;t[0].replace(/%[a-zA-Z%]/g,n=>{n!=="%%"&&(i++,n==="%c"&&(r=i))}),t.splice(r,0,e)}ht.log=console.debug||console.log||(()=>{});function Up(t){try{t?ht.storage.setItem("debug",t):ht.storage.removeItem("debug")}catch{}}function jp(){let t;try{t=ht.storage.getItem("debug")}catch{}return!t&&typeof process!="undefined"&&"env"in process&&(t=process.env.DEBUG),t}function qp(){try{return localStorage}catch{}}zr.exports=zn()(ht);var{formatters:Vp}=zr.exports;Vp.j=function(t){try{return JSON.stringify(t)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}});var Ua=E((sx,Da)=>{"use strict";Da.exports=(t,e)=>{e=e||process.argv;let i=t.startsWith("-")?"":t.length===1?"-":"--",r=e.indexOf(i+t),n=e.indexOf("--");return r!==-1&&(n===-1?!0:r<n)}});var qa=E((ox,ja)=>{"use strict";var Hp=require("os"),Ot=Ua(),Ze=process.env,Di;Ot("no-color")||Ot("no-colors")||Ot("color=false")?Di=!1:(Ot("color")||Ot("colors")||Ot("color=true")||Ot("color=always"))&&(Di=!0);"FORCE_COLOR"in Ze&&(Di=Ze.FORCE_COLOR.length===0||parseInt(Ze.FORCE_COLOR,10)!==0);function $p(t){return t===0?!1:{level:t,hasBasic:!0,has256:t>=2,has16m:t>=3}}function Gp(t){if(Di===!1)return 0;if(Ot("color=16m")||Ot("color=full")||Ot("color=truecolor"))return 3;if(Ot("color=256"))return 2;if(t&&!t.isTTY&&Di!==!0)return 0;let e=Di?1:0;if(process.platform==="win32"){let i=Hp.release().split(".");return Number(process.versions.node.split(".")[0])>=8&&Number(i[0])>=10&&Number(i[2])>=10586?Number(i[2])>=14931?3:2:1}if("CI"in Ze)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI"].some(i=>i in Ze)||Ze.CI_NAME==="codeship"?1:e;if("TEAMCITY_VERSION"in Ze)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(Ze.TEAMCITY_VERSION)?1:0;if(Ze.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in Ze){let i=parseInt((Ze.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(Ze.TERM_PROGRAM){case"iTerm.app":return i>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(Ze.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(Ze.TERM)||"COLORTERM"in Ze?1:(Ze.TERM==="dumb",e)}function Wn(t){let e=Gp(t);return $p(e)}ja.exports={supportsColor:Wn,stdout:Wn(process.stdout),stderr:Wn(process.stderr)}});var Ha=E((We,Yr)=>{var zp=require("tty"),Wr=require("util");We.init=Qp;We.log=Zp;We.formatArgs=Yp;We.save=Xp;We.load=Jp;We.useColors=Wp;We.destroy=Wr.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.");We.colors=[6,2,3,4,5,1];try{let t=qa();t&&(t.stderr||t).level>=2&&(We.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch{}We.inspectOpts=Object.keys(process.env).filter(t=>/^debug_/i.test(t)).reduce((t,e)=>{let i=e.substring(6).toLowerCase().replace(/_([a-z])/g,(n,s)=>s.toUpperCase()),r=process.env[e];return/^(yes|on|true|enabled)$/i.test(r)?r=!0:/^(no|off|false|disabled)$/i.test(r)?r=!1:r==="null"?r=null:r=Number(r),t[i]=r,t},{});function Wp(){return"colors"in We.inspectOpts?!!We.inspectOpts.colors:zp.isatty(process.stderr.fd)}function Yp(t){let{namespace:e,useColors:i}=this;if(i){let r=this.color,n="\x1B[3"+(r<8?r:"8;5;"+r),s=`  ${n};1m${e} \x1B[0m`;t[0]=s+t[0].split(`
`).join(`
`+s),t.push(n+"m+"+Yr.exports.humanize(this.diff)+"\x1B[0m")}else t[0]=Kp()+e+" "+t[0]}function Kp(){return We.inspectOpts.hideDate?"":new Date().toISOString()+" "}function Zp(...t){return process.stderr.write(Wr.format(...t)+`
`)}function Xp(t){t?process.env.DEBUG=t:delete process.env.DEBUG}function Jp(){return process.env.DEBUG}function Qp(t){t.inspectOpts={};let e=Object.keys(We.inspectOpts);for(let i=0;i<e.length;i++)t.inspectOpts[e[i]]=We.inspectOpts[e[i]]}Yr.exports=zn()(We);var{formatters:Va}=Yr.exports;Va.o=function(t){return this.inspectOpts.colors=this.useColors,Wr.inspect(t,this.inspectOpts).split(`
`).map(e=>e.trim()).join(" ")};Va.O=function(t){return this.inspectOpts.colors=this.useColors,Wr.inspect(t,this.inspectOpts)}});var Ui=E((ax,Yn)=>{typeof process=="undefined"||process.type==="renderer"||process.browser===!0||process.__nwjs?Yn.exports=Ma():Yn.exports=Ha()});var $a=E((lx,ed)=>{ed.exports={name:"dotenv",version:"16.4.5",description:"Loads environment variables from .env file",main:"lib/main.js",types:"lib/main.d.ts",exports:{".":{types:"./lib/main.d.ts",require:"./lib/main.js",default:"./lib/main.js"},"./config":"./config.js","./config.js":"./config.js","./lib/env-options":"./lib/env-options.js","./lib/env-options.js":"./lib/env-options.js","./lib/cli-options":"./lib/cli-options.js","./lib/cli-options.js":"./lib/cli-options.js","./package.json":"./package.json"},scripts:{"dts-check":"tsc --project tests/types/tsconfig.json",lint:"standard","lint-readme":"standard-markdown",pretest:"npm run lint && npm run dts-check",test:"tap tests/*.js --100 -Rspec","test:coverage":"tap --coverage-report=lcov",prerelease:"npm test",release:"standard-version"},repository:{type:"git",url:"git://github.com/motdotla/dotenv.git"},funding:"https://dotenvx.com",keywords:["dotenv","env",".env","environment","variables","config","settings"],readmeFilename:"README.md",license:"BSD-2-Clause",devDependencies:{"@definitelytyped/dtslint":"^0.0.133","@types/node":"^18.11.3",decache:"^4.6.1",sinon:"^14.0.1",standard:"^17.0.0","standard-markdown":"^7.1.0","standard-version":"^9.5.0",tap:"^16.3.0",tar:"^6.1.11",typescript:"^4.8.4"},engines:{node:">=12"},browser:{fs:!1}}});var Ya=E((cx,qt)=>{var Kn=require("fs"),Zn=require("path"),td=require("os"),id=require("crypto"),rd=$a(),Xn=rd.version,nd=/(?:^|^)\s*(?:export\s+)?([\w.-]+)(?:\s*=\s*?|:\s+?)(\s*'(?:\\'|[^'])*'|\s*"(?:\\"|[^"])*"|\s*`(?:\\`|[^`])*`|[^#\r\n]+)?\s*(?:#.*)?(?:$|$)/mg;function sd(t){let e={},i=t.toString();i=i.replace(/\r\n?/mg,`
`);let r;for(;(r=nd.exec(i))!=null;){let n=r[1],s=r[2]||"";s=s.trim();let o=s[0];s=s.replace(/^(['"`])([\s\S]*)\1$/mg,"$2"),o==='"'&&(s=s.replace(/\\n/g,`
`),s=s.replace(/\\r/g,"\r")),e[n]=s}return e}function od(t){let e=Wa(t),i=Ue.configDotenv({path:e});if(!i.parsed){let o=new Error(`MISSING_DATA: Cannot parse ${e} for an unknown reason`);throw o.code="MISSING_DATA",o}let r=za(t).split(","),n=r.length,s;for(let o=0;o<n;o++)try{let c=r[o].trim(),u=cd(i,c);s=Ue.decrypt(u.ciphertext,u.key);break}catch(c){if(o+1>=n)throw c}return Ue.parse(s)}function ad(t){console.log(`[dotenv@${Xn}][INFO] ${t}`)}function ld(t){console.log(`[dotenv@${Xn}][WARN] ${t}`)}function Kr(t){console.log(`[dotenv@${Xn}][DEBUG] ${t}`)}function za(t){return t&&t.DOTENV_KEY&&t.DOTENV_KEY.length>0?t.DOTENV_KEY:process.env.DOTENV_KEY&&process.env.DOTENV_KEY.length>0?process.env.DOTENV_KEY:""}function cd(t,e){let i;try{i=new URL(e)}catch(c){if(c.code==="ERR_INVALID_URL"){let u=new Error("INVALID_DOTENV_KEY: Wrong format. Must be in valid uri format like dotenv://:<EMAIL>/vault/.env.vault?environment=development");throw u.code="INVALID_DOTENV_KEY",u}throw c}let r=i.password;if(!r){let c=new Error("INVALID_DOTENV_KEY: Missing key part");throw c.code="INVALID_DOTENV_KEY",c}let n=i.searchParams.get("environment");if(!n){let c=new Error("INVALID_DOTENV_KEY: Missing environment part");throw c.code="INVALID_DOTENV_KEY",c}let s=`DOTENV_VAULT_${n.toUpperCase()}`,o=t.parsed[s];if(!o){let c=new Error(`NOT_FOUND_DOTENV_ENVIRONMENT: Cannot locate environment ${s} in your .env.vault file.`);throw c.code="NOT_FOUND_DOTENV_ENVIRONMENT",c}return{ciphertext:o,key:r}}function Wa(t){let e=null;if(t&&t.path&&t.path.length>0)if(Array.isArray(t.path))for(let i of t.path)Kn.existsSync(i)&&(e=i.endsWith(".vault")?i:`${i}.vault`);else e=t.path.endsWith(".vault")?t.path:`${t.path}.vault`;else e=Zn.resolve(process.cwd(),".env.vault");return Kn.existsSync(e)?e:null}function Ga(t){return t[0]==="~"?Zn.join(td.homedir(),t.slice(1)):t}function ud(t){ad("Loading env from encrypted .env.vault");let e=Ue._parseVault(t),i=process.env;return t&&t.processEnv!=null&&(i=t.processEnv),Ue.populate(i,e,t),{parsed:e}}function fd(t){let e=Zn.resolve(process.cwd(),".env"),i="utf8",r=!!(t&&t.debug);t&&t.encoding?i=t.encoding:r&&Kr("No encoding is specified. UTF-8 is used by default");let n=[e];if(t&&t.path)if(!Array.isArray(t.path))n=[Ga(t.path)];else{n=[];for(let u of t.path)n.push(Ga(u))}let s,o={};for(let u of n)try{let h=Ue.parse(Kn.readFileSync(u,{encoding:i}));Ue.populate(o,h,t)}catch(h){r&&Kr(`Failed to load ${u} ${h.message}`),s=h}let c=process.env;return t&&t.processEnv!=null&&(c=t.processEnv),Ue.populate(c,o,t),s?{parsed:o,error:s}:{parsed:o}}function hd(t){if(za(t).length===0)return Ue.configDotenv(t);let e=Wa(t);return e?Ue._configVault(t):(ld(`You set DOTENV_KEY but you are missing a .env.vault file at ${e}. Did you forget to build it?`),Ue.configDotenv(t))}function pd(t,e){let i=Buffer.from(e.slice(-64),"hex"),r=Buffer.from(t,"base64"),n=r.subarray(0,12),s=r.subarray(-16);r=r.subarray(12,-16);try{let o=id.createDecipheriv("aes-256-gcm",i,n);return o.setAuthTag(s),`${o.update(r)}${o.final()}`}catch(o){let c=o instanceof RangeError,u=o.message==="Invalid key length",h=o.message==="Unsupported state or unable to authenticate data";if(c||u){let l=new Error("INVALID_DOTENV_KEY: It must be 64 characters long (or more)");throw l.code="INVALID_DOTENV_KEY",l}else if(h){let l=new Error("DECRYPTION_FAILED: Please check your DOTENV_KEY");throw l.code="DECRYPTION_FAILED",l}else throw o}}function dd(t,e,i={}){let r=!!(i&&i.debug),n=!!(i&&i.override);if(typeof e!="object"){let s=new Error("OBJECT_REQUIRED: Please check the processEnv argument being passed to populate");throw s.code="OBJECT_REQUIRED",s}for(let s of Object.keys(e))Object.prototype.hasOwnProperty.call(t,s)?(n===!0&&(t[s]=e[s]),r&&Kr(n===!0?`"${s}" is already defined and WAS overwritten`:`"${s}" is already defined and was NOT overwritten`)):t[s]=e[s]}var Ue={configDotenv:fd,_configVault:ud,_parseVault:od,config:hd,decrypt:pd,parse:sd,populate:dd};qt.exports.configDotenv=Ue.configDotenv;qt.exports._configVault=Ue._configVault;qt.exports._parseVault=Ue._parseVault;qt.exports.config=Ue.config;qt.exports.decrypt=Ue.decrypt;qt.exports.parse=Ue.parse;qt.exports.populate=Ue.populate;qt.exports=Ue});var Za=E(Ka=>{"use strict";var md=require("url").parse,gd={ftp:21,gopher:70,http:80,https:443,ws:80,wss:443},vd=String.prototype.endsWith||function(t){return t.length<=this.length&&this.indexOf(t,this.length-t.length)!==-1};function _d(t){var e=typeof t=="string"?md(t):t||{},i=e.protocol,r=e.host,n=e.port;if(typeof r!="string"||!r||typeof i!="string"||(i=i.split(":",1)[0],r=r.replace(/:\d*$/,""),n=parseInt(n)||gd[i]||0,!xd(r,n)))return"";var s=ji("npm_config_"+i+"_proxy")||ji(i+"_proxy")||ji("npm_config_proxy")||ji("all_proxy");return s&&s.indexOf("://")===-1&&(s=i+"://"+s),s}function xd(t,e){var i=(ji("npm_config_no_proxy")||ji("no_proxy")).toLowerCase();return i?i==="*"?!1:i.split(/[,\s]/).every(function(r){if(!r)return!0;var n=r.match(/^(.+):(\d+)$/),s=n?n[1]:r,o=n?parseInt(n[2]):0;return o&&o!==e?!0:/^[.*]/.test(s)?(s.charAt(0)==="*"&&(s=s.slice(1)),!vd.call(t,s)):t!==s}):!0}function ji(t){return process.env[t.toLowerCase()]||process.env[t.toUpperCase()]||""}Ka.getProxyForUrl=_d});var Xa=E(Jn=>{"use strict";Object.defineProperty(Jn,"__esModule",{value:!0});function yd(t){return function(e,i){return new Promise((r,n)=>{t.call(this,e,i,(s,o)=>{s?n(s):r(o)})})}}Jn.default=yd});var ts=E((es,Qa)=>{"use strict";var Ja=es&&es.__importDefault||function(t){return t&&t.__esModule?t:{default:t}},bd=require("events"),wd=Ja(Ui()),Ed=Ja(Xa()),cr=wd.default("agent-base");function Sd(t){return!!t&&typeof t.addRequest=="function"}function Qn(){let{stack:t}=new Error;return typeof t!="string"?!1:t.split(`
`).some(e=>e.indexOf("(https.js:")!==-1||e.indexOf("node:https:")!==-1)}function Zr(t,e){return new Zr.Agent(t,e)}(function(t){class e extends bd.EventEmitter{constructor(r,n){super();let s=n;typeof r=="function"?this.callback=r:r&&(s=r),this.timeout=null,s&&typeof s.timeout=="number"&&(this.timeout=s.timeout),this.maxFreeSockets=1,this.maxSockets=1,this.maxTotalSockets=1/0,this.sockets={},this.freeSockets={},this.requests={},this.options={}}get defaultPort(){return typeof this.explicitDefaultPort=="number"?this.explicitDefaultPort:Qn()?443:80}set defaultPort(r){this.explicitDefaultPort=r}get protocol(){return typeof this.explicitProtocol=="string"?this.explicitProtocol:Qn()?"https:":"http:"}set protocol(r){this.explicitProtocol=r}callback(r,n,s){throw new Error('"agent-base" has no default implementation, you must subclass and override `callback()`')}addRequest(r,n){let s=Object.assign({},n);typeof s.secureEndpoint!="boolean"&&(s.secureEndpoint=Qn()),s.host==null&&(s.host="localhost"),s.port==null&&(s.port=s.secureEndpoint?443:80),s.protocol==null&&(s.protocol=s.secureEndpoint?"https:":"http:"),s.host&&s.path&&delete s.path,delete s.agent,delete s.hostname,delete s._defaultAgent,delete s.defaultPort,delete s.createConnection,r._last=!0,r.shouldKeepAlive=!1;let o=!1,c=null,u=s.timeout||this.timeout,h=v=>{r._hadError||(r.emit("error",v),r._hadError=!0)},l=()=>{c=null,o=!0;let v=new Error(`A "socket" was not created for HTTP request before ${u}ms`);v.code="ETIMEOUT",h(v)},d=v=>{o||(c!==null&&(clearTimeout(c),c=null),h(v))},m=v=>{if(o)return;if(c!=null&&(clearTimeout(c),c=null),Sd(v)){cr("Callback returned another Agent instance %o",v.constructor.name),v.addRequest(r,s);return}if(v){v.once("free",()=>{this.freeSocket(v,s)}),r.onSocket(v);return}let g=new Error(`no Duplex stream was returned to agent-base for \`${r.method} ${r.path}\``);h(g)};if(typeof this.callback!="function"){h(new Error("`callback` is not defined"));return}this.promisifiedCallback||(this.callback.length>=3?(cr("Converting legacy callback function to promise"),this.promisifiedCallback=Ed.default(this.callback)):this.promisifiedCallback=this.callback),typeof u=="number"&&u>0&&(c=setTimeout(l,u)),"port"in s&&typeof s.port!="number"&&(s.port=Number(s.port));try{cr("Resolving socket for %o request: %o",s.protocol,`${r.method} ${r.path}`),Promise.resolve(this.promisifiedCallback(r,s)).then(m,d)}catch(v){Promise.reject(v).catch(d)}}freeSocket(r,n){cr("Freeing socket %o %o",r.constructor.name,n),r.destroy()}destroy(){cr("Destroying agent %o",this.constructor.name)}}t.Agent=e,t.prototype=t.Agent.prototype})(Zr||(Zr={}));Qa.exports=Zr});var el=E(fr=>{"use strict";var kd=fr&&fr.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(fr,"__esModule",{value:!0});var Od=kd(Ui()),ur=Od.default("https-proxy-agent:parse-proxy-response");function Cd(t){return new Promise((e,i)=>{let r=0,n=[];function s(){let d=t.read();d?l(d):t.once("readable",s)}function o(){t.removeListener("end",u),t.removeListener("error",h),t.removeListener("close",c),t.removeListener("readable",s)}function c(d){ur("onclose had error %o",d)}function u(){ur("onend")}function h(d){o(),ur("onerror %o",d),i(d)}function l(d){n.push(d),r+=d.length;let m=Buffer.concat(n,r);if(m.indexOf(`\r
\r
`)===-1){ur("have not received end of HTTP headers yet..."),s();return}let g=m.toString("ascii",0,m.indexOf(`\r
`)),x=+g.split(" ")[1];ur("got proxy server response: %o",g),e({statusCode:x,buffered:m})}t.on("error",h),t.on("close",c),t.on("end",u),s()})}fr.default=Cd});var rl=E(mi=>{"use strict";var Td=mi&&mi.__awaiter||function(t,e,i,r){function n(s){return s instanceof i?s:new i(function(o){o(s)})}return new(i||(i=Promise))(function(s,o){function c(l){try{h(r.next(l))}catch(d){o(d)}}function u(l){try{h(r.throw(l))}catch(d){o(d)}}function h(l){l.done?s(l.value):n(l.value).then(c,u)}h((r=r.apply(t,e||[])).next())})},qi=mi&&mi.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(mi,"__esModule",{value:!0});var tl=qi(require("net")),il=qi(require("tls")),Id=qi(require("url")),Ad=qi(require("assert")),Bd=qi(Ui()),Rd=ts(),Pd=qi(el()),hr=Bd.default("https-proxy-agent:agent"),is=class extends Rd.Agent{constructor(e){let i;if(typeof e=="string"?i=Id.default.parse(e):i=e,!i)throw new Error("an HTTP(S) proxy server `host` and `port` must be specified!");hr("creating new HttpsProxyAgent instance: %o",i),super(i);let r=Object.assign({},i);this.secureProxy=i.secureProxy||Fd(r.protocol),r.host=r.hostname||r.host,typeof r.port=="string"&&(r.port=parseInt(r.port,10)),!r.port&&r.host&&(r.port=this.secureProxy?443:80),this.secureProxy&&!("ALPNProtocols"in r)&&(r.ALPNProtocols=["http 1.1"]),r.host&&r.path&&(delete r.path,delete r.pathname),this.proxy=r}callback(e,i){return Td(this,void 0,void 0,function*(){let{proxy:r,secureProxy:n}=this,s;n?(hr("Creating `tls.Socket`: %o",r),s=il.default.connect(r)):(hr("Creating `net.Socket`: %o",r),s=tl.default.connect(r));let o=Object.assign({},r.headers),u=`CONNECT ${`${i.host}:${i.port}`} HTTP/1.1\r
`;r.auth&&(o["Proxy-Authorization"]=`Basic ${Buffer.from(r.auth).toString("base64")}`);let{host:h,port:l,secureEndpoint:d}=i;Ld(l,d)||(h+=`:${l}`),o.Host=h,o.Connection="close";for(let y of Object.keys(o))u+=`${y}: ${o[y]}\r
`;let m=Pd.default(s);s.write(`${u}\r
`);let{statusCode:v,buffered:g}=yield m;if(v===200){if(e.once("socket",Nd),i.secureEndpoint){let y=i.servername||i.host;if(!y)throw new Error('Could not determine "servername"');return hr("Upgrading socket connection to TLS"),il.default.connect(Object.assign(Object.assign({},Md(i,"host","hostname","path","port")),{socket:s,servername:y}))}return s}s.destroy();let x=new tl.default.Socket;return x.readable=!0,e.once("socket",y=>{hr("replaying proxy buffer for failed request"),Ad.default(y.listenerCount("data")>0),y.push(g),y.push(null)}),x})}};mi.default=is;function Nd(t){t.resume()}function Ld(t,e){return!!(!e&&t===80||e&&t===443)}function Fd(t){return typeof t=="string"?/^https:?$/i.test(t):!1}function Md(t,...e){let i={},r;for(r in t)e.includes(r)||(i[r]=t[r]);return i}});var sl=E((ss,nl)=>{"use strict";var Dd=ss&&ss.__importDefault||function(t){return t&&t.__esModule?t:{default:t}},rs=Dd(rl());function ns(t){return new rs.default(t)}(function(t){t.HttpsProxyAgent=rs.default,t.prototype=rs.default.prototype})(ns||(ns={}));nl.exports=ns});var ll=E((dx,Xr)=>{var al=al||function(t){return Buffer.from(t).toString("base64")};function Ud(t){var e=this,i=Math.round,r=Math.floor,n=new Array(64),s=new Array(64),o=new Array(64),c=new Array(64),u,h,l,d,m=new Array(65535),v=new Array(65535),g=new Array(64),x=new Array(64),y=[],O=0,B=7,C=new Array(64),P=new Array(64),S=new Array(64),J=new Array(256),A=new Array(2048),z,k=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],L=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],D=[0,1,2,3,4,5,6,7,8,9,10,11],X=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],j=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],se=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],M=[0,1,2,3,4,5,6,7,8,9,10,11],$=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],Y=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function Q(b){for(var W=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],ee=0;ee<64;ee++){var Z=r((W[ee]*b+50)/100);Z<1?Z=1:Z>255&&(Z=255),n[k[ee]]=Z}for(var oe=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],ae=0;ae<64;ae++){var xe=r((oe[ae]*b+50)/100);xe<1?xe=1:xe>255&&(xe=255),s[k[ae]]=xe}for(var ye=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],Be=0,Oe=0;Oe<8;Oe++)for(var N=0;N<8;N++)o[Be]=1/(n[k[Be]]*ye[Oe]*ye[N]*8),c[Be]=1/(s[k[Be]]*ye[Oe]*ye[N]*8),Be++}function V(b,W){for(var ee=0,Z=0,oe=new Array,ae=1;ae<=16;ae++){for(var xe=1;xe<=b[ae];xe++)oe[W[Z]]=[],oe[W[Z]][0]=ee,oe[W[Z]][1]=ae,Z++,ee++;ee*=2}return oe}function we(){u=V(L,D),h=V(se,M),l=V(X,j),d=V($,Y)}function de(){for(var b=1,W=2,ee=1;ee<=15;ee++){for(var Z=b;Z<W;Z++)v[32767+Z]=ee,m[32767+Z]=[],m[32767+Z][1]=ee,m[32767+Z][0]=Z;for(var oe=-(W-1);oe<=-b;oe++)v[32767+oe]=ee,m[32767+oe]=[],m[32767+oe][1]=ee,m[32767+oe][0]=W-1+oe;b<<=1,W<<=1}}function le(){for(var b=0;b<256;b++)A[b]=19595*b,A[b+256>>0]=38470*b,A[b+512>>0]=7471*b+32768,A[b+768>>0]=-11059*b,A[b+1024>>0]=-21709*b,A[b+1280>>0]=32768*b+8421375,A[b+1536>>0]=-27439*b,A[b+1792>>0]=-5329*b}function ce(b){for(var W=b[0],ee=b[1]-1;ee>=0;)W&1<<ee&&(O|=1<<B),ee--,B--,B<0&&(O==255?(w(255),w(0)):w(O),B=7,O=0)}function w(b){y.push(b)}function K(b){w(b>>8&255),w(b&255)}function Se(b,W){var ee,Z,oe,ae,xe,ye,Be,Oe,N=0,G,re=8,Te=64;for(G=0;G<re;++G){ee=b[N],Z=b[N+1],oe=b[N+2],ae=b[N+3],xe=b[N+4],ye=b[N+5],Be=b[N+6],Oe=b[N+7];var ne=ee+Oe,pe=ee-Oe,Ee=Z+Be,te=Z-Be,be=oe+ye,Me=oe-ye,Ce=ae+xe,at=ae-xe,gt=ne+Ce,Dt=ne-Ce,Wt=Ee+be,Yt=Ee-be;b[N]=gt+Wt,b[N+4]=gt-Wt;var si=(Yt+Dt)*.707106781;b[N+2]=Dt+si,b[N+6]=Dt-si,gt=at+Me,Wt=Me+te,Yt=te+pe;var oi=(gt-Yt)*.382683433,Bi=.5411961*gt+oi,ai=1.306562965*Yt+oi,li=Wt*.707106781,ci=pe+li,ui=pe-li;b[N+5]=ui+Bi,b[N+3]=ui-Bi,b[N+1]=ci+ai,b[N+7]=ci-ai,N+=8}for(N=0,G=0;G<re;++G){ee=b[N],Z=b[N+8],oe=b[N+16],ae=b[N+24],xe=b[N+32],ye=b[N+40],Be=b[N+48],Oe=b[N+56];var Ir=ee+Oe,lr=ee-Oe,Ar=Z+Be,Br=Z-Be,Rr=oe+ye,Pr=oe-ye,Nr=ae+xe,jn=ae-xe,fi=Ir+Nr,Ut=Ir-Nr,hi=Ar+Rr,Ri=Ar-Rr;b[N]=fi+hi,b[N+32]=fi-hi;var Lr=(Ri+Ut)*.707106781;b[N+16]=Ut+Lr,b[N+48]=Ut-Lr,fi=jn+Pr,hi=Pr+Br,Ri=Br+lr;var Fr=(fi-Ri)*.382683433,Mr=.5411961*fi+Fr,Dr=1.306562965*Ri+Fr,Bt=hi*.707106781,Ur=lr+Bt,jr=lr-Bt;b[N+40]=jr+Mr,b[N+24]=jr-Mr,b[N+8]=Ur+Dr,b[N+56]=Ur-Dr,N++}var Pi;for(G=0;G<Te;++G)Pi=b[G]*W[G],g[G]=Pi>0?Pi+.5|0:Pi-.5|0;return g}function _e(){K(65504),K(16),w(74),w(70),w(73),w(70),w(0),w(1),w(1),w(0),K(1),K(1),w(0),w(0)}function me(b){if(b){K(65505),b[0]===69&&b[1]===120&&b[2]===105&&b[3]===102?K(b.length+2):(K(b.length+5+2),w(69),w(120),w(105),w(102),w(0));for(var W=0;W<b.length;W++)w(b[W])}}function ge(b,W){K(65472),K(17),w(8),K(W),K(b),w(3),w(1),w(17),w(0),w(2),w(17),w(1),w(3),w(17),w(1)}function ue(){K(65499),K(132),w(0);for(var b=0;b<64;b++)w(n[b]);w(1);for(var W=0;W<64;W++)w(s[W])}function H(){K(65476),K(418),w(0);for(var b=0;b<16;b++)w(L[b+1]);for(var W=0;W<=11;W++)w(D[W]);w(16);for(var ee=0;ee<16;ee++)w(X[ee+1]);for(var Z=0;Z<=161;Z++)w(j[Z]);w(1);for(var oe=0;oe<16;oe++)w(se[oe+1]);for(var ae=0;ae<=11;ae++)w(M[ae]);w(17);for(var xe=0;xe<16;xe++)w($[xe+1]);for(var ye=0;ye<=161;ye++)w(Y[ye])}function F(b){typeof b=="undefined"||b.constructor!==Array||b.forEach(W=>{if(typeof W=="string"){K(65534);var ee=W.length;K(ee+2);var Z;for(Z=0;Z<ee;Z++)w(W.charCodeAt(Z))}})}function ke(){K(65498),K(12),w(3),w(1),w(0),w(2),w(17),w(3),w(17),w(0),w(63),w(0)}function ie(b,W,ee,Z,oe){for(var ae=oe[0],xe=oe[240],ye,Be=16,Oe=63,N=64,G=Se(b,W),re=0;re<N;++re)x[k[re]]=G[re];var Te=x[0]-ee;ee=x[0],Te==0?ce(Z[0]):(ye=32767+Te,ce(Z[v[ye]]),ce(m[ye]));for(var ne=63;ne>0&&x[ne]==0;ne--);if(ne==0)return ce(ae),ee;for(var pe=1,Ee;pe<=ne;){for(var te=pe;x[pe]==0&&pe<=ne;++pe);var be=pe-te;if(be>=Be){Ee=be>>4;for(var Me=1;Me<=Ee;++Me)ce(xe);be=be&15}ye=32767+x[pe],ce(oe[(be<<4)+v[ye]]),ce(m[ye]),pe++}return ne!=Oe&&ce(ae),ee}function fe(){for(var b=String.fromCharCode,W=0;W<256;W++)J[W]=b(W)}this.encode=function(b,W){var ee=new Date().getTime();W&&ot(W),y=new Array,O=0,B=7,K(65496),_e(),F(b.comments),me(b.exifBuffer),ue(),ge(b.width,b.height),H(),ke();var Z=0,oe=0,ae=0;O=0,B=7,this.encode.displayName="_encode_";for(var xe=b.data,ye=b.width,Be=b.height,Oe=ye*4,N=ye*3,G,re=0,Te,ne,pe,Ee,te,be,Me,Ce;re<Be;){for(G=0;G<Oe;){for(Ee=Oe*re+G,te=Ee,be=-1,Me=0,Ce=0;Ce<64;Ce++)Me=Ce>>3,be=(Ce&7)*4,te=Ee+Me*Oe+be,re+Me>=Be&&(te-=Oe*(re+1+Me-Be)),G+be>=Oe&&(te-=G+be-Oe+4),Te=xe[te++],ne=xe[te++],pe=xe[te++],C[Ce]=(A[Te]+A[ne+256>>0]+A[pe+512>>0]>>16)-128,P[Ce]=(A[Te+768>>0]+A[ne+1024>>0]+A[pe+1280>>0]>>16)-128,S[Ce]=(A[Te+1280>>0]+A[ne+1536>>0]+A[pe+1792>>0]>>16)-128;Z=ie(C,o,Z,u,l),oe=ie(P,c,oe,h,d),ae=ie(S,c,ae,h,d),G+=32}re+=8}if(B>=0){var at=[];at[1]=B+1,at[0]=(1<<B+1)-1,ce(at)}if(K(65497),typeof Xr=="undefined")return new Uint8Array(y);return Buffer.from(y);var gt,Dt};function ot(b){if(b<=0&&(b=1),b>100&&(b=100),z!=b){var W=0;b<50?W=Math.floor(5e3/b):W=Math.floor(200-b*2),Q(W),z=b}}function ct(){var b=new Date().getTime();t||(t=50),fe(),we(),de(),le(),ot(t);var W=new Date().getTime()-b}ct()}typeof Xr!="undefined"?Xr.exports=ol:typeof window!="undefined"&&(window["jpeg-js"]=window["jpeg-js"]||{},window["jpeg-js"].encode=ol);function ol(t,e){typeof e=="undefined"&&(e=50);var i=new Ud(e),r=i.encode(t,e);return{data:r,width:t.width,height:t.height}}});var ul=E((mx,as)=>{var os=function(){"use strict";var e=new Int32Array([0,1,8,16,9,2,3,10,17,24,32,25,18,11,4,5,12,19,26,33,40,48,41,34,27,20,13,6,7,14,21,28,35,42,49,56,57,50,43,36,29,22,15,23,30,37,44,51,58,59,52,45,38,31,39,46,53,60,61,54,47,55,62,63]),i=4017,r=799,n=3406,s=2276,o=1567,c=3784,u=5793,h=2896;function l(){}function d(B,C){for(var P=0,S=[],J,A,z=16;z>0&&!B[z-1];)z--;S.push({children:[],index:0});var k=S[0],L;for(J=0;J<z;J++){for(A=0;A<B[J];A++){for(k=S.pop(),k.children[k.index]=C[P];k.index>0;){if(S.length===0)throw new Error("Could not recreate Huffman Table");k=S.pop()}for(k.index++,S.push(k);S.length<=J;)S.push(L={children:[],index:0}),k.children[k.index]=L.children,k=L;P++}J+1<z&&(S.push(L={children:[],index:0}),k.children[k.index]=L.children,k=L)}return S[0].children}function m(B,C,P,S,J,A,z,k,L,D){var X=P.precision,j=P.samplesPerLine,se=P.scanLines,M=P.mcusPerLine,$=P.progressive,Y=P.maxH,Q=P.maxV,V=C,we=0,de=0;function le(){if(de>0)return de--,we>>de&1;if(we=B[C++],we==255){var N=B[C++];if(N)throw new Error("unexpected marker: "+(we<<8|N).toString(16))}return de=7,we>>>7}function ce(N){for(var G=N,re;(re=le())!==null;){if(G=G[re],typeof G=="number")return G;if(typeof G!="object")throw new Error("invalid huffman sequence")}return null}function w(N){for(var G=0;N>0;){var re=le();if(re===null)return;G=G<<1|re,N--}return G}function K(N){var G=w(N);return G>=1<<N-1?G:G+(-1<<N)+1}function Se(N,G){var re=ce(N.huffmanTableDC),Te=re===0?0:K(re);G[0]=N.pred+=Te;for(var ne=1;ne<64;){var pe=ce(N.huffmanTableAC),Ee=pe&15,te=pe>>4;if(Ee===0){if(te<15)break;ne+=16;continue}ne+=te;var be=e[ne];G[be]=K(Ee),ne++}}function _e(N,G){var re=ce(N.huffmanTableDC),Te=re===0?0:K(re)<<L;G[0]=N.pred+=Te}function me(N,G){G[0]|=le()<<L}var ge=0;function ue(N,G){if(ge>0){ge--;return}for(var re=A,Te=z;re<=Te;){var ne=ce(N.huffmanTableAC),pe=ne&15,Ee=ne>>4;if(pe===0){if(Ee<15){ge=w(Ee)+(1<<Ee)-1;break}re+=16;continue}re+=Ee;var te=e[re];G[te]=K(pe)*(1<<L),re++}}var H=0,F;function ke(N,G){for(var re=A,Te=z,ne=0;re<=Te;){var pe=e[re],Ee=G[pe]<0?-1:1;switch(H){case 0:var te=ce(N.huffmanTableAC),be=te&15,ne=te>>4;if(be===0)ne<15?(ge=w(ne)+(1<<ne),H=4):(ne=16,H=1);else{if(be!==1)throw new Error("invalid ACn encoding");F=K(be),H=ne?2:3}continue;case 1:case 2:G[pe]?G[pe]+=(le()<<L)*Ee:(ne--,ne===0&&(H=H==2?3:0));break;case 3:G[pe]?G[pe]+=(le()<<L)*Ee:(G[pe]=F<<L,H=0);break;case 4:G[pe]&&(G[pe]+=(le()<<L)*Ee);break}re++}H===4&&(ge--,ge===0&&(H=0))}function ie(N,G,re,Te,ne){var pe=re/M|0,Ee=re%M,te=pe*N.v+Te,be=Ee*N.h+ne;N.blocks[te]===void 0&&D.tolerantDecoding||G(N,N.blocks[te][be])}function fe(N,G,re){var Te=re/N.blocksPerLine|0,ne=re%N.blocksPerLine;N.blocks[Te]===void 0&&D.tolerantDecoding||G(N,N.blocks[Te][ne])}var ot=S.length,ct,b,W,ee,Z,oe;$?A===0?oe=k===0?_e:me:oe=k===0?ue:ke:oe=Se;var ae=0,xe,ye;ot==1?ye=S[0].blocksPerLine*S[0].blocksPerColumn:ye=M*P.mcusPerColumn,J||(J=ye);for(var Be,Oe;ae<ye;){for(b=0;b<ot;b++)S[b].pred=0;if(ge=0,ot==1)for(ct=S[0],Z=0;Z<J;Z++)fe(ct,oe,ae),ae++;else for(Z=0;Z<J;Z++){for(b=0;b<ot;b++)for(ct=S[b],Be=ct.h,Oe=ct.v,W=0;W<Oe;W++)for(ee=0;ee<Be;ee++)ie(ct,oe,ae,W,ee);if(ae++,ae===ye)break}if(ae===ye)do{if(B[C]===255&&B[C+1]!==0)break;C+=1}while(C<B.length-2);if(de=0,xe=B[C]<<8|B[C+1],xe<65280)throw new Error("marker was not found");if(xe>=65488&&xe<=65495)C+=2;else break}return C-V}function v(B,C){var P=[],S=C.blocksPerLine,J=C.blocksPerColumn,A=S<<3,z=new Int32Array(64),k=new Uint8Array(64);function L(V,we,de){var le=C.quantizationTable,ce,w,K,Se,_e,me,ge,ue,H,F=de,ke;for(ke=0;ke<64;ke++)F[ke]=V[ke]*le[ke];for(ke=0;ke<8;++ke){var ie=8*ke;if(F[1+ie]==0&&F[2+ie]==0&&F[3+ie]==0&&F[4+ie]==0&&F[5+ie]==0&&F[6+ie]==0&&F[7+ie]==0){H=u*F[0+ie]+512>>10,F[0+ie]=H,F[1+ie]=H,F[2+ie]=H,F[3+ie]=H,F[4+ie]=H,F[5+ie]=H,F[6+ie]=H,F[7+ie]=H;continue}ce=u*F[0+ie]+128>>8,w=u*F[4+ie]+128>>8,K=F[2+ie],Se=F[6+ie],_e=h*(F[1+ie]-F[7+ie])+128>>8,ue=h*(F[1+ie]+F[7+ie])+128>>8,me=F[3+ie]<<4,ge=F[5+ie]<<4,H=ce-w+1>>1,ce=ce+w+1>>1,w=H,H=K*c+Se*o+128>>8,K=K*o-Se*c+128>>8,Se=H,H=_e-ge+1>>1,_e=_e+ge+1>>1,ge=H,H=ue+me+1>>1,me=ue-me+1>>1,ue=H,H=ce-Se+1>>1,ce=ce+Se+1>>1,Se=H,H=w-K+1>>1,w=w+K+1>>1,K=H,H=_e*s+ue*n+2048>>12,_e=_e*n-ue*s+2048>>12,ue=H,H=me*r+ge*i+2048>>12,me=me*i-ge*r+2048>>12,ge=H,F[0+ie]=ce+ue,F[7+ie]=ce-ue,F[1+ie]=w+ge,F[6+ie]=w-ge,F[2+ie]=K+me,F[5+ie]=K-me,F[3+ie]=Se+_e,F[4+ie]=Se-_e}for(ke=0;ke<8;++ke){var fe=ke;if(F[8+fe]==0&&F[16+fe]==0&&F[24+fe]==0&&F[32+fe]==0&&F[40+fe]==0&&F[48+fe]==0&&F[56+fe]==0){H=u*de[ke+0]+8192>>14,F[0+fe]=H,F[8+fe]=H,F[16+fe]=H,F[24+fe]=H,F[32+fe]=H,F[40+fe]=H,F[48+fe]=H,F[56+fe]=H;continue}ce=u*F[0+fe]+2048>>12,w=u*F[32+fe]+2048>>12,K=F[16+fe],Se=F[48+fe],_e=h*(F[8+fe]-F[56+fe])+2048>>12,ue=h*(F[8+fe]+F[56+fe])+2048>>12,me=F[24+fe],ge=F[40+fe],H=ce-w+1>>1,ce=ce+w+1>>1,w=H,H=K*c+Se*o+2048>>12,K=K*o-Se*c+2048>>12,Se=H,H=_e-ge+1>>1,_e=_e+ge+1>>1,ge=H,H=ue+me+1>>1,me=ue-me+1>>1,ue=H,H=ce-Se+1>>1,ce=ce+Se+1>>1,Se=H,H=w-K+1>>1,w=w+K+1>>1,K=H,H=_e*s+ue*n+2048>>12,_e=_e*n-ue*s+2048>>12,ue=H,H=me*r+ge*i+2048>>12,me=me*i-ge*r+2048>>12,ge=H,F[0+fe]=ce+ue,F[56+fe]=ce-ue,F[8+fe]=w+ge,F[48+fe]=w-ge,F[16+fe]=K+me,F[40+fe]=K-me,F[24+fe]=Se+_e,F[32+fe]=Se-_e}for(ke=0;ke<64;++ke){var ot=128+(F[ke]+8>>4);we[ke]=ot<0?0:ot>255?255:ot}}O(A*J*8);for(var D,X,j=0;j<J;j++){var se=j<<3;for(D=0;D<8;D++)P.push(new Uint8Array(A));for(var M=0;M<S;M++){L(C.blocks[j][M],k,z);var $=0,Y=M<<3;for(X=0;X<8;X++){var Q=P[se+X];for(D=0;D<8;D++)Q[Y+D]=k[$++]}}}return P}function g(B){return B<0?0:B>255?255:B}l.prototype={load:function(C){var P=new XMLHttpRequest;P.open("GET",C,!0),P.responseType="arraybuffer",P.onload=function(){var S=new Uint8Array(P.response||P.mozResponseArrayBuffer);this.parse(S),this.onload&&this.onload()}.bind(this),P.send(null)},parse:function(C){var P=this.opts.maxResolutionInMP*1e3*1e3,S=0,J=C.length;function A(){var te=C[S]<<8|C[S+1];return S+=2,te}function z(){var te=A(),be=C.subarray(S,S+te-2);return S+=be.length,be}function k(te){var be=1,Me=1,Ce,at;for(at in te.components)te.components.hasOwnProperty(at)&&(Ce=te.components[at],be<Ce.h&&(be=Ce.h),Me<Ce.v&&(Me=Ce.v));var gt=Math.ceil(te.samplesPerLine/8/be),Dt=Math.ceil(te.scanLines/8/Me);for(at in te.components)if(te.components.hasOwnProperty(at)){Ce=te.components[at];var Wt=Math.ceil(Math.ceil(te.samplesPerLine/8)*Ce.h/be),Yt=Math.ceil(Math.ceil(te.scanLines/8)*Ce.v/Me),si=gt*Ce.h,oi=Dt*Ce.v,Bi=oi*si,ai=[];O(Bi*256);for(var li=0;li<oi;li++){for(var ci=[],ui=0;ui<si;ui++)ci.push(new Int32Array(64));ai.push(ci)}Ce.blocksPerLine=Wt,Ce.blocksPerColumn=Yt,Ce.blocks=ai}te.maxH=be,te.maxV=Me,te.mcusPerLine=gt,te.mcusPerColumn=Dt}var L=null,D=null,X=null,j,se,M=[],$=[],Y=[],Q=[],V=A(),we=-1;if(this.comments=[],V!=65496)throw new Error("SOI not found");for(V=A();V!=65497;){var de,le,ce;switch(V){case 65280:break;case 65504:case 65505:case 65506:case 65507:case 65508:case 65509:case 65510:case 65511:case 65512:case 65513:case 65514:case 65515:case 65516:case 65517:case 65518:case 65519:case 65534:var w=z();if(V===65534){var K=String.fromCharCode.apply(null,w);this.comments.push(K)}V===65504&&w[0]===74&&w[1]===70&&w[2]===73&&w[3]===70&&w[4]===0&&(L={version:{major:w[5],minor:w[6]},densityUnits:w[7],xDensity:w[8]<<8|w[9],yDensity:w[10]<<8|w[11],thumbWidth:w[12],thumbHeight:w[13],thumbData:w.subarray(14,14+3*w[12]*w[13])}),V===65505&&w[0]===69&&w[1]===120&&w[2]===105&&w[3]===102&&w[4]===0&&(this.exifBuffer=w.subarray(5,w.length)),V===65518&&w[0]===65&&w[1]===100&&w[2]===111&&w[3]===98&&w[4]===101&&w[5]===0&&(D={version:w[6],flags0:w[7]<<8|w[8],flags1:w[9]<<8|w[10],transformCode:w[11]});break;case 65499:for(var Se=A(),_e=Se+S-2;S<_e;){var me=C[S++];O(256);var ge=new Int32Array(64);if(me>>4)if(me>>4===1)for(le=0;le<64;le++){var ue=e[le];ge[ue]=A()}else throw new Error("DQT: invalid table spec");else for(le=0;le<64;le++){var ue=e[le];ge[ue]=C[S++]}M[me&15]=ge}break;case 65472:case 65473:case 65474:A(),j={},j.extended=V===65473,j.progressive=V===65474,j.precision=C[S++],j.scanLines=A(),j.samplesPerLine=A(),j.components={},j.componentsOrder=[];var H=j.scanLines*j.samplesPerLine;if(H>P){var F=Math.ceil((H-P)/1e6);throw new Error(`maxResolutionInMP limit exceeded by ${F}MP`)}var ke=C[S++],ie,fe=0,ot=0;for(de=0;de<ke;de++){ie=C[S];var ct=C[S+1]>>4,b=C[S+1]&15,W=C[S+2];if(ct<=0||b<=0)throw new Error("Invalid sampling factor, expected values above 0");j.componentsOrder.push(ie),j.components[ie]={h:ct,v:b,quantizationIdx:W},S+=3}k(j),$.push(j);break;case 65476:var ee=A();for(de=2;de<ee;){var Z=C[S++],oe=new Uint8Array(16),ae=0;for(le=0;le<16;le++,S++)ae+=oe[le]=C[S];O(16+ae);var xe=new Uint8Array(ae);for(le=0;le<ae;le++,S++)xe[le]=C[S];de+=17+ae,(Z>>4?Y:Q)[Z&15]=d(oe,xe)}break;case 65501:A(),se=A();break;case 65500:A(),A();break;case 65498:var ye=A(),Be=C[S++],Oe=[],N;for(de=0;de<Be;de++){N=j.components[C[S++]];var G=C[S++];N.huffmanTableDC=Q[G>>4],N.huffmanTableAC=Y[G&15],Oe.push(N)}var re=C[S++],Te=C[S++],ne=C[S++],pe=m(C,S,j,Oe,se,re,Te,ne>>4,ne&15,this.opts);S+=pe;break;case 65535:C[S]!==255&&S--;break;default:if(C[S-3]==255&&C[S-2]>=192&&C[S-2]<=254){S-=3;break}else if(V===224||V==225){if(we!==-1)throw new Error(`first unknown JPEG marker at offset ${we.toString(16)}, second unknown JPEG marker ${V.toString(16)} at offset ${(S-1).toString(16)}`);we=S-1;let te=A();if(C[S+te-2]===255){S+=te-2;break}}throw new Error("unknown JPEG marker "+V.toString(16))}V=A()}if($.length!=1)throw new Error("only single frame JPEGs supported");for(var de=0;de<$.length;de++){var Ee=$[de].components;for(var le in Ee)Ee[le].quantizationTable=M[Ee[le].quantizationIdx],delete Ee[le].quantizationIdx}this.width=j.samplesPerLine,this.height=j.scanLines,this.jfif=L,this.adobe=D,this.components=[];for(var de=0;de<j.componentsOrder.length;de++){var N=j.components[j.componentsOrder[de]];this.components.push({lines:v(j,N),scaleX:N.h/j.maxH,scaleY:N.v/j.maxV})}},getData:function(C,P){var S=this.width/C,J=this.height/P,A,z,k,L,D,X,j,se,M,$,Y=0,Q,V,we,de,le,ce,w,K,Se,_e,me,ge=C*P*this.components.length;O(ge);var ue=new Uint8Array(ge);switch(this.components.length){case 1:for(A=this.components[0],$=0;$<P;$++)for(D=A.lines[0|$*A.scaleY*J],M=0;M<C;M++)Q=D[0|M*A.scaleX*S],ue[Y++]=Q;break;case 2:for(A=this.components[0],z=this.components[1],$=0;$<P;$++)for(D=A.lines[0|$*A.scaleY*J],X=z.lines[0|$*z.scaleY*J],M=0;M<C;M++)Q=D[0|M*A.scaleX*S],ue[Y++]=Q,Q=X[0|M*z.scaleX*S],ue[Y++]=Q;break;case 3:for(me=!0,this.adobe&&this.adobe.transformCode?me=!0:typeof this.opts.colorTransform!="undefined"&&(me=!!this.opts.colorTransform),A=this.components[0],z=this.components[1],k=this.components[2],$=0;$<P;$++)for(D=A.lines[0|$*A.scaleY*J],X=z.lines[0|$*z.scaleY*J],j=k.lines[0|$*k.scaleY*J],M=0;M<C;M++)me?(Q=D[0|M*A.scaleX*S],V=X[0|M*z.scaleX*S],we=j[0|M*k.scaleX*S],K=g(Q+1.402*(we-128)),Se=g(Q-.3441363*(V-128)-.71413636*(we-128)),_e=g(Q+1.772*(V-128))):(K=D[0|M*A.scaleX*S],Se=X[0|M*z.scaleX*S],_e=j[0|M*k.scaleX*S]),ue[Y++]=K,ue[Y++]=Se,ue[Y++]=_e;break;case 4:if(!this.adobe)throw new Error("Unsupported color mode (4 components)");for(me=!1,this.adobe&&this.adobe.transformCode?me=!0:typeof this.opts.colorTransform!="undefined"&&(me=!!this.opts.colorTransform),A=this.components[0],z=this.components[1],k=this.components[2],L=this.components[3],$=0;$<P;$++)for(D=A.lines[0|$*A.scaleY*J],X=z.lines[0|$*z.scaleY*J],j=k.lines[0|$*k.scaleY*J],se=L.lines[0|$*L.scaleY*J],M=0;M<C;M++)me?(Q=D[0|M*A.scaleX*S],V=X[0|M*z.scaleX*S],we=j[0|M*k.scaleX*S],de=se[0|M*L.scaleX*S],le=255-g(Q+1.402*(we-128)),ce=255-g(Q-.3441363*(V-128)-.71413636*(we-128)),w=255-g(Q+1.772*(V-128))):(le=D[0|M*A.scaleX*S],ce=X[0|M*z.scaleX*S],w=j[0|M*k.scaleX*S],de=se[0|M*L.scaleX*S]),ue[Y++]=255-le,ue[Y++]=255-ce,ue[Y++]=255-w,ue[Y++]=255-de;break;default:throw new Error("Unsupported color mode")}return ue},copyToImageData:function(C,P){var S=C.width,J=C.height,A=C.data,z=this.getData(S,J),k=0,L=0,D,X,j,se,M,$,Y,Q,V;switch(this.components.length){case 1:for(X=0;X<J;X++)for(D=0;D<S;D++)j=z[k++],A[L++]=j,A[L++]=j,A[L++]=j,P&&(A[L++]=255);break;case 3:for(X=0;X<J;X++)for(D=0;D<S;D++)Y=z[k++],Q=z[k++],V=z[k++],A[L++]=Y,A[L++]=Q,A[L++]=V,P&&(A[L++]=255);break;case 4:for(X=0;X<J;X++)for(D=0;D<S;D++)M=z[k++],$=z[k++],j=z[k++],se=z[k++],Y=255-g(M*(1-se/255)+se),Q=255-g($*(1-se/255)+se),V=255-g(j*(1-se/255)+se),A[L++]=Y,A[L++]=Q,A[L++]=V,P&&(A[L++]=255);break;default:throw new Error("Unsupported color mode")}}};var x=0,y=0;function O(B=0){var C=x+B;if(C>y){var P=Math.ceil((C-y)/1024/1024);throw new Error(`maxMemoryUsageInMB limit exceeded by at least ${P}MB`)}x=C}return l.resetMaxMemoryUsage=function(B){x=0,y=B},l.getBytesAllocated=function(){return x},l.requestMemoryAllocation=O,l}();typeof as!="undefined"?as.exports=cl:typeof window!="undefined"&&(window["jpeg-js"]=window["jpeg-js"]||{},window["jpeg-js"].decode=cl);function cl(t,e={}){var i={colorTransform:void 0,useTArray:!1,formatAsRGBA:!0,tolerantDecoding:!0,maxResolutionInMP:100,maxMemoryUsageInMB:512},r={...i,...e},n=new Uint8Array(t),s=new os;s.opts=r,os.resetMaxMemoryUsage(r.maxMemoryUsageInMB*1024*1024),s.parse(n);var o=r.formatAsRGBA?4:3,c=s.width*s.height*o;try{os.requestMemoryAllocation(c);var u={width:s.width,height:s.height,exifBuffer:s.exifBuffer,data:r.useTArray?new Uint8Array(c):Buffer.alloc(c)};s.comments.length>0&&(u.comments=s.comments)}catch(h){throw h instanceof RangeError?new Error("Could not allocate enough memory for the image. Required: "+c):h instanceof ReferenceError&&h.message==="Buffer is not defined"?new Error("Buffer is not globally defined in this environment. Consider setting useTArray to true"):h}return s.copyToImageData(u,r.formatAsRGBA),u}});var hl=E((gx,fl)=>{var jd=ll(),qd=ul();fl.exports={encode:jd,decode:qd}});var dl=E((vx,pl)=>{"use strict";function Jr(){this._types=Object.create(null),this._extensions=Object.create(null);for(let t=0;t<arguments.length;t++)this.define(arguments[t]);this.define=this.define.bind(this),this.getType=this.getType.bind(this),this.getExtension=this.getExtension.bind(this)}Jr.prototype.define=function(t,e){for(let i in t){let r=t[i].map(function(n){return n.toLowerCase()});i=i.toLowerCase();for(let n=0;n<r.length;n++){let s=r[n];if(s[0]!=="*"){if(!e&&s in this._types)throw new Error('Attempt to change mapping for "'+s+'" extension from "'+this._types[s]+'" to "'+i+'". Pass `force=true` to allow this, otherwise remove "'+s+'" from the list of extensions for "'+i+'".');this._types[s]=i}}if(e||!this._extensions[i]){let n=r[0];this._extensions[i]=n[0]!=="*"?n:n.substr(1)}}};Jr.prototype.getType=function(t){t=String(t);let e=t.replace(/^.*[/\\]/,"").toLowerCase(),i=e.replace(/^.*\./,"").toLowerCase(),r=e.length<t.length;return(i.length<e.length-1||!r)&&this._types[i]||null};Jr.prototype.getExtension=function(t){return t=/^\s*([^;\s]*)/.test(t)&&RegExp.$1,t&&this._extensions[t.toLowerCase()]||null};pl.exports=Jr});var gl=E((_x,ml)=>{ml.exports={"application/andrew-inset":["ez"],"application/applixware":["aw"],"application/atom+xml":["atom"],"application/atomcat+xml":["atomcat"],"application/atomdeleted+xml":["atomdeleted"],"application/atomsvc+xml":["atomsvc"],"application/atsc-dwd+xml":["dwd"],"application/atsc-held+xml":["held"],"application/atsc-rsat+xml":["rsat"],"application/bdoc":["bdoc"],"application/calendar+xml":["xcs"],"application/ccxml+xml":["ccxml"],"application/cdfx+xml":["cdfx"],"application/cdmi-capability":["cdmia"],"application/cdmi-container":["cdmic"],"application/cdmi-domain":["cdmid"],"application/cdmi-object":["cdmio"],"application/cdmi-queue":["cdmiq"],"application/cu-seeme":["cu"],"application/dash+xml":["mpd"],"application/davmount+xml":["davmount"],"application/docbook+xml":["dbk"],"application/dssc+der":["dssc"],"application/dssc+xml":["xdssc"],"application/ecmascript":["es","ecma"],"application/emma+xml":["emma"],"application/emotionml+xml":["emotionml"],"application/epub+zip":["epub"],"application/exi":["exi"],"application/express":["exp"],"application/fdt+xml":["fdt"],"application/font-tdpfr":["pfr"],"application/geo+json":["geojson"],"application/gml+xml":["gml"],"application/gpx+xml":["gpx"],"application/gxf":["gxf"],"application/gzip":["gz"],"application/hjson":["hjson"],"application/hyperstudio":["stk"],"application/inkml+xml":["ink","inkml"],"application/ipfix":["ipfix"],"application/its+xml":["its"],"application/java-archive":["jar","war","ear"],"application/java-serialized-object":["ser"],"application/java-vm":["class"],"application/javascript":["js","mjs"],"application/json":["json","map"],"application/json5":["json5"],"application/jsonml+json":["jsonml"],"application/ld+json":["jsonld"],"application/lgr+xml":["lgr"],"application/lost+xml":["lostxml"],"application/mac-binhex40":["hqx"],"application/mac-compactpro":["cpt"],"application/mads+xml":["mads"],"application/manifest+json":["webmanifest"],"application/marc":["mrc"],"application/marcxml+xml":["mrcx"],"application/mathematica":["ma","nb","mb"],"application/mathml+xml":["mathml"],"application/mbox":["mbox"],"application/mediaservercontrol+xml":["mscml"],"application/metalink+xml":["metalink"],"application/metalink4+xml":["meta4"],"application/mets+xml":["mets"],"application/mmt-aei+xml":["maei"],"application/mmt-usd+xml":["musd"],"application/mods+xml":["mods"],"application/mp21":["m21","mp21"],"application/mp4":["mp4s","m4p"],"application/msword":["doc","dot"],"application/mxf":["mxf"],"application/n-quads":["nq"],"application/n-triples":["nt"],"application/node":["cjs"],"application/octet-stream":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"],"application/oda":["oda"],"application/oebps-package+xml":["opf"],"application/ogg":["ogx"],"application/omdoc+xml":["omdoc"],"application/onenote":["onetoc","onetoc2","onetmp","onepkg"],"application/oxps":["oxps"],"application/p2p-overlay+xml":["relo"],"application/patch-ops-error+xml":["xer"],"application/pdf":["pdf"],"application/pgp-encrypted":["pgp"],"application/pgp-signature":["asc","sig"],"application/pics-rules":["prf"],"application/pkcs10":["p10"],"application/pkcs7-mime":["p7m","p7c"],"application/pkcs7-signature":["p7s"],"application/pkcs8":["p8"],"application/pkix-attr-cert":["ac"],"application/pkix-cert":["cer"],"application/pkix-crl":["crl"],"application/pkix-pkipath":["pkipath"],"application/pkixcmp":["pki"],"application/pls+xml":["pls"],"application/postscript":["ai","eps","ps"],"application/provenance+xml":["provx"],"application/pskc+xml":["pskcxml"],"application/raml+yaml":["raml"],"application/rdf+xml":["rdf","owl"],"application/reginfo+xml":["rif"],"application/relax-ng-compact-syntax":["rnc"],"application/resource-lists+xml":["rl"],"application/resource-lists-diff+xml":["rld"],"application/rls-services+xml":["rs"],"application/route-apd+xml":["rapd"],"application/route-s-tsid+xml":["sls"],"application/route-usd+xml":["rusd"],"application/rpki-ghostbusters":["gbr"],"application/rpki-manifest":["mft"],"application/rpki-roa":["roa"],"application/rsd+xml":["rsd"],"application/rss+xml":["rss"],"application/rtf":["rtf"],"application/sbml+xml":["sbml"],"application/scvp-cv-request":["scq"],"application/scvp-cv-response":["scs"],"application/scvp-vp-request":["spq"],"application/scvp-vp-response":["spp"],"application/sdp":["sdp"],"application/senml+xml":["senmlx"],"application/sensml+xml":["sensmlx"],"application/set-payment-initiation":["setpay"],"application/set-registration-initiation":["setreg"],"application/shf+xml":["shf"],"application/sieve":["siv","sieve"],"application/smil+xml":["smi","smil"],"application/sparql-query":["rq"],"application/sparql-results+xml":["srx"],"application/srgs":["gram"],"application/srgs+xml":["grxml"],"application/sru+xml":["sru"],"application/ssdl+xml":["ssdl"],"application/ssml+xml":["ssml"],"application/swid+xml":["swidtag"],"application/tei+xml":["tei","teicorpus"],"application/thraud+xml":["tfi"],"application/timestamped-data":["tsd"],"application/toml":["toml"],"application/trig":["trig"],"application/ttml+xml":["ttml"],"application/ubjson":["ubj"],"application/urc-ressheet+xml":["rsheet"],"application/urc-targetdesc+xml":["td"],"application/voicexml+xml":["vxml"],"application/wasm":["wasm"],"application/widget":["wgt"],"application/winhlp":["hlp"],"application/wsdl+xml":["wsdl"],"application/wspolicy+xml":["wspolicy"],"application/xaml+xml":["xaml"],"application/xcap-att+xml":["xav"],"application/xcap-caps+xml":["xca"],"application/xcap-diff+xml":["xdf"],"application/xcap-el+xml":["xel"],"application/xcap-ns+xml":["xns"],"application/xenc+xml":["xenc"],"application/xhtml+xml":["xhtml","xht"],"application/xliff+xml":["xlf"],"application/xml":["xml","xsl","xsd","rng"],"application/xml-dtd":["dtd"],"application/xop+xml":["xop"],"application/xproc+xml":["xpl"],"application/xslt+xml":["*xsl","xslt"],"application/xspf+xml":["xspf"],"application/xv+xml":["mxml","xhvml","xvml","xvm"],"application/yang":["yang"],"application/yin+xml":["yin"],"application/zip":["zip"],"audio/3gpp":["*3gpp"],"audio/adpcm":["adp"],"audio/amr":["amr"],"audio/basic":["au","snd"],"audio/midi":["mid","midi","kar","rmi"],"audio/mobile-xmf":["mxmf"],"audio/mp3":["*mp3"],"audio/mp4":["m4a","mp4a"],"audio/mpeg":["mpga","mp2","mp2a","mp3","m2a","m3a"],"audio/ogg":["oga","ogg","spx","opus"],"audio/s3m":["s3m"],"audio/silk":["sil"],"audio/wav":["wav"],"audio/wave":["*wav"],"audio/webm":["weba"],"audio/xm":["xm"],"font/collection":["ttc"],"font/otf":["otf"],"font/ttf":["ttf"],"font/woff":["woff"],"font/woff2":["woff2"],"image/aces":["exr"],"image/apng":["apng"],"image/avif":["avif"],"image/bmp":["bmp"],"image/cgm":["cgm"],"image/dicom-rle":["drle"],"image/emf":["emf"],"image/fits":["fits"],"image/g3fax":["g3"],"image/gif":["gif"],"image/heic":["heic"],"image/heic-sequence":["heics"],"image/heif":["heif"],"image/heif-sequence":["heifs"],"image/hej2k":["hej2"],"image/hsj2":["hsj2"],"image/ief":["ief"],"image/jls":["jls"],"image/jp2":["jp2","jpg2"],"image/jpeg":["jpeg","jpg","jpe"],"image/jph":["jph"],"image/jphc":["jhc"],"image/jpm":["jpm"],"image/jpx":["jpx","jpf"],"image/jxr":["jxr"],"image/jxra":["jxra"],"image/jxrs":["jxrs"],"image/jxs":["jxs"],"image/jxsc":["jxsc"],"image/jxsi":["jxsi"],"image/jxss":["jxss"],"image/ktx":["ktx"],"image/ktx2":["ktx2"],"image/png":["png"],"image/sgi":["sgi"],"image/svg+xml":["svg","svgz"],"image/t38":["t38"],"image/tiff":["tif","tiff"],"image/tiff-fx":["tfx"],"image/webp":["webp"],"image/wmf":["wmf"],"message/disposition-notification":["disposition-notification"],"message/global":["u8msg"],"message/global-delivery-status":["u8dsn"],"message/global-disposition-notification":["u8mdn"],"message/global-headers":["u8hdr"],"message/rfc822":["eml","mime"],"model/3mf":["3mf"],"model/gltf+json":["gltf"],"model/gltf-binary":["glb"],"model/iges":["igs","iges"],"model/mesh":["msh","mesh","silo"],"model/mtl":["mtl"],"model/obj":["obj"],"model/step+xml":["stpx"],"model/step+zip":["stpz"],"model/step-xml+zip":["stpxz"],"model/stl":["stl"],"model/vrml":["wrl","vrml"],"model/x3d+binary":["*x3db","x3dbz"],"model/x3d+fastinfoset":["x3db"],"model/x3d+vrml":["*x3dv","x3dvz"],"model/x3d+xml":["x3d","x3dz"],"model/x3d-vrml":["x3dv"],"text/cache-manifest":["appcache","manifest"],"text/calendar":["ics","ifb"],"text/coffeescript":["coffee","litcoffee"],"text/css":["css"],"text/csv":["csv"],"text/html":["html","htm","shtml"],"text/jade":["jade"],"text/jsx":["jsx"],"text/less":["less"],"text/markdown":["markdown","md"],"text/mathml":["mml"],"text/mdx":["mdx"],"text/n3":["n3"],"text/plain":["txt","text","conf","def","list","log","in","ini"],"text/richtext":["rtx"],"text/rtf":["*rtf"],"text/sgml":["sgml","sgm"],"text/shex":["shex"],"text/slim":["slim","slm"],"text/spdx":["spdx"],"text/stylus":["stylus","styl"],"text/tab-separated-values":["tsv"],"text/troff":["t","tr","roff","man","me","ms"],"text/turtle":["ttl"],"text/uri-list":["uri","uris","urls"],"text/vcard":["vcard"],"text/vtt":["vtt"],"text/xml":["*xml"],"text/yaml":["yaml","yml"],"video/3gpp":["3gp","3gpp"],"video/3gpp2":["3g2"],"video/h261":["h261"],"video/h263":["h263"],"video/h264":["h264"],"video/iso.segment":["m4s"],"video/jpeg":["jpgv"],"video/jpm":["*jpm","jpgm"],"video/mj2":["mj2","mjp2"],"video/mp2t":["ts"],"video/mp4":["mp4","mp4v","mpg4"],"video/mpeg":["mpeg","mpg","mpe","m1v","m2v"],"video/ogg":["ogv"],"video/quicktime":["qt","mov"],"video/webm":["webm"]}});var _l=E((xx,vl)=>{vl.exports={"application/prs.cww":["cww"],"application/vnd.1000minds.decision-model+xml":["1km"],"application/vnd.3gpp.pic-bw-large":["plb"],"application/vnd.3gpp.pic-bw-small":["psb"],"application/vnd.3gpp.pic-bw-var":["pvb"],"application/vnd.3gpp2.tcap":["tcap"],"application/vnd.3m.post-it-notes":["pwn"],"application/vnd.accpac.simply.aso":["aso"],"application/vnd.accpac.simply.imp":["imp"],"application/vnd.acucobol":["acu"],"application/vnd.acucorp":["atc","acutc"],"application/vnd.adobe.air-application-installer-package+zip":["air"],"application/vnd.adobe.formscentral.fcdt":["fcdt"],"application/vnd.adobe.fxp":["fxp","fxpl"],"application/vnd.adobe.xdp+xml":["xdp"],"application/vnd.adobe.xfdf":["xfdf"],"application/vnd.ahead.space":["ahead"],"application/vnd.airzip.filesecure.azf":["azf"],"application/vnd.airzip.filesecure.azs":["azs"],"application/vnd.amazon.ebook":["azw"],"application/vnd.americandynamics.acc":["acc"],"application/vnd.amiga.ami":["ami"],"application/vnd.android.package-archive":["apk"],"application/vnd.anser-web-certificate-issue-initiation":["cii"],"application/vnd.anser-web-funds-transfer-initiation":["fti"],"application/vnd.antix.game-component":["atx"],"application/vnd.apple.installer+xml":["mpkg"],"application/vnd.apple.keynote":["key"],"application/vnd.apple.mpegurl":["m3u8"],"application/vnd.apple.numbers":["numbers"],"application/vnd.apple.pages":["pages"],"application/vnd.apple.pkpass":["pkpass"],"application/vnd.aristanetworks.swi":["swi"],"application/vnd.astraea-software.iota":["iota"],"application/vnd.audiograph":["aep"],"application/vnd.balsamiq.bmml+xml":["bmml"],"application/vnd.blueice.multipass":["mpm"],"application/vnd.bmi":["bmi"],"application/vnd.businessobjects":["rep"],"application/vnd.chemdraw+xml":["cdxml"],"application/vnd.chipnuts.karaoke-mmd":["mmd"],"application/vnd.cinderella":["cdy"],"application/vnd.citationstyles.style+xml":["csl"],"application/vnd.claymore":["cla"],"application/vnd.cloanto.rp9":["rp9"],"application/vnd.clonk.c4group":["c4g","c4d","c4f","c4p","c4u"],"application/vnd.cluetrust.cartomobile-config":["c11amc"],"application/vnd.cluetrust.cartomobile-config-pkg":["c11amz"],"application/vnd.commonspace":["csp"],"application/vnd.contact.cmsg":["cdbcmsg"],"application/vnd.cosmocaller":["cmc"],"application/vnd.crick.clicker":["clkx"],"application/vnd.crick.clicker.keyboard":["clkk"],"application/vnd.crick.clicker.palette":["clkp"],"application/vnd.crick.clicker.template":["clkt"],"application/vnd.crick.clicker.wordbank":["clkw"],"application/vnd.criticaltools.wbs+xml":["wbs"],"application/vnd.ctc-posml":["pml"],"application/vnd.cups-ppd":["ppd"],"application/vnd.curl.car":["car"],"application/vnd.curl.pcurl":["pcurl"],"application/vnd.dart":["dart"],"application/vnd.data-vision.rdz":["rdz"],"application/vnd.dbf":["dbf"],"application/vnd.dece.data":["uvf","uvvf","uvd","uvvd"],"application/vnd.dece.ttml+xml":["uvt","uvvt"],"application/vnd.dece.unspecified":["uvx","uvvx"],"application/vnd.dece.zip":["uvz","uvvz"],"application/vnd.denovo.fcselayout-link":["fe_launch"],"application/vnd.dna":["dna"],"application/vnd.dolby.mlp":["mlp"],"application/vnd.dpgraph":["dpg"],"application/vnd.dreamfactory":["dfac"],"application/vnd.ds-keypoint":["kpxx"],"application/vnd.dvb.ait":["ait"],"application/vnd.dvb.service":["svc"],"application/vnd.dynageo":["geo"],"application/vnd.ecowin.chart":["mag"],"application/vnd.enliven":["nml"],"application/vnd.epson.esf":["esf"],"application/vnd.epson.msf":["msf"],"application/vnd.epson.quickanime":["qam"],"application/vnd.epson.salt":["slt"],"application/vnd.epson.ssf":["ssf"],"application/vnd.eszigno3+xml":["es3","et3"],"application/vnd.ezpix-album":["ez2"],"application/vnd.ezpix-package":["ez3"],"application/vnd.fdf":["fdf"],"application/vnd.fdsn.mseed":["mseed"],"application/vnd.fdsn.seed":["seed","dataless"],"application/vnd.flographit":["gph"],"application/vnd.fluxtime.clip":["ftc"],"application/vnd.framemaker":["fm","frame","maker","book"],"application/vnd.frogans.fnc":["fnc"],"application/vnd.frogans.ltf":["ltf"],"application/vnd.fsc.weblaunch":["fsc"],"application/vnd.fujitsu.oasys":["oas"],"application/vnd.fujitsu.oasys2":["oa2"],"application/vnd.fujitsu.oasys3":["oa3"],"application/vnd.fujitsu.oasysgp":["fg5"],"application/vnd.fujitsu.oasysprs":["bh2"],"application/vnd.fujixerox.ddd":["ddd"],"application/vnd.fujixerox.docuworks":["xdw"],"application/vnd.fujixerox.docuworks.binder":["xbd"],"application/vnd.fuzzysheet":["fzs"],"application/vnd.genomatix.tuxedo":["txd"],"application/vnd.geogebra.file":["ggb"],"application/vnd.geogebra.tool":["ggt"],"application/vnd.geometry-explorer":["gex","gre"],"application/vnd.geonext":["gxt"],"application/vnd.geoplan":["g2w"],"application/vnd.geospace":["g3w"],"application/vnd.gmx":["gmx"],"application/vnd.google-apps.document":["gdoc"],"application/vnd.google-apps.presentation":["gslides"],"application/vnd.google-apps.spreadsheet":["gsheet"],"application/vnd.google-earth.kml+xml":["kml"],"application/vnd.google-earth.kmz":["kmz"],"application/vnd.grafeq":["gqf","gqs"],"application/vnd.groove-account":["gac"],"application/vnd.groove-help":["ghf"],"application/vnd.groove-identity-message":["gim"],"application/vnd.groove-injector":["grv"],"application/vnd.groove-tool-message":["gtm"],"application/vnd.groove-tool-template":["tpl"],"application/vnd.groove-vcard":["vcg"],"application/vnd.hal+xml":["hal"],"application/vnd.handheld-entertainment+xml":["zmm"],"application/vnd.hbci":["hbci"],"application/vnd.hhe.lesson-player":["les"],"application/vnd.hp-hpgl":["hpgl"],"application/vnd.hp-hpid":["hpid"],"application/vnd.hp-hps":["hps"],"application/vnd.hp-jlyt":["jlt"],"application/vnd.hp-pcl":["pcl"],"application/vnd.hp-pclxl":["pclxl"],"application/vnd.hydrostatix.sof-data":["sfd-hdstx"],"application/vnd.ibm.minipay":["mpy"],"application/vnd.ibm.modcap":["afp","listafp","list3820"],"application/vnd.ibm.rights-management":["irm"],"application/vnd.ibm.secure-container":["sc"],"application/vnd.iccprofile":["icc","icm"],"application/vnd.igloader":["igl"],"application/vnd.immervision-ivp":["ivp"],"application/vnd.immervision-ivu":["ivu"],"application/vnd.insors.igm":["igm"],"application/vnd.intercon.formnet":["xpw","xpx"],"application/vnd.intergeo":["i2g"],"application/vnd.intu.qbo":["qbo"],"application/vnd.intu.qfx":["qfx"],"application/vnd.ipunplugged.rcprofile":["rcprofile"],"application/vnd.irepository.package+xml":["irp"],"application/vnd.is-xpr":["xpr"],"application/vnd.isac.fcs":["fcs"],"application/vnd.jam":["jam"],"application/vnd.jcp.javame.midlet-rms":["rms"],"application/vnd.jisp":["jisp"],"application/vnd.joost.joda-archive":["joda"],"application/vnd.kahootz":["ktz","ktr"],"application/vnd.kde.karbon":["karbon"],"application/vnd.kde.kchart":["chrt"],"application/vnd.kde.kformula":["kfo"],"application/vnd.kde.kivio":["flw"],"application/vnd.kde.kontour":["kon"],"application/vnd.kde.kpresenter":["kpr","kpt"],"application/vnd.kde.kspread":["ksp"],"application/vnd.kde.kword":["kwd","kwt"],"application/vnd.kenameaapp":["htke"],"application/vnd.kidspiration":["kia"],"application/vnd.kinar":["kne","knp"],"application/vnd.koan":["skp","skd","skt","skm"],"application/vnd.kodak-descriptor":["sse"],"application/vnd.las.las+xml":["lasxml"],"application/vnd.llamagraphics.life-balance.desktop":["lbd"],"application/vnd.llamagraphics.life-balance.exchange+xml":["lbe"],"application/vnd.lotus-1-2-3":["123"],"application/vnd.lotus-approach":["apr"],"application/vnd.lotus-freelance":["pre"],"application/vnd.lotus-notes":["nsf"],"application/vnd.lotus-organizer":["org"],"application/vnd.lotus-screencam":["scm"],"application/vnd.lotus-wordpro":["lwp"],"application/vnd.macports.portpkg":["portpkg"],"application/vnd.mapbox-vector-tile":["mvt"],"application/vnd.mcd":["mcd"],"application/vnd.medcalcdata":["mc1"],"application/vnd.mediastation.cdkey":["cdkey"],"application/vnd.mfer":["mwf"],"application/vnd.mfmp":["mfm"],"application/vnd.micrografx.flo":["flo"],"application/vnd.micrografx.igx":["igx"],"application/vnd.mif":["mif"],"application/vnd.mobius.daf":["daf"],"application/vnd.mobius.dis":["dis"],"application/vnd.mobius.mbk":["mbk"],"application/vnd.mobius.mqy":["mqy"],"application/vnd.mobius.msl":["msl"],"application/vnd.mobius.plc":["plc"],"application/vnd.mobius.txf":["txf"],"application/vnd.mophun.application":["mpn"],"application/vnd.mophun.certificate":["mpc"],"application/vnd.mozilla.xul+xml":["xul"],"application/vnd.ms-artgalry":["cil"],"application/vnd.ms-cab-compressed":["cab"],"application/vnd.ms-excel":["xls","xlm","xla","xlc","xlt","xlw"],"application/vnd.ms-excel.addin.macroenabled.12":["xlam"],"application/vnd.ms-excel.sheet.binary.macroenabled.12":["xlsb"],"application/vnd.ms-excel.sheet.macroenabled.12":["xlsm"],"application/vnd.ms-excel.template.macroenabled.12":["xltm"],"application/vnd.ms-fontobject":["eot"],"application/vnd.ms-htmlhelp":["chm"],"application/vnd.ms-ims":["ims"],"application/vnd.ms-lrm":["lrm"],"application/vnd.ms-officetheme":["thmx"],"application/vnd.ms-outlook":["msg"],"application/vnd.ms-pki.seccat":["cat"],"application/vnd.ms-pki.stl":["*stl"],"application/vnd.ms-powerpoint":["ppt","pps","pot"],"application/vnd.ms-powerpoint.addin.macroenabled.12":["ppam"],"application/vnd.ms-powerpoint.presentation.macroenabled.12":["pptm"],"application/vnd.ms-powerpoint.slide.macroenabled.12":["sldm"],"application/vnd.ms-powerpoint.slideshow.macroenabled.12":["ppsm"],"application/vnd.ms-powerpoint.template.macroenabled.12":["potm"],"application/vnd.ms-project":["mpp","mpt"],"application/vnd.ms-word.document.macroenabled.12":["docm"],"application/vnd.ms-word.template.macroenabled.12":["dotm"],"application/vnd.ms-works":["wps","wks","wcm","wdb"],"application/vnd.ms-wpl":["wpl"],"application/vnd.ms-xpsdocument":["xps"],"application/vnd.mseq":["mseq"],"application/vnd.musician":["mus"],"application/vnd.muvee.style":["msty"],"application/vnd.mynfc":["taglet"],"application/vnd.neurolanguage.nlu":["nlu"],"application/vnd.nitf":["ntf","nitf"],"application/vnd.noblenet-directory":["nnd"],"application/vnd.noblenet-sealer":["nns"],"application/vnd.noblenet-web":["nnw"],"application/vnd.nokia.n-gage.ac+xml":["*ac"],"application/vnd.nokia.n-gage.data":["ngdat"],"application/vnd.nokia.n-gage.symbian.install":["n-gage"],"application/vnd.nokia.radio-preset":["rpst"],"application/vnd.nokia.radio-presets":["rpss"],"application/vnd.novadigm.edm":["edm"],"application/vnd.novadigm.edx":["edx"],"application/vnd.novadigm.ext":["ext"],"application/vnd.oasis.opendocument.chart":["odc"],"application/vnd.oasis.opendocument.chart-template":["otc"],"application/vnd.oasis.opendocument.database":["odb"],"application/vnd.oasis.opendocument.formula":["odf"],"application/vnd.oasis.opendocument.formula-template":["odft"],"application/vnd.oasis.opendocument.graphics":["odg"],"application/vnd.oasis.opendocument.graphics-template":["otg"],"application/vnd.oasis.opendocument.image":["odi"],"application/vnd.oasis.opendocument.image-template":["oti"],"application/vnd.oasis.opendocument.presentation":["odp"],"application/vnd.oasis.opendocument.presentation-template":["otp"],"application/vnd.oasis.opendocument.spreadsheet":["ods"],"application/vnd.oasis.opendocument.spreadsheet-template":["ots"],"application/vnd.oasis.opendocument.text":["odt"],"application/vnd.oasis.opendocument.text-master":["odm"],"application/vnd.oasis.opendocument.text-template":["ott"],"application/vnd.oasis.opendocument.text-web":["oth"],"application/vnd.olpc-sugar":["xo"],"application/vnd.oma.dd2+xml":["dd2"],"application/vnd.openblox.game+xml":["obgx"],"application/vnd.openofficeorg.extension":["oxt"],"application/vnd.openstreetmap.data+xml":["osm"],"application/vnd.openxmlformats-officedocument.presentationml.presentation":["pptx"],"application/vnd.openxmlformats-officedocument.presentationml.slide":["sldx"],"application/vnd.openxmlformats-officedocument.presentationml.slideshow":["ppsx"],"application/vnd.openxmlformats-officedocument.presentationml.template":["potx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":["xlsx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.template":["xltx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":["docx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.template":["dotx"],"application/vnd.osgeo.mapguide.package":["mgp"],"application/vnd.osgi.dp":["dp"],"application/vnd.osgi.subsystem":["esa"],"application/vnd.palm":["pdb","pqa","oprc"],"application/vnd.pawaafile":["paw"],"application/vnd.pg.format":["str"],"application/vnd.pg.osasli":["ei6"],"application/vnd.picsel":["efif"],"application/vnd.pmi.widget":["wg"],"application/vnd.pocketlearn":["plf"],"application/vnd.powerbuilder6":["pbd"],"application/vnd.previewsystems.box":["box"],"application/vnd.proteus.magazine":["mgz"],"application/vnd.publishare-delta-tree":["qps"],"application/vnd.pvi.ptid1":["ptid"],"application/vnd.quark.quarkxpress":["qxd","qxt","qwd","qwt","qxl","qxb"],"application/vnd.rar":["rar"],"application/vnd.realvnc.bed":["bed"],"application/vnd.recordare.musicxml":["mxl"],"application/vnd.recordare.musicxml+xml":["musicxml"],"application/vnd.rig.cryptonote":["cryptonote"],"application/vnd.rim.cod":["cod"],"application/vnd.rn-realmedia":["rm"],"application/vnd.rn-realmedia-vbr":["rmvb"],"application/vnd.route66.link66+xml":["link66"],"application/vnd.sailingtracker.track":["st"],"application/vnd.seemail":["see"],"application/vnd.sema":["sema"],"application/vnd.semd":["semd"],"application/vnd.semf":["semf"],"application/vnd.shana.informed.formdata":["ifm"],"application/vnd.shana.informed.formtemplate":["itp"],"application/vnd.shana.informed.interchange":["iif"],"application/vnd.shana.informed.package":["ipk"],"application/vnd.simtech-mindmapper":["twd","twds"],"application/vnd.smaf":["mmf"],"application/vnd.smart.teacher":["teacher"],"application/vnd.software602.filler.form+xml":["fo"],"application/vnd.solent.sdkm+xml":["sdkm","sdkd"],"application/vnd.spotfire.dxp":["dxp"],"application/vnd.spotfire.sfs":["sfs"],"application/vnd.stardivision.calc":["sdc"],"application/vnd.stardivision.draw":["sda"],"application/vnd.stardivision.impress":["sdd"],"application/vnd.stardivision.math":["smf"],"application/vnd.stardivision.writer":["sdw","vor"],"application/vnd.stardivision.writer-global":["sgl"],"application/vnd.stepmania.package":["smzip"],"application/vnd.stepmania.stepchart":["sm"],"application/vnd.sun.wadl+xml":["wadl"],"application/vnd.sun.xml.calc":["sxc"],"application/vnd.sun.xml.calc.template":["stc"],"application/vnd.sun.xml.draw":["sxd"],"application/vnd.sun.xml.draw.template":["std"],"application/vnd.sun.xml.impress":["sxi"],"application/vnd.sun.xml.impress.template":["sti"],"application/vnd.sun.xml.math":["sxm"],"application/vnd.sun.xml.writer":["sxw"],"application/vnd.sun.xml.writer.global":["sxg"],"application/vnd.sun.xml.writer.template":["stw"],"application/vnd.sus-calendar":["sus","susp"],"application/vnd.svd":["svd"],"application/vnd.symbian.install":["sis","sisx"],"application/vnd.syncml+xml":["xsm"],"application/vnd.syncml.dm+wbxml":["bdm"],"application/vnd.syncml.dm+xml":["xdm"],"application/vnd.syncml.dmddf+xml":["ddf"],"application/vnd.tao.intent-module-archive":["tao"],"application/vnd.tcpdump.pcap":["pcap","cap","dmp"],"application/vnd.tmobile-livetv":["tmo"],"application/vnd.trid.tpt":["tpt"],"application/vnd.triscape.mxs":["mxs"],"application/vnd.trueapp":["tra"],"application/vnd.ufdl":["ufd","ufdl"],"application/vnd.uiq.theme":["utz"],"application/vnd.umajin":["umj"],"application/vnd.unity":["unityweb"],"application/vnd.uoml+xml":["uoml"],"application/vnd.vcx":["vcx"],"application/vnd.visio":["vsd","vst","vss","vsw"],"application/vnd.visionary":["vis"],"application/vnd.vsf":["vsf"],"application/vnd.wap.wbxml":["wbxml"],"application/vnd.wap.wmlc":["wmlc"],"application/vnd.wap.wmlscriptc":["wmlsc"],"application/vnd.webturbo":["wtb"],"application/vnd.wolfram.player":["nbp"],"application/vnd.wordperfect":["wpd"],"application/vnd.wqd":["wqd"],"application/vnd.wt.stf":["stf"],"application/vnd.xara":["xar"],"application/vnd.xfdl":["xfdl"],"application/vnd.yamaha.hv-dic":["hvd"],"application/vnd.yamaha.hv-script":["hvs"],"application/vnd.yamaha.hv-voice":["hvp"],"application/vnd.yamaha.openscoreformat":["osf"],"application/vnd.yamaha.openscoreformat.osfpvg+xml":["osfpvg"],"application/vnd.yamaha.smaf-audio":["saf"],"application/vnd.yamaha.smaf-phrase":["spf"],"application/vnd.yellowriver-custom-menu":["cmp"],"application/vnd.zul":["zir","zirz"],"application/vnd.zzazz.deck+xml":["zaz"],"application/x-7z-compressed":["7z"],"application/x-abiword":["abw"],"application/x-ace-compressed":["ace"],"application/x-apple-diskimage":["*dmg"],"application/x-arj":["arj"],"application/x-authorware-bin":["aab","x32","u32","vox"],"application/x-authorware-map":["aam"],"application/x-authorware-seg":["aas"],"application/x-bcpio":["bcpio"],"application/x-bdoc":["*bdoc"],"application/x-bittorrent":["torrent"],"application/x-blorb":["blb","blorb"],"application/x-bzip":["bz"],"application/x-bzip2":["bz2","boz"],"application/x-cbr":["cbr","cba","cbt","cbz","cb7"],"application/x-cdlink":["vcd"],"application/x-cfs-compressed":["cfs"],"application/x-chat":["chat"],"application/x-chess-pgn":["pgn"],"application/x-chrome-extension":["crx"],"application/x-cocoa":["cco"],"application/x-conference":["nsc"],"application/x-cpio":["cpio"],"application/x-csh":["csh"],"application/x-debian-package":["*deb","udeb"],"application/x-dgc-compressed":["dgc"],"application/x-director":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"],"application/x-doom":["wad"],"application/x-dtbncx+xml":["ncx"],"application/x-dtbook+xml":["dtb"],"application/x-dtbresource+xml":["res"],"application/x-dvi":["dvi"],"application/x-envoy":["evy"],"application/x-eva":["eva"],"application/x-font-bdf":["bdf"],"application/x-font-ghostscript":["gsf"],"application/x-font-linux-psf":["psf"],"application/x-font-pcf":["pcf"],"application/x-font-snf":["snf"],"application/x-font-type1":["pfa","pfb","pfm","afm"],"application/x-freearc":["arc"],"application/x-futuresplash":["spl"],"application/x-gca-compressed":["gca"],"application/x-glulx":["ulx"],"application/x-gnumeric":["gnumeric"],"application/x-gramps-xml":["gramps"],"application/x-gtar":["gtar"],"application/x-hdf":["hdf"],"application/x-httpd-php":["php"],"application/x-install-instructions":["install"],"application/x-iso9660-image":["*iso"],"application/x-iwork-keynote-sffkey":["*key"],"application/x-iwork-numbers-sffnumbers":["*numbers"],"application/x-iwork-pages-sffpages":["*pages"],"application/x-java-archive-diff":["jardiff"],"application/x-java-jnlp-file":["jnlp"],"application/x-keepass2":["kdbx"],"application/x-latex":["latex"],"application/x-lua-bytecode":["luac"],"application/x-lzh-compressed":["lzh","lha"],"application/x-makeself":["run"],"application/x-mie":["mie"],"application/x-mobipocket-ebook":["prc","mobi"],"application/x-ms-application":["application"],"application/x-ms-shortcut":["lnk"],"application/x-ms-wmd":["wmd"],"application/x-ms-wmz":["wmz"],"application/x-ms-xbap":["xbap"],"application/x-msaccess":["mdb"],"application/x-msbinder":["obd"],"application/x-mscardfile":["crd"],"application/x-msclip":["clp"],"application/x-msdos-program":["*exe"],"application/x-msdownload":["*exe","*dll","com","bat","*msi"],"application/x-msmediaview":["mvb","m13","m14"],"application/x-msmetafile":["*wmf","*wmz","*emf","emz"],"application/x-msmoney":["mny"],"application/x-mspublisher":["pub"],"application/x-msschedule":["scd"],"application/x-msterminal":["trm"],"application/x-mswrite":["wri"],"application/x-netcdf":["nc","cdf"],"application/x-ns-proxy-autoconfig":["pac"],"application/x-nzb":["nzb"],"application/x-perl":["pl","pm"],"application/x-pilot":["*prc","*pdb"],"application/x-pkcs12":["p12","pfx"],"application/x-pkcs7-certificates":["p7b","spc"],"application/x-pkcs7-certreqresp":["p7r"],"application/x-rar-compressed":["*rar"],"application/x-redhat-package-manager":["rpm"],"application/x-research-info-systems":["ris"],"application/x-sea":["sea"],"application/x-sh":["sh"],"application/x-shar":["shar"],"application/x-shockwave-flash":["swf"],"application/x-silverlight-app":["xap"],"application/x-sql":["sql"],"application/x-stuffit":["sit"],"application/x-stuffitx":["sitx"],"application/x-subrip":["srt"],"application/x-sv4cpio":["sv4cpio"],"application/x-sv4crc":["sv4crc"],"application/x-t3vm-image":["t3"],"application/x-tads":["gam"],"application/x-tar":["tar"],"application/x-tcl":["tcl","tk"],"application/x-tex":["tex"],"application/x-tex-tfm":["tfm"],"application/x-texinfo":["texinfo","texi"],"application/x-tgif":["*obj"],"application/x-ustar":["ustar"],"application/x-virtualbox-hdd":["hdd"],"application/x-virtualbox-ova":["ova"],"application/x-virtualbox-ovf":["ovf"],"application/x-virtualbox-vbox":["vbox"],"application/x-virtualbox-vbox-extpack":["vbox-extpack"],"application/x-virtualbox-vdi":["vdi"],"application/x-virtualbox-vhd":["vhd"],"application/x-virtualbox-vmdk":["vmdk"],"application/x-wais-source":["src"],"application/x-web-app-manifest+json":["webapp"],"application/x-x509-ca-cert":["der","crt","pem"],"application/x-xfig":["fig"],"application/x-xliff+xml":["*xlf"],"application/x-xpinstall":["xpi"],"application/x-xz":["xz"],"application/x-zmachine":["z1","z2","z3","z4","z5","z6","z7","z8"],"audio/vnd.dece.audio":["uva","uvva"],"audio/vnd.digital-winds":["eol"],"audio/vnd.dra":["dra"],"audio/vnd.dts":["dts"],"audio/vnd.dts.hd":["dtshd"],"audio/vnd.lucent.voice":["lvp"],"audio/vnd.ms-playready.media.pya":["pya"],"audio/vnd.nuera.ecelp4800":["ecelp4800"],"audio/vnd.nuera.ecelp7470":["ecelp7470"],"audio/vnd.nuera.ecelp9600":["ecelp9600"],"audio/vnd.rip":["rip"],"audio/x-aac":["aac"],"audio/x-aiff":["aif","aiff","aifc"],"audio/x-caf":["caf"],"audio/x-flac":["flac"],"audio/x-m4a":["*m4a"],"audio/x-matroska":["mka"],"audio/x-mpegurl":["m3u"],"audio/x-ms-wax":["wax"],"audio/x-ms-wma":["wma"],"audio/x-pn-realaudio":["ram","ra"],"audio/x-pn-realaudio-plugin":["rmp"],"audio/x-realaudio":["*ra"],"audio/x-wav":["*wav"],"chemical/x-cdx":["cdx"],"chemical/x-cif":["cif"],"chemical/x-cmdf":["cmdf"],"chemical/x-cml":["cml"],"chemical/x-csml":["csml"],"chemical/x-xyz":["xyz"],"image/prs.btif":["btif"],"image/prs.pti":["pti"],"image/vnd.adobe.photoshop":["psd"],"image/vnd.airzip.accelerator.azv":["azv"],"image/vnd.dece.graphic":["uvi","uvvi","uvg","uvvg"],"image/vnd.djvu":["djvu","djv"],"image/vnd.dvb.subtitle":["*sub"],"image/vnd.dwg":["dwg"],"image/vnd.dxf":["dxf"],"image/vnd.fastbidsheet":["fbs"],"image/vnd.fpx":["fpx"],"image/vnd.fst":["fst"],"image/vnd.fujixerox.edmics-mmr":["mmr"],"image/vnd.fujixerox.edmics-rlc":["rlc"],"image/vnd.microsoft.icon":["ico"],"image/vnd.ms-dds":["dds"],"image/vnd.ms-modi":["mdi"],"image/vnd.ms-photo":["wdp"],"image/vnd.net-fpx":["npx"],"image/vnd.pco.b16":["b16"],"image/vnd.tencent.tap":["tap"],"image/vnd.valve.source.texture":["vtf"],"image/vnd.wap.wbmp":["wbmp"],"image/vnd.xiff":["xif"],"image/vnd.zbrush.pcx":["pcx"],"image/x-3ds":["3ds"],"image/x-cmu-raster":["ras"],"image/x-cmx":["cmx"],"image/x-freehand":["fh","fhc","fh4","fh5","fh7"],"image/x-icon":["*ico"],"image/x-jng":["jng"],"image/x-mrsid-image":["sid"],"image/x-ms-bmp":["*bmp"],"image/x-pcx":["*pcx"],"image/x-pict":["pic","pct"],"image/x-portable-anymap":["pnm"],"image/x-portable-bitmap":["pbm"],"image/x-portable-graymap":["pgm"],"image/x-portable-pixmap":["ppm"],"image/x-rgb":["rgb"],"image/x-tga":["tga"],"image/x-xbitmap":["xbm"],"image/x-xpixmap":["xpm"],"image/x-xwindowdump":["xwd"],"message/vnd.wfa.wsc":["wsc"],"model/vnd.collada+xml":["dae"],"model/vnd.dwf":["dwf"],"model/vnd.gdl":["gdl"],"model/vnd.gtw":["gtw"],"model/vnd.mts":["mts"],"model/vnd.opengex":["ogex"],"model/vnd.parasolid.transmit.binary":["x_b"],"model/vnd.parasolid.transmit.text":["x_t"],"model/vnd.sap.vds":["vds"],"model/vnd.usdz+zip":["usdz"],"model/vnd.valve.source.compiled-map":["bsp"],"model/vnd.vtu":["vtu"],"text/prs.lines.tag":["dsc"],"text/vnd.curl":["curl"],"text/vnd.curl.dcurl":["dcurl"],"text/vnd.curl.mcurl":["mcurl"],"text/vnd.curl.scurl":["scurl"],"text/vnd.dvb.subtitle":["sub"],"text/vnd.fly":["fly"],"text/vnd.fmi.flexstor":["flx"],"text/vnd.graphviz":["gv"],"text/vnd.in3d.3dml":["3dml"],"text/vnd.in3d.spot":["spot"],"text/vnd.sun.j2me.app-descriptor":["jad"],"text/vnd.wap.wml":["wml"],"text/vnd.wap.wmlscript":["wmls"],"text/x-asm":["s","asm"],"text/x-c":["c","cc","cxx","cpp","h","hh","dic"],"text/x-component":["htc"],"text/x-fortran":["f","for","f77","f90"],"text/x-handlebars-template":["hbs"],"text/x-java-source":["java"],"text/x-lua":["lua"],"text/x-markdown":["mkd"],"text/x-nfo":["nfo"],"text/x-opml":["opml"],"text/x-org":["*org"],"text/x-pascal":["p","pas"],"text/x-processing":["pde"],"text/x-sass":["sass"],"text/x-scss":["scss"],"text/x-setext":["etx"],"text/x-sfv":["sfv"],"text/x-suse-ymp":["ymp"],"text/x-uuencode":["uu"],"text/x-vcalendar":["vcs"],"text/x-vcard":["vcf"],"video/vnd.dece.hd":["uvh","uvvh"],"video/vnd.dece.mobile":["uvm","uvvm"],"video/vnd.dece.pd":["uvp","uvvp"],"video/vnd.dece.sd":["uvs","uvvs"],"video/vnd.dece.video":["uvv","uvvv"],"video/vnd.dvb.file":["dvb"],"video/vnd.fvt":["fvt"],"video/vnd.mpegurl":["mxu","m4u"],"video/vnd.ms-playready.media.pyv":["pyv"],"video/vnd.uvvu.mp4":["uvu","uvvu"],"video/vnd.vivo":["viv"],"video/x-f4v":["f4v"],"video/x-fli":["fli"],"video/x-flv":["flv"],"video/x-m4v":["m4v"],"video/x-matroska":["mkv","mk3d","mks"],"video/x-mng":["mng"],"video/x-ms-asf":["asf","asx"],"video/x-ms-vob":["vob"],"video/x-ms-wm":["wm"],"video/x-ms-wmv":["wmv"],"video/x-ms-wmx":["wmx"],"video/x-ms-wvx":["wvx"],"video/x-msvideo":["avi"],"video/x-sgi-movie":["movie"],"video/x-smv":["smv"],"x-conference/x-cooltalk":["ice"]}});var yl=E((yx,xl)=>{"use strict";var Vd=dl();xl.exports=new Vd(gl(),_l())});var wl=E((bx,bl)=>{bl.exports=function(t,e){for(var i=[],r=0;r<t.length;r++){var n=e(t[r],r);Hd(n)?i.push.apply(i,n):i.push(n)}return i};var Hd=Array.isArray||function(t){return Object.prototype.toString.call(t)==="[object Array]"}});var Cl=E((wx,Ol)=>{"use strict";Ol.exports=Sl;function Sl(t,e,i){t instanceof RegExp&&(t=El(t,i)),e instanceof RegExp&&(e=El(e,i));var r=kl(t,e,i);return r&&{start:r[0],end:r[1],pre:i.slice(0,r[0]),body:i.slice(r[0]+t.length,r[1]),post:i.slice(r[1]+e.length)}}function El(t,e){var i=e.match(t);return i?i[0]:null}Sl.range=kl;function kl(t,e,i){var r,n,s,o,c,u=i.indexOf(t),h=i.indexOf(e,u+1),l=u;if(u>=0&&h>0){if(t===e)return[u,h];for(r=[],s=i.length;l>=0&&!c;)l==u?(r.push(l),u=i.indexOf(t,l+1)):r.length==1?c=[r.pop(),h]:(n=r.pop(),n<s&&(s=n,o=h),h=i.indexOf(e,l+1)),l=u<h&&u>=0?u:h;r.length&&(c=[s,o])}return c}});var Ll=E((Ex,Nl)=>{var $d=wl(),Tl=Cl();Nl.exports=Wd;var Il="\0SLASH"+Math.random()+"\0",Al="\0OPEN"+Math.random()+"\0",cs="\0CLOSE"+Math.random()+"\0",Bl="\0COMMA"+Math.random()+"\0",Rl="\0PERIOD"+Math.random()+"\0";function ls(t){return parseInt(t,10)==t?parseInt(t,10):t.charCodeAt(0)}function Gd(t){return t.split("\\\\").join(Il).split("\\{").join(Al).split("\\}").join(cs).split("\\,").join(Bl).split("\\.").join(Rl)}function zd(t){return t.split(Il).join("\\").split(Al).join("{").split(cs).join("}").split(Bl).join(",").split(Rl).join(".")}function Pl(t){if(!t)return[""];var e=[],i=Tl("{","}",t);if(!i)return t.split(",");var r=i.pre,n=i.body,s=i.post,o=r.split(",");o[o.length-1]+="{"+n+"}";var c=Pl(s);return s.length&&(o[o.length-1]+=c.shift(),o.push.apply(o,c)),e.push.apply(e,o),e}function Wd(t){return t?(t.substr(0,2)==="{}"&&(t="\\{\\}"+t.substr(2)),Vi(Gd(t),!0).map(zd)):[]}function Yd(t){return"{"+t+"}"}function Kd(t){return/^-?0\d/.test(t)}function Zd(t,e){return t<=e}function Xd(t,e){return t>=e}function Vi(t,e){var i=[],r=Tl("{","}",t);if(!r||/\$$/.test(r.pre))return[t];var n=/^-?\d+\.\.-?\d+(?:\.\.-?\d+)?$/.test(r.body),s=/^[a-zA-Z]\.\.[a-zA-Z](?:\.\.-?\d+)?$/.test(r.body),o=n||s,c=r.body.indexOf(",")>=0;if(!o&&!c)return r.post.match(/,.*\}/)?(t=r.pre+"{"+r.body+cs+r.post,Vi(t)):[t];var u;if(o)u=r.body.split(/\.\./);else if(u=Pl(r.body),u.length===1&&(u=Vi(u[0],!1).map(Yd),u.length===1)){var l=r.post.length?Vi(r.post,!1):[""];return l.map(function(D){return r.pre+u[0]+D})}var h=r.pre,l=r.post.length?Vi(r.post,!1):[""],d;if(o){var m=ls(u[0]),v=ls(u[1]),g=Math.max(u[0].length,u[1].length),x=u.length==3?Math.abs(ls(u[2])):1,y=Zd,O=v<m;O&&(x*=-1,y=Xd);var B=u.some(Kd);d=[];for(var C=m;y(C,v);C+=x){var P;if(s)P=String.fromCharCode(C),P==="\\"&&(P="");else if(P=String(C),B){var S=g-P.length;if(S>0){var J=new Array(S+1).join("0");C<0?P="-"+J+P.slice(1):P=J+P}}d.push(P)}}else d=$d(u,function(L){return Vi(L,!1)});for(var A=0;A<d.length;A++)for(var z=0;z<l.length;z++){var k=h+d[A]+l[z];(!e||o||k)&&i.push(k)}return i}});var ql=E((Sx,jl)=>{jl.exports=pt;pt.Minimatch=Ye;var pr=function(){try{return require("path")}catch{}}()||{sep:"/"};pt.sep=pr.sep;var hs=pt.GLOBSTAR=Ye.GLOBSTAR={},Jd=Ll(),Fl={"!":{open:"(?:(?!(?:",close:"))[^/]*?)"},"?":{open:"(?:",close:")?"},"+":{open:"(?:",close:")+"},"*":{open:"(?:",close:")*"},"@":{open:"(?:",close:")"}},us="[^/]",fs=us+"*?",Qd="(?:(?!(?:\\/|^)(?:\\.{1,2})($|\\/)).)*?",em="(?:(?!(?:\\/|^)\\.).)*?",Ml=tm("().*{}+?[]^$\\!");function tm(t){return t.split("").reduce(function(e,i){return e[i]=!0,e},{})}var Dl=/\/+/;pt.filter=im;function im(t,e){return e=e||{},function(i,r,n){return pt(i,t,e)}}function Zt(t,e){e=e||{};var i={};return Object.keys(t).forEach(function(r){i[r]=t[r]}),Object.keys(e).forEach(function(r){i[r]=e[r]}),i}pt.defaults=function(t){if(!t||typeof t!="object"||!Object.keys(t).length)return pt;var e=pt,i=function(n,s,o){return e(n,s,Zt(t,o))};return i.Minimatch=function(n,s){return new e.Minimatch(n,Zt(t,s))},i.Minimatch.defaults=function(n){return e.defaults(Zt(t,n)).Minimatch},i.filter=function(n,s){return e.filter(n,Zt(t,s))},i.defaults=function(n){return e.defaults(Zt(t,n))},i.makeRe=function(n,s){return e.makeRe(n,Zt(t,s))},i.braceExpand=function(n,s){return e.braceExpand(n,Zt(t,s))},i.match=function(r,n,s){return e.match(r,n,Zt(t,s))},i};Ye.defaults=function(t){return pt.defaults(t).Minimatch};function pt(t,e,i){return en(e),i||(i={}),!i.nocomment&&e.charAt(0)==="#"?!1:new Ye(e,i).match(t)}function Ye(t,e){if(!(this instanceof Ye))return new Ye(t,e);en(t),e||(e={}),t=t.trim(),!e.allowWindowsEscape&&pr.sep!=="/"&&(t=t.split(pr.sep).join("/")),this.options=e,this.set=[],this.pattern=t,this.regexp=null,this.negate=!1,this.comment=!1,this.empty=!1,this.partial=!!e.partial,this.make()}Ye.prototype.debug=function(){};Ye.prototype.make=rm;function rm(){var t=this.pattern,e=this.options;if(!e.nocomment&&t.charAt(0)==="#"){this.comment=!0;return}if(!t){this.empty=!0;return}this.parseNegate();var i=this.globSet=this.braceExpand();e.debug&&(this.debug=function(){console.error.apply(console,arguments)}),this.debug(this.pattern,i),i=this.globParts=i.map(function(r){return r.split(Dl)}),this.debug(this.pattern,i),i=i.map(function(r,n,s){return r.map(this.parse,this)},this),this.debug(this.pattern,i),i=i.filter(function(r){return r.indexOf(!1)===-1}),this.debug(this.pattern,i),this.set=i}Ye.prototype.parseNegate=nm;function nm(){var t=this.pattern,e=!1,i=this.options,r=0;if(!i.nonegate){for(var n=0,s=t.length;n<s&&t.charAt(n)==="!";n++)e=!e,r++;r&&(this.pattern=t.substr(r)),this.negate=e}}pt.braceExpand=function(t,e){return Ul(t,e)};Ye.prototype.braceExpand=Ul;function Ul(t,e){return e||(this instanceof Ye?e=this.options:e={}),t=typeof t=="undefined"?this.pattern:t,en(t),e.nobrace||!/\{(?:(?!\{).)*\}/.test(t)?[t]:Jd(t)}var sm=1024*64,en=function(t){if(typeof t!="string")throw new TypeError("invalid pattern");if(t.length>sm)throw new TypeError("pattern is too long")};Ye.prototype.parse=om;var Qr={};function om(t,e){en(t);var i=this.options;if(t==="**")if(i.noglobstar)t="*";else return hs;if(t==="")return"";var r="",n=!!i.nocase,s=!1,o=[],c=[],u,h=!1,l=-1,d=-1,m=t.charAt(0)==="."?"":i.dot?"(?!(?:^|\\/)\\.{1,2}(?:$|\\/))":"(?!\\.)",v=this;function g(){if(u){switch(u){case"*":r+=fs,n=!0;break;case"?":r+=us,n=!0;break;default:r+="\\"+u;break}v.debug("clearStateChar %j %j",u,r),u=!1}}for(var x=0,y=t.length,O;x<y&&(O=t.charAt(x));x++){if(this.debug("%s	%s %s %j",t,x,r,O),s&&Ml[O]){r+="\\"+O,s=!1;continue}switch(O){case"/":return!1;case"\\":g(),s=!0;continue;case"?":case"*":case"+":case"@":case"!":if(this.debug("%s	%s %s %j <-- stateChar",t,x,r,O),h){this.debug("  in class"),O==="!"&&x===d+1&&(O="^"),r+=O;continue}v.debug("call clearStateChar %j",u),g(),u=O,i.noext&&g();continue;case"(":if(h){r+="(";continue}if(!u){r+="\\(";continue}o.push({type:u,start:x-1,reStart:r.length,open:Fl[u].open,close:Fl[u].close}),r+=u==="!"?"(?:(?!(?:":"(?:",this.debug("plType %j %j",u,r),u=!1;continue;case")":if(h||!o.length){r+="\\)";continue}g(),n=!0;var B=o.pop();r+=B.close,B.type==="!"&&c.push(B),B.reEnd=r.length;continue;case"|":if(h||!o.length||s){r+="\\|",s=!1;continue}g(),r+="|";continue;case"[":if(g(),h){r+="\\"+O;continue}h=!0,d=x,l=r.length,r+=O;continue;case"]":if(x===d+1||!h){r+="\\"+O,s=!1;continue}var C=t.substring(d+1,x);try{RegExp("["+C+"]")}catch{var P=this.parse(C,Qr);r=r.substr(0,l)+"\\["+P[0]+"\\]",n=n||P[1],h=!1;continue}n=!0,h=!1,r+=O;continue;default:g(),s?s=!1:Ml[O]&&!(O==="^"&&h)&&(r+="\\"),r+=O}}for(h&&(C=t.substr(d+1),P=this.parse(C,Qr),r=r.substr(0,l)+"\\["+P[0],n=n||P[1]),B=o.pop();B;B=o.pop()){var S=r.slice(B.reStart+B.open.length);this.debug("setting tail",r,B),S=S.replace(/((?:\\{2}){0,64})(\\?)\|/g,function(we,de,le){return le||(le="\\"),de+de+le+"|"}),this.debug(`tail=%j
   %s`,S,S,B,r);var J=B.type==="*"?fs:B.type==="?"?us:"\\"+B.type;n=!0,r=r.slice(0,B.reStart)+J+"\\("+S}g(),s&&(r+="\\\\");var A=!1;switch(r.charAt(0)){case"[":case".":case"(":A=!0}for(var z=c.length-1;z>-1;z--){var k=c[z],L=r.slice(0,k.reStart),D=r.slice(k.reStart,k.reEnd-8),X=r.slice(k.reEnd-8,k.reEnd),j=r.slice(k.reEnd);X+=j;var se=L.split("(").length-1,M=j;for(x=0;x<se;x++)M=M.replace(/\)[+*?]?/,"");j=M;var $="";j===""&&e!==Qr&&($="$");var Y=L+D+j+$+X;r=Y}if(r!==""&&n&&(r="(?=.)"+r),A&&(r=m+r),e===Qr)return[r,n];if(!n)return lm(t);var Q=i.nocase?"i":"";try{var V=new RegExp("^"+r+"$",Q)}catch{return new RegExp("$.")}return V._glob=t,V._src=r,V}pt.makeRe=function(t,e){return new Ye(t,e||{}).makeRe()};Ye.prototype.makeRe=am;function am(){if(this.regexp||this.regexp===!1)return this.regexp;var t=this.set;if(!t.length)return this.regexp=!1,this.regexp;var e=this.options,i=e.noglobstar?fs:e.dot?Qd:em,r=e.nocase?"i":"",n=t.map(function(s){return s.map(function(o){return o===hs?i:typeof o=="string"?cm(o):o._src}).join("\\/")}).join("|");n="^(?:"+n+")$",this.negate&&(n="^(?!"+n+").*$");try{this.regexp=new RegExp(n,r)}catch{this.regexp=!1}return this.regexp}pt.match=function(t,e,i){i=i||{};var r=new Ye(e,i);return t=t.filter(function(n){return r.match(n)}),r.options.nonull&&!t.length&&t.push(e),t};Ye.prototype.match=function(e,i){if(typeof i=="undefined"&&(i=this.partial),this.debug("match",e,this.pattern),this.comment)return!1;if(this.empty)return e==="";if(e==="/"&&i)return!0;var r=this.options;pr.sep!=="/"&&(e=e.split(pr.sep).join("/")),e=e.split(Dl),this.debug(this.pattern,"split",e);var n=this.set;this.debug(this.pattern,"set",n);var s,o;for(o=e.length-1;o>=0&&(s=e[o],!s);o--);for(o=0;o<n.length;o++){var c=n[o],u=e;r.matchBase&&c.length===1&&(u=[s]);var h=this.matchOne(u,c,i);if(h)return r.flipNegate?!0:!this.negate}return r.flipNegate?!1:this.negate};Ye.prototype.matchOne=function(t,e,i){var r=this.options;this.debug("matchOne",{this:this,file:t,pattern:e}),this.debug("matchOne",t.length,e.length);for(var n=0,s=0,o=t.length,c=e.length;n<o&&s<c;n++,s++){this.debug("matchOne loop");var u=e[s],h=t[n];if(this.debug(e,u,h),u===!1)return!1;if(u===hs){this.debug("GLOBSTAR",[e,u,h]);var l=n,d=s+1;if(d===c){for(this.debug("** at the end");n<o;n++)if(t[n]==="."||t[n]===".."||!r.dot&&t[n].charAt(0)===".")return!1;return!0}for(;l<o;){var m=t[l];if(this.debug(`
globstar while`,t,l,e,d,m),this.matchOne(t.slice(l),e.slice(d),i))return this.debug("globstar found match!",l,o,m),!0;if(m==="."||m===".."||!r.dot&&m.charAt(0)==="."){this.debug("dot detected!",t,l,e,d);break}this.debug("globstar swallow a segment, and continue"),l++}return!!(i&&(this.debug(`
>>> no match, partial?`,t,l,e,d),l===o))}var v;if(typeof u=="string"?(v=h===u,this.debug("string match",u,h,v)):(v=h.match(u),this.debug("pattern match",u,h,v)),!v)return!1}if(n===o&&s===c)return!0;if(n===o)return i;if(s===c)return n===o-1&&t[n]==="";throw new Error("wtf?")};function lm(t){return t.replace(/\\(.)/g,"$1")}function cm(t){return t.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&")}});var ds=E((kx,Hl)=>{"use strict";var Vl=require("fs"),ps;function um(){try{return Vl.statSync("/.dockerenv"),!0}catch{return!1}}function fm(){try{return Vl.readFileSync("/proc/self/cgroup","utf8").includes("docker")}catch{return!1}}Hl.exports=()=>(ps===void 0&&(ps=um()||fm()),ps)});var zl=E((Ox,ms)=>{"use strict";var hm=require("os"),pm=require("fs"),$l=ds(),Gl=()=>{if(process.platform!=="linux")return!1;if(hm.release().toLowerCase().includes("microsoft"))return!$l();try{return pm.readFileSync("/proc/version","utf8").toLowerCase().includes("microsoft")?!$l():!1}catch{return!1}};process.env.__IS_WSL_TEST__?ms.exports=Gl:ms.exports=Gl()});var Yl=E((Cx,Wl)=>{"use strict";Wl.exports=(t,e,i)=>{let r=n=>Object.defineProperty(t,e,{value:n,enumerable:!0,writable:!0});return Object.defineProperty(t,e,{configurable:!0,enumerable:!0,get(){let n=i();return r(n),n},set(n){r(n)}}),t}});var tc=E((Tx,ec)=>{var dm=require("path"),mm=require("child_process"),{promises:gs,constants:Ql}=require("fs"),tn=zl(),gm=ds(),vs=Yl(),Kl=dm.join(__dirname,"xdg-open"),{platform:Hi,arch:Zl}=process,vm=(()=>{let t="/mnt/",e;return async function(){if(e)return e;let i="/etc/wsl.conf",r=!1;try{await gs.access(i,Ql.F_OK),r=!0}catch{}if(!r)return t;let n=await gs.readFile(i,{encoding:"utf8"}),s=/(?<!#.*)root\s*=\s*(?<mountPoint>.*)/g.exec(n);return s?(e=s.groups.mountPoint.trim(),e=e.endsWith("/")?e:`${e}/`,e):t}})(),Xl=async(t,e)=>{let i;for(let r of t)try{return await e(r)}catch(n){i=n}throw i},rn=async t=>{if(t={wait:!1,background:!1,newInstance:!1,allowNonzeroExitCode:!1,...t},Array.isArray(t.app))return Xl(t.app,c=>rn({...t,app:c}));let{name:e,arguments:i=[]}=t.app||{};if(i=[...i],Array.isArray(e))return Xl(e,c=>rn({...t,app:{name:c,arguments:i}}));let r,n=[],s={};if(Hi==="darwin")r="open",t.wait&&n.push("--wait-apps"),t.background&&n.push("--background"),t.newInstance&&n.push("--new"),e&&n.push("-a",e);else if(Hi==="win32"||tn&&!gm()){let c=await vm();r=tn?`${c}c/Windows/System32/WindowsPowerShell/v1.0/powershell.exe`:`${process.env.SYSTEMROOT}\\System32\\WindowsPowerShell\\v1.0\\powershell`,n.push("-NoProfile","-NonInteractive","\u2013ExecutionPolicy","Bypass","-EncodedCommand"),tn||(s.windowsVerbatimArguments=!0);let u=["Start"];t.wait&&u.push("-Wait"),e?(u.push(`"\`"${e}\`""`,"-ArgumentList"),t.target&&i.unshift(t.target)):t.target&&u.push(`"${t.target}"`),i.length>0&&(i=i.map(h=>`"\`"${h}\`""`),u.push(i.join(","))),t.target=Buffer.from(u.join(" "),"utf16le").toString("base64")}else{if(e)r=e;else{let c=!__dirname||__dirname==="/",u=!1;try{await gs.access(Kl,Ql.X_OK),u=!0}catch{}r=process.versions.electron||Hi==="android"||c||!u?"xdg-open":Kl}i.length>0&&n.push(...i),t.wait||(s.stdio="ignore",s.detached=!0)}t.target&&n.push(t.target),Hi==="darwin"&&i.length>0&&n.push("--args",...i);let o=mm.spawn(r,n,s);return t.wait?new Promise((c,u)=>{o.once("error",u),o.once("close",h=>{if(t.allowNonzeroExitCode&&h>0){u(new Error(`Exited with code ${h}`));return}c(o)})}):(o.unref(),o)},_s=(t,e)=>{if(typeof t!="string")throw new TypeError("Expected a `target`");return rn({...e,target:t})},_m=(t,e)=>{if(typeof t!="string")throw new TypeError("Expected a `name`");let{arguments:i=[]}=e||{};if(i!=null&&!Array.isArray(i))throw new TypeError("Expected `appArguments` as Array type");return rn({...e,app:{name:t,arguments:i}})};function Jl(t){if(typeof t=="string"||Array.isArray(t))return t;let{[Zl]:e}=t;if(!e)throw new Error(`${Zl} is not supported`);return e}function xs({[Hi]:t},{wsl:e}){if(e&&tn)return Jl(e);if(!t)throw new Error(`${Hi} is not supported`);return Jl(t)}var nn={};vs(nn,"chrome",()=>xs({darwin:"google chrome",win32:"chrome",linux:["google-chrome","google-chrome-stable","chromium"]},{wsl:{ia32:"/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe",x64:["/mnt/c/Program Files/Google/Chrome/Application/chrome.exe","/mnt/c/Program Files (x86)/Google/Chrome/Application/chrome.exe"]}}));vs(nn,"firefox",()=>xs({darwin:"firefox",win32:"C:\\Program Files\\Mozilla Firefox\\firefox.exe",linux:"firefox"},{wsl:"/mnt/c/Program Files/Mozilla Firefox/firefox.exe"}));vs(nn,"edge",()=>xs({darwin:"microsoft edge",win32:"msedge",linux:["microsoft-edge","microsoft-edge-dev"]},{wsl:"/mnt/c/Program Files (x86)/Microsoft/Edge/Application/msedge.exe"}));_s.apps=nn;_s.openApp=_m;ec.exports=_s});var ys=E((Ix,rc)=>{"use strict";var xm=require("util"),ic=require("stream"),Ct=rc.exports=function(){ic.call(this),this._buffers=[],this._buffered=0,this._reads=[],this._paused=!1,this._encoding="utf8",this.writable=!0};xm.inherits(Ct,ic);Ct.prototype.read=function(t,e){this._reads.push({length:Math.abs(t),allowLess:t<0,func:e}),process.nextTick(function(){this._process(),this._paused&&this._reads&&this._reads.length>0&&(this._paused=!1,this.emit("drain"))}.bind(this))};Ct.prototype.write=function(t,e){if(!this.writable)return this.emit("error",new Error("Stream not writable")),!1;let i;return Buffer.isBuffer(t)?i=t:i=Buffer.from(t,e||this._encoding),this._buffers.push(i),this._buffered+=i.length,this._process(),this._reads&&this._reads.length===0&&(this._paused=!0),this.writable&&!this._paused};Ct.prototype.end=function(t,e){t&&this.write(t,e),this.writable=!1,this._buffers&&(this._buffers.length===0?this._end():(this._buffers.push(null),this._process()))};Ct.prototype.destroySoon=Ct.prototype.end;Ct.prototype._end=function(){this._reads.length>0&&this.emit("error",new Error("Unexpected end of input")),this.destroy()};Ct.prototype.destroy=function(){this._buffers&&(this.writable=!1,this._reads=null,this._buffers=null,this.emit("close"))};Ct.prototype._processReadAllowingLess=function(t){this._reads.shift();let e=this._buffers[0];e.length>t.length?(this._buffered-=t.length,this._buffers[0]=e.slice(t.length),t.func.call(this,e.slice(0,t.length))):(this._buffered-=e.length,this._buffers.shift(),t.func.call(this,e))};Ct.prototype._processRead=function(t){this._reads.shift();let e=0,i=0,r=Buffer.alloc(t.length);for(;e<t.length;){let n=this._buffers[i++],s=Math.min(n.length,t.length-e);n.copy(r,e,0,s),e+=s,s!==n.length&&(this._buffers[--i]=n.slice(s))}i>0&&this._buffers.splice(0,i),this._buffered-=t.length,t.func.call(this,r)};Ct.prototype._process=function(){try{for(;this._buffered>0&&this._reads&&this._reads.length>0;){let t=this._reads[0];if(t.allowLess)this._processReadAllowingLess(t);else if(this._buffered>=t.length)this._processRead(t);else break}this._buffers&&!this.writable&&this._end()}catch(t){this.emit("error",t)}}});var ws=E(bs=>{"use strict";var Xt=[{x:[0],y:[0]},{x:[4],y:[0]},{x:[0,4],y:[4]},{x:[2,6],y:[0,4]},{x:[0,2,4,6],y:[2,6]},{x:[1,3,5,7],y:[0,2,4,6]},{x:[0,1,2,3,4,5,6,7],y:[1,3,5,7]}];bs.getImagePasses=function(t,e){let i=[],r=t%8,n=e%8,s=(t-r)/8,o=(e-n)/8;for(let c=0;c<Xt.length;c++){let u=Xt[c],h=s*u.x.length,l=o*u.y.length;for(let d=0;d<u.x.length&&u.x[d]<r;d++)h++;for(let d=0;d<u.y.length&&u.y[d]<n;d++)l++;h>0&&l>0&&i.push({width:h,height:l,index:c})}return i};bs.getInterlaceIterator=function(t){return function(e,i,r){let n=e%Xt[r].x.length,s=(e-n)/Xt[r].x.length*8+Xt[r].x[n],o=i%Xt[r].y.length,c=(i-o)/Xt[r].y.length*8+Xt[r].y[o];return s*4+c*t*4}}});var Es=E((Bx,nc)=>{"use strict";nc.exports=function(e,i,r){let n=e+i-r,s=Math.abs(n-e),o=Math.abs(n-i),c=Math.abs(n-r);return s<=o&&s<=c?e:o<=c?i:r}});var Ss=E((Rx,oc)=>{"use strict";var ym=ws(),bm=Es();function sc(t,e,i){let r=t*e;return i!==8&&(r=Math.ceil(r/(8/i))),r}var $i=oc.exports=function(t,e){let i=t.width,r=t.height,n=t.interlace,s=t.bpp,o=t.depth;if(this.read=e.read,this.write=e.write,this.complete=e.complete,this._imageIndex=0,this._images=[],n){let c=ym.getImagePasses(i,r);for(let u=0;u<c.length;u++)this._images.push({byteWidth:sc(c[u].width,s,o),height:c[u].height,lineIndex:0})}else this._images.push({byteWidth:sc(i,s,o),height:r,lineIndex:0});o===8?this._xComparison=s:o===16?this._xComparison=s*2:this._xComparison=1};$i.prototype.start=function(){this.read(this._images[this._imageIndex].byteWidth+1,this._reverseFilterLine.bind(this))};$i.prototype._unFilterType1=function(t,e,i){let r=this._xComparison,n=r-1;for(let s=0;s<i;s++){let o=t[1+s],c=s>n?e[s-r]:0;e[s]=o+c}};$i.prototype._unFilterType2=function(t,e,i){let r=this._lastLine;for(let n=0;n<i;n++){let s=t[1+n],o=r?r[n]:0;e[n]=s+o}};$i.prototype._unFilterType3=function(t,e,i){let r=this._xComparison,n=r-1,s=this._lastLine;for(let o=0;o<i;o++){let c=t[1+o],u=s?s[o]:0,h=o>n?e[o-r]:0,l=Math.floor((h+u)/2);e[o]=c+l}};$i.prototype._unFilterType4=function(t,e,i){let r=this._xComparison,n=r-1,s=this._lastLine;for(let o=0;o<i;o++){let c=t[1+o],u=s?s[o]:0,h=o>n?e[o-r]:0,l=o>n&&s?s[o-r]:0,d=bm(h,u,l);e[o]=c+d}};$i.prototype._reverseFilterLine=function(t){let e=t[0],i,r=this._images[this._imageIndex],n=r.byteWidth;if(e===0)i=t.slice(1,n+1);else switch(i=Buffer.alloc(n),e){case 1:this._unFilterType1(t,i,n);break;case 2:this._unFilterType2(t,i,n);break;case 3:this._unFilterType3(t,i,n);break;case 4:this._unFilterType4(t,i,n);break;default:throw new Error("Unrecognised filter type - "+e)}this.write(i),r.lineIndex++,r.lineIndex>=r.height?(this._lastLine=null,this._imageIndex++,r=this._images[this._imageIndex]):this._lastLine=i,r?this.read(r.byteWidth+1,this._reverseFilterLine.bind(this)):(this._lastLine=null,this.complete())}});var cc=E((Px,lc)=>{"use strict";var wm=require("util"),ac=ys(),Em=Ss(),Sm=lc.exports=function(t){ac.call(this);let e=[],i=this;this._filter=new Em(t,{read:this.read.bind(this),write:function(r){e.push(r)},complete:function(){i.emit("complete",Buffer.concat(e))}}),this._filter.start()};wm.inherits(Sm,ac)});var Gi=E((Nx,uc)=>{"use strict";uc.exports={PNG_SIGNATURE:[137,80,78,71,13,10,26,10],TYPE_IHDR:1229472850,TYPE_IEND:1229278788,TYPE_IDAT:1229209940,TYPE_PLTE:1347179589,TYPE_tRNS:1951551059,TYPE_gAMA:1732332865,COLORTYPE_GRAYSCALE:0,COLORTYPE_PALETTE:1,COLORTYPE_COLOR:2,COLORTYPE_ALPHA:4,COLORTYPE_PALETTE_COLOR:3,COLORTYPE_COLOR_ALPHA:6,COLORTYPE_TO_BPP_MAP:{0:1,2:3,3:1,4:2,6:4},GAMMA_DIVISION:1e5}});var Cs=E((Lx,fc)=>{"use strict";var ks=[];(function(){for(let t=0;t<256;t++){let e=t;for(let i=0;i<8;i++)e&1?e=3988292384^e>>>1:e=e>>>1;ks[t]=e}})();var Os=fc.exports=function(){this._crc=-1};Os.prototype.write=function(t){for(let e=0;e<t.length;e++)this._crc=ks[(this._crc^t[e])&255]^this._crc>>>8;return!0};Os.prototype.crc32=function(){return this._crc^-1};Os.crc32=function(t){let e=-1;for(let i=0;i<t.length;i++)e=ks[(e^t[i])&255]^e>>>8;return e^-1}});var Ts=E((Fx,hc)=>{"use strict";var je=Gi(),km=Cs(),He=hc.exports=function(t,e){this._options=t,t.checkCRC=t.checkCRC!==!1,this._hasIHDR=!1,this._hasIEND=!1,this._emittedHeadersFinished=!1,this._palette=[],this._colorType=0,this._chunks={},this._chunks[je.TYPE_IHDR]=this._handleIHDR.bind(this),this._chunks[je.TYPE_IEND]=this._handleIEND.bind(this),this._chunks[je.TYPE_IDAT]=this._handleIDAT.bind(this),this._chunks[je.TYPE_PLTE]=this._handlePLTE.bind(this),this._chunks[je.TYPE_tRNS]=this._handleTRNS.bind(this),this._chunks[je.TYPE_gAMA]=this._handleGAMA.bind(this),this.read=e.read,this.error=e.error,this.metadata=e.metadata,this.gamma=e.gamma,this.transColor=e.transColor,this.palette=e.palette,this.parsed=e.parsed,this.inflateData=e.inflateData,this.finished=e.finished,this.simpleTransparency=e.simpleTransparency,this.headersFinished=e.headersFinished||function(){}};He.prototype.start=function(){this.read(je.PNG_SIGNATURE.length,this._parseSignature.bind(this))};He.prototype._parseSignature=function(t){let e=je.PNG_SIGNATURE;for(let i=0;i<e.length;i++)if(t[i]!==e[i]){this.error(new Error("Invalid file signature"));return}this.read(8,this._parseChunkBegin.bind(this))};He.prototype._parseChunkBegin=function(t){let e=t.readUInt32BE(0),i=t.readUInt32BE(4),r="";for(let s=4;s<8;s++)r+=String.fromCharCode(t[s]);let n=!!(t[4]&32);if(!this._hasIHDR&&i!==je.TYPE_IHDR){this.error(new Error("Expected IHDR on beggining"));return}if(this._crc=new km,this._crc.write(Buffer.from(r)),this._chunks[i])return this._chunks[i](e);if(!n){this.error(new Error("Unsupported critical chunk type "+r));return}this.read(e+4,this._skipChunk.bind(this))};He.prototype._skipChunk=function(){this.read(8,this._parseChunkBegin.bind(this))};He.prototype._handleChunkEnd=function(){this.read(4,this._parseChunkEnd.bind(this))};He.prototype._parseChunkEnd=function(t){let e=t.readInt32BE(0),i=this._crc.crc32();if(this._options.checkCRC&&i!==e){this.error(new Error("Crc error - "+e+" - "+i));return}this._hasIEND||this.read(8,this._parseChunkBegin.bind(this))};He.prototype._handleIHDR=function(t){this.read(t,this._parseIHDR.bind(this))};He.prototype._parseIHDR=function(t){this._crc.write(t);let e=t.readUInt32BE(0),i=t.readUInt32BE(4),r=t[8],n=t[9],s=t[10],o=t[11],c=t[12];if(r!==8&&r!==4&&r!==2&&r!==1&&r!==16){this.error(new Error("Unsupported bit depth "+r));return}if(!(n in je.COLORTYPE_TO_BPP_MAP)){this.error(new Error("Unsupported color type"));return}if(s!==0){this.error(new Error("Unsupported compression method"));return}if(o!==0){this.error(new Error("Unsupported filter method"));return}if(c!==0&&c!==1){this.error(new Error("Unsupported interlace method"));return}this._colorType=n;let u=je.COLORTYPE_TO_BPP_MAP[this._colorType];this._hasIHDR=!0,this.metadata({width:e,height:i,depth:r,interlace:!!c,palette:!!(n&je.COLORTYPE_PALETTE),color:!!(n&je.COLORTYPE_COLOR),alpha:!!(n&je.COLORTYPE_ALPHA),bpp:u,colorType:n}),this._handleChunkEnd()};He.prototype._handlePLTE=function(t){this.read(t,this._parsePLTE.bind(this))};He.prototype._parsePLTE=function(t){this._crc.write(t);let e=Math.floor(t.length/3);for(let i=0;i<e;i++)this._palette.push([t[i*3],t[i*3+1],t[i*3+2],255]);this.palette(this._palette),this._handleChunkEnd()};He.prototype._handleTRNS=function(t){this.simpleTransparency(),this.read(t,this._parseTRNS.bind(this))};He.prototype._parseTRNS=function(t){if(this._crc.write(t),this._colorType===je.COLORTYPE_PALETTE_COLOR){if(this._palette.length===0){this.error(new Error("Transparency chunk must be after palette"));return}if(t.length>this._palette.length){this.error(new Error("More transparent colors than palette size"));return}for(let e=0;e<t.length;e++)this._palette[e][3]=t[e];this.palette(this._palette)}this._colorType===je.COLORTYPE_GRAYSCALE&&this.transColor([t.readUInt16BE(0)]),this._colorType===je.COLORTYPE_COLOR&&this.transColor([t.readUInt16BE(0),t.readUInt16BE(2),t.readUInt16BE(4)]),this._handleChunkEnd()};He.prototype._handleGAMA=function(t){this.read(t,this._parseGAMA.bind(this))};He.prototype._parseGAMA=function(t){this._crc.write(t),this.gamma(t.readUInt32BE(0)/je.GAMMA_DIVISION),this._handleChunkEnd()};He.prototype._handleIDAT=function(t){this._emittedHeadersFinished||(this._emittedHeadersFinished=!0,this.headersFinished()),this.read(-t,this._parseIDAT.bind(this,t))};He.prototype._parseIDAT=function(t,e){if(this._crc.write(e),this._colorType===je.COLORTYPE_PALETTE_COLOR&&this._palette.length===0)throw new Error("Expected palette not found");this.inflateData(e);let i=t-e.length;i>0?this._handleIDAT(i):this._handleChunkEnd()};He.prototype._handleIEND=function(t){this.read(t,this._parseIEND.bind(this))};He.prototype._parseIEND=function(t){this._crc.write(t),this._hasIEND=!0,this._handleChunkEnd(),this.finished&&this.finished()}});var Is=E(dc=>{"use strict";var pc=ws(),Om=[function(){},function(t,e,i,r){if(r===e.length)throw new Error("Ran out of data");let n=e[r];t[i]=n,t[i+1]=n,t[i+2]=n,t[i+3]=255},function(t,e,i,r){if(r+1>=e.length)throw new Error("Ran out of data");let n=e[r];t[i]=n,t[i+1]=n,t[i+2]=n,t[i+3]=e[r+1]},function(t,e,i,r){if(r+2>=e.length)throw new Error("Ran out of data");t[i]=e[r],t[i+1]=e[r+1],t[i+2]=e[r+2],t[i+3]=255},function(t,e,i,r){if(r+3>=e.length)throw new Error("Ran out of data");t[i]=e[r],t[i+1]=e[r+1],t[i+2]=e[r+2],t[i+3]=e[r+3]}],Cm=[function(){},function(t,e,i,r){let n=e[0];t[i]=n,t[i+1]=n,t[i+2]=n,t[i+3]=r},function(t,e,i){let r=e[0];t[i]=r,t[i+1]=r,t[i+2]=r,t[i+3]=e[1]},function(t,e,i,r){t[i]=e[0],t[i+1]=e[1],t[i+2]=e[2],t[i+3]=r},function(t,e,i){t[i]=e[0],t[i+1]=e[1],t[i+2]=e[2],t[i+3]=e[3]}];function Tm(t,e){let i=[],r=0;function n(){if(r===t.length)throw new Error("Ran out of data");let s=t[r];r++;let o,c,u,h,l,d,m,v;switch(e){default:throw new Error("unrecognised depth");case 16:m=t[r],r++,i.push((s<<8)+m);break;case 4:m=s&15,v=s>>4,i.push(v,m);break;case 2:l=s&3,d=s>>2&3,m=s>>4&3,v=s>>6&3,i.push(v,m,d,l);break;case 1:o=s&1,c=s>>1&1,u=s>>2&1,h=s>>3&1,l=s>>4&1,d=s>>5&1,m=s>>6&1,v=s>>7&1,i.push(v,m,d,l,h,u,c,o);break}}return{get:function(s){for(;i.length<s;)n();let o=i.slice(0,s);return i=i.slice(s),o},resetAfterLine:function(){i.length=0},end:function(){if(r!==t.length)throw new Error("extra data found")}}}function Im(t,e,i,r,n,s){let o=t.width,c=t.height,u=t.index;for(let h=0;h<c;h++)for(let l=0;l<o;l++){let d=i(l,h,u);Om[r](e,n,d,s),s+=r}return s}function Am(t,e,i,r,n,s){let o=t.width,c=t.height,u=t.index;for(let h=0;h<c;h++){for(let l=0;l<o;l++){let d=n.get(r),m=i(l,h,u);Cm[r](e,d,m,s)}n.resetAfterLine()}}dc.dataToBitMap=function(t,e){let i=e.width,r=e.height,n=e.depth,s=e.bpp,o=e.interlace,c;n!==8&&(c=Tm(t,n));let u;n<=8?u=Buffer.alloc(i*r*4):u=new Uint16Array(i*r*4);let h=Math.pow(2,n)-1,l=0,d,m;if(o)d=pc.getImagePasses(i,r),m=pc.getInterlaceIterator(i,r);else{let v=0;m=function(){let g=v;return v+=4,g},d=[{width:i,height:r}]}for(let v=0;v<d.length;v++)n===8?l=Im(d[v],u,m,s,t,l):Am(d[v],u,m,s,c,h);if(n===8){if(l!==t.length)throw new Error("extra data found")}else c.end();return u}});var As=E((Dx,mc)=>{"use strict";function Bm(t,e,i,r,n){let s=0;for(let o=0;o<r;o++)for(let c=0;c<i;c++){let u=n[t[s]];if(!u)throw new Error("index "+t[s]+" not in palette");for(let h=0;h<4;h++)e[s+h]=u[h];s+=4}}function Rm(t,e,i,r,n){let s=0;for(let o=0;o<r;o++)for(let c=0;c<i;c++){let u=!1;if(n.length===1?n[0]===t[s]&&(u=!0):n[0]===t[s]&&n[1]===t[s+1]&&n[2]===t[s+2]&&(u=!0),u)for(let h=0;h<4;h++)e[s+h]=0;s+=4}}function Pm(t,e,i,r,n){let s=255,o=Math.pow(2,n)-1,c=0;for(let u=0;u<r;u++)for(let h=0;h<i;h++){for(let l=0;l<4;l++)e[c+l]=Math.floor(t[c+l]*s/o+.5);c+=4}}mc.exports=function(t,e,i=!1){let r=e.depth,n=e.width,s=e.height,o=e.colorType,c=e.transColor,u=e.palette,h=t;return o===3?Bm(t,h,n,s,u):(c&&Rm(t,h,n,s,c),r!==8&&!i&&(r===16&&(h=Buffer.alloc(n*s*4)),Pm(t,h,n,s,r))),h}});var _c=E((Ux,vc)=>{"use strict";var Nm=require("util"),Bs=require("zlib"),gc=ys(),Lm=cc(),Fm=Ts(),Mm=Is(),Dm=As(),Rt=vc.exports=function(t){gc.call(this),this._parser=new Fm(t,{read:this.read.bind(this),error:this._handleError.bind(this),metadata:this._handleMetaData.bind(this),gamma:this.emit.bind(this,"gamma"),palette:this._handlePalette.bind(this),transColor:this._handleTransColor.bind(this),finished:this._finished.bind(this),inflateData:this._inflateData.bind(this),simpleTransparency:this._simpleTransparency.bind(this),headersFinished:this._headersFinished.bind(this)}),this._options=t,this.writable=!0,this._parser.start()};Nm.inherits(Rt,gc);Rt.prototype._handleError=function(t){this.emit("error",t),this.writable=!1,this.destroy(),this._inflate&&this._inflate.destroy&&this._inflate.destroy(),this._filter&&(this._filter.destroy(),this._filter.on("error",function(){})),this.errord=!0};Rt.prototype._inflateData=function(t){if(!this._inflate)if(this._bitmapInfo.interlace)this._inflate=Bs.createInflate(),this._inflate.on("error",this.emit.bind(this,"error")),this._filter.on("complete",this._complete.bind(this)),this._inflate.pipe(this._filter);else{let i=((this._bitmapInfo.width*this._bitmapInfo.bpp*this._bitmapInfo.depth+7>>3)+1)*this._bitmapInfo.height,r=Math.max(i,Bs.Z_MIN_CHUNK);this._inflate=Bs.createInflate({chunkSize:r});let n=i,s=this.emit.bind(this,"error");this._inflate.on("error",function(c){n&&s(c)}),this._filter.on("complete",this._complete.bind(this));let o=this._filter.write.bind(this._filter);this._inflate.on("data",function(c){n&&(c.length>n&&(c=c.slice(0,n)),n-=c.length,o(c))}),this._inflate.on("end",this._filter.end.bind(this._filter))}this._inflate.write(t)};Rt.prototype._handleMetaData=function(t){this._metaData=t,this._bitmapInfo=Object.create(t),this._filter=new Lm(this._bitmapInfo)};Rt.prototype._handleTransColor=function(t){this._bitmapInfo.transColor=t};Rt.prototype._handlePalette=function(t){this._bitmapInfo.palette=t};Rt.prototype._simpleTransparency=function(){this._metaData.alpha=!0};Rt.prototype._headersFinished=function(){this.emit("metadata",this._metaData)};Rt.prototype._finished=function(){this.errord||(this._inflate?this._inflate.end():this.emit("error","No Inflate block"))};Rt.prototype._complete=function(t){if(this.errord)return;let e;try{let i=Mm.dataToBitMap(t,this._bitmapInfo);e=Dm(i,this._bitmapInfo,this._options.skipRescale),i=null}catch(i){this._handleError(i);return}this.emit("parsed",e)}});var yc=E((jx,xc)=>{"use strict";var _t=Gi();xc.exports=function(t,e,i,r){let n=[_t.COLORTYPE_COLOR_ALPHA,_t.COLORTYPE_ALPHA].indexOf(r.colorType)!==-1;if(r.colorType===r.inputColorType){let g=function(){let x=new ArrayBuffer(2);return new DataView(x).setInt16(0,256,!0),new Int16Array(x)[0]!==256}();if(r.bitDepth===8||r.bitDepth===16&&g)return t}let s=r.bitDepth!==16?t:new Uint16Array(t.buffer),o=255,c=_t.COLORTYPE_TO_BPP_MAP[r.inputColorType];c===4&&!r.inputHasAlpha&&(c=3);let u=_t.COLORTYPE_TO_BPP_MAP[r.colorType];r.bitDepth===16&&(o=65535,u*=2);let h=Buffer.alloc(e*i*u),l=0,d=0,m=r.bgColor||{};m.red===void 0&&(m.red=o),m.green===void 0&&(m.green=o),m.blue===void 0&&(m.blue=o);function v(){let g,x,y,O=o;switch(r.inputColorType){case _t.COLORTYPE_COLOR_ALPHA:O=s[l+3],g=s[l],x=s[l+1],y=s[l+2];break;case _t.COLORTYPE_COLOR:g=s[l],x=s[l+1],y=s[l+2];break;case _t.COLORTYPE_ALPHA:O=s[l+1],g=s[l],x=g,y=g;break;case _t.COLORTYPE_GRAYSCALE:g=s[l],x=g,y=g;break;default:throw new Error("input color type:"+r.inputColorType+" is not supported at present")}return r.inputHasAlpha&&(n||(O/=o,g=Math.min(Math.max(Math.round((1-O)*m.red+O*g),0),o),x=Math.min(Math.max(Math.round((1-O)*m.green+O*x),0),o),y=Math.min(Math.max(Math.round((1-O)*m.blue+O*y),0),o))),{red:g,green:x,blue:y,alpha:O}}for(let g=0;g<i;g++)for(let x=0;x<e;x++){let y=v(s,l);switch(r.colorType){case _t.COLORTYPE_COLOR_ALPHA:case _t.COLORTYPE_COLOR:r.bitDepth===8?(h[d]=y.red,h[d+1]=y.green,h[d+2]=y.blue,n&&(h[d+3]=y.alpha)):(h.writeUInt16BE(y.red,d),h.writeUInt16BE(y.green,d+2),h.writeUInt16BE(y.blue,d+4),n&&h.writeUInt16BE(y.alpha,d+6));break;case _t.COLORTYPE_ALPHA:case _t.COLORTYPE_GRAYSCALE:{let O=(y.red+y.green+y.blue)/3;r.bitDepth===8?(h[d]=O,n&&(h[d+1]=y.alpha)):(h.writeUInt16BE(O,d),n&&h.writeUInt16BE(y.alpha,d+2));break}default:throw new Error("unrecognised color Type "+r.colorType)}l+=c,d+=u}return h}});var Ec=E((qx,wc)=>{"use strict";var bc=Es();function Um(t,e,i,r,n){for(let s=0;s<i;s++)r[n+s]=t[e+s]}function jm(t,e,i){let r=0,n=e+i;for(let s=e;s<n;s++)r+=Math.abs(t[s]);return r}function qm(t,e,i,r,n,s){for(let o=0;o<i;o++){let c=o>=s?t[e+o-s]:0,u=t[e+o]-c;r[n+o]=u}}function Vm(t,e,i,r){let n=0;for(let s=0;s<i;s++){let o=s>=r?t[e+s-r]:0,c=t[e+s]-o;n+=Math.abs(c)}return n}function Hm(t,e,i,r,n){for(let s=0;s<i;s++){let o=e>0?t[e+s-i]:0,c=t[e+s]-o;r[n+s]=c}}function $m(t,e,i){let r=0,n=e+i;for(let s=e;s<n;s++){let o=e>0?t[s-i]:0,c=t[s]-o;r+=Math.abs(c)}return r}function Gm(t,e,i,r,n,s){for(let o=0;o<i;o++){let c=o>=s?t[e+o-s]:0,u=e>0?t[e+o-i]:0,h=t[e+o]-(c+u>>1);r[n+o]=h}}function zm(t,e,i,r){let n=0;for(let s=0;s<i;s++){let o=s>=r?t[e+s-r]:0,c=e>0?t[e+s-i]:0,u=t[e+s]-(o+c>>1);n+=Math.abs(u)}return n}function Wm(t,e,i,r,n,s){for(let o=0;o<i;o++){let c=o>=s?t[e+o-s]:0,u=e>0?t[e+o-i]:0,h=e>0&&o>=s?t[e+o-(i+s)]:0,l=t[e+o]-bc(c,u,h);r[n+o]=l}}function Ym(t,e,i,r){let n=0;for(let s=0;s<i;s++){let o=s>=r?t[e+s-r]:0,c=e>0?t[e+s-i]:0,u=e>0&&s>=r?t[e+s-(i+r)]:0,h=t[e+s]-bc(o,c,u);n+=Math.abs(h)}return n}var Km={0:Um,1:qm,2:Hm,3:Gm,4:Wm},Zm={0:jm,1:Vm,2:$m,3:zm,4:Ym};wc.exports=function(t,e,i,r,n){let s;if(!("filterType"in r)||r.filterType===-1)s=[0,1,2,3,4];else if(typeof r.filterType=="number")s=[r.filterType];else throw new Error("unrecognised filter types");r.bitDepth===16&&(n*=2);let o=e*n,c=0,u=0,h=Buffer.alloc((o+1)*i),l=s[0];for(let d=0;d<i;d++){if(s.length>1){let m=1/0;for(let v=0;v<s.length;v++){let g=Zm[s[v]](t,u,o,n);g<m&&(l=s[v],m=g)}}h[c]=l,c++,Km[l](t,u,o,h,c,n),c+=o,u+=o}return h}});var Rs=E((Vx,Sc)=>{"use strict";var Xe=Gi(),Xm=Cs(),Jm=yc(),Qm=Ec(),e0=require("zlib"),Jt=Sc.exports=function(t){if(this._options=t,t.deflateChunkSize=t.deflateChunkSize||32*1024,t.deflateLevel=t.deflateLevel!=null?t.deflateLevel:9,t.deflateStrategy=t.deflateStrategy!=null?t.deflateStrategy:3,t.inputHasAlpha=t.inputHasAlpha!=null?t.inputHasAlpha:!0,t.deflateFactory=t.deflateFactory||e0.createDeflate,t.bitDepth=t.bitDepth||8,t.colorType=typeof t.colorType=="number"?t.colorType:Xe.COLORTYPE_COLOR_ALPHA,t.inputColorType=typeof t.inputColorType=="number"?t.inputColorType:Xe.COLORTYPE_COLOR_ALPHA,[Xe.COLORTYPE_GRAYSCALE,Xe.COLORTYPE_COLOR,Xe.COLORTYPE_COLOR_ALPHA,Xe.COLORTYPE_ALPHA].indexOf(t.colorType)===-1)throw new Error("option color type:"+t.colorType+" is not supported at present");if([Xe.COLORTYPE_GRAYSCALE,Xe.COLORTYPE_COLOR,Xe.COLORTYPE_COLOR_ALPHA,Xe.COLORTYPE_ALPHA].indexOf(t.inputColorType)===-1)throw new Error("option input color type:"+t.inputColorType+" is not supported at present");if(t.bitDepth!==8&&t.bitDepth!==16)throw new Error("option bit depth:"+t.bitDepth+" is not supported at present")};Jt.prototype.getDeflateOptions=function(){return{chunkSize:this._options.deflateChunkSize,level:this._options.deflateLevel,strategy:this._options.deflateStrategy}};Jt.prototype.createDeflate=function(){return this._options.deflateFactory(this.getDeflateOptions())};Jt.prototype.filterData=function(t,e,i){let r=Jm(t,e,i,this._options),n=Xe.COLORTYPE_TO_BPP_MAP[this._options.colorType];return Qm(r,e,i,this._options,n)};Jt.prototype._packChunk=function(t,e){let i=e?e.length:0,r=Buffer.alloc(i+12);return r.writeUInt32BE(i,0),r.writeUInt32BE(t,4),e&&e.copy(r,8),r.writeInt32BE(Xm.crc32(r.slice(4,r.length-4)),r.length-4),r};Jt.prototype.packGAMA=function(t){let e=Buffer.alloc(4);return e.writeUInt32BE(Math.floor(t*Xe.GAMMA_DIVISION),0),this._packChunk(Xe.TYPE_gAMA,e)};Jt.prototype.packIHDR=function(t,e){let i=Buffer.alloc(13);return i.writeUInt32BE(t,0),i.writeUInt32BE(e,4),i[8]=this._options.bitDepth,i[9]=this._options.colorType,i[10]=0,i[11]=0,i[12]=0,this._packChunk(Xe.TYPE_IHDR,i)};Jt.prototype.packIDAT=function(t){return this._packChunk(Xe.TYPE_IDAT,t)};Jt.prototype.packIEND=function(){return this._packChunk(Xe.TYPE_IEND,null)}});var Tc=E((Hx,Cc)=>{"use strict";var t0=require("util"),kc=require("stream"),i0=Gi(),r0=Rs(),Oc=Cc.exports=function(t){kc.call(this);let e=t||{};this._packer=new r0(e),this._deflate=this._packer.createDeflate(),this.readable=!0};t0.inherits(Oc,kc);Oc.prototype.pack=function(t,e,i,r){this.emit("data",Buffer.from(i0.PNG_SIGNATURE)),this.emit("data",this._packer.packIHDR(e,i)),r&&this.emit("data",this._packer.packGAMA(r));let n=this._packer.filterData(t,e,i);this._deflate.on("error",this.emit.bind(this,"error")),this._deflate.on("data",function(s){this.emit("data",this._packer.packIDAT(s))}.bind(this)),this._deflate.on("end",function(){this.emit("data",this._packer.packIEND()),this.emit("end")}.bind(this)),this._deflate.end(n)}});var Nc=E((dr,Pc)=>{"use strict";var Ic=require("assert").ok,zi=require("zlib"),n0=require("util"),Ac=require("buffer").kMaxLength;function gi(t){if(!(this instanceof gi))return new gi(t);t&&t.chunkSize<zi.Z_MIN_CHUNK&&(t.chunkSize=zi.Z_MIN_CHUNK),zi.Inflate.call(this,t),this._offset=this._offset===void 0?this._outOffset:this._offset,this._buffer=this._buffer||this._outBuffer,t&&t.maxLength!=null&&(this._maxLength=t.maxLength)}function s0(t){return new gi(t)}function Bc(t,e){e&&process.nextTick(e),t._handle&&(t._handle.close(),t._handle=null)}gi.prototype._processChunk=function(t,e,i){if(typeof i=="function")return zi.Inflate._processChunk.call(this,t,e,i);let r=this,n=t&&t.length,s=this._chunkSize-this._offset,o=this._maxLength,c=0,u=[],h=0,l;this.on("error",function(g){l=g});function d(g,x){if(r._hadError)return;let y=s-x;if(Ic(y>=0,"have should not go down"),y>0){let O=r._buffer.slice(r._offset,r._offset+y);if(r._offset+=y,O.length>o&&(O=O.slice(0,o)),u.push(O),h+=O.length,o-=O.length,o===0)return!1}return(x===0||r._offset>=r._chunkSize)&&(s=r._chunkSize,r._offset=0,r._buffer=Buffer.allocUnsafe(r._chunkSize)),x===0?(c+=n-g,n=g,!0):!1}Ic(this._handle,"zlib binding closed");let m;do m=this._handle.writeSync(e,t,c,n,this._buffer,this._offset,s),m=m||this._writeState;while(!this._hadError&&d(m[0],m[1]));if(this._hadError)throw l;if(h>=Ac)throw Bc(this),new RangeError("Cannot create final Buffer. It would be larger than 0x"+Ac.toString(16)+" bytes");let v=Buffer.concat(u,h);return Bc(this),v};n0.inherits(gi,zi.Inflate);function o0(t,e){if(typeof e=="string"&&(e=Buffer.from(e)),!(e instanceof Buffer))throw new TypeError("Not a string or buffer");let i=t._finishFlushFlag;return i==null&&(i=zi.Z_FINISH),t._processChunk(e,i)}function Rc(t,e){return o0(new gi(e),t)}Pc.exports=dr=Rc;dr.Inflate=gi;dr.createInflate=s0;dr.inflateSync=Rc});var Ps=E(($x,Fc)=>{"use strict";var Lc=Fc.exports=function(t){this._buffer=t,this._reads=[]};Lc.prototype.read=function(t,e){this._reads.push({length:Math.abs(t),allowLess:t<0,func:e})};Lc.prototype.process=function(){for(;this._reads.length>0&&this._buffer.length;){let t=this._reads[0];if(this._buffer.length&&(this._buffer.length>=t.length||t.allowLess)){this._reads.shift();let e=this._buffer;this._buffer=e.slice(t.length),t.func.call(this,e.slice(0,t.length))}else break}if(this._reads.length>0)throw new Error("There are some read requests waitng on finished stream");if(this._buffer.length>0)throw new Error("unrecognised content at end of stream")}});var Dc=E(Mc=>{"use strict";var a0=Ps(),l0=Ss();Mc.process=function(t,e){let i=[],r=new a0(t);return new l0(e,{read:r.read.bind(r),write:function(s){i.push(s)},complete:function(){}}).start(),r.process(),Buffer.concat(i)}});var Vc=E((zx,qc)=>{"use strict";var Uc=!0,jc=require("zlib"),c0=Nc();jc.deflateSync||(Uc=!1);var u0=Ps(),f0=Dc(),h0=Ts(),p0=Is(),d0=As();qc.exports=function(t,e){if(!Uc)throw new Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");let i;function r(P){i=P}let n;function s(P){n=P}function o(P){n.transColor=P}function c(P){n.palette=P}function u(){n.alpha=!0}let h;function l(P){h=P}let d=[];function m(P){d.push(P)}let v=new u0(t);if(new h0(e,{read:v.read.bind(v),error:r,metadata:s,gamma:l,palette:c,transColor:o,inflateData:m,simpleTransparency:u}).start(),v.process(),i)throw i;let x=Buffer.concat(d);d.length=0;let y;if(n.interlace)y=jc.inflateSync(x);else{let S=((n.width*n.bpp*n.depth+7>>3)+1)*n.height;y=c0(x,{chunkSize:S,maxLength:S})}if(x=null,!y||!y.length)throw new Error("bad png - invalid inflate data response");let O=f0.process(y,n);x=null;let B=p0.dataToBitMap(O,n);O=null;let C=d0(B,n,e.skipRescale);return n.data=C,n.gamma=h||0,n}});var zc=E((Wx,Gc)=>{"use strict";var Hc=!0,$c=require("zlib");$c.deflateSync||(Hc=!1);var m0=Gi(),g0=Rs();Gc.exports=function(t,e){if(!Hc)throw new Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");let i=e||{},r=new g0(i),n=[];n.push(Buffer.from(m0.PNG_SIGNATURE)),n.push(r.packIHDR(t.width,t.height)),t.gamma&&n.push(r.packGAMA(t.gamma));let s=r.filterData(t.data,t.width,t.height),o=$c.deflateSync(s,r.getDeflateOptions());if(s=null,!o||!o.length)throw new Error("bad png - invalid compressed data response");return n.push(r.packIDAT(o)),n.push(r.packIEND()),Buffer.concat(n)}});var Wc=E(Ns=>{"use strict";var v0=Vc(),_0=zc();Ns.read=function(t,e){return v0(t,e||{})};Ns.write=function(t,e){return _0(t,e)}});var Zc=E(Kc=>{"use strict";var x0=require("util"),Yc=require("stream"),y0=_c(),b0=Tc(),w0=Wc(),it=Kc.PNG=function(t){Yc.call(this),t=t||{},this.width=t.width|0,this.height=t.height|0,this.data=this.width>0&&this.height>0?Buffer.alloc(4*this.width*this.height):null,t.fill&&this.data&&this.data.fill(0),this.gamma=0,this.readable=this.writable=!0,this._parser=new y0(t),this._parser.on("error",this.emit.bind(this,"error")),this._parser.on("close",this._handleClose.bind(this)),this._parser.on("metadata",this._metadata.bind(this)),this._parser.on("gamma",this._gamma.bind(this)),this._parser.on("parsed",function(e){this.data=e,this.emit("parsed",e)}.bind(this)),this._packer=new b0(t),this._packer.on("data",this.emit.bind(this,"data")),this._packer.on("end",this.emit.bind(this,"end")),this._parser.on("close",this._handleClose.bind(this)),this._packer.on("error",this.emit.bind(this,"error"))};x0.inherits(it,Yc);it.sync=w0;it.prototype.pack=function(){return!this.data||!this.data.length?(this.emit("error","No data provided"),this):(process.nextTick(function(){this._packer.pack(this.data,this.width,this.height,this.gamma)}.bind(this)),this)};it.prototype.parse=function(t,e){if(e){let i,r;i=function(n){this.removeListener("error",r),this.data=n,e(null,this)}.bind(this),r=function(n){this.removeListener("parsed",i),e(n,null)}.bind(this),this.once("parsed",i),this.once("error",r)}return this.end(t),this};it.prototype.write=function(t){return this._parser.write(t),!0};it.prototype.end=function(t){this._parser.end(t)};it.prototype._metadata=function(t){this.width=t.width,this.height=t.height,this.emit("metadata",t)};it.prototype._gamma=function(t){this.gamma=t};it.prototype._handleClose=function(){!this._parser.writable&&!this._packer.readable&&this.emit("close")};it.bitblt=function(t,e,i,r,n,s,o,c){if(i|=0,r|=0,n|=0,s|=0,o|=0,c|=0,i>t.width||r>t.height||i+n>t.width||r+s>t.height)throw new Error("bitblt reading outside image");if(o>e.width||c>e.height||o+n>e.width||c+s>e.height)throw new Error("bitblt writing outside image");for(let u=0;u<s;u++)t.data.copy(e.data,(c+u)*e.width+o<<2,(r+u)*t.width+i<<2,(r+u)*t.width+i+n<<2)};it.prototype.bitblt=function(t,e,i,r,n,s,o){return it.bitblt(this,t,e,i,r,n,s,o),this};it.adjustGamma=function(t){if(t.gamma){for(let e=0;e<t.height;e++)for(let i=0;i<t.width;i++){let r=t.width*e+i<<2;for(let n=0;n<3;n++){let s=t.data[r+n]/255;s=Math.pow(s,1/2.2/t.gamma),t.data[r+n]=Math.round(s*255)}}t.gamma=0}};it.prototype.adjustGamma=function(){it.adjustGamma(this)}});var mr=E(Fs=>{var sn=class extends Error{constructor(e,i,r){super(r),Error.captureStackTrace(this,this.constructor),this.name=this.constructor.name,this.code=i,this.exitCode=e,this.nestedError=void 0}},Ls=class extends sn{constructor(e){super(1,"commander.invalidArgument",e),Error.captureStackTrace(this,this.constructor),this.name=this.constructor.name}};Fs.CommanderError=sn;Fs.InvalidArgumentError=Ls});var on=E(Ds=>{var{InvalidArgumentError:E0}=mr(),Ms=class{constructor(e,i){switch(this.description=i||"",this.variadic=!1,this.parseArg=void 0,this.defaultValue=void 0,this.defaultValueDescription=void 0,this.argChoices=void 0,e[0]){case"<":this.required=!0,this._name=e.slice(1,-1);break;case"[":this.required=!1,this._name=e.slice(1,-1);break;default:this.required=!0,this._name=e;break}this._name.length>3&&this._name.slice(-3)==="..."&&(this.variadic=!0,this._name=this._name.slice(0,-3))}name(){return this._name}_concatValue(e,i){return i===this.defaultValue||!Array.isArray(i)?[e]:i.concat(e)}default(e,i){return this.defaultValue=e,this.defaultValueDescription=i,this}argParser(e){return this.parseArg=e,this}choices(e){return this.argChoices=e,this.parseArg=(i,r)=>{if(!e.includes(i))throw new E0(`Allowed choices are ${e.join(", ")}.`);return this.variadic?this._concatValue(i,r):i},this}argRequired(){return this.required=!0,this}argOptional(){return this.required=!1,this}};function S0(t){let e=t.name()+(t.variadic===!0?"...":"");return t.required?"<"+e+">":"["+e+"]"}Ds.Argument=Ms;Ds.humanReadableArgName=S0});var js=E(Xc=>{var{humanReadableArgName:k0}=on(),Us=class{constructor(){this.helpWidth=void 0,this.sortSubcommands=!1,this.sortOptions=!1}visibleCommands(e){let i=e.commands.filter(r=>!r._hidden);if(e._hasImplicitHelpCommand()){let[,r,n]=e._helpCommandnameAndArgs.match(/([^ ]+) *(.*)/),s=e.createCommand(r).helpOption(!1);s.description(e._helpCommandDescription),n&&s.arguments(n),i.push(s)}return this.sortSubcommands&&i.sort((r,n)=>r.name().localeCompare(n.name())),i}visibleOptions(e){let i=e.options.filter(s=>!s.hidden),r=e._hasHelpOption&&e._helpShortFlag&&!e._findOption(e._helpShortFlag),n=e._hasHelpOption&&!e._findOption(e._helpLongFlag);if(r||n){let s;r?n?s=e.createOption(e._helpFlags,e._helpDescription):s=e.createOption(e._helpShortFlag,e._helpDescription):s=e.createOption(e._helpLongFlag,e._helpDescription),i.push(s)}if(this.sortOptions){let s=o=>o.short?o.short.replace(/^-/,""):o.long.replace(/^--/,"");i.sort((o,c)=>s(o).localeCompare(s(c)))}return i}visibleArguments(e){return e._argsDescription&&e._args.forEach(i=>{i.description=i.description||e._argsDescription[i.name()]||""}),e._args.find(i=>i.description)?e._args:[]}subcommandTerm(e){let i=e._args.map(r=>k0(r)).join(" ");return e._name+(e._aliases[0]?"|"+e._aliases[0]:"")+(e.options.length?" [options]":"")+(i?" "+i:"")}optionTerm(e){return e.flags}argumentTerm(e){return e.name()}longestSubcommandTermLength(e,i){return i.visibleCommands(e).reduce((r,n)=>Math.max(r,i.subcommandTerm(n).length),0)}longestOptionTermLength(e,i){return i.visibleOptions(e).reduce((r,n)=>Math.max(r,i.optionTerm(n).length),0)}longestArgumentTermLength(e,i){return i.visibleArguments(e).reduce((r,n)=>Math.max(r,i.argumentTerm(n).length),0)}commandUsage(e){let i=e._name;e._aliases[0]&&(i=i+"|"+e._aliases[0]);let r="";for(let n=e.parent;n;n=n.parent)r=n.name()+" "+r;return r+i+" "+e.usage()}commandDescription(e){return e.description()}subcommandDescription(e){return e.description()}optionDescription(e){let i=[];return e.argChoices&&!e.negate&&i.push(`choices: ${e.argChoices.map(r=>JSON.stringify(r)).join(", ")}`),e.defaultValue!==void 0&&!e.negate&&i.push(`default: ${e.defaultValueDescription||JSON.stringify(e.defaultValue)}`),e.envVar!==void 0&&i.push(`env: ${e.envVar}`),i.length>0?`${e.description} (${i.join(", ")})`:e.description}argumentDescription(e){let i=[];if(e.argChoices&&i.push(`choices: ${e.argChoices.map(r=>JSON.stringify(r)).join(", ")}`),e.defaultValue!==void 0&&i.push(`default: ${e.defaultValueDescription||JSON.stringify(e.defaultValue)}`),i.length>0){let r=`(${i.join(", ")})`;return e.description?`${e.description} ${r}`:r}return e.description}formatHelp(e,i){let r=i.padWidth(e,i),n=i.helpWidth||80,s=2,o=2;function c(g,x){if(x){let y=`${g.padEnd(r+o)}${x}`;return i.wrap(y,n-s,r+o)}return g}function u(g){return g.join(`
`).replace(/^/gm," ".repeat(s))}let h=[`Usage: ${i.commandUsage(e)}`,""],l=i.commandDescription(e);l.length>0&&(h=h.concat([l,""]));let d=i.visibleArguments(e).map(g=>c(i.argumentTerm(g),i.argumentDescription(g)));d.length>0&&(h=h.concat(["Arguments:",u(d),""]));let m=i.visibleOptions(e).map(g=>c(i.optionTerm(g),i.optionDescription(g)));m.length>0&&(h=h.concat(["Options:",u(m),""]));let v=i.visibleCommands(e).map(g=>c(i.subcommandTerm(g),i.subcommandDescription(g)));return v.length>0&&(h=h.concat(["Commands:",u(v),""])),h.join(`
`)}padWidth(e,i){return Math.max(i.longestOptionTermLength(e,i),i.longestSubcommandTermLength(e,i),i.longestArgumentTermLength(e,i))}wrap(e,i,r,n=40){if(e.match(/[\n]\s+/))return e;let s=i-r;if(s<n)return e;let o=e.substr(0,r),c=e.substr(r),u=" ".repeat(r),h=new RegExp(".{1,"+(s-1)+"}([\\s\u200B]|$)|[^\\s\u200B]+?([\\s\u200B]|$)","g"),l=c.match(h)||[];return o+l.map((d,m)=>(d.slice(-1)===`
`&&(d=d.slice(0,d.length-1)),(m>0?u:"")+d.trimRight())).join(`
`)}};Xc.Help=Us});var Hs=E(Vs=>{var{InvalidArgumentError:O0}=mr(),qs=class{constructor(e,i){this.flags=e,this.description=i||"",this.required=e.includes("<"),this.optional=e.includes("["),this.variadic=/\w\.\.\.[>\]]$/.test(e),this.mandatory=!1;let r=Jc(e);this.short=r.shortFlag,this.long=r.longFlag,this.negate=!1,this.long&&(this.negate=this.long.startsWith("--no-")),this.defaultValue=void 0,this.defaultValueDescription=void 0,this.envVar=void 0,this.parseArg=void 0,this.hidden=!1,this.argChoices=void 0}default(e,i){return this.defaultValue=e,this.defaultValueDescription=i,this}env(e){return this.envVar=e,this}argParser(e){return this.parseArg=e,this}makeOptionMandatory(e=!0){return this.mandatory=!!e,this}hideHelp(e=!0){return this.hidden=!!e,this}_concatValue(e,i){return i===this.defaultValue||!Array.isArray(i)?[e]:i.concat(e)}choices(e){return this.argChoices=e,this.parseArg=(i,r)=>{if(!e.includes(i))throw new O0(`Allowed choices are ${e.join(", ")}.`);return this.variadic?this._concatValue(i,r):i},this}name(){return this.long?this.long.replace(/^--/,""):this.short.replace(/^-/,"")}attributeName(){return C0(this.name().replace(/^no-/,""))}is(e){return this.short===e||this.long===e}};function C0(t){return t.split("-").reduce((e,i)=>e+i[0].toUpperCase()+i.slice(1))}function Jc(t){let e,i,r=t.split(/[ |,]+/);return r.length>1&&!/^[[<]/.test(r[1])&&(e=r.shift()),i=r.shift(),!e&&/^-[^-]$/.test(i)&&(e=i,i=void 0),{shortFlag:e,longFlag:i}}Vs.Option=qs;Vs.splitOptionFlags=Jc});var eu=E(Qc=>{function T0(t,e){if(Math.abs(t.length-e.length)>3)return Math.max(t.length,e.length);let i=[];for(let r=0;r<=t.length;r++)i[r]=[r];for(let r=0;r<=e.length;r++)i[0][r]=r;for(let r=1;r<=e.length;r++)for(let n=1;n<=t.length;n++){let s=1;t[n-1]===e[r-1]?s=0:s=1,i[n][r]=Math.min(i[n-1][r]+1,i[n][r-1]+1,i[n-1][r-1]+s),n>1&&r>1&&t[n-1]===e[r-2]&&t[n-2]===e[r-1]&&(i[n][r]=Math.min(i[n][r],i[n-2][r-2]+1))}return i[t.length][e.length]}function I0(t,e){if(!e||e.length===0)return"";e=Array.from(new Set(e));let i=t.startsWith("--");i&&(t=t.slice(2),e=e.map(o=>o.slice(2)));let r=[],n=3,s=.4;return e.forEach(o=>{if(o.length<=1)return;let c=T0(t,o),u=Math.max(t.length,o.length);(u-c)/u>s&&(c<n?(n=c,r=[o]):c===n&&r.push(o))}),r.sort((o,c)=>o.localeCompare(c)),i&&(r=r.map(o=>`--${o}`)),r.length>1?`
(Did you mean one of ${r.join(", ")}?)`:r.length===1?`
(Did you mean ${r[0]}?)`:""}Qc.suggestSimilar=I0});var su=E(nu=>{var A0=require("events").EventEmitter,$s=require("child_process"),vi=require("path"),Gs=require("fs"),{Argument:B0,humanReadableArgName:R0}=on(),{CommanderError:zs}=mr(),{Help:P0}=js(),{Option:N0,splitOptionFlags:L0}=Hs(),{suggestSimilar:tu}=eu(),Ys=class t extends A0{constructor(e){super(),this.commands=[],this.options=[],this.parent=null,this._allowUnknownOption=!1,this._allowExcessArguments=!0,this._args=[],this.args=[],this.rawArgs=[],this.processedArgs=[],this._scriptPath=null,this._name=e||"",this._optionValues={},this._optionValueSources={},this._storeOptionsAsProperties=!1,this._actionHandler=null,this._executableHandler=!1,this._executableFile=null,this._defaultCommandName=null,this._exitCallback=null,this._aliases=[],this._combineFlagAndOptionalValue=!0,this._description="",this._argsDescription=void 0,this._enablePositionalOptions=!1,this._passThroughOptions=!1,this._lifeCycleHooks={},this._showHelpAfterError=!1,this._showSuggestionAfterError=!1,this._outputConfiguration={writeOut:i=>process.stdout.write(i),writeErr:i=>process.stderr.write(i),getOutHelpWidth:()=>process.stdout.isTTY?process.stdout.columns:void 0,getErrHelpWidth:()=>process.stderr.isTTY?process.stderr.columns:void 0,outputError:(i,r)=>r(i)},this._hidden=!1,this._hasHelpOption=!0,this._helpFlags="-h, --help",this._helpDescription="display help for command",this._helpShortFlag="-h",this._helpLongFlag="--help",this._addImplicitHelpCommand=void 0,this._helpCommandName="help",this._helpCommandnameAndArgs="help [command]",this._helpCommandDescription="display help for command",this._helpConfiguration={}}copyInheritedSettings(e){return this._outputConfiguration=e._outputConfiguration,this._hasHelpOption=e._hasHelpOption,this._helpFlags=e._helpFlags,this._helpDescription=e._helpDescription,this._helpShortFlag=e._helpShortFlag,this._helpLongFlag=e._helpLongFlag,this._helpCommandName=e._helpCommandName,this._helpCommandnameAndArgs=e._helpCommandnameAndArgs,this._helpCommandDescription=e._helpCommandDescription,this._helpConfiguration=e._helpConfiguration,this._exitCallback=e._exitCallback,this._storeOptionsAsProperties=e._storeOptionsAsProperties,this._combineFlagAndOptionalValue=e._combineFlagAndOptionalValue,this._allowExcessArguments=e._allowExcessArguments,this._enablePositionalOptions=e._enablePositionalOptions,this._showHelpAfterError=e._showHelpAfterError,this._showSuggestionAfterError=e._showSuggestionAfterError,this}command(e,i,r){let n=i,s=r;typeof n=="object"&&n!==null&&(s=n,n=null),s=s||{};let[,o,c]=e.match(/([^ ]+) *(.*)/),u=this.createCommand(o);return n&&(u.description(n),u._executableHandler=!0),s.isDefault&&(this._defaultCommandName=u._name),u._hidden=!!(s.noHelp||s.hidden),u._executableFile=s.executableFile||null,c&&u.arguments(c),this.commands.push(u),u.parent=this,u.copyInheritedSettings(this),n?this:u}createCommand(e){return new t(e)}createHelp(){return Object.assign(new P0,this.configureHelp())}configureHelp(e){return e===void 0?this._helpConfiguration:(this._helpConfiguration=e,this)}configureOutput(e){return e===void 0?this._outputConfiguration:(Object.assign(this._outputConfiguration,e),this)}showHelpAfterError(e=!0){return typeof e!="string"&&(e=!!e),this._showHelpAfterError=e,this}showSuggestionAfterError(e=!0){return this._showSuggestionAfterError=!!e,this}addCommand(e,i){if(!e._name)throw new Error("Command passed to .addCommand() must have a name");function r(n){n.forEach(s=>{if(s._executableHandler&&!s._executableFile)throw new Error(`Must specify executableFile for deeply nested executable: ${s.name()}`);r(s.commands)})}return r(e.commands),i=i||{},i.isDefault&&(this._defaultCommandName=e._name),(i.noHelp||i.hidden)&&(e._hidden=!0),this.commands.push(e),e.parent=this,this}createArgument(e,i){return new B0(e,i)}argument(e,i,r,n){let s=this.createArgument(e,i);return typeof r=="function"?s.default(n).argParser(r):s.default(r),this.addArgument(s),this}arguments(e){return e.split(/ +/).forEach(i=>{this.argument(i)}),this}addArgument(e){let i=this._args.slice(-1)[0];if(i&&i.variadic)throw new Error(`only the last argument can be variadic '${i.name()}'`);if(e.required&&e.defaultValue!==void 0&&e.parseArg===void 0)throw new Error(`a default value for a required argument is never used: '${e.name()}'`);return this._args.push(e),this}addHelpCommand(e,i){return e===!1?this._addImplicitHelpCommand=!1:(this._addImplicitHelpCommand=!0,typeof e=="string"&&(this._helpCommandName=e.split(" ")[0],this._helpCommandnameAndArgs=e),this._helpCommandDescription=i||this._helpCommandDescription),this}_hasImplicitHelpCommand(){return this._addImplicitHelpCommand===void 0?this.commands.length&&!this._actionHandler&&!this._findCommand("help"):this._addImplicitHelpCommand}hook(e,i){let r=["preAction","postAction"];if(!r.includes(e))throw new Error(`Unexpected value for event passed to hook : '${e}'.
Expecting one of '${r.join("', '")}'`);return this._lifeCycleHooks[e]?this._lifeCycleHooks[e].push(i):this._lifeCycleHooks[e]=[i],this}exitOverride(e){return e?this._exitCallback=e:this._exitCallback=i=>{if(i.code!=="commander.executeSubCommandAsync")throw i},this}_exit(e,i,r){this._exitCallback&&this._exitCallback(new zs(e,i,r)),process.exit(e)}action(e){let i=r=>{let n=this._args.length,s=r.slice(0,n);return this._storeOptionsAsProperties?s[n]=this:s[n]=this.opts(),s.push(this),e.apply(this,s)};return this._actionHandler=i,this}createOption(e,i){return new N0(e,i)}addOption(e){let i=e.name(),r=e.attributeName(),n=e.defaultValue;if(e.negate||e.optional||e.required||typeof n=="boolean"){if(e.negate){let o=e.long.replace(/^--no-/,"--");n=this._findOption(o)?this.getOptionValue(r):!0}n!==void 0&&this.setOptionValueWithSource(r,n,"default")}this.options.push(e);let s=(o,c,u)=>{let h=this.getOptionValue(r);if(o!==null&&e.parseArg)try{o=e.parseArg(o,h===void 0?n:h)}catch(l){if(l.code==="commander.invalidArgument"){let d=`${c} ${l.message}`;this._displayError(l.exitCode,l.code,d)}throw l}else o!==null&&e.variadic&&(o=e._concatValue(o,h));typeof h=="boolean"||typeof h=="undefined"?o==null?this.setOptionValueWithSource(r,e.negate?!1:n||!0,u):this.setOptionValueWithSource(r,o,u):o!==null&&this.setOptionValueWithSource(r,e.negate?!1:o,u)};return this.on("option:"+i,o=>{let c=`error: option '${e.flags}' argument '${o}' is invalid.`;s(o,c,"cli")}),e.envVar&&this.on("optionEnv:"+i,o=>{let c=`error: option '${e.flags}' value '${o}' from env '${e.envVar}' is invalid.`;s(o,c,"env")}),this}_optionEx(e,i,r,n,s){let o=this.createOption(i,r);if(o.makeOptionMandatory(!!e.mandatory),typeof n=="function")o.default(s).argParser(n);else if(n instanceof RegExp){let c=n;n=(u,h)=>{let l=c.exec(u);return l?l[0]:h},o.default(s).argParser(n)}else o.default(n);return this.addOption(o)}option(e,i,r,n){return this._optionEx({},e,i,r,n)}requiredOption(e,i,r,n){return this._optionEx({mandatory:!0},e,i,r,n)}combineFlagAndOptionalValue(e=!0){return this._combineFlagAndOptionalValue=!!e,this}allowUnknownOption(e=!0){return this._allowUnknownOption=!!e,this}allowExcessArguments(e=!0){return this._allowExcessArguments=!!e,this}enablePositionalOptions(e=!0){return this._enablePositionalOptions=!!e,this}passThroughOptions(e=!0){if(this._passThroughOptions=!!e,this.parent&&e&&!this.parent._enablePositionalOptions)throw new Error("passThroughOptions can not be used without turning on enablePositionalOptions for parent command(s)");return this}storeOptionsAsProperties(e=!0){if(this._storeOptionsAsProperties=!!e,this.options.length)throw new Error("call .storeOptionsAsProperties() before adding options");return this}getOptionValue(e){return this._storeOptionsAsProperties?this[e]:this._optionValues[e]}setOptionValue(e,i){return this._storeOptionsAsProperties?this[e]=i:this._optionValues[e]=i,this}setOptionValueWithSource(e,i,r){return this.setOptionValue(e,i),this._optionValueSources[e]=r,this}getOptionValueSource(e){return this._optionValueSources[e]}_prepareUserArgs(e,i){if(e!==void 0&&!Array.isArray(e))throw new Error("first parameter to parse must be array or undefined");i=i||{},e===void 0&&(e=process.argv,process.versions&&process.versions.electron&&(i.from="electron")),this.rawArgs=e.slice();let r;switch(i.from){case void 0:case"node":this._scriptPath=e[1],r=e.slice(2);break;case"electron":process.defaultApp?(this._scriptPath=e[1],r=e.slice(2)):r=e.slice(1);break;case"user":r=e.slice(0);break;default:throw new Error(`unexpected parse option { from: '${i.from}' }`)}return!this._scriptPath&&require.main&&(this._scriptPath=require.main.filename),this._name=this._name||this._scriptPath&&vi.basename(this._scriptPath,vi.extname(this._scriptPath)),r}parse(e,i){let r=this._prepareUserArgs(e,i);return this._parseCommand([],r),this}async parseAsync(e,i){let r=this._prepareUserArgs(e,i);return await this._parseCommand([],r),this}_executeSubCommand(e,i){i=i.slice();let r=!1,n=[".js",".ts",".tsx",".mjs",".cjs"];this._checkForMissingMandatoryOptions();let s=this._scriptPath;!s&&require.main&&(s=require.main.filename);let o;try{let m=Gs.realpathSync(s);o=vi.dirname(m)}catch{o="."}let c=vi.basename(s,vi.extname(s))+"-"+e._name;e._executableFile&&(c=e._executableFile);let u=vi.join(o,c);Gs.existsSync(u)?c=u:n.forEach(m=>{Gs.existsSync(`${u}${m}`)&&(c=`${u}${m}`)}),r=n.includes(vi.extname(c));let h;process.platform!=="win32"?r?(i.unshift(c),i=ru(process.execArgv).concat(i),h=$s.spawn(process.argv[0],i,{stdio:"inherit"})):h=$s.spawn(c,i,{stdio:"inherit"}):(i.unshift(c),i=ru(process.execArgv).concat(i),h=$s.spawn(process.execPath,i,{stdio:"inherit"})),["SIGUSR1","SIGUSR2","SIGTERM","SIGINT","SIGHUP"].forEach(m=>{process.on(m,()=>{h.killed===!1&&h.exitCode===null&&h.kill(m)})});let d=this._exitCallback;d?h.on("close",()=>{d(new zs(process.exitCode||0,"commander.executeSubCommandAsync","(close)"))}):h.on("close",process.exit.bind(process)),h.on("error",m=>{if(m.code==="ENOENT"){let v=`'${c}' does not exist
 - if '${e._name}' is not meant to be an executable command, remove description parameter from '.command()' and use '.description()' instead
 - if the default executable name is not suitable, use the executableFile option to supply a custom name`;throw new Error(v)}else if(m.code==="EACCES")throw new Error(`'${c}' not executable`);if(!d)process.exit(1);else{let v=new zs(1,"commander.executeSubCommandAsync","(error)");v.nestedError=m,d(v)}}),this.runningCommand=h}_dispatchSubcommand(e,i,r){let n=this._findCommand(e);if(n||this.help({error:!0}),n._executableHandler)this._executeSubCommand(n,i.concat(r));else return n._parseCommand(i,r)}_checkNumberOfArguments(){this._args.forEach((e,i)=>{e.required&&this.args[i]==null&&this.missingArgument(e.name())}),!(this._args.length>0&&this._args[this._args.length-1].variadic)&&this.args.length>this._args.length&&this._excessArguments(this.args)}_processArguments(){let e=(r,n,s)=>{let o=n;if(n!==null&&r.parseArg)try{o=r.parseArg(n,s)}catch(c){if(c.code==="commander.invalidArgument"){let u=`error: command-argument value '${n}' is invalid for argument '${r.name()}'. ${c.message}`;this._displayError(c.exitCode,c.code,u)}throw c}return o};this._checkNumberOfArguments();let i=[];this._args.forEach((r,n)=>{let s=r.defaultValue;r.variadic?n<this.args.length?(s=this.args.slice(n),r.parseArg&&(s=s.reduce((o,c)=>e(r,c,o),r.defaultValue))):s===void 0&&(s=[]):n<this.args.length&&(s=this.args[n],r.parseArg&&(s=e(r,s,r.defaultValue))),i[n]=s}),this.processedArgs=i}_chainOrCall(e,i){return e&&e.then&&typeof e.then=="function"?e.then(()=>i()):i()}_chainOrCallHooks(e,i){let r=e,n=[];return Ws(this).reverse().filter(s=>s._lifeCycleHooks[i]!==void 0).forEach(s=>{s._lifeCycleHooks[i].forEach(o=>{n.push({hookedCommand:s,callback:o})})}),i==="postAction"&&n.reverse(),n.forEach(s=>{r=this._chainOrCall(r,()=>s.callback(s.hookedCommand,this))}),r}_parseCommand(e,i){let r=this.parseOptions(i);if(this._parseOptionsEnv(),e=e.concat(r.operands),i=r.unknown,this.args=e.concat(i),e&&this._findCommand(e[0]))return this._dispatchSubcommand(e[0],e.slice(1),i);if(this._hasImplicitHelpCommand()&&e[0]===this._helpCommandName)return e.length===1&&this.help(),this._dispatchSubcommand(e[1],[],[this._helpLongFlag]);if(this._defaultCommandName)return iu(this,i),this._dispatchSubcommand(this._defaultCommandName,e,i);this.commands.length&&this.args.length===0&&!this._actionHandler&&!this._defaultCommandName&&this.help({error:!0}),iu(this,r.unknown),this._checkForMissingMandatoryOptions();let n=()=>{r.unknown.length>0&&this.unknownOption(r.unknown[0])},s=`command:${this.name()}`;if(this._actionHandler){n(),this._processArguments();let o;return o=this._chainOrCallHooks(o,"preAction"),o=this._chainOrCall(o,()=>this._actionHandler(this.processedArgs)),this.parent&&this.parent.emit(s,e,i),o=this._chainOrCallHooks(o,"postAction"),o}if(this.parent&&this.parent.listenerCount(s))n(),this._processArguments(),this.parent.emit(s,e,i);else if(e.length){if(this._findCommand("*"))return this._dispatchSubcommand("*",e,i);this.listenerCount("command:*")?this.emit("command:*",e,i):this.commands.length?this.unknownCommand():(n(),this._processArguments())}else this.commands.length?(n(),this.help({error:!0})):(n(),this._processArguments())}_findCommand(e){if(e)return this.commands.find(i=>i._name===e||i._aliases.includes(e))}_findOption(e){return this.options.find(i=>i.is(e))}_checkForMissingMandatoryOptions(){for(let e=this;e;e=e.parent)e.options.forEach(i=>{i.mandatory&&e.getOptionValue(i.attributeName())===void 0&&e.missingMandatoryOptionValue(i)})}parseOptions(e){let i=[],r=[],n=i,s=e.slice();function o(u){return u.length>1&&u[0]==="-"}let c=null;for(;s.length;){let u=s.shift();if(u==="--"){n===r&&n.push(u),n.push(...s);break}if(c&&!o(u)){this.emit(`option:${c.name()}`,u);continue}if(c=null,o(u)){let h=this._findOption(u);if(h){if(h.required){let l=s.shift();l===void 0&&this.optionMissingArgument(h),this.emit(`option:${h.name()}`,l)}else if(h.optional){let l=null;s.length>0&&!o(s[0])&&(l=s.shift()),this.emit(`option:${h.name()}`,l)}else this.emit(`option:${h.name()}`);c=h.variadic?h:null;continue}}if(u.length>2&&u[0]==="-"&&u[1]!=="-"){let h=this._findOption(`-${u[1]}`);if(h){h.required||h.optional&&this._combineFlagAndOptionalValue?this.emit(`option:${h.name()}`,u.slice(2)):(this.emit(`option:${h.name()}`),s.unshift(`-${u.slice(2)}`));continue}}if(/^--[^=]+=/.test(u)){let h=u.indexOf("="),l=this._findOption(u.slice(0,h));if(l&&(l.required||l.optional)){this.emit(`option:${l.name()}`,u.slice(h+1));continue}}if(o(u)&&(n=r),(this._enablePositionalOptions||this._passThroughOptions)&&i.length===0&&r.length===0){if(this._findCommand(u)){i.push(u),s.length>0&&r.push(...s);break}else if(u===this._helpCommandName&&this._hasImplicitHelpCommand()){i.push(u),s.length>0&&i.push(...s);break}else if(this._defaultCommandName){r.push(u),s.length>0&&r.push(...s);break}}if(this._passThroughOptions){n.push(u),s.length>0&&n.push(...s);break}n.push(u)}return{operands:i,unknown:r}}opts(){if(this._storeOptionsAsProperties){let e={},i=this.options.length;for(let r=0;r<i;r++){let n=this.options[r].attributeName();e[n]=n===this._versionOptionName?this._version:this[n]}return e}return this._optionValues}_displayError(e,i,r){this._outputConfiguration.outputError(`${r}
`,this._outputConfiguration.writeErr),typeof this._showHelpAfterError=="string"?this._outputConfiguration.writeErr(`${this._showHelpAfterError}
`):this._showHelpAfterError&&(this._outputConfiguration.writeErr(`
`),this.outputHelp({error:!0})),this._exit(e,i,r)}_parseOptionsEnv(){this.options.forEach(e=>{if(e.envVar&&e.envVar in process.env){let i=e.attributeName();(this.getOptionValue(i)===void 0||["default","config","env"].includes(this.getOptionValueSource(i)))&&(e.required||e.optional?this.emit(`optionEnv:${e.name()}`,process.env[e.envVar]):this.emit(`optionEnv:${e.name()}`))}})}missingArgument(e){let i=`error: missing required argument '${e}'`;this._displayError(1,"commander.missingArgument",i)}optionMissingArgument(e){let i=`error: option '${e.flags}' argument missing`;this._displayError(1,"commander.optionMissingArgument",i)}missingMandatoryOptionValue(e){let i=`error: required option '${e.flags}' not specified`;this._displayError(1,"commander.missingMandatoryOptionValue",i)}unknownOption(e){if(this._allowUnknownOption)return;let i="";if(e.startsWith("--")&&this._showSuggestionAfterError){let n=[],s=this;do{let o=s.createHelp().visibleOptions(s).filter(c=>c.long).map(c=>c.long);n=n.concat(o),s=s.parent}while(s&&!s._enablePositionalOptions);i=tu(e,n)}let r=`error: unknown option '${e}'${i}`;this._displayError(1,"commander.unknownOption",r)}_excessArguments(e){if(this._allowExcessArguments)return;let i=this._args.length,r=i===1?"":"s",s=`error: too many arguments${this.parent?` for '${this.name()}'`:""}. Expected ${i} argument${r} but got ${e.length}.`;this._displayError(1,"commander.excessArguments",s)}unknownCommand(){let e=this.args[0],i="";if(this._showSuggestionAfterError){let n=[];this.createHelp().visibleCommands(this).forEach(s=>{n.push(s.name()),s.alias()&&n.push(s.alias())}),i=tu(e,n)}let r=`error: unknown command '${e}'${i}`;this._displayError(1,"commander.unknownCommand",r)}version(e,i,r){if(e===void 0)return this._version;this._version=e,i=i||"-V, --version",r=r||"output the version number";let n=this.createOption(i,r);return this._versionOptionName=n.attributeName(),this.options.push(n),this.on("option:"+n.name(),()=>{this._outputConfiguration.writeOut(`${e}
`),this._exit(0,"commander.version",e)}),this}description(e,i){return e===void 0&&i===void 0?this._description:(this._description=e,i&&(this._argsDescription=i),this)}alias(e){if(e===void 0)return this._aliases[0];let i=this;if(this.commands.length!==0&&this.commands[this.commands.length-1]._executableHandler&&(i=this.commands[this.commands.length-1]),e===i._name)throw new Error("Command alias can't be the same as its name");return i._aliases.push(e),this}aliases(e){return e===void 0?this._aliases:(e.forEach(i=>this.alias(i)),this)}usage(e){if(e===void 0){if(this._usage)return this._usage;let i=this._args.map(r=>R0(r));return[].concat(this.options.length||this._hasHelpOption?"[options]":[],this.commands.length?"[command]":[],this._args.length?i:[]).join(" ")}return this._usage=e,this}name(e){return e===void 0?this._name:(this._name=e,this)}helpInformation(e){let i=this.createHelp();return i.helpWidth===void 0&&(i.helpWidth=e&&e.error?this._outputConfiguration.getErrHelpWidth():this._outputConfiguration.getOutHelpWidth()),i.formatHelp(this,i)}_getHelpContext(e){e=e||{};let i={error:!!e.error},r;return i.error?r=n=>this._outputConfiguration.writeErr(n):r=n=>this._outputConfiguration.writeOut(n),i.write=e.write||r,i.command=this,i}outputHelp(e){let i;typeof e=="function"&&(i=e,e=void 0);let r=this._getHelpContext(e);Ws(this).reverse().forEach(s=>s.emit("beforeAllHelp",r)),this.emit("beforeHelp",r);let n=this.helpInformation(r);if(i&&(n=i(n),typeof n!="string"&&!Buffer.isBuffer(n)))throw new Error("outputHelp callback must return a string or a Buffer");r.write(n),this.emit(this._helpLongFlag),this.emit("afterHelp",r),Ws(this).forEach(s=>s.emit("afterAllHelp",r))}helpOption(e,i){if(typeof e=="boolean")return this._hasHelpOption=e,this;this._helpFlags=e||this._helpFlags,this._helpDescription=i||this._helpDescription;let r=L0(this._helpFlags);return this._helpShortFlag=r.shortFlag,this._helpLongFlag=r.longFlag,this}help(e){this.outputHelp(e);let i=process.exitCode||0;i===0&&e&&typeof e!="function"&&e.error&&(i=1),this._exit(i,"commander.help","(outputHelp)")}addHelpText(e,i){let r=["beforeAll","before","after","afterAll"];if(!r.includes(e))throw new Error(`Unexpected value for position to addHelpText.
Expecting one of '${r.join("', '")}'`);let n=`${e}Help`;return this.on(n,s=>{let o;typeof i=="function"?o=i({error:s.error,command:s.command}):o=i,o&&s.write(`${o}
`)}),this}};function iu(t,e){t._hasHelpOption&&e.find(r=>r===t._helpLongFlag||r===t._helpShortFlag)&&(t.outputHelp(),t._exit(0,"commander.helpDisplayed","(outputHelp)"))}function ru(t){return t.map(e=>{if(!e.startsWith("--inspect"))return e;let i,r="127.0.0.1",n="9229",s;return(s=e.match(/^(--inspect(-brk)?)$/))!==null?i=s[1]:(s=e.match(/^(--inspect(-brk|-port)?)=([^:]+)$/))!==null?(i=s[1],/^\d+$/.test(s[3])?n=s[3]:r=s[3]):(s=e.match(/^(--inspect(-brk|-port)?)=([^:]+):(\d+)$/))!==null&&(i=s[1],r=s[3],n=s[4]),i&&n!=="0"?`${i}=${r}:${parseInt(n)+1}`:e})}function Ws(t){let e=[];for(let i=t;i;i=i.parent)e.push(i);return e}nu.Command=Ys});var cu=E((Tt,lu)=>{var{Argument:F0}=on(),{Command:ou}=su(),{CommanderError:M0,InvalidArgumentError:au}=mr(),{Help:D0}=js(),{Option:U0}=Hs();Tt=lu.exports=new ou;Tt.program=Tt;Tt.Argument=F0;Tt.Command=ou;Tt.CommanderError=M0;Tt.Help=D0;Tt.InvalidArgumentError=au;Tt.InvalidOptionArgumentError=au;Tt.Option=U0});var hu=E((uu,fu)=>{uu=fu.exports=Wi;function Wi(t,e){if(this.stream=e.stream||process.stderr,typeof e=="number"){var i=e;e={},e.total=i}else{if(e=e||{},typeof t!="string")throw new Error("format required");if(typeof e.total!="number")throw new Error("total required")}this.fmt=t,this.curr=e.curr||0,this.total=e.total,this.width=e.width||this.total,this.clear=e.clear,this.chars={complete:e.complete||"=",incomplete:e.incomplete||"-",head:e.head||e.complete||"="},this.renderThrottle=e.renderThrottle!==0?e.renderThrottle||16:0,this.lastRender=-1/0,this.callback=e.callback||function(){},this.tokens={},this.lastDraw=""}Wi.prototype.tick=function(t,e){if(t!==0&&(t=t||1),typeof t=="object"&&(e=t,t=1),e&&(this.tokens=e),this.curr==0&&(this.start=new Date),this.curr+=t,this.render(),this.curr>=this.total){this.render(void 0,!0),this.complete=!0,this.terminate(),this.callback(this);return}};Wi.prototype.render=function(t,e){if(e=e!==void 0?e:!1,t&&(this.tokens=t),!!this.stream.isTTY){var i=Date.now(),r=i-this.lastRender;if(!(!e&&r<this.renderThrottle)){this.lastRender=i;var n=this.curr/this.total;n=Math.min(Math.max(n,0),1);var s=Math.floor(n*100),o,c,u,h=new Date-this.start,l=s==100?0:h*(this.total/this.curr-1),d=this.curr/(h/1e3),m=this.fmt.replace(":current",this.curr).replace(":total",this.total).replace(":elapsed",isNaN(h)?"0.0":(h/1e3).toFixed(1)).replace(":eta",isNaN(l)||!isFinite(l)?"0.0":(l/1e3).toFixed(1)).replace(":percent",s.toFixed(0)+"%").replace(":rate",Math.round(d)),v=Math.max(0,this.stream.columns-m.replace(":bar","").length);v&&process.platform==="win32"&&(v=v-1);var g=Math.min(this.width,v);if(u=Math.round(g*n),c=Array(Math.max(0,u+1)).join(this.chars.complete),o=Array(Math.max(0,g-u+1)).join(this.chars.incomplete),u>0&&(c=c.slice(0,-1)+this.chars.head),m=m.replace(":bar",c+o),this.tokens)for(var x in this.tokens)m=m.replace(":"+x,this.tokens[x]);this.lastDraw!==m&&(this.stream.cursorTo(0),this.stream.write(m),this.stream.clearLine(1),this.lastDraw=m)}}};Wi.prototype.update=function(t,e){var i=Math.floor(t*this.total),r=i-this.curr;this.tick(r,e)};Wi.prototype.interrupt=function(t){this.stream.clearLine(),this.stream.cursorTo(0),this.stream.write(t),this.stream.write(`
`),this.stream.write(this.lastDraw)};Wi.prototype.terminate=function(){this.clear?this.stream.clearLine&&(this.stream.clearLine(),this.stream.cursorTo(0)):this.stream.write(`
`)}});var du=E((iy,pu)=>{pu.exports=hu()});var _u=E(Vt=>{"use strict";Object.defineProperty(Vt,"__esModule",{value:!0});var mu=require("buffer"),_i={INVALID_ENCODING:"Invalid encoding provided. Please specify a valid encoding the internal Node.js Buffer supports.",INVALID_SMARTBUFFER_SIZE:"Invalid size provided. Size must be a valid integer greater than zero.",INVALID_SMARTBUFFER_BUFFER:"Invalid Buffer provided in SmartBufferOptions.",INVALID_SMARTBUFFER_OBJECT:"Invalid SmartBufferOptions object supplied to SmartBuffer constructor or factory methods.",INVALID_OFFSET:"An invalid offset value was provided.",INVALID_OFFSET_NON_NUMBER:"An invalid offset value was provided. A numeric value is required.",INVALID_LENGTH:"An invalid length value was provided.",INVALID_LENGTH_NON_NUMBER:"An invalid length value was provived. A numeric value is required.",INVALID_TARGET_OFFSET:"Target offset is beyond the bounds of the internal SmartBuffer data.",INVALID_TARGET_LENGTH:"Specified length value moves cursor beyong the bounds of the internal SmartBuffer data.",INVALID_READ_BEYOND_BOUNDS:"Attempted to read beyond the bounds of the managed data.",INVALID_WRITE_BEYOND_BOUNDS:"Attempted to write beyond the bounds of the managed data."};Vt.ERRORS=_i;function j0(t){if(!mu.Buffer.isEncoding(t))throw new Error(_i.INVALID_ENCODING)}Vt.checkEncoding=j0;function gu(t){return typeof t=="number"&&isFinite(t)&&$0(t)}Vt.isFiniteInteger=gu;function vu(t,e){if(typeof t=="number"){if(!gu(t)||t<0)throw new Error(e?_i.INVALID_OFFSET:_i.INVALID_LENGTH)}else throw new Error(e?_i.INVALID_OFFSET_NON_NUMBER:_i.INVALID_LENGTH_NON_NUMBER)}function q0(t){vu(t,!1)}Vt.checkLengthValue=q0;function V0(t){vu(t,!0)}Vt.checkOffsetValue=V0;function H0(t,e){if(t<0||t>e.length)throw new Error(_i.INVALID_TARGET_OFFSET)}Vt.checkTargetOffset=H0;function $0(t){return typeof t=="number"&&isFinite(t)&&Math.floor(t)===t}function G0(t){if(typeof BigInt=="undefined")throw new Error("Platform does not support JS BigInt type.");if(typeof mu.Buffer.prototype[t]=="undefined")throw new Error(`Platform does not support Buffer.prototype.${t}.`)}Vt.bigIntAndBufferInt64Check=G0});var yu=E(Zs=>{"use strict";Object.defineProperty(Zs,"__esModule",{value:!0});var he=_u(),xu=4096,z0="utf8",Ks=class t{constructor(e){if(this.length=0,this._encoding=z0,this._writeOffset=0,this._readOffset=0,t.isSmartBufferOptions(e))if(e.encoding&&(he.checkEncoding(e.encoding),this._encoding=e.encoding),e.size)if(he.isFiniteInteger(e.size)&&e.size>0)this._buff=Buffer.allocUnsafe(e.size);else throw new Error(he.ERRORS.INVALID_SMARTBUFFER_SIZE);else if(e.buff)if(Buffer.isBuffer(e.buff))this._buff=e.buff,this.length=e.buff.length;else throw new Error(he.ERRORS.INVALID_SMARTBUFFER_BUFFER);else this._buff=Buffer.allocUnsafe(xu);else{if(typeof e!="undefined")throw new Error(he.ERRORS.INVALID_SMARTBUFFER_OBJECT);this._buff=Buffer.allocUnsafe(xu)}}static fromSize(e,i){return new this({size:e,encoding:i})}static fromBuffer(e,i){return new this({buff:e,encoding:i})}static fromOptions(e){return new this(e)}static isSmartBufferOptions(e){let i=e;return i&&(i.encoding!==void 0||i.size!==void 0||i.buff!==void 0)}readInt8(e){return this._readNumberValue(Buffer.prototype.readInt8,1,e)}readInt16BE(e){return this._readNumberValue(Buffer.prototype.readInt16BE,2,e)}readInt16LE(e){return this._readNumberValue(Buffer.prototype.readInt16LE,2,e)}readInt32BE(e){return this._readNumberValue(Buffer.prototype.readInt32BE,4,e)}readInt32LE(e){return this._readNumberValue(Buffer.prototype.readInt32LE,4,e)}readBigInt64BE(e){return he.bigIntAndBufferInt64Check("readBigInt64BE"),this._readNumberValue(Buffer.prototype.readBigInt64BE,8,e)}readBigInt64LE(e){return he.bigIntAndBufferInt64Check("readBigInt64LE"),this._readNumberValue(Buffer.prototype.readBigInt64LE,8,e)}writeInt8(e,i){return this._writeNumberValue(Buffer.prototype.writeInt8,1,e,i),this}insertInt8(e,i){return this._insertNumberValue(Buffer.prototype.writeInt8,1,e,i)}writeInt16BE(e,i){return this._writeNumberValue(Buffer.prototype.writeInt16BE,2,e,i)}insertInt16BE(e,i){return this._insertNumberValue(Buffer.prototype.writeInt16BE,2,e,i)}writeInt16LE(e,i){return this._writeNumberValue(Buffer.prototype.writeInt16LE,2,e,i)}insertInt16LE(e,i){return this._insertNumberValue(Buffer.prototype.writeInt16LE,2,e,i)}writeInt32BE(e,i){return this._writeNumberValue(Buffer.prototype.writeInt32BE,4,e,i)}insertInt32BE(e,i){return this._insertNumberValue(Buffer.prototype.writeInt32BE,4,e,i)}writeInt32LE(e,i){return this._writeNumberValue(Buffer.prototype.writeInt32LE,4,e,i)}insertInt32LE(e,i){return this._insertNumberValue(Buffer.prototype.writeInt32LE,4,e,i)}writeBigInt64BE(e,i){return he.bigIntAndBufferInt64Check("writeBigInt64BE"),this._writeNumberValue(Buffer.prototype.writeBigInt64BE,8,e,i)}insertBigInt64BE(e,i){return he.bigIntAndBufferInt64Check("writeBigInt64BE"),this._insertNumberValue(Buffer.prototype.writeBigInt64BE,8,e,i)}writeBigInt64LE(e,i){return he.bigIntAndBufferInt64Check("writeBigInt64LE"),this._writeNumberValue(Buffer.prototype.writeBigInt64LE,8,e,i)}insertBigInt64LE(e,i){return he.bigIntAndBufferInt64Check("writeBigInt64LE"),this._insertNumberValue(Buffer.prototype.writeBigInt64LE,8,e,i)}readUInt8(e){return this._readNumberValue(Buffer.prototype.readUInt8,1,e)}readUInt16BE(e){return this._readNumberValue(Buffer.prototype.readUInt16BE,2,e)}readUInt16LE(e){return this._readNumberValue(Buffer.prototype.readUInt16LE,2,e)}readUInt32BE(e){return this._readNumberValue(Buffer.prototype.readUInt32BE,4,e)}readUInt32LE(e){return this._readNumberValue(Buffer.prototype.readUInt32LE,4,e)}readBigUInt64BE(e){return he.bigIntAndBufferInt64Check("readBigUInt64BE"),this._readNumberValue(Buffer.prototype.readBigUInt64BE,8,e)}readBigUInt64LE(e){return he.bigIntAndBufferInt64Check("readBigUInt64LE"),this._readNumberValue(Buffer.prototype.readBigUInt64LE,8,e)}writeUInt8(e,i){return this._writeNumberValue(Buffer.prototype.writeUInt8,1,e,i)}insertUInt8(e,i){return this._insertNumberValue(Buffer.prototype.writeUInt8,1,e,i)}writeUInt16BE(e,i){return this._writeNumberValue(Buffer.prototype.writeUInt16BE,2,e,i)}insertUInt16BE(e,i){return this._insertNumberValue(Buffer.prototype.writeUInt16BE,2,e,i)}writeUInt16LE(e,i){return this._writeNumberValue(Buffer.prototype.writeUInt16LE,2,e,i)}insertUInt16LE(e,i){return this._insertNumberValue(Buffer.prototype.writeUInt16LE,2,e,i)}writeUInt32BE(e,i){return this._writeNumberValue(Buffer.prototype.writeUInt32BE,4,e,i)}insertUInt32BE(e,i){return this._insertNumberValue(Buffer.prototype.writeUInt32BE,4,e,i)}writeUInt32LE(e,i){return this._writeNumberValue(Buffer.prototype.writeUInt32LE,4,e,i)}insertUInt32LE(e,i){return this._insertNumberValue(Buffer.prototype.writeUInt32LE,4,e,i)}writeBigUInt64BE(e,i){return he.bigIntAndBufferInt64Check("writeBigUInt64BE"),this._writeNumberValue(Buffer.prototype.writeBigUInt64BE,8,e,i)}insertBigUInt64BE(e,i){return he.bigIntAndBufferInt64Check("writeBigUInt64BE"),this._insertNumberValue(Buffer.prototype.writeBigUInt64BE,8,e,i)}writeBigUInt64LE(e,i){return he.bigIntAndBufferInt64Check("writeBigUInt64LE"),this._writeNumberValue(Buffer.prototype.writeBigUInt64LE,8,e,i)}insertBigUInt64LE(e,i){return he.bigIntAndBufferInt64Check("writeBigUInt64LE"),this._insertNumberValue(Buffer.prototype.writeBigUInt64LE,8,e,i)}readFloatBE(e){return this._readNumberValue(Buffer.prototype.readFloatBE,4,e)}readFloatLE(e){return this._readNumberValue(Buffer.prototype.readFloatLE,4,e)}writeFloatBE(e,i){return this._writeNumberValue(Buffer.prototype.writeFloatBE,4,e,i)}insertFloatBE(e,i){return this._insertNumberValue(Buffer.prototype.writeFloatBE,4,e,i)}writeFloatLE(e,i){return this._writeNumberValue(Buffer.prototype.writeFloatLE,4,e,i)}insertFloatLE(e,i){return this._insertNumberValue(Buffer.prototype.writeFloatLE,4,e,i)}readDoubleBE(e){return this._readNumberValue(Buffer.prototype.readDoubleBE,8,e)}readDoubleLE(e){return this._readNumberValue(Buffer.prototype.readDoubleLE,8,e)}writeDoubleBE(e,i){return this._writeNumberValue(Buffer.prototype.writeDoubleBE,8,e,i)}insertDoubleBE(e,i){return this._insertNumberValue(Buffer.prototype.writeDoubleBE,8,e,i)}writeDoubleLE(e,i){return this._writeNumberValue(Buffer.prototype.writeDoubleLE,8,e,i)}insertDoubleLE(e,i){return this._insertNumberValue(Buffer.prototype.writeDoubleLE,8,e,i)}readString(e,i){let r;typeof e=="number"?(he.checkLengthValue(e),r=Math.min(e,this.length-this._readOffset)):(i=e,r=this.length-this._readOffset),typeof i!="undefined"&&he.checkEncoding(i);let n=this._buff.slice(this._readOffset,this._readOffset+r).toString(i||this._encoding);return this._readOffset+=r,n}insertString(e,i,r){return he.checkOffsetValue(i),this._handleString(e,!0,i,r)}writeString(e,i,r){return this._handleString(e,!1,i,r)}readStringNT(e){typeof e!="undefined"&&he.checkEncoding(e);let i=this.length;for(let n=this._readOffset;n<this.length;n++)if(this._buff[n]===0){i=n;break}let r=this._buff.slice(this._readOffset,i);return this._readOffset=i+1,r.toString(e||this._encoding)}insertStringNT(e,i,r){return he.checkOffsetValue(i),this.insertString(e,i,r),this.insertUInt8(0,i+e.length),this}writeStringNT(e,i,r){return this.writeString(e,i,r),this.writeUInt8(0,typeof i=="number"?i+e.length:this.writeOffset),this}readBuffer(e){typeof e!="undefined"&&he.checkLengthValue(e);let i=typeof e=="number"?e:this.length,r=Math.min(this.length,this._readOffset+i),n=this._buff.slice(this._readOffset,r);return this._readOffset=r,n}insertBuffer(e,i){return he.checkOffsetValue(i),this._handleBuffer(e,!0,i)}writeBuffer(e,i){return this._handleBuffer(e,!1,i)}readBufferNT(){let e=this.length;for(let r=this._readOffset;r<this.length;r++)if(this._buff[r]===0){e=r;break}let i=this._buff.slice(this._readOffset,e);return this._readOffset=e+1,i}insertBufferNT(e,i){return he.checkOffsetValue(i),this.insertBuffer(e,i),this.insertUInt8(0,i+e.length),this}writeBufferNT(e,i){return typeof i!="undefined"&&he.checkOffsetValue(i),this.writeBuffer(e,i),this.writeUInt8(0,typeof i=="number"?i+e.length:this._writeOffset),this}clear(){return this._writeOffset=0,this._readOffset=0,this.length=0,this}remaining(){return this.length-this._readOffset}get readOffset(){return this._readOffset}set readOffset(e){he.checkOffsetValue(e),he.checkTargetOffset(e,this),this._readOffset=e}get writeOffset(){return this._writeOffset}set writeOffset(e){he.checkOffsetValue(e),he.checkTargetOffset(e,this),this._writeOffset=e}get encoding(){return this._encoding}set encoding(e){he.checkEncoding(e),this._encoding=e}get internalBuffer(){return this._buff}toBuffer(){return this._buff.slice(0,this.length)}toString(e){let i=typeof e=="string"?e:this._encoding;return he.checkEncoding(i),this._buff.toString(i,0,this.length)}destroy(){return this.clear(),this}_handleString(e,i,r,n){let s=this._writeOffset,o=this._encoding;typeof r=="number"?s=r:typeof r=="string"&&(he.checkEncoding(r),o=r),typeof n=="string"&&(he.checkEncoding(n),o=n);let c=Buffer.byteLength(e,o);return i?this.ensureInsertable(c,s):this._ensureWriteable(c,s),this._buff.write(e,s,c,o),i?this._writeOffset+=c:typeof r=="number"?this._writeOffset=Math.max(this._writeOffset,s+c):this._writeOffset+=c,this}_handleBuffer(e,i,r){let n=typeof r=="number"?r:this._writeOffset;return i?this.ensureInsertable(e.length,n):this._ensureWriteable(e.length,n),e.copy(this._buff,n),i?this._writeOffset+=e.length:typeof r=="number"?this._writeOffset=Math.max(this._writeOffset,n+e.length):this._writeOffset+=e.length,this}ensureReadable(e,i){let r=this._readOffset;if(typeof i!="undefined"&&(he.checkOffsetValue(i),r=i),r<0||r+e>this.length)throw new Error(he.ERRORS.INVALID_READ_BEYOND_BOUNDS)}ensureInsertable(e,i){he.checkOffsetValue(i),this._ensureCapacity(this.length+e),i<this.length&&this._buff.copy(this._buff,i+e,i,this._buff.length),i+e>this.length?this.length=i+e:this.length+=e}_ensureWriteable(e,i){let r=typeof i=="number"?i:this._writeOffset;this._ensureCapacity(r+e),r+e>this.length&&(this.length=r+e)}_ensureCapacity(e){let i=this._buff.length;if(e>i){let r=this._buff,n=i*3/2+1;n<e&&(n=e),this._buff=Buffer.allocUnsafe(n),r.copy(this._buff,0,0,i)}}_readNumberValue(e,i,r){this.ensureReadable(i,r);let n=e.call(this._buff,typeof r=="number"?r:this._readOffset);return typeof r=="undefined"&&(this._readOffset+=i),n}_insertNumberValue(e,i,r,n){return he.checkOffsetValue(n),this.ensureInsertable(i,n),e.call(this._buff,r,n),this._writeOffset+=i,this}_writeNumberValue(e,i,r,n){if(typeof n=="number"){if(n<0)throw new Error(he.ERRORS.INVALID_WRITE_BEYOND_BOUNDS);he.checkOffsetValue(n)}let s=typeof n=="number"?n:this._writeOffset;return this._ensureWriteable(i,s),e.call(this._buff,r,s),typeof n=="number"?this._writeOffset=Math.max(this._writeOffset,s+i):this._writeOffset+=i,this}};Zs.SmartBuffer=Ks});var Xs=E(Ie=>{"use strict";Object.defineProperty(Ie,"__esModule",{value:!0});Ie.SOCKS5_NO_ACCEPTABLE_AUTH=Ie.SOCKS5_CUSTOM_AUTH_END=Ie.SOCKS5_CUSTOM_AUTH_START=Ie.SOCKS_INCOMING_PACKET_SIZES=Ie.SocksClientState=Ie.Socks5Response=Ie.Socks5HostType=Ie.Socks5Auth=Ie.Socks4Response=Ie.SocksCommand=Ie.ERRORS=Ie.DEFAULT_TIMEOUT=void 0;var W0=3e4;Ie.DEFAULT_TIMEOUT=W0;var Y0={InvalidSocksCommand:"An invalid SOCKS command was provided. Valid options are connect, bind, and associate.",InvalidSocksCommandForOperation:"An invalid SOCKS command was provided. Only a subset of commands are supported for this operation.",InvalidSocksCommandChain:"An invalid SOCKS command was provided. Chaining currently only supports the connect command.",InvalidSocksClientOptionsDestination:"An invalid destination host was provided.",InvalidSocksClientOptionsExistingSocket:"An invalid existing socket was provided. This should be an instance of stream.Duplex.",InvalidSocksClientOptionsProxy:"Invalid SOCKS proxy details were provided.",InvalidSocksClientOptionsTimeout:"An invalid timeout value was provided. Please enter a value above 0 (in ms).",InvalidSocksClientOptionsProxiesLength:"At least two socks proxies must be provided for chaining.",InvalidSocksClientOptionsCustomAuthRange:"Custom auth must be a value between 0x80 and 0xFE.",InvalidSocksClientOptionsCustomAuthOptions:"When a custom_auth_method is provided, custom_auth_request_handler, custom_auth_response_size, and custom_auth_response_handler must also be provided and valid.",NegotiationError:"Negotiation error",SocketClosed:"Socket closed",ProxyConnectionTimedOut:"Proxy connection timed out",InternalError:"SocksClient internal error (this should not happen)",InvalidSocks4HandshakeResponse:"Received invalid Socks4 handshake response",Socks4ProxyRejectedConnection:"Socks4 Proxy rejected connection",InvalidSocks4IncomingConnectionResponse:"Socks4 invalid incoming connection response",Socks4ProxyRejectedIncomingBoundConnection:"Socks4 Proxy rejected incoming bound connection",InvalidSocks5InitialHandshakeResponse:"Received invalid Socks5 initial handshake response",InvalidSocks5IntiailHandshakeSocksVersion:"Received invalid Socks5 initial handshake (invalid socks version)",InvalidSocks5InitialHandshakeNoAcceptedAuthType:"Received invalid Socks5 initial handshake (no accepted authentication type)",InvalidSocks5InitialHandshakeUnknownAuthType:"Received invalid Socks5 initial handshake (unknown authentication type)",Socks5AuthenticationFailed:"Socks5 Authentication failed",InvalidSocks5FinalHandshake:"Received invalid Socks5 final handshake response",InvalidSocks5FinalHandshakeRejected:"Socks5 proxy rejected connection",InvalidSocks5IncomingConnectionResponse:"Received invalid Socks5 incoming connection response",Socks5ProxyRejectedIncomingBoundConnection:"Socks5 Proxy rejected incoming bound connection"};Ie.ERRORS=Y0;var K0={Socks5InitialHandshakeResponse:2,Socks5UserPassAuthenticationResponse:2,Socks5ResponseHeader:5,Socks5ResponseIPv4:10,Socks5ResponseIPv6:22,Socks5ResponseHostname:t=>t+7,Socks4Response:8};Ie.SOCKS_INCOMING_PACKET_SIZES=K0;var bu;(function(t){t[t.connect=1]="connect",t[t.bind=2]="bind",t[t.associate=3]="associate"})(bu||(Ie.SocksCommand=bu={}));var wu;(function(t){t[t.Granted=90]="Granted",t[t.Failed=91]="Failed",t[t.Rejected=92]="Rejected",t[t.RejectedIdent=93]="RejectedIdent"})(wu||(Ie.Socks4Response=wu={}));var Eu;(function(t){t[t.NoAuth=0]="NoAuth",t[t.GSSApi=1]="GSSApi",t[t.UserPass=2]="UserPass"})(Eu||(Ie.Socks5Auth=Eu={}));var Z0=128;Ie.SOCKS5_CUSTOM_AUTH_START=Z0;var X0=254;Ie.SOCKS5_CUSTOM_AUTH_END=X0;var J0=255;Ie.SOCKS5_NO_ACCEPTABLE_AUTH=J0;var Su;(function(t){t[t.Granted=0]="Granted",t[t.Failure=1]="Failure",t[t.NotAllowed=2]="NotAllowed",t[t.NetworkUnreachable=3]="NetworkUnreachable",t[t.HostUnreachable=4]="HostUnreachable",t[t.ConnectionRefused=5]="ConnectionRefused",t[t.TTLExpired=6]="TTLExpired",t[t.CommandNotSupported=7]="CommandNotSupported",t[t.AddressNotSupported=8]="AddressNotSupported"})(Su||(Ie.Socks5Response=Su={}));var ku;(function(t){t[t.IPv4=1]="IPv4",t[t.Hostname=3]="Hostname",t[t.IPv6=4]="IPv6"})(ku||(Ie.Socks5HostType=ku={}));var Ou;(function(t){t[t.Created=0]="Created",t[t.Connecting=1]="Connecting",t[t.Connected=2]="Connected",t[t.SentInitialHandshake=3]="SentInitialHandshake",t[t.ReceivedInitialHandshakeResponse=4]="ReceivedInitialHandshakeResponse",t[t.SentAuthentication=5]="SentAuthentication",t[t.ReceivedAuthenticationResponse=6]="ReceivedAuthenticationResponse",t[t.SentFinalHandshake=7]="SentFinalHandshake",t[t.ReceivedFinalResponse=8]="ReceivedFinalResponse",t[t.BoundWaitingForConnection=9]="BoundWaitingForConnection",t[t.Established=10]="Established",t[t.Disconnected=11]="Disconnected",t[t.Error=99]="Error"})(Ou||(Ie.SocksClientState=Ou={}))});var Qs=E(Yi=>{"use strict";Object.defineProperty(Yi,"__esModule",{value:!0});Yi.shuffleArray=Yi.SocksClientError=void 0;var Js=class extends Error{constructor(e,i){super(e),this.options=i}};Yi.SocksClientError=Js;function Q0(t){for(let e=t.length-1;e>0;e--){let i=Math.floor(Math.random()*(e+1));[t[e],t[i]]=[t[i],t[e]]}}Yi.shuffleArray=Q0});var eo=E(Ki=>{"use strict";Object.defineProperty(Ki,"__esModule",{value:!0});Ki.isCorrect=Ki.isInSubnet=void 0;function eg(t){return this.subnetMask<t.subnetMask?!1:this.mask(t.subnetMask)===t.mask()}Ki.isInSubnet=eg;function tg(t){return function(){return this.addressMinusSuffix!==this.correctForm()?!1:this.subnetMask===t&&!this.parsedSubnet?!0:this.parsedSubnet===String(this.subnetMask)}}Ki.isCorrect=tg});var to=E(Pt=>{"use strict";Object.defineProperty(Pt,"__esModule",{value:!0});Pt.RE_SUBNET_STRING=Pt.RE_ADDRESS=Pt.GROUPS=Pt.BITS=void 0;Pt.BITS=32;Pt.GROUPS=4;Pt.RE_ADDRESS=/^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/g;Pt.RE_SUBNET_STRING=/\/\d{1,2}$/});var ln=E(an=>{"use strict";Object.defineProperty(an,"__esModule",{value:!0});an.AddressError=void 0;var io=class extends Error{constructor(e,i){super(e),this.name="AddressError",i!==null&&(this.parseMessage=i)}};an.AddressError=io});var ro=E((cn,Cu)=>{(function(){var t,e=0xdeadbeefcafe,i=(e&16777215)==15715070;function r(a,f,p){a!=null&&(typeof a=="number"?this.fromNumber(a,f,p):f==null&&typeof a!="string"?this.fromString(a,256):this.fromString(a,f))}function n(){return new r(null)}function s(a,f,p,_,T,I){for(;--I>=0;){var U=f*this[a++]+p[_]+T;T=Math.floor(U/67108864),p[_++]=U&67108863}return T}function o(a,f,p,_,T,I){for(var U=f&32767,q=f>>15;--I>=0;){var Re=this[a]&32767,Ve=this[a++]>>15,vt=q*Re+Ve*U;Re=U*Re+((vt&32767)<<15)+p[_]+(T&1073741823),T=(Re>>>30)+(vt>>>15)+q*Ve+(T>>>30),p[_++]=Re&1073741823}return T}function c(a,f,p,_,T,I){for(var U=f&16383,q=f>>14;--I>=0;){var Re=this[a]&16383,Ve=this[a++]>>14,vt=q*Re+Ve*U;Re=U*Re+((vt&16383)<<14)+p[_]+T,T=(Re>>28)+(vt>>14)+q*Ve,p[_++]=Re&268435455}return T}var u=typeof navigator!="undefined";u&&i&&navigator.appName=="Microsoft Internet Explorer"?(r.prototype.am=o,t=30):u&&i&&navigator.appName!="Netscape"?(r.prototype.am=s,t=26):(r.prototype.am=c,t=28),r.prototype.DB=t,r.prototype.DM=(1<<t)-1,r.prototype.DV=1<<t;var h=52;r.prototype.FV=Math.pow(2,h),r.prototype.F1=h-t,r.prototype.F2=2*t-h;var l="0123456789abcdefghijklmnopqrstuvwxyz",d=new Array,m,v;for(m="0".charCodeAt(0),v=0;v<=9;++v)d[m++]=v;for(m="a".charCodeAt(0),v=10;v<36;++v)d[m++]=v;for(m="A".charCodeAt(0),v=10;v<36;++v)d[m++]=v;function g(a){return l.charAt(a)}function x(a,f){var p=d[a.charCodeAt(f)];return p==null?-1:p}function y(a){for(var f=this.t-1;f>=0;--f)a[f]=this[f];a.t=this.t,a.s=this.s}function O(a){this.t=1,this.s=a<0?-1:0,a>0?this[0]=a:a<-1?this[0]=a+this.DV:this.t=0}function B(a){var f=n();return f.fromInt(a),f}function C(a,f){var p;if(f==16)p=4;else if(f==8)p=3;else if(f==256)p=8;else if(f==2)p=1;else if(f==32)p=5;else if(f==4)p=2;else{this.fromRadix(a,f);return}this.t=0,this.s=0;for(var _=a.length,T=!1,I=0;--_>=0;){var U=p==8?a[_]&255:x(a,_);if(U<0){a.charAt(_)=="-"&&(T=!0);continue}T=!1,I==0?this[this.t++]=U:I+p>this.DB?(this[this.t-1]|=(U&(1<<this.DB-I)-1)<<I,this[this.t++]=U>>this.DB-I):this[this.t-1]|=U<<I,I+=p,I>=this.DB&&(I-=this.DB)}p==8&&a[0]&128&&(this.s=-1,I>0&&(this[this.t-1]|=(1<<this.DB-I)-1<<I)),this.clamp(),T&&r.ZERO.subTo(this,this)}function P(){for(var a=this.s&this.DM;this.t>0&&this[this.t-1]==a;)--this.t}function S(a){if(this.s<0)return"-"+this.negate().toString(a);var f;if(a==16)f=4;else if(a==8)f=3;else if(a==2)f=1;else if(a==32)f=5;else if(a==4)f=2;else return this.toRadix(a);var p=(1<<f)-1,_,T=!1,I="",U=this.t,q=this.DB-U*this.DB%f;if(U-- >0)for(q<this.DB&&(_=this[U]>>q)>0&&(T=!0,I=g(_));U>=0;)q<f?(_=(this[U]&(1<<q)-1)<<f-q,_|=this[--U]>>(q+=this.DB-f)):(_=this[U]>>(q-=f)&p,q<=0&&(q+=this.DB,--U)),_>0&&(T=!0),T&&(I+=g(_));return T?I:"0"}function J(){var a=n();return r.ZERO.subTo(this,a),a}function A(){return this.s<0?this.negate():this}function z(a){var f=this.s-a.s;if(f!=0)return f;var p=this.t;if(f=p-a.t,f!=0)return this.s<0?-f:f;for(;--p>=0;)if((f=this[p]-a[p])!=0)return f;return 0}function k(a){var f=1,p;return(p=a>>>16)!=0&&(a=p,f+=16),(p=a>>8)!=0&&(a=p,f+=8),(p=a>>4)!=0&&(a=p,f+=4),(p=a>>2)!=0&&(a=p,f+=2),(p=a>>1)!=0&&(a=p,f+=1),f}function L(){return this.t<=0?0:this.DB*(this.t-1)+k(this[this.t-1]^this.s&this.DM)}function D(a,f){var p;for(p=this.t-1;p>=0;--p)f[p+a]=this[p];for(p=a-1;p>=0;--p)f[p]=0;f.t=this.t+a,f.s=this.s}function X(a,f){for(var p=a;p<this.t;++p)f[p-a]=this[p];f.t=Math.max(this.t-a,0),f.s=this.s}function j(a,f){var p=a%this.DB,_=this.DB-p,T=(1<<_)-1,I=Math.floor(a/this.DB),U=this.s<<p&this.DM,q;for(q=this.t-1;q>=0;--q)f[q+I+1]=this[q]>>_|U,U=(this[q]&T)<<p;for(q=I-1;q>=0;--q)f[q]=0;f[I]=U,f.t=this.t+I+1,f.s=this.s,f.clamp()}function se(a,f){f.s=this.s;var p=Math.floor(a/this.DB);if(p>=this.t){f.t=0;return}var _=a%this.DB,T=this.DB-_,I=(1<<_)-1;f[0]=this[p]>>_;for(var U=p+1;U<this.t;++U)f[U-p-1]|=(this[U]&I)<<T,f[U-p]=this[U]>>_;_>0&&(f[this.t-p-1]|=(this.s&I)<<T),f.t=this.t-p,f.clamp()}function M(a,f){for(var p=0,_=0,T=Math.min(a.t,this.t);p<T;)_+=this[p]-a[p],f[p++]=_&this.DM,_>>=this.DB;if(a.t<this.t){for(_-=a.s;p<this.t;)_+=this[p],f[p++]=_&this.DM,_>>=this.DB;_+=this.s}else{for(_+=this.s;p<a.t;)_-=a[p],f[p++]=_&this.DM,_>>=this.DB;_-=a.s}f.s=_<0?-1:0,_<-1?f[p++]=this.DV+_:_>0&&(f[p++]=_),f.t=p,f.clamp()}function $(a,f){var p=this.abs(),_=a.abs(),T=p.t;for(f.t=T+_.t;--T>=0;)f[T]=0;for(T=0;T<_.t;++T)f[T+p.t]=p.am(0,_[T],f,T,0,p.t);f.s=0,f.clamp(),this.s!=a.s&&r.ZERO.subTo(f,f)}function Y(a){for(var f=this.abs(),p=a.t=2*f.t;--p>=0;)a[p]=0;for(p=0;p<f.t-1;++p){var _=f.am(p,f[p],a,2*p,0,1);(a[p+f.t]+=f.am(p+1,2*f[p],a,2*p+1,_,f.t-p-1))>=f.DV&&(a[p+f.t]-=f.DV,a[p+f.t+1]=1)}a.t>0&&(a[a.t-1]+=f.am(p,f[p],a,2*p,0,1)),a.s=0,a.clamp()}function Q(a,f,p){var _=a.abs();if(!(_.t<=0)){var T=this.abs();if(T.t<_.t){f!=null&&f.fromInt(0),p!=null&&this.copyTo(p);return}p==null&&(p=n());var I=n(),U=this.s,q=a.s,Re=this.DB-k(_[_.t-1]);Re>0?(_.lShiftTo(Re,I),T.lShiftTo(Re,p)):(_.copyTo(I),T.copyTo(p));var Ve=I.t,vt=I[Ve-1];if(vt!=0){var ft=vt*(1<<this.F1)+(Ve>1?I[Ve-2]>>this.F2:0),jt=this.FV/ft,Vr=(1<<this.F1)/ft,Et=1<<this.F2,St=p.t,Hr=St-Ve,Kt=f==null?n():f;for(I.dlShiftTo(Hr,Kt),p.compareTo(Kt)>=0&&(p[p.t++]=1,p.subTo(Kt,p)),r.ONE.dlShiftTo(Ve,Kt),Kt.subTo(I,I);I.t<Ve;)I[I.t++]=0;for(;--Hr>=0;){var $n=p[--St]==vt?this.DM:Math.floor(p[St]*jt+(p[St-1]+Et)*Vr);if((p[St]+=I.am(0,$n,p,Hr,0,Ve))<$n)for(I.dlShiftTo(Hr,Kt),p.subTo(Kt,p);p[St]<--$n;)p.subTo(Kt,p)}f!=null&&(p.drShiftTo(Ve,f),U!=q&&r.ZERO.subTo(f,f)),p.t=Ve,p.clamp(),Re>0&&p.rShiftTo(Re,p),U<0&&r.ZERO.subTo(p,p)}}}function V(a){var f=n();return this.abs().divRemTo(a,null,f),this.s<0&&f.compareTo(r.ZERO)>0&&a.subTo(f,f),f}function we(a){this.m=a}function de(a){return a.s<0||a.compareTo(this.m)>=0?a.mod(this.m):a}function le(a){return a}function ce(a){a.divRemTo(this.m,null,a)}function w(a,f,p){a.multiplyTo(f,p),this.reduce(p)}function K(a,f){a.squareTo(f),this.reduce(f)}we.prototype.convert=de,we.prototype.revert=le,we.prototype.reduce=ce,we.prototype.mulTo=w,we.prototype.sqrTo=K;function Se(){if(this.t<1)return 0;var a=this[0];if(!(a&1))return 0;var f=a&3;return f=f*(2-(a&15)*f)&15,f=f*(2-(a&255)*f)&255,f=f*(2-((a&65535)*f&65535))&65535,f=f*(2-a*f%this.DV)%this.DV,f>0?this.DV-f:-f}function _e(a){this.m=a,this.mp=a.invDigit(),this.mpl=this.mp&32767,this.mph=this.mp>>15,this.um=(1<<a.DB-15)-1,this.mt2=2*a.t}function me(a){var f=n();return a.abs().dlShiftTo(this.m.t,f),f.divRemTo(this.m,null,f),a.s<0&&f.compareTo(r.ZERO)>0&&this.m.subTo(f,f),f}function ge(a){var f=n();return a.copyTo(f),this.reduce(f),f}function ue(a){for(;a.t<=this.mt2;)a[a.t++]=0;for(var f=0;f<this.m.t;++f){var p=a[f]&32767,_=p*this.mpl+((p*this.mph+(a[f]>>15)*this.mpl&this.um)<<15)&a.DM;for(p=f+this.m.t,a[p]+=this.m.am(0,_,a,f,0,this.m.t);a[p]>=a.DV;)a[p]-=a.DV,a[++p]++}a.clamp(),a.drShiftTo(this.m.t,a),a.compareTo(this.m)>=0&&a.subTo(this.m,a)}function H(a,f){a.squareTo(f),this.reduce(f)}function F(a,f,p){a.multiplyTo(f,p),this.reduce(p)}_e.prototype.convert=me,_e.prototype.revert=ge,_e.prototype.reduce=ue,_e.prototype.mulTo=F,_e.prototype.sqrTo=H;function ke(){return(this.t>0?this[0]&1:this.s)==0}function ie(a,f){if(a>4294967295||a<1)return r.ONE;var p=n(),_=n(),T=f.convert(this),I=k(a)-1;for(T.copyTo(p);--I>=0;)if(f.sqrTo(p,_),(a&1<<I)>0)f.mulTo(_,T,p);else{var U=p;p=_,_=U}return f.revert(p)}function fe(a,f){var p;return a<256||f.isEven()?p=new we(f):p=new _e(f),this.exp(a,p)}r.prototype.copyTo=y,r.prototype.fromInt=O,r.prototype.fromString=C,r.prototype.clamp=P,r.prototype.dlShiftTo=D,r.prototype.drShiftTo=X,r.prototype.lShiftTo=j,r.prototype.rShiftTo=se,r.prototype.subTo=M,r.prototype.multiplyTo=$,r.prototype.squareTo=Y,r.prototype.divRemTo=Q,r.prototype.invDigit=Se,r.prototype.isEven=ke,r.prototype.exp=ie,r.prototype.toString=S,r.prototype.negate=J,r.prototype.abs=A,r.prototype.compareTo=z,r.prototype.bitLength=L,r.prototype.mod=V,r.prototype.modPowInt=fe,r.ZERO=B(0),r.ONE=B(1);function ot(){var a=n();return this.copyTo(a),a}function ct(){if(this.s<0){if(this.t==1)return this[0]-this.DV;if(this.t==0)return-1}else{if(this.t==1)return this[0];if(this.t==0)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]}function b(){return this.t==0?this.s:this[0]<<24>>24}function W(){return this.t==0?this.s:this[0]<<16>>16}function ee(a){return Math.floor(Math.LN2*this.DB/Math.log(a))}function Z(){return this.s<0?-1:this.t<=0||this.t==1&&this[0]<=0?0:1}function oe(a){if(a==null&&(a=10),this.signum()==0||a<2||a>36)return"0";var f=this.chunkSize(a),p=Math.pow(a,f),_=B(p),T=n(),I=n(),U="";for(this.divRemTo(_,T,I);T.signum()>0;)U=(p+I.intValue()).toString(a).substr(1)+U,T.divRemTo(_,T,I);return I.intValue().toString(a)+U}function ae(a,f){this.fromInt(0),f==null&&(f=10);for(var p=this.chunkSize(f),_=Math.pow(f,p),T=!1,I=0,U=0,q=0;q<a.length;++q){var Re=x(a,q);if(Re<0){a.charAt(q)=="-"&&this.signum()==0&&(T=!0);continue}U=f*U+Re,++I>=p&&(this.dMultiply(_),this.dAddOffset(U,0),I=0,U=0)}I>0&&(this.dMultiply(Math.pow(f,I)),this.dAddOffset(U,0)),T&&r.ZERO.subTo(this,this)}function xe(a,f,p){if(typeof f=="number")if(a<2)this.fromInt(1);else for(this.fromNumber(a,p),this.testBit(a-1)||this.bitwiseTo(r.ONE.shiftLeft(a-1),ne,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(f);)this.dAddOffset(2,0),this.bitLength()>a&&this.subTo(r.ONE.shiftLeft(a-1),this);else{var _=new Array,T=a&7;_.length=(a>>3)+1,f.nextBytes(_),T>0?_[0]&=(1<<T)-1:_[0]=0,this.fromString(_,256)}}function ye(){var a=this.t,f=new Array;f[0]=this.s;var p=this.DB-a*this.DB%8,_,T=0;if(a-- >0)for(p<this.DB&&(_=this[a]>>p)!=(this.s&this.DM)>>p&&(f[T++]=_|this.s<<this.DB-p);a>=0;)p<8?(_=(this[a]&(1<<p)-1)<<8-p,_|=this[--a]>>(p+=this.DB-8)):(_=this[a]>>(p-=8)&255,p<=0&&(p+=this.DB,--a)),_&128&&(_|=-256),T==0&&(this.s&128)!=(_&128)&&++T,(T>0||_!=this.s)&&(f[T++]=_);return f}function Be(a){return this.compareTo(a)==0}function Oe(a){return this.compareTo(a)<0?this:a}function N(a){return this.compareTo(a)>0?this:a}function G(a,f,p){var _,T,I=Math.min(a.t,this.t);for(_=0;_<I;++_)p[_]=f(this[_],a[_]);if(a.t<this.t){for(T=a.s&this.DM,_=I;_<this.t;++_)p[_]=f(this[_],T);p.t=this.t}else{for(T=this.s&this.DM,_=I;_<a.t;++_)p[_]=f(T,a[_]);p.t=a.t}p.s=f(this.s,a.s),p.clamp()}function re(a,f){return a&f}function Te(a){var f=n();return this.bitwiseTo(a,re,f),f}function ne(a,f){return a|f}function pe(a){var f=n();return this.bitwiseTo(a,ne,f),f}function Ee(a,f){return a^f}function te(a){var f=n();return this.bitwiseTo(a,Ee,f),f}function be(a,f){return a&~f}function Me(a){var f=n();return this.bitwiseTo(a,be,f),f}function Ce(){for(var a=n(),f=0;f<this.t;++f)a[f]=this.DM&~this[f];return a.t=this.t,a.s=~this.s,a}function at(a){var f=n();return a<0?this.rShiftTo(-a,f):this.lShiftTo(a,f),f}function gt(a){var f=n();return a<0?this.lShiftTo(-a,f):this.rShiftTo(a,f),f}function Dt(a){if(a==0)return-1;var f=0;return a&65535||(a>>=16,f+=16),a&255||(a>>=8,f+=8),a&15||(a>>=4,f+=4),a&3||(a>>=2,f+=2),a&1||++f,f}function Wt(){for(var a=0;a<this.t;++a)if(this[a]!=0)return a*this.DB+Dt(this[a]);return this.s<0?this.t*this.DB:-1}function Yt(a){for(var f=0;a!=0;)a&=a-1,++f;return f}function si(){for(var a=0,f=this.s&this.DM,p=0;p<this.t;++p)a+=Yt(this[p]^f);return a}function oi(a){var f=Math.floor(a/this.DB);return f>=this.t?this.s!=0:(this[f]&1<<a%this.DB)!=0}function Bi(a,f){var p=r.ONE.shiftLeft(a);return this.bitwiseTo(p,f,p),p}function ai(a){return this.changeBit(a,ne)}function li(a){return this.changeBit(a,be)}function ci(a){return this.changeBit(a,Ee)}function ui(a,f){for(var p=0,_=0,T=Math.min(a.t,this.t);p<T;)_+=this[p]+a[p],f[p++]=_&this.DM,_>>=this.DB;if(a.t<this.t){for(_+=a.s;p<this.t;)_+=this[p],f[p++]=_&this.DM,_>>=this.DB;_+=this.s}else{for(_+=this.s;p<a.t;)_+=a[p],f[p++]=_&this.DM,_>>=this.DB;_+=a.s}f.s=_<0?-1:0,_>0?f[p++]=_:_<-1&&(f[p++]=this.DV+_),f.t=p,f.clamp()}function Ir(a){var f=n();return this.addTo(a,f),f}function lr(a){var f=n();return this.subTo(a,f),f}function Ar(a){var f=n();return this.multiplyTo(a,f),f}function Br(){var a=n();return this.squareTo(a),a}function Rr(a){var f=n();return this.divRemTo(a,f,null),f}function Pr(a){var f=n();return this.divRemTo(a,null,f),f}function Nr(a){var f=n(),p=n();return this.divRemTo(a,f,p),new Array(f,p)}function jn(a){this[this.t]=this.am(0,a-1,this,0,0,this.t),++this.t,this.clamp()}function fi(a,f){if(a!=0){for(;this.t<=f;)this[this.t++]=0;for(this[f]+=a;this[f]>=this.DV;)this[f]-=this.DV,++f>=this.t&&(this[this.t++]=0),++this[f]}}function Ut(){}function hi(a){return a}function Ri(a,f,p){a.multiplyTo(f,p)}function Lr(a,f){a.squareTo(f)}Ut.prototype.convert=hi,Ut.prototype.revert=hi,Ut.prototype.mulTo=Ri,Ut.prototype.sqrTo=Lr;function Fr(a){return this.exp(a,new Ut)}function Mr(a,f,p){var _=Math.min(this.t+a.t,f);for(p.s=0,p.t=_;_>0;)p[--_]=0;var T;for(T=p.t-this.t;_<T;++_)p[_+this.t]=this.am(0,a[_],p,_,0,this.t);for(T=Math.min(a.t,f);_<T;++_)this.am(0,a[_],p,_,0,f-_);p.clamp()}function Dr(a,f,p){--f;var _=p.t=this.t+a.t-f;for(p.s=0;--_>=0;)p[_]=0;for(_=Math.max(f-this.t,0);_<a.t;++_)p[this.t+_-f]=this.am(f-_,a[_],p,0,0,this.t+_-f);p.clamp(),p.drShiftTo(1,p)}function Bt(a){this.r2=n(),this.q3=n(),r.ONE.dlShiftTo(2*a.t,this.r2),this.mu=this.r2.divide(a),this.m=a}function Ur(a){if(a.s<0||a.t>2*this.m.t)return a.mod(this.m);if(a.compareTo(this.m)<0)return a;var f=n();return a.copyTo(f),this.reduce(f),f}function jr(a){return a}function Pi(a){for(a.drShiftTo(this.m.t-1,this.r2),a.t>this.m.t+1&&(a.t=this.m.t+1,a.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);a.compareTo(this.r2)<0;)a.dAddOffset(1,this.m.t+1);for(a.subTo(this.r2,a);a.compareTo(this.m)>=0;)a.subTo(this.m,a)}function Xh(a,f){a.squareTo(f),this.reduce(f)}function Jh(a,f,p){a.multiplyTo(f,p),this.reduce(p)}Bt.prototype.convert=Ur,Bt.prototype.revert=jr,Bt.prototype.reduce=Pi,Bt.prototype.mulTo=Jh,Bt.prototype.sqrTo=Xh;function Qh(a,f){var p=a.bitLength(),_,T=B(1),I;if(p<=0)return T;p<18?_=1:p<48?_=3:p<144?_=4:p<768?_=5:_=6,p<8?I=new we(f):f.isEven()?I=new Bt(f):I=new _e(f);var U=new Array,q=3,Re=_-1,Ve=(1<<_)-1;if(U[1]=I.convert(this),_>1){var vt=n();for(I.sqrTo(U[1],vt);q<=Ve;)U[q]=n(),I.mulTo(vt,U[q-2],U[q]),q+=2}var ft=a.t-1,jt,Vr=!0,Et=n(),St;for(p=k(a[ft])-1;ft>=0;){for(p>=Re?jt=a[ft]>>p-Re&Ve:(jt=(a[ft]&(1<<p+1)-1)<<Re-p,ft>0&&(jt|=a[ft-1]>>this.DB+p-Re)),q=_;!(jt&1);)jt>>=1,--q;if((p-=q)<0&&(p+=this.DB,--ft),Vr)U[jt].copyTo(T),Vr=!1;else{for(;q>1;)I.sqrTo(T,Et),I.sqrTo(Et,T),q-=2;q>0?I.sqrTo(T,Et):(St=T,T=Et,Et=St),I.mulTo(Et,U[jt],T)}for(;ft>=0&&!(a[ft]&1<<p);)I.sqrTo(T,Et),St=T,T=Et,Et=St,--p<0&&(p=this.DB-1,--ft)}return I.revert(T)}function ep(a){var f=this.s<0?this.negate():this.clone(),p=a.s<0?a.negate():a.clone();if(f.compareTo(p)<0){var _=f;f=p,p=_}var T=f.getLowestSetBit(),I=p.getLowestSetBit();if(I<0)return f;for(T<I&&(I=T),I>0&&(f.rShiftTo(I,f),p.rShiftTo(I,p));f.signum()>0;)(T=f.getLowestSetBit())>0&&f.rShiftTo(T,f),(T=p.getLowestSetBit())>0&&p.rShiftTo(T,p),f.compareTo(p)>=0?(f.subTo(p,f),f.rShiftTo(1,f)):(p.subTo(f,p),p.rShiftTo(1,p));return I>0&&p.lShiftTo(I,p),p}function tp(a){if(a<=0)return 0;var f=this.DV%a,p=this.s<0?a-1:0;if(this.t>0)if(f==0)p=this[0]%a;else for(var _=this.t-1;_>=0;--_)p=(f*p+this[_])%a;return p}function ip(a){var f=a.isEven();if(this.isEven()&&f||a.signum()==0)return r.ZERO;for(var p=a.clone(),_=this.clone(),T=B(1),I=B(0),U=B(0),q=B(1);p.signum()!=0;){for(;p.isEven();)p.rShiftTo(1,p),f?((!T.isEven()||!I.isEven())&&(T.addTo(this,T),I.subTo(a,I)),T.rShiftTo(1,T)):I.isEven()||I.subTo(a,I),I.rShiftTo(1,I);for(;_.isEven();)_.rShiftTo(1,_),f?((!U.isEven()||!q.isEven())&&(U.addTo(this,U),q.subTo(a,q)),U.rShiftTo(1,U)):q.isEven()||q.subTo(a,q),q.rShiftTo(1,q);p.compareTo(_)>=0?(p.subTo(_,p),f&&T.subTo(U,T),I.subTo(q,I)):(_.subTo(p,_),f&&U.subTo(T,U),q.subTo(I,q))}if(_.compareTo(r.ONE)!=0)return r.ZERO;if(q.compareTo(a)>=0)return q.subtract(a);if(q.signum()<0)q.addTo(a,q);else return q;return q.signum()<0?q.add(a):q}var et=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],rp=(1<<26)/et[et.length-1];function np(a){var f,p=this.abs();if(p.t==1&&p[0]<=et[et.length-1]){for(f=0;f<et.length;++f)if(p[0]==et[f])return!0;return!1}if(p.isEven())return!1;for(f=1;f<et.length;){for(var _=et[f],T=f+1;T<et.length&&_<rp;)_*=et[T++];for(_=p.modInt(_);f<T;)if(_%et[f++]==0)return!1}return p.millerRabin(a)}function sp(a){var f=this.subtract(r.ONE),p=f.getLowestSetBit();if(p<=0)return!1;var _=f.shiftRight(p);a=a+1>>1,a>et.length&&(a=et.length);for(var T=n(),I=0;I<a;++I){T.fromInt(et[Math.floor(Math.random()*et.length)]);var U=T.modPow(_,this);if(U.compareTo(r.ONE)!=0&&U.compareTo(f)!=0){for(var q=1;q++<p&&U.compareTo(f)!=0;)if(U=U.modPowInt(2,this),U.compareTo(r.ONE)==0)return!1;if(U.compareTo(f)!=0)return!1}}return!0}r.prototype.chunkSize=ee,r.prototype.toRadix=oe,r.prototype.fromRadix=ae,r.prototype.fromNumber=xe,r.prototype.bitwiseTo=G,r.prototype.changeBit=Bi,r.prototype.addTo=ui,r.prototype.dMultiply=jn,r.prototype.dAddOffset=fi,r.prototype.multiplyLowerTo=Mr,r.prototype.multiplyUpperTo=Dr,r.prototype.modInt=tp,r.prototype.millerRabin=sp,r.prototype.clone=ot,r.prototype.intValue=ct,r.prototype.byteValue=b,r.prototype.shortValue=W,r.prototype.signum=Z,r.prototype.toByteArray=ye,r.prototype.equals=Be,r.prototype.min=Oe,r.prototype.max=N,r.prototype.and=Te,r.prototype.or=pe,r.prototype.xor=te,r.prototype.andNot=Me,r.prototype.not=Ce,r.prototype.shiftLeft=at,r.prototype.shiftRight=gt,r.prototype.getLowestSetBit=Wt,r.prototype.bitCount=si,r.prototype.testBit=oi,r.prototype.setBit=ai,r.prototype.clearBit=li,r.prototype.flipBit=ci,r.prototype.add=Ir,r.prototype.subtract=lr,r.prototype.multiply=Ar,r.prototype.divide=Rr,r.prototype.remainder=Pr,r.prototype.divideAndRemainder=Nr,r.prototype.modPow=Qh,r.prototype.modInverse=ip,r.prototype.pow=Fr,r.prototype.gcd=ep,r.prototype.isProbablePrime=np,r.prototype.square=Br,r.prototype.Barrett=Bt;var qr,ut,qe;function op(a){ut[qe++]^=a&255,ut[qe++]^=a>>8&255,ut[qe++]^=a>>16&255,ut[qe++]^=a>>24&255,qe>=Hn&&(qe-=Hn)}function ia(){op(new Date().getTime())}if(ut==null){ut=new Array,qe=0;var wt;if(typeof window!="undefined"&&window.crypto){if(window.crypto.getRandomValues){var ra=new Uint8Array(32);for(window.crypto.getRandomValues(ra),wt=0;wt<32;++wt)ut[qe++]=ra[wt]}else if(navigator.appName=="Netscape"&&navigator.appVersion<"5"){var na=window.crypto.random(32);for(wt=0;wt<na.length;++wt)ut[qe++]=na.charCodeAt(wt)&255}}for(;qe<Hn;)wt=Math.floor(65536*Math.random()),ut[qe++]=wt>>>8,ut[qe++]=wt&255;qe=0,ia()}function ap(){if(qr==null){for(ia(),qr=fp(),qr.init(ut),qe=0;qe<ut.length;++qe)ut[qe]=0;qe=0}return qr.next()}function lp(a){var f;for(f=0;f<a.length;++f)a[f]=ap()}function qn(){}qn.prototype.nextBytes=lp;function Vn(){this.i=0,this.j=0,this.S=new Array}function cp(a){var f,p,_;for(f=0;f<256;++f)this.S[f]=f;for(p=0,f=0;f<256;++f)p=p+this.S[f]+a[f%a.length]&255,_=this.S[f],this.S[f]=this.S[p],this.S[p]=_;this.i=0,this.j=0}function up(){var a;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,a=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=a,this.S[a+this.S[this.i]&255]}Vn.prototype.init=cp,Vn.prototype.next=up;function fp(){return new Vn}var Hn=256;typeof cn!="undefined"?cn=Cu.exports={default:r,BigInteger:r,SecureRandom:qn}:this.jsbn={BigInteger:r,SecureRandom:qn}}).call(cn)});var gr=E(un=>{(function(){"use strict";var t={not_string:/[^s]/,not_bool:/[^t]/,not_type:/[^T]/,not_primitive:/[^v]/,number:/[diefg]/,numeric_arg:/[bcdiefguxX]/,json:/[j]/,not_json:/[^j]/,text:/^[^\x25]+/,modulo:/^\x25{2}/,placeholder:/^\x25(?:([1-9]\d*)\$|\(([^)]+)\))?(\+)?(0|'[^$])?(-)?(\d+)?(?:\.(\d+))?([b-gijostTuvxX])/,key:/^([a-z_][a-z_\d]*)/i,key_access:/^\.([a-z_][a-z_\d]*)/i,index_access:/^\[(\d+)\]/,sign:/^[+-]/};function e(o){return r(s(o),arguments)}function i(o,c){return e.apply(null,[o].concat(c||[]))}function r(o,c){var u=1,h=o.length,l,d="",m,v,g,x,y,O,B,C;for(m=0;m<h;m++)if(typeof o[m]=="string")d+=o[m];else if(typeof o[m]=="object"){if(g=o[m],g.keys)for(l=c[u],v=0;v<g.keys.length;v++){if(l==null)throw new Error(e('[sprintf] Cannot access property "%s" of undefined value "%s"',g.keys[v],g.keys[v-1]));l=l[g.keys[v]]}else g.param_no?l=c[g.param_no]:l=c[u++];if(t.not_type.test(g.type)&&t.not_primitive.test(g.type)&&l instanceof Function&&(l=l()),t.numeric_arg.test(g.type)&&typeof l!="number"&&isNaN(l))throw new TypeError(e("[sprintf] expecting number but found %T",l));switch(t.number.test(g.type)&&(B=l>=0),g.type){case"b":l=parseInt(l,10).toString(2);break;case"c":l=String.fromCharCode(parseInt(l,10));break;case"d":case"i":l=parseInt(l,10);break;case"j":l=JSON.stringify(l,null,g.width?parseInt(g.width):0);break;case"e":l=g.precision?parseFloat(l).toExponential(g.precision):parseFloat(l).toExponential();break;case"f":l=g.precision?parseFloat(l).toFixed(g.precision):parseFloat(l);break;case"g":l=g.precision?String(Number(l.toPrecision(g.precision))):parseFloat(l);break;case"o":l=(parseInt(l,10)>>>0).toString(8);break;case"s":l=String(l),l=g.precision?l.substring(0,g.precision):l;break;case"t":l=String(!!l),l=g.precision?l.substring(0,g.precision):l;break;case"T":l=Object.prototype.toString.call(l).slice(8,-1).toLowerCase(),l=g.precision?l.substring(0,g.precision):l;break;case"u":l=parseInt(l,10)>>>0;break;case"v":l=l.valueOf(),l=g.precision?l.substring(0,g.precision):l;break;case"x":l=(parseInt(l,10)>>>0).toString(16);break;case"X":l=(parseInt(l,10)>>>0).toString(16).toUpperCase();break}t.json.test(g.type)?d+=l:(t.number.test(g.type)&&(!B||g.sign)?(C=B?"+":"-",l=l.toString().replace(t.sign,"")):C="",y=g.pad_char?g.pad_char==="0"?"0":g.pad_char.charAt(1):" ",O=g.width-(C+l).length,x=g.width&&O>0?y.repeat(O):"",d+=g.align?C+l+x:y==="0"?C+x+l:x+C+l)}return d}var n=Object.create(null);function s(o){if(n[o])return n[o];for(var c=o,u,h=[],l=0;c;){if((u=t.text.exec(c))!==null)h.push(u[0]);else if((u=t.modulo.exec(c))!==null)h.push("%");else if((u=t.placeholder.exec(c))!==null){if(u[2]){l|=1;var d=[],m=u[2],v=[];if((v=t.key.exec(m))!==null)for(d.push(v[1]);(m=m.substring(v[0].length))!=="";)if((v=t.key_access.exec(m))!==null)d.push(v[1]);else if((v=t.index_access.exec(m))!==null)d.push(v[1]);else throw new SyntaxError("[sprintf] failed to parse named argument key");else throw new SyntaxError("[sprintf] failed to parse named argument key");u[2]=d}else l|=2;if(l===3)throw new Error("[sprintf] mixing positional and named placeholders is not (yet) supported");h.push({placeholder:u[0],param_no:u[1],keys:u[2],sign:u[3],pad_char:u[4],align:u[5],width:u[6],precision:u[7],type:u[8]})}else throw new SyntaxError("[sprintf] unexpected placeholder");c=c.substring(u[0].length)}return n[o]=h}typeof un!="undefined"&&(un.sprintf=e,un.vsprintf=i),typeof window!="undefined"&&(window.sprintf=e,window.vsprintf=i,typeof define=="function"&&define.amd&&define(function(){return{sprintf:e,vsprintf:i}}))})()});var so=E(Nt=>{"use strict";var ig=Nt&&Nt.__createBinding||(Object.create?function(t,e,i,r){r===void 0&&(r=i);var n=Object.getOwnPropertyDescriptor(e,i);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[i]}}),Object.defineProperty(t,r,n)}:function(t,e,i,r){r===void 0&&(r=i),t[r]=e[i]}),rg=Nt&&Nt.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),Au=Nt&&Nt.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var i in t)i!=="default"&&Object.prototype.hasOwnProperty.call(t,i)&&ig(e,t,i);return rg(e,t),e};Object.defineProperty(Nt,"__esModule",{value:!0});Nt.Address4=void 0;var Tu=Au(eo()),It=Au(to()),Iu=ln(),vr=ro(),Zi=gr(),no=class t{constructor(e){this.groups=It.GROUPS,this.parsedAddress=[],this.parsedSubnet="",this.subnet="/32",this.subnetMask=32,this.v4=!0,this.isCorrect=Tu.isCorrect(It.BITS),this.isInSubnet=Tu.isInSubnet,this.address=e;let i=It.RE_SUBNET_STRING.exec(e);if(i){if(this.parsedSubnet=i[0].replace("/",""),this.subnetMask=parseInt(this.parsedSubnet,10),this.subnet=`/${this.subnetMask}`,this.subnetMask<0||this.subnetMask>It.BITS)throw new Iu.AddressError("Invalid subnet mask.");e=e.replace(It.RE_SUBNET_STRING,"")}this.addressMinusSuffix=e,this.parsedAddress=this.parse(e)}static isValid(e){try{return new t(e),!0}catch{return!1}}parse(e){let i=e.split(".");if(!e.match(It.RE_ADDRESS))throw new Iu.AddressError("Invalid IPv4 address.");return i}correctForm(){return this.parsedAddress.map(e=>parseInt(e,10)).join(".")}static fromHex(e){let i=e.replace(/:/g,"").padStart(8,"0"),r=[],n;for(n=0;n<8;n+=2){let s=i.slice(n,n+2);r.push(parseInt(s,16))}return new t(r.join("."))}static fromInteger(e){return t.fromHex(e.toString(16))}static fromArpa(e){let r=e.replace(/(\.in-addr\.arpa)?\.$/,"").split(".").reverse().join(".");return new t(r)}toHex(){return this.parsedAddress.map(e=>(0,Zi.sprintf)("%02x",parseInt(e,10))).join(":")}toArray(){return this.parsedAddress.map(e=>parseInt(e,10))}toGroup6(){let e=[],i;for(i=0;i<It.GROUPS;i+=2){let r=(0,Zi.sprintf)("%02x%02x",parseInt(this.parsedAddress[i],10),parseInt(this.parsedAddress[i+1],10));e.push((0,Zi.sprintf)("%x",parseInt(r,16)))}return e.join(":")}bigInteger(){return new vr.BigInteger(this.parsedAddress.map(e=>(0,Zi.sprintf)("%02x",parseInt(e,10))).join(""),16)}_startAddress(){return new vr.BigInteger(this.mask()+"0".repeat(It.BITS-this.subnetMask),2)}startAddress(){return t.fromBigInteger(this._startAddress())}startAddressExclusive(){let e=new vr.BigInteger("1");return t.fromBigInteger(this._startAddress().add(e))}_endAddress(){return new vr.BigInteger(this.mask()+"1".repeat(It.BITS-this.subnetMask),2)}endAddress(){return t.fromBigInteger(this._endAddress())}endAddressExclusive(){let e=new vr.BigInteger("1");return t.fromBigInteger(this._endAddress().subtract(e))}static fromBigInteger(e){return t.fromInteger(parseInt(e.toString(),10))}mask(e){return e===void 0&&(e=this.subnetMask),this.getBitsBase2(0,e)}getBitsBase2(e,i){return this.binaryZeroPad().slice(e,i)}reverseForm(e){e||(e={});let i=this.correctForm().split(".").reverse().join(".");return e.omitSuffix?i:(0,Zi.sprintf)("%s.in-addr.arpa.",i)}isMulticast(){return this.isInSubnet(new t("*********/4"))}binaryZeroPad(){return this.bigInteger().toString(2).padStart(It.BITS,"0")}groupForV6(){let e=this.parsedAddress;return this.address.replace(It.RE_ADDRESS,(0,Zi.sprintf)('<span class="hover-group group-v4 group-6">%s</span>.<span class="hover-group group-v4 group-7">%s</span>',e.slice(0,2).join("."),e.slice(2,4).join(".")))}};Nt.Address4=no});var oo=E(Le=>{"use strict";Object.defineProperty(Le,"__esModule",{value:!0});Le.RE_URL_WITH_PORT=Le.RE_URL=Le.RE_ZONE_STRING=Le.RE_SUBNET_STRING=Le.RE_BAD_ADDRESS=Le.RE_BAD_CHARACTERS=Le.TYPES=Le.SCOPES=Le.GROUPS=Le.BITS=void 0;Le.BITS=128;Le.GROUPS=8;Le.SCOPES={0:"Reserved",1:"Interface local",2:"Link local",4:"Admin local",5:"Site local",8:"Organization local",14:"Global",15:"Reserved"};Le.TYPES={"ff01::1/128":"Multicast (All nodes on this interface)","ff01::2/128":"Multicast (All routers on this interface)","ff02::1/128":"Multicast (All nodes on this link)","ff02::2/128":"Multicast (All routers on this link)","ff05::2/128":"Multicast (All routers in this site)","ff02::5/128":"Multicast (OSPFv3 AllSPF routers)","ff02::6/128":"Multicast (OSPFv3 AllDR routers)","ff02::9/128":"Multicast (RIP routers)","ff02::a/128":"Multicast (EIGRP routers)","ff02::d/128":"Multicast (PIM routers)","ff02::16/128":"Multicast (MLDv2 reports)","ff01::fb/128":"Multicast (mDNSv6)","ff02::fb/128":"Multicast (mDNSv6)","ff05::fb/128":"Multicast (mDNSv6)","ff02::1:2/128":"Multicast (All DHCP servers and relay agents on this link)","ff05::1:2/128":"Multicast (All DHCP servers and relay agents in this site)","ff02::1:3/128":"Multicast (All DHCP servers on this link)","ff05::1:3/128":"Multicast (All DHCP servers in this site)","::/128":"Unspecified","::1/128":"Loopback","ff00::/8":"Multicast","fe80::/10":"Link-local unicast"};Le.RE_BAD_CHARACTERS=/([^0-9a-f:/%])/gi;Le.RE_BAD_ADDRESS=/([0-9a-f]{5,}|:{3,}|[^:]:$|^:[^:]|\/$)/gi;Le.RE_SUBNET_STRING=/\/\d{1,3}(?=%|$)/;Le.RE_ZONE_STRING=/%.*$/;Le.RE_URL=new RegExp(/^\[{0,1}([0-9a-f:]+)\]{0,1}/);Le.RE_URL_WITH_PORT=new RegExp(/\[([0-9a-f:]+)\]:([0-9]{1,5})/)});var ao=E(Lt=>{"use strict";Object.defineProperty(Lt,"__esModule",{value:!0});Lt.simpleGroup=Lt.spanLeadingZeroes=Lt.spanAll=Lt.spanAllZeroes=void 0;var Bu=gr();function Ru(t){return t.replace(/(0+)/g,'<span class="zero">$1</span>')}Lt.spanAllZeroes=Ru;function ng(t,e=0){return t.split("").map((r,n)=>(0,Bu.sprintf)('<span class="digit value-%s position-%d">%s</span>',r,n+e,Ru(r))).join("")}Lt.spanAll=ng;function Pu(t){return t.replace(/^(0+)/,'<span class="zero">$1</span>')}function sg(t){return t.split(":").map(i=>Pu(i)).join(":")}Lt.spanLeadingZeroes=sg;function og(t,e=0){return t.split(":").map((r,n)=>/group-v4/.test(r)?r:(0,Bu.sprintf)('<span class="hover-group group-%d">%s</span>',n+e,Pu(r)))}Lt.simpleGroup=og});var Nu=E($e=>{"use strict";var ag=$e&&$e.__createBinding||(Object.create?function(t,e,i,r){r===void 0&&(r=i);var n=Object.getOwnPropertyDescriptor(e,i);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[i]}}),Object.defineProperty(t,r,n)}:function(t,e,i,r){r===void 0&&(r=i),t[r]=e[i]}),lg=$e&&$e.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),cg=$e&&$e.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var i in t)i!=="default"&&Object.prototype.hasOwnProperty.call(t,i)&&ag(e,t,i);return lg(e,t),e};Object.defineProperty($e,"__esModule",{value:!0});$e.possibleElisions=$e.simpleRegularExpression=$e.ADDRESS_BOUNDARY=$e.padGroup=$e.groupPossibilities=void 0;var ug=cg(oo()),Xi=gr();function hn(t){return(0,Xi.sprintf)("(%s)",t.join("|"))}$e.groupPossibilities=hn;function fn(t){return t.length<4?(0,Xi.sprintf)("0{0,%d}%s",4-t.length,t):t}$e.padGroup=fn;$e.ADDRESS_BOUNDARY="[^A-Fa-f0-9:]";function fg(t){let e=[];t.forEach((r,n)=>{parseInt(r,16)===0&&e.push(n)});let i=e.map(r=>t.map((n,s)=>{if(s===r){let o=s===0||s===ug.GROUPS-1?":":"";return hn([fn(n),o])}return fn(n)}).join(":"));return i.push(t.map(fn).join(":")),hn(i)}$e.simpleRegularExpression=fg;function hg(t,e,i){let r=e?"":":",n=i?"":":",s=[];!e&&!i&&s.push("::"),e&&i&&s.push(""),(i&&!e||!i&&e)&&s.push(":"),s.push((0,Xi.sprintf)("%s(:0{1,4}){1,%d}",r,t-1)),s.push((0,Xi.sprintf)("(0{1,4}:){1,%d}%s",t-1,n)),s.push((0,Xi.sprintf)("(0{1,4}:){%d}0{1,4}",t-1));for(let o=1;o<t-1;o++)for(let c=1;c<t-o;c++)s.push((0,Xi.sprintf)("(0{1,4}:){%d}:(0{1,4}:){%d}0{1,4}",c,t-c-o-1));return hn(s)}$e.possibleElisions=hg});var Du=E(Ft=>{"use strict";var pg=Ft&&Ft.__createBinding||(Object.create?function(t,e,i,r){r===void 0&&(r=i);var n=Object.getOwnPropertyDescriptor(e,i);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[i]}}),Object.defineProperty(t,r,n)}:function(t,e,i,r){r===void 0&&(r=i),t[r]=e[i]}),dg=Ft&&Ft.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),dn=Ft&&Ft.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var i in t)i!=="default"&&Object.prototype.hasOwnProperty.call(t,i)&&pg(e,t,i);return dg(e,t),e};Object.defineProperty(Ft,"__esModule",{value:!0});Ft.Address6=void 0;var Lu=dn(eo()),lo=dn(to()),Ae=dn(oo()),co=dn(ao()),xi=so(),yi=Nu(),Ht=ln(),rt=ro(),nt=gr();function pn(t){if(!t)throw new Error("Assertion failed.")}function mg(t){let e=/(\d+)(\d{3})/;for(;e.test(t);)t=t.replace(e,"$1,$2");return t}function gg(t){return t=t.replace(/^(0{1,})([1-9]+)$/,'<span class="parse-error">$1</span>$2'),t=t.replace(/^(0{1,})(0)$/,'<span class="parse-error">$1</span>$2'),t}function vg(t,e){let i=[],r=[],n;for(n=0;n<t.length;n++)n<e[0]?i.push(t[n]):n>e[1]&&r.push(t[n]);return i.concat(["compact"]).concat(r)}function Fu(t){return(0,nt.sprintf)("%04x",parseInt(t,16))}function Mu(t){return t&255}var uo=class t{constructor(e,i){this.addressMinusSuffix="",this.parsedSubnet="",this.subnet="/128",this.subnetMask=128,this.v4=!1,this.zone="",this.isInSubnet=Lu.isInSubnet,this.isCorrect=Lu.isCorrect(Ae.BITS),i===void 0?this.groups=Ae.GROUPS:this.groups=i,this.address=e;let r=Ae.RE_SUBNET_STRING.exec(e);if(r){if(this.parsedSubnet=r[0].replace("/",""),this.subnetMask=parseInt(this.parsedSubnet,10),this.subnet=`/${this.subnetMask}`,Number.isNaN(this.subnetMask)||this.subnetMask<0||this.subnetMask>Ae.BITS)throw new Ht.AddressError("Invalid subnet mask.");e=e.replace(Ae.RE_SUBNET_STRING,"")}else if(/\//.test(e))throw new Ht.AddressError("Invalid subnet mask.");let n=Ae.RE_ZONE_STRING.exec(e);n&&(this.zone=n[0],e=e.replace(Ae.RE_ZONE_STRING,"")),this.addressMinusSuffix=e,this.parsedAddress=this.parse(this.addressMinusSuffix)}static isValid(e){try{return new t(e),!0}catch{return!1}}static fromBigInteger(e){let i=e.toString(16).padStart(32,"0"),r=[],n;for(n=0;n<Ae.GROUPS;n++)r.push(i.slice(n*4,(n+1)*4));return new t(r.join(":"))}static fromURL(e){let i,r=null,n;if(e.indexOf("[")!==-1&&e.indexOf("]:")!==-1){if(n=Ae.RE_URL_WITH_PORT.exec(e),n===null)return{error:"failed to parse address with port",address:null,port:null};i=n[1],r=n[2]}else if(e.indexOf("/")!==-1){if(e=e.replace(/^[a-z0-9]+:\/\//,""),n=Ae.RE_URL.exec(e),n===null)return{error:"failed to parse address from URL",address:null,port:null};i=n[1]}else i=e;return r?(r=parseInt(r,10),(r<0||r>65536)&&(r=null)):r=null,{address:new t(i),port:r}}static fromAddress4(e){let i=new xi.Address4(e),r=Ae.BITS-(lo.BITS-i.subnetMask);return new t(`::ffff:${i.correctForm()}/${r}`)}static fromArpa(e){let i=e.replace(/(\.ip6\.arpa)?\.$/,""),r=7;if(i.length!==63)throw new Ht.AddressError("Invalid 'ip6.arpa' form.");let n=i.split(".").reverse();for(let s=r;s>0;s--){let o=s*4;n.splice(o,0,":")}return i=n.join(""),new t(i)}microsoftTranscription(){return(0,nt.sprintf)("%s.ipv6-literal.net",this.correctForm().replace(/:/g,"-"))}mask(e=this.subnetMask){return this.getBitsBase2(0,e)}possibleSubnets(e=128){let i=Ae.BITS-this.subnetMask,r=Math.abs(e-Ae.BITS),n=i-r;return n<0?"0":mg(new rt.BigInteger("2",10).pow(n).toString(10))}_startAddress(){return new rt.BigInteger(this.mask()+"0".repeat(Ae.BITS-this.subnetMask),2)}startAddress(){return t.fromBigInteger(this._startAddress())}startAddressExclusive(){let e=new rt.BigInteger("1");return t.fromBigInteger(this._startAddress().add(e))}_endAddress(){return new rt.BigInteger(this.mask()+"1".repeat(Ae.BITS-this.subnetMask),2)}endAddress(){return t.fromBigInteger(this._endAddress())}endAddressExclusive(){let e=new rt.BigInteger("1");return t.fromBigInteger(this._endAddress().subtract(e))}getScope(){let e=Ae.SCOPES[this.getBits(12,16).intValue()];return this.getType()==="Global unicast"&&e!=="Link local"&&(e="Global"),e||"Unknown"}getType(){for(let e of Object.keys(Ae.TYPES))if(this.isInSubnet(new t(e)))return Ae.TYPES[e];return"Global unicast"}getBits(e,i){return new rt.BigInteger(this.getBitsBase2(e,i),2)}getBitsBase2(e,i){return this.binaryZeroPad().slice(e,i)}getBitsBase16(e,i){let r=i-e;if(r%4!==0)throw new Error("Length of bits to retrieve must be divisible by four");return this.getBits(e,i).toString(16).padStart(r/4,"0")}getBitsPastSubnet(){return this.getBitsBase2(this.subnetMask,Ae.BITS)}reverseForm(e){e||(e={});let i=Math.floor(this.subnetMask/4),r=this.canonicalForm().replace(/:/g,"").split("").slice(0,i).reverse().join(".");return i>0?e.omitSuffix?r:(0,nt.sprintf)("%s.ip6.arpa.",r):e.omitSuffix?"":"ip6.arpa."}correctForm(){let e,i=[],r=0,n=[];for(e=0;e<this.parsedAddress.length;e++){let c=parseInt(this.parsedAddress[e],16);c===0&&r++,c!==0&&r>0&&(r>1&&n.push([e-r,e-1]),r=0)}r>1&&n.push([this.parsedAddress.length-r,this.parsedAddress.length-1]);let s=n.map(c=>c[1]-c[0]+1);if(n.length>0){let c=s.indexOf(Math.max(...s));i=vg(this.parsedAddress,n[c])}else i=this.parsedAddress;for(e=0;e<i.length;e++)i[e]!=="compact"&&(i[e]=parseInt(i[e],16).toString(16));let o=i.join(":");return o=o.replace(/^compact$/,"::"),o=o.replace(/^compact|compact$/,":"),o=o.replace(/compact/,""),o}binaryZeroPad(){return this.bigInteger().toString(2).padStart(Ae.BITS,"0")}parse4in6(e){let i=e.split(":"),n=i.slice(-1)[0].match(lo.RE_ADDRESS);if(n){this.parsedAddress4=n[0],this.address4=new xi.Address4(this.parsedAddress4);for(let s=0;s<this.address4.groups;s++)if(/^0[0-9]+/.test(this.address4.parsedAddress[s]))throw new Ht.AddressError("IPv4 addresses can't have leading zeroes.",e.replace(lo.RE_ADDRESS,this.address4.parsedAddress.map(gg).join(".")));this.v4=!0,i[i.length-1]=this.address4.toGroup6(),e=i.join(":")}return e}parse(e){e=this.parse4in6(e);let i=e.match(Ae.RE_BAD_CHARACTERS);if(i)throw new Ht.AddressError((0,nt.sprintf)("Bad character%s detected in address: %s",i.length>1?"s":"",i.join("")),e.replace(Ae.RE_BAD_CHARACTERS,'<span class="parse-error">$1</span>'));let r=e.match(Ae.RE_BAD_ADDRESS);if(r)throw new Ht.AddressError((0,nt.sprintf)("Address failed regex: %s",r.join("")),e.replace(Ae.RE_BAD_ADDRESS,'<span class="parse-error">$1</span>'));let n=[],s=e.split("::");if(s.length===2){let o=s[0].split(":"),c=s[1].split(":");o.length===1&&o[0]===""&&(o=[]),c.length===1&&c[0]===""&&(c=[]);let u=this.groups-(o.length+c.length);if(!u)throw new Ht.AddressError("Error parsing groups");this.elidedGroups=u,this.elisionBegin=o.length,this.elisionEnd=o.length+this.elidedGroups,n=n.concat(o);for(let h=0;h<u;h++)n.push("0");n=n.concat(c)}else if(s.length===1)n=e.split(":"),this.elidedGroups=0;else throw new Ht.AddressError("Too many :: groups found");if(n=n.map(o=>(0,nt.sprintf)("%x",parseInt(o,16))),n.length!==this.groups)throw new Ht.AddressError("Incorrect number of groups found");return n}canonicalForm(){return this.parsedAddress.map(Fu).join(":")}decimal(){return this.parsedAddress.map(e=>(0,nt.sprintf)("%05d",parseInt(e,16))).join(":")}bigInteger(){return new rt.BigInteger(this.parsedAddress.map(Fu).join(""),16)}to4(){let e=this.binaryZeroPad().split("");return xi.Address4.fromHex(new rt.BigInteger(e.slice(96,128).join(""),2).toString(16))}to4in6(){let e=this.to4(),r=new t(this.parsedAddress.slice(0,6).join(":"),6).correctForm(),n="";return/:$/.test(r)||(n=":"),r+n+e.address}inspectTeredo(){let e=this.getBitsBase16(0,32),i=this.getBits(80,96).xor(new rt.BigInteger("ffff",16)).toString(),r=xi.Address4.fromHex(this.getBitsBase16(32,64)),n=xi.Address4.fromHex(this.getBits(96,128).xor(new rt.BigInteger("ffffffff",16)).toString(16)),s=this.getBits(64,80),o=this.getBitsBase2(64,80),c=s.testBit(15),u=s.testBit(14),h=s.testBit(8),l=s.testBit(9),d=new rt.BigInteger(o.slice(2,6)+o.slice(8,16),2).toString(10);return{prefix:(0,nt.sprintf)("%s:%s",e.slice(0,4),e.slice(4,8)),server4:r.address,client4:n.address,flags:o,coneNat:c,microsoft:{reserved:u,universalLocal:l,groupIndividual:h,nonce:d},udpPort:i}}inspect6to4(){let e=this.getBitsBase16(0,16),i=xi.Address4.fromHex(this.getBitsBase16(16,48));return{prefix:(0,nt.sprintf)("%s",e.slice(0,4)),gateway:i.address}}to6to4(){if(!this.is4())return null;let e=["2002",this.getBitsBase16(96,112),this.getBitsBase16(112,128),"","/16"].join(":");return new t(e)}toByteArray(){let e=this.bigInteger().toByteArray();return e.length===17&&e[0]===0?e.slice(1):e}toUnsignedByteArray(){return this.toByteArray().map(Mu)}static fromByteArray(e){return this.fromUnsignedByteArray(e.map(Mu))}static fromUnsignedByteArray(e){let i=new rt.BigInteger("256",10),r=new rt.BigInteger("0",10),n=new rt.BigInteger("1",10);for(let s=e.length-1;s>=0;s--)r=r.add(n.multiply(new rt.BigInteger(e[s].toString(10),10))),n=n.multiply(i);return t.fromBigInteger(r)}isCanonical(){return this.addressMinusSuffix===this.canonicalForm()}isLinkLocal(){return this.getBitsBase2(0,64)==="1111111010000000000000000000000000000000000000000000000000000000"}isMulticast(){return this.getType()==="Multicast"}is4(){return this.v4}isTeredo(){return this.isInSubnet(new t("2001::/32"))}is6to4(){return this.isInSubnet(new t("2002::/16"))}isLoopback(){return this.getType()==="Loopback"}href(e){return e===void 0?e="":e=(0,nt.sprintf)(":%s",e),(0,nt.sprintf)("http://[%s]%s/",this.correctForm(),e)}link(e){e||(e={}),e.className===void 0&&(e.className=""),e.prefix===void 0&&(e.prefix="/#address="),e.v4===void 0&&(e.v4=!1);let i=this.correctForm;return e.v4&&(i=this.to4in6),e.className?(0,nt.sprintf)('<a href="%1$s%2$s" class="%3$s">%2$s</a>',e.prefix,i.call(this),e.className):(0,nt.sprintf)('<a href="%1$s%2$s">%2$s</a>',e.prefix,i.call(this))}group(){if(this.elidedGroups===0)return co.simpleGroup(this.address).join(":");pn(typeof this.elidedGroups=="number"),pn(typeof this.elisionBegin=="number");let e=[],[i,r]=this.address.split("::");i.length?e.push(...co.simpleGroup(i)):e.push("");let n=["hover-group"];for(let s=this.elisionBegin;s<this.elisionBegin+this.elidedGroups;s++)n.push((0,nt.sprintf)("group-%d",s));return e.push((0,nt.sprintf)('<span class="%s"></span>',n.join(" "))),r.length?e.push(...co.simpleGroup(r,this.elisionEnd)):e.push(""),this.is4()&&(pn(this.address4 instanceof xi.Address4),e.pop(),e.push(this.address4.groupForV6())),e.join(":")}regularExpressionString(e=!1){let i=[],r=new t(this.correctForm());if(r.elidedGroups===0)i.push((0,yi.simpleRegularExpression)(r.parsedAddress));else if(r.elidedGroups===Ae.GROUPS)i.push((0,yi.possibleElisions)(Ae.GROUPS));else{let n=r.address.split("::");n[0].length&&i.push((0,yi.simpleRegularExpression)(n[0].split(":"))),pn(typeof r.elidedGroups=="number"),i.push((0,yi.possibleElisions)(r.elidedGroups,n[0].length!==0,n[1].length!==0)),n[1].length&&i.push((0,yi.simpleRegularExpression)(n[1].split(":"))),i=[i.join(":")]}return e||(i=["(?=^|",yi.ADDRESS_BOUNDARY,"|[^\\w\\:])(",...i,")(?=[^\\w\\:]|",yi.ADDRESS_BOUNDARY,"|$)"]),i.join("")}regularExpression(e=!1){return new RegExp(this.regularExpressionString(e),"i")}};Ft.Address6=uo});var fo=E(Je=>{"use strict";var _g=Je&&Je.__createBinding||(Object.create?function(t,e,i,r){r===void 0&&(r=i);var n=Object.getOwnPropertyDescriptor(e,i);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[i]}}),Object.defineProperty(t,r,n)}:function(t,e,i,r){r===void 0&&(r=i),t[r]=e[i]}),xg=Je&&Je.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),yg=Je&&Je.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var i in t)i!=="default"&&Object.prototype.hasOwnProperty.call(t,i)&&_g(e,t,i);return xg(e,t),e};Object.defineProperty(Je,"__esModule",{value:!0});Je.v6=Je.AddressError=Je.Address6=Je.Address4=void 0;var bg=so();Object.defineProperty(Je,"Address4",{enumerable:!0,get:function(){return bg.Address4}});var wg=Du();Object.defineProperty(Je,"Address6",{enumerable:!0,get:function(){return wg.Address6}});var Eg=ln();Object.defineProperty(Je,"AddressError",{enumerable:!0,get:function(){return Eg.AddressError}});var Sg=yg(ao());Je.v6={helpers:Sg}});var $u=E(xt=>{"use strict";Object.defineProperty(xt,"__esModule",{value:!0});xt.ipToBuffer=xt.int32ToIpv4=xt.ipv4ToInt32=xt.validateSocksClientChainOptions=xt.validateSocksClientOptions=void 0;var st=Qs(),Ge=Xs(),kg=require("stream"),ho=fo(),Uu=require("net");function Og(t,e=["connect","bind","associate"]){if(!Ge.SocksCommand[t.command])throw new st.SocksClientError(Ge.ERRORS.InvalidSocksCommand,t);if(e.indexOf(t.command)===-1)throw new st.SocksClientError(Ge.ERRORS.InvalidSocksCommandForOperation,t);if(!qu(t.destination))throw new st.SocksClientError(Ge.ERRORS.InvalidSocksClientOptionsDestination,t);if(!Vu(t.proxy))throw new st.SocksClientError(Ge.ERRORS.InvalidSocksClientOptionsProxy,t);if(ju(t.proxy,t),t.timeout&&!Hu(t.timeout))throw new st.SocksClientError(Ge.ERRORS.InvalidSocksClientOptionsTimeout,t);if(t.existing_socket&&!(t.existing_socket instanceof kg.Duplex))throw new st.SocksClientError(Ge.ERRORS.InvalidSocksClientOptionsExistingSocket,t)}xt.validateSocksClientOptions=Og;function Cg(t){if(t.command!=="connect")throw new st.SocksClientError(Ge.ERRORS.InvalidSocksCommandChain,t);if(!qu(t.destination))throw new st.SocksClientError(Ge.ERRORS.InvalidSocksClientOptionsDestination,t);if(!(t.proxies&&Array.isArray(t.proxies)&&t.proxies.length>=2))throw new st.SocksClientError(Ge.ERRORS.InvalidSocksClientOptionsProxiesLength,t);if(t.proxies.forEach(e=>{if(!Vu(e))throw new st.SocksClientError(Ge.ERRORS.InvalidSocksClientOptionsProxy,t);ju(e,t)}),t.timeout&&!Hu(t.timeout))throw new st.SocksClientError(Ge.ERRORS.InvalidSocksClientOptionsTimeout,t)}xt.validateSocksClientChainOptions=Cg;function ju(t,e){if(t.custom_auth_method!==void 0){if(t.custom_auth_method<Ge.SOCKS5_CUSTOM_AUTH_START||t.custom_auth_method>Ge.SOCKS5_CUSTOM_AUTH_END)throw new st.SocksClientError(Ge.ERRORS.InvalidSocksClientOptionsCustomAuthRange,e);if(t.custom_auth_request_handler===void 0||typeof t.custom_auth_request_handler!="function")throw new st.SocksClientError(Ge.ERRORS.InvalidSocksClientOptionsCustomAuthOptions,e);if(t.custom_auth_response_size===void 0)throw new st.SocksClientError(Ge.ERRORS.InvalidSocksClientOptionsCustomAuthOptions,e);if(t.custom_auth_response_handler===void 0||typeof t.custom_auth_response_handler!="function")throw new st.SocksClientError(Ge.ERRORS.InvalidSocksClientOptionsCustomAuthOptions,e)}}function qu(t){return t&&typeof t.host=="string"&&typeof t.port=="number"&&t.port>=0&&t.port<=65535}function Vu(t){return t&&(typeof t.host=="string"||typeof t.ipaddress=="string")&&typeof t.port=="number"&&t.port>=0&&t.port<=65535&&(t.type===4||t.type===5)}function Hu(t){return typeof t=="number"&&t>0}function Tg(t){return new ho.Address4(t).toArray().reduce((i,r)=>(i<<8)+r,0)}xt.ipv4ToInt32=Tg;function Ig(t){let e=t>>>24&255,i=t>>>16&255,r=t>>>8&255,n=t&255;return[e,i,r,n].join(".")}xt.int32ToIpv4=Ig;function Ag(t){if(Uu.isIPv4(t)){let e=new ho.Address4(t);return Buffer.from(e.toArray())}else if(Uu.isIPv6(t)){let e=new ho.Address6(t);return Buffer.from(e.canonicalForm().split(":").map(i=>i.padStart(4,"0")).join(""),"hex")}else throw new Error("Invalid IP address format")}xt.ipToBuffer=Ag});var Gu=E(mn=>{"use strict";Object.defineProperty(mn,"__esModule",{value:!0});mn.ReceiveBuffer=void 0;var po=class{constructor(e=4096){this.buffer=Buffer.allocUnsafe(e),this.offset=0,this.originalSize=e}get length(){return this.offset}append(e){if(!Buffer.isBuffer(e))throw new Error("Attempted to append a non-buffer instance to ReceiveBuffer.");if(this.offset+e.length>=this.buffer.length){let i=this.buffer;this.buffer=Buffer.allocUnsafe(Math.max(this.buffer.length+this.originalSize,this.buffer.length+e.length)),i.copy(this.buffer)}return e.copy(this.buffer,this.offset),this.offset+=e.length}peek(e){if(e>this.offset)throw new Error("Attempted to read beyond the bounds of the managed internal data.");return this.buffer.slice(0,e)}get(e){if(e>this.offset)throw new Error("Attempted to read beyond the bounds of the managed internal data.");let i=Buffer.allocUnsafe(e);return this.buffer.slice(0,e).copy(i),this.buffer.copyWithin(0,e,e+this.offset-e),this.offset-=e,i}};mn.ReceiveBuffer=po});var zu=E(Qt=>{"use strict";var Ji=Qt&&Qt.__awaiter||function(t,e,i,r){function n(s){return s instanceof i?s:new i(function(o){o(s)})}return new(i||(i=Promise))(function(s,o){function c(l){try{h(r.next(l))}catch(d){o(d)}}function u(l){try{h(r.throw(l))}catch(d){o(d)}}function h(l){l.done?s(l.value):n(l.value).then(c,u)}h((r=r.apply(t,e||[])).next())})};Object.defineProperty(Qt,"__esModule",{value:!0});Qt.SocksClientError=Qt.SocksClient=void 0;var Bg=require("events"),Qi=require("net"),lt=yu(),R=Xs(),dt=$u(),Rg=Gu(),go=Qs();Object.defineProperty(Qt,"SocksClientError",{enumerable:!0,get:function(){return go.SocksClientError}});var mo=fo(),vo=class t extends Bg.EventEmitter{constructor(e){super(),this.options=Object.assign({},e),(0,dt.validateSocksClientOptions)(e),this.setState(R.SocksClientState.Created)}static createConnection(e,i){return new Promise((r,n)=>{try{(0,dt.validateSocksClientOptions)(e,["connect"])}catch(o){return typeof i=="function"?(i(o),r(o)):n(o)}let s=new t(e);s.connect(e.existing_socket),s.once("established",o=>{s.removeAllListeners(),typeof i=="function"&&i(null,o),r(o)}),s.once("error",o=>{s.removeAllListeners(),typeof i=="function"?(i(o),r(o)):n(o)})})}static createConnectionChain(e,i){return new Promise((r,n)=>Ji(this,void 0,void 0,function*(){try{(0,dt.validateSocksClientChainOptions)(e)}catch(s){return typeof i=="function"?(i(s),r(s)):n(s)}e.randomizeChain&&(0,go.shuffleArray)(e.proxies);try{let s;for(let o=0;o<e.proxies.length;o++){let c=e.proxies[o],u=o===e.proxies.length-1?e.destination:{host:e.proxies[o+1].host||e.proxies[o+1].ipaddress,port:e.proxies[o+1].port},h=yield t.createConnection({command:"connect",proxy:c,destination:u,existing_socket:s});s=s||h.socket}typeof i=="function"?(i(null,{socket:s}),r({socket:s})):r({socket:s})}catch(s){typeof i=="function"?(i(s),r(s)):n(s)}}))}static createUDPFrame(e){let i=new lt.SmartBuffer;return i.writeUInt16BE(0),i.writeUInt8(e.frameNumber||0),Qi.isIPv4(e.remoteHost.host)?(i.writeUInt8(R.Socks5HostType.IPv4),i.writeUInt32BE((0,dt.ipv4ToInt32)(e.remoteHost.host))):Qi.isIPv6(e.remoteHost.host)?(i.writeUInt8(R.Socks5HostType.IPv6),i.writeBuffer((0,dt.ipToBuffer)(e.remoteHost.host))):(i.writeUInt8(R.Socks5HostType.Hostname),i.writeUInt8(Buffer.byteLength(e.remoteHost.host)),i.writeString(e.remoteHost.host)),i.writeUInt16BE(e.remoteHost.port),i.writeBuffer(e.data),i.toBuffer()}static parseUDPFrame(e){let i=lt.SmartBuffer.fromBuffer(e);i.readOffset=2;let r=i.readUInt8(),n=i.readUInt8(),s;n===R.Socks5HostType.IPv4?s=(0,dt.int32ToIpv4)(i.readUInt32BE()):n===R.Socks5HostType.IPv6?s=mo.Address6.fromByteArray(Array.from(i.readBuffer(16))).canonicalForm():s=i.readString(i.readUInt8());let o=i.readUInt16BE();return{frameNumber:r,remoteHost:{host:s,port:o},data:i.readBuffer()}}setState(e){this.state!==R.SocksClientState.Error&&(this.state=e)}connect(e){this.onDataReceived=r=>this.onDataReceivedHandler(r),this.onClose=()=>this.onCloseHandler(),this.onError=r=>this.onErrorHandler(r),this.onConnect=()=>this.onConnectHandler();let i=setTimeout(()=>this.onEstablishedTimeout(),this.options.timeout||R.DEFAULT_TIMEOUT);i.unref&&typeof i.unref=="function"&&i.unref(),e?this.socket=e:this.socket=new Qi.Socket,this.socket.once("close",this.onClose),this.socket.once("error",this.onError),this.socket.once("connect",this.onConnect),this.socket.on("data",this.onDataReceived),this.setState(R.SocksClientState.Connecting),this.receiveBuffer=new Rg.ReceiveBuffer,e?this.socket.emit("connect"):(this.socket.connect(this.getSocketOptions()),this.options.set_tcp_nodelay!==void 0&&this.options.set_tcp_nodelay!==null&&this.socket.setNoDelay(!!this.options.set_tcp_nodelay)),this.prependOnceListener("established",r=>{setImmediate(()=>{if(this.receiveBuffer.length>0){let n=this.receiveBuffer.get(this.receiveBuffer.length);r.socket.emit("data",n)}r.socket.resume()})})}getSocketOptions(){return Object.assign(Object.assign({},this.options.socket_options),{host:this.options.proxy.host||this.options.proxy.ipaddress,port:this.options.proxy.port})}onEstablishedTimeout(){this.state!==R.SocksClientState.Established&&this.state!==R.SocksClientState.BoundWaitingForConnection&&this.closeSocket(R.ERRORS.ProxyConnectionTimedOut)}onConnectHandler(){this.setState(R.SocksClientState.Connected),this.options.proxy.type===4?this.sendSocks4InitialHandshake():this.sendSocks5InitialHandshake(),this.setState(R.SocksClientState.SentInitialHandshake)}onDataReceivedHandler(e){this.receiveBuffer.append(e),this.processData()}processData(){for(;this.state!==R.SocksClientState.Established&&this.state!==R.SocksClientState.Error&&this.receiveBuffer.length>=this.nextRequiredPacketBufferSize;)if(this.state===R.SocksClientState.SentInitialHandshake)this.options.proxy.type===4?this.handleSocks4FinalHandshakeResponse():this.handleInitialSocks5HandshakeResponse();else if(this.state===R.SocksClientState.SentAuthentication)this.handleInitialSocks5AuthenticationHandshakeResponse();else if(this.state===R.SocksClientState.SentFinalHandshake)this.handleSocks5FinalHandshakeResponse();else if(this.state===R.SocksClientState.BoundWaitingForConnection)this.options.proxy.type===4?this.handleSocks4IncomingConnectionResponse():this.handleSocks5IncomingConnectionResponse();else{this.closeSocket(R.ERRORS.InternalError);break}}onCloseHandler(){this.closeSocket(R.ERRORS.SocketClosed)}onErrorHandler(e){this.closeSocket(e.message)}removeInternalSocketHandlers(){this.socket.pause(),this.socket.removeListener("data",this.onDataReceived),this.socket.removeListener("close",this.onClose),this.socket.removeListener("error",this.onError),this.socket.removeListener("connect",this.onConnect)}closeSocket(e){this.state!==R.SocksClientState.Error&&(this.setState(R.SocksClientState.Error),this.socket.destroy(),this.removeInternalSocketHandlers(),this.emit("error",new go.SocksClientError(e,this.options)))}sendSocks4InitialHandshake(){let e=this.options.proxy.userId||"",i=new lt.SmartBuffer;i.writeUInt8(4),i.writeUInt8(R.SocksCommand[this.options.command]),i.writeUInt16BE(this.options.destination.port),Qi.isIPv4(this.options.destination.host)?(i.writeBuffer((0,dt.ipToBuffer)(this.options.destination.host)),i.writeStringNT(e)):(i.writeUInt8(0),i.writeUInt8(0),i.writeUInt8(0),i.writeUInt8(1),i.writeStringNT(e),i.writeStringNT(this.options.destination.host)),this.nextRequiredPacketBufferSize=R.SOCKS_INCOMING_PACKET_SIZES.Socks4Response,this.socket.write(i.toBuffer())}handleSocks4FinalHandshakeResponse(){let e=this.receiveBuffer.get(8);if(e[1]!==R.Socks4Response.Granted)this.closeSocket(`${R.ERRORS.Socks4ProxyRejectedConnection} - (${R.Socks4Response[e[1]]})`);else if(R.SocksCommand[this.options.command]===R.SocksCommand.bind){let i=lt.SmartBuffer.fromBuffer(e);i.readOffset=2;let r={port:i.readUInt16BE(),host:(0,dt.int32ToIpv4)(i.readUInt32BE())};r.host==="0.0.0.0"&&(r.host=this.options.proxy.ipaddress),this.setState(R.SocksClientState.BoundWaitingForConnection),this.emit("bound",{remoteHost:r,socket:this.socket})}else this.setState(R.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{socket:this.socket})}handleSocks4IncomingConnectionResponse(){let e=this.receiveBuffer.get(8);if(e[1]!==R.Socks4Response.Granted)this.closeSocket(`${R.ERRORS.Socks4ProxyRejectedIncomingBoundConnection} - (${R.Socks4Response[e[1]]})`);else{let i=lt.SmartBuffer.fromBuffer(e);i.readOffset=2;let r={port:i.readUInt16BE(),host:(0,dt.int32ToIpv4)(i.readUInt32BE())};this.setState(R.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{remoteHost:r,socket:this.socket})}}sendSocks5InitialHandshake(){let e=new lt.SmartBuffer,i=[R.Socks5Auth.NoAuth];(this.options.proxy.userId||this.options.proxy.password)&&i.push(R.Socks5Auth.UserPass),this.options.proxy.custom_auth_method!==void 0&&i.push(this.options.proxy.custom_auth_method),e.writeUInt8(5),e.writeUInt8(i.length);for(let r of i)e.writeUInt8(r);this.nextRequiredPacketBufferSize=R.SOCKS_INCOMING_PACKET_SIZES.Socks5InitialHandshakeResponse,this.socket.write(e.toBuffer()),this.setState(R.SocksClientState.SentInitialHandshake)}handleInitialSocks5HandshakeResponse(){let e=this.receiveBuffer.get(2);e[0]!==5?this.closeSocket(R.ERRORS.InvalidSocks5IntiailHandshakeSocksVersion):e[1]===R.SOCKS5_NO_ACCEPTABLE_AUTH?this.closeSocket(R.ERRORS.InvalidSocks5InitialHandshakeNoAcceptedAuthType):e[1]===R.Socks5Auth.NoAuth?(this.socks5ChosenAuthType=R.Socks5Auth.NoAuth,this.sendSocks5CommandRequest()):e[1]===R.Socks5Auth.UserPass?(this.socks5ChosenAuthType=R.Socks5Auth.UserPass,this.sendSocks5UserPassAuthentication()):e[1]===this.options.proxy.custom_auth_method?(this.socks5ChosenAuthType=this.options.proxy.custom_auth_method,this.sendSocks5CustomAuthentication()):this.closeSocket(R.ERRORS.InvalidSocks5InitialHandshakeUnknownAuthType)}sendSocks5UserPassAuthentication(){let e=this.options.proxy.userId||"",i=this.options.proxy.password||"",r=new lt.SmartBuffer;r.writeUInt8(1),r.writeUInt8(Buffer.byteLength(e)),r.writeString(e),r.writeUInt8(Buffer.byteLength(i)),r.writeString(i),this.nextRequiredPacketBufferSize=R.SOCKS_INCOMING_PACKET_SIZES.Socks5UserPassAuthenticationResponse,this.socket.write(r.toBuffer()),this.setState(R.SocksClientState.SentAuthentication)}sendSocks5CustomAuthentication(){return Ji(this,void 0,void 0,function*(){this.nextRequiredPacketBufferSize=this.options.proxy.custom_auth_response_size,this.socket.write(yield this.options.proxy.custom_auth_request_handler()),this.setState(R.SocksClientState.SentAuthentication)})}handleSocks5CustomAuthHandshakeResponse(e){return Ji(this,void 0,void 0,function*(){return yield this.options.proxy.custom_auth_response_handler(e)})}handleSocks5AuthenticationNoAuthHandshakeResponse(e){return Ji(this,void 0,void 0,function*(){return e[1]===0})}handleSocks5AuthenticationUserPassHandshakeResponse(e){return Ji(this,void 0,void 0,function*(){return e[1]===0})}handleInitialSocks5AuthenticationHandshakeResponse(){return Ji(this,void 0,void 0,function*(){this.setState(R.SocksClientState.ReceivedAuthenticationResponse);let e=!1;this.socks5ChosenAuthType===R.Socks5Auth.NoAuth?e=yield this.handleSocks5AuthenticationNoAuthHandshakeResponse(this.receiveBuffer.get(2)):this.socks5ChosenAuthType===R.Socks5Auth.UserPass?e=yield this.handleSocks5AuthenticationUserPassHandshakeResponse(this.receiveBuffer.get(2)):this.socks5ChosenAuthType===this.options.proxy.custom_auth_method&&(e=yield this.handleSocks5CustomAuthHandshakeResponse(this.receiveBuffer.get(this.options.proxy.custom_auth_response_size))),e?this.sendSocks5CommandRequest():this.closeSocket(R.ERRORS.Socks5AuthenticationFailed)})}sendSocks5CommandRequest(){let e=new lt.SmartBuffer;e.writeUInt8(5),e.writeUInt8(R.SocksCommand[this.options.command]),e.writeUInt8(0),Qi.isIPv4(this.options.destination.host)?(e.writeUInt8(R.Socks5HostType.IPv4),e.writeBuffer((0,dt.ipToBuffer)(this.options.destination.host))):Qi.isIPv6(this.options.destination.host)?(e.writeUInt8(R.Socks5HostType.IPv6),e.writeBuffer((0,dt.ipToBuffer)(this.options.destination.host))):(e.writeUInt8(R.Socks5HostType.Hostname),e.writeUInt8(this.options.destination.host.length),e.writeString(this.options.destination.host)),e.writeUInt16BE(this.options.destination.port),this.nextRequiredPacketBufferSize=R.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHeader,this.socket.write(e.toBuffer()),this.setState(R.SocksClientState.SentFinalHandshake)}handleSocks5FinalHandshakeResponse(){let e=this.receiveBuffer.peek(5);if(e[0]!==5||e[1]!==R.Socks5Response.Granted)this.closeSocket(`${R.ERRORS.InvalidSocks5FinalHandshakeRejected} - ${R.Socks5Response[e[1]]}`);else{let i=e[3],r,n;if(i===R.Socks5HostType.IPv4){let s=R.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv4;if(this.receiveBuffer.length<s){this.nextRequiredPacketBufferSize=s;return}n=lt.SmartBuffer.fromBuffer(this.receiveBuffer.get(s).slice(4)),r={host:(0,dt.int32ToIpv4)(n.readUInt32BE()),port:n.readUInt16BE()},r.host==="0.0.0.0"&&(r.host=this.options.proxy.ipaddress)}else if(i===R.Socks5HostType.Hostname){let s=e[4],o=R.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHostname(s);if(this.receiveBuffer.length<o){this.nextRequiredPacketBufferSize=o;return}n=lt.SmartBuffer.fromBuffer(this.receiveBuffer.get(o).slice(5)),r={host:n.readString(s),port:n.readUInt16BE()}}else if(i===R.Socks5HostType.IPv6){let s=R.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv6;if(this.receiveBuffer.length<s){this.nextRequiredPacketBufferSize=s;return}n=lt.SmartBuffer.fromBuffer(this.receiveBuffer.get(s).slice(4)),r={host:mo.Address6.fromByteArray(Array.from(n.readBuffer(16))).canonicalForm(),port:n.readUInt16BE()}}this.setState(R.SocksClientState.ReceivedFinalResponse),R.SocksCommand[this.options.command]===R.SocksCommand.connect?(this.setState(R.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{remoteHost:r,socket:this.socket})):R.SocksCommand[this.options.command]===R.SocksCommand.bind?(this.setState(R.SocksClientState.BoundWaitingForConnection),this.nextRequiredPacketBufferSize=R.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHeader,this.emit("bound",{remoteHost:r,socket:this.socket})):R.SocksCommand[this.options.command]===R.SocksCommand.associate&&(this.setState(R.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{remoteHost:r,socket:this.socket}))}}handleSocks5IncomingConnectionResponse(){let e=this.receiveBuffer.peek(5);if(e[0]!==5||e[1]!==R.Socks5Response.Granted)this.closeSocket(`${R.ERRORS.Socks5ProxyRejectedIncomingBoundConnection} - ${R.Socks5Response[e[1]]}`);else{let i=e[3],r,n;if(i===R.Socks5HostType.IPv4){let s=R.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv4;if(this.receiveBuffer.length<s){this.nextRequiredPacketBufferSize=s;return}n=lt.SmartBuffer.fromBuffer(this.receiveBuffer.get(s).slice(4)),r={host:(0,dt.int32ToIpv4)(n.readUInt32BE()),port:n.readUInt16BE()},r.host==="0.0.0.0"&&(r.host=this.options.proxy.ipaddress)}else if(i===R.Socks5HostType.Hostname){let s=e[4],o=R.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHostname(s);if(this.receiveBuffer.length<o){this.nextRequiredPacketBufferSize=o;return}n=lt.SmartBuffer.fromBuffer(this.receiveBuffer.get(o).slice(5)),r={host:n.readString(s),port:n.readUInt16BE()}}else if(i===R.Socks5HostType.IPv6){let s=R.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv6;if(this.receiveBuffer.length<s){this.nextRequiredPacketBufferSize=s;return}n=lt.SmartBuffer.fromBuffer(this.receiveBuffer.get(s).slice(4)),r={host:mo.Address6.fromByteArray(Array.from(n.readBuffer(16))).canonicalForm(),port:n.readUInt16BE()}}this.setState(R.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{remoteHost:r,socket:this.socket})}}get socksClientOptions(){return Object.assign({},this.options)}};Qt.SocksClient=vo});var Wu=E(bi=>{"use strict";var Pg=bi&&bi.__createBinding||(Object.create?function(t,e,i,r){r===void 0&&(r=i);var n=Object.getOwnPropertyDescriptor(e,i);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[i]}}),Object.defineProperty(t,r,n)}:function(t,e,i,r){r===void 0&&(r=i),t[r]=e[i]}),Ng=bi&&bi.__exportStar||function(t,e){for(var i in t)i!=="default"&&!Object.prototype.hasOwnProperty.call(e,i)&&Pg(e,t,i)};Object.defineProperty(bi,"__esModule",{value:!0});Ng(zu(),bi)});var Yu=E(wi=>{"use strict";var Lg=wi&&wi.__awaiter||function(t,e,i,r){function n(s){return s instanceof i?s:new i(function(o){o(s)})}return new(i||(i=Promise))(function(s,o){function c(l){try{h(r.next(l))}catch(d){o(d)}}function u(l){try{h(r.throw(l))}catch(d){o(d)}}function h(l){l.done?s(l.value):n(l.value).then(c,u)}h((r=r.apply(t,e||[])).next())})},gn=wi&&wi.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(wi,"__esModule",{value:!0});var Fg=gn(require("dns")),Mg=gn(require("tls")),Dg=gn(require("url")),Ug=gn(Ui()),jg=ts(),qg=Wu(),_o=Ug.default("socks-proxy-agent");function Vg(t){return new Promise((e,i)=>{Fg.default.lookup(t,(r,n)=>{r?i(r):e(n)})})}function Hg(t){let e=0,i=!1,r=5,n=t.hostname||t.host;if(!n)throw new TypeError('No "host"');if(typeof t.port=="number"?e=t.port:typeof t.port=="string"&&(e=parseInt(t.port,10)),e||(e=1080),t.protocol)switch(t.protocol.replace(":","")){case"socks4":i=!0;case"socks4a":r=4;break;case"socks5":i=!0;case"socks":case"socks5h":r=5;break;default:throw new TypeError(`A "socks" protocol must be specified! Got: ${t.protocol}`)}if(typeof t.type!="undefined")if(t.type===4||t.type===5)r=t.type;else throw new TypeError(`"type" must be 4 or 5, got: ${t.type}`);let s={host:n,port:e,type:r},o=t.userId||t.username,c=t.password;if(t.auth){let u=t.auth.split(":");o=u[0],c=u[1]}return o&&Object.defineProperty(s,"userId",{value:o,enumerable:!1}),c&&Object.defineProperty(s,"password",{value:c,enumerable:!1}),{lookup:i,proxy:s}}var xo=class extends jg.Agent{constructor(e){let i;if(typeof e=="string"?i=Dg.default.parse(e):i=e,!i)throw new TypeError("a SOCKS proxy server `host` and `port` must be specified!");super(i);let r=Hg(i);this.lookup=r.lookup,this.proxy=r.proxy,this.tlsConnectionOptions=i.tls||{}}callback(e,i){return Lg(this,void 0,void 0,function*(){let{lookup:r,proxy:n}=this,{host:s,port:o,timeout:c}=i;if(!s)throw new Error("No `host` defined!");r&&(s=yield Vg(s));let u={proxy:n,destination:{host:s,port:o},command:"connect",timeout:c};_o("Creating socks proxy connection: %o",u);let{socket:h}=yield qg.SocksClient.createConnection(u);if(_o("Successfully created socks proxy connection"),i.secureEndpoint){_o("Upgrading socket connection to TLS");let l=i.servername||i.host;return Mg.default.connect(Object.assign(Object.assign(Object.assign({},$g(i,"host","hostname","path","port")),{socket:h,servername:l}),this.tlsConnectionOptions))}return h})}};wi.default=xo;function $g(t,...e){let i={},r;for(r in t)e.includes(r)||(i[r]=t[r]);return i}});var Zu=E((wo,Ku)=>{"use strict";var Gg=wo&&wo.__importDefault||function(t){return t&&t.__esModule?t:{default:t}},yo=Gg(Yu());function bo(t){return new yo.default(t)}(function(t){t.SocksProxyAgent=yo.default,t.prototype=yo.default.prototype})(bo||(bo={}));Ku.exports=bo});var Ju=E((wy,Xu)=>{"use strict";var zg=/[|\\{}()[\]^$+*?.-]/g;Xu.exports=t=>{if(typeof t!="string")throw new TypeError("Expected a string");return t.replace(zg,"\\$&")}});var rf=E((Ey,tf)=>{"use strict";var Wg=Ju(),Yg=typeof process=="object"&&process&&typeof process.cwd=="function"?process.cwd():".",ef=[].concat(require("module").builtinModules,"bootstrap_node","node").map(t=>new RegExp(`(?:\\((?:node:)?${t}(?:\\.js)?:\\d+:\\d+\\)$|^\\s*at (?:node:)?${t}(?:\\.js)?:\\d+:\\d+$)`));ef.push(/\((?:node:)?internal\/[^:]+:\d+:\d+\)$/,/\s*at (?:node:)?internal\/[^:]+:\d+:\d+$/,/\/\.node-spawn-wrap-\w+-\w+\/node:\d+:\d+\)?$/);var Eo=class t{constructor(e){e={ignoredPackages:[],...e},"internals"in e||(e.internals=t.nodeInternals()),"cwd"in e||(e.cwd=Yg),this._cwd=e.cwd.replace(/\\/g,"/"),this._internals=[].concat(e.internals,Kg(e.ignoredPackages)),this._wrapCallSite=e.wrapCallSite||!1}static nodeInternals(){return[...ef]}clean(e,i=0){i=" ".repeat(i),Array.isArray(e)||(e=e.split(`
`)),!/^\s*at /.test(e[0])&&/^\s*at /.test(e[1])&&(e=e.slice(1));let r=!1,n=null,s=[];return e.forEach(o=>{if(o=o.replace(/\\/g,"/"),this._internals.some(u=>u.test(o)))return;let c=/^\s*at /.test(o);r?o=o.trimEnd().replace(/^(\s+)at /,"$1"):(o=o.trim(),c&&(o=o.slice(3))),o=o.replace(`${this._cwd}/`,""),o&&(c?(n&&(s.push(n),n=null),s.push(o)):(r=!0,n=o))}),s.map(o=>`${i}${o}
`).join("")}captureString(e,i=this.captureString){typeof e=="function"&&(i=e,e=1/0);let{stackTraceLimit:r}=Error;e&&(Error.stackTraceLimit=e);let n={};Error.captureStackTrace(n,i);let{stack:s}=n;return Error.stackTraceLimit=r,this.clean(s)}capture(e,i=this.capture){typeof e=="function"&&(i=e,e=1/0);let{prepareStackTrace:r,stackTraceLimit:n}=Error;Error.prepareStackTrace=(c,u)=>this._wrapCallSite?u.map(this._wrapCallSite):u,e&&(Error.stackTraceLimit=e);let s={};Error.captureStackTrace(s,i);let{stack:o}=s;return Object.assign(Error,{prepareStackTrace:r,stackTraceLimit:n}),o}at(e=this.at){let[i]=this.capture(1,e);if(!i)return{};let r={line:i.getLineNumber(),column:i.getColumnNumber()};Qu(r,i.getFileName(),this._cwd),i.isConstructor()&&(r.constructor=!0),i.isEval()&&(r.evalOrigin=i.getEvalOrigin()),i.isNative()&&(r.native=!0);let n;try{n=i.getTypeName()}catch{}n&&n!=="Object"&&n!=="[object Object]"&&(r.type=n);let s=i.getFunctionName();s&&(r.function=s);let o=i.getMethodName();return o&&s!==o&&(r.method=o),r}parseLine(e){let i=e&&e.match(Zg);if(!i)return null;let r=i[1]==="new",n=i[2],s=i[3],o=i[4],c=Number(i[5]),u=Number(i[6]),h=i[7],l=i[8],d=i[9],m=i[10]==="native",v=i[11]===")",g,x={};if(l&&(x.line=Number(l)),d&&(x.column=Number(d)),v&&h){let y=0;for(let O=h.length-1;O>0;O--)if(h.charAt(O)===")")y++;else if(h.charAt(O)==="("&&h.charAt(O-1)===" "&&(y--,y===-1&&h.charAt(O-1)===" ")){let B=h.slice(0,O-1);h=h.slice(O+1),n+=` (${B}`;break}}if(n){let y=n.match(Xg);y&&(n=y[1],g=y[2])}return Qu(x,h,this._cwd),r&&(x.constructor=!0),s&&(x.evalOrigin=s,x.evalLine=c,x.evalColumn=u,x.evalFile=o&&o.replace(/\\/g,"/")),m&&(x.native=!0),n&&(x.function=n),g&&n!==g&&(x.method=g),x}};function Qu(t,e,i){e&&(e=e.replace(/\\/g,"/"),e.startsWith(`${i}/`)&&(e=e.slice(i.length+1)),t.file=e)}function Kg(t){if(t.length===0)return[];let e=t.map(i=>Wg(i));return new RegExp(`[/\\\\]node_modules[/\\\\](?:${e.join("|")})[/\\\\][^:]+:\\d+:\\d+`)}var Zg=new RegExp("^(?:\\s*at )?(?:(new) )?(?:(.*?) \\()?(?:eval at ([^ ]+) \\((.+?):(\\d+):(\\d+)\\), )?(?:(.+?):(\\d+):(\\d+)|(native))(\\)?)$"),Xg=/^(.*?) \[as (.*?)\]$/;tf.exports=Eo});var af=E((Sy,of)=>{"use strict";var{Duplex:Jg}=require("stream");function nf(t){t.emit("close")}function Qg(){!this.destroyed&&this._writableState.finished&&this.destroy()}function sf(t){this.removeListener("error",sf),this.destroy(),this.listenerCount("error")===0&&this.emit("error",t)}function ev(t,e){let i=!0,r=new Jg({...e,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return t.on("message",function(s,o){let c=!o&&r._readableState.objectMode?s.toString():s;r.push(c)||t.pause()}),t.once("error",function(s){r.destroyed||(i=!1,r.destroy(s))}),t.once("close",function(){r.destroyed||r.push(null)}),r._destroy=function(n,s){if(t.readyState===t.CLOSED){s(n),process.nextTick(nf,r);return}let o=!1;t.once("error",function(u){o=!0,s(u)}),t.once("close",function(){o||s(n),process.nextTick(nf,r)}),i&&t.terminate()},r._final=function(n){if(t.readyState===t.CONNECTING){t.once("open",function(){r._final(n)});return}t._socket!==null&&(t._socket._writableState.finished?(n(),r._readableState.endEmitted&&r.destroy()):(t._socket.once("finish",function(){n()}),t.close()))},r._read=function(){t.isPaused&&t.resume()},r._write=function(n,s,o){if(t.readyState===t.CONNECTING){t.once("open",function(){r._write(n,s,o)});return}t.send(n,o)},r.on("end",Qg),r.on("error",sf),r}of.exports=ev});var ei=E((ky,lf)=>{"use strict";lf.exports={BINARY_TYPES:["nodebuffer","arraybuffer","fragments"],EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}});var _r=E((Oy,vn)=>{"use strict";var{EMPTY_BUFFER:tv}=ei(),So=Buffer[Symbol.species];function iv(t,e){if(t.length===0)return tv;if(t.length===1)return t[0];let i=Buffer.allocUnsafe(e),r=0;for(let n=0;n<t.length;n++){let s=t[n];i.set(s,r),r+=s.length}return r<e?new So(i.buffer,i.byteOffset,r):i}function cf(t,e,i,r,n){for(let s=0;s<n;s++)i[r+s]=t[s]^e[s&3]}function uf(t,e){for(let i=0;i<t.length;i++)t[i]^=e[i&3]}function rv(t){return t.length===t.buffer.byteLength?t.buffer:t.buffer.slice(t.byteOffset,t.byteOffset+t.length)}function ko(t){if(ko.readOnly=!0,Buffer.isBuffer(t))return t;let e;return t instanceof ArrayBuffer?e=new So(t):ArrayBuffer.isView(t)?e=new So(t.buffer,t.byteOffset,t.byteLength):(e=Buffer.from(t),ko.readOnly=!1),e}vn.exports={concat:iv,mask:cf,toArrayBuffer:rv,toBuffer:ko,unmask:uf};if(!process.env.WS_NO_BUFFER_UTIL)try{let t=require("bufferutil");vn.exports.mask=function(e,i,r,n,s){s<48?cf(e,i,r,n,s):t.mask(e,i,r,n,s)},vn.exports.unmask=function(e,i){e.length<32?uf(e,i):t.unmask(e,i)}}catch{}});var pf=E((Cy,hf)=>{"use strict";var ff=Symbol("kDone"),Oo=Symbol("kRun"),Co=class{constructor(e){this[ff]=()=>{this.pending--,this[Oo]()},this.concurrency=e||1/0,this.jobs=[],this.pending=0}add(e){this.jobs.push(e),this[Oo]()}[Oo](){if(this.pending!==this.concurrency&&this.jobs.length){let e=this.jobs.shift();this.pending++,e(this[ff])}}};hf.exports=Co});var br=E((Ty,vf)=>{"use strict";var xr=require("zlib"),df=_r(),nv=pf(),{kStatusCode:mf}=ei(),sv=Buffer[Symbol.species],ov=Buffer.from([0,0,255,255]),yn=Symbol("permessage-deflate"),$t=Symbol("total-length"),yr=Symbol("callback"),ti=Symbol("buffers"),xn=Symbol("error"),_n,To=class{constructor(e,i,r){if(this._maxPayload=r|0,this._options=e||{},this._threshold=this._options.threshold!==void 0?this._options.threshold:1024,this._isServer=!!i,this._deflate=null,this._inflate=null,this.params=null,!_n){let n=this._options.concurrencyLimit!==void 0?this._options.concurrencyLimit:10;_n=new nv(n)}}static get extensionName(){return"permessage-deflate"}offer(){let e={};return this._options.serverNoContextTakeover&&(e.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(e.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(e.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?e.client_max_window_bits=this._options.clientMaxWindowBits:this._options.clientMaxWindowBits==null&&(e.client_max_window_bits=!0),e}accept(e){return e=this.normalizeParams(e),this.params=this._isServer?this.acceptAsServer(e):this.acceptAsClient(e),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){let e=this._deflate[yr];this._deflate.close(),this._deflate=null,e&&e(new Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(e){let i=this._options,r=e.find(n=>!(i.serverNoContextTakeover===!1&&n.server_no_context_takeover||n.server_max_window_bits&&(i.serverMaxWindowBits===!1||typeof i.serverMaxWindowBits=="number"&&i.serverMaxWindowBits>n.server_max_window_bits)||typeof i.clientMaxWindowBits=="number"&&!n.client_max_window_bits));if(!r)throw new Error("None of the extension offers can be accepted");return i.serverNoContextTakeover&&(r.server_no_context_takeover=!0),i.clientNoContextTakeover&&(r.client_no_context_takeover=!0),typeof i.serverMaxWindowBits=="number"&&(r.server_max_window_bits=i.serverMaxWindowBits),typeof i.clientMaxWindowBits=="number"?r.client_max_window_bits=i.clientMaxWindowBits:(r.client_max_window_bits===!0||i.clientMaxWindowBits===!1)&&delete r.client_max_window_bits,r}acceptAsClient(e){let i=e[0];if(this._options.clientNoContextTakeover===!1&&i.client_no_context_takeover)throw new Error('Unexpected parameter "client_no_context_takeover"');if(!i.client_max_window_bits)typeof this._options.clientMaxWindowBits=="number"&&(i.client_max_window_bits=this._options.clientMaxWindowBits);else if(this._options.clientMaxWindowBits===!1||typeof this._options.clientMaxWindowBits=="number"&&i.client_max_window_bits>this._options.clientMaxWindowBits)throw new Error('Unexpected or invalid parameter "client_max_window_bits"');return i}normalizeParams(e){return e.forEach(i=>{Object.keys(i).forEach(r=>{let n=i[r];if(n.length>1)throw new Error(`Parameter "${r}" must have only a single value`);if(n=n[0],r==="client_max_window_bits"){if(n!==!0){let s=+n;if(!Number.isInteger(s)||s<8||s>15)throw new TypeError(`Invalid value for parameter "${r}": ${n}`);n=s}else if(!this._isServer)throw new TypeError(`Invalid value for parameter "${r}": ${n}`)}else if(r==="server_max_window_bits"){let s=+n;if(!Number.isInteger(s)||s<8||s>15)throw new TypeError(`Invalid value for parameter "${r}": ${n}`);n=s}else if(r==="client_no_context_takeover"||r==="server_no_context_takeover"){if(n!==!0)throw new TypeError(`Invalid value for parameter "${r}": ${n}`)}else throw new Error(`Unknown parameter "${r}"`);i[r]=n})}),e}decompress(e,i,r){_n.add(n=>{this._decompress(e,i,(s,o)=>{n(),r(s,o)})})}compress(e,i,r){_n.add(n=>{this._compress(e,i,(s,o)=>{n(),r(s,o)})})}_decompress(e,i,r){let n=this._isServer?"client":"server";if(!this._inflate){let s=`${n}_max_window_bits`,o=typeof this.params[s]!="number"?xr.Z_DEFAULT_WINDOWBITS:this.params[s];this._inflate=xr.createInflateRaw({...this._options.zlibInflateOptions,windowBits:o}),this._inflate[yn]=this,this._inflate[$t]=0,this._inflate[ti]=[],this._inflate.on("error",lv),this._inflate.on("data",gf)}this._inflate[yr]=r,this._inflate.write(e),i&&this._inflate.write(ov),this._inflate.flush(()=>{let s=this._inflate[xn];if(s){this._inflate.close(),this._inflate=null,r(s);return}let o=df.concat(this._inflate[ti],this._inflate[$t]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[$t]=0,this._inflate[ti]=[],i&&this.params[`${n}_no_context_takeover`]&&this._inflate.reset()),r(null,o)})}_compress(e,i,r){let n=this._isServer?"server":"client";if(!this._deflate){let s=`${n}_max_window_bits`,o=typeof this.params[s]!="number"?xr.Z_DEFAULT_WINDOWBITS:this.params[s];this._deflate=xr.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:o}),this._deflate[$t]=0,this._deflate[ti]=[],this._deflate.on("data",av)}this._deflate[yr]=r,this._deflate.write(e),this._deflate.flush(xr.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let s=df.concat(this._deflate[ti],this._deflate[$t]);i&&(s=new sv(s.buffer,s.byteOffset,s.length-4)),this._deflate[yr]=null,this._deflate[$t]=0,this._deflate[ti]=[],i&&this.params[`${n}_no_context_takeover`]&&this._deflate.reset(),r(null,s)})}};vf.exports=To;function av(t){this[ti].push(t),this[$t]+=t.length}function gf(t){if(this[$t]+=t.length,this[yn]._maxPayload<1||this[$t]<=this[yn]._maxPayload){this[ti].push(t);return}this[xn]=new RangeError("Max payload size exceeded"),this[xn].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[xn][mf]=1009,this.removeListener("data",gf),this.reset()}function lv(t){this[yn]._inflate=null,t[mf]=1007,this[yr](t)}});var wr=E((Iy,bn)=>{"use strict";var{isUtf8:_f}=require("buffer"),cv=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0];function uv(t){return t>=1e3&&t<=1014&&t!==1004&&t!==1005&&t!==1006||t>=3e3&&t<=4999}function Io(t){let e=t.length,i=0;for(;i<e;)if(!(t[i]&128))i++;else if((t[i]&224)===192){if(i+1===e||(t[i+1]&192)!==128||(t[i]&254)===192)return!1;i+=2}else if((t[i]&240)===224){if(i+2>=e||(t[i+1]&192)!==128||(t[i+2]&192)!==128||t[i]===224&&(t[i+1]&224)===128||t[i]===237&&(t[i+1]&224)===160)return!1;i+=3}else if((t[i]&248)===240){if(i+3>=e||(t[i+1]&192)!==128||(t[i+2]&192)!==128||(t[i+3]&192)!==128||t[i]===240&&(t[i+1]&240)===128||t[i]===244&&t[i+1]>143||t[i]>244)return!1;i+=4}else return!1;return!0}bn.exports={isValidStatusCode:uv,isValidUTF8:Io,tokenChars:cv};if(_f)bn.exports.isValidUTF8=function(t){return t.length<24?Io(t):_f(t)};else if(!process.env.WS_NO_UTF_8_VALIDATE)try{let t=require("utf-8-validate");bn.exports.isValidUTF8=function(e){return e.length<32?Io(e):t(e)}}catch{}});var No=E((Ay,kf)=>{"use strict";var{Writable:fv}=require("stream"),xf=br(),{BINARY_TYPES:hv,EMPTY_BUFFER:yf,kStatusCode:pv,kWebSocket:dv}=ei(),{concat:Ao,toArrayBuffer:mv,unmask:gv}=_r(),{isValidStatusCode:vv,isValidUTF8:bf}=wr(),wn=Buffer[Symbol.species],yt=0,wf=1,Ef=2,Sf=3,Bo=4,Ro=5,En=6,Po=class extends fv{constructor(e={}){super(),this._allowSynchronousEvents=e.allowSynchronousEvents!==void 0?e.allowSynchronousEvents:!0,this._binaryType=e.binaryType||hv[0],this._extensions=e.extensions||{},this._isServer=!!e.isServer,this._maxPayload=e.maxPayload|0,this._skipUTF8Validation=!!e.skipUTF8Validation,this[dv]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._errored=!1,this._loop=!1,this._state=yt}_write(e,i,r){if(this._opcode===8&&this._state==yt)return r();this._bufferedBytes+=e.length,this._buffers.push(e),this.startLoop(r)}consume(e){if(this._bufferedBytes-=e,e===this._buffers[0].length)return this._buffers.shift();if(e<this._buffers[0].length){let r=this._buffers[0];return this._buffers[0]=new wn(r.buffer,r.byteOffset+e,r.length-e),new wn(r.buffer,r.byteOffset,e)}let i=Buffer.allocUnsafe(e);do{let r=this._buffers[0],n=i.length-e;e>=r.length?i.set(this._buffers.shift(),n):(i.set(new Uint8Array(r.buffer,r.byteOffset,e),n),this._buffers[0]=new wn(r.buffer,r.byteOffset+e,r.length-e)),e-=r.length}while(e>0);return i}startLoop(e){this._loop=!0;do switch(this._state){case yt:this.getInfo(e);break;case wf:this.getPayloadLength16(e);break;case Ef:this.getPayloadLength64(e);break;case Sf:this.getMask();break;case Bo:this.getData(e);break;case Ro:case En:this._loop=!1;return}while(this._loop);this._errored||e()}getInfo(e){if(this._bufferedBytes<2){this._loop=!1;return}let i=this.consume(2);if(i[0]&48){let n=this.createError(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3");e(n);return}let r=(i[0]&64)===64;if(r&&!this._extensions[xf.extensionName]){let n=this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");e(n);return}if(this._fin=(i[0]&128)===128,this._opcode=i[0]&15,this._payloadLength=i[1]&127,this._opcode===0){if(r){let n=this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");e(n);return}if(!this._fragmented){let n=this.createError(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE");e(n);return}this._opcode=this._fragmented}else if(this._opcode===1||this._opcode===2){if(this._fragmented){let n=this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE");e(n);return}this._compressed=r}else if(this._opcode>7&&this._opcode<11){if(!this._fin){let n=this.createError(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN");e(n);return}if(r){let n=this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");e(n);return}if(this._payloadLength>125||this._opcode===8&&this._payloadLength===1){let n=this.createError(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH");e(n);return}}else{let n=this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE");e(n);return}if(!this._fin&&!this._fragmented&&(this._fragmented=this._opcode),this._masked=(i[1]&128)===128,this._isServer){if(!this._masked){let n=this.createError(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK");e(n);return}}else if(this._masked){let n=this.createError(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK");e(n);return}this._payloadLength===126?this._state=wf:this._payloadLength===127?this._state=Ef:this.haveLength(e)}getPayloadLength16(e){if(this._bufferedBytes<2){this._loop=!1;return}this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength(e)}getPayloadLength64(e){if(this._bufferedBytes<8){this._loop=!1;return}let i=this.consume(8),r=i.readUInt32BE(0);if(r>Math.pow(2,53-32)-1){let n=this.createError(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH");e(n);return}this._payloadLength=r*Math.pow(2,32)+i.readUInt32BE(4),this.haveLength(e)}haveLength(e){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0)){let i=this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH");e(i);return}this._masked?this._state=Sf:this._state=Bo}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=Bo}getData(e){let i=yf;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}i=this.consume(this._payloadLength),this._masked&&this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3]&&gv(i,this._mask)}if(this._opcode>7){this.controlMessage(i,e);return}if(this._compressed){this._state=Ro,this.decompress(i,e);return}i.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(i)),this.dataMessage(e)}decompress(e,i){this._extensions[xf.extensionName].decompress(e,this._fin,(n,s)=>{if(n)return i(n);if(s.length){if(this._messageLength+=s.length,this._messageLength>this._maxPayload&&this._maxPayload>0){let o=this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH");i(o);return}this._fragments.push(s)}this.dataMessage(i),this._state===yt&&this.startLoop(i)})}dataMessage(e){if(!this._fin){this._state=yt;return}let i=this._messageLength,r=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],this._opcode===2){let n;this._binaryType==="nodebuffer"?n=Ao(r,i):this._binaryType==="arraybuffer"?n=mv(Ao(r,i)):n=r,this._allowSynchronousEvents?(this.emit("message",n,!0),this._state=yt):(this._state=En,setImmediate(()=>{this.emit("message",n,!0),this._state=yt,this.startLoop(e)}))}else{let n=Ao(r,i);if(!this._skipUTF8Validation&&!bf(n)){let s=this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8");e(s);return}this._state===Ro||this._allowSynchronousEvents?(this.emit("message",n,!1),this._state=yt):(this._state=En,setImmediate(()=>{this.emit("message",n,!1),this._state=yt,this.startLoop(e)}))}}controlMessage(e,i){if(this._opcode===8){if(e.length===0)this._loop=!1,this.emit("conclude",1005,yf),this.end();else{let r=e.readUInt16BE(0);if(!vv(r)){let s=this.createError(RangeError,`invalid status code ${r}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE");i(s);return}let n=new wn(e.buffer,e.byteOffset+2,e.length-2);if(!this._skipUTF8Validation&&!bf(n)){let s=this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8");i(s);return}this._loop=!1,this.emit("conclude",r,n),this.end()}this._state=yt;return}this._allowSynchronousEvents?(this.emit(this._opcode===9?"ping":"pong",e),this._state=yt):(this._state=En,setImmediate(()=>{this.emit(this._opcode===9?"ping":"pong",e),this._state=yt,this.startLoop(i)}))}createError(e,i,r,n,s){this._loop=!1,this._errored=!0;let o=new e(r?`Invalid WebSocket frame: ${i}`:i);return Error.captureStackTrace(o,this.createError),o.code=s,o[pv]=n,o}};kf.exports=Po});var Fo=E((Ry,Tf)=>{"use strict";var{Duplex:By}=require("stream"),{randomFillSync:_v}=require("crypto"),Of=br(),{EMPTY_BUFFER:xv}=ei(),{isValidStatusCode:yv}=wr(),{mask:Cf,toBuffer:er}=_r(),At=Symbol("kByteLength"),bv=Buffer.alloc(4),Sn=8*1024,Ei,tr=Sn,Lo=class t{constructor(e,i,r){this._extensions=i||{},r&&(this._generateMask=r,this._maskBuffer=Buffer.alloc(4)),this._socket=e,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._deflating=!1,this._queue=[]}static frame(e,i){let r,n=!1,s=2,o=!1;i.mask&&(r=i.maskBuffer||bv,i.generateMask?i.generateMask(r):(tr===Sn&&(Ei===void 0&&(Ei=Buffer.alloc(Sn)),_v(Ei,0,Sn),tr=0),r[0]=Ei[tr++],r[1]=Ei[tr++],r[2]=Ei[tr++],r[3]=Ei[tr++]),o=(r[0]|r[1]|r[2]|r[3])===0,s=6);let c;typeof e=="string"?(!i.mask||o)&&i[At]!==void 0?c=i[At]:(e=Buffer.from(e),c=e.length):(c=e.length,n=i.mask&&i.readOnly&&!o);let u=c;c>=65536?(s+=8,u=127):c>125&&(s+=2,u=126);let h=Buffer.allocUnsafe(n?c+s:s);return h[0]=i.fin?i.opcode|128:i.opcode,i.rsv1&&(h[0]|=64),h[1]=u,u===126?h.writeUInt16BE(c,2):u===127&&(h[2]=h[3]=0,h.writeUIntBE(c,4,6)),i.mask?(h[1]|=128,h[s-4]=r[0],h[s-3]=r[1],h[s-2]=r[2],h[s-1]=r[3],o?[h,e]:n?(Cf(e,r,h,s,c),[h]):(Cf(e,r,e,0,c),[h,e])):[h,e]}close(e,i,r,n){let s;if(e===void 0)s=xv;else{if(typeof e!="number"||!yv(e))throw new TypeError("First argument must be a valid error code number");if(i===void 0||!i.length)s=Buffer.allocUnsafe(2),s.writeUInt16BE(e,0);else{let c=Buffer.byteLength(i);if(c>123)throw new RangeError("The message must not be greater than 123 bytes");s=Buffer.allocUnsafe(2+c),s.writeUInt16BE(e,0),typeof i=="string"?s.write(i,2):s.set(i,2)}}let o={[At]:s.length,fin:!0,generateMask:this._generateMask,mask:r,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};this._deflating?this.enqueue([this.dispatch,s,!1,o,n]):this.sendFrame(t.frame(s,o),n)}ping(e,i,r){let n,s;if(typeof e=="string"?(n=Buffer.byteLength(e),s=!1):(e=er(e),n=e.length,s=er.readOnly),n>125)throw new RangeError("The data size must not be greater than 125 bytes");let o={[At]:n,fin:!0,generateMask:this._generateMask,mask:i,maskBuffer:this._maskBuffer,opcode:9,readOnly:s,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,o,r]):this.sendFrame(t.frame(e,o),r)}pong(e,i,r){let n,s;if(typeof e=="string"?(n=Buffer.byteLength(e),s=!1):(e=er(e),n=e.length,s=er.readOnly),n>125)throw new RangeError("The data size must not be greater than 125 bytes");let o={[At]:n,fin:!0,generateMask:this._generateMask,mask:i,maskBuffer:this._maskBuffer,opcode:10,readOnly:s,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,o,r]):this.sendFrame(t.frame(e,o),r)}send(e,i,r){let n=this._extensions[Of.extensionName],s=i.binary?2:1,o=i.compress,c,u;if(typeof e=="string"?(c=Buffer.byteLength(e),u=!1):(e=er(e),c=e.length,u=er.readOnly),this._firstFragment?(this._firstFragment=!1,o&&n&&n.params[n._isServer?"server_no_context_takeover":"client_no_context_takeover"]&&(o=c>=n._threshold),this._compress=o):(o=!1,s=0),i.fin&&(this._firstFragment=!0),n){let h={[At]:c,fin:i.fin,generateMask:this._generateMask,mask:i.mask,maskBuffer:this._maskBuffer,opcode:s,readOnly:u,rsv1:o};this._deflating?this.enqueue([this.dispatch,e,this._compress,h,r]):this.dispatch(e,this._compress,h,r)}else this.sendFrame(t.frame(e,{[At]:c,fin:i.fin,generateMask:this._generateMask,mask:i.mask,maskBuffer:this._maskBuffer,opcode:s,readOnly:u,rsv1:!1}),r)}dispatch(e,i,r,n){if(!i){this.sendFrame(t.frame(e,r),n);return}let s=this._extensions[Of.extensionName];this._bufferedBytes+=r[At],this._deflating=!0,s.compress(e,r.fin,(o,c)=>{if(this._socket.destroyed){let u=new Error("The socket was closed while data was being compressed");typeof n=="function"&&n(u);for(let h=0;h<this._queue.length;h++){let l=this._queue[h],d=l[l.length-1];typeof d=="function"&&d(u)}return}this._bufferedBytes-=r[At],this._deflating=!1,r.readOnly=!1,this.sendFrame(t.frame(c,r),n),this.dequeue()})}dequeue(){for(;!this._deflating&&this._queue.length;){let e=this._queue.shift();this._bufferedBytes-=e[3][At],Reflect.apply(e[0],this,e.slice(1))}}enqueue(e){this._bufferedBytes+=e[3][At],this._queue.push(e)}sendFrame(e,i){e.length===2?(this._socket.cork(),this._socket.write(e[0]),this._socket.write(e[1],i),this._socket.uncork()):this._socket.write(e[0],i)}};Tf.exports=Lo});var Mf=E((Py,Ff)=>{"use strict";var{kForOnEventAttribute:Er,kListener:Mo}=ei(),If=Symbol("kCode"),Af=Symbol("kData"),Bf=Symbol("kError"),Rf=Symbol("kMessage"),Pf=Symbol("kReason"),ir=Symbol("kTarget"),Nf=Symbol("kType"),Lf=Symbol("kWasClean"),Gt=class{constructor(e){this[ir]=null,this[Nf]=e}get target(){return this[ir]}get type(){return this[Nf]}};Object.defineProperty(Gt.prototype,"target",{enumerable:!0});Object.defineProperty(Gt.prototype,"type",{enumerable:!0});var Si=class extends Gt{constructor(e,i={}){super(e),this[If]=i.code===void 0?0:i.code,this[Pf]=i.reason===void 0?"":i.reason,this[Lf]=i.wasClean===void 0?!1:i.wasClean}get code(){return this[If]}get reason(){return this[Pf]}get wasClean(){return this[Lf]}};Object.defineProperty(Si.prototype,"code",{enumerable:!0});Object.defineProperty(Si.prototype,"reason",{enumerable:!0});Object.defineProperty(Si.prototype,"wasClean",{enumerable:!0});var rr=class extends Gt{constructor(e,i={}){super(e),this[Bf]=i.error===void 0?null:i.error,this[Rf]=i.message===void 0?"":i.message}get error(){return this[Bf]}get message(){return this[Rf]}};Object.defineProperty(rr.prototype,"error",{enumerable:!0});Object.defineProperty(rr.prototype,"message",{enumerable:!0});var Sr=class extends Gt{constructor(e,i={}){super(e),this[Af]=i.data===void 0?null:i.data}get data(){return this[Af]}};Object.defineProperty(Sr.prototype,"data",{enumerable:!0});var wv={addEventListener(t,e,i={}){for(let n of this.listeners(t))if(!i[Er]&&n[Mo]===e&&!n[Er])return;let r;if(t==="message")r=function(s,o){let c=new Sr("message",{data:o?s:s.toString()});c[ir]=this,kn(e,this,c)};else if(t==="close")r=function(s,o){let c=new Si("close",{code:s,reason:o.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});c[ir]=this,kn(e,this,c)};else if(t==="error")r=function(s){let o=new rr("error",{error:s,message:s.message});o[ir]=this,kn(e,this,o)};else if(t==="open")r=function(){let s=new Gt("open");s[ir]=this,kn(e,this,s)};else return;r[Er]=!!i[Er],r[Mo]=e,i.once?this.once(t,r):this.on(t,r)},removeEventListener(t,e){for(let i of this.listeners(t))if(i[Mo]===e&&!i[Er]){this.removeListener(t,i);break}}};Ff.exports={CloseEvent:Si,ErrorEvent:rr,Event:Gt,EventTarget:wv,MessageEvent:Sr};function kn(t,e,i){typeof t=="object"&&t.handleEvent?t.handleEvent.call(t,i):t.call(e,i)}});var Do=E((Ny,Df)=>{"use strict";var{tokenChars:kr}=wr();function Mt(t,e,i){t[e]===void 0?t[e]=[i]:t[e].push(i)}function Ev(t){let e=Object.create(null),i=Object.create(null),r=!1,n=!1,s=!1,o,c,u=-1,h=-1,l=-1,d=0;for(;d<t.length;d++)if(h=t.charCodeAt(d),o===void 0)if(l===-1&&kr[h]===1)u===-1&&(u=d);else if(d!==0&&(h===32||h===9))l===-1&&u!==-1&&(l=d);else if(h===59||h===44){if(u===-1)throw new SyntaxError(`Unexpected character at index ${d}`);l===-1&&(l=d);let v=t.slice(u,l);h===44?(Mt(e,v,i),i=Object.create(null)):o=v,u=l=-1}else throw new SyntaxError(`Unexpected character at index ${d}`);else if(c===void 0)if(l===-1&&kr[h]===1)u===-1&&(u=d);else if(h===32||h===9)l===-1&&u!==-1&&(l=d);else if(h===59||h===44){if(u===-1)throw new SyntaxError(`Unexpected character at index ${d}`);l===-1&&(l=d),Mt(i,t.slice(u,l),!0),h===44&&(Mt(e,o,i),i=Object.create(null),o=void 0),u=l=-1}else if(h===61&&u!==-1&&l===-1)c=t.slice(u,d),u=l=-1;else throw new SyntaxError(`Unexpected character at index ${d}`);else if(n){if(kr[h]!==1)throw new SyntaxError(`Unexpected character at index ${d}`);u===-1?u=d:r||(r=!0),n=!1}else if(s)if(kr[h]===1)u===-1&&(u=d);else if(h===34&&u!==-1)s=!1,l=d;else if(h===92)n=!0;else throw new SyntaxError(`Unexpected character at index ${d}`);else if(h===34&&t.charCodeAt(d-1)===61)s=!0;else if(l===-1&&kr[h]===1)u===-1&&(u=d);else if(u!==-1&&(h===32||h===9))l===-1&&(l=d);else if(h===59||h===44){if(u===-1)throw new SyntaxError(`Unexpected character at index ${d}`);l===-1&&(l=d);let v=t.slice(u,l);r&&(v=v.replace(/\\/g,""),r=!1),Mt(i,c,v),h===44&&(Mt(e,o,i),i=Object.create(null),o=void 0),c=void 0,u=l=-1}else throw new SyntaxError(`Unexpected character at index ${d}`);if(u===-1||s||h===32||h===9)throw new SyntaxError("Unexpected end of input");l===-1&&(l=d);let m=t.slice(u,l);return o===void 0?Mt(e,m,i):(c===void 0?Mt(i,m,!0):r?Mt(i,c,m.replace(/\\/g,"")):Mt(i,c,m),Mt(e,o,i)),e}function Sv(t){return Object.keys(t).map(e=>{let i=t[e];return Array.isArray(i)||(i=[i]),i.map(r=>[e].concat(Object.keys(r).map(n=>{let s=r[n];return Array.isArray(s)||(s=[s]),s.map(o=>o===!0?n:`${n}=${o}`).join("; ")})).join("; ")).join(", ")}).join(", ")}Df.exports={format:Sv,parse:Ev}});var Ho=E((My,Kf)=>{"use strict";var kv=require("events"),Ov=require("https"),Cv=require("http"),qf=require("net"),Tv=require("tls"),{randomBytes:Iv,createHash:Av}=require("crypto"),{Duplex:Ly,Readable:Fy}=require("stream"),{URL:Uo}=require("url"),ii=br(),Bv=No(),Rv=Fo(),{BINARY_TYPES:Uf,EMPTY_BUFFER:On,GUID:Pv,kForOnEventAttribute:jo,kListener:Nv,kStatusCode:Lv,kWebSocket:Qe,NOOP:Vf}=ei(),{EventTarget:{addEventListener:Fv,removeEventListener:Mv}}=Mf(),{format:Dv,parse:Uv}=Do(),{toBuffer:jv}=_r(),qv=30*1e3,Hf=Symbol("kAborted"),qo=[8,13],zt=["CONNECTING","OPEN","CLOSING","CLOSED"],Vv=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/,Fe=class t extends kv{constructor(e,i,r){super(),this._binaryType=Uf[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=On,this._closeTimer=null,this._extensions={},this._paused=!1,this._protocol="",this._readyState=t.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,e!==null?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,i===void 0?i=[]:Array.isArray(i)||(typeof i=="object"&&i!==null?(r=i,i=[]):i=[i]),$f(this,e,i,r)):(this._autoPong=r.autoPong,this._isServer=!0)}get binaryType(){return this._binaryType}set binaryType(e){Uf.includes(e)&&(this._binaryType=e,this._receiver&&(this._receiver._binaryType=e))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(e,i,r){let n=new Bv({allowSynchronousEvents:r.allowSynchronousEvents,binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:r.maxPayload,skipUTF8Validation:r.skipUTF8Validation});this._sender=new Rv(e,this._extensions,r.generateMask),this._receiver=n,this._socket=e,n[Qe]=this,e[Qe]=this,n.on("conclude",Gv),n.on("drain",zv),n.on("error",Wv),n.on("message",Yv),n.on("ping",Kv),n.on("pong",Zv),e.setTimeout&&e.setTimeout(0),e.setNoDelay&&e.setNoDelay(),i.length>0&&e.unshift(i),e.on("close",zf),e.on("data",Tn),e.on("end",Wf),e.on("error",Yf),this._readyState=t.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=t.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}this._extensions[ii.extensionName]&&this._extensions[ii.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=t.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(e,i){if(this.readyState!==t.CLOSED){if(this.readyState===t.CONNECTING){let r="WebSocket was closed before the connection was established";mt(this,this._req,r);return}if(this.readyState===t.CLOSING){this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end();return}this._readyState=t.CLOSING,this._sender.close(e,i,!this._isServer,r=>{r||(this._closeFrameSent=!0,(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end())}),this._closeTimer=setTimeout(this._socket.destroy.bind(this._socket),qv)}}pause(){this.readyState===t.CONNECTING||this.readyState===t.CLOSED||(this._paused=!0,this._socket.pause())}ping(e,i,r){if(this.readyState===t.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof e=="function"?(r=e,e=i=void 0):typeof i=="function"&&(r=i,i=void 0),typeof e=="number"&&(e=e.toString()),this.readyState!==t.OPEN){Vo(this,e,r);return}i===void 0&&(i=!this._isServer),this._sender.ping(e||On,i,r)}pong(e,i,r){if(this.readyState===t.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof e=="function"?(r=e,e=i=void 0):typeof i=="function"&&(r=i,i=void 0),typeof e=="number"&&(e=e.toString()),this.readyState!==t.OPEN){Vo(this,e,r);return}i===void 0&&(i=!this._isServer),this._sender.pong(e||On,i,r)}resume(){this.readyState===t.CONNECTING||this.readyState===t.CLOSED||(this._paused=!1,this._receiver._writableState.needDrain||this._socket.resume())}send(e,i,r){if(this.readyState===t.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof i=="function"&&(r=i,i={}),typeof e=="number"&&(e=e.toString()),this.readyState!==t.OPEN){Vo(this,e,r);return}let n={binary:typeof e!="string",mask:!this._isServer,compress:!0,fin:!0,...i};this._extensions[ii.extensionName]||(n.compress=!1),this._sender.send(e||On,n,r)}terminate(){if(this.readyState!==t.CLOSED){if(this.readyState===t.CONNECTING){let e="WebSocket was closed before the connection was established";mt(this,this._req,e);return}this._socket&&(this._readyState=t.CLOSING,this._socket.destroy())}}};Object.defineProperty(Fe,"CONNECTING",{enumerable:!0,value:zt.indexOf("CONNECTING")});Object.defineProperty(Fe.prototype,"CONNECTING",{enumerable:!0,value:zt.indexOf("CONNECTING")});Object.defineProperty(Fe,"OPEN",{enumerable:!0,value:zt.indexOf("OPEN")});Object.defineProperty(Fe.prototype,"OPEN",{enumerable:!0,value:zt.indexOf("OPEN")});Object.defineProperty(Fe,"CLOSING",{enumerable:!0,value:zt.indexOf("CLOSING")});Object.defineProperty(Fe.prototype,"CLOSING",{enumerable:!0,value:zt.indexOf("CLOSING")});Object.defineProperty(Fe,"CLOSED",{enumerable:!0,value:zt.indexOf("CLOSED")});Object.defineProperty(Fe.prototype,"CLOSED",{enumerable:!0,value:zt.indexOf("CLOSED")});["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach(t=>{Object.defineProperty(Fe.prototype,t,{enumerable:!0})});["open","error","close","message"].forEach(t=>{Object.defineProperty(Fe.prototype,`on${t}`,{enumerable:!0,get(){for(let e of this.listeners(t))if(e[jo])return e[Nv];return null},set(e){for(let i of this.listeners(t))if(i[jo]){this.removeListener(t,i);break}typeof e=="function"&&this.addEventListener(t,e,{[jo]:!0})}})});Fe.prototype.addEventListener=Fv;Fe.prototype.removeEventListener=Mv;Kf.exports=Fe;function $f(t,e,i,r){let n={allowSynchronousEvents:!0,autoPong:!0,protocolVersion:qo[1],maxPayload:*********,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...r,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:"GET",host:void 0,path:void 0,port:void 0};if(t._autoPong=n.autoPong,!qo.includes(n.protocolVersion))throw new RangeError(`Unsupported protocol version: ${n.protocolVersion} (supported versions: ${qo.join(", ")})`);let s;if(e instanceof Uo)s=e;else try{s=new Uo(e)}catch{throw new SyntaxError(`Invalid URL: ${e}`)}s.protocol==="http:"?s.protocol="ws:":s.protocol==="https:"&&(s.protocol="wss:"),t._url=s.href;let o=s.protocol==="wss:",c=s.protocol==="ws+unix:",u;if(s.protocol!=="ws:"&&!o&&!c?u=`The URL's protocol must be one of "ws:", "wss:", "http:", "https", or "ws+unix:"`:c&&!s.pathname?u="The URL's pathname is empty":s.hash&&(u="The URL contains a fragment identifier"),u){let x=new SyntaxError(u);if(t._redirects===0)throw x;Cn(t,x);return}let h=o?443:80,l=Iv(16).toString("base64"),d=o?Ov.request:Cv.request,m=new Set,v;if(n.createConnection=n.createConnection||(o?$v:Hv),n.defaultPort=n.defaultPort||h,n.port=s.port||h,n.host=s.hostname.startsWith("[")?s.hostname.slice(1,-1):s.hostname,n.headers={...n.headers,"Sec-WebSocket-Version":n.protocolVersion,"Sec-WebSocket-Key":l,Connection:"Upgrade",Upgrade:"websocket"},n.path=s.pathname+s.search,n.timeout=n.handshakeTimeout,n.perMessageDeflate&&(v=new ii(n.perMessageDeflate!==!0?n.perMessageDeflate:{},!1,n.maxPayload),n.headers["Sec-WebSocket-Extensions"]=Dv({[ii.extensionName]:v.offer()})),i.length){for(let x of i){if(typeof x!="string"||!Vv.test(x)||m.has(x))throw new SyntaxError("An invalid or duplicated subprotocol was specified");m.add(x)}n.headers["Sec-WebSocket-Protocol"]=i.join(",")}if(n.origin&&(n.protocolVersion<13?n.headers["Sec-WebSocket-Origin"]=n.origin:n.headers.Origin=n.origin),(s.username||s.password)&&(n.auth=`${s.username}:${s.password}`),c){let x=n.path.split(":");n.socketPath=x[0],n.path=x[1]}let g;if(n.followRedirects){if(t._redirects===0){t._originalIpc=c,t._originalSecure=o,t._originalHostOrSocketPath=c?n.socketPath:s.host;let x=r&&r.headers;if(r={...r,headers:{}},x)for(let[y,O]of Object.entries(x))r.headers[y.toLowerCase()]=O}else if(t.listenerCount("redirect")===0){let x=c?t._originalIpc?n.socketPath===t._originalHostOrSocketPath:!1:t._originalIpc?!1:s.host===t._originalHostOrSocketPath;(!x||t._originalSecure&&!o)&&(delete n.headers.authorization,delete n.headers.cookie,x||delete n.headers.host,n.auth=void 0)}n.auth&&!r.headers.authorization&&(r.headers.authorization="Basic "+Buffer.from(n.auth).toString("base64")),g=t._req=d(n),t._redirects&&t.emit("redirect",t.url,g)}else g=t._req=d(n);n.timeout&&g.on("timeout",()=>{mt(t,g,"Opening handshake has timed out")}),g.on("error",x=>{g===null||g[Hf]||(g=t._req=null,Cn(t,x))}),g.on("response",x=>{let y=x.headers.location,O=x.statusCode;if(y&&n.followRedirects&&O>=300&&O<400){if(++t._redirects>n.maxRedirects){mt(t,g,"Maximum redirects exceeded");return}g.abort();let B;try{B=new Uo(y,e)}catch{let P=new SyntaxError(`Invalid URL: ${y}`);Cn(t,P);return}$f(t,B,i,r)}else t.emit("unexpected-response",g,x)||mt(t,g,`Unexpected server response: ${x.statusCode}`)}),g.on("upgrade",(x,y,O)=>{if(t.emit("upgrade",x),t.readyState!==Fe.CONNECTING)return;g=t._req=null;let B=x.headers.upgrade;if(B===void 0||B.toLowerCase()!=="websocket"){mt(t,y,"Invalid Upgrade header");return}let C=Av("sha1").update(l+Pv).digest("base64");if(x.headers["sec-websocket-accept"]!==C){mt(t,y,"Invalid Sec-WebSocket-Accept header");return}let P=x.headers["sec-websocket-protocol"],S;if(P!==void 0?m.size?m.has(P)||(S="Server sent an invalid subprotocol"):S="Server sent a subprotocol but none was requested":m.size&&(S="Server sent no subprotocol"),S){mt(t,y,S);return}P&&(t._protocol=P);let J=x.headers["sec-websocket-extensions"];if(J!==void 0){if(!v){mt(t,y,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");return}let A;try{A=Uv(J)}catch{mt(t,y,"Invalid Sec-WebSocket-Extensions header");return}let z=Object.keys(A);if(z.length!==1||z[0]!==ii.extensionName){mt(t,y,"Server indicated an extension that was not requested");return}try{v.accept(A[ii.extensionName])}catch{mt(t,y,"Invalid Sec-WebSocket-Extensions header");return}t._extensions[ii.extensionName]=v}t.setSocket(y,O,{allowSynchronousEvents:n.allowSynchronousEvents,generateMask:n.generateMask,maxPayload:n.maxPayload,skipUTF8Validation:n.skipUTF8Validation})}),n.finishRequest?n.finishRequest(g,t):g.end()}function Cn(t,e){t._readyState=Fe.CLOSING,t.emit("error",e),t.emitClose()}function Hv(t){return t.path=t.socketPath,qf.connect(t)}function $v(t){return t.path=void 0,!t.servername&&t.servername!==""&&(t.servername=qf.isIP(t.host)?"":t.host),Tv.connect(t)}function mt(t,e,i){t._readyState=Fe.CLOSING;let r=new Error(i);Error.captureStackTrace(r,mt),e.setHeader?(e[Hf]=!0,e.abort(),e.socket&&!e.socket.destroyed&&e.socket.destroy(),process.nextTick(Cn,t,r)):(e.destroy(r),e.once("error",t.emit.bind(t,"error")),e.once("close",t.emitClose.bind(t)))}function Vo(t,e,i){if(e){let r=jv(e).length;t._socket?t._sender._bufferedBytes+=r:t._bufferedAmount+=r}if(i){let r=new Error(`WebSocket is not open: readyState ${t.readyState} (${zt[t.readyState]})`);process.nextTick(i,r)}}function Gv(t,e){let i=this[Qe];i._closeFrameReceived=!0,i._closeMessage=e,i._closeCode=t,i._socket[Qe]!==void 0&&(i._socket.removeListener("data",Tn),process.nextTick(Gf,i._socket),t===1005?i.close():i.close(t,e))}function zv(){let t=this[Qe];t.isPaused||t._socket.resume()}function Wv(t){let e=this[Qe];e._socket[Qe]!==void 0&&(e._socket.removeListener("data",Tn),process.nextTick(Gf,e._socket),e.close(t[Lv])),e.emit("error",t)}function jf(){this[Qe].emitClose()}function Yv(t,e){this[Qe].emit("message",t,e)}function Kv(t){let e=this[Qe];e._autoPong&&e.pong(t,!this._isServer,Vf),e.emit("ping",t)}function Zv(t){this[Qe].emit("pong",t)}function Gf(t){t.resume()}function zf(){let t=this[Qe];this.removeListener("close",zf),this.removeListener("data",Tn),this.removeListener("end",Wf),t._readyState=Fe.CLOSING;let e;!this._readableState.endEmitted&&!t._closeFrameReceived&&!t._receiver._writableState.errorEmitted&&(e=t._socket.read())!==null&&t._receiver.write(e),t._receiver.end(),this[Qe]=void 0,clearTimeout(t._closeTimer),t._receiver._writableState.finished||t._receiver._writableState.errorEmitted?t.emitClose():(t._receiver.on("error",jf),t._receiver.on("finish",jf))}function Tn(t){this[Qe]._receiver.write(t)||this.pause()}function Wf(){let t=this[Qe];t._readyState=Fe.CLOSING,t._receiver.end(),this.end()}function Yf(){let t=this[Qe];this.removeListener("error",Yf),this.on("error",Vf),t&&(t._readyState=Fe.CLOSING,this.destroy())}});var Xf=E((Dy,Zf)=>{"use strict";var{tokenChars:Xv}=wr();function Jv(t){let e=new Set,i=-1,r=-1,n=0;for(n;n<t.length;n++){let o=t.charCodeAt(n);if(r===-1&&Xv[o]===1)i===-1&&(i=n);else if(n!==0&&(o===32||o===9))r===-1&&i!==-1&&(r=n);else if(o===44){if(i===-1)throw new SyntaxError(`Unexpected character at index ${n}`);r===-1&&(r=n);let c=t.slice(i,r);if(e.has(c))throw new SyntaxError(`The "${c}" subprotocol is duplicated`);e.add(c),i=r=-1}else throw new SyntaxError(`Unexpected character at index ${n}`)}if(i===-1||r!==-1)throw new SyntaxError("Unexpected end of input");let s=t.slice(i,n);if(e.has(s))throw new SyntaxError(`The "${s}" subprotocol is duplicated`);return e.add(s),e}Zf.exports={parse:Jv}});var nh=E((jy,rh)=>{"use strict";var Qv=require("events"),In=require("http"),{Duplex:Uy}=require("stream"),{createHash:e_}=require("crypto"),Jf=Do(),ki=br(),t_=Xf(),i_=Ho(),{GUID:r_,kWebSocket:n_}=ei(),s_=/^[+/0-9A-Za-z]{22}==$/,Qf=0,eh=1,ih=2,$o=class extends Qv{constructor(e,i){if(super(),e={allowSynchronousEvents:!0,autoPong:!0,maxPayload:100*1024*1024,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,WebSocket:i_,...e},e.port==null&&!e.server&&!e.noServer||e.port!=null&&(e.server||e.noServer)||e.server&&e.noServer)throw new TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(e.port!=null?(this._server=In.createServer((r,n)=>{let s=In.STATUS_CODES[426];n.writeHead(426,{"Content-Length":s.length,"Content-Type":"text/plain"}),n.end(s)}),this._server.listen(e.port,e.host,e.backlog,i)):e.server&&(this._server=e.server),this._server){let r=this.emit.bind(this,"connection");this._removeListeners=o_(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(n,s,o)=>{this.handleUpgrade(n,s,o,r)}})}e.perMessageDeflate===!0&&(e.perMessageDeflate={}),e.clientTracking&&(this.clients=new Set,this._shouldEmitClose=!1),this.options=e,this._state=Qf}address(){if(this.options.noServer)throw new Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(e){if(this._state===ih){e&&this.once("close",()=>{e(new Error("The server is not running"))}),process.nextTick(Or,this);return}if(e&&this.once("close",e),this._state!==eh)if(this._state=eh,this.options.noServer||this.options.server)this._server&&(this._removeListeners(),this._removeListeners=this._server=null),this.clients?this.clients.size?this._shouldEmitClose=!0:process.nextTick(Or,this):process.nextTick(Or,this);else{let i=this._server;this._removeListeners(),this._removeListeners=this._server=null,i.close(()=>{Or(this)})}}shouldHandle(e){if(this.options.path){let i=e.url.indexOf("?");if((i!==-1?e.url.slice(0,i):e.url)!==this.options.path)return!1}return!0}handleUpgrade(e,i,r,n){i.on("error",th);let s=e.headers["sec-websocket-key"],o=e.headers.upgrade,c=+e.headers["sec-websocket-version"];if(e.method!=="GET"){Oi(this,e,i,405,"Invalid HTTP method");return}if(o===void 0||o.toLowerCase()!=="websocket"){Oi(this,e,i,400,"Invalid Upgrade header");return}if(s===void 0||!s_.test(s)){Oi(this,e,i,400,"Missing or invalid Sec-WebSocket-Key header");return}if(c!==8&&c!==13){Oi(this,e,i,400,"Missing or invalid Sec-WebSocket-Version header");return}if(!this.shouldHandle(e)){Cr(i,400);return}let u=e.headers["sec-websocket-protocol"],h=new Set;if(u!==void 0)try{h=t_.parse(u)}catch{Oi(this,e,i,400,"Invalid Sec-WebSocket-Protocol header");return}let l=e.headers["sec-websocket-extensions"],d={};if(this.options.perMessageDeflate&&l!==void 0){let m=new ki(this.options.perMessageDeflate,!0,this.options.maxPayload);try{let v=Jf.parse(l);v[ki.extensionName]&&(m.accept(v[ki.extensionName]),d[ki.extensionName]=m)}catch{Oi(this,e,i,400,"Invalid or unacceptable Sec-WebSocket-Extensions header");return}}if(this.options.verifyClient){let m={origin:e.headers[`${c===8?"sec-websocket-origin":"origin"}`],secure:!!(e.socket.authorized||e.socket.encrypted),req:e};if(this.options.verifyClient.length===2){this.options.verifyClient(m,(v,g,x,y)=>{if(!v)return Cr(i,g||401,x,y);this.completeUpgrade(d,s,h,e,i,r,n)});return}if(!this.options.verifyClient(m))return Cr(i,401)}this.completeUpgrade(d,s,h,e,i,r,n)}completeUpgrade(e,i,r,n,s,o,c){if(!s.readable||!s.writable)return s.destroy();if(s[n_])throw new Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>Qf)return Cr(s,503);let h=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${e_("sha1").update(i+r_).digest("base64")}`],l=new this.options.WebSocket(null,void 0,this.options);if(r.size){let d=this.options.handleProtocols?this.options.handleProtocols(r,n):r.values().next().value;d&&(h.push(`Sec-WebSocket-Protocol: ${d}`),l._protocol=d)}if(e[ki.extensionName]){let d=e[ki.extensionName].params,m=Jf.format({[ki.extensionName]:[d]});h.push(`Sec-WebSocket-Extensions: ${m}`),l._extensions=e}this.emit("headers",h,n),s.write(h.concat(`\r
`).join(`\r
`)),s.removeListener("error",th),l.setSocket(s,o,{allowSynchronousEvents:this.options.allowSynchronousEvents,maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients&&(this.clients.add(l),l.on("close",()=>{this.clients.delete(l),this._shouldEmitClose&&!this.clients.size&&process.nextTick(Or,this)})),c(l,n)}};rh.exports=$o;function o_(t,e){for(let i of Object.keys(e))t.on(i,e[i]);return function(){for(let r of Object.keys(e))t.removeListener(r,e[r])}}function Or(t){t._state=ih,t.emit("close")}function th(){this.destroy()}function Cr(t,e,i,r){i=i||In.STATUS_CODES[e],r={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(i),...r},t.once("finish",t.destroy),t.end(`HTTP/1.1 ${e} ${In.STATUS_CODES[e]}\r
`+Object.keys(r).map(n=>`${n}: ${r[n]}`).join(`\r
`)+`\r
\r
`+i)}function Oi(t,e,i,r,n){if(t.listenerCount("wsClientError")){let s=new Error(n);Error.captureStackTrace(s,Oi),t.emit("wsClientError",s,i,e)}else Cr(i,r,n)}});var lh=E((Vy,ah)=>{var ri=require("constants"),l_=process.cwd,An=null,c_=process.env.GRACEFUL_FS_PLATFORM||process.platform;process.cwd=function(){return An||(An=l_.call(process)),An};try{process.cwd()}catch{}typeof process.chdir=="function"&&(Yo=process.chdir,process.chdir=function(t){An=null,Yo.call(process,t)},Object.setPrototypeOf&&Object.setPrototypeOf(process.chdir,Yo));var Yo;ah.exports=u_;function u_(t){ri.hasOwnProperty("O_SYMLINK")&&process.version.match(/^v0\.6\.[0-2]|^v0\.5\./)&&e(t),t.lutimes||i(t),t.chown=s(t.chown),t.fchown=s(t.fchown),t.lchown=s(t.lchown),t.chmod=r(t.chmod),t.fchmod=r(t.fchmod),t.lchmod=r(t.lchmod),t.chownSync=o(t.chownSync),t.fchownSync=o(t.fchownSync),t.lchownSync=o(t.lchownSync),t.chmodSync=n(t.chmodSync),t.fchmodSync=n(t.fchmodSync),t.lchmodSync=n(t.lchmodSync),t.stat=c(t.stat),t.fstat=c(t.fstat),t.lstat=c(t.lstat),t.statSync=u(t.statSync),t.fstatSync=u(t.fstatSync),t.lstatSync=u(t.lstatSync),t.chmod&&!t.lchmod&&(t.lchmod=function(l,d,m){m&&process.nextTick(m)},t.lchmodSync=function(){}),t.chown&&!t.lchown&&(t.lchown=function(l,d,m,v){v&&process.nextTick(v)},t.lchownSync=function(){}),c_==="win32"&&(t.rename=typeof t.rename!="function"?t.rename:function(l){function d(m,v,g){var x=Date.now(),y=0;l(m,v,function O(B){if(B&&(B.code==="EACCES"||B.code==="EPERM")&&Date.now()-x<6e4){setTimeout(function(){t.stat(v,function(C,P){C&&C.code==="ENOENT"?l(m,v,O):g(B)})},y),y<100&&(y+=10);return}g&&g(B)})}return Object.setPrototypeOf&&Object.setPrototypeOf(d,l),d}(t.rename)),t.read=typeof t.read!="function"?t.read:function(l){function d(m,v,g,x,y,O){var B;if(O&&typeof O=="function"){var C=0;B=function(P,S,J){if(P&&P.code==="EAGAIN"&&C<10)return C++,l.call(t,m,v,g,x,y,B);O.apply(this,arguments)}}return l.call(t,m,v,g,x,y,B)}return Object.setPrototypeOf&&Object.setPrototypeOf(d,l),d}(t.read),t.readSync=typeof t.readSync!="function"?t.readSync:function(l){return function(d,m,v,g,x){for(var y=0;;)try{return l.call(t,d,m,v,g,x)}catch(O){if(O.code==="EAGAIN"&&y<10){y++;continue}throw O}}}(t.readSync);function e(l){l.lchmod=function(d,m,v){l.open(d,ri.O_WRONLY|ri.O_SYMLINK,m,function(g,x){if(g){v&&v(g);return}l.fchmod(x,m,function(y){l.close(x,function(O){v&&v(y||O)})})})},l.lchmodSync=function(d,m){var v=l.openSync(d,ri.O_WRONLY|ri.O_SYMLINK,m),g=!0,x;try{x=l.fchmodSync(v,m),g=!1}finally{if(g)try{l.closeSync(v)}catch{}else l.closeSync(v)}return x}}function i(l){ri.hasOwnProperty("O_SYMLINK")&&l.futimes?(l.lutimes=function(d,m,v,g){l.open(d,ri.O_SYMLINK,function(x,y){if(x){g&&g(x);return}l.futimes(y,m,v,function(O){l.close(y,function(B){g&&g(O||B)})})})},l.lutimesSync=function(d,m,v){var g=l.openSync(d,ri.O_SYMLINK),x,y=!0;try{x=l.futimesSync(g,m,v),y=!1}finally{if(y)try{l.closeSync(g)}catch{}else l.closeSync(g)}return x}):l.futimes&&(l.lutimes=function(d,m,v,g){g&&process.nextTick(g)},l.lutimesSync=function(){})}function r(l){return l&&function(d,m,v){return l.call(t,d,m,function(g){h(g)&&(g=null),v&&v.apply(this,arguments)})}}function n(l){return l&&function(d,m){try{return l.call(t,d,m)}catch(v){if(!h(v))throw v}}}function s(l){return l&&function(d,m,v,g){return l.call(t,d,m,v,function(x){h(x)&&(x=null),g&&g.apply(this,arguments)})}}function o(l){return l&&function(d,m,v){try{return l.call(t,d,m,v)}catch(g){if(!h(g))throw g}}}function c(l){return l&&function(d,m,v){typeof m=="function"&&(v=m,m=null);function g(x,y){y&&(y.uid<0&&(y.uid+=4294967296),y.gid<0&&(y.gid+=4294967296)),v&&v.apply(this,arguments)}return m?l.call(t,d,m,g):l.call(t,d,g)}}function u(l){return l&&function(d,m){var v=m?l.call(t,d,m):l.call(t,d);return v&&(v.uid<0&&(v.uid+=4294967296),v.gid<0&&(v.gid+=4294967296)),v}}function h(l){if(!l||l.code==="ENOSYS")return!0;var d=!process.getuid||process.getuid()!==0;return!!(d&&(l.code==="EINVAL"||l.code==="EPERM"))}}});var fh=E((Hy,uh)=>{var ch=require("stream").Stream;uh.exports=f_;function f_(t){return{ReadStream:e,WriteStream:i};function e(r,n){if(!(this instanceof e))return new e(r,n);ch.call(this);var s=this;this.path=r,this.fd=null,this.readable=!0,this.paused=!1,this.flags="r",this.mode=438,this.bufferSize=64*1024,n=n||{};for(var o=Object.keys(n),c=0,u=o.length;c<u;c++){var h=o[c];this[h]=n[h]}if(this.encoding&&this.setEncoding(this.encoding),this.start!==void 0){if(typeof this.start!="number")throw TypeError("start must be a Number");if(this.end===void 0)this.end=1/0;else if(typeof this.end!="number")throw TypeError("end must be a Number");if(this.start>this.end)throw new Error("start must be <= end");this.pos=this.start}if(this.fd!==null){process.nextTick(function(){s._read()});return}t.open(this.path,this.flags,this.mode,function(l,d){if(l){s.emit("error",l),s.readable=!1;return}s.fd=d,s.emit("open",d),s._read()})}function i(r,n){if(!(this instanceof i))return new i(r,n);ch.call(this),this.path=r,this.fd=null,this.writable=!0,this.flags="w",this.encoding="binary",this.mode=438,this.bytesWritten=0,n=n||{};for(var s=Object.keys(n),o=0,c=s.length;o<c;o++){var u=s[o];this[u]=n[u]}if(this.start!==void 0){if(typeof this.start!="number")throw TypeError("start must be a Number");if(this.start<0)throw new Error("start must be >= zero");this.pos=this.start}this.busy=!1,this._queue=[],this.fd===null&&(this._open=t.open,this._queue.push([this._open,this.path,this.flags,this.mode,void 0]),this.flush())}}});var ph=E(($y,hh)=>{"use strict";hh.exports=p_;var h_=Object.getPrototypeOf||function(t){return t.__proto__};function p_(t){if(t===null||typeof t!="object")return t;if(t instanceof Object)var e={__proto__:h_(t)};else var e=Object.create(null);return Object.getOwnPropertyNames(t).forEach(function(i){Object.defineProperty(e,i,Object.getOwnPropertyDescriptor(t,i))}),e}});var vh=E((Gy,Xo)=>{var Ne=require("fs"),d_=lh(),m_=fh(),g_=ph(),Bn=require("util"),Ke,Pn;typeof Symbol=="function"&&typeof Symbol.for=="function"?(Ke=Symbol.for("graceful-fs.queue"),Pn=Symbol.for("graceful-fs.previous")):(Ke="___graceful-fs.queue",Pn="___graceful-fs.previous");function v_(){}function gh(t,e){Object.defineProperty(t,Ke,{get:function(){return e}})}var Ci=v_;Bn.debuglog?Ci=Bn.debuglog("gfs4"):/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&(Ci=function(){var t=Bn.format.apply(Bn,arguments);t="GFS4: "+t.split(/\n/).join(`
GFS4: `),console.error(t)});Ne[Ke]||(dh=global[Ke]||[],gh(Ne,dh),Ne.close=function(t){function e(i,r){return t.call(Ne,i,function(n){n||mh(),typeof r=="function"&&r.apply(this,arguments)})}return Object.defineProperty(e,Pn,{value:t}),e}(Ne.close),Ne.closeSync=function(t){function e(i){t.apply(Ne,arguments),mh()}return Object.defineProperty(e,Pn,{value:t}),e}(Ne.closeSync),/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&process.on("exit",function(){Ci(Ne[Ke]),require("assert").equal(Ne[Ke].length,0)}));var dh;global[Ke]||gh(global,Ne[Ke]);Xo.exports=Ko(g_(Ne));process.env.TEST_GRACEFUL_FS_GLOBAL_PATCH&&!Ne.__patched&&(Xo.exports=Ko(Ne),Ne.__patched=!0);function Ko(t){d_(t),t.gracefulify=Ko,t.createReadStream=S,t.createWriteStream=J;var e=t.readFile;t.readFile=i;function i(k,L,D){return typeof L=="function"&&(D=L,L=null),X(k,L,D);function X(j,se,M,$){return e(j,se,function(Y){Y&&(Y.code==="EMFILE"||Y.code==="ENFILE")?nr([X,[j,se,M],Y,$||Date.now(),Date.now()]):typeof M=="function"&&M.apply(this,arguments)})}}var r=t.writeFile;t.writeFile=n;function n(k,L,D,X){return typeof D=="function"&&(X=D,D=null),j(k,L,D,X);function j(se,M,$,Y,Q){return r(se,M,$,function(V){V&&(V.code==="EMFILE"||V.code==="ENFILE")?nr([j,[se,M,$,Y],V,Q||Date.now(),Date.now()]):typeof Y=="function"&&Y.apply(this,arguments)})}}var s=t.appendFile;s&&(t.appendFile=o);function o(k,L,D,X){return typeof D=="function"&&(X=D,D=null),j(k,L,D,X);function j(se,M,$,Y,Q){return s(se,M,$,function(V){V&&(V.code==="EMFILE"||V.code==="ENFILE")?nr([j,[se,M,$,Y],V,Q||Date.now(),Date.now()]):typeof Y=="function"&&Y.apply(this,arguments)})}}var c=t.copyFile;c&&(t.copyFile=u);function u(k,L,D,X){return typeof D=="function"&&(X=D,D=0),j(k,L,D,X);function j(se,M,$,Y,Q){return c(se,M,$,function(V){V&&(V.code==="EMFILE"||V.code==="ENFILE")?nr([j,[se,M,$,Y],V,Q||Date.now(),Date.now()]):typeof Y=="function"&&Y.apply(this,arguments)})}}var h=t.readdir;t.readdir=d;var l=/^v[0-5]\./;function d(k,L,D){typeof L=="function"&&(D=L,L=null);var X=l.test(process.version)?function(M,$,Y,Q){return h(M,j(M,$,Y,Q))}:function(M,$,Y,Q){return h(M,$,j(M,$,Y,Q))};return X(k,L,D);function j(se,M,$,Y){return function(Q,V){Q&&(Q.code==="EMFILE"||Q.code==="ENFILE")?nr([X,[se,M,$],Q,Y||Date.now(),Date.now()]):(V&&V.sort&&V.sort(),typeof $=="function"&&$.call(this,Q,V))}}}if(process.version.substr(0,4)==="v0.8"){var m=m_(t);O=m.ReadStream,C=m.WriteStream}var v=t.ReadStream;v&&(O.prototype=Object.create(v.prototype),O.prototype.open=B);var g=t.WriteStream;g&&(C.prototype=Object.create(g.prototype),C.prototype.open=P),Object.defineProperty(t,"ReadStream",{get:function(){return O},set:function(k){O=k},enumerable:!0,configurable:!0}),Object.defineProperty(t,"WriteStream",{get:function(){return C},set:function(k){C=k},enumerable:!0,configurable:!0});var x=O;Object.defineProperty(t,"FileReadStream",{get:function(){return x},set:function(k){x=k},enumerable:!0,configurable:!0});var y=C;Object.defineProperty(t,"FileWriteStream",{get:function(){return y},set:function(k){y=k},enumerable:!0,configurable:!0});function O(k,L){return this instanceof O?(v.apply(this,arguments),this):O.apply(Object.create(O.prototype),arguments)}function B(){var k=this;z(k.path,k.flags,k.mode,function(L,D){L?(k.autoClose&&k.destroy(),k.emit("error",L)):(k.fd=D,k.emit("open",D),k.read())})}function C(k,L){return this instanceof C?(g.apply(this,arguments),this):C.apply(Object.create(C.prototype),arguments)}function P(){var k=this;z(k.path,k.flags,k.mode,function(L,D){L?(k.destroy(),k.emit("error",L)):(k.fd=D,k.emit("open",D))})}function S(k,L){return new t.ReadStream(k,L)}function J(k,L){return new t.WriteStream(k,L)}var A=t.open;t.open=z;function z(k,L,D,X){return typeof D=="function"&&(X=D,D=null),j(k,L,D,X);function j(se,M,$,Y,Q){return A(se,M,$,function(V,we){V&&(V.code==="EMFILE"||V.code==="ENFILE")?nr([j,[se,M,$,Y],V,Q||Date.now(),Date.now()]):typeof Y=="function"&&Y.apply(this,arguments)})}}return t}function nr(t){Ci("ENQUEUE",t[0].name,t[1]),Ne[Ke].push(t),Zo()}var Rn;function mh(){for(var t=Date.now(),e=0;e<Ne[Ke].length;++e)Ne[Ke][e].length>2&&(Ne[Ke][e][3]=t,Ne[Ke][e][4]=t);Zo()}function Zo(){if(clearTimeout(Rn),Rn=void 0,Ne[Ke].length!==0){var t=Ne[Ke].shift(),e=t[0],i=t[1],r=t[2],n=t[3],s=t[4];if(n===void 0)Ci("RETRY",e.name,i),e.apply(null,i);else if(Date.now()-n>=6e4){Ci("TIMEOUT",e.name,i);var o=i.pop();typeof o=="function"&&o.call(null,r)}else{var c=Date.now()-s,u=Math.max(s-n,1),h=Math.min(u*1.2,100);c>=h?(Ci("RETRY",e.name,i),e.apply(null,i.concat([n]))):Ne[Ke].push(t)}Rn===void 0&&(Rn=setTimeout(Zo,0))}}});var xh=E((zy,_h)=>{function bt(t,e){typeof e=="boolean"&&(e={forever:e}),this._originalTimeouts=JSON.parse(JSON.stringify(t)),this._timeouts=t,this._options=e||{},this._maxRetryTime=e&&e.maxRetryTime||1/0,this._fn=null,this._errors=[],this._attempts=1,this._operationTimeout=null,this._operationTimeoutCb=null,this._timeout=null,this._operationStart=null,this._options.forever&&(this._cachedTimeouts=this._timeouts.slice(0))}_h.exports=bt;bt.prototype.reset=function(){this._attempts=1,this._timeouts=this._originalTimeouts};bt.prototype.stop=function(){this._timeout&&clearTimeout(this._timeout),this._timeouts=[],this._cachedTimeouts=null};bt.prototype.retry=function(t){if(this._timeout&&clearTimeout(this._timeout),!t)return!1;var e=new Date().getTime();if(t&&e-this._operationStart>=this._maxRetryTime)return this._errors.unshift(new Error("RetryOperation timeout occurred")),!1;this._errors.push(t);var i=this._timeouts.shift();if(i===void 0)if(this._cachedTimeouts)this._errors.splice(this._errors.length-1,this._errors.length),this._timeouts=this._cachedTimeouts.slice(0),i=this._timeouts.shift();else return!1;var r=this,n=setTimeout(function(){r._attempts++,r._operationTimeoutCb&&(r._timeout=setTimeout(function(){r._operationTimeoutCb(r._attempts)},r._operationTimeout),r._options.unref&&r._timeout.unref()),r._fn(r._attempts)},i);return this._options.unref&&n.unref(),!0};bt.prototype.attempt=function(t,e){this._fn=t,e&&(e.timeout&&(this._operationTimeout=e.timeout),e.cb&&(this._operationTimeoutCb=e.cb));var i=this;this._operationTimeoutCb&&(this._timeout=setTimeout(function(){i._operationTimeoutCb()},i._operationTimeout)),this._operationStart=new Date().getTime(),this._fn(this._attempts)};bt.prototype.try=function(t){console.log("Using RetryOperation.try() is deprecated"),this.attempt(t)};bt.prototype.start=function(t){console.log("Using RetryOperation.start() is deprecated"),this.attempt(t)};bt.prototype.start=bt.prototype.try;bt.prototype.errors=function(){return this._errors};bt.prototype.attempts=function(){return this._attempts};bt.prototype.mainError=function(){if(this._errors.length===0)return null;for(var t={},e=null,i=0,r=0;r<this._errors.length;r++){var n=this._errors[r],s=n.message,o=(t[s]||0)+1;t[s]=o,o>=i&&(e=n,i=o)}return e}});var yh=E(Ti=>{var __=xh();Ti.operation=function(t){var e=Ti.timeouts(t);return new __(e,{forever:t&&t.forever,unref:t&&t.unref,maxRetryTime:t&&t.maxRetryTime})};Ti.timeouts=function(t){if(t instanceof Array)return[].concat(t);var e={retries:10,factor:2,minTimeout:1*1e3,maxTimeout:1/0,randomize:!1};for(var i in t)e[i]=t[i];if(e.minTimeout>e.maxTimeout)throw new Error("minTimeout is greater than maxTimeout");for(var r=[],n=0;n<e.retries;n++)r.push(this.createTimeout(n,e));return t&&t.forever&&!r.length&&r.push(this.createTimeout(n,e)),r.sort(function(s,o){return s-o}),r};Ti.createTimeout=function(t,e){var i=e.randomize?Math.random()+1:1,r=Math.round(i*e.minTimeout*Math.pow(e.factor,t));return r=Math.min(r,e.maxTimeout),r};Ti.wrap=function(t,e,i){if(e instanceof Array&&(i=e,e=null),!i){i=[];for(var r in t)typeof t[r]=="function"&&i.push(r)}for(var n=0;n<i.length;n++){var s=i[n],o=t[s];t[s]=function(u){var h=Ti.operation(e),l=Array.prototype.slice.call(arguments,1),d=l.pop();l.push(function(m){h.retry(m)||(m&&(arguments[0]=h.mainError()),d.apply(this,arguments))}),h.attempt(function(){u.apply(t,l)})}.bind(t,o),t[s].options=e}}});var wh=E((Yy,bh)=>{bh.exports=yh()});var Eh=E((Ky,Nn)=>{Nn.exports=["SIGABRT","SIGALRM","SIGHUP","SIGINT","SIGTERM"];process.platform!=="win32"&&Nn.exports.push("SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT");process.platform==="linux"&&Nn.exports.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT","SIGUNUSED")});var Th=E((Zy,ar)=>{var Pe=global.process,Ii=function(t){return t&&typeof t=="object"&&typeof t.removeListener=="function"&&typeof t.emit=="function"&&typeof t.reallyExit=="function"&&typeof t.listeners=="function"&&typeof t.kill=="function"&&typeof t.pid=="number"&&typeof t.on=="function"};Ii(Pe)?(Sh=require("assert"),sr=Eh(),kh=/^win/i.test(Pe.platform),Tr=require("events"),typeof Tr!="function"&&(Tr=Tr.EventEmitter),Pe.__signal_exit_emitter__?ze=Pe.__signal_exit_emitter__:(ze=Pe.__signal_exit_emitter__=new Tr,ze.count=0,ze.emitted={}),ze.infinite||(ze.setMaxListeners(1/0),ze.infinite=!0),ar.exports=function(t,e){if(!Ii(global.process))return function(){};Sh.equal(typeof t,"function","a callback must be provided for exit handler"),or===!1&&Jo();var i="exit";e&&e.alwaysLast&&(i="afterexit");var r=function(){ze.removeListener(i,t),ze.listeners("exit").length===0&&ze.listeners("afterexit").length===0&&Ln()};return ze.on(i,t),r},Ln=function(){!or||!Ii(global.process)||(or=!1,sr.forEach(function(e){try{Pe.removeListener(e,Fn[e])}catch{}}),Pe.emit=Mn,Pe.reallyExit=Qo,ze.count-=1)},ar.exports.unload=Ln,Ai=function(e,i,r){ze.emitted[e]||(ze.emitted[e]=!0,ze.emit(e,i,r))},Fn={},sr.forEach(function(t){Fn[t]=function(){if(Ii(global.process)){var i=Pe.listeners(t);i.length===ze.count&&(Ln(),Ai("exit",null,t),Ai("afterexit",null,t),kh&&t==="SIGHUP"&&(t="SIGINT"),Pe.kill(Pe.pid,t))}}}),ar.exports.signals=function(){return sr},or=!1,Jo=function(){or||!Ii(global.process)||(or=!0,ze.count+=1,sr=sr.filter(function(e){try{return Pe.on(e,Fn[e]),!0}catch{return!1}}),Pe.emit=Ch,Pe.reallyExit=Oh)},ar.exports.load=Jo,Qo=Pe.reallyExit,Oh=function(e){Ii(global.process)&&(Pe.exitCode=e||0,Ai("exit",Pe.exitCode,null),Ai("afterexit",Pe.exitCode,null),Qo.call(Pe,Pe.exitCode))},Mn=Pe.emit,Ch=function(e,i){if(e==="exit"&&Ii(global.process)){i!==void 0&&(Pe.exitCode=i);var r=Mn.apply(this,arguments);return Ai("exit",Pe.exitCode,null),Ai("afterexit",Pe.exitCode,null),r}else return Mn.apply(this,arguments)}):ar.exports=function(){return function(){}};var Sh,sr,kh,Tr,ze,Ln,Ai,Fn,or,Jo,Qo,Oh,Mn,Ch});var Fh=E((Xy,Lh)=>{"use strict";var x_=require("path"),Rh=vh(),y_=wh(),b_=Th(),ni={},Ih=Symbol();function w_(t,e,i){let r=e[Ih];if(r)return e.stat(t,(s,o)=>{if(s)return i(s);i(null,o.mtime,r)});let n=new Date(Math.ceil(Date.now()/1e3)*1e3+5);e.utimes(t,n,n,s=>{if(s)return i(s);e.stat(t,(o,c)=>{if(o)return i(o);let u=c.mtime.getTime()%1e3===0?"s":"ms";Object.defineProperty(e,Ih,{value:u}),i(null,c.mtime,u)})})}function E_(t){let e=Date.now();return t==="s"&&(e=Math.ceil(e/1e3)*1e3),new Date(e)}function Un(t,e){return e.lockfilePath||`${t}.lock`}function Ph(t,e,i){if(!e.realpath)return i(null,x_.resolve(t));e.fs.realpath(t,i)}function ta(t,e,i){let r=Un(t,e);e.fs.mkdir(r,n=>{if(!n)return w_(r,e.fs,(s,o,c)=>{if(s)return e.fs.rmdir(r,()=>{}),i(s);i(null,o,c)});if(n.code!=="EEXIST")return i(n);if(e.stale<=0)return i(Object.assign(new Error("Lock file is already being held"),{code:"ELOCKED",file:t}));e.fs.stat(r,(s,o)=>{if(s)return s.code==="ENOENT"?ta(t,{...e,stale:0},i):i(s);if(!S_(o,e))return i(Object.assign(new Error("Lock file is already being held"),{code:"ELOCKED",file:t}));Nh(t,e,c=>{if(c)return i(c);ta(t,{...e,stale:0},i)})})})}function S_(t,e){return t.mtime.getTime()<Date.now()-e.stale}function Nh(t,e,i){e.fs.rmdir(Un(t,e),r=>{if(r&&r.code!=="ENOENT")return i(r);i()})}function Dn(t,e){let i=ni[t];i.updateTimeout||(i.updateDelay=i.updateDelay||e.update,i.updateTimeout=setTimeout(()=>{i.updateTimeout=null,e.fs.stat(i.lockfilePath,(r,n)=>{let s=i.lastUpdate+e.stale<Date.now();if(r)return r.code==="ENOENT"||s?ea(t,i,Object.assign(r,{code:"ECOMPROMISED"})):(i.updateDelay=1e3,Dn(t,e));if(!(i.mtime.getTime()===n.mtime.getTime()))return ea(t,i,Object.assign(new Error("Unable to update lock within the stale threshold"),{code:"ECOMPROMISED"}));let c=E_(i.mtimePrecision);e.fs.utimes(i.lockfilePath,c,c,u=>{let h=i.lastUpdate+e.stale<Date.now();if(!i.released){if(u)return u.code==="ENOENT"||h?ea(t,i,Object.assign(u,{code:"ECOMPROMISED"})):(i.updateDelay=1e3,Dn(t,e));i.mtime=c,i.lastUpdate=Date.now(),i.updateDelay=null,Dn(t,e)}})})},i.updateDelay),i.updateTimeout.unref&&i.updateTimeout.unref())}function ea(t,e,i){e.released=!0,e.updateTimeout&&clearTimeout(e.updateTimeout),ni[t]===e&&delete ni[t],e.options.onCompromised(i)}function k_(t,e,i){e={stale:1e4,update:null,realpath:!0,retries:0,fs:Rh,onCompromised:r=>{throw r},...e},e.retries=e.retries||0,e.retries=typeof e.retries=="number"?{retries:e.retries}:e.retries,e.stale=Math.max(e.stale||0,2e3),e.update=e.update==null?e.stale/2:e.update||0,e.update=Math.max(Math.min(e.update,e.stale/2),1e3),Ph(t,e,(r,n)=>{if(r)return i(r);let s=y_.operation(e.retries);s.attempt(()=>{ta(n,e,(o,c,u)=>{if(s.retry(o))return;if(o)return i(s.mainError());let h=ni[n]={lockfilePath:Un(n,e),mtime:c,mtimePrecision:u,options:e,lastUpdate:Date.now()};Dn(n,e),i(null,l=>{if(h.released)return l&&l(Object.assign(new Error("Lock is already released"),{code:"ERELEASED"}));O_(n,{...e,realpath:!1},l)})})})})}function O_(t,e,i){e={fs:Rh,realpath:!0,...e},Ph(t,e,(r,n)=>{if(r)return i(r);let s=ni[n];if(!s)return i(Object.assign(new Error("Lock is not acquired/owned by you"),{code:"ENOTACQUIRED"}));s.updateTimeout&&clearTimeout(s.updateTimeout),s.released=!0,delete ni[n],Nh(n,e,i)})}function Ah(t){return(...e)=>new Promise((i,r)=>{e.push((n,s)=>{n?r(n):i(s)}),t(...e)})}var Bh=!1;function C_(){Bh||(Bh=!0,b_(()=>{for(let t in ni){let e=ni[t].options;try{e.fs.rmdirSync(Un(t,e))}catch{}}}))}Lh.exports.lock=async(t,e)=>{C_();let i=await Ah(k_)(t,e);return Ah(i)}});var H_={};vp(H_,{HttpsProxyAgent:()=>Wh.HttpsProxyAgent,PNG:()=>Yh.PNG,SocksProxyAgent:()=>Zh.SocksProxyAgent,StackUtils:()=>D_,colors:()=>T_,debug:()=>I_,dotenv:()=>A_,getProxyForUrl:()=>zh.getProxyForUrl,jpegjs:()=>B_,lockfile:()=>P_,mime:()=>N_,minimatch:()=>L_,open:()=>F_,program:()=>Kh.program,progress:()=>M_,ws:()=>U_,wsReceiver:()=>q_,wsSender:()=>V_,wsServer:()=>j_});module.exports=_p(H_);var Mh=De(Pa()),Dh=De(Ui()),Uh=De(Ya()),zh=De(Za()),Wh=De(sl()),jh=De(hl()),qh=De(yl()),Vh=De(ql()),Hh=De(tc()),Yh=De(Zc()),Kh=De(cu()),$h=De(du()),Zh=De(Zu()),Gh=De(rf());var a_=De(af(),1),Go=De(No(),1),zo=De(Fo(),1),sh=De(Ho(),1),Wo=De(nh(),1);var oh=sh.default;var T_=Mh.default,I_=Dh.default,A_=Uh.default,B_=jh.default,R_=Fh(),P_=R_,N_=qh.default,L_=Vh.default,F_=Hh.default,M_=$h.default,D_=Gh.default,U_=oh,j_=Wo.default,q_=Go.default,V_=zo.default;0&&(module.exports={HttpsProxyAgent,PNG,SocksProxyAgent,StackUtils,colors,debug,dotenv,getProxyForUrl,jpegjs,lockfile,mime,minimatch,open,program,progress,ws,wsReceiver,wsSender,wsServer});
/*! Bundled license information:

progress/lib/node-progress.js:
  (*!
   * node-progress
   * Copyright(c) 2011 TJ Holowaychuk <<EMAIL>>
   * MIT Licensed
   *)
*/
