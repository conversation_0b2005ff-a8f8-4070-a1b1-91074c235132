@echo off
REM ============================================================================
REM NZ Supermarket Scrapers - Multi-Window Launcher
REM ============================================================================
REM This script launches all 3 scrapers in separate command windows
REM Each scraper runs independently and shows real-time progress
REM ============================================================================

echo.
echo ========================================
echo  NZ SUPERMARKET SCRAPERS LAUNCHER
echo ========================================
echo.
echo Starting all 3 scrapers in separate windows...
echo.

REM Check if MongoDB is running
echo [1/4] Checking MongoDB status...
docker ps | findstr mongodb >nul
if %errorlevel% neq 0 (
    echo WARNING: MongoDB container not found. Starting MongoDB...
    docker run -d -p 27017:27017 --name mongodb mongo:latest
    timeout /t 5 /nobreak >nul
    echo MongoDB started successfully.
) else (
    echo MongoDB is already running.
)
echo.

REM Launch Woolworths scraper in new window
echo [2/4] Launching Woolworths scraper...
start "Woolworths Scraper" cmd /k "cd /d %~dp0Woolworths && echo === WOOLWORTHS SCRAPER === && echo Starting scraper in database mode... && npm run db"

REM Wait a moment before launching next scraper
timeout /t 3 /nobreak >nul

REM Launch Pak'n Save scraper in new window
echo [3/4] Launching Pak'n Save scraper...
start "Pak'n Save Scraper" cmd /k "cd /d %~dp0paknsave\src && echo === PAK'N SAVE SCRAPER === && echo Starting scraper... && dotnet run"

REM Wait a moment before launching next scraper
timeout /t 3 /nobreak >nul

REM Launch New World scraper in new window
echo [4/4] Launching New World scraper...
start "New World Scraper" cmd /k "cd /d %~dp0new-world\src && echo === NEW WORLD SCRAPER === && echo Starting scraper... && dotnet run"

echo.
echo ========================================
echo  ALL SCRAPERS LAUNCHED SUCCESSFULLY!
echo ========================================
echo.
echo Three command windows should now be open:
echo  1. Woolworths Scraper (Node.js/TypeScript)
echo  2. Pak'n Save Scraper (.NET/C#)
echo  3. New World Scraper (.NET/C#)
echo.
echo Each window shows real-time scraping progress.
echo Close individual windows to stop specific scrapers.
echo.
echo MongoDB is running on: localhost:27017
echo Database: nz-supermarket-scraper
echo.
echo Press any key to exit this launcher window...
pause >nul
