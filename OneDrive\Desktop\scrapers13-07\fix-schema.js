#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();
require('dotenv').config({ path: `.env.local`, override: true });

function showMigrationSql(filename) {
  console.log('📝 Please run this SQL in your Supabase SQL editor:');
  console.log('─'.repeat(60));
  const migrationPath = path.join(__dirname, 'supabase/migrations', filename);
  const migrationSql = fs.readFileSync(migrationPath, 'utf8');
  console.log(migrationSql);
  console.log('─'.repeat(60));
  console.log('🔗 Supabase SQL Editor: https://supabase.com/dashboard/project/[your-project]/sql');
  console.log('');
  console.log('💡 After running the SQL, your scrapers should work correctly.');
}

async function checkAndFixSchema() {
  console.log('🔍 Checking database schema...');
  
  const url = process.env.SUPABASE_URL;
  const key = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;
  
  if (!url || !key) {
    console.error('❌ SUPABASE_URL or SUPABASE_ANON_KEY not set in environment');
    process.exit(1);
  }

  const supabase = createClient(url, key, { auth: { persistSession: false } });

  try {
    // Try to execute a simple query to test the products table structure
    const { data: testData, error: testError } = await supabase
      .from('products')
      .select('original_unit_qty')
      .limit(1);

    if (!testError) {
      console.log('✅ Column original_unit_qty already exists');
      return;
    }

    // Check for specific schema issues
    if (testError.message.includes('column "original_unit_qty" does not exist')) {
      console.log('⚠️  Column original_unit_qty is missing');
      showMigrationSql('20250122_001_add_missing_column.sql');
      return;
    }
    
    if (testError.message.includes('null value in column') && testError.message.includes('violates not-null constraint')) {
      console.log('⚠️  Database has extra columns with NOT NULL constraints');
      console.log('   This usually happens when the database schema doesn\'t match our migration files');
      showMigrationSql('20250122_002_fix_products_schema.sql');
      return;
    }

    // Some other error
    console.error('❌ Unexpected database error:', testError.message);
    console.log('📝 You may need to run one of these SQL migrations in your Supabase SQL editor:');
    showMigrationSql('20250122_002_fix_products_schema.sql');

  } catch (err) {
    console.error('❌ Unexpected error:', err.message);
    console.log('📝 Please run this SQL manually in your Supabase SQL editor:');
    console.log('─'.repeat(60));
    const migrationPath = path.join(__dirname, 'supabase/migrations/20250122_001_add_missing_column.sql');
    const migrationSql = fs.readFileSync(migrationPath, 'utf8');
    console.log(migrationSql);
    console.log('─'.repeat(60));
  }
}

if (require.main === module) {
  checkAndFixSchema();
}

module.exports = { checkAndFixSchema };