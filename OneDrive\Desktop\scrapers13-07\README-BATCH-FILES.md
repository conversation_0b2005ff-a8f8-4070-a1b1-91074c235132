# New Zealand Supermarket Scrapers - Batch File Usage

## Overview

This directory contains batch files and PowerShell scripts to run all three New Zealand supermarket scrapers simultaneously:
- **Woolworths** (TypeScript/Node.js)
- **New World** (.NET/C#)
- **PakNSave** (.NET/C#)

## Available Scripts

### 1. `run-all-scrapers.bat` (Windows Batch File)

**Usage:**
```cmd
run-all-scrapers.bat [mode]
```

**Modes:**
- `dev` - Dry run mode (console output only, no database writes)
- `db` - Save to database (without image upload)
- `db-images` - Save to database with image upload to GridFS

**Examples:**
```cmd
# Dry run mode (testing)
run-all-scrapers.bat dev

# Save to database without images
run-all-scrapers.bat db

# Save to database with images
run-all-scrapers.bat db-images
```

### 2. `run-all-scrapers.ps1` (PowerShell Script)

**Usage:**
```powershell
.\run-all-scrapers.ps1 -Mode [dev|db|db-images]
```

**Examples:**
```powershell
# Dry run mode
.\run-all-scrapers.ps1 -Mode dev

# Save to database without images
.\run-all-scrapers.ps1 -Mode db

# Save to database with images
.\run-all-scrapers.ps1 -Mode db-images
```

## Prerequisites

### Required Software
1. **Node.js** (v16+) - For Woolworths scraper
2. **.NET 6 SDK** - For New World and PakNSave scrapers
3. **Docker** - For MongoDB container
4. **MongoDB** - Database for consolidated products

### MongoDB Setup
The scripts automatically check for and start a MongoDB Docker container:
```bash
docker run -d -p 27017:27017 --name mongodb mongo:latest
```

## Log Files

All scrapers output their logs to the `logs/` directory:
- `logs/woolworths.log` - Woolworths scraper output
- `logs/newworld.log` - New World scraper output
- `logs/paknsave.log` - PakNSave scraper output

## Troubleshooting

### Common Issues

#### 1. "Path not found" errors
- Ensure you're running the batch file from the `scrapers13-07` directory
- Check that all subdirectories exist: `Woolworths/`, `new-world/`, `paknsave/`

#### 2. MongoDB connection errors
- Verify Docker is running: `docker ps`
- Check MongoDB container status: `docker ps | findstr mongodb`
- Restart MongoDB if needed: `docker restart mongodb`

#### 3. Node.js/npm errors (Woolworths)
- Verify Node.js installation: `node --version`
- Install dependencies: `cd Woolworths && npm install`
- Check package.json scripts are correct

#### 4. .NET compilation errors (New World/PakNSave)
- Verify .NET SDK installation: `dotnet --version`
- Restore packages: `dotnet restore`
- Build projects: `dotnet build`

### Script Fixes Applied

#### Fixed Issues:
1. **Woolworths package.json**: Changed `"db images"` to `"db-images"` for proper npm script naming
2. **Batch file paths**: Changed from hardcoded paths to relative paths using `%~dp0`
3. **Log file paths**: Corrected relative paths for proper log file creation

## Enhanced Features

### Cross-Store Product Matching
All scrapers now include:
- **152 NZ brand mappings** with 500+ variations
- **Advanced fuzzy matching** (Levenshtein + Jaccard similarity)
- **Automatic product consolidation** across stores
- **Dynamic alias management** for improved matching over time

### Database Structure
- **Consolidated Products**: Master product catalog with normalized data
- **Price History**: Time-series pricing data across all stores
- **GridFS Images**: Product images stored in MongoDB
- **Enhanced Indexing**: Optimized for fast cross-store queries

## Performance Monitoring

### Expected Scraping Times
- **Woolworths**: ~260 pages, ~30 minutes (with delays)
- **New World**: ~86 pages, ~15 minutes (with delays)
- **PakNSave**: ~30 pages, ~6 minutes (with delays)

### Resource Usage
- **Memory**: ~500MB per scraper (total ~1.5GB)
- **Network**: Respectful delays between requests (7-11 seconds)
- **Storage**: ~50MB database size per full scrape cycle

## Advanced Usage

### Running Individual Scrapers

**Woolworths:**
```cmd
cd Woolworths
npm run dev          # Dry run
npm run db           # Database mode
npm run db-images    # Database + images
```

**New World:**
```cmd
cd new-world\src
dotnet run           # Dry run
dotnet run db        # Database mode
dotnet run db images # Database + images
```

**PakNSave:**
```cmd
cd paknsave\src
dotnet run           # Dry run
dotnet run db        # Database mode
dotnet run db images # Database + images
```

### Environment Configuration

Each scraper can be configured via `.env` files:
- `Woolworths/.env` - Store location, MongoDB connection, etc.
- `new-world/.env` - Database settings, scraping parameters
- `paknsave/.env` - Database settings, scraping parameters

## Support

For issues or questions:
1. Check log files in `logs/` directory
2. Verify all prerequisites are installed
3. Ensure MongoDB is running and accessible
4. Check individual scraper documentation in their respective directories

---

**Last Updated**: January 2024  
**Version**: 2.0  
**Compatibility**: Windows 10+, PowerShell 5.1+, .NET 6+, Node.js 16+
