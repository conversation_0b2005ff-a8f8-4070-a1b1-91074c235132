Write-Host "New Zealand Supermarket Scrapers - Status Check" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Gray
Write-Host ""

# Check MongoDB
Write-Host "MongoDB Status:" -ForegroundColor Cyan
$mongo = docker ps | Select-String "mongodb"
if ($mongo) {
    Write-Host "  MongoDB: Running" -ForegroundColor Green
} else {
    Write-Host "  MongoDB: Not running" -ForegroundColor Red
}
Write-Host ""

# Check Woolworths
Write-Host "Woolworths Scraper:" -ForegroundColor Green
if (Test-Path "logs\woolworths.log") {
    $woolLog = Get-Content "logs\woolworths.log" -Tail 5
    $mongoConn = $woolLog | Select-String "MongoDB connection established"
    if ($mongoConn) {
        Write-Host "  Status: Connected to MongoDB" -ForegroundColor Green
    } else {
        Write-Host "  Status: Check log file" -ForegroundColor Yellow
    }
    Write-Host "  Log: logs\woolworths.log" -ForegroundColor Gray
} else {
    Write-Host "  Status: No log file found" -ForegroundColor Red
}
Write-Host ""

# Check New World
Write-Host "New World Scraper:" -ForegroundColor Blue
if (Test-Path "logs\newworld.log") {
    $nwLog = Get-Content "logs\newworld.log" -Tail 5
    $mongoConn = $nwLog | Select-String "MongoDB connection established"
    if ($mongoConn) {
        Write-Host "  Status: Connected to MongoDB" -ForegroundColor Green
    } else {
        Write-Host "  Status: Check log file" -ForegroundColor Yellow
    }
    Write-Host "  Log: logs\newworld.log" -ForegroundColor Gray
} else {
    Write-Host "  Status: No log file found" -ForegroundColor Red
}
Write-Host ""

# Check PakNSave
Write-Host "PakNSave Scraper:" -ForegroundColor Magenta
if (Test-Path "logs\paknsave.log") {
    $pnLog = Get-Content "logs\paknsave.log" -Tail 5
    $mongoConn = $pnLog | Select-String "MongoDB connection established"
    if ($mongoConn) {
        Write-Host "  Status: Connected to MongoDB" -ForegroundColor Green
    } else {
        Write-Host "  Status: Check log file" -ForegroundColor Yellow
    }
    Write-Host "  Log: logs\paknsave.log" -ForegroundColor Gray
} else {
    Write-Host "  Status: No log file found" -ForegroundColor Red
}
Write-Host ""

Write-Host "Commands:" -ForegroundColor Cyan
Write-Host "  Start MongoDB:    docker start mongodb" -ForegroundColor White
Write-Host "  Run all scrapers: run-all-scrapers.bat db-images" -ForegroundColor White
Write-Host "  View logs:        Get-Content logs\woolworths.log -Wait" -ForegroundColor White
