(function(){const n=document.createElement("link").relList;if(n&&n.supports&&n.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))u(a);new MutationObserver(a=>{for(const c of a)if(c.type==="childList")for(const p of c.addedNodes)p.tagName==="LINK"&&p.rel==="modulepreload"&&u(p)}).observe(document,{childList:!0,subtree:!0});function i(a){const c={};return a.integrity&&(c.integrity=a.integrity),a.referrerpolicy&&(c.referrerPolicy=a.referrerpolicy),a.crossorigin==="use-credentials"?c.credentials="include":a.crossorigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function u(a){if(a.ep)return;a.ep=!0;const c=i(a);fetch(a.href,c)}})();var y1=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},Cu={},w1={get exports(){return Cu},set exports(t){Cu=t}},qu={},tt={},x1={get exports(){return tt},set exports(t){tt=t}},_e={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ts=Symbol.for("react.element"),S1=Symbol.for("react.portal"),k1=Symbol.for("react.fragment"),C1=Symbol.for("react.strict_mode"),T1=Symbol.for("react.profiler"),L1=Symbol.for("react.provider"),E1=Symbol.for("react.context"),N1=Symbol.for("react.forward_ref"),M1=Symbol.for("react.suspense"),b1=Symbol.for("react.memo"),_1=Symbol.for("react.lazy"),Lp=Symbol.iterator;function P1(t){return t===null||typeof t!="object"?null:(t=Lp&&t[Lp]||t["@@iterator"],typeof t=="function"?t:null)}var Uv={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},jv=Object.assign,$v={};function Pl(t,n,i){this.props=t,this.context=n,this.refs=$v,this.updater=i||Uv}Pl.prototype.isReactComponent={};Pl.prototype.setState=function(t,n){if(typeof t!="object"&&typeof t!="function"&&t!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,t,n,"setState")};Pl.prototype.forceUpdate=function(t){this.updater.enqueueForceUpdate(this,t,"forceUpdate")};function Kv(){}Kv.prototype=Pl.prototype;function Oc(t,n,i){this.props=t,this.context=n,this.refs=$v,this.updater=i||Uv}var Dc=Oc.prototype=new Kv;Dc.constructor=Oc;jv(Dc,Pl.prototype);Dc.isPureReactComponent=!0;var Ep=Array.isArray,Gv=Object.prototype.hasOwnProperty,zc={current:null},Qv={key:!0,ref:!0,__self:!0,__source:!0};function Vv(t,n,i){var u,a={},c=null,p=null;if(n!=null)for(u in n.ref!==void 0&&(p=n.ref),n.key!==void 0&&(c=""+n.key),n)Gv.call(n,u)&&!Qv.hasOwnProperty(u)&&(a[u]=n[u]);var g=arguments.length-2;if(g===1)a.children=i;else if(1<g){for(var y=Array(g),S=0;S<g;S++)y[S]=arguments[S+2];a.children=y}if(t&&t.defaultProps)for(u in g=t.defaultProps,g)a[u]===void 0&&(a[u]=g[u]);return{$$typeof:ts,type:t,key:c,ref:p,props:a,_owner:zc.current}}function O1(t,n){return{$$typeof:ts,type:t.type,key:n,ref:t.ref,props:t.props,_owner:t._owner}}function Ic(t){return typeof t=="object"&&t!==null&&t.$$typeof===ts}function D1(t){var n={"=":"=0",":":"=2"};return"$"+t.replace(/[=:]/g,function(i){return n[i]})}var Np=/\/+/g;function af(t,n){return typeof t=="object"&&t!==null&&t.key!=null?D1(""+t.key):n.toString(36)}function su(t,n,i,u,a){var c=typeof t;(c==="undefined"||c==="boolean")&&(t=null);var p=!1;if(t===null)p=!0;else switch(c){case"string":case"number":p=!0;break;case"object":switch(t.$$typeof){case ts:case S1:p=!0}}if(p)return p=t,a=a(p),t=u===""?"."+af(p,0):u,Ep(a)?(i="",t!=null&&(i=t.replace(Np,"$&/")+"/"),su(a,n,i,"",function(S){return S})):a!=null&&(Ic(a)&&(a=O1(a,i+(!a.key||p&&p.key===a.key?"":(""+a.key).replace(Np,"$&/")+"/")+t)),n.push(a)),1;if(p=0,u=u===""?".":u+":",Ep(t))for(var g=0;g<t.length;g++){c=t[g];var y=u+af(c,g);p+=su(c,n,i,y,a)}else if(y=P1(t),typeof y=="function")for(t=y.call(t),g=0;!(c=t.next()).done;)c=c.value,y=u+af(c,g++),p+=su(c,n,i,y,a);else if(c==="object")throw n=String(t),Error("Objects are not valid as a React child (found: "+(n==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":n)+"). If you meant to render a collection of children, use an array instead.");return p}function js(t,n,i){if(t==null)return t;var u=[],a=0;return su(t,u,"","",function(c){return n.call(i,c,a++)}),u}function z1(t){if(t._status===-1){var n=t._result;n=n(),n.then(function(i){(t._status===0||t._status===-1)&&(t._status=1,t._result=i)},function(i){(t._status===0||t._status===-1)&&(t._status=2,t._result=i)}),t._status===-1&&(t._status=0,t._result=n)}if(t._status===1)return t._result.default;throw t._result}var Xt={current:null},uu={transition:null},I1={ReactCurrentDispatcher:Xt,ReactCurrentBatchConfig:uu,ReactCurrentOwner:zc};_e.Children={map:js,forEach:function(t,n,i){js(t,function(){n.apply(this,arguments)},i)},count:function(t){var n=0;return js(t,function(){n++}),n},toArray:function(t){return js(t,function(n){return n})||[]},only:function(t){if(!Ic(t))throw Error("React.Children.only expected to receive a single React element child.");return t}};_e.Component=Pl;_e.Fragment=k1;_e.Profiler=T1;_e.PureComponent=Oc;_e.StrictMode=C1;_e.Suspense=M1;_e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=I1;_e.cloneElement=function(t,n,i){if(t==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+t+".");var u=jv({},t.props),a=t.key,c=t.ref,p=t._owner;if(n!=null){if(n.ref!==void 0&&(c=n.ref,p=zc.current),n.key!==void 0&&(a=""+n.key),t.type&&t.type.defaultProps)var g=t.type.defaultProps;for(y in n)Gv.call(n,y)&&!Qv.hasOwnProperty(y)&&(u[y]=n[y]===void 0&&g!==void 0?g[y]:n[y])}var y=arguments.length-2;if(y===1)u.children=i;else if(1<y){g=Array(y);for(var S=0;S<y;S++)g[S]=arguments[S+2];u.children=g}return{$$typeof:ts,type:t.type,key:a,ref:c,props:u,_owner:p}};_e.createContext=function(t){return t={$$typeof:E1,_currentValue:t,_currentValue2:t,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},t.Provider={$$typeof:L1,_context:t},t.Consumer=t};_e.createElement=Vv;_e.createFactory=function(t){var n=Vv.bind(null,t);return n.type=t,n};_e.createRef=function(){return{current:null}};_e.forwardRef=function(t){return{$$typeof:N1,render:t}};_e.isValidElement=Ic;_e.lazy=function(t){return{$$typeof:_1,_payload:{_status:-1,_result:t},_init:z1}};_e.memo=function(t,n){return{$$typeof:b1,type:t,compare:n===void 0?null:n}};_e.startTransition=function(t){var n=uu.transition;uu.transition={};try{t()}finally{uu.transition=n}};_e.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")};_e.useCallback=function(t,n){return Xt.current.useCallback(t,n)};_e.useContext=function(t){return Xt.current.useContext(t)};_e.useDebugValue=function(){};_e.useDeferredValue=function(t){return Xt.current.useDeferredValue(t)};_e.useEffect=function(t,n){return Xt.current.useEffect(t,n)};_e.useId=function(){return Xt.current.useId()};_e.useImperativeHandle=function(t,n,i){return Xt.current.useImperativeHandle(t,n,i)};_e.useInsertionEffect=function(t,n){return Xt.current.useInsertionEffect(t,n)};_e.useLayoutEffect=function(t,n){return Xt.current.useLayoutEffect(t,n)};_e.useMemo=function(t,n){return Xt.current.useMemo(t,n)};_e.useReducer=function(t,n,i){return Xt.current.useReducer(t,n,i)};_e.useRef=function(t){return Xt.current.useRef(t)};_e.useState=function(t){return Xt.current.useState(t)};_e.useSyncExternalStore=function(t,n,i){return Xt.current.useSyncExternalStore(t,n,i)};_e.useTransition=function(){return Xt.current.useTransition()};_e.version="18.1.0";(function(t){t.exports=_e})(x1);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var A1=tt,F1=Symbol.for("react.element"),R1=Symbol.for("react.fragment"),W1=Object.prototype.hasOwnProperty,H1=A1.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,B1={key:!0,ref:!0,__self:!0,__source:!0};function Yv(t,n,i){var u,a={},c=null,p=null;i!==void 0&&(c=""+i),n.key!==void 0&&(c=""+n.key),n.ref!==void 0&&(p=n.ref);for(u in n)W1.call(n,u)&&!B1.hasOwnProperty(u)&&(a[u]=n[u]);if(t&&t.defaultProps)for(u in n=t.defaultProps,n)a[u]===void 0&&(a[u]=n[u]);return{$$typeof:F1,type:t,key:c,ref:p,props:a,_owner:H1.current}}qu.Fragment=R1;qu.jsx=Yv;qu.jsxs=Yv;(function(t){t.exports=qu})(w1);const Pe=Cu.jsx,jn=Cu.jsxs;function U1(){if(document.playwrightThemeInitialized)return;document.playwrightThemeInitialized=!0,document.defaultView.addEventListener("focus",i=>{i.target.document.nodeType===Node.DOCUMENT_NODE&&document.body.classList.remove("inactive")},!1),document.defaultView.addEventListener("blur",i=>{document.body.classList.add("inactive")},!1);const t=localStorage.getItem("theme"),n=window.matchMedia("(prefers-color-scheme: dark)");(t==="dark-mode"||n.matches)&&document.body.classList.add("dark-mode")}function j1(){const t=localStorage.getItem("theme");let n;t==="dark-mode"?n="light-mode":n="dark-mode",t&&document.body.classList.remove(t),document.body.classList.add(n),localStorage.setItem("theme",n)}var Ff={},$1={get exports(){return Ff},set exports(t){Ff=t}},yn={},Rf={},K1={get exports(){return Rf},set exports(t){Rf=t}},qv={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(t){function n(k,E){var F=k.length;k.push(E);e:for(;0<F;){var J=F-1>>>1,ue=k[J];if(0<a(ue,E))k[J]=E,k[F]=ue,F=J;else break e}}function i(k){return k.length===0?null:k[0]}function u(k){if(k.length===0)return null;var E=k[0],F=k.pop();if(F!==E){k[0]=F;e:for(var J=0,ue=k.length,ye=ue>>>1;J<ye;){var Be=2*(J+1)-1,Ve=k[Be],he=Be+1,fe=k[he];if(0>a(Ve,F))he<ue&&0>a(fe,Ve)?(k[J]=fe,k[he]=F,J=he):(k[J]=Ve,k[Be]=F,J=Be);else if(he<ue&&0>a(fe,F))k[J]=fe,k[he]=F,J=he;else break e}}return E}function a(k,E){var F=k.sortIndex-E.sortIndex;return F!==0?F:k.id-E.id}if(typeof performance=="object"&&typeof performance.now=="function"){var c=performance;t.unstable_now=function(){return c.now()}}else{var p=Date,g=p.now();t.unstable_now=function(){return p.now()-g}}var y=[],S=[],N=1,W=null,O=3,U=!1,B=!1,oe=!1,G=typeof setTimeout=="function"?setTimeout:null,_=typeof clearTimeout=="function"?clearTimeout:null,C=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function D(k){for(var E=i(S);E!==null;){if(E.callback===null)u(S);else if(E.startTime<=k)u(S),E.sortIndex=E.expirationTime,n(y,E);else break;E=i(S)}}function $(k){if(oe=!1,D(k),!B)if(i(y)!==null)B=!0,Q(Y);else{var E=i(S);E!==null&&X($,E.startTime-k)}}function Y(k,E){B=!1,oe&&(oe=!1,_(Z),Z=-1),U=!0;var F=O;try{for(D(E),W=i(y);W!==null&&(!(W.expirationTime>E)||k&&!Re());){var J=W.callback;if(typeof J=="function"){W.callback=null,O=W.priorityLevel;var ue=J(W.expirationTime<=E);E=t.unstable_now(),typeof ue=="function"?W.callback=ue:W===i(y)&&u(y),D(E)}else u(y);W=i(y)}if(W!==null)var ye=!0;else{var Be=i(S);Be!==null&&X($,Be.startTime-E),ye=!1}return ye}finally{W=null,O=F,U=!1}}var ie=!1,I=null,Z=-1,ke=5,me=-1;function Re(){return!(t.unstable_now()-me<ke)}function K(){if(I!==null){var k=t.unstable_now();me=k;var E=!0;try{E=I(!0,k)}finally{E?A():(ie=!1,I=null)}}else ie=!1}var A;if(typeof C=="function")A=function(){C(K)};else if(typeof MessageChannel<"u"){var ne=new MessageChannel,M=ne.port2;ne.port1.onmessage=K,A=function(){M.postMessage(null)}}else A=function(){G(K,0)};function Q(k){I=k,ie||(ie=!0,A())}function X(k,E){Z=G(function(){k(t.unstable_now())},E)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(k){k.callback=null},t.unstable_continueExecution=function(){B||U||(B=!0,Q(Y))},t.unstable_forceFrameRate=function(k){0>k||125<k?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):ke=0<k?Math.floor(1e3/k):5},t.unstable_getCurrentPriorityLevel=function(){return O},t.unstable_getFirstCallbackNode=function(){return i(y)},t.unstable_next=function(k){switch(O){case 1:case 2:case 3:var E=3;break;default:E=O}var F=O;O=E;try{return k()}finally{O=F}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(k,E){switch(k){case 1:case 2:case 3:case 4:case 5:break;default:k=3}var F=O;O=k;try{return E()}finally{O=F}},t.unstable_scheduleCallback=function(k,E,F){var J=t.unstable_now();switch(typeof F=="object"&&F!==null?(F=F.delay,F=typeof F=="number"&&0<F?J+F:J):F=J,k){case 1:var ue=-1;break;case 2:ue=250;break;case 5:ue=**********;break;case 4:ue=1e4;break;default:ue=5e3}return ue=F+ue,k={id:N++,callback:E,priorityLevel:k,startTime:F,expirationTime:ue,sortIndex:-1},F>J?(k.sortIndex=F,n(S,k),i(y)===null&&k===i(S)&&(oe?(_(Z),Z=-1):oe=!0,X($,F-J))):(k.sortIndex=ue,n(y,k),B||U||(B=!0,Q(Y))),k},t.unstable_shouldYield=Re,t.unstable_wrapCallback=function(k){var E=O;return function(){var F=O;O=E;try{return k.apply(this,arguments)}finally{O=F}}}})(qv);(function(t){t.exports=qv})(K1);/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Xv=tt,mn=Rf;function q(t){for(var n="https://reactjs.org/docs/error-decoder.html?invariant="+t,i=1;i<arguments.length;i++)n+="&args[]="+encodeURIComponent(arguments[i]);return"Minified React error #"+t+"; visit "+n+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Zv=new Set,Ro={};function zi(t,n){Tl(t,n),Tl(t+"Capture",n)}function Tl(t,n){for(Ro[t]=n,t=0;t<n.length;t++)Zv.add(n[t])}var Mr=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Wf=Object.prototype.hasOwnProperty,G1=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Mp={},bp={};function Q1(t){return Wf.call(bp,t)?!0:Wf.call(Mp,t)?!1:G1.test(t)?bp[t]=!0:(Mp[t]=!0,!1)}function V1(t,n,i,u){if(i!==null&&i.type===0)return!1;switch(typeof n){case"function":case"symbol":return!0;case"boolean":return u?!1:i!==null?!i.acceptsBooleans:(t=t.toLowerCase().slice(0,5),t!=="data-"&&t!=="aria-");default:return!1}}function Y1(t,n,i,u){if(n===null||typeof n>"u"||V1(t,n,i,u))return!0;if(u)return!1;if(i!==null)switch(i.type){case 3:return!n;case 4:return n===!1;case 5:return isNaN(n);case 6:return isNaN(n)||1>n}return!1}function Zt(t,n,i,u,a,c,p){this.acceptsBooleans=n===2||n===3||n===4,this.attributeName=u,this.attributeNamespace=a,this.mustUseProperty=i,this.propertyName=t,this.type=n,this.sanitizeURL=c,this.removeEmptyString=p}var Ot={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(t){Ot[t]=new Zt(t,0,!1,t,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(t){var n=t[0];Ot[n]=new Zt(n,1,!1,t[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(t){Ot[t]=new Zt(t,2,!1,t.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(t){Ot[t]=new Zt(t,2,!1,t,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(t){Ot[t]=new Zt(t,3,!1,t.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(t){Ot[t]=new Zt(t,3,!0,t,null,!1,!1)});["capture","download"].forEach(function(t){Ot[t]=new Zt(t,4,!1,t,null,!1,!1)});["cols","rows","size","span"].forEach(function(t){Ot[t]=new Zt(t,6,!1,t,null,!1,!1)});["rowSpan","start"].forEach(function(t){Ot[t]=new Zt(t,5,!1,t.toLowerCase(),null,!1,!1)});var Ac=/[\-:]([a-z])/g;function Fc(t){return t[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(t){var n=t.replace(Ac,Fc);Ot[n]=new Zt(n,1,!1,t,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(t){var n=t.replace(Ac,Fc);Ot[n]=new Zt(n,1,!1,t,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(t){var n=t.replace(Ac,Fc);Ot[n]=new Zt(n,1,!1,t,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(t){Ot[t]=new Zt(t,1,!1,t.toLowerCase(),null,!1,!1)});Ot.xlinkHref=new Zt("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(t){Ot[t]=new Zt(t,1,!1,t.toLowerCase(),null,!0,!0)});function Rc(t,n,i,u){var a=Ot.hasOwnProperty(n)?Ot[n]:null;(a!==null?a.type!==0:u||!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(Y1(n,i,a,u)&&(i=null),u||a===null?Q1(n)&&(i===null?t.removeAttribute(n):t.setAttribute(n,""+i)):a.mustUseProperty?t[a.propertyName]=i===null?a.type===3?!1:"":i:(n=a.attributeName,u=a.attributeNamespace,i===null?t.removeAttribute(n):(a=a.type,i=a===3||a===4&&i===!0?"":""+i,u?t.setAttributeNS(u,n,i):t.setAttribute(n,i))))}var Pr=Xv.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,$s=Symbol.for("react.element"),ol=Symbol.for("react.portal"),sl=Symbol.for("react.fragment"),Wc=Symbol.for("react.strict_mode"),Hf=Symbol.for("react.profiler"),Jv=Symbol.for("react.provider"),eg=Symbol.for("react.context"),Hc=Symbol.for("react.forward_ref"),Bf=Symbol.for("react.suspense"),Uf=Symbol.for("react.suspense_list"),Bc=Symbol.for("react.memo"),Qr=Symbol.for("react.lazy"),tg=Symbol.for("react.offscreen"),_p=Symbol.iterator;function go(t){return t===null||typeof t!="object"?null:(t=_p&&t[_p]||t["@@iterator"],typeof t=="function"?t:null)}var rt=Object.assign,ff;function Lo(t){if(ff===void 0)try{throw Error()}catch(i){var n=i.stack.trim().match(/\n( *(at )?)/);ff=n&&n[1]||""}return`
`+ff+t}var cf=!1;function df(t,n){if(!t||cf)return"";cf=!0;var i=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(n)if(n=function(){throw Error()},Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(n,[])}catch(S){var u=S}Reflect.construct(t,[],n)}else{try{n.call()}catch(S){u=S}t.call(n.prototype)}else{try{throw Error()}catch(S){u=S}t()}}catch(S){if(S&&u&&typeof S.stack=="string"){for(var a=S.stack.split(`
`),c=u.stack.split(`
`),p=a.length-1,g=c.length-1;1<=p&&0<=g&&a[p]!==c[g];)g--;for(;1<=p&&0<=g;p--,g--)if(a[p]!==c[g]){if(p!==1||g!==1)do if(p--,g--,0>g||a[p]!==c[g]){var y=`
`+a[p].replace(" at new "," at ");return t.displayName&&y.includes("<anonymous>")&&(y=y.replace("<anonymous>",t.displayName)),y}while(1<=p&&0<=g);break}}}finally{cf=!1,Error.prepareStackTrace=i}return(t=t?t.displayName||t.name:"")?Lo(t):""}function q1(t){switch(t.tag){case 5:return Lo(t.type);case 16:return Lo("Lazy");case 13:return Lo("Suspense");case 19:return Lo("SuspenseList");case 0:case 2:case 15:return t=df(t.type,!1),t;case 11:return t=df(t.type.render,!1),t;case 1:return t=df(t.type,!0),t;default:return""}}function jf(t){if(t==null)return null;if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case sl:return"Fragment";case ol:return"Portal";case Hf:return"Profiler";case Wc:return"StrictMode";case Bf:return"Suspense";case Uf:return"SuspenseList"}if(typeof t=="object")switch(t.$$typeof){case eg:return(t.displayName||"Context")+".Consumer";case Jv:return(t._context.displayName||"Context")+".Provider";case Hc:var n=t.render;return t=t.displayName,t||(t=n.displayName||n.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case Bc:return n=t.displayName||null,n!==null?n:jf(t.type)||"Memo";case Qr:n=t._payload,t=t._init;try{return jf(t(n))}catch{}}return null}function X1(t){var n=t.type;switch(t.tag){case 24:return"Cache";case 9:return(n.displayName||"Context")+".Consumer";case 10:return(n._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return t=n.render,t=t.displayName||t.name||"",n.displayName||(t!==""?"ForwardRef("+t+")":"ForwardRef");case 7:return"Fragment";case 5:return n;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return jf(n);case 8:return n===Wc?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof n=="function")return n.displayName||n.name||null;if(typeof n=="string")return n}return null}function li(t){switch(typeof t){case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function ng(t){var n=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(n==="checkbox"||n==="radio")}function Z1(t){var n=ng(t)?"checked":"value",i=Object.getOwnPropertyDescriptor(t.constructor.prototype,n),u=""+t[n];if(!t.hasOwnProperty(n)&&typeof i<"u"&&typeof i.get=="function"&&typeof i.set=="function"){var a=i.get,c=i.set;return Object.defineProperty(t,n,{configurable:!0,get:function(){return a.call(this)},set:function(p){u=""+p,c.call(this,p)}}),Object.defineProperty(t,n,{enumerable:i.enumerable}),{getValue:function(){return u},setValue:function(p){u=""+p},stopTracking:function(){t._valueTracker=null,delete t[n]}}}}function Ks(t){t._valueTracker||(t._valueTracker=Z1(t))}function rg(t){if(!t)return!1;var n=t._valueTracker;if(!n)return!0;var i=n.getValue(),u="";return t&&(u=ng(t)?t.checked?"true":"false":t.value),t=u,t!==i?(n.setValue(t),!0):!1}function Tu(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}function $f(t,n){var i=n.checked;return rt({},n,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:i??t._wrapperState.initialChecked})}function Pp(t,n){var i=n.defaultValue==null?"":n.defaultValue,u=n.checked!=null?n.checked:n.defaultChecked;i=li(n.value!=null?n.value:i),t._wrapperState={initialChecked:u,initialValue:i,controlled:n.type==="checkbox"||n.type==="radio"?n.checked!=null:n.value!=null}}function ig(t,n){n=n.checked,n!=null&&Rc(t,"checked",n,!1)}function Kf(t,n){ig(t,n);var i=li(n.value),u=n.type;if(i!=null)u==="number"?(i===0&&t.value===""||t.value!=i)&&(t.value=""+i):t.value!==""+i&&(t.value=""+i);else if(u==="submit"||u==="reset"){t.removeAttribute("value");return}n.hasOwnProperty("value")?Gf(t,n.type,i):n.hasOwnProperty("defaultValue")&&Gf(t,n.type,li(n.defaultValue)),n.checked==null&&n.defaultChecked!=null&&(t.defaultChecked=!!n.defaultChecked)}function Op(t,n,i){if(n.hasOwnProperty("value")||n.hasOwnProperty("defaultValue")){var u=n.type;if(!(u!=="submit"&&u!=="reset"||n.value!==void 0&&n.value!==null))return;n=""+t._wrapperState.initialValue,i||n===t.value||(t.value=n),t.defaultValue=n}i=t.name,i!==""&&(t.name=""),t.defaultChecked=!!t._wrapperState.initialChecked,i!==""&&(t.name=i)}function Gf(t,n,i){(n!=="number"||Tu(t.ownerDocument)!==t)&&(i==null?t.defaultValue=""+t._wrapperState.initialValue:t.defaultValue!==""+i&&(t.defaultValue=""+i))}var Eo=Array.isArray;function yl(t,n,i,u){if(t=t.options,n){n={};for(var a=0;a<i.length;a++)n["$"+i[a]]=!0;for(i=0;i<t.length;i++)a=n.hasOwnProperty("$"+t[i].value),t[i].selected!==a&&(t[i].selected=a),a&&u&&(t[i].defaultSelected=!0)}else{for(i=""+li(i),n=null,a=0;a<t.length;a++){if(t[a].value===i){t[a].selected=!0,u&&(t[a].defaultSelected=!0);return}n!==null||t[a].disabled||(n=t[a])}n!==null&&(n.selected=!0)}}function Qf(t,n){if(n.dangerouslySetInnerHTML!=null)throw Error(q(91));return rt({},n,{value:void 0,defaultValue:void 0,children:""+t._wrapperState.initialValue})}function Dp(t,n){var i=n.value;if(i==null){if(i=n.children,n=n.defaultValue,i!=null){if(n!=null)throw Error(q(92));if(Eo(i)){if(1<i.length)throw Error(q(93));i=i[0]}n=i}n==null&&(n=""),i=n}t._wrapperState={initialValue:li(i)}}function lg(t,n){var i=li(n.value),u=li(n.defaultValue);i!=null&&(i=""+i,i!==t.value&&(t.value=i),n.defaultValue==null&&t.defaultValue!==i&&(t.defaultValue=i)),u!=null&&(t.defaultValue=""+u)}function zp(t){var n=t.textContent;n===t._wrapperState.initialValue&&n!==""&&n!==null&&(t.value=n)}function og(t){switch(t){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Vf(t,n){return t==null||t==="http://www.w3.org/1999/xhtml"?og(n):t==="http://www.w3.org/2000/svg"&&n==="foreignObject"?"http://www.w3.org/1999/xhtml":t}var Gs,sg=function(t){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(n,i,u,a){MSApp.execUnsafeLocalFunction(function(){return t(n,i,u,a)})}:t}(function(t,n){if(t.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in t)t.innerHTML=n;else{for(Gs=Gs||document.createElement("div"),Gs.innerHTML="<svg>"+n.valueOf().toString()+"</svg>",n=Gs.firstChild;t.firstChild;)t.removeChild(t.firstChild);for(;n.firstChild;)t.appendChild(n.firstChild)}});function Wo(t,n){if(n){var i=t.firstChild;if(i&&i===t.lastChild&&i.nodeType===3){i.nodeValue=n;return}}t.textContent=n}var bo={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},J1=["Webkit","ms","Moz","O"];Object.keys(bo).forEach(function(t){J1.forEach(function(n){n=n+t.charAt(0).toUpperCase()+t.substring(1),bo[n]=bo[t]})});function ug(t,n,i){return n==null||typeof n=="boolean"||n===""?"":i||typeof n!="number"||n===0||bo.hasOwnProperty(t)&&bo[t]?(""+n).trim():n+"px"}function ag(t,n){t=t.style;for(var i in n)if(n.hasOwnProperty(i)){var u=i.indexOf("--")===0,a=ug(i,n[i],u);i==="float"&&(i="cssFloat"),u?t.setProperty(i,a):t[i]=a}}var ew=rt({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Yf(t,n){if(n){if(ew[t]&&(n.children!=null||n.dangerouslySetInnerHTML!=null))throw Error(q(137,t));if(n.dangerouslySetInnerHTML!=null){if(n.children!=null)throw Error(q(60));if(typeof n.dangerouslySetInnerHTML!="object"||!("__html"in n.dangerouslySetInnerHTML))throw Error(q(61))}if(n.style!=null&&typeof n.style!="object")throw Error(q(62))}}function qf(t,n){if(t.indexOf("-")===-1)return typeof n.is=="string";switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Xf=null;function Uc(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var Zf=null,wl=null,xl=null;function Ip(t){if(t=is(t)){if(typeof Zf!="function")throw Error(q(280));var n=t.stateNode;n&&(n=ta(n),Zf(t.stateNode,t.type,n))}}function fg(t){wl?xl?xl.push(t):xl=[t]:wl=t}function cg(){if(wl){var t=wl,n=xl;if(xl=wl=null,Ip(t),n)for(t=0;t<n.length;t++)Ip(n[t])}}function dg(t,n){return t(n)}function hg(){}var hf=!1;function pg(t,n,i){if(hf)return t(n,i);hf=!0;try{return dg(t,n,i)}finally{hf=!1,(wl!==null||xl!==null)&&(hg(),cg())}}function Ho(t,n){var i=t.stateNode;if(i===null)return null;var u=ta(i);if(u===null)return null;i=u[n];e:switch(n){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(u=!u.disabled)||(t=t.type,u=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!u;break e;default:t=!1}if(t)return null;if(i&&typeof i!="function")throw Error(q(231,n,typeof i));return i}var Jf=!1;if(Mr)try{var mo={};Object.defineProperty(mo,"passive",{get:function(){Jf=!0}}),window.addEventListener("test",mo,mo),window.removeEventListener("test",mo,mo)}catch{Jf=!1}function tw(t,n,i,u,a,c,p,g,y){var S=Array.prototype.slice.call(arguments,3);try{n.apply(i,S)}catch(N){this.onError(N)}}var _o=!1,Lu=null,Eu=!1,ec=null,nw={onError:function(t){_o=!0,Lu=t}};function rw(t,n,i,u,a,c,p,g,y){_o=!1,Lu=null,tw.apply(nw,arguments)}function iw(t,n,i,u,a,c,p,g,y){if(rw.apply(this,arguments),_o){if(_o){var S=Lu;_o=!1,Lu=null}else throw Error(q(198));Eu||(Eu=!0,ec=S)}}function Ii(t){var n=t,i=t;if(t.alternate)for(;n.return;)n=n.return;else{t=n;do n=t,n.flags&4098&&(i=n.return),t=n.return;while(t)}return n.tag===3?i:null}function vg(t){if(t.tag===13){var n=t.memoizedState;if(n===null&&(t=t.alternate,t!==null&&(n=t.memoizedState)),n!==null)return n.dehydrated}return null}function Ap(t){if(Ii(t)!==t)throw Error(q(188))}function lw(t){var n=t.alternate;if(!n){if(n=Ii(t),n===null)throw Error(q(188));return n!==t?null:t}for(var i=t,u=n;;){var a=i.return;if(a===null)break;var c=a.alternate;if(c===null){if(u=a.return,u!==null){i=u;continue}break}if(a.child===c.child){for(c=a.child;c;){if(c===i)return Ap(a),t;if(c===u)return Ap(a),n;c=c.sibling}throw Error(q(188))}if(i.return!==u.return)i=a,u=c;else{for(var p=!1,g=a.child;g;){if(g===i){p=!0,i=a,u=c;break}if(g===u){p=!0,u=a,i=c;break}g=g.sibling}if(!p){for(g=c.child;g;){if(g===i){p=!0,i=c,u=a;break}if(g===u){p=!0,u=c,i=a;break}g=g.sibling}if(!p)throw Error(q(189))}}if(i.alternate!==u)throw Error(q(190))}if(i.tag!==3)throw Error(q(188));return i.stateNode.current===i?t:n}function gg(t){return t=lw(t),t!==null?mg(t):null}function mg(t){if(t.tag===5||t.tag===6)return t;for(t=t.child;t!==null;){var n=mg(t);if(n!==null)return n;t=t.sibling}return null}var yg=mn.unstable_scheduleCallback,Fp=mn.unstable_cancelCallback,ow=mn.unstable_shouldYield,sw=mn.unstable_requestPaint,ut=mn.unstable_now,uw=mn.unstable_getCurrentPriorityLevel,jc=mn.unstable_ImmediatePriority,wg=mn.unstable_UserBlockingPriority,Nu=mn.unstable_NormalPriority,aw=mn.unstable_LowPriority,xg=mn.unstable_IdlePriority,Xu=null,lr=null;function fw(t){if(lr&&typeof lr.onCommitFiberRoot=="function")try{lr.onCommitFiberRoot(Xu,t,void 0,(t.current.flags&128)===128)}catch{}}var Kn=Math.clz32?Math.clz32:hw,cw=Math.log,dw=Math.LN2;function hw(t){return t>>>=0,t===0?32:31-(cw(t)/dw|0)|0}var Qs=64,Vs=4194304;function No(t){switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return t&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return t}}function Mu(t,n){var i=t.pendingLanes;if(i===0)return 0;var u=0,a=t.suspendedLanes,c=t.pingedLanes,p=i&268435455;if(p!==0){var g=p&~a;g!==0?u=No(g):(c&=p,c!==0&&(u=No(c)))}else p=i&~a,p!==0?u=No(p):c!==0&&(u=No(c));if(u===0)return 0;if(n!==0&&n!==u&&!(n&a)&&(a=u&-u,c=n&-n,a>=c||a===16&&(c&4194240)!==0))return n;if(u&4&&(u|=i&16),n=t.entangledLanes,n!==0)for(t=t.entanglements,n&=u;0<n;)i=31-Kn(n),a=1<<i,u|=t[i],n&=~a;return u}function pw(t,n){switch(t){case 1:case 2:case 4:return n+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return n+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function vw(t,n){for(var i=t.suspendedLanes,u=t.pingedLanes,a=t.expirationTimes,c=t.pendingLanes;0<c;){var p=31-Kn(c),g=1<<p,y=a[p];y===-1?(!(g&i)||g&u)&&(a[p]=pw(g,n)):y<=n&&(t.expiredLanes|=g),c&=~g}}function tc(t){return t=t.pendingLanes&-1073741825,t!==0?t:t&1073741824?1073741824:0}function Sg(){var t=Qs;return Qs<<=1,!(Qs&4194240)&&(Qs=64),t}function pf(t){for(var n=[],i=0;31>i;i++)n.push(t);return n}function ns(t,n,i){t.pendingLanes|=n,n!==536870912&&(t.suspendedLanes=0,t.pingedLanes=0),t=t.eventTimes,n=31-Kn(n),t[n]=i}function gw(t,n){var i=t.pendingLanes&~n;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.expiredLanes&=n,t.mutableReadLanes&=n,t.entangledLanes&=n,n=t.entanglements;var u=t.eventTimes;for(t=t.expirationTimes;0<i;){var a=31-Kn(i),c=1<<a;n[a]=0,u[a]=-1,t[a]=-1,i&=~c}}function $c(t,n){var i=t.entangledLanes|=n;for(t=t.entanglements;i;){var u=31-Kn(i),a=1<<u;a&n|t[u]&n&&(t[u]|=n),i&=~a}}var We=0;function kg(t){return t&=-t,1<t?4<t?t&268435455?16:536870912:4:1}var Cg,Kc,Tg,Lg,Eg,nc=!1,Ys=[],Jr=null,ei=null,ti=null,Bo=new Map,Uo=new Map,Yr=[],mw="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Rp(t,n){switch(t){case"focusin":case"focusout":Jr=null;break;case"dragenter":case"dragleave":ei=null;break;case"mouseover":case"mouseout":ti=null;break;case"pointerover":case"pointerout":Bo.delete(n.pointerId);break;case"gotpointercapture":case"lostpointercapture":Uo.delete(n.pointerId)}}function yo(t,n,i,u,a,c){return t===null||t.nativeEvent!==c?(t={blockedOn:n,domEventName:i,eventSystemFlags:u,nativeEvent:c,targetContainers:[a]},n!==null&&(n=is(n),n!==null&&Kc(n)),t):(t.eventSystemFlags|=u,n=t.targetContainers,a!==null&&n.indexOf(a)===-1&&n.push(a),t)}function yw(t,n,i,u,a){switch(n){case"focusin":return Jr=yo(Jr,t,n,i,u,a),!0;case"dragenter":return ei=yo(ei,t,n,i,u,a),!0;case"mouseover":return ti=yo(ti,t,n,i,u,a),!0;case"pointerover":var c=a.pointerId;return Bo.set(c,yo(Bo.get(c)||null,t,n,i,u,a)),!0;case"gotpointercapture":return c=a.pointerId,Uo.set(c,yo(Uo.get(c)||null,t,n,i,u,a)),!0}return!1}function Ng(t){var n=Li(t.target);if(n!==null){var i=Ii(n);if(i!==null){if(n=i.tag,n===13){if(n=vg(i),n!==null){t.blockedOn=n,Eg(t.priority,function(){Tg(i)});return}}else if(n===3&&i.stateNode.current.memoizedState.isDehydrated){t.blockedOn=i.tag===3?i.stateNode.containerInfo:null;return}}}t.blockedOn=null}function au(t){if(t.blockedOn!==null)return!1;for(var n=t.targetContainers;0<n.length;){var i=rc(t.domEventName,t.eventSystemFlags,n[0],t.nativeEvent);if(i===null){i=t.nativeEvent;var u=new i.constructor(i.type,i);Xf=u,i.target.dispatchEvent(u),Xf=null}else return n=is(i),n!==null&&Kc(n),t.blockedOn=i,!1;n.shift()}return!0}function Wp(t,n,i){au(t)&&i.delete(n)}function ww(){nc=!1,Jr!==null&&au(Jr)&&(Jr=null),ei!==null&&au(ei)&&(ei=null),ti!==null&&au(ti)&&(ti=null),Bo.forEach(Wp),Uo.forEach(Wp)}function wo(t,n){t.blockedOn===n&&(t.blockedOn=null,nc||(nc=!0,mn.unstable_scheduleCallback(mn.unstable_NormalPriority,ww)))}function jo(t){function n(a){return wo(a,t)}if(0<Ys.length){wo(Ys[0],t);for(var i=1;i<Ys.length;i++){var u=Ys[i];u.blockedOn===t&&(u.blockedOn=null)}}for(Jr!==null&&wo(Jr,t),ei!==null&&wo(ei,t),ti!==null&&wo(ti,t),Bo.forEach(n),Uo.forEach(n),i=0;i<Yr.length;i++)u=Yr[i],u.blockedOn===t&&(u.blockedOn=null);for(;0<Yr.length&&(i=Yr[0],i.blockedOn===null);)Ng(i),i.blockedOn===null&&Yr.shift()}var Sl=Pr.ReactCurrentBatchConfig,bu=!0;function xw(t,n,i,u){var a=We,c=Sl.transition;Sl.transition=null;try{We=1,Gc(t,n,i,u)}finally{We=a,Sl.transition=c}}function Sw(t,n,i,u){var a=We,c=Sl.transition;Sl.transition=null;try{We=4,Gc(t,n,i,u)}finally{We=a,Sl.transition=c}}function Gc(t,n,i,u){if(bu){var a=rc(t,n,i,u);if(a===null)Tf(t,n,u,_u,i),Rp(t,u);else if(yw(a,t,n,i,u))u.stopPropagation();else if(Rp(t,u),n&4&&-1<mw.indexOf(t)){for(;a!==null;){var c=is(a);if(c!==null&&Cg(c),c=rc(t,n,i,u),c===null&&Tf(t,n,u,_u,i),c===a)break;a=c}a!==null&&u.stopPropagation()}else Tf(t,n,u,null,i)}}var _u=null;function rc(t,n,i,u){if(_u=null,t=Uc(u),t=Li(t),t!==null)if(n=Ii(t),n===null)t=null;else if(i=n.tag,i===13){if(t=vg(n),t!==null)return t;t=null}else if(i===3){if(n.stateNode.current.memoizedState.isDehydrated)return n.tag===3?n.stateNode.containerInfo:null;t=null}else n!==t&&(t=null);return _u=t,null}function Mg(t){switch(t){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(uw()){case jc:return 1;case wg:return 4;case Nu:case aw:return 16;case xg:return 536870912;default:return 16}default:return 16}}var Xr=null,Qc=null,fu=null;function bg(){if(fu)return fu;var t,n=Qc,i=n.length,u,a="value"in Xr?Xr.value:Xr.textContent,c=a.length;for(t=0;t<i&&n[t]===a[t];t++);var p=i-t;for(u=1;u<=p&&n[i-u]===a[c-u];u++);return fu=a.slice(t,1<u?1-u:void 0)}function cu(t){var n=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&n===13&&(t=13)):t=n,t===10&&(t=13),32<=t||t===13?t:0}function qs(){return!0}function Hp(){return!1}function wn(t){function n(i,u,a,c,p){this._reactName=i,this._targetInst=a,this.type=u,this.nativeEvent=c,this.target=p,this.currentTarget=null;for(var g in t)t.hasOwnProperty(g)&&(i=t[g],this[g]=i?i(c):c[g]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?qs:Hp,this.isPropagationStopped=Hp,this}return rt(n.prototype,{preventDefault:function(){this.defaultPrevented=!0;var i=this.nativeEvent;i&&(i.preventDefault?i.preventDefault():typeof i.returnValue!="unknown"&&(i.returnValue=!1),this.isDefaultPrevented=qs)},stopPropagation:function(){var i=this.nativeEvent;i&&(i.stopPropagation?i.stopPropagation():typeof i.cancelBubble!="unknown"&&(i.cancelBubble=!0),this.isPropagationStopped=qs)},persist:function(){},isPersistent:qs}),n}var Ol={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Vc=wn(Ol),rs=rt({},Ol,{view:0,detail:0}),kw=wn(rs),vf,gf,xo,Zu=rt({},rs,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Yc,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==xo&&(xo&&t.type==="mousemove"?(vf=t.screenX-xo.screenX,gf=t.screenY-xo.screenY):gf=vf=0,xo=t),vf)},movementY:function(t){return"movementY"in t?t.movementY:gf}}),Bp=wn(Zu),Cw=rt({},Zu,{dataTransfer:0}),Tw=wn(Cw),Lw=rt({},rs,{relatedTarget:0}),mf=wn(Lw),Ew=rt({},Ol,{animationName:0,elapsedTime:0,pseudoElement:0}),Nw=wn(Ew),Mw=rt({},Ol,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),bw=wn(Mw),_w=rt({},Ol,{data:0}),Up=wn(_w),Pw={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Ow={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Dw={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function zw(t){var n=this.nativeEvent;return n.getModifierState?n.getModifierState(t):(t=Dw[t])?!!n[t]:!1}function Yc(){return zw}var Iw=rt({},rs,{key:function(t){if(t.key){var n=Pw[t.key]||t.key;if(n!=="Unidentified")return n}return t.type==="keypress"?(t=cu(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?Ow[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Yc,charCode:function(t){return t.type==="keypress"?cu(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?cu(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Aw=wn(Iw),Fw=rt({},Zu,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),jp=wn(Fw),Rw=rt({},rs,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Yc}),Ww=wn(Rw),Hw=rt({},Ol,{propertyName:0,elapsedTime:0,pseudoElement:0}),Bw=wn(Hw),Uw=rt({},Zu,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),jw=wn(Uw),$w=[9,13,27,32],qc=Mr&&"CompositionEvent"in window,Po=null;Mr&&"documentMode"in document&&(Po=document.documentMode);var Kw=Mr&&"TextEvent"in window&&!Po,_g=Mr&&(!qc||Po&&8<Po&&11>=Po),$p=String.fromCharCode(32),Kp=!1;function Pg(t,n){switch(t){case"keyup":return $w.indexOf(n.keyCode)!==-1;case"keydown":return n.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Og(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var ul=!1;function Gw(t,n){switch(t){case"compositionend":return Og(n);case"keypress":return n.which!==32?null:(Kp=!0,$p);case"textInput":return t=n.data,t===$p&&Kp?null:t;default:return null}}function Qw(t,n){if(ul)return t==="compositionend"||!qc&&Pg(t,n)?(t=bg(),fu=Qc=Xr=null,ul=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(n.ctrlKey||n.altKey||n.metaKey)||n.ctrlKey&&n.altKey){if(n.char&&1<n.char.length)return n.char;if(n.which)return String.fromCharCode(n.which)}return null;case"compositionend":return _g&&n.locale!=="ko"?null:n.data;default:return null}}var Vw={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Gp(t){var n=t&&t.nodeName&&t.nodeName.toLowerCase();return n==="input"?!!Vw[t.type]:n==="textarea"}function Dg(t,n,i,u){fg(u),n=Pu(n,"onChange"),0<n.length&&(i=new Vc("onChange","change",null,i,u),t.push({event:i,listeners:n}))}var Oo=null,$o=null;function Yw(t){$g(t,0)}function Ju(t){var n=cl(t);if(rg(n))return t}function qw(t,n){if(t==="change")return n}var zg=!1;if(Mr){var yf;if(Mr){var wf="oninput"in document;if(!wf){var Qp=document.createElement("div");Qp.setAttribute("oninput","return;"),wf=typeof Qp.oninput=="function"}yf=wf}else yf=!1;zg=yf&&(!document.documentMode||9<document.documentMode)}function Vp(){Oo&&(Oo.detachEvent("onpropertychange",Ig),$o=Oo=null)}function Ig(t){if(t.propertyName==="value"&&Ju($o)){var n=[];Dg(n,$o,t,Uc(t)),pg(Yw,n)}}function Xw(t,n,i){t==="focusin"?(Vp(),Oo=n,$o=i,Oo.attachEvent("onpropertychange",Ig)):t==="focusout"&&Vp()}function Zw(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Ju($o)}function Jw(t,n){if(t==="click")return Ju(n)}function ex(t,n){if(t==="input"||t==="change")return Ju(n)}function tx(t,n){return t===n&&(t!==0||1/t===1/n)||t!==t&&n!==n}var Gn=typeof Object.is=="function"?Object.is:tx;function Ko(t,n){if(Gn(t,n))return!0;if(typeof t!="object"||t===null||typeof n!="object"||n===null)return!1;var i=Object.keys(t),u=Object.keys(n);if(i.length!==u.length)return!1;for(u=0;u<i.length;u++){var a=i[u];if(!Wf.call(n,a)||!Gn(t[a],n[a]))return!1}return!0}function Yp(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function qp(t,n){var i=Yp(t);t=0;for(var u;i;){if(i.nodeType===3){if(u=t+i.textContent.length,t<=n&&u>=n)return{node:i,offset:n-t};t=u}e:{for(;i;){if(i.nextSibling){i=i.nextSibling;break e}i=i.parentNode}i=void 0}i=Yp(i)}}function Ag(t,n){return t&&n?t===n?!0:t&&t.nodeType===3?!1:n&&n.nodeType===3?Ag(t,n.parentNode):"contains"in t?t.contains(n):t.compareDocumentPosition?!!(t.compareDocumentPosition(n)&16):!1:!1}function Fg(){for(var t=window,n=Tu();n instanceof t.HTMLIFrameElement;){try{var i=typeof n.contentWindow.location.href=="string"}catch{i=!1}if(i)t=n.contentWindow;else break;n=Tu(t.document)}return n}function Xc(t){var n=t&&t.nodeName&&t.nodeName.toLowerCase();return n&&(n==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||n==="textarea"||t.contentEditable==="true")}function nx(t){var n=Fg(),i=t.focusedElem,u=t.selectionRange;if(n!==i&&i&&i.ownerDocument&&Ag(i.ownerDocument.documentElement,i)){if(u!==null&&Xc(i)){if(n=u.start,t=u.end,t===void 0&&(t=n),"selectionStart"in i)i.selectionStart=n,i.selectionEnd=Math.min(t,i.value.length);else if(t=(n=i.ownerDocument||document)&&n.defaultView||window,t.getSelection){t=t.getSelection();var a=i.textContent.length,c=Math.min(u.start,a);u=u.end===void 0?c:Math.min(u.end,a),!t.extend&&c>u&&(a=u,u=c,c=a),a=qp(i,c);var p=qp(i,u);a&&p&&(t.rangeCount!==1||t.anchorNode!==a.node||t.anchorOffset!==a.offset||t.focusNode!==p.node||t.focusOffset!==p.offset)&&(n=n.createRange(),n.setStart(a.node,a.offset),t.removeAllRanges(),c>u?(t.addRange(n),t.extend(p.node,p.offset)):(n.setEnd(p.node,p.offset),t.addRange(n)))}}for(n=[],t=i;t=t.parentNode;)t.nodeType===1&&n.push({element:t,left:t.scrollLeft,top:t.scrollTop});for(typeof i.focus=="function"&&i.focus(),i=0;i<n.length;i++)t=n[i],t.element.scrollLeft=t.left,t.element.scrollTop=t.top}}var rx=Mr&&"documentMode"in document&&11>=document.documentMode,al=null,ic=null,Do=null,lc=!1;function Xp(t,n,i){var u=i.window===i?i.document:i.nodeType===9?i:i.ownerDocument;lc||al==null||al!==Tu(u)||(u=al,"selectionStart"in u&&Xc(u)?u={start:u.selectionStart,end:u.selectionEnd}:(u=(u.ownerDocument&&u.ownerDocument.defaultView||window).getSelection(),u={anchorNode:u.anchorNode,anchorOffset:u.anchorOffset,focusNode:u.focusNode,focusOffset:u.focusOffset}),Do&&Ko(Do,u)||(Do=u,u=Pu(ic,"onSelect"),0<u.length&&(n=new Vc("onSelect","select",null,n,i),t.push({event:n,listeners:u}),n.target=al)))}function Xs(t,n){var i={};return i[t.toLowerCase()]=n.toLowerCase(),i["Webkit"+t]="webkit"+n,i["Moz"+t]="moz"+n,i}var fl={animationend:Xs("Animation","AnimationEnd"),animationiteration:Xs("Animation","AnimationIteration"),animationstart:Xs("Animation","AnimationStart"),transitionend:Xs("Transition","TransitionEnd")},xf={},Rg={};Mr&&(Rg=document.createElement("div").style,"AnimationEvent"in window||(delete fl.animationend.animation,delete fl.animationiteration.animation,delete fl.animationstart.animation),"TransitionEvent"in window||delete fl.transitionend.transition);function ea(t){if(xf[t])return xf[t];if(!fl[t])return t;var n=fl[t],i;for(i in n)if(n.hasOwnProperty(i)&&i in Rg)return xf[t]=n[i];return t}var Wg=ea("animationend"),Hg=ea("animationiteration"),Bg=ea("animationstart"),Ug=ea("transitionend"),jg=new Map,Zp="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function ui(t,n){jg.set(t,n),zi(n,[t])}for(var Sf=0;Sf<Zp.length;Sf++){var kf=Zp[Sf],ix=kf.toLowerCase(),lx=kf[0].toUpperCase()+kf.slice(1);ui(ix,"on"+lx)}ui(Wg,"onAnimationEnd");ui(Hg,"onAnimationIteration");ui(Bg,"onAnimationStart");ui("dblclick","onDoubleClick");ui("focusin","onFocus");ui("focusout","onBlur");ui(Ug,"onTransitionEnd");Tl("onMouseEnter",["mouseout","mouseover"]);Tl("onMouseLeave",["mouseout","mouseover"]);Tl("onPointerEnter",["pointerout","pointerover"]);Tl("onPointerLeave",["pointerout","pointerover"]);zi("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));zi("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));zi("onBeforeInput",["compositionend","keypress","textInput","paste"]);zi("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));zi("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));zi("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Mo="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),ox=new Set("cancel close invalid load scroll toggle".split(" ").concat(Mo));function Jp(t,n,i){var u=t.type||"unknown-event";t.currentTarget=i,iw(u,n,void 0,t),t.currentTarget=null}function $g(t,n){n=(n&4)!==0;for(var i=0;i<t.length;i++){var u=t[i],a=u.event;u=u.listeners;e:{var c=void 0;if(n)for(var p=u.length-1;0<=p;p--){var g=u[p],y=g.instance,S=g.currentTarget;if(g=g.listener,y!==c&&a.isPropagationStopped())break e;Jp(a,g,S),c=y}else for(p=0;p<u.length;p++){if(g=u[p],y=g.instance,S=g.currentTarget,g=g.listener,y!==c&&a.isPropagationStopped())break e;Jp(a,g,S),c=y}}}if(Eu)throw t=ec,Eu=!1,ec=null,t}function Ge(t,n){var i=n[fc];i===void 0&&(i=n[fc]=new Set);var u=t+"__bubble";i.has(u)||(Kg(n,t,2,!1),i.add(u))}function Cf(t,n,i){var u=0;n&&(u|=4),Kg(i,t,u,n)}var Zs="_reactListening"+Math.random().toString(36).slice(2);function Go(t){if(!t[Zs]){t[Zs]=!0,Zv.forEach(function(i){i!=="selectionchange"&&(ox.has(i)||Cf(i,!1,t),Cf(i,!0,t))});var n=t.nodeType===9?t:t.ownerDocument;n===null||n[Zs]||(n[Zs]=!0,Cf("selectionchange",!1,n))}}function Kg(t,n,i,u){switch(Mg(n)){case 1:var a=xw;break;case 4:a=Sw;break;default:a=Gc}i=a.bind(null,n,i,t),a=void 0,!Jf||n!=="touchstart"&&n!=="touchmove"&&n!=="wheel"||(a=!0),u?a!==void 0?t.addEventListener(n,i,{capture:!0,passive:a}):t.addEventListener(n,i,!0):a!==void 0?t.addEventListener(n,i,{passive:a}):t.addEventListener(n,i,!1)}function Tf(t,n,i,u,a){var c=u;if(!(n&1)&&!(n&2)&&u!==null)e:for(;;){if(u===null)return;var p=u.tag;if(p===3||p===4){var g=u.stateNode.containerInfo;if(g===a||g.nodeType===8&&g.parentNode===a)break;if(p===4)for(p=u.return;p!==null;){var y=p.tag;if((y===3||y===4)&&(y=p.stateNode.containerInfo,y===a||y.nodeType===8&&y.parentNode===a))return;p=p.return}for(;g!==null;){if(p=Li(g),p===null)return;if(y=p.tag,y===5||y===6){u=c=p;continue e}g=g.parentNode}}u=u.return}pg(function(){var S=c,N=Uc(i),W=[];e:{var O=jg.get(t);if(O!==void 0){var U=Vc,B=t;switch(t){case"keypress":if(cu(i)===0)break e;case"keydown":case"keyup":U=Aw;break;case"focusin":B="focus",U=mf;break;case"focusout":B="blur",U=mf;break;case"beforeblur":case"afterblur":U=mf;break;case"click":if(i.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":U=Bp;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":U=Tw;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":U=Ww;break;case Wg:case Hg:case Bg:U=Nw;break;case Ug:U=Bw;break;case"scroll":U=kw;break;case"wheel":U=jw;break;case"copy":case"cut":case"paste":U=bw;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":U=jp}var oe=(n&4)!==0,G=!oe&&t==="scroll",_=oe?O!==null?O+"Capture":null:O;oe=[];for(var C=S,D;C!==null;){D=C;var $=D.stateNode;if(D.tag===5&&$!==null&&(D=$,_!==null&&($=Ho(C,_),$!=null&&oe.push(Qo(C,$,D)))),G)break;C=C.return}0<oe.length&&(O=new U(O,B,null,i,N),W.push({event:O,listeners:oe}))}}if(!(n&7)){e:{if(O=t==="mouseover"||t==="pointerover",U=t==="mouseout"||t==="pointerout",O&&i!==Xf&&(B=i.relatedTarget||i.fromElement)&&(Li(B)||B[br]))break e;if((U||O)&&(O=N.window===N?N:(O=N.ownerDocument)?O.defaultView||O.parentWindow:window,U?(B=i.relatedTarget||i.toElement,U=S,B=B?Li(B):null,B!==null&&(G=Ii(B),B!==G||B.tag!==5&&B.tag!==6)&&(B=null)):(U=null,B=S),U!==B)){if(oe=Bp,$="onMouseLeave",_="onMouseEnter",C="mouse",(t==="pointerout"||t==="pointerover")&&(oe=jp,$="onPointerLeave",_="onPointerEnter",C="pointer"),G=U==null?O:cl(U),D=B==null?O:cl(B),O=new oe($,C+"leave",U,i,N),O.target=G,O.relatedTarget=D,$=null,Li(N)===S&&(oe=new oe(_,C+"enter",B,i,N),oe.target=D,oe.relatedTarget=G,$=oe),G=$,U&&B)t:{for(oe=U,_=B,C=0,D=oe;D;D=ll(D))C++;for(D=0,$=_;$;$=ll($))D++;for(;0<C-D;)oe=ll(oe),C--;for(;0<D-C;)_=ll(_),D--;for(;C--;){if(oe===_||_!==null&&oe===_.alternate)break t;oe=ll(oe),_=ll(_)}oe=null}else oe=null;U!==null&&ev(W,O,U,oe,!1),B!==null&&G!==null&&ev(W,G,B,oe,!0)}}e:{if(O=S?cl(S):window,U=O.nodeName&&O.nodeName.toLowerCase(),U==="select"||U==="input"&&O.type==="file")var Y=qw;else if(Gp(O))if(zg)Y=ex;else{Y=Zw;var ie=Xw}else(U=O.nodeName)&&U.toLowerCase()==="input"&&(O.type==="checkbox"||O.type==="radio")&&(Y=Jw);if(Y&&(Y=Y(t,S))){Dg(W,Y,i,N);break e}ie&&ie(t,O,S),t==="focusout"&&(ie=O._wrapperState)&&ie.controlled&&O.type==="number"&&Gf(O,"number",O.value)}switch(ie=S?cl(S):window,t){case"focusin":(Gp(ie)||ie.contentEditable==="true")&&(al=ie,ic=S,Do=null);break;case"focusout":Do=ic=al=null;break;case"mousedown":lc=!0;break;case"contextmenu":case"mouseup":case"dragend":lc=!1,Xp(W,i,N);break;case"selectionchange":if(rx)break;case"keydown":case"keyup":Xp(W,i,N)}var I;if(qc)e:{switch(t){case"compositionstart":var Z="onCompositionStart";break e;case"compositionend":Z="onCompositionEnd";break e;case"compositionupdate":Z="onCompositionUpdate";break e}Z=void 0}else ul?Pg(t,i)&&(Z="onCompositionEnd"):t==="keydown"&&i.keyCode===229&&(Z="onCompositionStart");Z&&(_g&&i.locale!=="ko"&&(ul||Z!=="onCompositionStart"?Z==="onCompositionEnd"&&ul&&(I=bg()):(Xr=N,Qc="value"in Xr?Xr.value:Xr.textContent,ul=!0)),ie=Pu(S,Z),0<ie.length&&(Z=new Up(Z,t,null,i,N),W.push({event:Z,listeners:ie}),I?Z.data=I:(I=Og(i),I!==null&&(Z.data=I)))),(I=Kw?Gw(t,i):Qw(t,i))&&(S=Pu(S,"onBeforeInput"),0<S.length&&(N=new Up("onBeforeInput","beforeinput",null,i,N),W.push({event:N,listeners:S}),N.data=I))}$g(W,n)})}function Qo(t,n,i){return{instance:t,listener:n,currentTarget:i}}function Pu(t,n){for(var i=n+"Capture",u=[];t!==null;){var a=t,c=a.stateNode;a.tag===5&&c!==null&&(a=c,c=Ho(t,i),c!=null&&u.unshift(Qo(t,c,a)),c=Ho(t,n),c!=null&&u.push(Qo(t,c,a))),t=t.return}return u}function ll(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5);return t||null}function ev(t,n,i,u,a){for(var c=n._reactName,p=[];i!==null&&i!==u;){var g=i,y=g.alternate,S=g.stateNode;if(y!==null&&y===u)break;g.tag===5&&S!==null&&(g=S,a?(y=Ho(i,c),y!=null&&p.unshift(Qo(i,y,g))):a||(y=Ho(i,c),y!=null&&p.push(Qo(i,y,g)))),i=i.return}p.length!==0&&t.push({event:n,listeners:p})}var sx=/\r\n?/g,ux=/\u0000|\uFFFD/g;function tv(t){return(typeof t=="string"?t:""+t).replace(sx,`
`).replace(ux,"")}function Js(t,n,i){if(n=tv(n),tv(t)!==n&&i)throw Error(q(425))}function Ou(){}var oc=null,sc=null;function uc(t,n){return t==="textarea"||t==="noscript"||typeof n.children=="string"||typeof n.children=="number"||typeof n.dangerouslySetInnerHTML=="object"&&n.dangerouslySetInnerHTML!==null&&n.dangerouslySetInnerHTML.__html!=null}var ac=typeof setTimeout=="function"?setTimeout:void 0,ax=typeof clearTimeout=="function"?clearTimeout:void 0,nv=typeof Promise=="function"?Promise:void 0,fx=typeof queueMicrotask=="function"?queueMicrotask:typeof nv<"u"?function(t){return nv.resolve(null).then(t).catch(cx)}:ac;function cx(t){setTimeout(function(){throw t})}function Lf(t,n){var i=n,u=0;do{var a=i.nextSibling;if(t.removeChild(i),a&&a.nodeType===8)if(i=a.data,i==="/$"){if(u===0){t.removeChild(a),jo(n);return}u--}else i!=="$"&&i!=="$?"&&i!=="$!"||u++;i=a}while(i);jo(n)}function Tr(t){for(;t!=null;t=t.nextSibling){var n=t.nodeType;if(n===1||n===3)break;if(n===8){if(n=t.data,n==="$"||n==="$!"||n==="$?")break;if(n==="/$")return null}}return t}function rv(t){t=t.previousSibling;for(var n=0;t;){if(t.nodeType===8){var i=t.data;if(i==="$"||i==="$!"||i==="$?"){if(n===0)return t;n--}else i==="/$"&&n++}t=t.previousSibling}return null}var Dl=Math.random().toString(36).slice(2),ir="__reactFiber$"+Dl,Vo="__reactProps$"+Dl,br="__reactContainer$"+Dl,fc="__reactEvents$"+Dl,dx="__reactListeners$"+Dl,hx="__reactHandles$"+Dl;function Li(t){var n=t[ir];if(n)return n;for(var i=t.parentNode;i;){if(n=i[br]||i[ir]){if(i=n.alternate,n.child!==null||i!==null&&i.child!==null)for(t=rv(t);t!==null;){if(i=t[ir])return i;t=rv(t)}return n}t=i,i=t.parentNode}return null}function is(t){return t=t[ir]||t[br],!t||t.tag!==5&&t.tag!==6&&t.tag!==13&&t.tag!==3?null:t}function cl(t){if(t.tag===5||t.tag===6)return t.stateNode;throw Error(q(33))}function ta(t){return t[Vo]||null}var cc=[],dl=-1;function ai(t){return{current:t}}function Qe(t){0>dl||(t.current=cc[dl],cc[dl]=null,dl--)}function $e(t,n){dl++,cc[dl]=t.current,t.current=n}var oi={},Ut=ai(oi),un=ai(!1),bi=oi;function Ll(t,n){var i=t.type.contextTypes;if(!i)return oi;var u=t.stateNode;if(u&&u.__reactInternalMemoizedUnmaskedChildContext===n)return u.__reactInternalMemoizedMaskedChildContext;var a={},c;for(c in i)a[c]=n[c];return u&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=n,t.__reactInternalMemoizedMaskedChildContext=a),a}function an(t){return t=t.childContextTypes,t!=null}function Du(){Qe(un),Qe(Ut)}function iv(t,n,i){if(Ut.current!==oi)throw Error(q(168));$e(Ut,n),$e(un,i)}function Gg(t,n,i){var u=t.stateNode;if(n=n.childContextTypes,typeof u.getChildContext!="function")return i;u=u.getChildContext();for(var a in u)if(!(a in n))throw Error(q(108,X1(t)||"Unknown",a));return rt({},i,u)}function zu(t){return t=(t=t.stateNode)&&t.__reactInternalMemoizedMergedChildContext||oi,bi=Ut.current,$e(Ut,t),$e(un,un.current),!0}function lv(t,n,i){var u=t.stateNode;if(!u)throw Error(q(169));i?(t=Gg(t,n,bi),u.__reactInternalMemoizedMergedChildContext=t,Qe(un),Qe(Ut),$e(Ut,t)):Qe(un),$e(un,i)}var Cr=null,na=!1,Ef=!1;function Qg(t){Cr===null?Cr=[t]:Cr.push(t)}function px(t){na=!0,Qg(t)}function fi(){if(!Ef&&Cr!==null){Ef=!0;var t=0,n=We;try{var i=Cr;for(We=1;t<i.length;t++){var u=i[t];do u=u(!0);while(u!==null)}Cr=null,na=!1}catch(a){throw Cr!==null&&(Cr=Cr.slice(t+1)),yg(jc,fi),a}finally{We=n,Ef=!1}}return null}var vx=Pr.ReactCurrentBatchConfig;function Hn(t,n){if(t&&t.defaultProps){n=rt({},n),t=t.defaultProps;for(var i in t)n[i]===void 0&&(n[i]=t[i]);return n}return n}var Iu=ai(null),Au=null,hl=null,Zc=null;function Jc(){Zc=hl=Au=null}function ed(t){var n=Iu.current;Qe(Iu),t._currentValue=n}function dc(t,n,i){for(;t!==null;){var u=t.alternate;if((t.childLanes&n)!==n?(t.childLanes|=n,u!==null&&(u.childLanes|=n)):u!==null&&(u.childLanes&n)!==n&&(u.childLanes|=n),t===i)break;t=t.return}}function kl(t,n){Au=t,Zc=hl=null,t=t.dependencies,t!==null&&t.firstContext!==null&&(t.lanes&n&&(sn=!0),t.firstContext=null)}function On(t){var n=t._currentValue;if(Zc!==t)if(t={context:t,memoizedValue:n,next:null},hl===null){if(Au===null)throw Error(q(308));hl=t,Au.dependencies={lanes:0,firstContext:t}}else hl=hl.next=t;return n}var $n=null,Vr=!1;function td(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Vg(t,n){t=t.updateQueue,n.updateQueue===t&&(n.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,effects:t.effects})}function Nr(t,n){return{eventTime:t,lane:n,tag:0,payload:null,callback:null,next:null}}function ni(t,n){var i=t.updateQueue;i!==null&&(i=i.shared,F0(t)?(t=i.interleaved,t===null?(n.next=n,$n===null?$n=[i]:$n.push(i)):(n.next=t.next,t.next=n),i.interleaved=n):(t=i.pending,t===null?n.next=n:(n.next=t.next,t.next=n),i.pending=n))}function du(t,n,i){if(n=n.updateQueue,n!==null&&(n=n.shared,(i&4194240)!==0)){var u=n.lanes;u&=t.pendingLanes,i|=u,n.lanes=i,$c(t,i)}}function ov(t,n){var i=t.updateQueue,u=t.alternate;if(u!==null&&(u=u.updateQueue,i===u)){var a=null,c=null;if(i=i.firstBaseUpdate,i!==null){do{var p={eventTime:i.eventTime,lane:i.lane,tag:i.tag,payload:i.payload,callback:i.callback,next:null};c===null?a=c=p:c=c.next=p,i=i.next}while(i!==null);c===null?a=c=n:c=c.next=n}else a=c=n;i={baseState:u.baseState,firstBaseUpdate:a,lastBaseUpdate:c,shared:u.shared,effects:u.effects},t.updateQueue=i;return}t=i.lastBaseUpdate,t===null?i.firstBaseUpdate=n:t.next=n,i.lastBaseUpdate=n}function Fu(t,n,i,u){var a=t.updateQueue;Vr=!1;var c=a.firstBaseUpdate,p=a.lastBaseUpdate,g=a.shared.pending;if(g!==null){a.shared.pending=null;var y=g,S=y.next;y.next=null,p===null?c=S:p.next=S,p=y;var N=t.alternate;N!==null&&(N=N.updateQueue,g=N.lastBaseUpdate,g!==p&&(g===null?N.firstBaseUpdate=S:g.next=S,N.lastBaseUpdate=y))}if(c!==null){var W=a.baseState;p=0,N=S=y=null,g=c;do{var O=g.lane,U=g.eventTime;if((u&O)===O){N!==null&&(N=N.next={eventTime:U,lane:0,tag:g.tag,payload:g.payload,callback:g.callback,next:null});e:{var B=t,oe=g;switch(O=n,U=i,oe.tag){case 1:if(B=oe.payload,typeof B=="function"){W=B.call(U,W,O);break e}W=B;break e;case 3:B.flags=B.flags&-65537|128;case 0:if(B=oe.payload,O=typeof B=="function"?B.call(U,W,O):B,O==null)break e;W=rt({},W,O);break e;case 2:Vr=!0}}g.callback!==null&&g.lane!==0&&(t.flags|=64,O=a.effects,O===null?a.effects=[g]:O.push(g))}else U={eventTime:U,lane:O,tag:g.tag,payload:g.payload,callback:g.callback,next:null},N===null?(S=N=U,y=W):N=N.next=U,p|=O;if(g=g.next,g===null){if(g=a.shared.pending,g===null)break;O=g,g=O.next,O.next=null,a.lastBaseUpdate=O,a.shared.pending=null}}while(1);if(N===null&&(y=W),a.baseState=y,a.firstBaseUpdate=S,a.lastBaseUpdate=N,n=a.shared.interleaved,n!==null){a=n;do p|=a.lane,a=a.next;while(a!==n)}else c===null&&(a.shared.lanes=0);Oi|=p,t.lanes=p,t.memoizedState=W}}function sv(t,n,i){if(t=n.effects,n.effects=null,t!==null)for(n=0;n<t.length;n++){var u=t[n],a=u.callback;if(a!==null){if(u.callback=null,u=i,typeof a!="function")throw Error(q(191,a));a.call(u)}}}var Yg=new Xv.Component().refs;function hc(t,n,i,u){n=t.memoizedState,i=i(u,n),i=i==null?n:rt({},n,i),t.memoizedState=i,t.lanes===0&&(t.updateQueue.baseState=i)}var ra={isMounted:function(t){return(t=t._reactInternals)?Ii(t)===t:!1},enqueueSetState:function(t,n,i){t=t._reactInternals;var u=qt(),a=ii(t),c=Nr(u,a);c.payload=n,i!=null&&(c.callback=i),ni(t,c),n=_n(t,a,u),n!==null&&du(n,t,a)},enqueueReplaceState:function(t,n,i){t=t._reactInternals;var u=qt(),a=ii(t),c=Nr(u,a);c.tag=1,c.payload=n,i!=null&&(c.callback=i),ni(t,c),n=_n(t,a,u),n!==null&&du(n,t,a)},enqueueForceUpdate:function(t,n){t=t._reactInternals;var i=qt(),u=ii(t),a=Nr(i,u);a.tag=2,n!=null&&(a.callback=n),ni(t,a),n=_n(t,u,i),n!==null&&du(n,t,u)}};function uv(t,n,i,u,a,c,p){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(u,c,p):n.prototype&&n.prototype.isPureReactComponent?!Ko(i,u)||!Ko(a,c):!0}function qg(t,n,i){var u=!1,a=oi,c=n.contextType;return typeof c=="object"&&c!==null?c=On(c):(a=an(n)?bi:Ut.current,u=n.contextTypes,c=(u=u!=null)?Ll(t,a):oi),n=new n(i,c),t.memoizedState=n.state!==null&&n.state!==void 0?n.state:null,n.updater=ra,t.stateNode=n,n._reactInternals=t,u&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=a,t.__reactInternalMemoizedMaskedChildContext=c),n}function av(t,n,i,u){t=n.state,typeof n.componentWillReceiveProps=="function"&&n.componentWillReceiveProps(i,u),typeof n.UNSAFE_componentWillReceiveProps=="function"&&n.UNSAFE_componentWillReceiveProps(i,u),n.state!==t&&ra.enqueueReplaceState(n,n.state,null)}function pc(t,n,i,u){var a=t.stateNode;a.props=i,a.state=t.memoizedState,a.refs=Yg,td(t);var c=n.contextType;typeof c=="object"&&c!==null?a.context=On(c):(c=an(n)?bi:Ut.current,a.context=Ll(t,c)),a.state=t.memoizedState,c=n.getDerivedStateFromProps,typeof c=="function"&&(hc(t,n,c,i),a.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof a.getSnapshotBeforeUpdate=="function"||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(n=a.state,typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount(),n!==a.state&&ra.enqueueReplaceState(a,a.state,null),Fu(t,i,a,u),a.state=t.memoizedState),typeof a.componentDidMount=="function"&&(t.flags|=4194308)}var pl=[],vl=0,Ru=null,Wu=0,En=[],Nn=0,_i=null,Lr=1,Er="";function Ci(t,n){pl[vl++]=Wu,pl[vl++]=Ru,Ru=t,Wu=n}function Xg(t,n,i){En[Nn++]=Lr,En[Nn++]=Er,En[Nn++]=_i,_i=t;var u=Lr;t=Er;var a=32-Kn(u)-1;u&=~(1<<a),i+=1;var c=32-Kn(n)+a;if(30<c){var p=a-a%5;c=(u&(1<<p)-1).toString(32),u>>=p,a-=p,Lr=1<<32-Kn(n)+a|i<<a|u,Er=c+t}else Lr=1<<c|i<<a|u,Er=t}function nd(t){t.return!==null&&(Ci(t,1),Xg(t,1,0))}function rd(t){for(;t===Ru;)Ru=pl[--vl],pl[vl]=null,Wu=pl[--vl],pl[vl]=null;for(;t===_i;)_i=En[--Nn],En[Nn]=null,Er=En[--Nn],En[Nn]=null,Lr=En[--Nn],En[Nn]=null}var gn=null,on=null,qe=!1,Un=null;function Zg(t,n){var i=Mn(5,null,null,0);i.elementType="DELETED",i.stateNode=n,i.return=t,n=t.deletions,n===null?(t.deletions=[i],t.flags|=16):n.push(i)}function fv(t,n){switch(t.tag){case 5:var i=t.type;return n=n.nodeType!==1||i.toLowerCase()!==n.nodeName.toLowerCase()?null:n,n!==null?(t.stateNode=n,gn=t,on=Tr(n.firstChild),!0):!1;case 6:return n=t.pendingProps===""||n.nodeType!==3?null:n,n!==null?(t.stateNode=n,gn=t,on=null,!0):!1;case 13:return n=n.nodeType!==8?null:n,n!==null?(i=_i!==null?{id:Lr,overflow:Er}:null,t.memoizedState={dehydrated:n,treeContext:i,retryLane:1073741824},i=Mn(18,null,null,0),i.stateNode=n,i.return=t,t.child=i,gn=t,on=null,!0):!1;default:return!1}}function vc(t){return(t.mode&1)!==0&&(t.flags&128)===0}function gc(t){if(qe){var n=on;if(n){var i=n;if(!fv(t,n)){if(vc(t))throw Error(q(418));n=Tr(i.nextSibling);var u=gn;n&&fv(t,n)?Zg(u,i):(t.flags=t.flags&-4097|2,qe=!1,gn=t)}}else{if(vc(t))throw Error(q(418));t.flags=t.flags&-4097|2,qe=!1,gn=t}}}function cv(t){for(t=t.return;t!==null&&t.tag!==5&&t.tag!==3&&t.tag!==13;)t=t.return;gn=t}function So(t){if(t!==gn)return!1;if(!qe)return cv(t),qe=!0,!1;var n;if((n=t.tag!==3)&&!(n=t.tag!==5)&&(n=t.type,n=n!=="head"&&n!=="body"&&!uc(t.type,t.memoizedProps)),n&&(n=on)){if(vc(t)){for(t=on;t;)t=Tr(t.nextSibling);throw Error(q(418))}for(;n;)Zg(t,n),n=Tr(n.nextSibling)}if(cv(t),t.tag===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(q(317));e:{for(t=t.nextSibling,n=0;t;){if(t.nodeType===8){var i=t.data;if(i==="/$"){if(n===0){on=Tr(t.nextSibling);break e}n--}else i!=="$"&&i!=="$!"&&i!=="$?"||n++}t=t.nextSibling}on=null}}else on=gn?Tr(t.stateNode.nextSibling):null;return!0}function El(){on=gn=null,qe=!1}function id(t){Un===null?Un=[t]:Un.push(t)}function ko(t,n,i){if(t=i.ref,t!==null&&typeof t!="function"&&typeof t!="object"){if(i._owner){if(i=i._owner,i){if(i.tag!==1)throw Error(q(309));var u=i.stateNode}if(!u)throw Error(q(147,t));var a=u,c=""+t;return n!==null&&n.ref!==null&&typeof n.ref=="function"&&n.ref._stringRef===c?n.ref:(n=function(p){var g=a.refs;g===Yg&&(g=a.refs={}),p===null?delete g[c]:g[c]=p},n._stringRef=c,n)}if(typeof t!="string")throw Error(q(284));if(!i._owner)throw Error(q(290,t))}return t}function eu(t,n){throw t=Object.prototype.toString.call(n),Error(q(31,t==="[object Object]"?"object with keys {"+Object.keys(n).join(", ")+"}":t))}function dv(t){var n=t._init;return n(t._payload)}function Jg(t){function n(_,C){if(t){var D=_.deletions;D===null?(_.deletions=[C],_.flags|=16):D.push(C)}}function i(_,C){if(!t)return null;for(;C!==null;)n(_,C),C=C.sibling;return null}function u(_,C){for(_=new Map;C!==null;)C.key!==null?_.set(C.key,C):_.set(C.index,C),C=C.sibling;return _}function a(_,C){return _=si(_,C),_.index=0,_.sibling=null,_}function c(_,C,D){return _.index=D,t?(D=_.alternate,D!==null?(D=D.index,D<C?(_.flags|=2,C):D):(_.flags|=2,C)):(_.flags|=1048576,C)}function p(_){return t&&_.alternate===null&&(_.flags|=2),_}function g(_,C,D,$){return C===null||C.tag!==6?(C=Of(D,_.mode,$),C.return=_,C):(C=a(C,D),C.return=_,C)}function y(_,C,D,$){var Y=D.type;return Y===sl?N(_,C,D.props.children,$,D.key):C!==null&&(C.elementType===Y||typeof Y=="object"&&Y!==null&&Y.$$typeof===Qr&&dv(Y)===C.type)?($=a(C,D.props),$.ref=ko(_,C,D),$.return=_,$):($=mu(D.type,D.key,D.props,null,_.mode,$),$.ref=ko(_,C,D),$.return=_,$)}function S(_,C,D,$){return C===null||C.tag!==4||C.stateNode.containerInfo!==D.containerInfo||C.stateNode.implementation!==D.implementation?(C=Df(D,_.mode,$),C.return=_,C):(C=a(C,D.children||[]),C.return=_,C)}function N(_,C,D,$,Y){return C===null||C.tag!==7?(C=Mi(D,_.mode,$,Y),C.return=_,C):(C=a(C,D),C.return=_,C)}function W(_,C,D){if(typeof C=="string"&&C!==""||typeof C=="number")return C=Of(""+C,_.mode,D),C.return=_,C;if(typeof C=="object"&&C!==null){switch(C.$$typeof){case $s:return D=mu(C.type,C.key,C.props,null,_.mode,D),D.ref=ko(_,null,C),D.return=_,D;case ol:return C=Df(C,_.mode,D),C.return=_,C;case Qr:var $=C._init;return W(_,$(C._payload),D)}if(Eo(C)||go(C))return C=Mi(C,_.mode,D,null),C.return=_,C;eu(_,C)}return null}function O(_,C,D,$){var Y=C!==null?C.key:null;if(typeof D=="string"&&D!==""||typeof D=="number")return Y!==null?null:g(_,C,""+D,$);if(typeof D=="object"&&D!==null){switch(D.$$typeof){case $s:return D.key===Y?y(_,C,D,$):null;case ol:return D.key===Y?S(_,C,D,$):null;case Qr:return Y=D._init,O(_,C,Y(D._payload),$)}if(Eo(D)||go(D))return Y!==null?null:N(_,C,D,$,null);eu(_,D)}return null}function U(_,C,D,$,Y){if(typeof $=="string"&&$!==""||typeof $=="number")return _=_.get(D)||null,g(C,_,""+$,Y);if(typeof $=="object"&&$!==null){switch($.$$typeof){case $s:return _=_.get($.key===null?D:$.key)||null,y(C,_,$,Y);case ol:return _=_.get($.key===null?D:$.key)||null,S(C,_,$,Y);case Qr:var ie=$._init;return U(_,C,D,ie($._payload),Y)}if(Eo($)||go($))return _=_.get(D)||null,N(C,_,$,Y,null);eu(C,$)}return null}function B(_,C,D,$){for(var Y=null,ie=null,I=C,Z=C=0,ke=null;I!==null&&Z<D.length;Z++){I.index>Z?(ke=I,I=null):ke=I.sibling;var me=O(_,I,D[Z],$);if(me===null){I===null&&(I=ke);break}t&&I&&me.alternate===null&&n(_,I),C=c(me,C,Z),ie===null?Y=me:ie.sibling=me,ie=me,I=ke}if(Z===D.length)return i(_,I),qe&&Ci(_,Z),Y;if(I===null){for(;Z<D.length;Z++)I=W(_,D[Z],$),I!==null&&(C=c(I,C,Z),ie===null?Y=I:ie.sibling=I,ie=I);return qe&&Ci(_,Z),Y}for(I=u(_,I);Z<D.length;Z++)ke=U(I,_,Z,D[Z],$),ke!==null&&(t&&ke.alternate!==null&&I.delete(ke.key===null?Z:ke.key),C=c(ke,C,Z),ie===null?Y=ke:ie.sibling=ke,ie=ke);return t&&I.forEach(function(Re){return n(_,Re)}),qe&&Ci(_,Z),Y}function oe(_,C,D,$){var Y=go(D);if(typeof Y!="function")throw Error(q(150));if(D=Y.call(D),D==null)throw Error(q(151));for(var ie=Y=null,I=C,Z=C=0,ke=null,me=D.next();I!==null&&!me.done;Z++,me=D.next()){I.index>Z?(ke=I,I=null):ke=I.sibling;var Re=O(_,I,me.value,$);if(Re===null){I===null&&(I=ke);break}t&&I&&Re.alternate===null&&n(_,I),C=c(Re,C,Z),ie===null?Y=Re:ie.sibling=Re,ie=Re,I=ke}if(me.done)return i(_,I),qe&&Ci(_,Z),Y;if(I===null){for(;!me.done;Z++,me=D.next())me=W(_,me.value,$),me!==null&&(C=c(me,C,Z),ie===null?Y=me:ie.sibling=me,ie=me);return qe&&Ci(_,Z),Y}for(I=u(_,I);!me.done;Z++,me=D.next())me=U(I,_,Z,me.value,$),me!==null&&(t&&me.alternate!==null&&I.delete(me.key===null?Z:me.key),C=c(me,C,Z),ie===null?Y=me:ie.sibling=me,ie=me);return t&&I.forEach(function(K){return n(_,K)}),qe&&Ci(_,Z),Y}function G(_,C,D,$){if(typeof D=="object"&&D!==null&&D.type===sl&&D.key===null&&(D=D.props.children),typeof D=="object"&&D!==null){switch(D.$$typeof){case $s:e:{for(var Y=D.key,ie=C;ie!==null;){if(ie.key===Y){if(Y=D.type,Y===sl){if(ie.tag===7){i(_,ie.sibling),C=a(ie,D.props.children),C.return=_,_=C;break e}}else if(ie.elementType===Y||typeof Y=="object"&&Y!==null&&Y.$$typeof===Qr&&dv(Y)===ie.type){i(_,ie.sibling),C=a(ie,D.props),C.ref=ko(_,ie,D),C.return=_,_=C;break e}i(_,ie);break}else n(_,ie);ie=ie.sibling}D.type===sl?(C=Mi(D.props.children,_.mode,$,D.key),C.return=_,_=C):($=mu(D.type,D.key,D.props,null,_.mode,$),$.ref=ko(_,C,D),$.return=_,_=$)}return p(_);case ol:e:{for(ie=D.key;C!==null;){if(C.key===ie)if(C.tag===4&&C.stateNode.containerInfo===D.containerInfo&&C.stateNode.implementation===D.implementation){i(_,C.sibling),C=a(C,D.children||[]),C.return=_,_=C;break e}else{i(_,C);break}else n(_,C);C=C.sibling}C=Df(D,_.mode,$),C.return=_,_=C}return p(_);case Qr:return ie=D._init,G(_,C,ie(D._payload),$)}if(Eo(D))return B(_,C,D,$);if(go(D))return oe(_,C,D,$);eu(_,D)}return typeof D=="string"&&D!==""||typeof D=="number"?(D=""+D,C!==null&&C.tag===6?(i(_,C.sibling),C=a(C,D),C.return=_,_=C):(i(_,C),C=Of(D,_.mode,$),C.return=_,_=C),p(_)):i(_,C)}return G}var Nl=Jg(!0),e0=Jg(!1),ls={},or=ai(ls),Yo=ai(ls),qo=ai(ls);function Ei(t){if(t===ls)throw Error(q(174));return t}function ld(t,n){switch($e(qo,n),$e(Yo,t),$e(or,ls),t=n.nodeType,t){case 9:case 11:n=(n=n.documentElement)?n.namespaceURI:Vf(null,"");break;default:t=t===8?n.parentNode:n,n=t.namespaceURI||null,t=t.tagName,n=Vf(n,t)}Qe(or),$e(or,n)}function Ml(){Qe(or),Qe(Yo),Qe(qo)}function t0(t){Ei(qo.current);var n=Ei(or.current),i=Vf(n,t.type);n!==i&&($e(Yo,t),$e(or,i))}function od(t){Yo.current===t&&(Qe(or),Qe(Yo))}var et=ai(0);function Hu(t){for(var n=t;n!==null;){if(n.tag===13){var i=n.memoizedState;if(i!==null&&(i=i.dehydrated,i===null||i.data==="$?"||i.data==="$!"))return n}else if(n.tag===19&&n.memoizedProps.revealOrder!==void 0){if(n.flags&128)return n}else if(n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return null;n=n.return}n.sibling.return=n.return,n=n.sibling}return null}var Nf=[];function sd(){for(var t=0;t<Nf.length;t++)Nf[t]._workInProgressVersionPrimary=null;Nf.length=0}var hu=Pr.ReactCurrentDispatcher,Mf=Pr.ReactCurrentBatchConfig,Pi=0,nt=null,St=null,Lt=null,Bu=!1,zo=!1,Xo=0,gx=0;function Ft(){throw Error(q(321))}function ud(t,n){if(n===null)return!1;for(var i=0;i<n.length&&i<t.length;i++)if(!Gn(t[i],n[i]))return!1;return!0}function ad(t,n,i,u,a,c){if(Pi=c,nt=n,n.memoizedState=null,n.updateQueue=null,n.lanes=0,hu.current=t===null||t.memoizedState===null?xx:Sx,t=i(u,a),zo){c=0;do{if(zo=!1,Xo=0,25<=c)throw Error(q(301));c+=1,Lt=St=null,n.updateQueue=null,hu.current=kx,t=i(u,a)}while(zo)}if(hu.current=Uu,n=St!==null&&St.next!==null,Pi=0,Lt=St=nt=null,Bu=!1,n)throw Error(q(300));return t}function fd(){var t=Xo!==0;return Xo=0,t}function rr(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Lt===null?nt.memoizedState=Lt=t:Lt=Lt.next=t,Lt}function Dn(){if(St===null){var t=nt.alternate;t=t!==null?t.memoizedState:null}else t=St.next;var n=Lt===null?nt.memoizedState:Lt.next;if(n!==null)Lt=n,St=t;else{if(t===null)throw Error(q(310));St=t,t={memoizedState:St.memoizedState,baseState:St.baseState,baseQueue:St.baseQueue,queue:St.queue,next:null},Lt===null?nt.memoizedState=Lt=t:Lt=Lt.next=t}return Lt}function Zo(t,n){return typeof n=="function"?n(t):n}function bf(t){var n=Dn(),i=n.queue;if(i===null)throw Error(q(311));i.lastRenderedReducer=t;var u=St,a=u.baseQueue,c=i.pending;if(c!==null){if(a!==null){var p=a.next;a.next=c.next,c.next=p}u.baseQueue=a=c,i.pending=null}if(a!==null){c=a.next,u=u.baseState;var g=p=null,y=null,S=c;do{var N=S.lane;if((Pi&N)===N)y!==null&&(y=y.next={lane:0,action:S.action,hasEagerState:S.hasEagerState,eagerState:S.eagerState,next:null}),u=S.hasEagerState?S.eagerState:t(u,S.action);else{var W={lane:N,action:S.action,hasEagerState:S.hasEagerState,eagerState:S.eagerState,next:null};y===null?(g=y=W,p=u):y=y.next=W,nt.lanes|=N,Oi|=N}S=S.next}while(S!==null&&S!==c);y===null?p=u:y.next=g,Gn(u,n.memoizedState)||(sn=!0),n.memoizedState=u,n.baseState=p,n.baseQueue=y,i.lastRenderedState=u}if(t=i.interleaved,t!==null){a=t;do c=a.lane,nt.lanes|=c,Oi|=c,a=a.next;while(a!==t)}else a===null&&(i.lanes=0);return[n.memoizedState,i.dispatch]}function _f(t){var n=Dn(),i=n.queue;if(i===null)throw Error(q(311));i.lastRenderedReducer=t;var u=i.dispatch,a=i.pending,c=n.memoizedState;if(a!==null){i.pending=null;var p=a=a.next;do c=t(c,p.action),p=p.next;while(p!==a);Gn(c,n.memoizedState)||(sn=!0),n.memoizedState=c,n.baseQueue===null&&(n.baseState=c),i.lastRenderedState=c}return[c,u]}function n0(){}function r0(t,n){var i=nt,u=Dn(),a=n(),c=!Gn(u.memoizedState,a);if(c&&(u.memoizedState=a,sn=!0),u=u.queue,cd(o0.bind(null,i,u,t),[t]),u.getSnapshot!==n||c||Lt!==null&&Lt.memoizedState.tag&1){if(i.flags|=2048,Jo(9,l0.bind(null,i,u,a,n),void 0,null),Ct===null)throw Error(q(349));Pi&30||i0(i,n,a)}return a}function i0(t,n,i){t.flags|=16384,t={getSnapshot:n,value:i},n=nt.updateQueue,n===null?(n={lastEffect:null,stores:null},nt.updateQueue=n,n.stores=[t]):(i=n.stores,i===null?n.stores=[t]:i.push(t))}function l0(t,n,i,u){n.value=i,n.getSnapshot=u,s0(n)&&_n(t,1,-1)}function o0(t,n,i){return i(function(){s0(n)&&_n(t,1,-1)})}function s0(t){var n=t.getSnapshot;t=t.value;try{var i=n();return!Gn(t,i)}catch{return!0}}function hv(t){var n=rr();return typeof t=="function"&&(t=t()),n.memoizedState=n.baseState=t,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Zo,lastRenderedState:t},n.queue=t,t=t.dispatch=wx.bind(null,nt,t),[n.memoizedState,t]}function Jo(t,n,i,u){return t={tag:t,create:n,destroy:i,deps:u,next:null},n=nt.updateQueue,n===null?(n={lastEffect:null,stores:null},nt.updateQueue=n,n.lastEffect=t.next=t):(i=n.lastEffect,i===null?n.lastEffect=t.next=t:(u=i.next,i.next=t,t.next=u,n.lastEffect=t)),t}function u0(){return Dn().memoizedState}function pu(t,n,i,u){var a=rr();nt.flags|=t,a.memoizedState=Jo(1|n,i,void 0,u===void 0?null:u)}function ia(t,n,i,u){var a=Dn();u=u===void 0?null:u;var c=void 0;if(St!==null){var p=St.memoizedState;if(c=p.destroy,u!==null&&ud(u,p.deps)){a.memoizedState=Jo(n,i,c,u);return}}nt.flags|=t,a.memoizedState=Jo(1|n,i,c,u)}function pv(t,n){return pu(8390656,8,t,n)}function cd(t,n){return ia(2048,8,t,n)}function a0(t,n){return ia(4,2,t,n)}function f0(t,n){return ia(4,4,t,n)}function c0(t,n){if(typeof n=="function")return t=t(),n(t),function(){n(null)};if(n!=null)return t=t(),n.current=t,function(){n.current=null}}function d0(t,n,i){return i=i!=null?i.concat([t]):null,ia(4,4,c0.bind(null,n,t),i)}function dd(){}function h0(t,n){var i=Dn();n=n===void 0?null:n;var u=i.memoizedState;return u!==null&&n!==null&&ud(n,u[1])?u[0]:(i.memoizedState=[t,n],t)}function p0(t,n){var i=Dn();n=n===void 0?null:n;var u=i.memoizedState;return u!==null&&n!==null&&ud(n,u[1])?u[0]:(t=t(),i.memoizedState=[t,n],t)}function v0(t,n,i){return Pi&21?(Gn(i,n)||(i=Sg(),nt.lanes|=i,Oi|=i,t.baseState=!0),n):(t.baseState&&(t.baseState=!1,sn=!0),t.memoizedState=i)}function mx(t,n){var i=We;We=i!==0&&4>i?i:4,t(!0);var u=Mf.transition;Mf.transition={};try{t(!1),n()}finally{We=i,Mf.transition=u}}function g0(){return Dn().memoizedState}function yx(t,n,i){var u=ii(t);i={lane:u,action:i,hasEagerState:!1,eagerState:null,next:null},m0(t)?y0(n,i):(w0(t,n,i),i=qt(),t=_n(t,u,i),t!==null&&x0(t,n,u))}function wx(t,n,i){var u=ii(t),a={lane:u,action:i,hasEagerState:!1,eagerState:null,next:null};if(m0(t))y0(n,a);else{w0(t,n,a);var c=t.alternate;if(t.lanes===0&&(c===null||c.lanes===0)&&(c=n.lastRenderedReducer,c!==null))try{var p=n.lastRenderedState,g=c(p,i);if(a.hasEagerState=!0,a.eagerState=g,Gn(g,p))return}catch{}finally{}i=qt(),t=_n(t,u,i),t!==null&&x0(t,n,u)}}function m0(t){var n=t.alternate;return t===nt||n!==null&&n===nt}function y0(t,n){zo=Bu=!0;var i=t.pending;i===null?n.next=n:(n.next=i.next,i.next=n),t.pending=n}function w0(t,n,i){F0(t)?(t=n.interleaved,t===null?(i.next=i,$n===null?$n=[n]:$n.push(n)):(i.next=t.next,t.next=i),n.interleaved=i):(t=n.pending,t===null?i.next=i:(i.next=t.next,t.next=i),n.pending=i)}function x0(t,n,i){if(i&4194240){var u=n.lanes;u&=t.pendingLanes,i|=u,n.lanes=i,$c(t,i)}}var Uu={readContext:On,useCallback:Ft,useContext:Ft,useEffect:Ft,useImperativeHandle:Ft,useInsertionEffect:Ft,useLayoutEffect:Ft,useMemo:Ft,useReducer:Ft,useRef:Ft,useState:Ft,useDebugValue:Ft,useDeferredValue:Ft,useTransition:Ft,useMutableSource:Ft,useSyncExternalStore:Ft,useId:Ft,unstable_isNewReconciler:!1},xx={readContext:On,useCallback:function(t,n){return rr().memoizedState=[t,n===void 0?null:n],t},useContext:On,useEffect:pv,useImperativeHandle:function(t,n,i){return i=i!=null?i.concat([t]):null,pu(4194308,4,c0.bind(null,n,t),i)},useLayoutEffect:function(t,n){return pu(4194308,4,t,n)},useInsertionEffect:function(t,n){return pu(4,2,t,n)},useMemo:function(t,n){var i=rr();return n=n===void 0?null:n,t=t(),i.memoizedState=[t,n],t},useReducer:function(t,n,i){var u=rr();return n=i!==void 0?i(n):n,u.memoizedState=u.baseState=n,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:n},u.queue=t,t=t.dispatch=yx.bind(null,nt,t),[u.memoizedState,t]},useRef:function(t){var n=rr();return t={current:t},n.memoizedState=t},useState:hv,useDebugValue:dd,useDeferredValue:function(t){return rr().memoizedState=t},useTransition:function(){var t=hv(!1),n=t[0];return t=mx.bind(null,t[1]),rr().memoizedState=t,[n,t]},useMutableSource:function(){},useSyncExternalStore:function(t,n,i){var u=nt,a=rr();if(qe){if(i===void 0)throw Error(q(407));i=i()}else{if(i=n(),Ct===null)throw Error(q(349));Pi&30||i0(u,n,i)}a.memoizedState=i;var c={value:i,getSnapshot:n};return a.queue=c,pv(o0.bind(null,u,c,t),[t]),u.flags|=2048,Jo(9,l0.bind(null,u,c,i,n),void 0,null),i},useId:function(){var t=rr(),n=Ct.identifierPrefix;if(qe){var i=Er,u=Lr;i=(u&~(1<<32-Kn(u)-1)).toString(32)+i,n=":"+n+"R"+i,i=Xo++,0<i&&(n+="H"+i.toString(32)),n+=":"}else i=gx++,n=":"+n+"r"+i.toString(32)+":";return t.memoizedState=n},unstable_isNewReconciler:!1},Sx={readContext:On,useCallback:h0,useContext:On,useEffect:cd,useImperativeHandle:d0,useInsertionEffect:a0,useLayoutEffect:f0,useMemo:p0,useReducer:bf,useRef:u0,useState:function(){return bf(Zo)},useDebugValue:dd,useDeferredValue:function(t){var n=Dn();return v0(n,St.memoizedState,t)},useTransition:function(){var t=bf(Zo)[0],n=Dn().memoizedState;return[t,n]},useMutableSource:n0,useSyncExternalStore:r0,useId:g0,unstable_isNewReconciler:!1},kx={readContext:On,useCallback:h0,useContext:On,useEffect:cd,useImperativeHandle:d0,useInsertionEffect:a0,useLayoutEffect:f0,useMemo:p0,useReducer:_f,useRef:u0,useState:function(){return _f(Zo)},useDebugValue:dd,useDeferredValue:function(t){var n=Dn();return St===null?n.memoizedState=t:v0(n,St.memoizedState,t)},useTransition:function(){var t=_f(Zo)[0],n=Dn().memoizedState;return[t,n]},useMutableSource:n0,useSyncExternalStore:r0,useId:g0,unstable_isNewReconciler:!1};function hd(t,n){try{var i="",u=n;do i+=q1(u),u=u.return;while(u);var a=i}catch(c){a=`
Error generating stack: `+c.message+`
`+c.stack}return{value:t,source:n,stack:a}}function mc(t,n){try{console.error(n.value)}catch(i){setTimeout(function(){throw i})}}var Cx=typeof WeakMap=="function"?WeakMap:Map;function S0(t,n,i){i=Nr(-1,i),i.tag=3,i.payload={element:null};var u=n.value;return i.callback=function(){$u||($u=!0,Ec=u),mc(t,n)},i}function k0(t,n,i){i=Nr(-1,i),i.tag=3;var u=t.type.getDerivedStateFromError;if(typeof u=="function"){var a=n.value;i.payload=function(){return u(a)},i.callback=function(){mc(t,n)}}var c=t.stateNode;return c!==null&&typeof c.componentDidCatch=="function"&&(i.callback=function(){mc(t,n),typeof u!="function"&&(ri===null?ri=new Set([this]):ri.add(this));var p=n.stack;this.componentDidCatch(n.value,{componentStack:p!==null?p:""})}),i}function vv(t,n,i){var u=t.pingCache;if(u===null){u=t.pingCache=new Cx;var a=new Set;u.set(n,a)}else a=u.get(n),a===void 0&&(a=new Set,u.set(n,a));a.has(i)||(a.add(i),t=Ax.bind(null,t,n,i),n.then(t,t))}function gv(t){do{var n;if((n=t.tag===13)&&(n=t.memoizedState,n=n!==null?n.dehydrated!==null:!0),n)return t;t=t.return}while(t!==null);return null}function mv(t,n,i,u,a){return t.mode&1?(t.flags|=65536,t.lanes=a,t):(t===n?t.flags|=65536:(t.flags|=128,i.flags|=131072,i.flags&=-52805,i.tag===1&&(i.alternate===null?i.tag=17:(n=Nr(-1,1),n.tag=2,ni(i,n))),i.lanes|=1),t)}var C0,yc,T0,L0;C0=function(t,n){for(var i=n.child;i!==null;){if(i.tag===5||i.tag===6)t.appendChild(i.stateNode);else if(i.tag!==4&&i.child!==null){i.child.return=i,i=i.child;continue}if(i===n)break;for(;i.sibling===null;){if(i.return===null||i.return===n)return;i=i.return}i.sibling.return=i.return,i=i.sibling}};yc=function(){};T0=function(t,n,i,u){var a=t.memoizedProps;if(a!==u){t=n.stateNode,Ei(or.current);var c=null;switch(i){case"input":a=$f(t,a),u=$f(t,u),c=[];break;case"select":a=rt({},a,{value:void 0}),u=rt({},u,{value:void 0}),c=[];break;case"textarea":a=Qf(t,a),u=Qf(t,u),c=[];break;default:typeof a.onClick!="function"&&typeof u.onClick=="function"&&(t.onclick=Ou)}Yf(i,u);var p;i=null;for(S in a)if(!u.hasOwnProperty(S)&&a.hasOwnProperty(S)&&a[S]!=null)if(S==="style"){var g=a[S];for(p in g)g.hasOwnProperty(p)&&(i||(i={}),i[p]="")}else S!=="dangerouslySetInnerHTML"&&S!=="children"&&S!=="suppressContentEditableWarning"&&S!=="suppressHydrationWarning"&&S!=="autoFocus"&&(Ro.hasOwnProperty(S)?c||(c=[]):(c=c||[]).push(S,null));for(S in u){var y=u[S];if(g=a!=null?a[S]:void 0,u.hasOwnProperty(S)&&y!==g&&(y!=null||g!=null))if(S==="style")if(g){for(p in g)!g.hasOwnProperty(p)||y&&y.hasOwnProperty(p)||(i||(i={}),i[p]="");for(p in y)y.hasOwnProperty(p)&&g[p]!==y[p]&&(i||(i={}),i[p]=y[p])}else i||(c||(c=[]),c.push(S,i)),i=y;else S==="dangerouslySetInnerHTML"?(y=y?y.__html:void 0,g=g?g.__html:void 0,y!=null&&g!==y&&(c=c||[]).push(S,y)):S==="children"?typeof y!="string"&&typeof y!="number"||(c=c||[]).push(S,""+y):S!=="suppressContentEditableWarning"&&S!=="suppressHydrationWarning"&&(Ro.hasOwnProperty(S)?(y!=null&&S==="onScroll"&&Ge("scroll",t),c||g===y||(c=[])):(c=c||[]).push(S,y))}i&&(c=c||[]).push("style",i);var S=c;(n.updateQueue=S)&&(n.flags|=4)}};L0=function(t,n,i,u){i!==u&&(n.flags|=4)};function Co(t,n){if(!qe)switch(t.tailMode){case"hidden":n=t.tail;for(var i=null;n!==null;)n.alternate!==null&&(i=n),n=n.sibling;i===null?t.tail=null:i.sibling=null;break;case"collapsed":i=t.tail;for(var u=null;i!==null;)i.alternate!==null&&(u=i),i=i.sibling;u===null?n||t.tail===null?t.tail=null:t.tail.sibling=null:u.sibling=null}}function Rt(t){var n=t.alternate!==null&&t.alternate.child===t.child,i=0,u=0;if(n)for(var a=t.child;a!==null;)i|=a.lanes|a.childLanes,u|=a.subtreeFlags&14680064,u|=a.flags&14680064,a.return=t,a=a.sibling;else for(a=t.child;a!==null;)i|=a.lanes|a.childLanes,u|=a.subtreeFlags,u|=a.flags,a.return=t,a=a.sibling;return t.subtreeFlags|=u,t.childLanes=i,n}function Tx(t,n,i){var u=n.pendingProps;switch(rd(n),n.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Rt(n),null;case 1:return an(n.type)&&Du(),Rt(n),null;case 3:return u=n.stateNode,Ml(),Qe(un),Qe(Ut),sd(),u.pendingContext&&(u.context=u.pendingContext,u.pendingContext=null),(t===null||t.child===null)&&(So(n)?n.flags|=4:t===null||t.memoizedState.isDehydrated&&!(n.flags&256)||(n.flags|=1024,Un!==null&&(bc(Un),Un=null))),yc(t,n),Rt(n),null;case 5:od(n);var a=Ei(qo.current);if(i=n.type,t!==null&&n.stateNode!=null)T0(t,n,i,u,a),t.ref!==n.ref&&(n.flags|=512,n.flags|=2097152);else{if(!u){if(n.stateNode===null)throw Error(q(166));return Rt(n),null}if(t=Ei(or.current),So(n)){u=n.stateNode,i=n.type;var c=n.memoizedProps;switch(u[ir]=n,u[Vo]=c,t=(n.mode&1)!==0,i){case"dialog":Ge("cancel",u),Ge("close",u);break;case"iframe":case"object":case"embed":Ge("load",u);break;case"video":case"audio":for(a=0;a<Mo.length;a++)Ge(Mo[a],u);break;case"source":Ge("error",u);break;case"img":case"image":case"link":Ge("error",u),Ge("load",u);break;case"details":Ge("toggle",u);break;case"input":Pp(u,c),Ge("invalid",u);break;case"select":u._wrapperState={wasMultiple:!!c.multiple},Ge("invalid",u);break;case"textarea":Dp(u,c),Ge("invalid",u)}Yf(i,c),a=null;for(var p in c)if(c.hasOwnProperty(p)){var g=c[p];p==="children"?typeof g=="string"?u.textContent!==g&&(c.suppressHydrationWarning!==!0&&Js(u.textContent,g,t),a=["children",g]):typeof g=="number"&&u.textContent!==""+g&&(c.suppressHydrationWarning!==!0&&Js(u.textContent,g,t),a=["children",""+g]):Ro.hasOwnProperty(p)&&g!=null&&p==="onScroll"&&Ge("scroll",u)}switch(i){case"input":Ks(u),Op(u,c,!0);break;case"textarea":Ks(u),zp(u);break;case"select":case"option":break;default:typeof c.onClick=="function"&&(u.onclick=Ou)}u=a,n.updateQueue=u,u!==null&&(n.flags|=4)}else{p=a.nodeType===9?a:a.ownerDocument,t==="http://www.w3.org/1999/xhtml"&&(t=og(i)),t==="http://www.w3.org/1999/xhtml"?i==="script"?(t=p.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild)):typeof u.is=="string"?t=p.createElement(i,{is:u.is}):(t=p.createElement(i),i==="select"&&(p=t,u.multiple?p.multiple=!0:u.size&&(p.size=u.size))):t=p.createElementNS(t,i),t[ir]=n,t[Vo]=u,C0(t,n,!1,!1),n.stateNode=t;e:{switch(p=qf(i,u),i){case"dialog":Ge("cancel",t),Ge("close",t),a=u;break;case"iframe":case"object":case"embed":Ge("load",t),a=u;break;case"video":case"audio":for(a=0;a<Mo.length;a++)Ge(Mo[a],t);a=u;break;case"source":Ge("error",t),a=u;break;case"img":case"image":case"link":Ge("error",t),Ge("load",t),a=u;break;case"details":Ge("toggle",t),a=u;break;case"input":Pp(t,u),a=$f(t,u),Ge("invalid",t);break;case"option":a=u;break;case"select":t._wrapperState={wasMultiple:!!u.multiple},a=rt({},u,{value:void 0}),Ge("invalid",t);break;case"textarea":Dp(t,u),a=Qf(t,u),Ge("invalid",t);break;default:a=u}Yf(i,a),g=a;for(c in g)if(g.hasOwnProperty(c)){var y=g[c];c==="style"?ag(t,y):c==="dangerouslySetInnerHTML"?(y=y?y.__html:void 0,y!=null&&sg(t,y)):c==="children"?typeof y=="string"?(i!=="textarea"||y!=="")&&Wo(t,y):typeof y=="number"&&Wo(t,""+y):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(Ro.hasOwnProperty(c)?y!=null&&c==="onScroll"&&Ge("scroll",t):y!=null&&Rc(t,c,y,p))}switch(i){case"input":Ks(t),Op(t,u,!1);break;case"textarea":Ks(t),zp(t);break;case"option":u.value!=null&&t.setAttribute("value",""+li(u.value));break;case"select":t.multiple=!!u.multiple,c=u.value,c!=null?yl(t,!!u.multiple,c,!1):u.defaultValue!=null&&yl(t,!!u.multiple,u.defaultValue,!0);break;default:typeof a.onClick=="function"&&(t.onclick=Ou)}switch(i){case"button":case"input":case"select":case"textarea":u=!!u.autoFocus;break e;case"img":u=!0;break e;default:u=!1}}u&&(n.flags|=4)}n.ref!==null&&(n.flags|=512,n.flags|=2097152)}return Rt(n),null;case 6:if(t&&n.stateNode!=null)L0(t,n,t.memoizedProps,u);else{if(typeof u!="string"&&n.stateNode===null)throw Error(q(166));if(i=Ei(qo.current),Ei(or.current),So(n)){if(u=n.stateNode,i=n.memoizedProps,u[ir]=n,(c=u.nodeValue!==i)&&(t=gn,t!==null))switch(t.tag){case 3:Js(u.nodeValue,i,(t.mode&1)!==0);break;case 5:t.memoizedProps.suppressHydrationWarning!==!0&&Js(u.nodeValue,i,(t.mode&1)!==0)}c&&(n.flags|=4)}else u=(i.nodeType===9?i:i.ownerDocument).createTextNode(u),u[ir]=n,n.stateNode=u}return Rt(n),null;case 13:if(Qe(et),u=n.memoizedState,qe&&on!==null&&n.mode&1&&!(n.flags&128)){for(u=on;u;)u=Tr(u.nextSibling);return El(),n.flags|=98560,n}if(u!==null&&u.dehydrated!==null){if(u=So(n),t===null){if(!u)throw Error(q(318));if(u=n.memoizedState,u=u!==null?u.dehydrated:null,!u)throw Error(q(317));u[ir]=n}else El(),!(n.flags&128)&&(n.memoizedState=null),n.flags|=4;return Rt(n),null}return Un!==null&&(bc(Un),Un=null),n.flags&128?(n.lanes=i,n):(u=u!==null,i=!1,t===null?So(n):i=t.memoizedState!==null,u!==i&&u&&(n.child.flags|=8192,n.mode&1&&(t===null||et.current&1?kt===0&&(kt=3):wd())),n.updateQueue!==null&&(n.flags|=4),Rt(n),null);case 4:return Ml(),yc(t,n),t===null&&Go(n.stateNode.containerInfo),Rt(n),null;case 10:return ed(n.type._context),Rt(n),null;case 17:return an(n.type)&&Du(),Rt(n),null;case 19:if(Qe(et),c=n.memoizedState,c===null)return Rt(n),null;if(u=(n.flags&128)!==0,p=c.rendering,p===null)if(u)Co(c,!1);else{if(kt!==0||t!==null&&t.flags&128)for(t=n.child;t!==null;){if(p=Hu(t),p!==null){for(n.flags|=128,Co(c,!1),u=p.updateQueue,u!==null&&(n.updateQueue=u,n.flags|=4),n.subtreeFlags=0,u=i,i=n.child;i!==null;)c=i,t=u,c.flags&=14680066,p=c.alternate,p===null?(c.childLanes=0,c.lanes=t,c.child=null,c.subtreeFlags=0,c.memoizedProps=null,c.memoizedState=null,c.updateQueue=null,c.dependencies=null,c.stateNode=null):(c.childLanes=p.childLanes,c.lanes=p.lanes,c.child=p.child,c.subtreeFlags=0,c.deletions=null,c.memoizedProps=p.memoizedProps,c.memoizedState=p.memoizedState,c.updateQueue=p.updateQueue,c.type=p.type,t=p.dependencies,c.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),i=i.sibling;return $e(et,et.current&1|2),n.child}t=t.sibling}c.tail!==null&&ut()>bl&&(n.flags|=128,u=!0,Co(c,!1),n.lanes=4194304)}else{if(!u)if(t=Hu(p),t!==null){if(n.flags|=128,u=!0,i=t.updateQueue,i!==null&&(n.updateQueue=i,n.flags|=4),Co(c,!0),c.tail===null&&c.tailMode==="hidden"&&!p.alternate&&!qe)return Rt(n),null}else 2*ut()-c.renderingStartTime>bl&&i!==1073741824&&(n.flags|=128,u=!0,Co(c,!1),n.lanes=4194304);c.isBackwards?(p.sibling=n.child,n.child=p):(i=c.last,i!==null?i.sibling=p:n.child=p,c.last=p)}return c.tail!==null?(n=c.tail,c.rendering=n,c.tail=n.sibling,c.renderingStartTime=ut(),n.sibling=null,i=et.current,$e(et,u?i&1|2:i&1),n):(Rt(n),null);case 22:case 23:return yd(),u=n.memoizedState!==null,t!==null&&t.memoizedState!==null!==u&&(n.flags|=8192),u&&n.mode&1?vn&1073741824&&(Rt(n),n.subtreeFlags&6&&(n.flags|=8192)):Rt(n),null;case 24:return null;case 25:return null}throw Error(q(156,n.tag))}var Lx=Pr.ReactCurrentOwner,sn=!1;function Yt(t,n,i,u){n.child=t===null?e0(n,null,i,u):Nl(n,t.child,i,u)}function yv(t,n,i,u,a){i=i.render;var c=n.ref;return kl(n,a),u=ad(t,n,i,u,c,a),i=fd(),t!==null&&!sn?(n.updateQueue=t.updateQueue,n.flags&=-2053,t.lanes&=~a,_r(t,n,a)):(qe&&i&&nd(n),n.flags|=1,Yt(t,n,u,a),n.child)}function wv(t,n,i,u,a){if(t===null){var c=i.type;return typeof c=="function"&&!xd(c)&&c.defaultProps===void 0&&i.compare===null&&i.defaultProps===void 0?(n.tag=15,n.type=c,E0(t,n,c,u,a)):(t=mu(i.type,null,u,n,n.mode,a),t.ref=n.ref,t.return=n,n.child=t)}if(c=t.child,!(t.lanes&a)){var p=c.memoizedProps;if(i=i.compare,i=i!==null?i:Ko,i(p,u)&&t.ref===n.ref)return _r(t,n,a)}return n.flags|=1,t=si(c,u),t.ref=n.ref,t.return=n,n.child=t}function E0(t,n,i,u,a){if(t!==null){var c=t.memoizedProps;if(Ko(c,u)&&t.ref===n.ref)if(sn=!1,n.pendingProps=u=c,(t.lanes&a)!==0)t.flags&131072&&(sn=!0);else return n.lanes=t.lanes,_r(t,n,a)}return wc(t,n,i,u,a)}function N0(t,n,i){var u=n.pendingProps,a=u.children,c=t!==null?t.memoizedState:null;if(u.mode==="hidden")if(!(n.mode&1))n.memoizedState={baseLanes:0,cachePool:null,transitions:null},$e(ml,vn),vn|=i;else if(i&1073741824)n.memoizedState={baseLanes:0,cachePool:null,transitions:null},u=c!==null?c.baseLanes:i,$e(ml,vn),vn|=u;else return t=c!==null?c.baseLanes|i:i,n.lanes=n.childLanes=1073741824,n.memoizedState={baseLanes:t,cachePool:null,transitions:null},n.updateQueue=null,$e(ml,vn),vn|=t,null;else c!==null?(u=c.baseLanes|i,n.memoizedState=null):u=i,$e(ml,vn),vn|=u;return Yt(t,n,a,i),n.child}function M0(t,n){var i=n.ref;(t===null&&i!==null||t!==null&&t.ref!==i)&&(n.flags|=512,n.flags|=2097152)}function wc(t,n,i,u,a){var c=an(i)?bi:Ut.current;return c=Ll(n,c),kl(n,a),i=ad(t,n,i,u,c,a),u=fd(),t!==null&&!sn?(n.updateQueue=t.updateQueue,n.flags&=-2053,t.lanes&=~a,_r(t,n,a)):(qe&&u&&nd(n),n.flags|=1,Yt(t,n,i,a),n.child)}function xv(t,n,i,u,a){if(an(i)){var c=!0;zu(n)}else c=!1;if(kl(n,a),n.stateNode===null)t!==null&&(t.alternate=null,n.alternate=null,n.flags|=2),qg(n,i,u),pc(n,i,u,a),u=!0;else if(t===null){var p=n.stateNode,g=n.memoizedProps;p.props=g;var y=p.context,S=i.contextType;typeof S=="object"&&S!==null?S=On(S):(S=an(i)?bi:Ut.current,S=Ll(n,S));var N=i.getDerivedStateFromProps,W=typeof N=="function"||typeof p.getSnapshotBeforeUpdate=="function";W||typeof p.UNSAFE_componentWillReceiveProps!="function"&&typeof p.componentWillReceiveProps!="function"||(g!==u||y!==S)&&av(n,p,u,S),Vr=!1;var O=n.memoizedState;p.state=O,Fu(n,u,p,a),y=n.memoizedState,g!==u||O!==y||un.current||Vr?(typeof N=="function"&&(hc(n,i,N,u),y=n.memoizedState),(g=Vr||uv(n,i,g,u,O,y,S))?(W||typeof p.UNSAFE_componentWillMount!="function"&&typeof p.componentWillMount!="function"||(typeof p.componentWillMount=="function"&&p.componentWillMount(),typeof p.UNSAFE_componentWillMount=="function"&&p.UNSAFE_componentWillMount()),typeof p.componentDidMount=="function"&&(n.flags|=4194308)):(typeof p.componentDidMount=="function"&&(n.flags|=4194308),n.memoizedProps=u,n.memoizedState=y),p.props=u,p.state=y,p.context=S,u=g):(typeof p.componentDidMount=="function"&&(n.flags|=4194308),u=!1)}else{p=n.stateNode,Vg(t,n),g=n.memoizedProps,S=n.type===n.elementType?g:Hn(n.type,g),p.props=S,W=n.pendingProps,O=p.context,y=i.contextType,typeof y=="object"&&y!==null?y=On(y):(y=an(i)?bi:Ut.current,y=Ll(n,y));var U=i.getDerivedStateFromProps;(N=typeof U=="function"||typeof p.getSnapshotBeforeUpdate=="function")||typeof p.UNSAFE_componentWillReceiveProps!="function"&&typeof p.componentWillReceiveProps!="function"||(g!==W||O!==y)&&av(n,p,u,y),Vr=!1,O=n.memoizedState,p.state=O,Fu(n,u,p,a);var B=n.memoizedState;g!==W||O!==B||un.current||Vr?(typeof U=="function"&&(hc(n,i,U,u),B=n.memoizedState),(S=Vr||uv(n,i,S,u,O,B,y)||!1)?(N||typeof p.UNSAFE_componentWillUpdate!="function"&&typeof p.componentWillUpdate!="function"||(typeof p.componentWillUpdate=="function"&&p.componentWillUpdate(u,B,y),typeof p.UNSAFE_componentWillUpdate=="function"&&p.UNSAFE_componentWillUpdate(u,B,y)),typeof p.componentDidUpdate=="function"&&(n.flags|=4),typeof p.getSnapshotBeforeUpdate=="function"&&(n.flags|=1024)):(typeof p.componentDidUpdate!="function"||g===t.memoizedProps&&O===t.memoizedState||(n.flags|=4),typeof p.getSnapshotBeforeUpdate!="function"||g===t.memoizedProps&&O===t.memoizedState||(n.flags|=1024),n.memoizedProps=u,n.memoizedState=B),p.props=u,p.state=B,p.context=y,u=S):(typeof p.componentDidUpdate!="function"||g===t.memoizedProps&&O===t.memoizedState||(n.flags|=4),typeof p.getSnapshotBeforeUpdate!="function"||g===t.memoizedProps&&O===t.memoizedState||(n.flags|=1024),u=!1)}return xc(t,n,i,u,c,a)}function xc(t,n,i,u,a,c){M0(t,n);var p=(n.flags&128)!==0;if(!u&&!p)return a&&lv(n,i,!1),_r(t,n,c);u=n.stateNode,Lx.current=n;var g=p&&typeof i.getDerivedStateFromError!="function"?null:u.render();return n.flags|=1,t!==null&&p?(n.child=Nl(n,t.child,null,c),n.child=Nl(n,null,g,c)):Yt(t,n,g,c),n.memoizedState=u.state,a&&lv(n,i,!0),n.child}function b0(t){var n=t.stateNode;n.pendingContext?iv(t,n.pendingContext,n.pendingContext!==n.context):n.context&&iv(t,n.context,!1),ld(t,n.containerInfo)}function Sv(t,n,i,u,a){return El(),id(a),n.flags|=256,Yt(t,n,i,u),n.child}var tu={dehydrated:null,treeContext:null,retryLane:0};function nu(t){return{baseLanes:t,cachePool:null,transitions:null}}function kv(t,n){return{baseLanes:t.baseLanes|n,cachePool:null,transitions:t.transitions}}function _0(t,n,i){var u=n.pendingProps,a=et.current,c=!1,p=(n.flags&128)!==0,g;if((g=p)||(g=t!==null&&t.memoizedState===null?!1:(a&2)!==0),g?(c=!0,n.flags&=-129):(t===null||t.memoizedState!==null)&&(a|=1),$e(et,a&1),t===null)return gc(n),t=n.memoizedState,t!==null&&(t=t.dehydrated,t!==null)?(n.mode&1?t.data==="$!"?n.lanes=8:n.lanes=1073741824:n.lanes=1,null):(a=u.children,t=u.fallback,c?(u=n.mode,c=n.child,a={mode:"hidden",children:a},!(u&1)&&c!==null?(c.childLanes=0,c.pendingProps=a):c=Qu(a,u,0,null),t=Mi(t,u,i,null),c.return=n,t.return=n,c.sibling=t,n.child=c,n.child.memoizedState=nu(i),n.memoizedState=tu,t):Sc(n,a));if(a=t.memoizedState,a!==null){if(g=a.dehydrated,g!==null){if(p)return n.flags&256?(n.flags&=-257,ru(t,n,i,Error(q(422)))):n.memoizedState!==null?(n.child=t.child,n.flags|=128,null):(c=u.fallback,a=n.mode,u=Qu({mode:"visible",children:u.children},a,0,null),c=Mi(c,a,i,null),c.flags|=2,u.return=n,c.return=n,u.sibling=c,n.child=u,n.mode&1&&Nl(n,t.child,null,i),n.child.memoizedState=nu(i),n.memoizedState=tu,c);if(!(n.mode&1))n=ru(t,n,i,null);else if(g.data==="$!")n=ru(t,n,i,Error(q(419)));else if(u=(i&t.childLanes)!==0,sn||u){if(u=Ct,u!==null){switch(i&-i){case 4:c=2;break;case 16:c=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:c=32;break;case 536870912:c=268435456;break;default:c=0}u=c&(u.suspendedLanes|i)?0:c,u!==0&&u!==a.retryLane&&(a.retryLane=u,_n(t,u,-1))}wd(),n=ru(t,n,i,Error(q(421)))}else g.data==="$?"?(n.flags|=128,n.child=t.child,n=Fx.bind(null,t),g._reactRetry=n,n=null):(i=a.treeContext,on=Tr(g.nextSibling),gn=n,qe=!0,Un=null,i!==null&&(En[Nn++]=Lr,En[Nn++]=Er,En[Nn++]=_i,Lr=i.id,Er=i.overflow,_i=n),n=Sc(n,n.pendingProps.children),n.flags|=4096);return n}return c?(u=Tv(t,n,u.children,u.fallback,i),c=n.child,a=t.child.memoizedState,c.memoizedState=a===null?nu(i):kv(a,i),c.childLanes=t.childLanes&~i,n.memoizedState=tu,u):(i=Cv(t,n,u.children,i),n.memoizedState=null,i)}return c?(u=Tv(t,n,u.children,u.fallback,i),c=n.child,a=t.child.memoizedState,c.memoizedState=a===null?nu(i):kv(a,i),c.childLanes=t.childLanes&~i,n.memoizedState=tu,u):(i=Cv(t,n,u.children,i),n.memoizedState=null,i)}function Sc(t,n){return n=Qu({mode:"visible",children:n},t.mode,0,null),n.return=t,t.child=n}function Cv(t,n,i,u){var a=t.child;return t=a.sibling,i=si(a,{mode:"visible",children:i}),!(n.mode&1)&&(i.lanes=u),i.return=n,i.sibling=null,t!==null&&(u=n.deletions,u===null?(n.deletions=[t],n.flags|=16):u.push(t)),n.child=i}function Tv(t,n,i,u,a){var c=n.mode;t=t.child;var p=t.sibling,g={mode:"hidden",children:i};return!(c&1)&&n.child!==t?(i=n.child,i.childLanes=0,i.pendingProps=g,n.deletions=null):(i=si(t,g),i.subtreeFlags=t.subtreeFlags&14680064),p!==null?u=si(p,u):(u=Mi(u,c,a,null),u.flags|=2),u.return=n,i.return=n,i.sibling=u,n.child=i,u}function ru(t,n,i,u){return u!==null&&id(u),Nl(n,t.child,null,i),t=Sc(n,n.pendingProps.children),t.flags|=2,n.memoizedState=null,t}function Lv(t,n,i){t.lanes|=n;var u=t.alternate;u!==null&&(u.lanes|=n),dc(t.return,n,i)}function Pf(t,n,i,u,a){var c=t.memoizedState;c===null?t.memoizedState={isBackwards:n,rendering:null,renderingStartTime:0,last:u,tail:i,tailMode:a}:(c.isBackwards=n,c.rendering=null,c.renderingStartTime=0,c.last=u,c.tail=i,c.tailMode=a)}function P0(t,n,i){var u=n.pendingProps,a=u.revealOrder,c=u.tail;if(Yt(t,n,u.children,i),u=et.current,u&2)u=u&1|2,n.flags|=128;else{if(t!==null&&t.flags&128)e:for(t=n.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Lv(t,i,n);else if(t.tag===19)Lv(t,i,n);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===n)break e;for(;t.sibling===null;){if(t.return===null||t.return===n)break e;t=t.return}t.sibling.return=t.return,t=t.sibling}u&=1}if($e(et,u),!(n.mode&1))n.memoizedState=null;else switch(a){case"forwards":for(i=n.child,a=null;i!==null;)t=i.alternate,t!==null&&Hu(t)===null&&(a=i),i=i.sibling;i=a,i===null?(a=n.child,n.child=null):(a=i.sibling,i.sibling=null),Pf(n,!1,a,i,c);break;case"backwards":for(i=null,a=n.child,n.child=null;a!==null;){if(t=a.alternate,t!==null&&Hu(t)===null){n.child=a;break}t=a.sibling,a.sibling=i,i=a,a=t}Pf(n,!0,i,null,c);break;case"together":Pf(n,!1,null,null,void 0);break;default:n.memoizedState=null}return n.child}function _r(t,n,i){if(t!==null&&(n.dependencies=t.dependencies),Oi|=n.lanes,!(i&n.childLanes))return null;if(t!==null&&n.child!==t.child)throw Error(q(153));if(n.child!==null){for(t=n.child,i=si(t,t.pendingProps),n.child=i,i.return=n;t.sibling!==null;)t=t.sibling,i=i.sibling=si(t,t.pendingProps),i.return=n;i.sibling=null}return n.child}function Ex(t,n,i){switch(n.tag){case 3:b0(n),El();break;case 5:t0(n);break;case 1:an(n.type)&&zu(n);break;case 4:ld(n,n.stateNode.containerInfo);break;case 10:var u=n.type._context,a=n.memoizedProps.value;$e(Iu,u._currentValue),u._currentValue=a;break;case 13:if(u=n.memoizedState,u!==null)return u.dehydrated!==null?($e(et,et.current&1),n.flags|=128,null):i&n.child.childLanes?_0(t,n,i):($e(et,et.current&1),t=_r(t,n,i),t!==null?t.sibling:null);$e(et,et.current&1);break;case 19:if(u=(i&n.childLanes)!==0,t.flags&128){if(u)return P0(t,n,i);n.flags|=128}if(a=n.memoizedState,a!==null&&(a.rendering=null,a.tail=null,a.lastEffect=null),$e(et,et.current),u)break;return null;case 22:case 23:return n.lanes=0,N0(t,n,i)}return _r(t,n,i)}function Nx(t,n){switch(rd(n),n.tag){case 1:return an(n.type)&&Du(),t=n.flags,t&65536?(n.flags=t&-65537|128,n):null;case 3:return Ml(),Qe(un),Qe(Ut),sd(),t=n.flags,t&65536&&!(t&128)?(n.flags=t&-65537|128,n):null;case 5:return od(n),null;case 13:if(Qe(et),t=n.memoizedState,t!==null&&t.dehydrated!==null){if(n.alternate===null)throw Error(q(340));El()}return t=n.flags,t&65536?(n.flags=t&-65537|128,n):null;case 19:return Qe(et),null;case 4:return Ml(),null;case 10:return ed(n.type._context),null;case 22:case 23:return yd(),null;case 24:return null;default:return null}}var iu=!1,Ht=!1,Mx=typeof WeakSet=="function"?WeakSet:Set,ae=null;function gl(t,n){var i=t.ref;if(i!==null)if(typeof i=="function")try{i(null)}catch(u){lt(t,n,u)}else i.current=null}function kc(t,n,i){try{i()}catch(u){lt(t,n,u)}}var Ev=!1;function bx(t,n){if(oc=bu,t=Fg(),Xc(t)){if("selectionStart"in t)var i={start:t.selectionStart,end:t.selectionEnd};else e:{i=(i=t.ownerDocument)&&i.defaultView||window;var u=i.getSelection&&i.getSelection();if(u&&u.rangeCount!==0){i=u.anchorNode;var a=u.anchorOffset,c=u.focusNode;u=u.focusOffset;try{i.nodeType,c.nodeType}catch{i=null;break e}var p=0,g=-1,y=-1,S=0,N=0,W=t,O=null;t:for(;;){for(var U;W!==i||a!==0&&W.nodeType!==3||(g=p+a),W!==c||u!==0&&W.nodeType!==3||(y=p+u),W.nodeType===3&&(p+=W.nodeValue.length),(U=W.firstChild)!==null;)O=W,W=U;for(;;){if(W===t)break t;if(O===i&&++S===a&&(g=p),O===c&&++N===u&&(y=p),(U=W.nextSibling)!==null)break;W=O,O=W.parentNode}W=U}i=g===-1||y===-1?null:{start:g,end:y}}else i=null}i=i||{start:0,end:0}}else i=null;for(sc={focusedElem:t,selectionRange:i},bu=!1,ae=n;ae!==null;)if(n=ae,t=n.child,(n.subtreeFlags&1028)!==0&&t!==null)t.return=n,ae=t;else for(;ae!==null;){n=ae;try{var B=n.alternate;if(n.flags&1024)switch(n.tag){case 0:case 11:case 15:break;case 1:if(B!==null){var oe=B.memoizedProps,G=B.memoizedState,_=n.stateNode,C=_.getSnapshotBeforeUpdate(n.elementType===n.type?oe:Hn(n.type,oe),G);_.__reactInternalSnapshotBeforeUpdate=C}break;case 3:var D=n.stateNode.containerInfo;if(D.nodeType===1)D.textContent="";else if(D.nodeType===9){var $=D.body;$!=null&&($.textContent="")}break;case 5:case 6:case 4:case 17:break;default:throw Error(q(163))}}catch(Y){lt(n,n.return,Y)}if(t=n.sibling,t!==null){t.return=n.return,ae=t;break}ae=n.return}return B=Ev,Ev=!1,B}function Io(t,n,i){var u=n.updateQueue;if(u=u!==null?u.lastEffect:null,u!==null){var a=u=u.next;do{if((a.tag&t)===t){var c=a.destroy;a.destroy=void 0,c!==void 0&&kc(n,i,c)}a=a.next}while(a!==u)}}function la(t,n){if(n=n.updateQueue,n=n!==null?n.lastEffect:null,n!==null){var i=n=n.next;do{if((i.tag&t)===t){var u=i.create;i.destroy=u()}i=i.next}while(i!==n)}}function Cc(t){var n=t.ref;if(n!==null){var i=t.stateNode;switch(t.tag){case 5:t=i;break;default:t=i}typeof n=="function"?n(t):n.current=t}}function O0(t){var n=t.alternate;n!==null&&(t.alternate=null,O0(n)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(n=t.stateNode,n!==null&&(delete n[ir],delete n[Vo],delete n[fc],delete n[dx],delete n[hx])),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}function D0(t){return t.tag===5||t.tag===3||t.tag===4}function Nv(t){e:for(;;){for(;t.sibling===null;){if(t.return===null||D0(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.flags&2||t.child===null||t.tag===4)continue e;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Tc(t,n,i){var u=t.tag;if(u===5||u===6)t=t.stateNode,n?i.nodeType===8?i.parentNode.insertBefore(t,n):i.insertBefore(t,n):(i.nodeType===8?(n=i.parentNode,n.insertBefore(t,i)):(n=i,n.appendChild(t)),i=i._reactRootContainer,i!=null||n.onclick!==null||(n.onclick=Ou));else if(u!==4&&(t=t.child,t!==null))for(Tc(t,n,i),t=t.sibling;t!==null;)Tc(t,n,i),t=t.sibling}function Lc(t,n,i){var u=t.tag;if(u===5||u===6)t=t.stateNode,n?i.insertBefore(t,n):i.appendChild(t);else if(u!==4&&(t=t.child,t!==null))for(Lc(t,n,i),t=t.sibling;t!==null;)Lc(t,n,i),t=t.sibling}var _t=null,Bn=!1;function Gr(t,n,i){for(i=i.child;i!==null;)z0(t,n,i),i=i.sibling}function z0(t,n,i){if(lr&&typeof lr.onCommitFiberUnmount=="function")try{lr.onCommitFiberUnmount(Xu,i)}catch{}switch(i.tag){case 5:Ht||gl(i,n);case 6:var u=_t,a=Bn;_t=null,Gr(t,n,i),_t=u,Bn=a,_t!==null&&(Bn?(t=_t,i=i.stateNode,t.nodeType===8?t.parentNode.removeChild(i):t.removeChild(i)):_t.removeChild(i.stateNode));break;case 18:_t!==null&&(Bn?(t=_t,i=i.stateNode,t.nodeType===8?Lf(t.parentNode,i):t.nodeType===1&&Lf(t,i),jo(t)):Lf(_t,i.stateNode));break;case 4:u=_t,a=Bn,_t=i.stateNode.containerInfo,Bn=!0,Gr(t,n,i),_t=u,Bn=a;break;case 0:case 11:case 14:case 15:if(!Ht&&(u=i.updateQueue,u!==null&&(u=u.lastEffect,u!==null))){a=u=u.next;do{var c=a,p=c.destroy;c=c.tag,p!==void 0&&(c&2||c&4)&&kc(i,n,p),a=a.next}while(a!==u)}Gr(t,n,i);break;case 1:if(!Ht&&(gl(i,n),u=i.stateNode,typeof u.componentWillUnmount=="function"))try{u.props=i.memoizedProps,u.state=i.memoizedState,u.componentWillUnmount()}catch(g){lt(i,n,g)}Gr(t,n,i);break;case 21:Gr(t,n,i);break;case 22:i.mode&1?(Ht=(u=Ht)||i.memoizedState!==null,Gr(t,n,i),Ht=u):Gr(t,n,i);break;default:Gr(t,n,i)}}function Mv(t){var n=t.updateQueue;if(n!==null){t.updateQueue=null;var i=t.stateNode;i===null&&(i=t.stateNode=new Mx),n.forEach(function(u){var a=Rx.bind(null,t,u);i.has(u)||(i.add(u),u.then(a,a))})}}function Wn(t,n){var i=n.deletions;if(i!==null)for(var u=0;u<i.length;u++){var a=i[u];try{var c=t,p=n,g=p;e:for(;g!==null;){switch(g.tag){case 5:_t=g.stateNode,Bn=!1;break e;case 3:_t=g.stateNode.containerInfo,Bn=!0;break e;case 4:_t=g.stateNode.containerInfo,Bn=!0;break e}g=g.return}if(_t===null)throw Error(q(160));z0(c,p,a),_t=null,Bn=!1;var y=a.alternate;y!==null&&(y.return=null),a.return=null}catch(S){lt(a,n,S)}}if(n.subtreeFlags&12854)for(n=n.child;n!==null;)I0(n,t),n=n.sibling}function I0(t,n){var i=t.alternate,u=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:if(Wn(n,t),nr(t),u&4){try{Io(3,t,t.return),la(3,t)}catch(B){lt(t,t.return,B)}try{Io(5,t,t.return)}catch(B){lt(t,t.return,B)}}break;case 1:Wn(n,t),nr(t),u&512&&i!==null&&gl(i,i.return);break;case 5:if(Wn(n,t),nr(t),u&512&&i!==null&&gl(i,i.return),t.flags&32){var a=t.stateNode;try{Wo(a,"")}catch(B){lt(t,t.return,B)}}if(u&4&&(a=t.stateNode,a!=null)){var c=t.memoizedProps,p=i!==null?i.memoizedProps:c,g=t.type,y=t.updateQueue;if(t.updateQueue=null,y!==null)try{g==="input"&&c.type==="radio"&&c.name!=null&&ig(a,c),qf(g,p);var S=qf(g,c);for(p=0;p<y.length;p+=2){var N=y[p],W=y[p+1];N==="style"?ag(a,W):N==="dangerouslySetInnerHTML"?sg(a,W):N==="children"?Wo(a,W):Rc(a,N,W,S)}switch(g){case"input":Kf(a,c);break;case"textarea":lg(a,c);break;case"select":var O=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!c.multiple;var U=c.value;U!=null?yl(a,!!c.multiple,U,!1):O!==!!c.multiple&&(c.defaultValue!=null?yl(a,!!c.multiple,c.defaultValue,!0):yl(a,!!c.multiple,c.multiple?[]:"",!1))}a[Vo]=c}catch(B){lt(t,t.return,B)}}break;case 6:if(Wn(n,t),nr(t),u&4){if(t.stateNode===null)throw Error(q(162));S=t.stateNode,N=t.memoizedProps;try{S.nodeValue=N}catch(B){lt(t,t.return,B)}}break;case 3:if(Wn(n,t),nr(t),u&4&&i!==null&&i.memoizedState.isDehydrated)try{jo(n.containerInfo)}catch(B){lt(t,t.return,B)}break;case 4:Wn(n,t),nr(t);break;case 13:Wn(n,t),nr(t),S=t.child,S.flags&8192&&S.memoizedState!==null&&(S.alternate===null||S.alternate.memoizedState===null)&&(gd=ut()),u&4&&Mv(t);break;case 22:if(S=i!==null&&i.memoizedState!==null,t.mode&1?(Ht=(N=Ht)||S,Wn(n,t),Ht=N):Wn(n,t),nr(t),u&8192){N=t.memoizedState!==null;e:for(W=null,O=t;;){if(O.tag===5){if(W===null){W=O;try{a=O.stateNode,N?(c=a.style,typeof c.setProperty=="function"?c.setProperty("display","none","important"):c.display="none"):(g=O.stateNode,y=O.memoizedProps.style,p=y!=null&&y.hasOwnProperty("display")?y.display:null,g.style.display=ug("display",p))}catch(B){lt(t,t.return,B)}}}else if(O.tag===6){if(W===null)try{O.stateNode.nodeValue=N?"":O.memoizedProps}catch(B){lt(t,t.return,B)}}else if((O.tag!==22&&O.tag!==23||O.memoizedState===null||O===t)&&O.child!==null){O.child.return=O,O=O.child;continue}if(O===t)break e;for(;O.sibling===null;){if(O.return===null||O.return===t)break e;W===O&&(W=null),O=O.return}W===O&&(W=null),O.sibling.return=O.return,O=O.sibling}if(N&&!S&&t.mode&1)for(ae=t,t=t.child;t!==null;){for(S=ae=t;ae!==null;){switch(N=ae,W=N.child,N.tag){case 0:case 11:case 14:case 15:Io(4,N,N.return);break;case 1:if(gl(N,N.return),c=N.stateNode,typeof c.componentWillUnmount=="function"){O=N,U=N.return;try{a=O,c.props=a.memoizedProps,c.state=a.memoizedState,c.componentWillUnmount()}catch(B){lt(O,U,B)}}break;case 5:gl(N,N.return);break;case 22:if(N.memoizedState!==null){_v(S);continue}}W!==null?(W.return=N,ae=W):_v(S)}t=t.sibling}}break;case 19:Wn(n,t),nr(t),u&4&&Mv(t);break;case 21:break;default:Wn(n,t),nr(t)}}function nr(t){var n=t.flags;if(n&2){try{e:{for(var i=t.return;i!==null;){if(D0(i)){var u=i;break e}i=i.return}throw Error(q(160))}switch(u.tag){case 5:var a=u.stateNode;u.flags&32&&(Wo(a,""),u.flags&=-33);var c=Nv(t);Lc(t,c,a);break;case 3:case 4:var p=u.stateNode.containerInfo,g=Nv(t);Tc(t,g,p);break;default:throw Error(q(161))}}catch(y){lt(t,t.return,y)}t.flags&=-3}n&4096&&(t.flags&=-4097)}function _x(t,n,i){ae=t,A0(t)}function A0(t,n,i){for(var u=(t.mode&1)!==0;ae!==null;){var a=ae,c=a.child;if(a.tag===22&&u){var p=a.memoizedState!==null||iu;if(!p){var g=a.alternate,y=g!==null&&g.memoizedState!==null||Ht;g=iu;var S=Ht;if(iu=p,(Ht=y)&&!S)for(ae=a;ae!==null;)p=ae,y=p.child,p.tag===22&&p.memoizedState!==null?Pv(a):y!==null?(y.return=p,ae=y):Pv(a);for(;c!==null;)ae=c,A0(c),c=c.sibling;ae=a,iu=g,Ht=S}bv(t)}else a.subtreeFlags&8772&&c!==null?(c.return=a,ae=c):bv(t)}}function bv(t){for(;ae!==null;){var n=ae;if(n.flags&8772){var i=n.alternate;try{if(n.flags&8772)switch(n.tag){case 0:case 11:case 15:Ht||la(5,n);break;case 1:var u=n.stateNode;if(n.flags&4&&!Ht)if(i===null)u.componentDidMount();else{var a=n.elementType===n.type?i.memoizedProps:Hn(n.type,i.memoizedProps);u.componentDidUpdate(a,i.memoizedState,u.__reactInternalSnapshotBeforeUpdate)}var c=n.updateQueue;c!==null&&sv(n,c,u);break;case 3:var p=n.updateQueue;if(p!==null){if(i=null,n.child!==null)switch(n.child.tag){case 5:i=n.child.stateNode;break;case 1:i=n.child.stateNode}sv(n,p,i)}break;case 5:var g=n.stateNode;if(i===null&&n.flags&4){i=g;var y=n.memoizedProps;switch(n.type){case"button":case"input":case"select":case"textarea":y.autoFocus&&i.focus();break;case"img":y.src&&(i.src=y.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(n.memoizedState===null){var S=n.alternate;if(S!==null){var N=S.memoizedState;if(N!==null){var W=N.dehydrated;W!==null&&jo(W)}}}break;case 19:case 17:case 21:case 22:case 23:break;default:throw Error(q(163))}Ht||n.flags&512&&Cc(n)}catch(O){lt(n,n.return,O)}}if(n===t){ae=null;break}if(i=n.sibling,i!==null){i.return=n.return,ae=i;break}ae=n.return}}function _v(t){for(;ae!==null;){var n=ae;if(n===t){ae=null;break}var i=n.sibling;if(i!==null){i.return=n.return,ae=i;break}ae=n.return}}function Pv(t){for(;ae!==null;){var n=ae;try{switch(n.tag){case 0:case 11:case 15:var i=n.return;try{la(4,n)}catch(y){lt(n,i,y)}break;case 1:var u=n.stateNode;if(typeof u.componentDidMount=="function"){var a=n.return;try{u.componentDidMount()}catch(y){lt(n,a,y)}}var c=n.return;try{Cc(n)}catch(y){lt(n,c,y)}break;case 5:var p=n.return;try{Cc(n)}catch(y){lt(n,p,y)}}}catch(y){lt(n,n.return,y)}if(n===t){ae=null;break}var g=n.sibling;if(g!==null){g.return=n.return,ae=g;break}ae=n.return}}var Px=Math.ceil,ju=Pr.ReactCurrentDispatcher,pd=Pr.ReactCurrentOwner,bn=Pr.ReactCurrentBatchConfig,De=0,Ct=null,pt=null,Pt=0,vn=0,ml=ai(0),kt=0,es=null,Oi=0,oa=0,vd=0,Ao=null,ln=null,gd=0,bl=1/0,kr=null,$u=!1,Ec=null,ri=null,lu=!1,Zr=null,Ku=0,Fo=0,Nc=null,vu=-1,gu=0;function qt(){return De&6?ut():vu!==-1?vu:vu=ut()}function ii(t){return t.mode&1?De&2&&Pt!==0?Pt&-Pt:vx.transition!==null?(gu===0&&(gu=Sg()),gu):(t=We,t!==0||(t=window.event,t=t===void 0?16:Mg(t.type)),t):1}function _n(t,n,i){if(50<Fo)throw Fo=0,Nc=null,Error(q(185));var u=sa(t,n);return u===null?null:(ns(u,n,i),(!(De&2)||u!==Ct)&&(u===Ct&&(!(De&2)&&(oa|=n),kt===4&&qr(u,Pt)),fn(u,i),n===1&&De===0&&!(t.mode&1)&&(bl=ut()+500,na&&fi())),u)}function sa(t,n){t.lanes|=n;var i=t.alternate;for(i!==null&&(i.lanes|=n),i=t,t=t.return;t!==null;)t.childLanes|=n,i=t.alternate,i!==null&&(i.childLanes|=n),i=t,t=t.return;return i.tag===3?i.stateNode:null}function F0(t){return(Ct!==null||$n!==null)&&(t.mode&1)!==0&&(De&2)===0}function fn(t,n){var i=t.callbackNode;vw(t,n);var u=Mu(t,t===Ct?Pt:0);if(u===0)i!==null&&Fp(i),t.callbackNode=null,t.callbackPriority=0;else if(n=u&-u,t.callbackPriority!==n){if(i!=null&&Fp(i),n===1)t.tag===0?px(Ov.bind(null,t)):Qg(Ov.bind(null,t)),fx(function(){De===0&&fi()}),i=null;else{switch(kg(u)){case 1:i=jc;break;case 4:i=wg;break;case 16:i=Nu;break;case 536870912:i=xg;break;default:i=Nu}i=K0(i,R0.bind(null,t))}t.callbackPriority=n,t.callbackNode=i}}function R0(t,n){if(vu=-1,gu=0,De&6)throw Error(q(327));var i=t.callbackNode;if(Cl()&&t.callbackNode!==i)return null;var u=Mu(t,t===Ct?Pt:0);if(u===0)return null;if(u&30||u&t.expiredLanes||n)n=Gu(t,u);else{n=u;var a=De;De|=2;var c=H0();(Ct!==t||Pt!==n)&&(kr=null,bl=ut()+500,Ni(t,n));do try{zx();break}catch(g){W0(t,g)}while(1);Jc(),ju.current=c,De=a,pt!==null?n=0:(Ct=null,Pt=0,n=kt)}if(n!==0){if(n===2&&(a=tc(t),a!==0&&(u=a,n=Mc(t,a))),n===1)throw i=es,Ni(t,0),qr(t,u),fn(t,ut()),i;if(n===6)qr(t,u);else{if(a=t.current.alternate,!(u&30)&&!Ox(a)&&(n=Gu(t,u),n===2&&(c=tc(t),c!==0&&(u=c,n=Mc(t,c))),n===1))throw i=es,Ni(t,0),qr(t,u),fn(t,ut()),i;switch(t.finishedWork=a,t.finishedLanes=u,n){case 0:case 1:throw Error(q(345));case 2:Ti(t,ln,kr);break;case 3:if(qr(t,u),(u&130023424)===u&&(n=gd+500-ut(),10<n)){if(Mu(t,0)!==0)break;if(a=t.suspendedLanes,(a&u)!==u){qt(),t.pingedLanes|=t.suspendedLanes&a;break}t.timeoutHandle=ac(Ti.bind(null,t,ln,kr),n);break}Ti(t,ln,kr);break;case 4:if(qr(t,u),(u&4194240)===u)break;for(n=t.eventTimes,a=-1;0<u;){var p=31-Kn(u);c=1<<p,p=n[p],p>a&&(a=p),u&=~c}if(u=a,u=ut()-u,u=(120>u?120:480>u?480:1080>u?1080:1920>u?1920:3e3>u?3e3:4320>u?4320:1960*Px(u/1960))-u,10<u){t.timeoutHandle=ac(Ti.bind(null,t,ln,kr),u);break}Ti(t,ln,kr);break;case 5:Ti(t,ln,kr);break;default:throw Error(q(329))}}}return fn(t,ut()),t.callbackNode===i?R0.bind(null,t):null}function Mc(t,n){var i=Ao;return t.current.memoizedState.isDehydrated&&(Ni(t,n).flags|=256),t=Gu(t,n),t!==2&&(n=ln,ln=i,n!==null&&bc(n)),t}function bc(t){ln===null?ln=t:ln.push.apply(ln,t)}function Ox(t){for(var n=t;;){if(n.flags&16384){var i=n.updateQueue;if(i!==null&&(i=i.stores,i!==null))for(var u=0;u<i.length;u++){var a=i[u],c=a.getSnapshot;a=a.value;try{if(!Gn(c(),a))return!1}catch{return!1}}}if(i=n.child,n.subtreeFlags&16384&&i!==null)i.return=n,n=i;else{if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return!0;n=n.return}n.sibling.return=n.return,n=n.sibling}}return!0}function qr(t,n){for(n&=~vd,n&=~oa,t.suspendedLanes|=n,t.pingedLanes&=~n,t=t.expirationTimes;0<n;){var i=31-Kn(n),u=1<<i;t[i]=-1,n&=~u}}function Ov(t){if(De&6)throw Error(q(327));Cl();var n=Mu(t,0);if(!(n&1))return fn(t,ut()),null;var i=Gu(t,n);if(t.tag!==0&&i===2){var u=tc(t);u!==0&&(n=u,i=Mc(t,u))}if(i===1)throw i=es,Ni(t,0),qr(t,n),fn(t,ut()),i;if(i===6)throw Error(q(345));return t.finishedWork=t.current.alternate,t.finishedLanes=n,Ti(t,ln,kr),fn(t,ut()),null}function md(t,n){var i=De;De|=1;try{return t(n)}finally{De=i,De===0&&(bl=ut()+500,na&&fi())}}function Di(t){Zr!==null&&Zr.tag===0&&!(De&6)&&Cl();var n=De;De|=1;var i=bn.transition,u=We;try{if(bn.transition=null,We=1,t)return t()}finally{We=u,bn.transition=i,De=n,!(De&6)&&fi()}}function yd(){vn=ml.current,Qe(ml)}function Ni(t,n){t.finishedWork=null,t.finishedLanes=0;var i=t.timeoutHandle;if(i!==-1&&(t.timeoutHandle=-1,ax(i)),pt!==null)for(i=pt.return;i!==null;){var u=i;switch(rd(u),u.tag){case 1:u=u.type.childContextTypes,u!=null&&Du();break;case 3:Ml(),Qe(un),Qe(Ut),sd();break;case 5:od(u);break;case 4:Ml();break;case 13:Qe(et);break;case 19:Qe(et);break;case 10:ed(u.type._context);break;case 22:case 23:yd()}i=i.return}if(Ct=t,pt=t=si(t.current,null),Pt=vn=n,kt=0,es=null,vd=oa=Oi=0,ln=Ao=null,$n!==null){for(n=0;n<$n.length;n++)if(i=$n[n],u=i.interleaved,u!==null){i.interleaved=null;var a=u.next,c=i.pending;if(c!==null){var p=c.next;c.next=a,u.next=p}i.pending=u}$n=null}return t}function W0(t,n){do{var i=pt;try{if(Jc(),hu.current=Uu,Bu){for(var u=nt.memoizedState;u!==null;){var a=u.queue;a!==null&&(a.pending=null),u=u.next}Bu=!1}if(Pi=0,Lt=St=nt=null,zo=!1,Xo=0,pd.current=null,i===null||i.return===null){kt=1,es=n,pt=null;break}e:{var c=t,p=i.return,g=i,y=n;if(n=Pt,g.flags|=32768,y!==null&&typeof y=="object"&&typeof y.then=="function"){var S=y,N=g,W=N.tag;if(!(N.mode&1)&&(W===0||W===11||W===15)){var O=N.alternate;O?(N.updateQueue=O.updateQueue,N.memoizedState=O.memoizedState,N.lanes=O.lanes):(N.updateQueue=null,N.memoizedState=null)}var U=gv(p);if(U!==null){U.flags&=-257,mv(U,p,g,c,n),U.mode&1&&vv(c,S,n),n=U,y=S;var B=n.updateQueue;if(B===null){var oe=new Set;oe.add(y),n.updateQueue=oe}else B.add(y);break e}else{if(!(n&1)){vv(c,S,n),wd();break e}y=Error(q(426))}}else if(qe&&g.mode&1){var G=gv(p);if(G!==null){!(G.flags&65536)&&(G.flags|=256),mv(G,p,g,c,n),id(y);break e}}c=y,kt!==4&&(kt=2),Ao===null?Ao=[c]:Ao.push(c),y=hd(y,g),g=p;do{switch(g.tag){case 3:g.flags|=65536,n&=-n,g.lanes|=n;var _=S0(g,y,n);ov(g,_);break e;case 1:c=y;var C=g.type,D=g.stateNode;if(!(g.flags&128)&&(typeof C.getDerivedStateFromError=="function"||D!==null&&typeof D.componentDidCatch=="function"&&(ri===null||!ri.has(D)))){g.flags|=65536,n&=-n,g.lanes|=n;var $=k0(g,c,n);ov(g,$);break e}}g=g.return}while(g!==null)}U0(i)}catch(Y){n=Y,pt===i&&i!==null&&(pt=i=i.return);continue}break}while(1)}function H0(){var t=ju.current;return ju.current=Uu,t===null?Uu:t}function wd(){(kt===0||kt===3||kt===2)&&(kt=4),Ct===null||!(Oi&268435455)&&!(oa&268435455)||qr(Ct,Pt)}function Gu(t,n){var i=De;De|=2;var u=H0();(Ct!==t||Pt!==n)&&(kr=null,Ni(t,n));do try{Dx();break}catch(a){W0(t,a)}while(1);if(Jc(),De=i,ju.current=u,pt!==null)throw Error(q(261));return Ct=null,Pt=0,kt}function Dx(){for(;pt!==null;)B0(pt)}function zx(){for(;pt!==null&&!ow();)B0(pt)}function B0(t){var n=$0(t.alternate,t,vn);t.memoizedProps=t.pendingProps,n===null?U0(t):pt=n,pd.current=null}function U0(t){var n=t;do{var i=n.alternate;if(t=n.return,n.flags&32768){if(i=Nx(i,n),i!==null){i.flags&=32767,pt=i;return}if(t!==null)t.flags|=32768,t.subtreeFlags=0,t.deletions=null;else{kt=6,pt=null;return}}else if(i=Tx(i,n,vn),i!==null){pt=i;return}if(n=n.sibling,n!==null){pt=n;return}pt=n=t}while(n!==null);kt===0&&(kt=5)}function Ti(t,n,i){var u=We,a=bn.transition;try{bn.transition=null,We=1,Ix(t,n,i,u)}finally{bn.transition=a,We=u}return null}function Ix(t,n,i,u){do Cl();while(Zr!==null);if(De&6)throw Error(q(327));i=t.finishedWork;var a=t.finishedLanes;if(i===null)return null;if(t.finishedWork=null,t.finishedLanes=0,i===t.current)throw Error(q(177));t.callbackNode=null,t.callbackPriority=0;var c=i.lanes|i.childLanes;if(gw(t,c),t===Ct&&(pt=Ct=null,Pt=0),!(i.subtreeFlags&2064)&&!(i.flags&2064)||lu||(lu=!0,K0(Nu,function(){return Cl(),null})),c=(i.flags&15990)!==0,i.subtreeFlags&15990||c){c=bn.transition,bn.transition=null;var p=We;We=1;var g=De;De|=4,pd.current=null,bx(t,i),I0(i,t),nx(sc),bu=!!oc,sc=oc=null,t.current=i,_x(i),sw(),De=g,We=p,bn.transition=c}else t.current=i;if(lu&&(lu=!1,Zr=t,Ku=a),c=t.pendingLanes,c===0&&(ri=null),fw(i.stateNode),fn(t,ut()),n!==null)for(u=t.onRecoverableError,i=0;i<n.length;i++)u(n[i]);if($u)throw $u=!1,t=Ec,Ec=null,t;return Ku&1&&t.tag!==0&&Cl(),c=t.pendingLanes,c&1?t===Nc?Fo++:(Fo=0,Nc=t):Fo=0,fi(),null}function Cl(){if(Zr!==null){var t=kg(Ku),n=bn.transition,i=We;try{if(bn.transition=null,We=16>t?16:t,Zr===null)var u=!1;else{if(t=Zr,Zr=null,Ku=0,De&6)throw Error(q(331));var a=De;for(De|=4,ae=t.current;ae!==null;){var c=ae,p=c.child;if(ae.flags&16){var g=c.deletions;if(g!==null){for(var y=0;y<g.length;y++){var S=g[y];for(ae=S;ae!==null;){var N=ae;switch(N.tag){case 0:case 11:case 15:Io(8,N,c)}var W=N.child;if(W!==null)W.return=N,ae=W;else for(;ae!==null;){N=ae;var O=N.sibling,U=N.return;if(O0(N),N===S){ae=null;break}if(O!==null){O.return=U,ae=O;break}ae=U}}}var B=c.alternate;if(B!==null){var oe=B.child;if(oe!==null){B.child=null;do{var G=oe.sibling;oe.sibling=null,oe=G}while(oe!==null)}}ae=c}}if(c.subtreeFlags&2064&&p!==null)p.return=c,ae=p;else e:for(;ae!==null;){if(c=ae,c.flags&2048)switch(c.tag){case 0:case 11:case 15:Io(9,c,c.return)}var _=c.sibling;if(_!==null){_.return=c.return,ae=_;break e}ae=c.return}}var C=t.current;for(ae=C;ae!==null;){p=ae;var D=p.child;if(p.subtreeFlags&2064&&D!==null)D.return=p,ae=D;else e:for(p=C;ae!==null;){if(g=ae,g.flags&2048)try{switch(g.tag){case 0:case 11:case 15:la(9,g)}}catch(Y){lt(g,g.return,Y)}if(g===p){ae=null;break e}var $=g.sibling;if($!==null){$.return=g.return,ae=$;break e}ae=g.return}}if(De=a,fi(),lr&&typeof lr.onPostCommitFiberRoot=="function")try{lr.onPostCommitFiberRoot(Xu,t)}catch{}u=!0}return u}finally{We=i,bn.transition=n}}return!1}function Dv(t,n,i){n=hd(i,n),n=S0(t,n,1),ni(t,n),n=qt(),t=sa(t,1),t!==null&&(ns(t,1,n),fn(t,n))}function lt(t,n,i){if(t.tag===3)Dv(t,t,i);else for(;n!==null;){if(n.tag===3){Dv(n,t,i);break}else if(n.tag===1){var u=n.stateNode;if(typeof n.type.getDerivedStateFromError=="function"||typeof u.componentDidCatch=="function"&&(ri===null||!ri.has(u))){t=hd(i,t),t=k0(n,t,1),ni(n,t),t=qt(),n=sa(n,1),n!==null&&(ns(n,1,t),fn(n,t));break}}n=n.return}}function Ax(t,n,i){var u=t.pingCache;u!==null&&u.delete(n),n=qt(),t.pingedLanes|=t.suspendedLanes&i,Ct===t&&(Pt&i)===i&&(kt===4||kt===3&&(Pt&130023424)===Pt&&500>ut()-gd?Ni(t,0):vd|=i),fn(t,n)}function j0(t,n){n===0&&(t.mode&1?(n=Vs,Vs<<=1,!(Vs&130023424)&&(Vs=4194304)):n=1);var i=qt();t=sa(t,n),t!==null&&(ns(t,n,i),fn(t,i))}function Fx(t){var n=t.memoizedState,i=0;n!==null&&(i=n.retryLane),j0(t,i)}function Rx(t,n){var i=0;switch(t.tag){case 13:var u=t.stateNode,a=t.memoizedState;a!==null&&(i=a.retryLane);break;case 19:u=t.stateNode;break;default:throw Error(q(314))}u!==null&&u.delete(n),j0(t,i)}var $0;$0=function(t,n,i){if(t!==null)if(t.memoizedProps!==n.pendingProps||un.current)sn=!0;else{if(!(t.lanes&i)&&!(n.flags&128))return sn=!1,Ex(t,n,i);sn=!!(t.flags&131072)}else sn=!1,qe&&n.flags&1048576&&Xg(n,Wu,n.index);switch(n.lanes=0,n.tag){case 2:var u=n.type;t!==null&&(t.alternate=null,n.alternate=null,n.flags|=2),t=n.pendingProps;var a=Ll(n,Ut.current);kl(n,i),a=ad(null,n,u,t,a,i);var c=fd();return n.flags|=1,typeof a=="object"&&a!==null&&typeof a.render=="function"&&a.$$typeof===void 0?(n.tag=1,n.memoizedState=null,n.updateQueue=null,an(u)?(c=!0,zu(n)):c=!1,n.memoizedState=a.state!==null&&a.state!==void 0?a.state:null,td(n),a.updater=ra,n.stateNode=a,a._reactInternals=n,pc(n,u,t,i),n=xc(null,n,u,!0,c,i)):(n.tag=0,qe&&c&&nd(n),Yt(null,n,a,i),n=n.child),n;case 16:u=n.elementType;e:{switch(t!==null&&(t.alternate=null,n.alternate=null,n.flags|=2),t=n.pendingProps,a=u._init,u=a(u._payload),n.type=u,a=n.tag=Hx(u),t=Hn(u,t),a){case 0:n=wc(null,n,u,t,i);break e;case 1:n=xv(null,n,u,t,i);break e;case 11:n=yv(null,n,u,t,i);break e;case 14:n=wv(null,n,u,Hn(u.type,t),i);break e}throw Error(q(306,u,""))}return n;case 0:return u=n.type,a=n.pendingProps,a=n.elementType===u?a:Hn(u,a),wc(t,n,u,a,i);case 1:return u=n.type,a=n.pendingProps,a=n.elementType===u?a:Hn(u,a),xv(t,n,u,a,i);case 3:e:{if(b0(n),t===null)throw Error(q(387));u=n.pendingProps,c=n.memoizedState,a=c.element,Vg(t,n),Fu(n,u,null,i);var p=n.memoizedState;if(u=p.element,c.isDehydrated)if(c={element:u,isDehydrated:!1,cache:p.cache,pendingSuspenseBoundaries:p.pendingSuspenseBoundaries,transitions:p.transitions},n.updateQueue.baseState=c,n.memoizedState=c,n.flags&256){a=Error(q(423)),n=Sv(t,n,u,i,a);break e}else if(u!==a){a=Error(q(424)),n=Sv(t,n,u,i,a);break e}else for(on=Tr(n.stateNode.containerInfo.firstChild),gn=n,qe=!0,Un=null,i=e0(n,null,u,i),n.child=i;i;)i.flags=i.flags&-3|4096,i=i.sibling;else{if(El(),u===a){n=_r(t,n,i);break e}Yt(t,n,u,i)}n=n.child}return n;case 5:return t0(n),t===null&&gc(n),u=n.type,a=n.pendingProps,c=t!==null?t.memoizedProps:null,p=a.children,uc(u,a)?p=null:c!==null&&uc(u,c)&&(n.flags|=32),M0(t,n),Yt(t,n,p,i),n.child;case 6:return t===null&&gc(n),null;case 13:return _0(t,n,i);case 4:return ld(n,n.stateNode.containerInfo),u=n.pendingProps,t===null?n.child=Nl(n,null,u,i):Yt(t,n,u,i),n.child;case 11:return u=n.type,a=n.pendingProps,a=n.elementType===u?a:Hn(u,a),yv(t,n,u,a,i);case 7:return Yt(t,n,n.pendingProps,i),n.child;case 8:return Yt(t,n,n.pendingProps.children,i),n.child;case 12:return Yt(t,n,n.pendingProps.children,i),n.child;case 10:e:{if(u=n.type._context,a=n.pendingProps,c=n.memoizedProps,p=a.value,$e(Iu,u._currentValue),u._currentValue=p,c!==null)if(Gn(c.value,p)){if(c.children===a.children&&!un.current){n=_r(t,n,i);break e}}else for(c=n.child,c!==null&&(c.return=n);c!==null;){var g=c.dependencies;if(g!==null){p=c.child;for(var y=g.firstContext;y!==null;){if(y.context===u){if(c.tag===1){y=Nr(-1,i&-i),y.tag=2;var S=c.updateQueue;if(S!==null){S=S.shared;var N=S.pending;N===null?y.next=y:(y.next=N.next,N.next=y),S.pending=y}}c.lanes|=i,y=c.alternate,y!==null&&(y.lanes|=i),dc(c.return,i,n),g.lanes|=i;break}y=y.next}}else if(c.tag===10)p=c.type===n.type?null:c.child;else if(c.tag===18){if(p=c.return,p===null)throw Error(q(341));p.lanes|=i,g=p.alternate,g!==null&&(g.lanes|=i),dc(p,i,n),p=c.sibling}else p=c.child;if(p!==null)p.return=c;else for(p=c;p!==null;){if(p===n){p=null;break}if(c=p.sibling,c!==null){c.return=p.return,p=c;break}p=p.return}c=p}Yt(t,n,a.children,i),n=n.child}return n;case 9:return a=n.type,u=n.pendingProps.children,kl(n,i),a=On(a),u=u(a),n.flags|=1,Yt(t,n,u,i),n.child;case 14:return u=n.type,a=Hn(u,n.pendingProps),a=Hn(u.type,a),wv(t,n,u,a,i);case 15:return E0(t,n,n.type,n.pendingProps,i);case 17:return u=n.type,a=n.pendingProps,a=n.elementType===u?a:Hn(u,a),t!==null&&(t.alternate=null,n.alternate=null,n.flags|=2),n.tag=1,an(u)?(t=!0,zu(n)):t=!1,kl(n,i),qg(n,u,a),pc(n,u,a,i),xc(null,n,u,!0,t,i);case 19:return P0(t,n,i);case 22:return N0(t,n,i)}throw Error(q(156,n.tag))};function K0(t,n){return yg(t,n)}function Wx(t,n,i,u){this.tag=t,this.key=i,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=n,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=u,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Mn(t,n,i,u){return new Wx(t,n,i,u)}function xd(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Hx(t){if(typeof t=="function")return xd(t)?1:0;if(t!=null){if(t=t.$$typeof,t===Hc)return 11;if(t===Bc)return 14}return 2}function si(t,n){var i=t.alternate;return i===null?(i=Mn(t.tag,n,t.key,t.mode),i.elementType=t.elementType,i.type=t.type,i.stateNode=t.stateNode,i.alternate=t,t.alternate=i):(i.pendingProps=n,i.type=t.type,i.flags=0,i.subtreeFlags=0,i.deletions=null),i.flags=t.flags&14680064,i.childLanes=t.childLanes,i.lanes=t.lanes,i.child=t.child,i.memoizedProps=t.memoizedProps,i.memoizedState=t.memoizedState,i.updateQueue=t.updateQueue,n=t.dependencies,i.dependencies=n===null?null:{lanes:n.lanes,firstContext:n.firstContext},i.sibling=t.sibling,i.index=t.index,i.ref=t.ref,i}function mu(t,n,i,u,a,c){var p=2;if(u=t,typeof t=="function")xd(t)&&(p=1);else if(typeof t=="string")p=5;else e:switch(t){case sl:return Mi(i.children,a,c,n);case Wc:p=8,a|=8;break;case Hf:return t=Mn(12,i,n,a|2),t.elementType=Hf,t.lanes=c,t;case Bf:return t=Mn(13,i,n,a),t.elementType=Bf,t.lanes=c,t;case Uf:return t=Mn(19,i,n,a),t.elementType=Uf,t.lanes=c,t;case tg:return Qu(i,a,c,n);default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case Jv:p=10;break e;case eg:p=9;break e;case Hc:p=11;break e;case Bc:p=14;break e;case Qr:p=16,u=null;break e}throw Error(q(130,t==null?t:typeof t,""))}return n=Mn(p,i,n,a),n.elementType=t,n.type=u,n.lanes=c,n}function Mi(t,n,i,u){return t=Mn(7,t,u,n),t.lanes=i,t}function Qu(t,n,i,u){return t=Mn(22,t,u,n),t.elementType=tg,t.lanes=i,t.stateNode={},t}function Of(t,n,i){return t=Mn(6,t,null,n),t.lanes=i,t}function Df(t,n,i){return n=Mn(4,t.children!==null?t.children:[],t.key,n),n.lanes=i,n.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},n}function Bx(t,n,i,u,a){this.tag=n,this.containerInfo=t,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=pf(0),this.expirationTimes=pf(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=pf(0),this.identifierPrefix=u,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function Sd(t,n,i,u,a,c,p,g,y){return t=new Bx(t,n,i,g,y),n===1?(n=1,c===!0&&(n|=8)):n=0,c=Mn(3,null,null,n),t.current=c,c.stateNode=t,c.memoizedState={element:u,isDehydrated:i,cache:null,transitions:null,pendingSuspenseBoundaries:null},td(c),t}function Ux(t,n,i){var u=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:ol,key:u==null?null:""+u,children:t,containerInfo:n,implementation:i}}function G0(t){if(!t)return oi;t=t._reactInternals;e:{if(Ii(t)!==t||t.tag!==1)throw Error(q(170));var n=t;do{switch(n.tag){case 3:n=n.stateNode.context;break e;case 1:if(an(n.type)){n=n.stateNode.__reactInternalMemoizedMergedChildContext;break e}}n=n.return}while(n!==null);throw Error(q(171))}if(t.tag===1){var i=t.type;if(an(i))return Gg(t,i,n)}return n}function Q0(t,n,i,u,a,c,p,g,y){return t=Sd(i,u,!0,t,a,c,p,g,y),t.context=G0(null),i=t.current,u=qt(),a=ii(i),c=Nr(u,a),c.callback=n??null,ni(i,c),t.current.lanes=a,ns(t,a,u),fn(t,u),t}function ua(t,n,i,u){var a=n.current,c=qt(),p=ii(a);return i=G0(i),n.context===null?n.context=i:n.pendingContext=i,n=Nr(c,p),n.payload={element:t},u=u===void 0?null:u,u!==null&&(n.callback=u),ni(a,n),t=_n(a,p,c),t!==null&&du(t,a,p),p}function Vu(t){if(t=t.current,!t.child)return null;switch(t.child.tag){case 5:return t.child.stateNode;default:return t.child.stateNode}}function zv(t,n){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var i=t.retryLane;t.retryLane=i!==0&&i<n?i:n}}function kd(t,n){zv(t,n),(t=t.alternate)&&zv(t,n)}function jx(){return null}var V0=typeof reportError=="function"?reportError:function(t){console.error(t)};function Cd(t){this._internalRoot=t}aa.prototype.render=Cd.prototype.render=function(t){var n=this._internalRoot;if(n===null)throw Error(q(409));ua(t,n,null,null)};aa.prototype.unmount=Cd.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var n=t.containerInfo;Di(function(){ua(null,t,null,null)}),n[br]=null}};function aa(t){this._internalRoot=t}aa.prototype.unstable_scheduleHydration=function(t){if(t){var n=Lg();t={blockedOn:null,target:t,priority:n};for(var i=0;i<Yr.length&&n!==0&&n<Yr[i].priority;i++);Yr.splice(i,0,t),i===0&&Ng(t)}};function Td(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function fa(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11&&(t.nodeType!==8||t.nodeValue!==" react-mount-point-unstable "))}function Iv(){}function $x(t,n,i,u,a){if(a){if(typeof u=="function"){var c=u;u=function(){var S=Vu(p);c.call(S)}}var p=Q0(n,u,t,0,null,!1,!1,"",Iv);return t._reactRootContainer=p,t[br]=p.current,Go(t.nodeType===8?t.parentNode:t),Di(),p}for(;a=t.lastChild;)t.removeChild(a);if(typeof u=="function"){var g=u;u=function(){var S=Vu(y);g.call(S)}}var y=Sd(t,0,!1,null,null,!1,!1,"",Iv);return t._reactRootContainer=y,t[br]=y.current,Go(t.nodeType===8?t.parentNode:t),Di(function(){ua(n,y,i,u)}),y}function ca(t,n,i,u,a){var c=i._reactRootContainer;if(c){var p=c;if(typeof a=="function"){var g=a;a=function(){var y=Vu(p);g.call(y)}}ua(n,p,t,a)}else p=$x(i,n,t,a,u);return Vu(p)}Cg=function(t){switch(t.tag){case 3:var n=t.stateNode;if(n.current.memoizedState.isDehydrated){var i=No(n.pendingLanes);i!==0&&($c(n,i|1),fn(n,ut()),!(De&6)&&(bl=ut()+500,fi()))}break;case 13:var u=qt();Di(function(){return _n(t,1,u)}),kd(t,1)}};Kc=function(t){if(t.tag===13){var n=qt();_n(t,134217728,n),kd(t,134217728)}};Tg=function(t){if(t.tag===13){var n=qt(),i=ii(t);_n(t,i,n),kd(t,i)}};Lg=function(){return We};Eg=function(t,n){var i=We;try{return We=t,n()}finally{We=i}};Zf=function(t,n,i){switch(n){case"input":if(Kf(t,i),n=i.name,i.type==="radio"&&n!=null){for(i=t;i.parentNode;)i=i.parentNode;for(i=i.querySelectorAll("input[name="+JSON.stringify(""+n)+'][type="radio"]'),n=0;n<i.length;n++){var u=i[n];if(u!==t&&u.form===t.form){var a=ta(u);if(!a)throw Error(q(90));rg(u),Kf(u,a)}}}break;case"textarea":lg(t,i);break;case"select":n=i.value,n!=null&&yl(t,!!i.multiple,n,!1)}};dg=md;hg=Di;var Kx={usingClientEntryPoint:!1,Events:[is,cl,ta,fg,cg,md]},To={findFiberByHostInstance:Li,bundleType:0,version:"18.1.0",rendererPackageName:"react-dom"},Gx={bundleType:To.bundleType,version:To.version,rendererPackageName:To.rendererPackageName,rendererConfig:To.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Pr.ReactCurrentDispatcher,findHostInstanceByFiber:function(t){return t=gg(t),t===null?null:t.stateNode},findFiberByHostInstance:To.findFiberByHostInstance||jx,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.1.0-next-22edb9f77-20220426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ou=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ou.isDisabled&&ou.supportsFiber)try{Xu=ou.inject(Gx),lr=ou}catch{}}yn.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Kx;yn.createPortal=function(t,n){var i=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Td(n))throw Error(q(200));return Ux(t,n,null,i)};yn.createRoot=function(t,n){if(!Td(t))throw Error(q(299));var i=!1,u="",a=V0;return n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(u=n.identifierPrefix),n.onRecoverableError!==void 0&&(a=n.onRecoverableError)),n=Sd(t,1,!1,null,null,i,!1,u,a),t[br]=n.current,Go(t.nodeType===8?t.parentNode:t),new Cd(n)};yn.findDOMNode=function(t){if(t==null)return null;if(t.nodeType===1)return t;var n=t._reactInternals;if(n===void 0)throw typeof t.render=="function"?Error(q(188)):(t=Object.keys(t).join(","),Error(q(268,t)));return t=gg(n),t=t===null?null:t.stateNode,t};yn.flushSync=function(t){return Di(t)};yn.hydrate=function(t,n,i){if(!fa(n))throw Error(q(200));return ca(null,t,n,!0,i)};yn.hydrateRoot=function(t,n,i){if(!Td(t))throw Error(q(405));var u=i!=null&&i.hydratedSources||null,a=!1,c="",p=V0;if(i!=null&&(i.unstable_strictMode===!0&&(a=!0),i.identifierPrefix!==void 0&&(c=i.identifierPrefix),i.onRecoverableError!==void 0&&(p=i.onRecoverableError)),n=Q0(n,null,t,1,i??null,a,!1,c,p),t[br]=n.current,Go(t),u)for(t=0;t<u.length;t++)i=u[t],a=i._getVersion,a=a(i._source),n.mutableSourceEagerHydrationData==null?n.mutableSourceEagerHydrationData=[i,a]:n.mutableSourceEagerHydrationData.push(i,a);return new aa(n)};yn.render=function(t,n,i){if(!fa(n))throw Error(q(200));return ca(null,t,n,!1,i)};yn.unmountComponentAtNode=function(t){if(!fa(t))throw Error(q(40));return t._reactRootContainer?(Di(function(){ca(null,null,t,!1,function(){t._reactRootContainer=null,t[br]=null})}),!0):!1};yn.unstable_batchedUpdates=md;yn.unstable_renderSubtreeIntoContainer=function(t,n,i,u){if(!fa(i))throw Error(q(200));if(t==null||t._reactInternals===void 0)throw Error(q(38));return ca(t,n,i,!1,u)};yn.version="18.1.0-next-22edb9f77-20220426";(function(t){function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(i){console.error(i)}}n(),t.exports=yn})($1);var _l={},Qx={get exports(){return _l},set exports(t){_l=t}};(function(t,n){(function(i,u){t.exports=u()})(y1,function(){var i=navigator.userAgent,u=navigator.platform,a=/gecko\/\d/i.test(i),c=/MSIE \d/.test(i),p=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(i),g=/Edge\/(\d+)/.exec(i),y=c||p||g,S=y&&(c?document.documentMode||6:+(g||p)[1]),N=!g&&/WebKit\//.test(i),W=N&&/Qt\/\d+\.\d+/.test(i),O=!g&&/Chrome\/(\d+)/.exec(i),U=O&&+O[1],B=/Opera\//.test(i),oe=/Apple Computer/.test(navigator.vendor),G=/Mac OS X 1\d\D([8-9]|\d\d)\D/.test(i),_=/PhantomJS/.test(i),C=oe&&(/Mobile\/\w+/.test(i)||navigator.maxTouchPoints>2),D=/Android/.test(i),$=C||D||/webOS|BlackBerry|Opera Mini|Opera Mobi|IEMobile/i.test(i),Y=C||/Mac/.test(u),ie=/\bCrOS\b/.test(i),I=/win/i.test(u),Z=B&&i.match(/Version\/(\d*\.\d*)/);Z&&(Z=Number(Z[1])),Z&&Z>=15&&(B=!1,N=!0);var ke=Y&&(W||B&&(Z==null||Z<12.11)),me=a||y&&S>=9;function Re(e){return new RegExp("(^|\\s)"+e+"(?:$|\\s)\\s*")}var K=function(e,r){var o=e.className,l=Re(r).exec(o);if(l){var s=o.slice(l.index+l[0].length);e.className=o.slice(0,l.index)+(s?l[1]+s:"")}};function A(e){for(var r=e.childNodes.length;r>0;--r)e.removeChild(e.firstChild);return e}function ne(e,r){return A(e).appendChild(r)}function M(e,r,o,l){var s=document.createElement(e);if(o&&(s.className=o),l&&(s.style.cssText=l),typeof r=="string")s.appendChild(document.createTextNode(r));else if(r)for(var f=0;f<r.length;++f)s.appendChild(r[f]);return s}function Q(e,r,o,l){var s=M(e,r,o,l);return s.setAttribute("role","presentation"),s}var X;document.createRange?X=function(e,r,o,l){var s=document.createRange();return s.setEnd(l||e,o),s.setStart(e,r),s}:X=function(e,r,o){var l=document.body.createTextRange();try{l.moveToElementText(e.parentNode)}catch{return l}return l.collapse(!0),l.moveEnd("character",o),l.moveStart("character",r),l};function k(e,r){if(r.nodeType==3&&(r=r.parentNode),e.contains)return e.contains(r);do if(r.nodeType==11&&(r=r.host),r==e)return!0;while(r=r.parentNode)}function E(e){var r;try{r=e.activeElement}catch{r=e.body||null}for(;r&&r.shadowRoot&&r.shadowRoot.activeElement;)r=r.shadowRoot.activeElement;return r}function F(e,r){var o=e.className;Re(r).test(o)||(e.className+=(o?" ":"")+r)}function J(e,r){for(var o=e.split(" "),l=0;l<o.length;l++)o[l]&&!Re(o[l]).test(r)&&(r+=" "+o[l]);return r}var ue=function(e){e.select()};C?ue=function(e){e.selectionStart=0,e.selectionEnd=e.value.length}:y&&(ue=function(e){try{e.select()}catch{}});function ye(e){return e.display.wrapper.ownerDocument}function Be(e){return ye(e).defaultView}function Ve(e){var r=Array.prototype.slice.call(arguments,1);return function(){return e.apply(null,r)}}function he(e,r,o){r||(r={});for(var l in e)e.hasOwnProperty(l)&&(o!==!1||!r.hasOwnProperty(l))&&(r[l]=e[l]);return r}function fe(e,r,o,l,s){r==null&&(r=e.search(/[^\s\u00a0]/),r==-1&&(r=e.length));for(var f=l||0,d=s||0;;){var h=e.indexOf("	",f);if(h<0||h>=r)return d+(r-f);d+=h-f,d+=o-d%o,f=h+1}}var Ce=function(){this.id=null,this.f=null,this.time=0,this.handler=Ve(this.onTimeout,this)};Ce.prototype.onTimeout=function(e){e.id=0,e.time<=+new Date?e.f():setTimeout(e.handler,e.time-+new Date)},Ce.prototype.set=function(e,r){this.f=r;var o=+new Date+e;(!this.id||o<this.time)&&(clearTimeout(this.id),this.id=setTimeout(this.handler,e),this.time=o)};function Le(e,r){for(var o=0;o<e.length;++o)if(e[o]==r)return o;return-1}var Ai=50,Ae={toString:function(){return"CodeMirror.Pass"}},je={scroll:!1},Qn={origin:"*mouse"},Vn={origin:"+move"};function xn(e,r,o){for(var l=0,s=0;;){var f=e.indexOf("	",l);f==-1&&(f=e.length);var d=f-l;if(f==e.length||s+d>=r)return l+Math.min(d,r-s);if(s+=f-l,s+=o-s%o,l=f+1,s>=r)return l}}var Jt=[""];function cn(e){for(;Jt.length<=e;)Jt.push(Ne(Jt)+" ");return Jt[e]}function Ne(e){return e[e.length-1]}function sr(e,r){for(var o=[],l=0;l<e.length;l++)o[l]=r(e[l],l);return o}function ft(e,r,o){for(var l=0,s=o(r);l<e.length&&o(e[l])<=s;)l++;e.splice(l,0,r)}function ur(){}function ci(e,r){var o;return Object.create?o=Object.create(e):(ur.prototype=e,o=new ur),r&&he(r,o),o}var Al=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;function Or(e){return/\w/.test(e)||e>""&&(e.toUpperCase()!=e.toLowerCase()||Al.test(e))}function ar(e,r){return r?r.source.indexOf("\\w")>-1&&Or(e)?!0:r.test(e):Or(e)}function ge(e){for(var r in e)if(e.hasOwnProperty(r)&&e[r])return!1;return!0}var we=/[\u0300-\u036f\u0483-\u0489\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u0610-\u061a\u064b-\u065e\u0670\u06d6-\u06dc\u06de-\u06e4\u06e7\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0900-\u0902\u093c\u0941-\u0948\u094d\u0951-\u0955\u0962\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2\u09e3\u0a01\u0a02\u0a3c\u0a41\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a70\u0a71\u0a75\u0a81\u0a82\u0abc\u0ac1-\u0ac5\u0ac7\u0ac8\u0acd\u0ae2\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c62\u0c63\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc\u0ccd\u0cd5\u0cd6\u0ce2\u0ce3\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb\u0ebc\u0ec8-\u0ecd\u0f18\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86\u0f87\u0f90-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039\u103a\u103d\u103e\u1058\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085\u1086\u108d\u109d\u135f\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193b\u1a17\u1a18\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80\u1b81\u1ba2-\u1ba5\u1ba8\u1ba9\u1c2c-\u1c33\u1c36\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1dc0-\u1de6\u1dfd-\u1dff\u200c\u200d\u20d0-\u20f0\u2cef-\u2cf1\u2de0-\u2dff\u302a-\u302f\u3099\u309a\ua66f-\ua672\ua67c\ua67d\ua6f0\ua6f1\ua802\ua806\ua80b\ua825\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\uaa29-\uaa2e\uaa31\uaa32\uaa35\uaa36\uaa43\uaa4c\uaab0\uaab2-\uaab4\uaab7\uaab8\uaabe\uaabf\uaac1\uabe5\uabe8\uabed\udc00-\udfff\ufb1e\ufe00-\ufe0f\ufe20-\ufe26\uff9e\uff9f]/;function de(e){return e.charCodeAt(0)>=768&&we.test(e)}function Me(e,r,o){for(;(o<0?r>0:r<e.length)&&de(e.charAt(r));)r+=o;return r}function Te(e,r,o){for(var l=r>o?-1:1;;){if(r==o)return r;var s=(r+o)/2,f=l<0?Math.ceil(s):Math.floor(s);if(f==r)return e(f)?r:o;e(f)?o=f:r=f+l}}function dn(e,r,o,l){if(!e)return l(r,o,"ltr",0);for(var s=!1,f=0;f<e.length;++f){var d=e[f];(d.from<o&&d.to>r||r==o&&d.to==r)&&(l(Math.max(d.from,r),Math.min(d.to,o),d.level==1?"rtl":"ltr",f),s=!0)}s||l(r,o,"ltr")}var Sn=null;function kn(e,r,o){var l;Sn=null;for(var s=0;s<e.length;++s){var f=e[s];if(f.from<r&&f.to>r)return s;f.to==r&&(f.from!=f.to&&o=="before"?l=s:Sn=s),f.from==r&&(f.from!=f.to&&o!="before"?l=s:Sn=s)}return l??Sn}var ha=function(){var e="bbbbbbbbbtstwsbbbbbbbbbbbbbbssstwNN%%%NNNNNN,N,N1111111111NNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNbbbbbbsbbbbbbbbbbbbbbbbbbbbbbbbbb,N%%%%NNNNLNNNNN%%11NLNNN1LNNNNNLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLN",r="nnnnnnNNr%%r,rNNmmmmmmmmmmmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmmmmmmmmnnnnnnnnnn%nnrrrmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmnNmmmmmmrrmmNmmmmrr1111111111";function o(m){return m<=247?e.charAt(m):1424<=m&&m<=1524?"R":1536<=m&&m<=1785?r.charAt(m-1536):1774<=m&&m<=2220?"r":8192<=m&&m<=8203?"w":m==8204?"b":"L"}var l=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/,s=/[stwN]/,f=/[LRr]/,d=/[Lb1n]/,h=/[1n]/;function v(m,x,T){this.level=m,this.from=x,this.to=T}return function(m,x){var T=x=="ltr"?"L":"R";if(m.length==0||x=="ltr"&&!l.test(m))return!1;for(var z=m.length,P=[],H=0;H<z;++H)P.push(o(m.charCodeAt(H)));for(var j=0,V=T;j<z;++j){var ee=P[j];ee=="m"?P[j]=V:V=ee}for(var le=0,te=T;le<z;++le){var se=P[le];se=="1"&&te=="r"?P[le]="n":f.test(se)&&(te=se,se=="r"&&(P[le]="R"))}for(var ve=1,pe=P[0];ve<z-1;++ve){var Ee=P[ve];Ee=="+"&&pe=="1"&&P[ve+1]=="1"?P[ve]="1":Ee==","&&pe==P[ve+1]&&(pe=="1"||pe=="n")&&(P[ve]=pe),pe=Ee}for(var He=0;He<z;++He){var yt=P[He];if(yt==",")P[He]="N";else if(yt=="%"){var Ye=void 0;for(Ye=He+1;Ye<z&&P[Ye]=="%";++Ye);for(var nn=He&&P[He-1]=="!"||Ye<z&&P[Ye]=="1"?"1":"N",Gt=He;Gt<Ye;++Gt)P[Gt]=nn;He=Ye-1}}for(var ot=0,Qt=T;ot<z;++ot){var Tt=P[ot];Qt=="L"&&Tt=="1"?P[ot]="L":f.test(Tt)&&(Qt=Tt)}for(var dt=0;dt<z;++dt)if(s.test(P[dt])){var st=void 0;for(st=dt+1;st<z&&s.test(P[st]);++st);for(var Je=(dt?P[dt-1]:T)=="L",Vt=(st<z?P[st]:T)=="L",rl=Je==Vt?Je?"L":"R":T,Kr=dt;Kr<st;++Kr)P[Kr]=rl;dt=st-1}for(var bt=[],tr,wt=0;wt<z;)if(d.test(P[wt])){var sf=wt;for(++wt;wt<z&&d.test(P[wt]);++wt);bt.push(new v(0,sf,wt))}else{var wr=wt,Si=bt.length,ki=x=="rtl"?1:0;for(++wt;wt<z&&P[wt]!="L";++wt);for(var At=wr;At<wt;)if(h.test(P[At])){wr<At&&(bt.splice(Si,0,new v(1,wr,At)),Si+=ki);var il=At;for(++At;At<wt&&h.test(P[At]);++At);bt.splice(Si,0,new v(2,il,At)),Si+=ki,wr=At}else++At;wr<wt&&bt.splice(Si,0,new v(1,wr,wt))}return x=="ltr"&&(bt[0].level==1&&(tr=m.match(/^\s+/))&&(bt[0].from=tr[0].length,bt.unshift(new v(0,0,tr[0].length))),Ne(bt).level==1&&(tr=m.match(/\s+$/))&&(Ne(bt).to-=tr[0].length,bt.push(new v(0,z-tr[0].length,z)))),x=="rtl"?bt.reverse():bt}}();function Cn(e,r){var o=e.order;return o==null&&(o=e.order=ha(e.text,r)),o}var ss=[],re=function(e,r,o){if(e.addEventListener)e.addEventListener(r,o,!1);else if(e.attachEvent)e.attachEvent("on"+r,o);else{var l=e._handlers||(e._handlers={});l[r]=(l[r]||ss).concat(o)}};function Fl(e,r){return e._handlers&&e._handlers[r]||ss}function Et(e,r,o){if(e.removeEventListener)e.removeEventListener(r,o,!1);else if(e.detachEvent)e.detachEvent("on"+r,o);else{var l=e._handlers,s=l&&l[r];if(s){var f=Le(s,o);f>-1&&(l[r]=s.slice(0,f).concat(s.slice(f+1)))}}}function Ue(e,r){var o=Fl(e,r);if(o.length)for(var l=Array.prototype.slice.call(arguments,2),s=0;s<o.length;++s)o[s].apply(null,l)}function Xe(e,r,o){return typeof r=="string"&&(r={type:r,preventDefault:function(){this.defaultPrevented=!0}}),Ue(e,o||r.type,e,r),Rl(r)||r.codemirrorIgnore}function us(e){var r=e._handlers&&e._handlers.cursorActivity;if(r)for(var o=e.curOp.cursorActivityHandlers||(e.curOp.cursorActivityHandlers=[]),l=0;l<r.length;++l)Le(o,r[l])==-1&&o.push(r[l])}function Dt(e,r){return Fl(e,r).length>0}function zt(e){e.prototype.on=function(r,o){re(this,r,o)},e.prototype.off=function(r,o){Et(this,r,o)}}function Nt(e){e.preventDefault?e.preventDefault():e.returnValue=!1}function di(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}function Rl(e){return e.defaultPrevented!=null?e.defaultPrevented:e.returnValue==!1}function fr(e){Nt(e),di(e)}function jt(e){return e.target||e.srcElement}function Wl(e){var r=e.which;return r==null&&(e.button&1?r=1:e.button&2?r=3:e.button&4&&(r=2)),Y&&e.ctrlKey&&r==1&&(r=3),r}var pa=function(){if(y&&S<9)return!1;var e=M("div");return"draggable"in e||"dragDrop"in e}(),Tn;function va(e){if(Tn==null){var r=M("span","​");ne(e,M("span",[r,document.createTextNode("x")])),e.firstChild.offsetHeight!=0&&(Tn=r.offsetWidth<=1&&r.offsetHeight>2&&!(y&&S<8))}var o=Tn?M("span","​"):M("span"," ",null,"display: inline-block; width: 1px; margin-right: -1px");return o.setAttribute("cm-text",""),o}var Fi;function as(e){if(Fi!=null)return Fi;var r=ne(e,document.createTextNode("AخA")),o=X(r,0,1).getBoundingClientRect(),l=X(r,1,2).getBoundingClientRect();return A(e),!o||o.left==o.right?!1:Fi=l.right-o.right<3}var Hl=`

b`.split(/\n/).length!=3?function(e){for(var r=0,o=[],l=e.length;r<=l;){var s=e.indexOf(`
`,r);s==-1&&(s=e.length);var f=e.slice(r,e.charAt(s-1)=="\r"?s-1:s),d=f.indexOf("\r");d!=-1?(o.push(f.slice(0,d)),r+=d+1):(o.push(f),r=s+1)}return o}:function(e){return e.split(/\r\n?|\n/)},Dr=window.getSelection?function(e){try{return e.selectionStart!=e.selectionEnd}catch{return!1}}:function(e){var r;try{r=e.ownerDocument.selection.createRange()}catch{}return!r||r.parentElement()!=e?!1:r.compareEndPoints("StartToEnd",r)!=0},Yn=function(){var e=M("div");return"oncopy"in e?!0:(e.setAttribute("oncopy","return;"),typeof e.oncopy=="function")}(),qn=null;function fs(e){if(qn!=null)return qn;var r=ne(e,M("span","x")),o=r.getBoundingClientRect(),l=X(r,0,1).getBoundingClientRect();return qn=Math.abs(o.left-l.left)>1}var zn={},zr={};function cs(e,r){arguments.length>2&&(r.dependencies=Array.prototype.slice.call(arguments,2)),zn[e]=r}function Ri(e,r){zr[e]=r}function en(e){if(typeof e=="string"&&zr.hasOwnProperty(e))e=zr[e];else if(e&&typeof e.name=="string"&&zr.hasOwnProperty(e.name)){var r=zr[e.name];typeof r=="string"&&(r={name:r}),e=ci(r,e),e.name=r.name}else{if(typeof e=="string"&&/^[\w\-]+\/[\w\-]+\+xml$/.test(e))return en("application/xml");if(typeof e=="string"&&/^[\w\-]+\/[\w\-]+\+json$/.test(e))return en("application/json")}return typeof e=="string"?{name:e}:e||{name:"null"}}function cr(e,r){r=en(r);var o=zn[r.name];if(!o)return cr(e,"text/plain");var l=o(e,r);if(Ir.hasOwnProperty(r.name)){var s=Ir[r.name];for(var f in s)s.hasOwnProperty(f)&&(l.hasOwnProperty(f)&&(l["_"+f]=l[f]),l[f]=s[f])}if(l.name=r.name,r.helperType&&(l.helperType=r.helperType),r.modeProps)for(var d in r.modeProps)l[d]=r.modeProps[d];return l}var Ir={};function ds(e,r){var o=Ir.hasOwnProperty(e)?Ir[e]:Ir[e]={};he(r,o)}function dr(e,r){if(r===!0)return r;if(e.copyState)return e.copyState(r);var o={};for(var l in r){var s=r[l];s instanceof Array&&(s=s.concat([])),o[l]=s}return o}function Ar(e,r){for(var o;e.innerMode&&(o=e.innerMode(r),!(!o||o.mode==e));)r=o.state,e=o.mode;return o||{mode:e,state:r}}function Bl(e,r,o){return e.startState?e.startState(r,o):!0}var Ze=function(e,r,o){this.pos=this.start=0,this.string=e,this.tabSize=r||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0,this.lineOracle=o};Ze.prototype.eol=function(){return this.pos>=this.string.length},Ze.prototype.sol=function(){return this.pos==this.lineStart},Ze.prototype.peek=function(){return this.string.charAt(this.pos)||void 0},Ze.prototype.next=function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},Ze.prototype.eat=function(e){var r=this.string.charAt(this.pos),o;if(typeof e=="string"?o=r==e:o=r&&(e.test?e.test(r):e(r)),o)return++this.pos,r},Ze.prototype.eatWhile=function(e){for(var r=this.pos;this.eat(e););return this.pos>r},Ze.prototype.eatSpace=function(){for(var e=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>e},Ze.prototype.skipToEnd=function(){this.pos=this.string.length},Ze.prototype.skipTo=function(e){var r=this.string.indexOf(e,this.pos);if(r>-1)return this.pos=r,!0},Ze.prototype.backUp=function(e){this.pos-=e},Ze.prototype.column=function(){return this.lastColumnPos<this.start&&(this.lastColumnValue=fe(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue-(this.lineStart?fe(this.string,this.lineStart,this.tabSize):0)},Ze.prototype.indentation=function(){return fe(this.string,null,this.tabSize)-(this.lineStart?fe(this.string,this.lineStart,this.tabSize):0)},Ze.prototype.match=function(e,r,o){if(typeof e=="string"){var l=function(d){return o?d.toLowerCase():d},s=this.string.substr(this.pos,e.length);if(l(s)==l(e))return r!==!1&&(this.pos+=e.length),!0}else{var f=this.string.slice(this.pos).match(e);return f&&f.index>0?null:(f&&r!==!1&&(this.pos+=f[0].length),f)}},Ze.prototype.current=function(){return this.string.slice(this.start,this.pos)},Ze.prototype.hideFirstChars=function(e,r){this.lineStart+=e;try{return r()}finally{this.lineStart-=e}},Ze.prototype.lookAhead=function(e){var r=this.lineOracle;return r&&r.lookAhead(e)},Ze.prototype.baseToken=function(){var e=this.lineOracle;return e&&e.baseToken(this.pos)};function ce(e,r){if(r-=e.first,r<0||r>=e.size)throw new Error("There is no line "+(r+e.first)+" in the document.");for(var o=e;!o.lines;)for(var l=0;;++l){var s=o.children[l],f=s.chunkSize();if(r<f){o=s;break}r-=f}return o.lines[r]}function hr(e,r,o){var l=[],s=r.line;return e.iter(r.line,o.line+1,function(f){var d=f.text;s==o.line&&(d=d.slice(0,o.ch)),s==r.line&&(d=d.slice(r.ch)),l.push(d),++s}),l}function Wi(e,r,o){var l=[];return e.iter(r,o,function(s){l.push(s.text)}),l}function Ln(e,r){var o=r-e.height;if(o)for(var l=e;l;l=l.parent)l.height+=o}function ze(e){if(e.parent==null)return null;for(var r=e.parent,o=Le(r.lines,e),l=r.parent;l;r=l,l=l.parent)for(var s=0;l.children[s]!=r;++s)o+=l.children[s].chunkSize();return o+r.first}function Xn(e,r){var o=e.first;e:do{for(var l=0;l<e.children.length;++l){var s=e.children[l],f=s.height;if(r<f){e=s;continue e}r-=f,o+=s.chunkSize()}return o}while(!e.lines);for(var d=0;d<e.lines.length;++d){var h=e.lines[d],v=h.height;if(r<v)break;r-=v}return o+d}function w(e,r){return r>=e.first&&r<e.first+e.size}function L(e,r){return String(e.lineNumberFormatter(r+e.firstLineNumber))}function b(e,r,o){if(o===void 0&&(o=null),!(this instanceof b))return new b(e,r,o);this.line=e,this.ch=r,this.sticky=o}function R(e,r){return e.line-r.line||e.ch-r.ch}function xe(e,r){return e.sticky==r.sticky&&R(e,r)==0}function be(e){return b(e.line,e.ch)}function Oe(e,r){return R(e,r)<0?r:e}function ct(e,r){return R(e,r)<0?e:r}function hn(e,r){return Math.max(e.first,Math.min(r,e.first+e.size-1))}function Se(e,r){if(r.line<e.first)return b(e.first,0);var o=e.first+e.size-1;return r.line>o?b(o,ce(e,o).text.length):wm(r,ce(e,r.line).text.length)}function wm(e,r){var o=e.ch;return o==null||o>r?b(e.line,r):o<0?b(e.line,0):e}function Ed(e,r){for(var o=[],l=0;l<r.length;l++)o[l]=Se(e,r[l]);return o}var hs=function(e,r){this.state=e,this.lookAhead=r},Zn=function(e,r,o,l){this.state=r,this.doc=e,this.line=o,this.maxLookAhead=l||0,this.baseTokens=null,this.baseTokenPos=1};Zn.prototype.lookAhead=function(e){var r=this.doc.getLine(this.line+e);return r!=null&&e>this.maxLookAhead&&(this.maxLookAhead=e),r},Zn.prototype.baseToken=function(e){if(!this.baseTokens)return null;for(;this.baseTokens[this.baseTokenPos]<=e;)this.baseTokenPos+=2;var r=this.baseTokens[this.baseTokenPos+1];return{type:r&&r.replace(/( |^)overlay .*/,""),size:this.baseTokens[this.baseTokenPos]-e}},Zn.prototype.nextLine=function(){this.line++,this.maxLookAhead>0&&this.maxLookAhead--},Zn.fromSaved=function(e,r,o){return r instanceof hs?new Zn(e,dr(e.mode,r.state),o,r.lookAhead):new Zn(e,dr(e.mode,r),o)},Zn.prototype.save=function(e){var r=e!==!1?dr(this.doc.mode,this.state):this.state;return this.maxLookAhead>0?new hs(r,this.maxLookAhead):r};function Nd(e,r,o,l){var s=[e.state.modeGen],f={};Dd(e,r.text,e.doc.mode,o,function(m,x){return s.push(m,x)},f,l);for(var d=o.state,h=function(m){o.baseTokens=s;var x=e.state.overlays[m],T=1,z=0;o.state=!0,Dd(e,r.text,x.mode,o,function(P,H){for(var j=T;z<P;){var V=s[T];V>P&&s.splice(T,1,P,s[T+1],V),T+=2,z=Math.min(P,V)}if(H)if(x.opaque)s.splice(j,T-j,P,"overlay "+H),T=j+2;else for(;j<T;j+=2){var ee=s[j+1];s[j+1]=(ee?ee+" ":"")+"overlay "+H}},f),o.state=d,o.baseTokens=null,o.baseTokenPos=1},v=0;v<e.state.overlays.length;++v)h(v);return{styles:s,classes:f.bgClass||f.textClass?f:null}}function Md(e,r,o){if(!r.styles||r.styles[0]!=e.state.modeGen){var l=Ul(e,ze(r)),s=r.text.length>e.options.maxHighlightLength&&dr(e.doc.mode,l.state),f=Nd(e,r,l);s&&(l.state=s),r.stateAfter=l.save(!s),r.styles=f.styles,f.classes?r.styleClasses=f.classes:r.styleClasses&&(r.styleClasses=null),o===e.doc.highlightFrontier&&(e.doc.modeFrontier=Math.max(e.doc.modeFrontier,++e.doc.highlightFrontier))}return r.styles}function Ul(e,r,o){var l=e.doc,s=e.display;if(!l.mode.startState)return new Zn(l,!0,r);var f=xm(e,r,o),d=f>l.first&&ce(l,f-1).stateAfter,h=d?Zn.fromSaved(l,d,f):new Zn(l,Bl(l.mode),f);return l.iter(f,r,function(v){ga(e,v.text,h);var m=h.line;v.stateAfter=m==r-1||m%5==0||m>=s.viewFrom&&m<s.viewTo?h.save():null,h.nextLine()}),o&&(l.modeFrontier=h.line),h}function ga(e,r,o,l){var s=e.doc.mode,f=new Ze(r,e.options.tabSize,o);for(f.start=f.pos=l||0,r==""&&bd(s,o.state);!f.eol();)ma(s,f,o.state),f.start=f.pos}function bd(e,r){if(e.blankLine)return e.blankLine(r);if(e.innerMode){var o=Ar(e,r);if(o.mode.blankLine)return o.mode.blankLine(o.state)}}function ma(e,r,o,l){for(var s=0;s<10;s++){l&&(l[0]=Ar(e,o).mode);var f=e.token(r,o);if(r.pos>r.start)return f}throw new Error("Mode "+e.name+" failed to advance stream.")}var _d=function(e,r,o){this.start=e.start,this.end=e.pos,this.string=e.current(),this.type=r||null,this.state=o};function Pd(e,r,o,l){var s=e.doc,f=s.mode,d;r=Se(s,r);var h=ce(s,r.line),v=Ul(e,r.line,o),m=new Ze(h.text,e.options.tabSize,v),x;for(l&&(x=[]);(l||m.pos<r.ch)&&!m.eol();)m.start=m.pos,d=ma(f,m,v.state),l&&x.push(new _d(m,d,dr(s.mode,v.state)));return l?x:new _d(m,d,v.state)}function Od(e,r){if(e)for(;;){var o=e.match(/(?:^|\s+)line-(background-)?(\S+)/);if(!o)break;e=e.slice(0,o.index)+e.slice(o.index+o[0].length);var l=o[1]?"bgClass":"textClass";r[l]==null?r[l]=o[2]:new RegExp("(?:^|\\s)"+o[2]+"(?:$|\\s)").test(r[l])||(r[l]+=" "+o[2])}return e}function Dd(e,r,o,l,s,f,d){var h=o.flattenSpans;h==null&&(h=e.options.flattenSpans);var v=0,m=null,x=new Ze(r,e.options.tabSize,l),T,z=e.options.addModeClass&&[null];for(r==""&&Od(bd(o,l.state),f);!x.eol();){if(x.pos>e.options.maxHighlightLength?(h=!1,d&&ga(e,r,l,x.pos),x.pos=r.length,T=null):T=Od(ma(o,x,l.state,z),f),z){var P=z[0].name;P&&(T="m-"+(T?P+" "+T:P))}if(!h||m!=T){for(;v<x.start;)v=Math.min(x.start,v+5e3),s(v,m);m=T}x.start=x.pos}for(;v<x.pos;){var H=Math.min(x.pos,v+5e3);s(H,m),v=H}}function xm(e,r,o){for(var l,s,f=e.doc,d=o?-1:r-(e.doc.mode.innerMode?1e3:100),h=r;h>d;--h){if(h<=f.first)return f.first;var v=ce(f,h-1),m=v.stateAfter;if(m&&(!o||h+(m instanceof hs?m.lookAhead:0)<=f.modeFrontier))return h;var x=fe(v.text,null,e.options.tabSize);(s==null||l>x)&&(s=h-1,l=x)}return s}function Sm(e,r){if(e.modeFrontier=Math.min(e.modeFrontier,r),!(e.highlightFrontier<r-10)){for(var o=e.first,l=r-1;l>o;l--){var s=ce(e,l).stateAfter;if(s&&(!(s instanceof hs)||l+s.lookAhead<r)){o=l+1;break}}e.highlightFrontier=Math.min(e.highlightFrontier,o)}}var zd=!1,pr=!1;function km(){zd=!0}function Cm(){pr=!0}function ps(e,r,o){this.marker=e,this.from=r,this.to=o}function jl(e,r){if(e)for(var o=0;o<e.length;++o){var l=e[o];if(l.marker==r)return l}}function Tm(e,r){for(var o,l=0;l<e.length;++l)e[l]!=r&&(o||(o=[])).push(e[l]);return o}function Lm(e,r,o){var l=o&&window.WeakSet&&(o.markedSpans||(o.markedSpans=new WeakSet));l&&e.markedSpans&&l.has(e.markedSpans)?e.markedSpans.push(r):(e.markedSpans=e.markedSpans?e.markedSpans.concat([r]):[r],l&&l.add(e.markedSpans)),r.marker.attachLine(e)}function Em(e,r,o){var l;if(e)for(var s=0;s<e.length;++s){var f=e[s],d=f.marker,h=f.from==null||(d.inclusiveLeft?f.from<=r:f.from<r);if(h||f.from==r&&d.type=="bookmark"&&(!o||!f.marker.insertLeft)){var v=f.to==null||(d.inclusiveRight?f.to>=r:f.to>r);(l||(l=[])).push(new ps(d,f.from,v?null:f.to))}}return l}function Nm(e,r,o){var l;if(e)for(var s=0;s<e.length;++s){var f=e[s],d=f.marker,h=f.to==null||(d.inclusiveRight?f.to>=r:f.to>r);if(h||f.from==r&&d.type=="bookmark"&&(!o||f.marker.insertLeft)){var v=f.from==null||(d.inclusiveLeft?f.from<=r:f.from<r);(l||(l=[])).push(new ps(d,v?null:f.from-r,f.to==null?null:f.to-r))}}return l}function ya(e,r){if(r.full)return null;var o=w(e,r.from.line)&&ce(e,r.from.line).markedSpans,l=w(e,r.to.line)&&ce(e,r.to.line).markedSpans;if(!o&&!l)return null;var s=r.from.ch,f=r.to.ch,d=R(r.from,r.to)==0,h=Em(o,s,d),v=Nm(l,f,d),m=r.text.length==1,x=Ne(r.text).length+(m?s:0);if(h)for(var T=0;T<h.length;++T){var z=h[T];if(z.to==null){var P=jl(v,z.marker);P?m&&(z.to=P.to==null?null:P.to+x):z.to=s}}if(v)for(var H=0;H<v.length;++H){var j=v[H];if(j.to!=null&&(j.to+=x),j.from==null){var V=jl(h,j.marker);V||(j.from=x,m&&(h||(h=[])).push(j))}else j.from+=x,m&&(h||(h=[])).push(j)}h&&(h=Id(h)),v&&v!=h&&(v=Id(v));var ee=[h];if(!m){var le=r.text.length-2,te;if(le>0&&h)for(var se=0;se<h.length;++se)h[se].to==null&&(te||(te=[])).push(new ps(h[se].marker,null,null));for(var ve=0;ve<le;++ve)ee.push(te);ee.push(v)}return ee}function Id(e){for(var r=0;r<e.length;++r){var o=e[r];o.from!=null&&o.from==o.to&&o.marker.clearWhenEmpty!==!1&&e.splice(r--,1)}return e.length?e:null}function Mm(e,r,o){var l=null;if(e.iter(r.line,o.line+1,function(P){if(P.markedSpans)for(var H=0;H<P.markedSpans.length;++H){var j=P.markedSpans[H].marker;j.readOnly&&(!l||Le(l,j)==-1)&&(l||(l=[])).push(j)}}),!l)return null;for(var s=[{from:r,to:o}],f=0;f<l.length;++f)for(var d=l[f],h=d.find(0),v=0;v<s.length;++v){var m=s[v];if(!(R(m.to,h.from)<0||R(m.from,h.to)>0)){var x=[v,1],T=R(m.from,h.from),z=R(m.to,h.to);(T<0||!d.inclusiveLeft&&!T)&&x.push({from:m.from,to:h.from}),(z>0||!d.inclusiveRight&&!z)&&x.push({from:h.to,to:m.to}),s.splice.apply(s,x),v+=x.length-3}}return s}function Ad(e){var r=e.markedSpans;if(r){for(var o=0;o<r.length;++o)r[o].marker.detachLine(e);e.markedSpans=null}}function Fd(e,r){if(r){for(var o=0;o<r.length;++o)r[o].marker.attachLine(e);e.markedSpans=r}}function vs(e){return e.inclusiveLeft?-1:0}function gs(e){return e.inclusiveRight?1:0}function wa(e,r){var o=e.lines.length-r.lines.length;if(o!=0)return o;var l=e.find(),s=r.find(),f=R(l.from,s.from)||vs(e)-vs(r);if(f)return-f;var d=R(l.to,s.to)||gs(e)-gs(r);return d||r.id-e.id}function Rd(e,r){var o=pr&&e.markedSpans,l;if(o)for(var s=void 0,f=0;f<o.length;++f)s=o[f],s.marker.collapsed&&(r?s.from:s.to)==null&&(!l||wa(l,s.marker)<0)&&(l=s.marker);return l}function Wd(e){return Rd(e,!0)}function ms(e){return Rd(e,!1)}function bm(e,r){var o=pr&&e.markedSpans,l;if(o)for(var s=0;s<o.length;++s){var f=o[s];f.marker.collapsed&&(f.from==null||f.from<r)&&(f.to==null||f.to>r)&&(!l||wa(l,f.marker)<0)&&(l=f.marker)}return l}function Hd(e,r,o,l,s){var f=ce(e,r),d=pr&&f.markedSpans;if(d)for(var h=0;h<d.length;++h){var v=d[h];if(v.marker.collapsed){var m=v.marker.find(0),x=R(m.from,o)||vs(v.marker)-vs(s),T=R(m.to,l)||gs(v.marker)-gs(s);if(!(x>=0&&T<=0||x<=0&&T>=0)&&(x<=0&&(v.marker.inclusiveRight&&s.inclusiveLeft?R(m.to,o)>=0:R(m.to,o)>0)||x>=0&&(v.marker.inclusiveRight&&s.inclusiveLeft?R(m.from,l)<=0:R(m.from,l)<0)))return!0}}}function In(e){for(var r;r=Wd(e);)e=r.find(-1,!0).line;return e}function _m(e){for(var r;r=ms(e);)e=r.find(1,!0).line;return e}function Pm(e){for(var r,o;r=ms(e);)e=r.find(1,!0).line,(o||(o=[])).push(e);return o}function xa(e,r){var o=ce(e,r),l=In(o);return o==l?r:ze(l)}function Bd(e,r){if(r>e.lastLine())return r;var o=ce(e,r),l;if(!Fr(e,o))return r;for(;l=ms(o);)o=l.find(1,!0).line;return ze(o)+1}function Fr(e,r){var o=pr&&r.markedSpans;if(o){for(var l=void 0,s=0;s<o.length;++s)if(l=o[s],!!l.marker.collapsed){if(l.from==null)return!0;if(!l.marker.widgetNode&&l.from==0&&l.marker.inclusiveLeft&&Sa(e,r,l))return!0}}}function Sa(e,r,o){if(o.to==null){var l=o.marker.find(1,!0);return Sa(e,l.line,jl(l.line.markedSpans,o.marker))}if(o.marker.inclusiveRight&&o.to==r.text.length)return!0;for(var s=void 0,f=0;f<r.markedSpans.length;++f)if(s=r.markedSpans[f],s.marker.collapsed&&!s.marker.widgetNode&&s.from==o.to&&(s.to==null||s.to!=o.from)&&(s.marker.inclusiveLeft||o.marker.inclusiveRight)&&Sa(e,r,s))return!0}function vr(e){e=In(e);for(var r=0,o=e.parent,l=0;l<o.lines.length;++l){var s=o.lines[l];if(s==e)break;r+=s.height}for(var f=o.parent;f;o=f,f=o.parent)for(var d=0;d<f.children.length;++d){var h=f.children[d];if(h==o)break;r+=h.height}return r}function ys(e){if(e.height==0)return 0;for(var r=e.text.length,o,l=e;o=Wd(l);){var s=o.find(0,!0);l=s.from.line,r+=s.from.ch-s.to.ch}for(l=e;o=ms(l);){var f=o.find(0,!0);r-=l.text.length-f.from.ch,l=f.to.line,r+=l.text.length-f.to.ch}return r}function ka(e){var r=e.display,o=e.doc;r.maxLine=ce(o,o.first),r.maxLineLength=ys(r.maxLine),r.maxLineChanged=!0,o.iter(function(l){var s=ys(l);s>r.maxLineLength&&(r.maxLineLength=s,r.maxLine=l)})}var Hi=function(e,r,o){this.text=e,Fd(this,r),this.height=o?o(this):1};Hi.prototype.lineNo=function(){return ze(this)},zt(Hi);function Om(e,r,o,l){e.text=r,e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null),e.order!=null&&(e.order=null),Ad(e),Fd(e,o);var s=l?l(e):1;s!=e.height&&Ln(e,s)}function Dm(e){e.parent=null,Ad(e)}var zm={},Im={};function Ud(e,r){if(!e||/^\s*$/.test(e))return null;var o=r.addModeClass?Im:zm;return o[e]||(o[e]=e.replace(/\S+/g,"cm-$&"))}function jd(e,r){var o=Q("span",null,null,N?"padding-right: .1px":null),l={pre:Q("pre",[o],"CodeMirror-line"),content:o,col:0,pos:0,cm:e,trailingSpace:!1,splitSpaces:e.getOption("lineWrapping")};r.measure={};for(var s=0;s<=(r.rest?r.rest.length:0);s++){var f=s?r.rest[s-1]:r.line,d=void 0;l.pos=0,l.addToken=Fm,as(e.display.measure)&&(d=Cn(f,e.doc.direction))&&(l.addToken=Wm(l.addToken,d)),l.map=[];var h=r!=e.display.externalMeasured&&ze(f);Hm(f,l,Md(e,f,h)),f.styleClasses&&(f.styleClasses.bgClass&&(l.bgClass=J(f.styleClasses.bgClass,l.bgClass||"")),f.styleClasses.textClass&&(l.textClass=J(f.styleClasses.textClass,l.textClass||""))),l.map.length==0&&l.map.push(0,0,l.content.appendChild(va(e.display.measure))),s==0?(r.measure.map=l.map,r.measure.cache={}):((r.measure.maps||(r.measure.maps=[])).push(l.map),(r.measure.caches||(r.measure.caches=[])).push({}))}if(N){var v=l.content.lastChild;(/\bcm-tab\b/.test(v.className)||v.querySelector&&v.querySelector(".cm-tab"))&&(l.content.className="cm-tab-wrap-hack")}return Ue(e,"renderLine",e,r.line,l.pre),l.pre.className&&(l.textClass=J(l.pre.className,l.textClass||"")),l}function Am(e){var r=M("span","•","cm-invalidchar");return r.title="\\u"+e.charCodeAt(0).toString(16),r.setAttribute("aria-label",r.title),r}function Fm(e,r,o,l,s,f,d){if(r){var h=e.splitSpaces?Rm(r,e.trailingSpace):r,v=e.cm.state.specialChars,m=!1,x;if(!v.test(r))e.col+=r.length,x=document.createTextNode(h),e.map.push(e.pos,e.pos+r.length,x),y&&S<9&&(m=!0),e.pos+=r.length;else{x=document.createDocumentFragment();for(var T=0;;){v.lastIndex=T;var z=v.exec(r),P=z?z.index-T:r.length-T;if(P){var H=document.createTextNode(h.slice(T,T+P));y&&S<9?x.appendChild(M("span",[H])):x.appendChild(H),e.map.push(e.pos,e.pos+P,H),e.col+=P,e.pos+=P}if(!z)break;T+=P+1;var j=void 0;if(z[0]=="	"){var V=e.cm.options.tabSize,ee=V-e.col%V;j=x.appendChild(M("span",cn(ee),"cm-tab")),j.setAttribute("role","presentation"),j.setAttribute("cm-text","	"),e.col+=ee}else z[0]=="\r"||z[0]==`
`?(j=x.appendChild(M("span",z[0]=="\r"?"␍":"␤","cm-invalidchar")),j.setAttribute("cm-text",z[0]),e.col+=1):(j=e.cm.options.specialCharPlaceholder(z[0]),j.setAttribute("cm-text",z[0]),y&&S<9?x.appendChild(M("span",[j])):x.appendChild(j),e.col+=1);e.map.push(e.pos,e.pos+1,j),e.pos++}}if(e.trailingSpace=h.charCodeAt(r.length-1)==32,o||l||s||m||f||d){var le=o||"";l&&(le+=l),s&&(le+=s);var te=M("span",[x],le,f);if(d)for(var se in d)d.hasOwnProperty(se)&&se!="style"&&se!="class"&&te.setAttribute(se,d[se]);return e.content.appendChild(te)}e.content.appendChild(x)}}function Rm(e,r){if(e.length>1&&!/  /.test(e))return e;for(var o=r,l="",s=0;s<e.length;s++){var f=e.charAt(s);f==" "&&o&&(s==e.length-1||e.charCodeAt(s+1)==32)&&(f=" "),l+=f,o=f==" "}return l}function Wm(e,r){return function(o,l,s,f,d,h,v){s=s?s+" cm-force-border":"cm-force-border";for(var m=o.pos,x=m+l.length;;){for(var T=void 0,z=0;z<r.length&&(T=r[z],!(T.to>m&&T.from<=m));z++);if(T.to>=x)return e(o,l,s,f,d,h,v);e(o,l.slice(0,T.to-m),s,f,null,h,v),f=null,l=l.slice(T.to-m),m=T.to}}}function $d(e,r,o,l){var s=!l&&o.widgetNode;s&&e.map.push(e.pos,e.pos+r,s),!l&&e.cm.display.input.needsContentAttribute&&(s||(s=e.content.appendChild(document.createElement("span"))),s.setAttribute("cm-marker",o.id)),s&&(e.cm.display.input.setUneditable(s),e.content.appendChild(s)),e.pos+=r,e.trailingSpace=!1}function Hm(e,r,o){var l=e.markedSpans,s=e.text,f=0;if(!l){for(var d=1;d<o.length;d+=2)r.addToken(r,s.slice(f,f=o[d]),Ud(o[d+1],r.cm.options));return}for(var h=s.length,v=0,m=1,x="",T,z,P=0,H,j,V,ee,le;;){if(P==v){H=j=V=z="",le=null,ee=null,P=1/0;for(var te=[],se=void 0,ve=0;ve<l.length;++ve){var pe=l[ve],Ee=pe.marker;if(Ee.type=="bookmark"&&pe.from==v&&Ee.widgetNode)te.push(Ee);else if(pe.from<=v&&(pe.to==null||pe.to>v||Ee.collapsed&&pe.to==v&&pe.from==v)){if(pe.to!=null&&pe.to!=v&&P>pe.to&&(P=pe.to,j=""),Ee.className&&(H+=" "+Ee.className),Ee.css&&(z=(z?z+";":"")+Ee.css),Ee.startStyle&&pe.from==v&&(V+=" "+Ee.startStyle),Ee.endStyle&&pe.to==P&&(se||(se=[])).push(Ee.endStyle,pe.to),Ee.title&&((le||(le={})).title=Ee.title),Ee.attributes)for(var He in Ee.attributes)(le||(le={}))[He]=Ee.attributes[He];Ee.collapsed&&(!ee||wa(ee.marker,Ee)<0)&&(ee=pe)}else pe.from>v&&P>pe.from&&(P=pe.from)}if(se)for(var yt=0;yt<se.length;yt+=2)se[yt+1]==P&&(j+=" "+se[yt]);if(!ee||ee.from==v)for(var Ye=0;Ye<te.length;++Ye)$d(r,0,te[Ye]);if(ee&&(ee.from||0)==v){if($d(r,(ee.to==null?h+1:ee.to)-v,ee.marker,ee.from==null),ee.to==null)return;ee.to==v&&(ee=!1)}}if(v>=h)break;for(var nn=Math.min(h,P);;){if(x){var Gt=v+x.length;if(!ee){var ot=Gt>nn?x.slice(0,nn-v):x;r.addToken(r,ot,T?T+H:H,V,v+ot.length==P?j:"",z,le)}if(Gt>=nn){x=x.slice(nn-v),v=nn;break}v=Gt,V=""}x=s.slice(f,f=o[m++]),T=Ud(o[m++],r.cm.options)}}}function Kd(e,r,o){this.line=r,this.rest=Pm(r),this.size=this.rest?ze(Ne(this.rest))-o+1:1,this.node=this.text=null,this.hidden=Fr(e,r)}function ws(e,r,o){for(var l=[],s,f=r;f<o;f=s){var d=new Kd(e.doc,ce(e.doc,f),f);s=f+d.size,l.push(d)}return l}var Bi=null;function Bm(e){Bi?Bi.ops.push(e):e.ownsGroup=Bi={ops:[e],delayedCallbacks:[]}}function Um(e){var r=e.delayedCallbacks,o=0;do{for(;o<r.length;o++)r[o].call(null);for(var l=0;l<e.ops.length;l++){var s=e.ops[l];if(s.cursorActivityHandlers)for(;s.cursorActivityCalled<s.cursorActivityHandlers.length;)s.cursorActivityHandlers[s.cursorActivityCalled++].call(null,s.cm)}}while(o<r.length)}function jm(e,r){var o=e.ownsGroup;if(o)try{Um(o)}finally{Bi=null,r(o)}}var $l=null;function vt(e,r){var o=Fl(e,r);if(o.length){var l=Array.prototype.slice.call(arguments,2),s;Bi?s=Bi.delayedCallbacks:$l?s=$l:(s=$l=[],setTimeout($m,0));for(var f=function(h){s.push(function(){return o[h].apply(null,l)})},d=0;d<o.length;++d)f(d)}}function $m(){var e=$l;$l=null;for(var r=0;r<e.length;++r)e[r]()}function Gd(e,r,o,l){for(var s=0;s<r.changes.length;s++){var f=r.changes[s];f=="text"?Gm(e,r):f=="gutter"?Vd(e,r,o,l):f=="class"?Ca(e,r):f=="widget"&&Qm(e,r,l)}r.changes=null}function Kl(e){return e.node==e.text&&(e.node=M("div",null,null,"position: relative"),e.text.parentNode&&e.text.parentNode.replaceChild(e.node,e.text),e.node.appendChild(e.text),y&&S<8&&(e.node.style.zIndex=2)),e.node}function Km(e,r){var o=r.bgClass?r.bgClass+" "+(r.line.bgClass||""):r.line.bgClass;if(o&&(o+=" CodeMirror-linebackground"),r.background)o?r.background.className=o:(r.background.parentNode.removeChild(r.background),r.background=null);else if(o){var l=Kl(r);r.background=l.insertBefore(M("div",null,o),l.firstChild),e.display.input.setUneditable(r.background)}}function Qd(e,r){var o=e.display.externalMeasured;return o&&o.line==r.line?(e.display.externalMeasured=null,r.measure=o.measure,o.built):jd(e,r)}function Gm(e,r){var o=r.text.className,l=Qd(e,r);r.text==r.node&&(r.node=l.pre),r.text.parentNode.replaceChild(l.pre,r.text),r.text=l.pre,l.bgClass!=r.bgClass||l.textClass!=r.textClass?(r.bgClass=l.bgClass,r.textClass=l.textClass,Ca(e,r)):o&&(r.text.className=o)}function Ca(e,r){Km(e,r),r.line.wrapClass?Kl(r).className=r.line.wrapClass:r.node!=r.text&&(r.node.className="");var o=r.textClass?r.textClass+" "+(r.line.textClass||""):r.line.textClass;r.text.className=o||""}function Vd(e,r,o,l){if(r.gutter&&(r.node.removeChild(r.gutter),r.gutter=null),r.gutterBackground&&(r.node.removeChild(r.gutterBackground),r.gutterBackground=null),r.line.gutterClass){var s=Kl(r);r.gutterBackground=M("div",null,"CodeMirror-gutter-background "+r.line.gutterClass,"left: "+(e.options.fixedGutter?l.fixedPos:-l.gutterTotalWidth)+"px; width: "+l.gutterTotalWidth+"px"),e.display.input.setUneditable(r.gutterBackground),s.insertBefore(r.gutterBackground,r.text)}var f=r.line.gutterMarkers;if(e.options.lineNumbers||f){var d=Kl(r),h=r.gutter=M("div",null,"CodeMirror-gutter-wrapper","left: "+(e.options.fixedGutter?l.fixedPos:-l.gutterTotalWidth)+"px");if(h.setAttribute("aria-hidden","true"),e.display.input.setUneditable(h),d.insertBefore(h,r.text),r.line.gutterClass&&(h.className+=" "+r.line.gutterClass),e.options.lineNumbers&&(!f||!f["CodeMirror-linenumbers"])&&(r.lineNumber=h.appendChild(M("div",L(e.options,o),"CodeMirror-linenumber CodeMirror-gutter-elt","left: "+l.gutterLeft["CodeMirror-linenumbers"]+"px; width: "+e.display.lineNumInnerWidth+"px"))),f)for(var v=0;v<e.display.gutterSpecs.length;++v){var m=e.display.gutterSpecs[v].className,x=f.hasOwnProperty(m)&&f[m];x&&h.appendChild(M("div",[x],"CodeMirror-gutter-elt","left: "+l.gutterLeft[m]+"px; width: "+l.gutterWidth[m]+"px"))}}}function Qm(e,r,o){r.alignable&&(r.alignable=null);for(var l=Re("CodeMirror-linewidget"),s=r.node.firstChild,f=void 0;s;s=f)f=s.nextSibling,l.test(s.className)&&r.node.removeChild(s);Yd(e,r,o)}function Vm(e,r,o,l){var s=Qd(e,r);return r.text=r.node=s.pre,s.bgClass&&(r.bgClass=s.bgClass),s.textClass&&(r.textClass=s.textClass),Ca(e,r),Vd(e,r,o,l),Yd(e,r,l),r.node}function Yd(e,r,o){if(qd(e,r.line,r,o,!0),r.rest)for(var l=0;l<r.rest.length;l++)qd(e,r.rest[l],r,o,!1)}function qd(e,r,o,l,s){if(r.widgets)for(var f=Kl(o),d=0,h=r.widgets;d<h.length;++d){var v=h[d],m=M("div",[v.node],"CodeMirror-linewidget"+(v.className?" "+v.className:""));v.handleMouseEvents||m.setAttribute("cm-ignore-events","true"),Ym(v,m,o,l),e.display.input.setUneditable(m),s&&v.above?f.insertBefore(m,o.gutter||o.text):f.appendChild(m),vt(v,"redraw")}}function Ym(e,r,o,l){if(e.noHScroll){(o.alignable||(o.alignable=[])).push(r);var s=l.wrapperWidth;r.style.left=l.fixedPos+"px",e.coverGutter||(s-=l.gutterTotalWidth,r.style.paddingLeft=l.gutterTotalWidth+"px"),r.style.width=s+"px"}e.coverGutter&&(r.style.zIndex=5,r.style.position="relative",e.noHScroll||(r.style.marginLeft=-l.gutterTotalWidth+"px"))}function Gl(e){if(e.height!=null)return e.height;var r=e.doc.cm;if(!r)return 0;if(!k(document.body,e.node)){var o="position: relative;";e.coverGutter&&(o+="margin-left: -"+r.display.gutters.offsetWidth+"px;"),e.noHScroll&&(o+="width: "+r.display.wrapper.clientWidth+"px;"),ne(r.display.measure,M("div",[e.node],null,o))}return e.height=e.node.parentNode.offsetHeight}function gr(e,r){for(var o=jt(r);o!=e.wrapper;o=o.parentNode)if(!o||o.nodeType==1&&o.getAttribute("cm-ignore-events")=="true"||o.parentNode==e.sizer&&o!=e.mover)return!0}function xs(e){return e.lineSpace.offsetTop}function Ta(e){return e.mover.offsetHeight-e.lineSpace.offsetHeight}function Xd(e){if(e.cachedPaddingH)return e.cachedPaddingH;var r=ne(e.measure,M("pre","x","CodeMirror-line-like")),o=window.getComputedStyle?window.getComputedStyle(r):r.currentStyle,l={left:parseInt(o.paddingLeft),right:parseInt(o.paddingRight)};return!isNaN(l.left)&&!isNaN(l.right)&&(e.cachedPaddingH=l),l}function Jn(e){return Ai-e.display.nativeBarWidth}function hi(e){return e.display.scroller.clientWidth-Jn(e)-e.display.barWidth}function La(e){return e.display.scroller.clientHeight-Jn(e)-e.display.barHeight}function qm(e,r,o){var l=e.options.lineWrapping,s=l&&hi(e);if(!r.measure.heights||l&&r.measure.width!=s){var f=r.measure.heights=[];if(l){r.measure.width=s;for(var d=r.text.firstChild.getClientRects(),h=0;h<d.length-1;h++){var v=d[h],m=d[h+1];Math.abs(v.bottom-m.bottom)>2&&f.push((v.bottom+m.top)/2-o.top)}}f.push(o.bottom-o.top)}}function Zd(e,r,o){if(e.line==r)return{map:e.measure.map,cache:e.measure.cache};if(e.rest){for(var l=0;l<e.rest.length;l++)if(e.rest[l]==r)return{map:e.measure.maps[l],cache:e.measure.caches[l]};for(var s=0;s<e.rest.length;s++)if(ze(e.rest[s])>o)return{map:e.measure.maps[s],cache:e.measure.caches[s],before:!0}}}function Xm(e,r){r=In(r);var o=ze(r),l=e.display.externalMeasured=new Kd(e.doc,r,o);l.lineN=o;var s=l.built=jd(e,l);return l.text=s.pre,ne(e.display.lineMeasure,s.pre),l}function Jd(e,r,o,l){return er(e,Ui(e,r),o,l)}function Ea(e,r){if(r>=e.display.viewFrom&&r<e.display.viewTo)return e.display.view[gi(e,r)];var o=e.display.externalMeasured;if(o&&r>=o.lineN&&r<o.lineN+o.size)return o}function Ui(e,r){var o=ze(r),l=Ea(e,o);l&&!l.text?l=null:l&&l.changes&&(Gd(e,l,o,Pa(e)),e.curOp.forceUpdate=!0),l||(l=Xm(e,r));var s=Zd(l,r,o);return{line:r,view:l,rect:null,map:s.map,cache:s.cache,before:s.before,hasHeights:!1}}function er(e,r,o,l,s){r.before&&(o=-1);var f=o+(l||""),d;return r.cache.hasOwnProperty(f)?d=r.cache[f]:(r.rect||(r.rect=r.view.text.getBoundingClientRect()),r.hasHeights||(qm(e,r.view,r.rect),r.hasHeights=!0),d=Jm(e,r,o,l),d.bogus||(r.cache[f]=d)),{left:d.left,right:d.right,top:s?d.rtop:d.top,bottom:s?d.rbottom:d.bottom}}var eh={left:0,right:0,top:0,bottom:0};function th(e,r,o){for(var l,s,f,d,h,v,m=0;m<e.length;m+=3)if(h=e[m],v=e[m+1],r<h?(s=0,f=1,d="left"):r<v?(s=r-h,f=s+1):(m==e.length-3||r==v&&e[m+3]>r)&&(f=v-h,s=f-1,r>=v&&(d="right")),s!=null){if(l=e[m+2],h==v&&o==(l.insertLeft?"left":"right")&&(d=o),o=="left"&&s==0)for(;m&&e[m-2]==e[m-3]&&e[m-1].insertLeft;)l=e[(m-=3)+2],d="left";if(o=="right"&&s==v-h)for(;m<e.length-3&&e[m+3]==e[m+4]&&!e[m+5].insertLeft;)l=e[(m+=3)+2],d="right";break}return{node:l,start:s,end:f,collapse:d,coverStart:h,coverEnd:v}}function Zm(e,r){var o=eh;if(r=="left")for(var l=0;l<e.length&&(o=e[l]).left==o.right;l++);else for(var s=e.length-1;s>=0&&(o=e[s]).left==o.right;s--);return o}function Jm(e,r,o,l){var s=th(r.map,o,l),f=s.node,d=s.start,h=s.end,v=s.collapse,m;if(f.nodeType==3){for(var x=0;x<4;x++){for(;d&&de(r.line.text.charAt(s.coverStart+d));)--d;for(;s.coverStart+h<s.coverEnd&&de(r.line.text.charAt(s.coverStart+h));)++h;if(y&&S<9&&d==0&&h==s.coverEnd-s.coverStart?m=f.parentNode.getBoundingClientRect():m=Zm(X(f,d,h).getClientRects(),l),m.left||m.right||d==0)break;h=d,d=d-1,v="right"}y&&S<11&&(m=ey(e.display.measure,m))}else{d>0&&(v=l="right");var T;e.options.lineWrapping&&(T=f.getClientRects()).length>1?m=T[l=="right"?T.length-1:0]:m=f.getBoundingClientRect()}if(y&&S<9&&!d&&(!m||!m.left&&!m.right)){var z=f.parentNode.getClientRects()[0];z?m={left:z.left,right:z.left+$i(e.display),top:z.top,bottom:z.bottom}:m=eh}for(var P=m.top-r.rect.top,H=m.bottom-r.rect.top,j=(P+H)/2,V=r.view.measure.heights,ee=0;ee<V.length-1&&!(j<V[ee]);ee++);var le=ee?V[ee-1]:0,te=V[ee],se={left:(v=="right"?m.right:m.left)-r.rect.left,right:(v=="left"?m.left:m.right)-r.rect.left,top:le,bottom:te};return!m.left&&!m.right&&(se.bogus=!0),e.options.singleCursorHeightPerLine||(se.rtop=P,se.rbottom=H),se}function ey(e,r){if(!window.screen||screen.logicalXDPI==null||screen.logicalXDPI==screen.deviceXDPI||!fs(e))return r;var o=screen.logicalXDPI/screen.deviceXDPI,l=screen.logicalYDPI/screen.deviceYDPI;return{left:r.left*o,right:r.right*o,top:r.top*l,bottom:r.bottom*l}}function nh(e){if(e.measure&&(e.measure.cache={},e.measure.heights=null,e.rest))for(var r=0;r<e.rest.length;r++)e.measure.caches[r]={}}function rh(e){e.display.externalMeasure=null,A(e.display.lineMeasure);for(var r=0;r<e.display.view.length;r++)nh(e.display.view[r])}function Ql(e){rh(e),e.display.cachedCharWidth=e.display.cachedTextHeight=e.display.cachedPaddingH=null,e.options.lineWrapping||(e.display.maxLineChanged=!0),e.display.lineNumChars=null}function ih(e){return O&&D?-(e.body.getBoundingClientRect().left-parseInt(getComputedStyle(e.body).marginLeft)):e.defaultView.pageXOffset||(e.documentElement||e.body).scrollLeft}function lh(e){return O&&D?-(e.body.getBoundingClientRect().top-parseInt(getComputedStyle(e.body).marginTop)):e.defaultView.pageYOffset||(e.documentElement||e.body).scrollTop}function Na(e){var r=In(e),o=r.widgets,l=0;if(o)for(var s=0;s<o.length;++s)o[s].above&&(l+=Gl(o[s]));return l}function Ss(e,r,o,l,s){if(!s){var f=Na(r);o.top+=f,o.bottom+=f}if(l=="line")return o;l||(l="local");var d=vr(r);if(l=="local"?d+=xs(e.display):d-=e.display.viewOffset,l=="page"||l=="window"){var h=e.display.lineSpace.getBoundingClientRect();d+=h.top+(l=="window"?0:lh(ye(e)));var v=h.left+(l=="window"?0:ih(ye(e)));o.left+=v,o.right+=v}return o.top+=d,o.bottom+=d,o}function oh(e,r,o){if(o=="div")return r;var l=r.left,s=r.top;if(o=="page")l-=ih(ye(e)),s-=lh(ye(e));else if(o=="local"||!o){var f=e.display.sizer.getBoundingClientRect();l+=f.left,s+=f.top}var d=e.display.lineSpace.getBoundingClientRect();return{left:l-d.left,top:s-d.top}}function ks(e,r,o,l,s){return l||(l=ce(e.doc,r.line)),Ss(e,l,Jd(e,l,r.ch,s),o)}function An(e,r,o,l,s,f){l=l||ce(e.doc,r.line),s||(s=Ui(e,l));function d(H,j){var V=er(e,s,H,j?"right":"left",f);return j?V.left=V.right:V.right=V.left,Ss(e,l,V,o)}var h=Cn(l,e.doc.direction),v=r.ch,m=r.sticky;if(v>=l.text.length?(v=l.text.length,m="before"):v<=0&&(v=0,m="after"),!h)return d(m=="before"?v-1:v,m=="before");function x(H,j,V){var ee=h[j],le=ee.level==1;return d(V?H-1:H,le!=V)}var T=kn(h,v,m),z=Sn,P=x(v,T,m=="before");return z!=null&&(P.other=x(v,z,m!="before")),P}function sh(e,r){var o=0;r=Se(e.doc,r),e.options.lineWrapping||(o=$i(e.display)*r.ch);var l=ce(e.doc,r.line),s=vr(l)+xs(e.display);return{left:o,right:o,top:s,bottom:s+l.height}}function Ma(e,r,o,l,s){var f=b(e,r,o);return f.xRel=s,l&&(f.outside=l),f}function ba(e,r,o){var l=e.doc;if(o+=e.display.viewOffset,o<0)return Ma(l.first,0,null,-1,-1);var s=Xn(l,o),f=l.first+l.size-1;if(s>f)return Ma(l.first+l.size-1,ce(l,f).text.length,null,1,1);r<0&&(r=0);for(var d=ce(l,s);;){var h=ty(e,d,s,r,o),v=bm(d,h.ch+(h.xRel>0||h.outside>0?1:0));if(!v)return h;var m=v.find(1);if(m.line==s)return m;d=ce(l,s=m.line)}}function uh(e,r,o,l){l-=Na(r);var s=r.text.length,f=Te(function(d){return er(e,o,d-1).bottom<=l},s,0);return s=Te(function(d){return er(e,o,d).top>l},f,s),{begin:f,end:s}}function ah(e,r,o,l){o||(o=Ui(e,r));var s=Ss(e,r,er(e,o,l),"line").top;return uh(e,r,o,s)}function _a(e,r,o,l){return e.bottom<=o?!1:e.top>o?!0:(l?e.left:e.right)>r}function ty(e,r,o,l,s){s-=vr(r);var f=Ui(e,r),d=Na(r),h=0,v=r.text.length,m=!0,x=Cn(r,e.doc.direction);if(x){var T=(e.options.lineWrapping?ry:ny)(e,r,o,f,x,l,s);m=T.level!=1,h=m?T.from:T.to-1,v=m?T.to:T.from-1}var z=null,P=null,H=Te(function(ve){var pe=er(e,f,ve);return pe.top+=d,pe.bottom+=d,_a(pe,l,s,!1)?(pe.top<=s&&pe.left<=l&&(z=ve,P=pe),!0):!1},h,v),j,V,ee=!1;if(P){var le=l-P.left<P.right-l,te=le==m;H=z+(te?0:1),V=te?"after":"before",j=le?P.left:P.right}else{!m&&(H==v||H==h)&&H++,V=H==0?"after":H==r.text.length?"before":er(e,f,H-(m?1:0)).bottom+d<=s==m?"after":"before";var se=An(e,b(o,H,V),"line",r,f);j=se.left,ee=s<se.top?-1:s>=se.bottom?1:0}return H=Me(r.text,H,1),Ma(o,H,V,ee,l-j)}function ny(e,r,o,l,s,f,d){var h=Te(function(T){var z=s[T],P=z.level!=1;return _a(An(e,b(o,P?z.to:z.from,P?"before":"after"),"line",r,l),f,d,!0)},0,s.length-1),v=s[h];if(h>0){var m=v.level!=1,x=An(e,b(o,m?v.from:v.to,m?"after":"before"),"line",r,l);_a(x,f,d,!0)&&x.top>d&&(v=s[h-1])}return v}function ry(e,r,o,l,s,f,d){var h=uh(e,r,l,d),v=h.begin,m=h.end;/\s/.test(r.text.charAt(m-1))&&m--;for(var x=null,T=null,z=0;z<s.length;z++){var P=s[z];if(!(P.from>=m||P.to<=v)){var H=P.level!=1,j=er(e,l,H?Math.min(m,P.to)-1:Math.max(v,P.from)).right,V=j<f?f-j+1e9:j-f;(!x||T>V)&&(x=P,T=V)}}return x||(x=s[s.length-1]),x.from<v&&(x={from:v,to:x.to,level:x.level}),x.to>m&&(x={from:x.from,to:m,level:x.level}),x}var pi;function ji(e){if(e.cachedTextHeight!=null)return e.cachedTextHeight;if(pi==null){pi=M("pre",null,"CodeMirror-line-like");for(var r=0;r<49;++r)pi.appendChild(document.createTextNode("x")),pi.appendChild(M("br"));pi.appendChild(document.createTextNode("x"))}ne(e.measure,pi);var o=pi.offsetHeight/50;return o>3&&(e.cachedTextHeight=o),A(e.measure),o||1}function $i(e){if(e.cachedCharWidth!=null)return e.cachedCharWidth;var r=M("span","xxxxxxxxxx"),o=M("pre",[r],"CodeMirror-line-like");ne(e.measure,o);var l=r.getBoundingClientRect(),s=(l.right-l.left)/10;return s>2&&(e.cachedCharWidth=s),s||10}function Pa(e){for(var r=e.display,o={},l={},s=r.gutters.clientLeft,f=r.gutters.firstChild,d=0;f;f=f.nextSibling,++d){var h=e.display.gutterSpecs[d].className;o[h]=f.offsetLeft+f.clientLeft+s,l[h]=f.clientWidth}return{fixedPos:Oa(r),gutterTotalWidth:r.gutters.offsetWidth,gutterLeft:o,gutterWidth:l,wrapperWidth:r.wrapper.clientWidth}}function Oa(e){return e.scroller.getBoundingClientRect().left-e.sizer.getBoundingClientRect().left}function fh(e){var r=ji(e.display),o=e.options.lineWrapping,l=o&&Math.max(5,e.display.scroller.clientWidth/$i(e.display)-3);return function(s){if(Fr(e.doc,s))return 0;var f=0;if(s.widgets)for(var d=0;d<s.widgets.length;d++)s.widgets[d].height&&(f+=s.widgets[d].height);return o?f+(Math.ceil(s.text.length/l)||1)*r:f+r}}function Da(e){var r=e.doc,o=fh(e);r.iter(function(l){var s=o(l);s!=l.height&&Ln(l,s)})}function vi(e,r,o,l){var s=e.display;if(!o&&jt(r).getAttribute("cm-not-content")=="true")return null;var f,d,h=s.lineSpace.getBoundingClientRect();try{f=r.clientX-h.left,d=r.clientY-h.top}catch{return null}var v=ba(e,f,d),m;if(l&&v.xRel>0&&(m=ce(e.doc,v.line).text).length==v.ch){var x=fe(m,m.length,e.options.tabSize)-m.length;v=b(v.line,Math.max(0,Math.round((f-Xd(e.display).left)/$i(e.display))-x))}return v}function gi(e,r){if(r>=e.display.viewTo||(r-=e.display.viewFrom,r<0))return null;for(var o=e.display.view,l=0;l<o.length;l++)if(r-=o[l].size,r<0)return l}function $t(e,r,o,l){r==null&&(r=e.doc.first),o==null&&(o=e.doc.first+e.doc.size),l||(l=0);var s=e.display;if(l&&o<s.viewTo&&(s.updateLineNumbers==null||s.updateLineNumbers>r)&&(s.updateLineNumbers=r),e.curOp.viewChanged=!0,r>=s.viewTo)pr&&xa(e.doc,r)<s.viewTo&&Wr(e);else if(o<=s.viewFrom)pr&&Bd(e.doc,o+l)>s.viewFrom?Wr(e):(s.viewFrom+=l,s.viewTo+=l);else if(r<=s.viewFrom&&o>=s.viewTo)Wr(e);else if(r<=s.viewFrom){var f=Cs(e,o,o+l,1);f?(s.view=s.view.slice(f.index),s.viewFrom=f.lineN,s.viewTo+=l):Wr(e)}else if(o>=s.viewTo){var d=Cs(e,r,r,-1);d?(s.view=s.view.slice(0,d.index),s.viewTo=d.lineN):Wr(e)}else{var h=Cs(e,r,r,-1),v=Cs(e,o,o+l,1);h&&v?(s.view=s.view.slice(0,h.index).concat(ws(e,h.lineN,v.lineN)).concat(s.view.slice(v.index)),s.viewTo+=l):Wr(e)}var m=s.externalMeasured;m&&(o<m.lineN?m.lineN+=l:r<m.lineN+m.size&&(s.externalMeasured=null))}function Rr(e,r,o){e.curOp.viewChanged=!0;var l=e.display,s=e.display.externalMeasured;if(s&&r>=s.lineN&&r<s.lineN+s.size&&(l.externalMeasured=null),!(r<l.viewFrom||r>=l.viewTo)){var f=l.view[gi(e,r)];if(f.node!=null){var d=f.changes||(f.changes=[]);Le(d,o)==-1&&d.push(o)}}}function Wr(e){e.display.viewFrom=e.display.viewTo=e.doc.first,e.display.view=[],e.display.viewOffset=0}function Cs(e,r,o,l){var s=gi(e,r),f,d=e.display.view;if(!pr||o==e.doc.first+e.doc.size)return{index:s,lineN:o};for(var h=e.display.viewFrom,v=0;v<s;v++)h+=d[v].size;if(h!=r){if(l>0){if(s==d.length-1)return null;f=h+d[s].size-r,s++}else f=h-r;r+=f,o+=f}for(;xa(e.doc,o)!=o;){if(s==(l<0?0:d.length-1))return null;o+=l*d[s-(l<0?1:0)].size,s+=l}return{index:s,lineN:o}}function iy(e,r,o){var l=e.display,s=l.view;s.length==0||r>=l.viewTo||o<=l.viewFrom?(l.view=ws(e,r,o),l.viewFrom=r):(l.viewFrom>r?l.view=ws(e,r,l.viewFrom).concat(l.view):l.viewFrom<r&&(l.view=l.view.slice(gi(e,r))),l.viewFrom=r,l.viewTo<o?l.view=l.view.concat(ws(e,l.viewTo,o)):l.viewTo>o&&(l.view=l.view.slice(0,gi(e,o)))),l.viewTo=o}function ch(e){for(var r=e.display.view,o=0,l=0;l<r.length;l++){var s=r[l];!s.hidden&&(!s.node||s.changes)&&++o}return o}function Vl(e){e.display.input.showSelection(e.display.input.prepareSelection())}function dh(e,r){r===void 0&&(r=!0);var o=e.doc,l={},s=l.cursors=document.createDocumentFragment(),f=l.selection=document.createDocumentFragment(),d=e.options.$customCursor;d&&(r=!0);for(var h=0;h<o.sel.ranges.length;h++)if(!(!r&&h==o.sel.primIndex)){var v=o.sel.ranges[h];if(!(v.from().line>=e.display.viewTo||v.to().line<e.display.viewFrom)){var m=v.empty();if(d){var x=d(e,v);x&&za(e,x,s)}else(m||e.options.showCursorWhenSelecting)&&za(e,v.head,s);m||ly(e,v,f)}}return l}function za(e,r,o){var l=An(e,r,"div",null,null,!e.options.singleCursorHeightPerLine),s=o.appendChild(M("div"," ","CodeMirror-cursor"));if(s.style.left=l.left+"px",s.style.top=l.top+"px",s.style.height=Math.max(0,l.bottom-l.top)*e.options.cursorHeight+"px",/\bcm-fat-cursor\b/.test(e.getWrapperElement().className)){var f=ks(e,r,"div",null,null),d=f.right-f.left;s.style.width=(d>0?d:e.defaultCharWidth())+"px"}if(l.other){var h=o.appendChild(M("div"," ","CodeMirror-cursor CodeMirror-secondarycursor"));h.style.display="",h.style.left=l.other.left+"px",h.style.top=l.other.top+"px",h.style.height=(l.other.bottom-l.other.top)*.85+"px"}}function Ts(e,r){return e.top-r.top||e.left-r.left}function ly(e,r,o){var l=e.display,s=e.doc,f=document.createDocumentFragment(),d=Xd(e.display),h=d.left,v=Math.max(l.sizerWidth,hi(e)-l.sizer.offsetLeft)-d.right,m=s.direction=="ltr";function x(te,se,ve,pe){se<0&&(se=0),se=Math.round(se),pe=Math.round(pe),f.appendChild(M("div",null,"CodeMirror-selected","position: absolute; left: "+te+`px;
                             top: `+se+"px; width: "+(ve??v-te)+`px;
                             height: `+(pe-se)+"px"))}function T(te,se,ve){var pe=ce(s,te),Ee=pe.text.length,He,yt;function Ye(ot,Qt){return ks(e,b(te,ot),"div",pe,Qt)}function nn(ot,Qt,Tt){var dt=ah(e,pe,null,ot),st=Qt=="ltr"==(Tt=="after")?"left":"right",Je=Tt=="after"?dt.begin:dt.end-(/\s/.test(pe.text.charAt(dt.end-1))?2:1);return Ye(Je,st)[st]}var Gt=Cn(pe,s.direction);return dn(Gt,se||0,ve??Ee,function(ot,Qt,Tt,dt){var st=Tt=="ltr",Je=Ye(ot,st?"left":"right"),Vt=Ye(Qt-1,st?"right":"left"),rl=se==null&&ot==0,Kr=ve==null&&Qt==Ee,bt=dt==0,tr=!Gt||dt==Gt.length-1;if(Vt.top-Je.top<=3){var wt=(m?rl:Kr)&&bt,sf=(m?Kr:rl)&&tr,wr=wt?h:(st?Je:Vt).left,Si=sf?v:(st?Vt:Je).right;x(wr,Je.top,Si-wr,Je.bottom)}else{var ki,At,il,uf;st?(ki=m&&rl&&bt?h:Je.left,At=m?v:nn(ot,Tt,"before"),il=m?h:nn(Qt,Tt,"after"),uf=m&&Kr&&tr?v:Vt.right):(ki=m?nn(ot,Tt,"before"):h,At=!m&&rl&&bt?v:Je.right,il=!m&&Kr&&tr?h:Vt.left,uf=m?nn(Qt,Tt,"after"):v),x(ki,Je.top,At-ki,Je.bottom),Je.bottom<Vt.top&&x(h,Je.bottom,null,Vt.top),x(il,Vt.top,uf-il,Vt.bottom)}(!He||Ts(Je,He)<0)&&(He=Je),Ts(Vt,He)<0&&(He=Vt),(!yt||Ts(Je,yt)<0)&&(yt=Je),Ts(Vt,yt)<0&&(yt=Vt)}),{start:He,end:yt}}var z=r.from(),P=r.to();if(z.line==P.line)T(z.line,z.ch,P.ch);else{var H=ce(s,z.line),j=ce(s,P.line),V=In(H)==In(j),ee=T(z.line,z.ch,V?H.text.length+1:null).end,le=T(P.line,V?0:null,P.ch).start;V&&(ee.top<le.top-2?(x(ee.right,ee.top,null,ee.bottom),x(h,le.top,le.left,le.bottom)):x(ee.right,ee.top,le.left-ee.right,ee.bottom)),ee.bottom<le.top&&x(h,ee.bottom,null,le.top)}o.appendChild(f)}function Ia(e){if(e.state.focused){var r=e.display;clearInterval(r.blinker);var o=!0;r.cursorDiv.style.visibility="",e.options.cursorBlinkRate>0?r.blinker=setInterval(function(){e.hasFocus()||Ki(e),r.cursorDiv.style.visibility=(o=!o)?"":"hidden"},e.options.cursorBlinkRate):e.options.cursorBlinkRate<0&&(r.cursorDiv.style.visibility="hidden")}}function hh(e){e.hasFocus()||(e.display.input.focus(),e.state.focused||Fa(e))}function Aa(e){e.state.delayingBlurEvent=!0,setTimeout(function(){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1,e.state.focused&&Ki(e))},100)}function Fa(e,r){e.state.delayingBlurEvent&&!e.state.draggingText&&(e.state.delayingBlurEvent=!1),e.options.readOnly!="nocursor"&&(e.state.focused||(Ue(e,"focus",e,r),e.state.focused=!0,F(e.display.wrapper,"CodeMirror-focused"),!e.curOp&&e.display.selForContextMenu!=e.doc.sel&&(e.display.input.reset(),N&&setTimeout(function(){return e.display.input.reset(!0)},20)),e.display.input.receivedFocus()),Ia(e))}function Ki(e,r){e.state.delayingBlurEvent||(e.state.focused&&(Ue(e,"blur",e,r),e.state.focused=!1,K(e.display.wrapper,"CodeMirror-focused")),clearInterval(e.display.blinker),setTimeout(function(){e.state.focused||(e.display.shift=!1)},150))}function Ls(e){for(var r=e.display,o=r.lineDiv.offsetTop,l=Math.max(0,r.scroller.getBoundingClientRect().top),s=r.lineDiv.getBoundingClientRect().top,f=0,d=0;d<r.view.length;d++){var h=r.view[d],v=e.options.lineWrapping,m=void 0,x=0;if(!h.hidden){if(s+=h.line.height,y&&S<8){var T=h.node.offsetTop+h.node.offsetHeight;m=T-o,o=T}else{var z=h.node.getBoundingClientRect();m=z.bottom-z.top,!v&&h.text.firstChild&&(x=h.text.firstChild.getBoundingClientRect().right-z.left-1)}var P=h.line.height-m;if((P>.005||P<-.005)&&(s<l&&(f-=P),Ln(h.line,m),ph(h.line),h.rest))for(var H=0;H<h.rest.length;H++)ph(h.rest[H]);if(x>e.display.sizerWidth){var j=Math.ceil(x/$i(e.display));j>e.display.maxLineLength&&(e.display.maxLineLength=j,e.display.maxLine=h.line,e.display.maxLineChanged=!0)}}}Math.abs(f)>2&&(r.scroller.scrollTop+=f)}function ph(e){if(e.widgets)for(var r=0;r<e.widgets.length;++r){var o=e.widgets[r],l=o.node.parentNode;l&&(o.height=l.offsetHeight)}}function Es(e,r,o){var l=o&&o.top!=null?Math.max(0,o.top):e.scroller.scrollTop;l=Math.floor(l-xs(e));var s=o&&o.bottom!=null?o.bottom:l+e.wrapper.clientHeight,f=Xn(r,l),d=Xn(r,s);if(o&&o.ensure){var h=o.ensure.from.line,v=o.ensure.to.line;h<f?(f=h,d=Xn(r,vr(ce(r,h))+e.wrapper.clientHeight)):Math.min(v,r.lastLine())>=d&&(f=Xn(r,vr(ce(r,v))-e.wrapper.clientHeight),d=v)}return{from:f,to:Math.max(d,f+1)}}function oy(e,r){if(!Xe(e,"scrollCursorIntoView")){var o=e.display,l=o.sizer.getBoundingClientRect(),s=null,f=o.wrapper.ownerDocument;if(r.top+l.top<0?s=!0:r.bottom+l.top>(f.defaultView.innerHeight||f.documentElement.clientHeight)&&(s=!1),s!=null&&!_){var d=M("div","​",null,`position: absolute;
                         top: `+(r.top-o.viewOffset-xs(e.display))+`px;
                         height: `+(r.bottom-r.top+Jn(e)+o.barHeight)+`px;
                         left: `+r.left+"px; width: "+Math.max(2,r.right-r.left)+"px;");e.display.lineSpace.appendChild(d),d.scrollIntoView(s),e.display.lineSpace.removeChild(d)}}}function sy(e,r,o,l){l==null&&(l=0);var s;!e.options.lineWrapping&&r==o&&(o=r.sticky=="before"?b(r.line,r.ch+1,"before"):r,r=r.ch?b(r.line,r.sticky=="before"?r.ch-1:r.ch,"after"):r);for(var f=0;f<5;f++){var d=!1,h=An(e,r),v=!o||o==r?h:An(e,o);s={left:Math.min(h.left,v.left),top:Math.min(h.top,v.top)-l,right:Math.max(h.left,v.left),bottom:Math.max(h.bottom,v.bottom)+l};var m=Ra(e,s),x=e.doc.scrollTop,T=e.doc.scrollLeft;if(m.scrollTop!=null&&(ql(e,m.scrollTop),Math.abs(e.doc.scrollTop-x)>1&&(d=!0)),m.scrollLeft!=null&&(mi(e,m.scrollLeft),Math.abs(e.doc.scrollLeft-T)>1&&(d=!0)),!d)break}return s}function uy(e,r){var o=Ra(e,r);o.scrollTop!=null&&ql(e,o.scrollTop),o.scrollLeft!=null&&mi(e,o.scrollLeft)}function Ra(e,r){var o=e.display,l=ji(e.display);r.top<0&&(r.top=0);var s=e.curOp&&e.curOp.scrollTop!=null?e.curOp.scrollTop:o.scroller.scrollTop,f=La(e),d={};r.bottom-r.top>f&&(r.bottom=r.top+f);var h=e.doc.height+Ta(o),v=r.top<l,m=r.bottom>h-l;if(r.top<s)d.scrollTop=v?0:r.top;else if(r.bottom>s+f){var x=Math.min(r.top,(m?h:r.bottom)-f);x!=s&&(d.scrollTop=x)}var T=e.options.fixedGutter?0:o.gutters.offsetWidth,z=e.curOp&&e.curOp.scrollLeft!=null?e.curOp.scrollLeft:o.scroller.scrollLeft-T,P=hi(e)-o.gutters.offsetWidth,H=r.right-r.left>P;return H&&(r.right=r.left+P),r.left<10?d.scrollLeft=0:r.left<z?d.scrollLeft=Math.max(0,r.left+T-(H?0:10)):r.right>P+z-3&&(d.scrollLeft=r.right+(H?0:10)-P),d}function Wa(e,r){r!=null&&(Ns(e),e.curOp.scrollTop=(e.curOp.scrollTop==null?e.doc.scrollTop:e.curOp.scrollTop)+r)}function Gi(e){Ns(e);var r=e.getCursor();e.curOp.scrollToPos={from:r,to:r,margin:e.options.cursorScrollMargin}}function Yl(e,r,o){(r!=null||o!=null)&&Ns(e),r!=null&&(e.curOp.scrollLeft=r),o!=null&&(e.curOp.scrollTop=o)}function ay(e,r){Ns(e),e.curOp.scrollToPos=r}function Ns(e){var r=e.curOp.scrollToPos;if(r){e.curOp.scrollToPos=null;var o=sh(e,r.from),l=sh(e,r.to);vh(e,o,l,r.margin)}}function vh(e,r,o,l){var s=Ra(e,{left:Math.min(r.left,o.left),top:Math.min(r.top,o.top)-l,right:Math.max(r.right,o.right),bottom:Math.max(r.bottom,o.bottom)+l});Yl(e,s.scrollLeft,s.scrollTop)}function ql(e,r){Math.abs(e.doc.scrollTop-r)<2||(a||Ba(e,{top:r}),gh(e,r,!0),a&&Ba(e),Jl(e,100))}function gh(e,r,o){r=Math.max(0,Math.min(e.display.scroller.scrollHeight-e.display.scroller.clientHeight,r)),!(e.display.scroller.scrollTop==r&&!o)&&(e.doc.scrollTop=r,e.display.scrollbars.setScrollTop(r),e.display.scroller.scrollTop!=r&&(e.display.scroller.scrollTop=r))}function mi(e,r,o,l){r=Math.max(0,Math.min(r,e.display.scroller.scrollWidth-e.display.scroller.clientWidth)),!((o?r==e.doc.scrollLeft:Math.abs(e.doc.scrollLeft-r)<2)&&!l)&&(e.doc.scrollLeft=r,Sh(e),e.display.scroller.scrollLeft!=r&&(e.display.scroller.scrollLeft=r),e.display.scrollbars.setScrollLeft(r))}function Xl(e){var r=e.display,o=r.gutters.offsetWidth,l=Math.round(e.doc.height+Ta(e.display));return{clientHeight:r.scroller.clientHeight,viewHeight:r.wrapper.clientHeight,scrollWidth:r.scroller.scrollWidth,clientWidth:r.scroller.clientWidth,viewWidth:r.wrapper.clientWidth,barLeft:e.options.fixedGutter?o:0,docHeight:l,scrollHeight:l+Jn(e)+r.barHeight,nativeBarWidth:r.nativeBarWidth,gutterWidth:o}}var yi=function(e,r,o){this.cm=o;var l=this.vert=M("div",[M("div",null,null,"min-width: 1px")],"CodeMirror-vscrollbar"),s=this.horiz=M("div",[M("div",null,null,"height: 100%; min-height: 1px")],"CodeMirror-hscrollbar");l.tabIndex=s.tabIndex=-1,e(l),e(s),re(l,"scroll",function(){l.clientHeight&&r(l.scrollTop,"vertical")}),re(s,"scroll",function(){s.clientWidth&&r(s.scrollLeft,"horizontal")}),this.checkedZeroWidth=!1,y&&S<8&&(this.horiz.style.minHeight=this.vert.style.minWidth="18px")};yi.prototype.update=function(e){var r=e.scrollWidth>e.clientWidth+1,o=e.scrollHeight>e.clientHeight+1,l=e.nativeBarWidth;if(o){this.vert.style.display="block",this.vert.style.bottom=r?l+"px":"0";var s=e.viewHeight-(r?l:0);this.vert.firstChild.style.height=Math.max(0,e.scrollHeight-e.clientHeight+s)+"px"}else this.vert.scrollTop=0,this.vert.style.display="",this.vert.firstChild.style.height="0";if(r){this.horiz.style.display="block",this.horiz.style.right=o?l+"px":"0",this.horiz.style.left=e.barLeft+"px";var f=e.viewWidth-e.barLeft-(o?l:0);this.horiz.firstChild.style.width=Math.max(0,e.scrollWidth-e.clientWidth+f)+"px"}else this.horiz.style.display="",this.horiz.firstChild.style.width="0";return!this.checkedZeroWidth&&e.clientHeight>0&&(l==0&&this.zeroWidthHack(),this.checkedZeroWidth=!0),{right:o?l:0,bottom:r?l:0}},yi.prototype.setScrollLeft=function(e){this.horiz.scrollLeft!=e&&(this.horiz.scrollLeft=e),this.disableHoriz&&this.enableZeroWidthBar(this.horiz,this.disableHoriz,"horiz")},yi.prototype.setScrollTop=function(e){this.vert.scrollTop!=e&&(this.vert.scrollTop=e),this.disableVert&&this.enableZeroWidthBar(this.vert,this.disableVert,"vert")},yi.prototype.zeroWidthHack=function(){var e=Y&&!G?"12px":"18px";this.horiz.style.height=this.vert.style.width=e,this.horiz.style.visibility=this.vert.style.visibility="hidden",this.disableHoriz=new Ce,this.disableVert=new Ce},yi.prototype.enableZeroWidthBar=function(e,r,o){e.style.visibility="";function l(){var s=e.getBoundingClientRect(),f=o=="vert"?document.elementFromPoint(s.right-1,(s.top+s.bottom)/2):document.elementFromPoint((s.right+s.left)/2,s.bottom-1);f!=e?e.style.visibility="hidden":r.set(1e3,l)}r.set(1e3,l)},yi.prototype.clear=function(){var e=this.horiz.parentNode;e.removeChild(this.horiz),e.removeChild(this.vert)};var Zl=function(){};Zl.prototype.update=function(){return{bottom:0,right:0}},Zl.prototype.setScrollLeft=function(){},Zl.prototype.setScrollTop=function(){},Zl.prototype.clear=function(){};function Qi(e,r){r||(r=Xl(e));var o=e.display.barWidth,l=e.display.barHeight;mh(e,r);for(var s=0;s<4&&o!=e.display.barWidth||l!=e.display.barHeight;s++)o!=e.display.barWidth&&e.options.lineWrapping&&Ls(e),mh(e,Xl(e)),o=e.display.barWidth,l=e.display.barHeight}function mh(e,r){var o=e.display,l=o.scrollbars.update(r);o.sizer.style.paddingRight=(o.barWidth=l.right)+"px",o.sizer.style.paddingBottom=(o.barHeight=l.bottom)+"px",o.heightForcer.style.borderBottom=l.bottom+"px solid transparent",l.right&&l.bottom?(o.scrollbarFiller.style.display="block",o.scrollbarFiller.style.height=l.bottom+"px",o.scrollbarFiller.style.width=l.right+"px"):o.scrollbarFiller.style.display="",l.bottom&&e.options.coverGutterNextToScrollbar&&e.options.fixedGutter?(o.gutterFiller.style.display="block",o.gutterFiller.style.height=l.bottom+"px",o.gutterFiller.style.width=r.gutterWidth+"px"):o.gutterFiller.style.display=""}var yh={native:yi,null:Zl};function wh(e){e.display.scrollbars&&(e.display.scrollbars.clear(),e.display.scrollbars.addClass&&K(e.display.wrapper,e.display.scrollbars.addClass)),e.display.scrollbars=new yh[e.options.scrollbarStyle](function(r){e.display.wrapper.insertBefore(r,e.display.scrollbarFiller),re(r,"mousedown",function(){e.state.focused&&setTimeout(function(){return e.display.input.focus()},0)}),r.setAttribute("cm-not-content","true")},function(r,o){o=="horizontal"?mi(e,r):ql(e,r)},e),e.display.scrollbars.addClass&&F(e.display.wrapper,e.display.scrollbars.addClass)}var fy=0;function wi(e){e.curOp={cm:e,viewChanged:!1,startHeight:e.doc.height,forceUpdate:!1,updateInput:0,typing:!1,changeObjs:null,cursorActivityHandlers:null,cursorActivityCalled:0,selectionChanged:!1,updateMaxLine:!1,scrollLeft:null,scrollTop:null,scrollToPos:null,focus:!1,id:++fy,markArrays:null},Bm(e.curOp)}function xi(e){var r=e.curOp;r&&jm(r,function(o){for(var l=0;l<o.ops.length;l++)o.ops[l].cm.curOp=null;cy(o)})}function cy(e){for(var r=e.ops,o=0;o<r.length;o++)dy(r[o]);for(var l=0;l<r.length;l++)hy(r[l]);for(var s=0;s<r.length;s++)py(r[s]);for(var f=0;f<r.length;f++)vy(r[f]);for(var d=0;d<r.length;d++)gy(r[d])}function dy(e){var r=e.cm,o=r.display;yy(r),e.updateMaxLine&&ka(r),e.mustUpdate=e.viewChanged||e.forceUpdate||e.scrollTop!=null||e.scrollToPos&&(e.scrollToPos.from.line<o.viewFrom||e.scrollToPos.to.line>=o.viewTo)||o.maxLineChanged&&r.options.lineWrapping,e.update=e.mustUpdate&&new Ms(r,e.mustUpdate&&{top:e.scrollTop,ensure:e.scrollToPos},e.forceUpdate)}function hy(e){e.updatedDisplay=e.mustUpdate&&Ha(e.cm,e.update)}function py(e){var r=e.cm,o=r.display;e.updatedDisplay&&Ls(r),e.barMeasure=Xl(r),o.maxLineChanged&&!r.options.lineWrapping&&(e.adjustWidthTo=Jd(r,o.maxLine,o.maxLine.text.length).left+3,r.display.sizerWidth=e.adjustWidthTo,e.barMeasure.scrollWidth=Math.max(o.scroller.clientWidth,o.sizer.offsetLeft+e.adjustWidthTo+Jn(r)+r.display.barWidth),e.maxScrollLeft=Math.max(0,o.sizer.offsetLeft+e.adjustWidthTo-hi(r))),(e.updatedDisplay||e.selectionChanged)&&(e.preparedSelection=o.input.prepareSelection())}function vy(e){var r=e.cm;e.adjustWidthTo!=null&&(r.display.sizer.style.minWidth=e.adjustWidthTo+"px",e.maxScrollLeft<r.doc.scrollLeft&&mi(r,Math.min(r.display.scroller.scrollLeft,e.maxScrollLeft),!0),r.display.maxLineChanged=!1);var o=e.focus&&e.focus==E(ye(r));e.preparedSelection&&r.display.input.showSelection(e.preparedSelection,o),(e.updatedDisplay||e.startHeight!=r.doc.height)&&Qi(r,e.barMeasure),e.updatedDisplay&&ja(r,e.barMeasure),e.selectionChanged&&Ia(r),r.state.focused&&e.updateInput&&r.display.input.reset(e.typing),o&&hh(e.cm)}function gy(e){var r=e.cm,o=r.display,l=r.doc;if(e.updatedDisplay&&xh(r,e.update),o.wheelStartX!=null&&(e.scrollTop!=null||e.scrollLeft!=null||e.scrollToPos)&&(o.wheelStartX=o.wheelStartY=null),e.scrollTop!=null&&gh(r,e.scrollTop,e.forceScroll),e.scrollLeft!=null&&mi(r,e.scrollLeft,!0,!0),e.scrollToPos){var s=sy(r,Se(l,e.scrollToPos.from),Se(l,e.scrollToPos.to),e.scrollToPos.margin);oy(r,s)}var f=e.maybeHiddenMarkers,d=e.maybeUnhiddenMarkers;if(f)for(var h=0;h<f.length;++h)f[h].lines.length||Ue(f[h],"hide");if(d)for(var v=0;v<d.length;++v)d[v].lines.length&&Ue(d[v],"unhide");o.wrapper.offsetHeight&&(l.scrollTop=r.display.scroller.scrollTop),e.changeObjs&&Ue(r,"changes",r,e.changeObjs),e.update&&e.update.finish()}function tn(e,r){if(e.curOp)return r();wi(e);try{return r()}finally{xi(e)}}function gt(e,r){return function(){if(e.curOp)return r.apply(e,arguments);wi(e);try{return r.apply(e,arguments)}finally{xi(e)}}}function It(e){return function(){if(this.curOp)return e.apply(this,arguments);wi(this);try{return e.apply(this,arguments)}finally{xi(this)}}}function mt(e){return function(){var r=this.cm;if(!r||r.curOp)return e.apply(this,arguments);wi(r);try{return e.apply(this,arguments)}finally{xi(r)}}}function Jl(e,r){e.doc.highlightFrontier<e.display.viewTo&&e.state.highlight.set(r,Ve(my,e))}function my(e){var r=e.doc;if(!(r.highlightFrontier>=e.display.viewTo)){var o=+new Date+e.options.workTime,l=Ul(e,r.highlightFrontier),s=[];r.iter(l.line,Math.min(r.first+r.size,e.display.viewTo+500),function(f){if(l.line>=e.display.viewFrom){var d=f.styles,h=f.text.length>e.options.maxHighlightLength?dr(r.mode,l.state):null,v=Nd(e,f,l,!0);h&&(l.state=h),f.styles=v.styles;var m=f.styleClasses,x=v.classes;x?f.styleClasses=x:m&&(f.styleClasses=null);for(var T=!d||d.length!=f.styles.length||m!=x&&(!m||!x||m.bgClass!=x.bgClass||m.textClass!=x.textClass),z=0;!T&&z<d.length;++z)T=d[z]!=f.styles[z];T&&s.push(l.line),f.stateAfter=l.save(),l.nextLine()}else f.text.length<=e.options.maxHighlightLength&&ga(e,f.text,l),f.stateAfter=l.line%5==0?l.save():null,l.nextLine();if(+new Date>o)return Jl(e,e.options.workDelay),!0}),r.highlightFrontier=l.line,r.modeFrontier=Math.max(r.modeFrontier,l.line),s.length&&tn(e,function(){for(var f=0;f<s.length;f++)Rr(e,s[f],"text")})}}var Ms=function(e,r,o){var l=e.display;this.viewport=r,this.visible=Es(l,e.doc,r),this.editorIsHidden=!l.wrapper.offsetWidth,this.wrapperHeight=l.wrapper.clientHeight,this.wrapperWidth=l.wrapper.clientWidth,this.oldDisplayWidth=hi(e),this.force=o,this.dims=Pa(e),this.events=[]};Ms.prototype.signal=function(e,r){Dt(e,r)&&this.events.push(arguments)},Ms.prototype.finish=function(){for(var e=0;e<this.events.length;e++)Ue.apply(null,this.events[e])};function yy(e){var r=e.display;!r.scrollbarsClipped&&r.scroller.offsetWidth&&(r.nativeBarWidth=r.scroller.offsetWidth-r.scroller.clientWidth,r.heightForcer.style.height=Jn(e)+"px",r.sizer.style.marginBottom=-r.nativeBarWidth+"px",r.sizer.style.borderRightWidth=Jn(e)+"px",r.scrollbarsClipped=!0)}function wy(e){if(e.hasFocus())return null;var r=E(ye(e));if(!r||!k(e.display.lineDiv,r))return null;var o={activeElt:r};if(window.getSelection){var l=Be(e).getSelection();l.anchorNode&&l.extend&&k(e.display.lineDiv,l.anchorNode)&&(o.anchorNode=l.anchorNode,o.anchorOffset=l.anchorOffset,o.focusNode=l.focusNode,o.focusOffset=l.focusOffset)}return o}function xy(e){if(!(!e||!e.activeElt||e.activeElt==E(e.activeElt.ownerDocument))&&(e.activeElt.focus(),!/^(INPUT|TEXTAREA)$/.test(e.activeElt.nodeName)&&e.anchorNode&&k(document.body,e.anchorNode)&&k(document.body,e.focusNode))){var r=e.activeElt.ownerDocument,o=r.defaultView.getSelection(),l=r.createRange();l.setEnd(e.anchorNode,e.anchorOffset),l.collapse(!1),o.removeAllRanges(),o.addRange(l),o.extend(e.focusNode,e.focusOffset)}}function Ha(e,r){var o=e.display,l=e.doc;if(r.editorIsHidden)return Wr(e),!1;if(!r.force&&r.visible.from>=o.viewFrom&&r.visible.to<=o.viewTo&&(o.updateLineNumbers==null||o.updateLineNumbers>=o.viewTo)&&o.renderedView==o.view&&ch(e)==0)return!1;kh(e)&&(Wr(e),r.dims=Pa(e));var s=l.first+l.size,f=Math.max(r.visible.from-e.options.viewportMargin,l.first),d=Math.min(s,r.visible.to+e.options.viewportMargin);o.viewFrom<f&&f-o.viewFrom<20&&(f=Math.max(l.first,o.viewFrom)),o.viewTo>d&&o.viewTo-d<20&&(d=Math.min(s,o.viewTo)),pr&&(f=xa(e.doc,f),d=Bd(e.doc,d));var h=f!=o.viewFrom||d!=o.viewTo||o.lastWrapHeight!=r.wrapperHeight||o.lastWrapWidth!=r.wrapperWidth;iy(e,f,d),o.viewOffset=vr(ce(e.doc,o.viewFrom)),e.display.mover.style.top=o.viewOffset+"px";var v=ch(e);if(!h&&v==0&&!r.force&&o.renderedView==o.view&&(o.updateLineNumbers==null||o.updateLineNumbers>=o.viewTo))return!1;var m=wy(e);return v>4&&(o.lineDiv.style.display="none"),Sy(e,o.updateLineNumbers,r.dims),v>4&&(o.lineDiv.style.display=""),o.renderedView=o.view,xy(m),A(o.cursorDiv),A(o.selectionDiv),o.gutters.style.height=o.sizer.style.minHeight=0,h&&(o.lastWrapHeight=r.wrapperHeight,o.lastWrapWidth=r.wrapperWidth,Jl(e,400)),o.updateLineNumbers=null,!0}function xh(e,r){for(var o=r.viewport,l=!0;;l=!1){if(!l||!e.options.lineWrapping||r.oldDisplayWidth==hi(e)){if(o&&o.top!=null&&(o={top:Math.min(e.doc.height+Ta(e.display)-La(e),o.top)}),r.visible=Es(e.display,e.doc,o),r.visible.from>=e.display.viewFrom&&r.visible.to<=e.display.viewTo)break}else l&&(r.visible=Es(e.display,e.doc,o));if(!Ha(e,r))break;Ls(e);var s=Xl(e);Vl(e),Qi(e,s),ja(e,s),r.force=!1}r.signal(e,"update",e),(e.display.viewFrom!=e.display.reportedViewFrom||e.display.viewTo!=e.display.reportedViewTo)&&(r.signal(e,"viewportChange",e,e.display.viewFrom,e.display.viewTo),e.display.reportedViewFrom=e.display.viewFrom,e.display.reportedViewTo=e.display.viewTo)}function Ba(e,r){var o=new Ms(e,r);if(Ha(e,o)){Ls(e),xh(e,o);var l=Xl(e);Vl(e),Qi(e,l),ja(e,l),o.finish()}}function Sy(e,r,o){var l=e.display,s=e.options.lineNumbers,f=l.lineDiv,d=f.firstChild;function h(H){var j=H.nextSibling;return N&&Y&&e.display.currentWheelTarget==H?H.style.display="none":H.parentNode.removeChild(H),j}for(var v=l.view,m=l.viewFrom,x=0;x<v.length;x++){var T=v[x];if(!T.hidden)if(!T.node||T.node.parentNode!=f){var z=Vm(e,T,m,o);f.insertBefore(z,d)}else{for(;d!=T.node;)d=h(d);var P=s&&r!=null&&r<=m&&T.lineNumber;T.changes&&(Le(T.changes,"gutter")>-1&&(P=!1),Gd(e,T,m,o)),P&&(A(T.lineNumber),T.lineNumber.appendChild(document.createTextNode(L(e.options,m)))),d=T.node.nextSibling}m+=T.size}for(;d;)d=h(d)}function Ua(e){var r=e.gutters.offsetWidth;e.sizer.style.marginLeft=r+"px",vt(e,"gutterChanged",e)}function ja(e,r){e.display.sizer.style.minHeight=r.docHeight+"px",e.display.heightForcer.style.top=r.docHeight+"px",e.display.gutters.style.height=r.docHeight+e.display.barHeight+Jn(e)+"px"}function Sh(e){var r=e.display,o=r.view;if(!(!r.alignWidgets&&(!r.gutters.firstChild||!e.options.fixedGutter))){for(var l=Oa(r)-r.scroller.scrollLeft+e.doc.scrollLeft,s=r.gutters.offsetWidth,f=l+"px",d=0;d<o.length;d++)if(!o[d].hidden){e.options.fixedGutter&&(o[d].gutter&&(o[d].gutter.style.left=f),o[d].gutterBackground&&(o[d].gutterBackground.style.left=f));var h=o[d].alignable;if(h)for(var v=0;v<h.length;v++)h[v].style.left=f}e.options.fixedGutter&&(r.gutters.style.left=l+s+"px")}}function kh(e){if(!e.options.lineNumbers)return!1;var r=e.doc,o=L(e.options,r.first+r.size-1),l=e.display;if(o.length!=l.lineNumChars){var s=l.measure.appendChild(M("div",[M("div",o)],"CodeMirror-linenumber CodeMirror-gutter-elt")),f=s.firstChild.offsetWidth,d=s.offsetWidth-f;return l.lineGutter.style.width="",l.lineNumInnerWidth=Math.max(f,l.lineGutter.offsetWidth-d)+1,l.lineNumWidth=l.lineNumInnerWidth+d,l.lineNumChars=l.lineNumInnerWidth?o.length:-1,l.lineGutter.style.width=l.lineNumWidth+"px",Ua(e.display),!0}return!1}function $a(e,r){for(var o=[],l=!1,s=0;s<e.length;s++){var f=e[s],d=null;if(typeof f!="string"&&(d=f.style,f=f.className),f=="CodeMirror-linenumbers")if(r)l=!0;else continue;o.push({className:f,style:d})}return r&&!l&&o.push({className:"CodeMirror-linenumbers",style:null}),o}function Ch(e){var r=e.gutters,o=e.gutterSpecs;A(r),e.lineGutter=null;for(var l=0;l<o.length;++l){var s=o[l],f=s.className,d=s.style,h=r.appendChild(M("div",null,"CodeMirror-gutter "+f));d&&(h.style.cssText=d),f=="CodeMirror-linenumbers"&&(e.lineGutter=h,h.style.width=(e.lineNumWidth||1)+"px")}r.style.display=o.length?"":"none",Ua(e)}function eo(e){Ch(e.display),$t(e),Sh(e)}function ky(e,r,o,l){var s=this;this.input=o,s.scrollbarFiller=M("div",null,"CodeMirror-scrollbar-filler"),s.scrollbarFiller.setAttribute("cm-not-content","true"),s.gutterFiller=M("div",null,"CodeMirror-gutter-filler"),s.gutterFiller.setAttribute("cm-not-content","true"),s.lineDiv=Q("div",null,"CodeMirror-code"),s.selectionDiv=M("div",null,null,"position: relative; z-index: 1"),s.cursorDiv=M("div",null,"CodeMirror-cursors"),s.measure=M("div",null,"CodeMirror-measure"),s.lineMeasure=M("div",null,"CodeMirror-measure"),s.lineSpace=Q("div",[s.measure,s.lineMeasure,s.selectionDiv,s.cursorDiv,s.lineDiv],null,"position: relative; outline: none");var f=Q("div",[s.lineSpace],"CodeMirror-lines");s.mover=M("div",[f],null,"position: relative"),s.sizer=M("div",[s.mover],"CodeMirror-sizer"),s.sizerWidth=null,s.heightForcer=M("div",null,null,"position: absolute; height: "+Ai+"px; width: 1px;"),s.gutters=M("div",null,"CodeMirror-gutters"),s.lineGutter=null,s.scroller=M("div",[s.sizer,s.heightForcer,s.gutters],"CodeMirror-scroll"),s.scroller.setAttribute("tabIndex","-1"),s.wrapper=M("div",[s.scrollbarFiller,s.gutterFiller,s.scroller],"CodeMirror"),O&&U>=105&&(s.wrapper.style.clipPath="inset(0px)"),s.wrapper.setAttribute("translate","no"),y&&S<8&&(s.gutters.style.zIndex=-1,s.scroller.style.paddingRight=0),!N&&!(a&&$)&&(s.scroller.draggable=!0),e&&(e.appendChild?e.appendChild(s.wrapper):e(s.wrapper)),s.viewFrom=s.viewTo=r.first,s.reportedViewFrom=s.reportedViewTo=r.first,s.view=[],s.renderedView=null,s.externalMeasured=null,s.viewOffset=0,s.lastWrapHeight=s.lastWrapWidth=0,s.updateLineNumbers=null,s.nativeBarWidth=s.barHeight=s.barWidth=0,s.scrollbarsClipped=!1,s.lineNumWidth=s.lineNumInnerWidth=s.lineNumChars=null,s.alignWidgets=!1,s.cachedCharWidth=s.cachedTextHeight=s.cachedPaddingH=null,s.maxLine=null,s.maxLineLength=0,s.maxLineChanged=!1,s.wheelDX=s.wheelDY=s.wheelStartX=s.wheelStartY=null,s.shift=!1,s.selForContextMenu=null,s.activeTouch=null,s.gutterSpecs=$a(l.gutters,l.lineNumbers),Ch(s),o.init(s)}var bs=0,mr=null;y?mr=-.53:a?mr=15:O?mr=-.7:oe&&(mr=-1/3);function Th(e){var r=e.wheelDeltaX,o=e.wheelDeltaY;return r==null&&e.detail&&e.axis==e.HORIZONTAL_AXIS&&(r=e.detail),o==null&&e.detail&&e.axis==e.VERTICAL_AXIS?o=e.detail:o==null&&(o=e.wheelDelta),{x:r,y:o}}function Cy(e){var r=Th(e);return r.x*=mr,r.y*=mr,r}function Lh(e,r){O&&U==102&&(e.display.chromeScrollHack==null?e.display.sizer.style.pointerEvents="none":clearTimeout(e.display.chromeScrollHack),e.display.chromeScrollHack=setTimeout(function(){e.display.chromeScrollHack=null,e.display.sizer.style.pointerEvents=""},100));var o=Th(r),l=o.x,s=o.y,f=mr;r.deltaMode===0&&(l=r.deltaX,s=r.deltaY,f=1);var d=e.display,h=d.scroller,v=h.scrollWidth>h.clientWidth,m=h.scrollHeight>h.clientHeight;if(l&&v||s&&m){if(s&&Y&&N){e:for(var x=r.target,T=d.view;x!=h;x=x.parentNode)for(var z=0;z<T.length;z++)if(T[z].node==x){e.display.currentWheelTarget=x;break e}}if(l&&!a&&!B&&f!=null){s&&m&&ql(e,Math.max(0,h.scrollTop+s*f)),mi(e,Math.max(0,h.scrollLeft+l*f)),(!s||s&&m)&&Nt(r),d.wheelStartX=null;return}if(s&&f!=null){var P=s*f,H=e.doc.scrollTop,j=H+d.wrapper.clientHeight;P<0?H=Math.max(0,H+P-50):j=Math.min(e.doc.height,j+P+50),Ba(e,{top:H,bottom:j})}bs<20&&r.deltaMode!==0&&(d.wheelStartX==null?(d.wheelStartX=h.scrollLeft,d.wheelStartY=h.scrollTop,d.wheelDX=l,d.wheelDY=s,setTimeout(function(){if(d.wheelStartX!=null){var V=h.scrollLeft-d.wheelStartX,ee=h.scrollTop-d.wheelStartY,le=ee&&d.wheelDY&&ee/d.wheelDY||V&&d.wheelDX&&V/d.wheelDX;d.wheelStartX=d.wheelStartY=null,le&&(mr=(mr*bs+le)/(bs+1),++bs)}},200)):(d.wheelDX+=l,d.wheelDY+=s))}}var pn=function(e,r){this.ranges=e,this.primIndex=r};pn.prototype.primary=function(){return this.ranges[this.primIndex]},pn.prototype.equals=function(e){if(e==this)return!0;if(e.primIndex!=this.primIndex||e.ranges.length!=this.ranges.length)return!1;for(var r=0;r<this.ranges.length;r++){var o=this.ranges[r],l=e.ranges[r];if(!xe(o.anchor,l.anchor)||!xe(o.head,l.head))return!1}return!0},pn.prototype.deepCopy=function(){for(var e=[],r=0;r<this.ranges.length;r++)e[r]=new Ie(be(this.ranges[r].anchor),be(this.ranges[r].head));return new pn(e,this.primIndex)},pn.prototype.somethingSelected=function(){for(var e=0;e<this.ranges.length;e++)if(!this.ranges[e].empty())return!0;return!1},pn.prototype.contains=function(e,r){r||(r=e);for(var o=0;o<this.ranges.length;o++){var l=this.ranges[o];if(R(r,l.from())>=0&&R(e,l.to())<=0)return o}return-1};var Ie=function(e,r){this.anchor=e,this.head=r};Ie.prototype.from=function(){return ct(this.anchor,this.head)},Ie.prototype.to=function(){return Oe(this.anchor,this.head)},Ie.prototype.empty=function(){return this.head.line==this.anchor.line&&this.head.ch==this.anchor.ch};function Fn(e,r,o){var l=e&&e.options.selectionsMayTouch,s=r[o];r.sort(function(z,P){return R(z.from(),P.from())}),o=Le(r,s);for(var f=1;f<r.length;f++){var d=r[f],h=r[f-1],v=R(h.to(),d.from());if(l&&!d.empty()?v>0:v>=0){var m=ct(h.from(),d.from()),x=Oe(h.to(),d.to()),T=h.empty()?d.from()==d.head:h.from()==h.head;f<=o&&--o,r.splice(--f,2,new Ie(T?x:m,T?m:x))}}return new pn(r,o)}function Hr(e,r){return new pn([new Ie(e,r||e)],0)}function Br(e){return e.text?b(e.from.line+e.text.length-1,Ne(e.text).length+(e.text.length==1?e.from.ch:0)):e.to}function Eh(e,r){if(R(e,r.from)<0)return e;if(R(e,r.to)<=0)return Br(r);var o=e.line+r.text.length-(r.to.line-r.from.line)-1,l=e.ch;return e.line==r.to.line&&(l+=Br(r).ch-r.to.ch),b(o,l)}function Ka(e,r){for(var o=[],l=0;l<e.sel.ranges.length;l++){var s=e.sel.ranges[l];o.push(new Ie(Eh(s.anchor,r),Eh(s.head,r)))}return Fn(e.cm,o,e.sel.primIndex)}function Nh(e,r,o){return e.line==r.line?b(o.line,e.ch-r.ch+o.ch):b(o.line+(e.line-r.line),e.ch)}function Ty(e,r,o){for(var l=[],s=b(e.first,0),f=s,d=0;d<r.length;d++){var h=r[d],v=Nh(h.from,s,f),m=Nh(Br(h),s,f);if(s=h.to,f=m,o=="around"){var x=e.sel.ranges[d],T=R(x.head,x.anchor)<0;l[d]=new Ie(T?m:v,T?v:m)}else l[d]=new Ie(v,v)}return new pn(l,e.sel.primIndex)}function Ga(e){e.doc.mode=cr(e.options,e.doc.modeOption),to(e)}function to(e){e.doc.iter(function(r){r.stateAfter&&(r.stateAfter=null),r.styles&&(r.styles=null)}),e.doc.modeFrontier=e.doc.highlightFrontier=e.doc.first,Jl(e,100),e.state.modeGen++,e.curOp&&$t(e)}function Mh(e,r){return r.from.ch==0&&r.to.ch==0&&Ne(r.text)==""&&(!e.cm||e.cm.options.wholeLineUpdateBefore)}function Qa(e,r,o,l){function s(le){return o?o[le]:null}function f(le,te,se){Om(le,te,se,l),vt(le,"change",le,r)}function d(le,te){for(var se=[],ve=le;ve<te;++ve)se.push(new Hi(m[ve],s(ve),l));return se}var h=r.from,v=r.to,m=r.text,x=ce(e,h.line),T=ce(e,v.line),z=Ne(m),P=s(m.length-1),H=v.line-h.line;if(r.full)e.insert(0,d(0,m.length)),e.remove(m.length,e.size-m.length);else if(Mh(e,r)){var j=d(0,m.length-1);f(T,T.text,P),H&&e.remove(h.line,H),j.length&&e.insert(h.line,j)}else if(x==T)if(m.length==1)f(x,x.text.slice(0,h.ch)+z+x.text.slice(v.ch),P);else{var V=d(1,m.length-1);V.push(new Hi(z+x.text.slice(v.ch),P,l)),f(x,x.text.slice(0,h.ch)+m[0],s(0)),e.insert(h.line+1,V)}else if(m.length==1)f(x,x.text.slice(0,h.ch)+m[0]+T.text.slice(v.ch),s(0)),e.remove(h.line+1,H);else{f(x,x.text.slice(0,h.ch)+m[0],s(0)),f(T,z+T.text.slice(v.ch),P);var ee=d(1,m.length-1);H>1&&e.remove(h.line+1,H-1),e.insert(h.line+1,ee)}vt(e,"change",e,r)}function Ur(e,r,o){function l(s,f,d){if(s.linked)for(var h=0;h<s.linked.length;++h){var v=s.linked[h];if(v.doc!=f){var m=d&&v.sharedHist;o&&!m||(r(v.doc,m),l(v.doc,s,m))}}}l(e,null,!0)}function bh(e,r){if(r.cm)throw new Error("This document is already in use.");e.doc=r,r.cm=e,Da(e),Ga(e),_h(e),e.options.direction=r.direction,e.options.lineWrapping||ka(e),e.options.mode=r.modeOption,$t(e)}function _h(e){(e.doc.direction=="rtl"?F:K)(e.display.lineDiv,"CodeMirror-rtl")}function Ly(e){tn(e,function(){_h(e),$t(e)})}function _s(e){this.done=[],this.undone=[],this.undoDepth=e?e.undoDepth:1/0,this.lastModTime=this.lastSelTime=0,this.lastOp=this.lastSelOp=null,this.lastOrigin=this.lastSelOrigin=null,this.generation=this.maxGeneration=e?e.maxGeneration:1}function Va(e,r){var o={from:be(r.from),to:Br(r),text:hr(e,r.from,r.to)};return Dh(e,o,r.from.line,r.to.line+1),Ur(e,function(l){return Dh(l,o,r.from.line,r.to.line+1)},!0),o}function Ph(e){for(;e.length;){var r=Ne(e);if(r.ranges)e.pop();else break}}function Ey(e,r){if(r)return Ph(e.done),Ne(e.done);if(e.done.length&&!Ne(e.done).ranges)return Ne(e.done);if(e.done.length>1&&!e.done[e.done.length-2].ranges)return e.done.pop(),Ne(e.done)}function Oh(e,r,o,l){var s=e.history;s.undone.length=0;var f=+new Date,d,h;if((s.lastOp==l||s.lastOrigin==r.origin&&r.origin&&(r.origin.charAt(0)=="+"&&s.lastModTime>f-(e.cm?e.cm.options.historyEventDelay:500)||r.origin.charAt(0)=="*"))&&(d=Ey(s,s.lastOp==l)))h=Ne(d.changes),R(r.from,r.to)==0&&R(r.from,h.to)==0?h.to=Br(r):d.changes.push(Va(e,r));else{var v=Ne(s.done);for((!v||!v.ranges)&&Ps(e.sel,s.done),d={changes:[Va(e,r)],generation:s.generation},s.done.push(d);s.done.length>s.undoDepth;)s.done.shift(),s.done[0].ranges||s.done.shift()}s.done.push(o),s.generation=++s.maxGeneration,s.lastModTime=s.lastSelTime=f,s.lastOp=s.lastSelOp=l,s.lastOrigin=s.lastSelOrigin=r.origin,h||Ue(e,"historyAdded")}function Ny(e,r,o,l){var s=r.charAt(0);return s=="*"||s=="+"&&o.ranges.length==l.ranges.length&&o.somethingSelected()==l.somethingSelected()&&new Date-e.history.lastSelTime<=(e.cm?e.cm.options.historyEventDelay:500)}function My(e,r,o,l){var s=e.history,f=l&&l.origin;o==s.lastSelOp||f&&s.lastSelOrigin==f&&(s.lastModTime==s.lastSelTime&&s.lastOrigin==f||Ny(e,f,Ne(s.done),r))?s.done[s.done.length-1]=r:Ps(r,s.done),s.lastSelTime=+new Date,s.lastSelOrigin=f,s.lastSelOp=o,l&&l.clearRedo!==!1&&Ph(s.undone)}function Ps(e,r){var o=Ne(r);o&&o.ranges&&o.equals(e)||r.push(e)}function Dh(e,r,o,l){var s=r["spans_"+e.id],f=0;e.iter(Math.max(e.first,o),Math.min(e.first+e.size,l),function(d){d.markedSpans&&((s||(s=r["spans_"+e.id]={}))[f]=d.markedSpans),++f})}function by(e){if(!e)return null;for(var r,o=0;o<e.length;++o)e[o].marker.explicitlyCleared?r||(r=e.slice(0,o)):r&&r.push(e[o]);return r?r.length?r:null:e}function _y(e,r){var o=r["spans_"+e.id];if(!o)return null;for(var l=[],s=0;s<r.text.length;++s)l.push(by(o[s]));return l}function zh(e,r){var o=_y(e,r),l=ya(e,r);if(!o)return l;if(!l)return o;for(var s=0;s<o.length;++s){var f=o[s],d=l[s];if(f&&d){e:for(var h=0;h<d.length;++h){for(var v=d[h],m=0;m<f.length;++m)if(f[m].marker==v.marker)continue e;f.push(v)}}else d&&(o[s]=d)}return o}function Vi(e,r,o){for(var l=[],s=0;s<e.length;++s){var f=e[s];if(f.ranges){l.push(o?pn.prototype.deepCopy.call(f):f);continue}var d=f.changes,h=[];l.push({changes:h});for(var v=0;v<d.length;++v){var m=d[v],x=void 0;if(h.push({from:m.from,to:m.to,text:m.text}),r)for(var T in m)(x=T.match(/^spans_(\d+)$/))&&Le(r,Number(x[1]))>-1&&(Ne(h)[T]=m[T],delete m[T])}}return l}function Ya(e,r,o,l){if(l){var s=e.anchor;if(o){var f=R(r,s)<0;f!=R(o,s)<0?(s=r,r=o):f!=R(r,o)<0&&(r=o)}return new Ie(s,r)}else return new Ie(o||r,r)}function Os(e,r,o,l,s){s==null&&(s=e.cm&&(e.cm.display.shift||e.extend)),Mt(e,new pn([Ya(e.sel.primary(),r,o,s)],0),l)}function Ih(e,r,o){for(var l=[],s=e.cm&&(e.cm.display.shift||e.extend),f=0;f<e.sel.ranges.length;f++)l[f]=Ya(e.sel.ranges[f],r[f],null,s);var d=Fn(e.cm,l,e.sel.primIndex);Mt(e,d,o)}function qa(e,r,o,l){var s=e.sel.ranges.slice(0);s[r]=o,Mt(e,Fn(e.cm,s,e.sel.primIndex),l)}function Ah(e,r,o,l){Mt(e,Hr(r,o),l)}function Py(e,r,o){var l={ranges:r.ranges,update:function(s){this.ranges=[];for(var f=0;f<s.length;f++)this.ranges[f]=new Ie(Se(e,s[f].anchor),Se(e,s[f].head))},origin:o&&o.origin};return Ue(e,"beforeSelectionChange",e,l),e.cm&&Ue(e.cm,"beforeSelectionChange",e.cm,l),l.ranges!=r.ranges?Fn(e.cm,l.ranges,l.ranges.length-1):r}function Fh(e,r,o){var l=e.history.done,s=Ne(l);s&&s.ranges?(l[l.length-1]=r,Ds(e,r,o)):Mt(e,r,o)}function Mt(e,r,o){Ds(e,r,o),My(e,e.sel,e.cm?e.cm.curOp.id:NaN,o)}function Ds(e,r,o){(Dt(e,"beforeSelectionChange")||e.cm&&Dt(e.cm,"beforeSelectionChange"))&&(r=Py(e,r,o));var l=o&&o.bias||(R(r.primary().head,e.sel.primary().head)<0?-1:1);Rh(e,Hh(e,r,l,!0)),!(o&&o.scroll===!1)&&e.cm&&e.cm.getOption("readOnly")!="nocursor"&&Gi(e.cm)}function Rh(e,r){r.equals(e.sel)||(e.sel=r,e.cm&&(e.cm.curOp.updateInput=1,e.cm.curOp.selectionChanged=!0,us(e.cm)),vt(e,"cursorActivity",e))}function Wh(e){Rh(e,Hh(e,e.sel,null,!1))}function Hh(e,r,o,l){for(var s,f=0;f<r.ranges.length;f++){var d=r.ranges[f],h=r.ranges.length==e.sel.ranges.length&&e.sel.ranges[f],v=zs(e,d.anchor,h&&h.anchor,o,l),m=d.head==d.anchor?v:zs(e,d.head,h&&h.head,o,l);(s||v!=d.anchor||m!=d.head)&&(s||(s=r.ranges.slice(0,f)),s[f]=new Ie(v,m))}return s?Fn(e.cm,s,r.primIndex):r}function Yi(e,r,o,l,s){var f=ce(e,r.line);if(f.markedSpans)for(var d=0;d<f.markedSpans.length;++d){var h=f.markedSpans[d],v=h.marker,m="selectLeft"in v?!v.selectLeft:v.inclusiveLeft,x="selectRight"in v?!v.selectRight:v.inclusiveRight;if((h.from==null||(m?h.from<=r.ch:h.from<r.ch))&&(h.to==null||(x?h.to>=r.ch:h.to>r.ch))){if(s&&(Ue(v,"beforeCursorEnter"),v.explicitlyCleared))if(f.markedSpans){--d;continue}else break;if(!v.atomic)continue;if(o){var T=v.find(l<0?1:-1),z=void 0;if((l<0?x:m)&&(T=Bh(e,T,-l,T&&T.line==r.line?f:null)),T&&T.line==r.line&&(z=R(T,o))&&(l<0?z<0:z>0))return Yi(e,T,r,l,s)}var P=v.find(l<0?-1:1);return(l<0?m:x)&&(P=Bh(e,P,l,P.line==r.line?f:null)),P?Yi(e,P,r,l,s):null}}return r}function zs(e,r,o,l,s){var f=l||1,d=Yi(e,r,o,f,s)||!s&&Yi(e,r,o,f,!0)||Yi(e,r,o,-f,s)||!s&&Yi(e,r,o,-f,!0);return d||(e.cantEdit=!0,b(e.first,0))}function Bh(e,r,o,l){return o<0&&r.ch==0?r.line>e.first?Se(e,b(r.line-1)):null:o>0&&r.ch==(l||ce(e,r.line)).text.length?r.line<e.first+e.size-1?b(r.line+1,0):null:new b(r.line,r.ch+o)}function Uh(e){e.setSelection(b(e.firstLine(),0),b(e.lastLine()),je)}function jh(e,r,o){var l={canceled:!1,from:r.from,to:r.to,text:r.text,origin:r.origin,cancel:function(){return l.canceled=!0}};return o&&(l.update=function(s,f,d,h){s&&(l.from=Se(e,s)),f&&(l.to=Se(e,f)),d&&(l.text=d),h!==void 0&&(l.origin=h)}),Ue(e,"beforeChange",e,l),e.cm&&Ue(e.cm,"beforeChange",e.cm,l),l.canceled?(e.cm&&(e.cm.curOp.updateInput=2),null):{from:l.from,to:l.to,text:l.text,origin:l.origin}}function qi(e,r,o){if(e.cm){if(!e.cm.curOp)return gt(e.cm,qi)(e,r,o);if(e.cm.state.suppressEdits)return}if(!((Dt(e,"beforeChange")||e.cm&&Dt(e.cm,"beforeChange"))&&(r=jh(e,r,!0),!r))){var l=zd&&!o&&Mm(e,r.from,r.to);if(l)for(var s=l.length-1;s>=0;--s)$h(e,{from:l[s].from,to:l[s].to,text:s?[""]:r.text,origin:r.origin});else $h(e,r)}}function $h(e,r){if(!(r.text.length==1&&r.text[0]==""&&R(r.from,r.to)==0)){var o=Ka(e,r);Oh(e,r,o,e.cm?e.cm.curOp.id:NaN),no(e,r,o,ya(e,r));var l=[];Ur(e,function(s,f){!f&&Le(l,s.history)==-1&&(Vh(s.history,r),l.push(s.history)),no(s,r,null,ya(s,r))})}}function Is(e,r,o){var l=e.cm&&e.cm.state.suppressEdits;if(!(l&&!o)){for(var s=e.history,f,d=e.sel,h=r=="undo"?s.done:s.undone,v=r=="undo"?s.undone:s.done,m=0;m<h.length&&(f=h[m],!(o?f.ranges&&!f.equals(e.sel):!f.ranges));m++);if(m!=h.length){for(s.lastOrigin=s.lastSelOrigin=null;;)if(f=h.pop(),f.ranges){if(Ps(f,v),o&&!f.equals(e.sel)){Mt(e,f,{clearRedo:!1});return}d=f}else if(l){h.push(f);return}else break;var x=[];Ps(d,v),v.push({changes:x,generation:s.generation}),s.generation=f.generation||++s.maxGeneration;for(var T=Dt(e,"beforeChange")||e.cm&&Dt(e.cm,"beforeChange"),z=function(j){var V=f.changes[j];if(V.origin=r,T&&!jh(e,V,!1))return h.length=0,{};x.push(Va(e,V));var ee=j?Ka(e,V):Ne(h);no(e,V,ee,zh(e,V)),!j&&e.cm&&e.cm.scrollIntoView({from:V.from,to:Br(V)});var le=[];Ur(e,function(te,se){!se&&Le(le,te.history)==-1&&(Vh(te.history,V),le.push(te.history)),no(te,V,null,zh(te,V))})},P=f.changes.length-1;P>=0;--P){var H=z(P);if(H)return H.v}}}}function Kh(e,r){if(r!=0&&(e.first+=r,e.sel=new pn(sr(e.sel.ranges,function(s){return new Ie(b(s.anchor.line+r,s.anchor.ch),b(s.head.line+r,s.head.ch))}),e.sel.primIndex),e.cm)){$t(e.cm,e.first,e.first-r,r);for(var o=e.cm.display,l=o.viewFrom;l<o.viewTo;l++)Rr(e.cm,l,"gutter")}}function no(e,r,o,l){if(e.cm&&!e.cm.curOp)return gt(e.cm,no)(e,r,o,l);if(r.to.line<e.first){Kh(e,r.text.length-1-(r.to.line-r.from.line));return}if(!(r.from.line>e.lastLine())){if(r.from.line<e.first){var s=r.text.length-1-(e.first-r.from.line);Kh(e,s),r={from:b(e.first,0),to:b(r.to.line+s,r.to.ch),text:[Ne(r.text)],origin:r.origin}}var f=e.lastLine();r.to.line>f&&(r={from:r.from,to:b(f,ce(e,f).text.length),text:[r.text[0]],origin:r.origin}),r.removed=hr(e,r.from,r.to),o||(o=Ka(e,r)),e.cm?Oy(e.cm,r,l):Qa(e,r,l),Ds(e,o,je),e.cantEdit&&zs(e,b(e.firstLine(),0))&&(e.cantEdit=!1)}}function Oy(e,r,o){var l=e.doc,s=e.display,f=r.from,d=r.to,h=!1,v=f.line;e.options.lineWrapping||(v=ze(In(ce(l,f.line))),l.iter(v,d.line+1,function(P){if(P==s.maxLine)return h=!0,!0})),l.sel.contains(r.from,r.to)>-1&&us(e),Qa(l,r,o,fh(e)),e.options.lineWrapping||(l.iter(v,f.line+r.text.length,function(P){var H=ys(P);H>s.maxLineLength&&(s.maxLine=P,s.maxLineLength=H,s.maxLineChanged=!0,h=!1)}),h&&(e.curOp.updateMaxLine=!0)),Sm(l,f.line),Jl(e,400);var m=r.text.length-(d.line-f.line)-1;r.full?$t(e):f.line==d.line&&r.text.length==1&&!Mh(e.doc,r)?Rr(e,f.line,"text"):$t(e,f.line,d.line+1,m);var x=Dt(e,"changes"),T=Dt(e,"change");if(T||x){var z={from:f,to:d,text:r.text,removed:r.removed,origin:r.origin};T&&vt(e,"change",e,z),x&&(e.curOp.changeObjs||(e.curOp.changeObjs=[])).push(z)}e.display.selForContextMenu=null}function Xi(e,r,o,l,s){var f;l||(l=o),R(l,o)<0&&(f=[l,o],o=f[0],l=f[1]),typeof r=="string"&&(r=e.splitLines(r)),qi(e,{from:o,to:l,text:r,origin:s})}function Gh(e,r,o,l){o<e.line?e.line+=l:r<e.line&&(e.line=r,e.ch=0)}function Qh(e,r,o,l){for(var s=0;s<e.length;++s){var f=e[s],d=!0;if(f.ranges){f.copied||(f=e[s]=f.deepCopy(),f.copied=!0);for(var h=0;h<f.ranges.length;h++)Gh(f.ranges[h].anchor,r,o,l),Gh(f.ranges[h].head,r,o,l);continue}for(var v=0;v<f.changes.length;++v){var m=f.changes[v];if(o<m.from.line)m.from=b(m.from.line+l,m.from.ch),m.to=b(m.to.line+l,m.to.ch);else if(r<=m.to.line){d=!1;break}}d||(e.splice(0,s+1),s=0)}}function Vh(e,r){var o=r.from.line,l=r.to.line,s=r.text.length-(l-o)-1;Qh(e.done,o,l,s),Qh(e.undone,o,l,s)}function ro(e,r,o,l){var s=r,f=r;return typeof r=="number"?f=ce(e,hn(e,r)):s=ze(r),s==null?null:(l(f,s)&&e.cm&&Rr(e.cm,s,o),f)}function io(e){this.lines=e,this.parent=null;for(var r=0,o=0;o<e.length;++o)e[o].parent=this,r+=e[o].height;this.height=r}io.prototype={chunkSize:function(){return this.lines.length},removeInner:function(e,r){for(var o=e,l=e+r;o<l;++o){var s=this.lines[o];this.height-=s.height,Dm(s),vt(s,"delete")}this.lines.splice(e,r)},collapse:function(e){e.push.apply(e,this.lines)},insertInner:function(e,r,o){this.height+=o,this.lines=this.lines.slice(0,e).concat(r).concat(this.lines.slice(e));for(var l=0;l<r.length;++l)r[l].parent=this},iterN:function(e,r,o){for(var l=e+r;e<l;++e)if(o(this.lines[e]))return!0}};function lo(e){this.children=e;for(var r=0,o=0,l=0;l<e.length;++l){var s=e[l];r+=s.chunkSize(),o+=s.height,s.parent=this}this.size=r,this.height=o,this.parent=null}lo.prototype={chunkSize:function(){return this.size},removeInner:function(e,r){this.size-=r;for(var o=0;o<this.children.length;++o){var l=this.children[o],s=l.chunkSize();if(e<s){var f=Math.min(r,s-e),d=l.height;if(l.removeInner(e,f),this.height-=d-l.height,s==f&&(this.children.splice(o--,1),l.parent=null),(r-=f)==0)break;e=0}else e-=s}if(this.size-r<25&&(this.children.length>1||!(this.children[0]instanceof io))){var h=[];this.collapse(h),this.children=[new io(h)],this.children[0].parent=this}},collapse:function(e){for(var r=0;r<this.children.length;++r)this.children[r].collapse(e)},insertInner:function(e,r,o){this.size+=r.length,this.height+=o;for(var l=0;l<this.children.length;++l){var s=this.children[l],f=s.chunkSize();if(e<=f){if(s.insertInner(e,r,o),s.lines&&s.lines.length>50){for(var d=s.lines.length%25+25,h=d;h<s.lines.length;){var v=new io(s.lines.slice(h,h+=25));s.height-=v.height,this.children.splice(++l,0,v),v.parent=this}s.lines=s.lines.slice(0,d),this.maybeSpill()}break}e-=f}},maybeSpill:function(){if(!(this.children.length<=10)){var e=this;do{var r=e.children.splice(e.children.length-5,5),o=new lo(r);if(e.parent){e.size-=o.size,e.height-=o.height;var s=Le(e.parent.children,e);e.parent.children.splice(s+1,0,o)}else{var l=new lo(e.children);l.parent=e,e.children=[l,o],e=l}o.parent=e.parent}while(e.children.length>10);e.parent.maybeSpill()}},iterN:function(e,r,o){for(var l=0;l<this.children.length;++l){var s=this.children[l],f=s.chunkSize();if(e<f){var d=Math.min(r,f-e);if(s.iterN(e,d,o))return!0;if((r-=d)==0)break;e=0}else e-=f}}};var oo=function(e,r,o){if(o)for(var l in o)o.hasOwnProperty(l)&&(this[l]=o[l]);this.doc=e,this.node=r};oo.prototype.clear=function(){var e=this.doc.cm,r=this.line.widgets,o=this.line,l=ze(o);if(!(l==null||!r)){for(var s=0;s<r.length;++s)r[s]==this&&r.splice(s--,1);r.length||(o.widgets=null);var f=Gl(this);Ln(o,Math.max(0,o.height-f)),e&&(tn(e,function(){Yh(e,o,-f),Rr(e,l,"widget")}),vt(e,"lineWidgetCleared",e,this,l))}},oo.prototype.changed=function(){var e=this,r=this.height,o=this.doc.cm,l=this.line;this.height=null;var s=Gl(this)-r;s&&(Fr(this.doc,l)||Ln(l,l.height+s),o&&tn(o,function(){o.curOp.forceUpdate=!0,Yh(o,l,s),vt(o,"lineWidgetChanged",o,e,ze(l))}))},zt(oo);function Yh(e,r,o){vr(r)<(e.curOp&&e.curOp.scrollTop||e.doc.scrollTop)&&Wa(e,o)}function Dy(e,r,o,l){var s=new oo(e,o,l),f=e.cm;return f&&s.noHScroll&&(f.display.alignWidgets=!0),ro(e,r,"widget",function(d){var h=d.widgets||(d.widgets=[]);if(s.insertAt==null?h.push(s):h.splice(Math.min(h.length,Math.max(0,s.insertAt)),0,s),s.line=d,f&&!Fr(e,d)){var v=vr(d)<e.scrollTop;Ln(d,d.height+Gl(s)),v&&Wa(f,s.height),f.curOp.forceUpdate=!0}return!0}),f&&vt(f,"lineWidgetAdded",f,s,typeof r=="number"?r:ze(r)),s}var qh=0,jr=function(e,r){this.lines=[],this.type=r,this.doc=e,this.id=++qh};jr.prototype.clear=function(){if(!this.explicitlyCleared){var e=this.doc.cm,r=e&&!e.curOp;if(r&&wi(e),Dt(this,"clear")){var o=this.find();o&&vt(this,"clear",o.from,o.to)}for(var l=null,s=null,f=0;f<this.lines.length;++f){var d=this.lines[f],h=jl(d.markedSpans,this);e&&!this.collapsed?Rr(e,ze(d),"text"):e&&(h.to!=null&&(s=ze(d)),h.from!=null&&(l=ze(d))),d.markedSpans=Tm(d.markedSpans,h),h.from==null&&this.collapsed&&!Fr(this.doc,d)&&e&&Ln(d,ji(e.display))}if(e&&this.collapsed&&!e.options.lineWrapping)for(var v=0;v<this.lines.length;++v){var m=In(this.lines[v]),x=ys(m);x>e.display.maxLineLength&&(e.display.maxLine=m,e.display.maxLineLength=x,e.display.maxLineChanged=!0)}l!=null&&e&&this.collapsed&&$t(e,l,s+1),this.lines.length=0,this.explicitlyCleared=!0,this.atomic&&this.doc.cantEdit&&(this.doc.cantEdit=!1,e&&Wh(e.doc)),e&&vt(e,"markerCleared",e,this,l,s),r&&xi(e),this.parent&&this.parent.clear()}},jr.prototype.find=function(e,r){e==null&&this.type=="bookmark"&&(e=1);for(var o,l,s=0;s<this.lines.length;++s){var f=this.lines[s],d=jl(f.markedSpans,this);if(d.from!=null&&(o=b(r?f:ze(f),d.from),e==-1))return o;if(d.to!=null&&(l=b(r?f:ze(f),d.to),e==1))return l}return o&&{from:o,to:l}},jr.prototype.changed=function(){var e=this,r=this.find(-1,!0),o=this,l=this.doc.cm;!r||!l||tn(l,function(){var s=r.line,f=ze(r.line),d=Ea(l,f);if(d&&(nh(d),l.curOp.selectionChanged=l.curOp.forceUpdate=!0),l.curOp.updateMaxLine=!0,!Fr(o.doc,s)&&o.height!=null){var h=o.height;o.height=null;var v=Gl(o)-h;v&&Ln(s,s.height+v)}vt(l,"markerChanged",l,e)})},jr.prototype.attachLine=function(e){if(!this.lines.length&&this.doc.cm){var r=this.doc.cm.curOp;(!r.maybeHiddenMarkers||Le(r.maybeHiddenMarkers,this)==-1)&&(r.maybeUnhiddenMarkers||(r.maybeUnhiddenMarkers=[])).push(this)}this.lines.push(e)},jr.prototype.detachLine=function(e){if(this.lines.splice(Le(this.lines,e),1),!this.lines.length&&this.doc.cm){var r=this.doc.cm.curOp;(r.maybeHiddenMarkers||(r.maybeHiddenMarkers=[])).push(this)}},zt(jr);function Zi(e,r,o,l,s){if(l&&l.shared)return zy(e,r,o,l,s);if(e.cm&&!e.cm.curOp)return gt(e.cm,Zi)(e,r,o,l,s);var f=new jr(e,s),d=R(r,o);if(l&&he(l,f,!1),d>0||d==0&&f.clearWhenEmpty!==!1)return f;if(f.replacedWith&&(f.collapsed=!0,f.widgetNode=Q("span",[f.replacedWith],"CodeMirror-widget"),l.handleMouseEvents||f.widgetNode.setAttribute("cm-ignore-events","true"),l.insertLeft&&(f.widgetNode.insertLeft=!0)),f.collapsed){if(Hd(e,r.line,r,o,f)||r.line!=o.line&&Hd(e,o.line,r,o,f))throw new Error("Inserting collapsed marker partially overlapping an existing one");Cm()}f.addToHistory&&Oh(e,{from:r,to:o,origin:"markText"},e.sel,NaN);var h=r.line,v=e.cm,m;if(e.iter(h,o.line+1,function(T){v&&f.collapsed&&!v.options.lineWrapping&&In(T)==v.display.maxLine&&(m=!0),f.collapsed&&h!=r.line&&Ln(T,0),Lm(T,new ps(f,h==r.line?r.ch:null,h==o.line?o.ch:null),e.cm&&e.cm.curOp),++h}),f.collapsed&&e.iter(r.line,o.line+1,function(T){Fr(e,T)&&Ln(T,0)}),f.clearOnEnter&&re(f,"beforeCursorEnter",function(){return f.clear()}),f.readOnly&&(km(),(e.history.done.length||e.history.undone.length)&&e.clearHistory()),f.collapsed&&(f.id=++qh,f.atomic=!0),v){if(m&&(v.curOp.updateMaxLine=!0),f.collapsed)$t(v,r.line,o.line+1);else if(f.className||f.startStyle||f.endStyle||f.css||f.attributes||f.title)for(var x=r.line;x<=o.line;x++)Rr(v,x,"text");f.atomic&&Wh(v.doc),vt(v,"markerAdded",v,f)}return f}var so=function(e,r){this.markers=e,this.primary=r;for(var o=0;o<e.length;++o)e[o].parent=this};so.prototype.clear=function(){if(!this.explicitlyCleared){this.explicitlyCleared=!0;for(var e=0;e<this.markers.length;++e)this.markers[e].clear();vt(this,"clear")}},so.prototype.find=function(e,r){return this.primary.find(e,r)},zt(so);function zy(e,r,o,l,s){l=he(l),l.shared=!1;var f=[Zi(e,r,o,l,s)],d=f[0],h=l.widgetNode;return Ur(e,function(v){h&&(l.widgetNode=h.cloneNode(!0)),f.push(Zi(v,Se(v,r),Se(v,o),l,s));for(var m=0;m<v.linked.length;++m)if(v.linked[m].isParent)return;d=Ne(f)}),new so(f,d)}function Xh(e){return e.findMarks(b(e.first,0),e.clipPos(b(e.lastLine())),function(r){return r.parent})}function Iy(e,r){for(var o=0;o<r.length;o++){var l=r[o],s=l.find(),f=e.clipPos(s.from),d=e.clipPos(s.to);if(R(f,d)){var h=Zi(e,f,d,l.primary,l.primary.type);l.markers.push(h),h.parent=l}}}function Ay(e){for(var r=function(l){var s=e[l],f=[s.primary.doc];Ur(s.primary.doc,function(v){return f.push(v)});for(var d=0;d<s.markers.length;d++){var h=s.markers[d];Le(f,h.doc)==-1&&(h.parent=null,s.markers.splice(d--,1))}},o=0;o<e.length;o++)r(o)}var Fy=0,Kt=function(e,r,o,l,s){if(!(this instanceof Kt))return new Kt(e,r,o,l,s);o==null&&(o=0),lo.call(this,[new io([new Hi("",null)])]),this.first=o,this.scrollTop=this.scrollLeft=0,this.cantEdit=!1,this.cleanGeneration=1,this.modeFrontier=this.highlightFrontier=o;var f=b(o,0);this.sel=Hr(f),this.history=new _s(null),this.id=++Fy,this.modeOption=r,this.lineSep=l,this.direction=s=="rtl"?"rtl":"ltr",this.extend=!1,typeof e=="string"&&(e=this.splitLines(e)),Qa(this,{from:f,to:f,text:e}),Mt(this,Hr(f),je)};Kt.prototype=ci(lo.prototype,{constructor:Kt,iter:function(e,r,o){o?this.iterN(e-this.first,r-e,o):this.iterN(this.first,this.first+this.size,e)},insert:function(e,r){for(var o=0,l=0;l<r.length;++l)o+=r[l].height;this.insertInner(e-this.first,r,o)},remove:function(e,r){this.removeInner(e-this.first,r)},getValue:function(e){var r=Wi(this,this.first,this.first+this.size);return e===!1?r:r.join(e||this.lineSeparator())},setValue:mt(function(e){var r=b(this.first,0),o=this.first+this.size-1;qi(this,{from:r,to:b(o,ce(this,o).text.length),text:this.splitLines(e),origin:"setValue",full:!0},!0),this.cm&&Yl(this.cm,0,0),Mt(this,Hr(r),je)}),replaceRange:function(e,r,o,l){r=Se(this,r),o=o?Se(this,o):r,Xi(this,e,r,o,l)},getRange:function(e,r,o){var l=hr(this,Se(this,e),Se(this,r));return o===!1?l:o===""?l.join(""):l.join(o||this.lineSeparator())},getLine:function(e){var r=this.getLineHandle(e);return r&&r.text},getLineHandle:function(e){if(w(this,e))return ce(this,e)},getLineNumber:function(e){return ze(e)},getLineHandleVisualStart:function(e){return typeof e=="number"&&(e=ce(this,e)),In(e)},lineCount:function(){return this.size},firstLine:function(){return this.first},lastLine:function(){return this.first+this.size-1},clipPos:function(e){return Se(this,e)},getCursor:function(e){var r=this.sel.primary(),o;return e==null||e=="head"?o=r.head:e=="anchor"?o=r.anchor:e=="end"||e=="to"||e===!1?o=r.to():o=r.from(),o},listSelections:function(){return this.sel.ranges},somethingSelected:function(){return this.sel.somethingSelected()},setCursor:mt(function(e,r,o){Ah(this,Se(this,typeof e=="number"?b(e,r||0):e),null,o)}),setSelection:mt(function(e,r,o){Ah(this,Se(this,e),Se(this,r||e),o)}),extendSelection:mt(function(e,r,o){Os(this,Se(this,e),r&&Se(this,r),o)}),extendSelections:mt(function(e,r){Ih(this,Ed(this,e),r)}),extendSelectionsBy:mt(function(e,r){var o=sr(this.sel.ranges,e);Ih(this,Ed(this,o),r)}),setSelections:mt(function(e,r,o){if(e.length){for(var l=[],s=0;s<e.length;s++)l[s]=new Ie(Se(this,e[s].anchor),Se(this,e[s].head||e[s].anchor));r==null&&(r=Math.min(e.length-1,this.sel.primIndex)),Mt(this,Fn(this.cm,l,r),o)}}),addSelection:mt(function(e,r,o){var l=this.sel.ranges.slice(0);l.push(new Ie(Se(this,e),Se(this,r||e))),Mt(this,Fn(this.cm,l,l.length-1),o)}),getSelection:function(e){for(var r=this.sel.ranges,o,l=0;l<r.length;l++){var s=hr(this,r[l].from(),r[l].to());o=o?o.concat(s):s}return e===!1?o:o.join(e||this.lineSeparator())},getSelections:function(e){for(var r=[],o=this.sel.ranges,l=0;l<o.length;l++){var s=hr(this,o[l].from(),o[l].to());e!==!1&&(s=s.join(e||this.lineSeparator())),r[l]=s}return r},replaceSelection:function(e,r,o){for(var l=[],s=0;s<this.sel.ranges.length;s++)l[s]=e;this.replaceSelections(l,r,o||"+input")},replaceSelections:mt(function(e,r,o){for(var l=[],s=this.sel,f=0;f<s.ranges.length;f++){var d=s.ranges[f];l[f]={from:d.from(),to:d.to(),text:this.splitLines(e[f]),origin:o}}for(var h=r&&r!="end"&&Ty(this,l,r),v=l.length-1;v>=0;v--)qi(this,l[v]);h?Fh(this,h):this.cm&&Gi(this.cm)}),undo:mt(function(){Is(this,"undo")}),redo:mt(function(){Is(this,"redo")}),undoSelection:mt(function(){Is(this,"undo",!0)}),redoSelection:mt(function(){Is(this,"redo",!0)}),setExtending:function(e){this.extend=e},getExtending:function(){return this.extend},historySize:function(){for(var e=this.history,r=0,o=0,l=0;l<e.done.length;l++)e.done[l].ranges||++r;for(var s=0;s<e.undone.length;s++)e.undone[s].ranges||++o;return{undo:r,redo:o}},clearHistory:function(){var e=this;this.history=new _s(this.history),Ur(this,function(r){return r.history=e.history},!0)},markClean:function(){this.cleanGeneration=this.changeGeneration(!0)},changeGeneration:function(e){return e&&(this.history.lastOp=this.history.lastSelOp=this.history.lastOrigin=null),this.history.generation},isClean:function(e){return this.history.generation==(e||this.cleanGeneration)},getHistory:function(){return{done:Vi(this.history.done),undone:Vi(this.history.undone)}},setHistory:function(e){var r=this.history=new _s(this.history);r.done=Vi(e.done.slice(0),null,!0),r.undone=Vi(e.undone.slice(0),null,!0)},setGutterMarker:mt(function(e,r,o){return ro(this,e,"gutter",function(l){var s=l.gutterMarkers||(l.gutterMarkers={});return s[r]=o,!o&&ge(s)&&(l.gutterMarkers=null),!0})}),clearGutter:mt(function(e){var r=this;this.iter(function(o){o.gutterMarkers&&o.gutterMarkers[e]&&ro(r,o,"gutter",function(){return o.gutterMarkers[e]=null,ge(o.gutterMarkers)&&(o.gutterMarkers=null),!0})})}),lineInfo:function(e){var r;if(typeof e=="number"){if(!w(this,e)||(r=e,e=ce(this,e),!e))return null}else if(r=ze(e),r==null)return null;return{line:r,handle:e,text:e.text,gutterMarkers:e.gutterMarkers,textClass:e.textClass,bgClass:e.bgClass,wrapClass:e.wrapClass,widgets:e.widgets}},addLineClass:mt(function(e,r,o){return ro(this,e,r=="gutter"?"gutter":"class",function(l){var s=r=="text"?"textClass":r=="background"?"bgClass":r=="gutter"?"gutterClass":"wrapClass";if(!l[s])l[s]=o;else{if(Re(o).test(l[s]))return!1;l[s]+=" "+o}return!0})}),removeLineClass:mt(function(e,r,o){return ro(this,e,r=="gutter"?"gutter":"class",function(l){var s=r=="text"?"textClass":r=="background"?"bgClass":r=="gutter"?"gutterClass":"wrapClass",f=l[s];if(f)if(o==null)l[s]=null;else{var d=f.match(Re(o));if(!d)return!1;var h=d.index+d[0].length;l[s]=f.slice(0,d.index)+(!d.index||h==f.length?"":" ")+f.slice(h)||null}else return!1;return!0})}),addLineWidget:mt(function(e,r,o){return Dy(this,e,r,o)}),removeLineWidget:function(e){e.clear()},markText:function(e,r,o){return Zi(this,Se(this,e),Se(this,r),o,o&&o.type||"range")},setBookmark:function(e,r){var o={replacedWith:r&&(r.nodeType==null?r.widget:r),insertLeft:r&&r.insertLeft,clearWhenEmpty:!1,shared:r&&r.shared,handleMouseEvents:r&&r.handleMouseEvents};return e=Se(this,e),Zi(this,e,e,o,"bookmark")},findMarksAt:function(e){e=Se(this,e);var r=[],o=ce(this,e.line).markedSpans;if(o)for(var l=0;l<o.length;++l){var s=o[l];(s.from==null||s.from<=e.ch)&&(s.to==null||s.to>=e.ch)&&r.push(s.marker.parent||s.marker)}return r},findMarks:function(e,r,o){e=Se(this,e),r=Se(this,r);var l=[],s=e.line;return this.iter(e.line,r.line+1,function(f){var d=f.markedSpans;if(d)for(var h=0;h<d.length;h++){var v=d[h];!(v.to!=null&&s==e.line&&e.ch>=v.to||v.from==null&&s!=e.line||v.from!=null&&s==r.line&&v.from>=r.ch)&&(!o||o(v.marker))&&l.push(v.marker.parent||v.marker)}++s}),l},getAllMarks:function(){var e=[];return this.iter(function(r){var o=r.markedSpans;if(o)for(var l=0;l<o.length;++l)o[l].from!=null&&e.push(o[l].marker)}),e},posFromIndex:function(e){var r,o=this.first,l=this.lineSeparator().length;return this.iter(function(s){var f=s.text.length+l;if(f>e)return r=e,!0;e-=f,++o}),Se(this,b(o,r))},indexFromPos:function(e){e=Se(this,e);var r=e.ch;if(e.line<this.first||e.ch<0)return 0;var o=this.lineSeparator().length;return this.iter(this.first,e.line,function(l){r+=l.text.length+o}),r},copy:function(e){var r=new Kt(Wi(this,this.first,this.first+this.size),this.modeOption,this.first,this.lineSep,this.direction);return r.scrollTop=this.scrollTop,r.scrollLeft=this.scrollLeft,r.sel=this.sel,r.extend=!1,e&&(r.history.undoDepth=this.history.undoDepth,r.setHistory(this.getHistory())),r},linkedDoc:function(e){e||(e={});var r=this.first,o=this.first+this.size;e.from!=null&&e.from>r&&(r=e.from),e.to!=null&&e.to<o&&(o=e.to);var l=new Kt(Wi(this,r,o),e.mode||this.modeOption,r,this.lineSep,this.direction);return e.sharedHist&&(l.history=this.history),(this.linked||(this.linked=[])).push({doc:l,sharedHist:e.sharedHist}),l.linked=[{doc:this,isParent:!0,sharedHist:e.sharedHist}],Iy(l,Xh(this)),l},unlinkDoc:function(e){if(e instanceof Ke&&(e=e.doc),this.linked)for(var r=0;r<this.linked.length;++r){var o=this.linked[r];if(o.doc==e){this.linked.splice(r,1),e.unlinkDoc(this),Ay(Xh(this));break}}if(e.history==this.history){var l=[e.id];Ur(e,function(s){return l.push(s.id)},!0),e.history=new _s(null),e.history.done=Vi(this.history.done,l),e.history.undone=Vi(this.history.undone,l)}},iterLinkedDocs:function(e){Ur(this,e)},getMode:function(){return this.mode},getEditor:function(){return this.cm},splitLines:function(e){return this.lineSep?e.split(this.lineSep):Hl(e)},lineSeparator:function(){return this.lineSep||`
`},setDirection:mt(function(e){e!="rtl"&&(e="ltr"),e!=this.direction&&(this.direction=e,this.iter(function(r){return r.order=null}),this.cm&&Ly(this.cm))})}),Kt.prototype.eachLine=Kt.prototype.iter;var Zh=0;function Ry(e){var r=this;if(Jh(r),!(Xe(r,e)||gr(r.display,e))){Nt(e),y&&(Zh=+new Date);var o=vi(r,e,!0),l=e.dataTransfer.files;if(!(!o||r.isReadOnly()))if(l&&l.length&&window.FileReader&&window.File)for(var s=l.length,f=Array(s),d=0,h=function(){++d==s&&gt(r,function(){o=Se(r.doc,o);var P={from:o,to:o,text:r.doc.splitLines(f.filter(function(H){return H!=null}).join(r.doc.lineSeparator())),origin:"paste"};qi(r.doc,P),Fh(r.doc,Hr(Se(r.doc,o),Se(r.doc,Br(P))))})()},v=function(P,H){if(r.options.allowDropFileTypes&&Le(r.options.allowDropFileTypes,P.type)==-1){h();return}var j=new FileReader;j.onerror=function(){return h()},j.onload=function(){var V=j.result;if(/[\x00-\x08\x0e-\x1f]{2}/.test(V)){h();return}f[H]=V,h()},j.readAsText(P)},m=0;m<l.length;m++)v(l[m],m);else{if(r.state.draggingText&&r.doc.sel.contains(o)>-1){r.state.draggingText(e),setTimeout(function(){return r.display.input.focus()},20);return}try{var x=e.dataTransfer.getData("Text");if(x){var T;if(r.state.draggingText&&!r.state.draggingText.copy&&(T=r.listSelections()),Ds(r.doc,Hr(o,o)),T)for(var z=0;z<T.length;++z)Xi(r.doc,"",T[z].anchor,T[z].head,"drag");r.replaceSelection(x,"around","paste"),r.display.input.focus()}}catch{}}}}function Wy(e,r){if(y&&(!e.state.draggingText||+new Date-Zh<100)){fr(r);return}if(!(Xe(e,r)||gr(e.display,r))&&(r.dataTransfer.setData("Text",e.getSelection()),r.dataTransfer.effectAllowed="copyMove",r.dataTransfer.setDragImage&&!oe)){var o=M("img",null,null,"position: fixed; left: 0; top: 0;");o.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",B&&(o.width=o.height=1,e.display.wrapper.appendChild(o),o._top=o.offsetTop),r.dataTransfer.setDragImage(o,0,0),B&&o.parentNode.removeChild(o)}}function Hy(e,r){var o=vi(e,r);if(o){var l=document.createDocumentFragment();za(e,o,l),e.display.dragCursor||(e.display.dragCursor=M("div",null,"CodeMirror-cursors CodeMirror-dragcursors"),e.display.lineSpace.insertBefore(e.display.dragCursor,e.display.cursorDiv)),ne(e.display.dragCursor,l)}}function Jh(e){e.display.dragCursor&&(e.display.lineSpace.removeChild(e.display.dragCursor),e.display.dragCursor=null)}function ep(e){if(document.getElementsByClassName){for(var r=document.getElementsByClassName("CodeMirror"),o=[],l=0;l<r.length;l++){var s=r[l].CodeMirror;s&&o.push(s)}o.length&&o[0].operation(function(){for(var f=0;f<o.length;f++)e(o[f])})}}var tp=!1;function By(){tp||(Uy(),tp=!0)}function Uy(){var e;re(window,"resize",function(){e==null&&(e=setTimeout(function(){e=null,ep(jy)},100))}),re(window,"blur",function(){return ep(Ki)})}function jy(e){var r=e.display;r.cachedCharWidth=r.cachedTextHeight=r.cachedPaddingH=null,r.scrollbarsClipped=!1,e.setSize()}for(var $r={3:"Pause",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",61:"=",91:"Mod",92:"Mod",93:"Mod",106:"*",107:"=",109:"-",110:".",111:"/",145:"ScrollLock",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",224:"Mod",63232:"Up",63233:"Down",63234:"Left",63235:"Right",63272:"Delete",63273:"Home",63275:"End",63276:"PageUp",63277:"PageDown",63302:"Insert"},uo=0;uo<10;uo++)$r[uo+48]=$r[uo+96]=String(uo);for(var As=65;As<=90;As++)$r[As]=String.fromCharCode(As);for(var ao=1;ao<=12;ao++)$r[ao+111]=$r[ao+63235]="F"+ao;var yr={};yr.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharAfter",Backspace:"delCharBefore","Shift-Backspace":"delCharBefore",Tab:"defaultTab","Shift-Tab":"indentAuto",Enter:"newlineAndIndent",Insert:"toggleOverwrite",Esc:"singleSelection"},yr.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Up":"goLineUp","Ctrl-Down":"goLineDown","Ctrl-Left":"goGroupLeft","Ctrl-Right":"goGroupRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delGroupBefore","Ctrl-Delete":"delGroupAfter","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll","Ctrl-[":"indentLess","Ctrl-]":"indentMore","Ctrl-U":"undoSelection","Shift-Ctrl-U":"redoSelection","Alt-U":"redoSelection",fallthrough:"basic"},yr.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageDown","Shift-Ctrl-V":"goPageUp","Ctrl-D":"delCharAfter","Ctrl-H":"delCharBefore","Alt-Backspace":"delWordBefore","Ctrl-K":"killLine","Ctrl-T":"transposeChars","Ctrl-O":"openLine"},yr.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Home":"goDocStart","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goGroupLeft","Alt-Right":"goGroupRight","Cmd-Left":"goLineLeft","Cmd-Right":"goLineRight","Alt-Backspace":"delGroupBefore","Ctrl-Alt-Backspace":"delGroupAfter","Alt-Delete":"delGroupAfter","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll","Cmd-[":"indentLess","Cmd-]":"indentMore","Cmd-Backspace":"delWrappedLineLeft","Cmd-Delete":"delWrappedLineRight","Cmd-U":"undoSelection","Shift-Cmd-U":"redoSelection","Ctrl-Up":"goDocStart","Ctrl-Down":"goDocEnd",fallthrough:["basic","emacsy"]},yr.default=Y?yr.macDefault:yr.pcDefault;function $y(e){var r=e.split(/-(?!$)/);e=r[r.length-1];for(var o,l,s,f,d=0;d<r.length-1;d++){var h=r[d];if(/^(cmd|meta|m)$/i.test(h))f=!0;else if(/^a(lt)?$/i.test(h))o=!0;else if(/^(c|ctrl|control)$/i.test(h))l=!0;else if(/^s(hift)?$/i.test(h))s=!0;else throw new Error("Unrecognized modifier name: "+h)}return o&&(e="Alt-"+e),l&&(e="Ctrl-"+e),f&&(e="Cmd-"+e),s&&(e="Shift-"+e),e}function Ky(e){var r={};for(var o in e)if(e.hasOwnProperty(o)){var l=e[o];if(/^(name|fallthrough|(de|at)tach)$/.test(o))continue;if(l=="..."){delete e[o];continue}for(var s=sr(o.split(" "),$y),f=0;f<s.length;f++){var d=void 0,h=void 0;f==s.length-1?(h=s.join(" "),d=l):(h=s.slice(0,f+1).join(" "),d="...");var v=r[h];if(!v)r[h]=d;else if(v!=d)throw new Error("Inconsistent bindings for "+h)}delete e[o]}for(var m in r)e[m]=r[m];return e}function Ji(e,r,o,l){r=Fs(r);var s=r.call?r.call(e,l):r[e];if(s===!1)return"nothing";if(s==="...")return"multi";if(s!=null&&o(s))return"handled";if(r.fallthrough){if(Object.prototype.toString.call(r.fallthrough)!="[object Array]")return Ji(e,r.fallthrough,o,l);for(var f=0;f<r.fallthrough.length;f++){var d=Ji(e,r.fallthrough[f],o,l);if(d)return d}}}function np(e){var r=typeof e=="string"?e:$r[e.keyCode];return r=="Ctrl"||r=="Alt"||r=="Shift"||r=="Mod"}function rp(e,r,o){var l=e;return r.altKey&&l!="Alt"&&(e="Alt-"+e),(ke?r.metaKey:r.ctrlKey)&&l!="Ctrl"&&(e="Ctrl-"+e),(ke?r.ctrlKey:r.metaKey)&&l!="Mod"&&(e="Cmd-"+e),!o&&r.shiftKey&&l!="Shift"&&(e="Shift-"+e),e}function ip(e,r){if(B&&e.keyCode==34&&e.char)return!1;var o=$r[e.keyCode];return o==null||e.altGraphKey?!1:(e.keyCode==3&&e.code&&(o=e.code),rp(o,e,r))}function Fs(e){return typeof e=="string"?yr[e]:e}function el(e,r){for(var o=e.doc.sel.ranges,l=[],s=0;s<o.length;s++){for(var f=r(o[s]);l.length&&R(f.from,Ne(l).to)<=0;){var d=l.pop();if(R(d.from,f.from)<0){f.from=d.from;break}}l.push(f)}tn(e,function(){for(var h=l.length-1;h>=0;h--)Xi(e.doc,"",l[h].from,l[h].to,"+delete");Gi(e)})}function Xa(e,r,o){var l=Me(e.text,r+o,o);return l<0||l>e.text.length?null:l}function Za(e,r,o){var l=Xa(e,r.ch,o);return l==null?null:new b(r.line,l,o<0?"after":"before")}function Ja(e,r,o,l,s){if(e){r.doc.direction=="rtl"&&(s=-s);var f=Cn(o,r.doc.direction);if(f){var d=s<0?Ne(f):f[0],h=s<0==(d.level==1),v=h?"after":"before",m;if(d.level>0||r.doc.direction=="rtl"){var x=Ui(r,o);m=s<0?o.text.length-1:0;var T=er(r,x,m).top;m=Te(function(z){return er(r,x,z).top==T},s<0==(d.level==1)?d.from:d.to-1,m),v=="before"&&(m=Xa(o,m,1))}else m=s<0?d.to:d.from;return new b(l,m,v)}}return new b(l,s<0?o.text.length:0,s<0?"before":"after")}function Gy(e,r,o,l){var s=Cn(r,e.doc.direction);if(!s)return Za(r,o,l);o.ch>=r.text.length?(o.ch=r.text.length,o.sticky="before"):o.ch<=0&&(o.ch=0,o.sticky="after");var f=kn(s,o.ch,o.sticky),d=s[f];if(e.doc.direction=="ltr"&&d.level%2==0&&(l>0?d.to>o.ch:d.from<o.ch))return Za(r,o,l);var h=function(ee,le){return Xa(r,ee instanceof b?ee.ch:ee,le)},v,m=function(ee){return e.options.lineWrapping?(v=v||Ui(e,r),ah(e,r,v,ee)):{begin:0,end:r.text.length}},x=m(o.sticky=="before"?h(o,-1):o.ch);if(e.doc.direction=="rtl"||d.level==1){var T=d.level==1==l<0,z=h(o,T?1:-1);if(z!=null&&(T?z<=d.to&&z<=x.end:z>=d.from&&z>=x.begin)){var P=T?"before":"after";return new b(o.line,z,P)}}var H=function(ee,le,te){for(var se=function(He,yt){return yt?new b(o.line,h(He,1),"before"):new b(o.line,He,"after")};ee>=0&&ee<s.length;ee+=le){var ve=s[ee],pe=le>0==(ve.level!=1),Ee=pe?te.begin:h(te.end,-1);if(ve.from<=Ee&&Ee<ve.to||(Ee=pe?ve.from:h(ve.to,-1),te.begin<=Ee&&Ee<te.end))return se(Ee,pe)}},j=H(f+l,l,x);if(j)return j;var V=l>0?x.end:h(x.begin,-1);return V!=null&&!(l>0&&V==r.text.length)&&(j=H(l>0?0:s.length-1,l,m(V)),j)?j:null}var fo={selectAll:Uh,singleSelection:function(e){return e.setSelection(e.getCursor("anchor"),e.getCursor("head"),je)},killLine:function(e){return el(e,function(r){if(r.empty()){var o=ce(e.doc,r.head.line).text.length;return r.head.ch==o&&r.head.line<e.lastLine()?{from:r.head,to:b(r.head.line+1,0)}:{from:r.head,to:b(r.head.line,o)}}else return{from:r.from(),to:r.to()}})},deleteLine:function(e){return el(e,function(r){return{from:b(r.from().line,0),to:Se(e.doc,b(r.to().line+1,0))}})},delLineLeft:function(e){return el(e,function(r){return{from:b(r.from().line,0),to:r.from()}})},delWrappedLineLeft:function(e){return el(e,function(r){var o=e.charCoords(r.head,"div").top+5,l=e.coordsChar({left:0,top:o},"div");return{from:l,to:r.from()}})},delWrappedLineRight:function(e){return el(e,function(r){var o=e.charCoords(r.head,"div").top+5,l=e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:o},"div");return{from:r.from(),to:l}})},undo:function(e){return e.undo()},redo:function(e){return e.redo()},undoSelection:function(e){return e.undoSelection()},redoSelection:function(e){return e.redoSelection()},goDocStart:function(e){return e.extendSelection(b(e.firstLine(),0))},goDocEnd:function(e){return e.extendSelection(b(e.lastLine()))},goLineStart:function(e){return e.extendSelectionsBy(function(r){return lp(e,r.head.line)},{origin:"+move",bias:1})},goLineStartSmart:function(e){return e.extendSelectionsBy(function(r){return op(e,r.head)},{origin:"+move",bias:1})},goLineEnd:function(e){return e.extendSelectionsBy(function(r){return Qy(e,r.head.line)},{origin:"+move",bias:-1})},goLineRight:function(e){return e.extendSelectionsBy(function(r){var o=e.cursorCoords(r.head,"div").top+5;return e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:o},"div")},Vn)},goLineLeft:function(e){return e.extendSelectionsBy(function(r){var o=e.cursorCoords(r.head,"div").top+5;return e.coordsChar({left:0,top:o},"div")},Vn)},goLineLeftSmart:function(e){return e.extendSelectionsBy(function(r){var o=e.cursorCoords(r.head,"div").top+5,l=e.coordsChar({left:0,top:o},"div");return l.ch<e.getLine(l.line).search(/\S/)?op(e,r.head):l},Vn)},goLineUp:function(e){return e.moveV(-1,"line")},goLineDown:function(e){return e.moveV(1,"line")},goPageUp:function(e){return e.moveV(-1,"page")},goPageDown:function(e){return e.moveV(1,"page")},goCharLeft:function(e){return e.moveH(-1,"char")},goCharRight:function(e){return e.moveH(1,"char")},goColumnLeft:function(e){return e.moveH(-1,"column")},goColumnRight:function(e){return e.moveH(1,"column")},goWordLeft:function(e){return e.moveH(-1,"word")},goGroupRight:function(e){return e.moveH(1,"group")},goGroupLeft:function(e){return e.moveH(-1,"group")},goWordRight:function(e){return e.moveH(1,"word")},delCharBefore:function(e){return e.deleteH(-1,"codepoint")},delCharAfter:function(e){return e.deleteH(1,"char")},delWordBefore:function(e){return e.deleteH(-1,"word")},delWordAfter:function(e){return e.deleteH(1,"word")},delGroupBefore:function(e){return e.deleteH(-1,"group")},delGroupAfter:function(e){return e.deleteH(1,"group")},indentAuto:function(e){return e.indentSelection("smart")},indentMore:function(e){return e.indentSelection("add")},indentLess:function(e){return e.indentSelection("subtract")},insertTab:function(e){return e.replaceSelection("	")},insertSoftTab:function(e){for(var r=[],o=e.listSelections(),l=e.options.tabSize,s=0;s<o.length;s++){var f=o[s].from(),d=fe(e.getLine(f.line),f.ch,l);r.push(cn(l-d%l))}e.replaceSelections(r)},defaultTab:function(e){e.somethingSelected()?e.indentSelection("add"):e.execCommand("insertTab")},transposeChars:function(e){return tn(e,function(){for(var r=e.listSelections(),o=[],l=0;l<r.length;l++)if(r[l].empty()){var s=r[l].head,f=ce(e.doc,s.line).text;if(f){if(s.ch==f.length&&(s=new b(s.line,s.ch-1)),s.ch>0)s=new b(s.line,s.ch+1),e.replaceRange(f.charAt(s.ch-1)+f.charAt(s.ch-2),b(s.line,s.ch-2),s,"+transpose");else if(s.line>e.doc.first){var d=ce(e.doc,s.line-1).text;d&&(s=new b(s.line,1),e.replaceRange(f.charAt(0)+e.doc.lineSeparator()+d.charAt(d.length-1),b(s.line-1,d.length-1),s,"+transpose"))}}o.push(new Ie(s,s))}e.setSelections(o)})},newlineAndIndent:function(e){return tn(e,function(){for(var r=e.listSelections(),o=r.length-1;o>=0;o--)e.replaceRange(e.doc.lineSeparator(),r[o].anchor,r[o].head,"+input");r=e.listSelections();for(var l=0;l<r.length;l++)e.indentLine(r[l].from().line,null,!0);Gi(e)})},openLine:function(e){return e.replaceSelection(`
`,"start")},toggleOverwrite:function(e){return e.toggleOverwrite()}};function lp(e,r){var o=ce(e.doc,r),l=In(o);return l!=o&&(r=ze(l)),Ja(!0,e,l,r,1)}function Qy(e,r){var o=ce(e.doc,r),l=_m(o);return l!=o&&(r=ze(l)),Ja(!0,e,o,r,-1)}function op(e,r){var o=lp(e,r.line),l=ce(e.doc,o.line),s=Cn(l,e.doc.direction);if(!s||s[0].level==0){var f=Math.max(o.ch,l.text.search(/\S/)),d=r.line==o.line&&r.ch<=f&&r.ch;return b(o.line,d?0:f,o.sticky)}return o}function Rs(e,r,o){if(typeof r=="string"&&(r=fo[r],!r))return!1;e.display.input.ensurePolled();var l=e.display.shift,s=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),o&&(e.display.shift=!1),s=r(e)!=Ae}finally{e.display.shift=l,e.state.suppressEdits=!1}return s}function Vy(e,r,o){for(var l=0;l<e.state.keyMaps.length;l++){var s=Ji(r,e.state.keyMaps[l],o,e);if(s)return s}return e.options.extraKeys&&Ji(r,e.options.extraKeys,o,e)||Ji(r,e.options.keyMap,o,e)}var Yy=new Ce;function co(e,r,o,l){var s=e.state.keySeq;if(s){if(np(r))return"handled";if(/\'$/.test(r)?e.state.keySeq=null:Yy.set(50,function(){e.state.keySeq==s&&(e.state.keySeq=null,e.display.input.reset())}),sp(e,s+" "+r,o,l))return!0}return sp(e,r,o,l)}function sp(e,r,o,l){var s=Vy(e,r,l);return s=="multi"&&(e.state.keySeq=r),s=="handled"&&vt(e,"keyHandled",e,r,o),(s=="handled"||s=="multi")&&(Nt(o),Ia(e)),!!s}function up(e,r){var o=ip(r,!0);return o?r.shiftKey&&!e.state.keySeq?co(e,"Shift-"+o,r,function(l){return Rs(e,l,!0)})||co(e,o,r,function(l){if(typeof l=="string"?/^go[A-Z]/.test(l):l.motion)return Rs(e,l)}):co(e,o,r,function(l){return Rs(e,l)}):!1}function qy(e,r,o){return co(e,"'"+o+"'",r,function(l){return Rs(e,l,!0)})}var ef=null;function ap(e){var r=this;if(!(e.target&&e.target!=r.display.input.getField())&&(r.curOp.focus=E(ye(r)),!Xe(r,e))){y&&S<11&&e.keyCode==27&&(e.returnValue=!1);var o=e.keyCode;r.display.shift=o==16||e.shiftKey;var l=up(r,e);B&&(ef=l?o:null,!l&&o==88&&!Yn&&(Y?e.metaKey:e.ctrlKey)&&r.replaceSelection("",null,"cut")),a&&!Y&&!l&&o==46&&e.shiftKey&&!e.ctrlKey&&document.execCommand&&document.execCommand("cut"),o==18&&!/\bCodeMirror-crosshair\b/.test(r.display.lineDiv.className)&&Xy(r)}}function Xy(e){var r=e.display.lineDiv;F(r,"CodeMirror-crosshair");function o(l){(l.keyCode==18||!l.altKey)&&(K(r,"CodeMirror-crosshair"),Et(document,"keyup",o),Et(document,"mouseover",o))}re(document,"keyup",o),re(document,"mouseover",o)}function fp(e){e.keyCode==16&&(this.doc.sel.shift=!1),Xe(this,e)}function cp(e){var r=this;if(!(e.target&&e.target!=r.display.input.getField())&&!(gr(r.display,e)||Xe(r,e)||e.ctrlKey&&!e.altKey||Y&&e.metaKey)){var o=e.keyCode,l=e.charCode;if(B&&o==ef){ef=null,Nt(e);return}if(!(B&&(!e.which||e.which<10)&&up(r,e))){var s=String.fromCharCode(l??o);s!="\b"&&(qy(r,e,s)||r.display.input.onKeyPress(e))}}}var Zy=400,tf=function(e,r,o){this.time=e,this.pos=r,this.button=o};tf.prototype.compare=function(e,r,o){return this.time+Zy>e&&R(r,this.pos)==0&&o==this.button};var ho,po;function Jy(e,r){var o=+new Date;return po&&po.compare(o,e,r)?(ho=po=null,"triple"):ho&&ho.compare(o,e,r)?(po=new tf(o,e,r),ho=null,"double"):(ho=new tf(o,e,r),po=null,"single")}function dp(e){var r=this,o=r.display;if(!(Xe(r,e)||o.activeTouch&&o.input.supportsTouch())){if(o.input.ensurePolled(),o.shift=e.shiftKey,gr(o,e)){N||(o.scroller.draggable=!1,setTimeout(function(){return o.scroller.draggable=!0},100));return}if(!nf(r,e)){var l=vi(r,e),s=Wl(e),f=l?Jy(l,s):"single";Be(r).focus(),s==1&&r.state.selectingText&&r.state.selectingText(e),!(l&&e1(r,s,l,f,e))&&(s==1?l?n1(r,l,f,e):jt(e)==o.scroller&&Nt(e):s==2?(l&&Os(r.doc,l),setTimeout(function(){return o.input.focus()},20)):s==3&&(me?r.display.input.onContextMenu(e):Aa(r)))}}}function e1(e,r,o,l,s){var f="Click";return l=="double"?f="Double"+f:l=="triple"&&(f="Triple"+f),f=(r==1?"Left":r==2?"Middle":"Right")+f,co(e,rp(f,s),s,function(d){if(typeof d=="string"&&(d=fo[d]),!d)return!1;var h=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),h=d(e,o)!=Ae}finally{e.state.suppressEdits=!1}return h})}function t1(e,r,o){var l=e.getOption("configureMouse"),s=l?l(e,r,o):{};if(s.unit==null){var f=ie?o.shiftKey&&o.metaKey:o.altKey;s.unit=f?"rectangle":r=="single"?"char":r=="double"?"word":"line"}return(s.extend==null||e.doc.extend)&&(s.extend=e.doc.extend||o.shiftKey),s.addNew==null&&(s.addNew=Y?o.metaKey:o.ctrlKey),s.moveOnDrag==null&&(s.moveOnDrag=!(Y?o.altKey:o.ctrlKey)),s}function n1(e,r,o,l){y?setTimeout(Ve(hh,e),0):e.curOp.focus=E(ye(e));var s=t1(e,o,l),f=e.doc.sel,d;e.options.dragDrop&&pa&&!e.isReadOnly()&&o=="single"&&(d=f.contains(r))>-1&&(R((d=f.ranges[d]).from(),r)<0||r.xRel>0)&&(R(d.to(),r)>0||r.xRel<0)?r1(e,l,r,s):i1(e,l,r,s)}function r1(e,r,o,l){var s=e.display,f=!1,d=gt(e,function(m){N&&(s.scroller.draggable=!1),e.state.draggingText=!1,e.state.delayingBlurEvent&&(e.hasFocus()?e.state.delayingBlurEvent=!1:Aa(e)),Et(s.wrapper.ownerDocument,"mouseup",d),Et(s.wrapper.ownerDocument,"mousemove",h),Et(s.scroller,"dragstart",v),Et(s.scroller,"drop",d),f||(Nt(m),l.addNew||Os(e.doc,o,null,null,l.extend),N&&!oe||y&&S==9?setTimeout(function(){s.wrapper.ownerDocument.body.focus({preventScroll:!0}),s.input.focus()},20):s.input.focus())}),h=function(m){f=f||Math.abs(r.clientX-m.clientX)+Math.abs(r.clientY-m.clientY)>=10},v=function(){return f=!0};N&&(s.scroller.draggable=!0),e.state.draggingText=d,d.copy=!l.moveOnDrag,re(s.wrapper.ownerDocument,"mouseup",d),re(s.wrapper.ownerDocument,"mousemove",h),re(s.scroller,"dragstart",v),re(s.scroller,"drop",d),e.state.delayingBlurEvent=!0,setTimeout(function(){return s.input.focus()},20),s.scroller.dragDrop&&s.scroller.dragDrop()}function hp(e,r,o){if(o=="char")return new Ie(r,r);if(o=="word")return e.findWordAt(r);if(o=="line")return new Ie(b(r.line,0),Se(e.doc,b(r.line+1,0)));var l=o(e,r);return new Ie(l.from,l.to)}function i1(e,r,o,l){y&&Aa(e);var s=e.display,f=e.doc;Nt(r);var d,h,v=f.sel,m=v.ranges;if(l.addNew&&!l.extend?(h=f.sel.contains(o),h>-1?d=m[h]:d=new Ie(o,o)):(d=f.sel.primary(),h=f.sel.primIndex),l.unit=="rectangle")l.addNew||(d=new Ie(o,o)),o=vi(e,r,!0,!0),h=-1;else{var x=hp(e,o,l.unit);l.extend?d=Ya(d,x.anchor,x.head,l.extend):d=x}l.addNew?h==-1?(h=m.length,Mt(f,Fn(e,m.concat([d]),h),{scroll:!1,origin:"*mouse"})):m.length>1&&m[h].empty()&&l.unit=="char"&&!l.extend?(Mt(f,Fn(e,m.slice(0,h).concat(m.slice(h+1)),0),{scroll:!1,origin:"*mouse"}),v=f.sel):qa(f,h,d,Qn):(h=0,Mt(f,new pn([d],0),Qn),v=f.sel);var T=o;function z(te){if(R(T,te)!=0)if(T=te,l.unit=="rectangle"){for(var se=[],ve=e.options.tabSize,pe=fe(ce(f,o.line).text,o.ch,ve),Ee=fe(ce(f,te.line).text,te.ch,ve),He=Math.min(pe,Ee),yt=Math.max(pe,Ee),Ye=Math.min(o.line,te.line),nn=Math.min(e.lastLine(),Math.max(o.line,te.line));Ye<=nn;Ye++){var Gt=ce(f,Ye).text,ot=xn(Gt,He,ve);He==yt?se.push(new Ie(b(Ye,ot),b(Ye,ot))):Gt.length>ot&&se.push(new Ie(b(Ye,ot),b(Ye,xn(Gt,yt,ve))))}se.length||se.push(new Ie(o,o)),Mt(f,Fn(e,v.ranges.slice(0,h).concat(se),h),{origin:"*mouse",scroll:!1}),e.scrollIntoView(te)}else{var Qt=d,Tt=hp(e,te,l.unit),dt=Qt.anchor,st;R(Tt.anchor,dt)>0?(st=Tt.head,dt=ct(Qt.from(),Tt.anchor)):(st=Tt.anchor,dt=Oe(Qt.to(),Tt.head));var Je=v.ranges.slice(0);Je[h]=l1(e,new Ie(Se(f,dt),st)),Mt(f,Fn(e,Je,h),Qn)}}var P=s.wrapper.getBoundingClientRect(),H=0;function j(te){var se=++H,ve=vi(e,te,!0,l.unit=="rectangle");if(ve)if(R(ve,T)!=0){e.curOp.focus=E(ye(e)),z(ve);var pe=Es(s,f);(ve.line>=pe.to||ve.line<pe.from)&&setTimeout(gt(e,function(){H==se&&j(te)}),150)}else{var Ee=te.clientY<P.top?-20:te.clientY>P.bottom?20:0;Ee&&setTimeout(gt(e,function(){H==se&&(s.scroller.scrollTop+=Ee,j(te))}),50)}}function V(te){e.state.selectingText=!1,H=1/0,te&&(Nt(te),s.input.focus()),Et(s.wrapper.ownerDocument,"mousemove",ee),Et(s.wrapper.ownerDocument,"mouseup",le),f.history.lastSelOrigin=null}var ee=gt(e,function(te){te.buttons===0||!Wl(te)?V(te):j(te)}),le=gt(e,V);e.state.selectingText=le,re(s.wrapper.ownerDocument,"mousemove",ee),re(s.wrapper.ownerDocument,"mouseup",le)}function l1(e,r){var o=r.anchor,l=r.head,s=ce(e.doc,o.line);if(R(o,l)==0&&o.sticky==l.sticky)return r;var f=Cn(s);if(!f)return r;var d=kn(f,o.ch,o.sticky),h=f[d];if(h.from!=o.ch&&h.to!=o.ch)return r;var v=d+(h.from==o.ch==(h.level!=1)?0:1);if(v==0||v==f.length)return r;var m;if(l.line!=o.line)m=(l.line-o.line)*(e.doc.direction=="ltr"?1:-1)>0;else{var x=kn(f,l.ch,l.sticky),T=x-d||(l.ch-o.ch)*(h.level==1?-1:1);x==v-1||x==v?m=T<0:m=T>0}var z=f[v+(m?-1:0)],P=m==(z.level==1),H=P?z.from:z.to,j=P?"after":"before";return o.ch==H&&o.sticky==j?r:new Ie(new b(o.line,H,j),l)}function pp(e,r,o,l){var s,f;if(r.touches)s=r.touches[0].clientX,f=r.touches[0].clientY;else try{s=r.clientX,f=r.clientY}catch{return!1}if(s>=Math.floor(e.display.gutters.getBoundingClientRect().right))return!1;l&&Nt(r);var d=e.display,h=d.lineDiv.getBoundingClientRect();if(f>h.bottom||!Dt(e,o))return Rl(r);f-=h.top-d.viewOffset;for(var v=0;v<e.display.gutterSpecs.length;++v){var m=d.gutters.childNodes[v];if(m&&m.getBoundingClientRect().right>=s){var x=Xn(e.doc,f),T=e.display.gutterSpecs[v];return Ue(e,o,e,x,T.className,r),Rl(r)}}}function nf(e,r){return pp(e,r,"gutterClick",!0)}function vp(e,r){gr(e.display,r)||o1(e,r)||Xe(e,r,"contextmenu")||me||e.display.input.onContextMenu(r)}function o1(e,r){return Dt(e,"gutterContextMenu")?pp(e,r,"gutterContextMenu",!1):!1}function gp(e){e.display.wrapper.className=e.display.wrapper.className.replace(/\s*cm-s-\S+/g,"")+e.options.theme.replace(/(^|\s)\s*/g," cm-s-"),Ql(e)}var tl={toString:function(){return"CodeMirror.Init"}},mp={},Ws={};function s1(e){var r=e.optionHandlers;function o(l,s,f,d){e.defaults[l]=s,f&&(r[l]=d?function(h,v,m){m!=tl&&f(h,v,m)}:f)}e.defineOption=o,e.Init=tl,o("value","",function(l,s){return l.setValue(s)},!0),o("mode",null,function(l,s){l.doc.modeOption=s,Ga(l)},!0),o("indentUnit",2,Ga,!0),o("indentWithTabs",!1),o("smartIndent",!0),o("tabSize",4,function(l){to(l),Ql(l),$t(l)},!0),o("lineSeparator",null,function(l,s){if(l.doc.lineSep=s,!!s){var f=[],d=l.doc.first;l.doc.iter(function(v){for(var m=0;;){var x=v.text.indexOf(s,m);if(x==-1)break;m=x+s.length,f.push(b(d,x))}d++});for(var h=f.length-1;h>=0;h--)Xi(l.doc,s,f[h],b(f[h].line,f[h].ch+s.length))}}),o("specialChars",/[\u0000-\u001f\u007f-\u009f\u00ad\u061c\u200b\u200e\u200f\u2028\u2029\u202d\u202e\u2066\u2067\u2069\ufeff\ufff9-\ufffc]/g,function(l,s,f){l.state.specialChars=new RegExp(s.source+(s.test("	")?"":"|	"),"g"),f!=tl&&l.refresh()}),o("specialCharPlaceholder",Am,function(l){return l.refresh()},!0),o("electricChars",!0),o("inputStyle",$?"contenteditable":"textarea",function(){throw new Error("inputStyle can not (yet) be changed in a running editor")},!0),o("spellcheck",!1,function(l,s){return l.getInputField().spellcheck=s},!0),o("autocorrect",!1,function(l,s){return l.getInputField().autocorrect=s},!0),o("autocapitalize",!1,function(l,s){return l.getInputField().autocapitalize=s},!0),o("rtlMoveVisually",!I),o("wholeLineUpdateBefore",!0),o("theme","default",function(l){gp(l),eo(l)},!0),o("keyMap","default",function(l,s,f){var d=Fs(s),h=f!=tl&&Fs(f);h&&h.detach&&h.detach(l,d),d.attach&&d.attach(l,h||null)}),o("extraKeys",null),o("configureMouse",null),o("lineWrapping",!1,a1,!0),o("gutters",[],function(l,s){l.display.gutterSpecs=$a(s,l.options.lineNumbers),eo(l)},!0),o("fixedGutter",!0,function(l,s){l.display.gutters.style.left=s?Oa(l.display)+"px":"0",l.refresh()},!0),o("coverGutterNextToScrollbar",!1,function(l){return Qi(l)},!0),o("scrollbarStyle","native",function(l){wh(l),Qi(l),l.display.scrollbars.setScrollTop(l.doc.scrollTop),l.display.scrollbars.setScrollLeft(l.doc.scrollLeft)},!0),o("lineNumbers",!1,function(l,s){l.display.gutterSpecs=$a(l.options.gutters,s),eo(l)},!0),o("firstLineNumber",1,eo,!0),o("lineNumberFormatter",function(l){return l},eo,!0),o("showCursorWhenSelecting",!1,Vl,!0),o("resetSelectionOnContextMenu",!0),o("lineWiseCopyCut",!0),o("pasteLinesPerSelection",!0),o("selectionsMayTouch",!1),o("readOnly",!1,function(l,s){s=="nocursor"&&(Ki(l),l.display.input.blur()),l.display.input.readOnlyChanged(s)}),o("screenReaderLabel",null,function(l,s){s=s===""?null:s,l.display.input.screenReaderLabelChanged(s)}),o("disableInput",!1,function(l,s){s||l.display.input.reset()},!0),o("dragDrop",!0,u1),o("allowDropFileTypes",null),o("cursorBlinkRate",530),o("cursorScrollMargin",0),o("cursorHeight",1,Vl,!0),o("singleCursorHeightPerLine",!0,Vl,!0),o("workTime",100),o("workDelay",100),o("flattenSpans",!0,to,!0),o("addModeClass",!1,to,!0),o("pollInterval",100),o("undoDepth",200,function(l,s){return l.doc.history.undoDepth=s}),o("historyEventDelay",1250),o("viewportMargin",10,function(l){return l.refresh()},!0),o("maxHighlightLength",1e4,to,!0),o("moveInputWithCursor",!0,function(l,s){s||l.display.input.resetPosition()}),o("tabindex",null,function(l,s){return l.display.input.getField().tabIndex=s||""}),o("autofocus",null),o("direction","ltr",function(l,s){return l.doc.setDirection(s)},!0),o("phrases",null)}function u1(e,r,o){var l=o&&o!=tl;if(!r!=!l){var s=e.display.dragFunctions,f=r?re:Et;f(e.display.scroller,"dragstart",s.start),f(e.display.scroller,"dragenter",s.enter),f(e.display.scroller,"dragover",s.over),f(e.display.scroller,"dragleave",s.leave),f(e.display.scroller,"drop",s.drop)}}function a1(e){e.options.lineWrapping?(F(e.display.wrapper,"CodeMirror-wrap"),e.display.sizer.style.minWidth="",e.display.sizerWidth=null):(K(e.display.wrapper,"CodeMirror-wrap"),ka(e)),Da(e),$t(e),Ql(e),setTimeout(function(){return Qi(e)},100)}function Ke(e,r){var o=this;if(!(this instanceof Ke))return new Ke(e,r);this.options=r=r?he(r):{},he(mp,r,!1);var l=r.value;typeof l=="string"?l=new Kt(l,r.mode,null,r.lineSeparator,r.direction):r.mode&&(l.modeOption=r.mode),this.doc=l;var s=new Ke.inputStyles[r.inputStyle](this),f=this.display=new ky(e,l,s,r);f.wrapper.CodeMirror=this,gp(this),r.lineWrapping&&(this.display.wrapper.className+=" CodeMirror-wrap"),wh(this),this.state={keyMaps:[],overlays:[],modeGen:0,overwrite:!1,delayingBlurEvent:!1,focused:!1,suppressEdits:!1,pasteIncoming:-1,cutIncoming:-1,selectingText:!1,draggingText:!1,highlight:new Ce,keySeq:null,specialChars:null},r.autofocus&&!$&&f.input.focus(),y&&S<11&&setTimeout(function(){return o.display.input.reset(!0)},20),f1(this),By(),wi(this),this.curOp.forceUpdate=!0,bh(this,l),r.autofocus&&!$||this.hasFocus()?setTimeout(function(){o.hasFocus()&&!o.state.focused&&Fa(o)},20):Ki(this);for(var d in Ws)Ws.hasOwnProperty(d)&&Ws[d](this,r[d],tl);kh(this),r.finishInit&&r.finishInit(this);for(var h=0;h<rf.length;++h)rf[h](this);xi(this),N&&r.lineWrapping&&getComputedStyle(f.lineDiv).textRendering=="optimizelegibility"&&(f.lineDiv.style.textRendering="auto")}Ke.defaults=mp,Ke.optionHandlers=Ws;function f1(e){var r=e.display;re(r.scroller,"mousedown",gt(e,dp)),y&&S<11?re(r.scroller,"dblclick",gt(e,function(v){if(!Xe(e,v)){var m=vi(e,v);if(!(!m||nf(e,v)||gr(e.display,v))){Nt(v);var x=e.findWordAt(m);Os(e.doc,x.anchor,x.head)}}})):re(r.scroller,"dblclick",function(v){return Xe(e,v)||Nt(v)}),re(r.scroller,"contextmenu",function(v){return vp(e,v)}),re(r.input.getField(),"contextmenu",function(v){r.scroller.contains(v.target)||vp(e,v)});var o,l={end:0};function s(){r.activeTouch&&(o=setTimeout(function(){return r.activeTouch=null},1e3),l=r.activeTouch,l.end=+new Date)}function f(v){if(v.touches.length!=1)return!1;var m=v.touches[0];return m.radiusX<=1&&m.radiusY<=1}function d(v,m){if(m.left==null)return!0;var x=m.left-v.left,T=m.top-v.top;return x*x+T*T>20*20}re(r.scroller,"touchstart",function(v){if(!Xe(e,v)&&!f(v)&&!nf(e,v)){r.input.ensurePolled(),clearTimeout(o);var m=+new Date;r.activeTouch={start:m,moved:!1,prev:m-l.end<=300?l:null},v.touches.length==1&&(r.activeTouch.left=v.touches[0].pageX,r.activeTouch.top=v.touches[0].pageY)}}),re(r.scroller,"touchmove",function(){r.activeTouch&&(r.activeTouch.moved=!0)}),re(r.scroller,"touchend",function(v){var m=r.activeTouch;if(m&&!gr(r,v)&&m.left!=null&&!m.moved&&new Date-m.start<300){var x=e.coordsChar(r.activeTouch,"page"),T;!m.prev||d(m,m.prev)?T=new Ie(x,x):!m.prev.prev||d(m,m.prev.prev)?T=e.findWordAt(x):T=new Ie(b(x.line,0),Se(e.doc,b(x.line+1,0))),e.setSelection(T.anchor,T.head),e.focus(),Nt(v)}s()}),re(r.scroller,"touchcancel",s),re(r.scroller,"scroll",function(){r.scroller.clientHeight&&(ql(e,r.scroller.scrollTop),mi(e,r.scroller.scrollLeft,!0),Ue(e,"scroll",e))}),re(r.scroller,"mousewheel",function(v){return Lh(e,v)}),re(r.scroller,"DOMMouseScroll",function(v){return Lh(e,v)}),re(r.wrapper,"scroll",function(){return r.wrapper.scrollTop=r.wrapper.scrollLeft=0}),r.dragFunctions={enter:function(v){Xe(e,v)||fr(v)},over:function(v){Xe(e,v)||(Hy(e,v),fr(v))},start:function(v){return Wy(e,v)},drop:gt(e,Ry),leave:function(v){Xe(e,v)||Jh(e)}};var h=r.input.getField();re(h,"keyup",function(v){return fp.call(e,v)}),re(h,"keydown",gt(e,ap)),re(h,"keypress",gt(e,cp)),re(h,"focus",function(v){return Fa(e,v)}),re(h,"blur",function(v){return Ki(e,v)})}var rf=[];Ke.defineInitHook=function(e){return rf.push(e)};function vo(e,r,o,l){var s=e.doc,f;o==null&&(o="add"),o=="smart"&&(s.mode.indent?f=Ul(e,r).state:o="prev");var d=e.options.tabSize,h=ce(s,r),v=fe(h.text,null,d);h.stateAfter&&(h.stateAfter=null);var m=h.text.match(/^\s*/)[0],x;if(!l&&!/\S/.test(h.text))x=0,o="not";else if(o=="smart"&&(x=s.mode.indent(f,h.text.slice(m.length),h.text),x==Ae||x>150)){if(!l)return;o="prev"}o=="prev"?r>s.first?x=fe(ce(s,r-1).text,null,d):x=0:o=="add"?x=v+e.options.indentUnit:o=="subtract"?x=v-e.options.indentUnit:typeof o=="number"&&(x=v+o),x=Math.max(0,x);var T="",z=0;if(e.options.indentWithTabs)for(var P=Math.floor(x/d);P;--P)z+=d,T+="	";if(z<x&&(T+=cn(x-z)),T!=m)return Xi(s,T,b(r,0),b(r,m.length),"+input"),h.stateAfter=null,!0;for(var H=0;H<s.sel.ranges.length;H++){var j=s.sel.ranges[H];if(j.head.line==r&&j.head.ch<m.length){var V=b(r,m.length);qa(s,H,new Ie(V,V));break}}}var Rn=null;function Hs(e){Rn=e}function lf(e,r,o,l,s){var f=e.doc;e.display.shift=!1,l||(l=f.sel);var d=+new Date-200,h=s=="paste"||e.state.pasteIncoming>d,v=Hl(r),m=null;if(h&&l.ranges.length>1)if(Rn&&Rn.text.join(`
`)==r){if(l.ranges.length%Rn.text.length==0){m=[];for(var x=0;x<Rn.text.length;x++)m.push(f.splitLines(Rn.text[x]))}}else v.length==l.ranges.length&&e.options.pasteLinesPerSelection&&(m=sr(v,function(ee){return[ee]}));for(var T=e.curOp.updateInput,z=l.ranges.length-1;z>=0;z--){var P=l.ranges[z],H=P.from(),j=P.to();P.empty()&&(o&&o>0?H=b(H.line,H.ch-o):e.state.overwrite&&!h?j=b(j.line,Math.min(ce(f,j.line).text.length,j.ch+Ne(v).length)):h&&Rn&&Rn.lineWise&&Rn.text.join(`
`)==v.join(`
`)&&(H=j=b(H.line,0)));var V={from:H,to:j,text:m?m[z%m.length]:v,origin:s||(h?"paste":e.state.cutIncoming>d?"cut":"+input")};qi(e.doc,V),vt(e,"inputRead",e,V)}r&&!h&&wp(e,r),Gi(e),e.curOp.updateInput<2&&(e.curOp.updateInput=T),e.curOp.typing=!0,e.state.pasteIncoming=e.state.cutIncoming=-1}function yp(e,r){var o=e.clipboardData&&e.clipboardData.getData("Text");if(o)return e.preventDefault(),!r.isReadOnly()&&!r.options.disableInput&&r.hasFocus()&&tn(r,function(){return lf(r,o,0,null,"paste")}),!0}function wp(e,r){if(!(!e.options.electricChars||!e.options.smartIndent))for(var o=e.doc.sel,l=o.ranges.length-1;l>=0;l--){var s=o.ranges[l];if(!(s.head.ch>100||l&&o.ranges[l-1].head.line==s.head.line)){var f=e.getModeAt(s.head),d=!1;if(f.electricChars){for(var h=0;h<f.electricChars.length;h++)if(r.indexOf(f.electricChars.charAt(h))>-1){d=vo(e,s.head.line,"smart");break}}else f.electricInput&&f.electricInput.test(ce(e.doc,s.head.line).text.slice(0,s.head.ch))&&(d=vo(e,s.head.line,"smart"));d&&vt(e,"electricInput",e,s.head.line)}}}function xp(e){for(var r=[],o=[],l=0;l<e.doc.sel.ranges.length;l++){var s=e.doc.sel.ranges[l].head.line,f={anchor:b(s,0),head:b(s+1,0)};o.push(f),r.push(e.getRange(f.anchor,f.head))}return{text:r,ranges:o}}function Sp(e,r,o,l){e.setAttribute("autocorrect",o?"":"off"),e.setAttribute("autocapitalize",l?"":"off"),e.setAttribute("spellcheck",!!r)}function kp(){var e=M("textarea",null,null,"position: absolute; bottom: -1em; padding: 0; width: 1px; height: 1em; min-height: 1em; outline: none"),r=M("div",[e],null,"overflow: hidden; position: relative; width: 3px; height: 0px;");return N?e.style.width="1000px":e.setAttribute("wrap","off"),C&&(e.style.border="1px solid black"),Sp(e),r}function c1(e){var r=e.optionHandlers,o=e.helpers={};e.prototype={constructor:e,focus:function(){Be(this).focus(),this.display.input.focus()},setOption:function(l,s){var f=this.options,d=f[l];f[l]==s&&l!="mode"||(f[l]=s,r.hasOwnProperty(l)&&gt(this,r[l])(this,s,d),Ue(this,"optionChange",this,l))},getOption:function(l){return this.options[l]},getDoc:function(){return this.doc},addKeyMap:function(l,s){this.state.keyMaps[s?"push":"unshift"](Fs(l))},removeKeyMap:function(l){for(var s=this.state.keyMaps,f=0;f<s.length;++f)if(s[f]==l||s[f].name==l)return s.splice(f,1),!0},addOverlay:It(function(l,s){var f=l.token?l:e.getMode(this.options,l);if(f.startState)throw new Error("Overlays may not be stateful.");ft(this.state.overlays,{mode:f,modeSpec:l,opaque:s&&s.opaque,priority:s&&s.priority||0},function(d){return d.priority}),this.state.modeGen++,$t(this)}),removeOverlay:It(function(l){for(var s=this.state.overlays,f=0;f<s.length;++f){var d=s[f].modeSpec;if(d==l||typeof l=="string"&&d.name==l){s.splice(f,1),this.state.modeGen++,$t(this);return}}}),indentLine:It(function(l,s,f){typeof s!="string"&&typeof s!="number"&&(s==null?s=this.options.smartIndent?"smart":"prev":s=s?"add":"subtract"),w(this.doc,l)&&vo(this,l,s,f)}),indentSelection:It(function(l){for(var s=this.doc.sel.ranges,f=-1,d=0;d<s.length;d++){var h=s[d];if(h.empty())h.head.line>f&&(vo(this,h.head.line,l,!0),f=h.head.line,d==this.doc.sel.primIndex&&Gi(this));else{var v=h.from(),m=h.to(),x=Math.max(f,v.line);f=Math.min(this.lastLine(),m.line-(m.ch?0:1))+1;for(var T=x;T<f;++T)vo(this,T,l);var z=this.doc.sel.ranges;v.ch==0&&s.length==z.length&&z[d].from().ch>0&&qa(this.doc,d,new Ie(v,z[d].to()),je)}}}),getTokenAt:function(l,s){return Pd(this,l,s)},getLineTokens:function(l,s){return Pd(this,b(l),s,!0)},getTokenTypeAt:function(l){l=Se(this.doc,l);var s=Md(this,ce(this.doc,l.line)),f=0,d=(s.length-1)/2,h=l.ch,v;if(h==0)v=s[2];else for(;;){var m=f+d>>1;if((m?s[m*2-1]:0)>=h)d=m;else if(s[m*2+1]<h)f=m+1;else{v=s[m*2+2];break}}var x=v?v.indexOf("overlay "):-1;return x<0?v:x==0?null:v.slice(0,x-1)},getModeAt:function(l){var s=this.doc.mode;return s.innerMode?e.innerMode(s,this.getTokenAt(l).state).mode:s},getHelper:function(l,s){return this.getHelpers(l,s)[0]},getHelpers:function(l,s){var f=[];if(!o.hasOwnProperty(s))return f;var d=o[s],h=this.getModeAt(l);if(typeof h[s]=="string")d[h[s]]&&f.push(d[h[s]]);else if(h[s])for(var v=0;v<h[s].length;v++){var m=d[h[s][v]];m&&f.push(m)}else h.helperType&&d[h.helperType]?f.push(d[h.helperType]):d[h.name]&&f.push(d[h.name]);for(var x=0;x<d._global.length;x++){var T=d._global[x];T.pred(h,this)&&Le(f,T.val)==-1&&f.push(T.val)}return f},getStateAfter:function(l,s){var f=this.doc;return l=hn(f,l??f.first+f.size-1),Ul(this,l+1,s).state},cursorCoords:function(l,s){var f,d=this.doc.sel.primary();return l==null?f=d.head:typeof l=="object"?f=Se(this.doc,l):f=l?d.from():d.to(),An(this,f,s||"page")},charCoords:function(l,s){return ks(this,Se(this.doc,l),s||"page")},coordsChar:function(l,s){return l=oh(this,l,s||"page"),ba(this,l.left,l.top)},lineAtHeight:function(l,s){return l=oh(this,{top:l,left:0},s||"page").top,Xn(this.doc,l+this.display.viewOffset)},heightAtLine:function(l,s,f){var d=!1,h;if(typeof l=="number"){var v=this.doc.first+this.doc.size-1;l<this.doc.first?l=this.doc.first:l>v&&(l=v,d=!0),h=ce(this.doc,l)}else h=l;return Ss(this,h,{top:0,left:0},s||"page",f||d).top+(d?this.doc.height-vr(h):0)},defaultTextHeight:function(){return ji(this.display)},defaultCharWidth:function(){return $i(this.display)},getViewport:function(){return{from:this.display.viewFrom,to:this.display.viewTo}},addWidget:function(l,s,f,d,h){var v=this.display;l=An(this,Se(this.doc,l));var m=l.bottom,x=l.left;if(s.style.position="absolute",s.setAttribute("cm-ignore-events","true"),this.display.input.setUneditable(s),v.sizer.appendChild(s),d=="over")m=l.top;else if(d=="above"||d=="near"){var T=Math.max(v.wrapper.clientHeight,this.doc.height),z=Math.max(v.sizer.clientWidth,v.lineSpace.clientWidth);(d=="above"||l.bottom+s.offsetHeight>T)&&l.top>s.offsetHeight?m=l.top-s.offsetHeight:l.bottom+s.offsetHeight<=T&&(m=l.bottom),x+s.offsetWidth>z&&(x=z-s.offsetWidth)}s.style.top=m+"px",s.style.left=s.style.right="",h=="right"?(x=v.sizer.clientWidth-s.offsetWidth,s.style.right="0px"):(h=="left"?x=0:h=="middle"&&(x=(v.sizer.clientWidth-s.offsetWidth)/2),s.style.left=x+"px"),f&&uy(this,{left:x,top:m,right:x+s.offsetWidth,bottom:m+s.offsetHeight})},triggerOnKeyDown:It(ap),triggerOnKeyPress:It(cp),triggerOnKeyUp:fp,triggerOnMouseDown:It(dp),execCommand:function(l){if(fo.hasOwnProperty(l))return fo[l].call(null,this)},triggerElectric:It(function(l){wp(this,l)}),findPosH:function(l,s,f,d){var h=1;s<0&&(h=-1,s=-s);for(var v=Se(this.doc,l),m=0;m<s&&(v=of(this.doc,v,h,f,d),!v.hitSide);++m);return v},moveH:It(function(l,s){var f=this;this.extendSelectionsBy(function(d){return f.display.shift||f.doc.extend||d.empty()?of(f.doc,d.head,l,s,f.options.rtlMoveVisually):l<0?d.from():d.to()},Vn)}),deleteH:It(function(l,s){var f=this.doc.sel,d=this.doc;f.somethingSelected()?d.replaceSelection("",null,"+delete"):el(this,function(h){var v=of(d,h.head,l,s,!1);return l<0?{from:v,to:h.head}:{from:h.head,to:v}})}),findPosV:function(l,s,f,d){var h=1,v=d;s<0&&(h=-1,s=-s);for(var m=Se(this.doc,l),x=0;x<s;++x){var T=An(this,m,"div");if(v==null?v=T.left:T.left=v,m=Cp(this,T,h,f),m.hitSide)break}return m},moveV:It(function(l,s){var f=this,d=this.doc,h=[],v=!this.display.shift&&!d.extend&&d.sel.somethingSelected();if(d.extendSelectionsBy(function(x){if(v)return l<0?x.from():x.to();var T=An(f,x.head,"div");x.goalColumn!=null&&(T.left=x.goalColumn),h.push(T.left);var z=Cp(f,T,l,s);return s=="page"&&x==d.sel.primary()&&Wa(f,ks(f,z,"div").top-T.top),z},Vn),h.length)for(var m=0;m<d.sel.ranges.length;m++)d.sel.ranges[m].goalColumn=h[m]}),findWordAt:function(l){var s=this.doc,f=ce(s,l.line).text,d=l.ch,h=l.ch;if(f){var v=this.getHelper(l,"wordChars");(l.sticky=="before"||h==f.length)&&d?--d:++h;for(var m=f.charAt(d),x=ar(m,v)?function(T){return ar(T,v)}:/\s/.test(m)?function(T){return/\s/.test(T)}:function(T){return!/\s/.test(T)&&!ar(T)};d>0&&x(f.charAt(d-1));)--d;for(;h<f.length&&x(f.charAt(h));)++h}return new Ie(b(l.line,d),b(l.line,h))},toggleOverwrite:function(l){l!=null&&l==this.state.overwrite||((this.state.overwrite=!this.state.overwrite)?F(this.display.cursorDiv,"CodeMirror-overwrite"):K(this.display.cursorDiv,"CodeMirror-overwrite"),Ue(this,"overwriteToggle",this,this.state.overwrite))},hasFocus:function(){return this.display.input.getField()==E(ye(this))},isReadOnly:function(){return!!(this.options.readOnly||this.doc.cantEdit)},scrollTo:It(function(l,s){Yl(this,l,s)}),getScrollInfo:function(){var l=this.display.scroller;return{left:l.scrollLeft,top:l.scrollTop,height:l.scrollHeight-Jn(this)-this.display.barHeight,width:l.scrollWidth-Jn(this)-this.display.barWidth,clientHeight:La(this),clientWidth:hi(this)}},scrollIntoView:It(function(l,s){l==null?(l={from:this.doc.sel.primary().head,to:null},s==null&&(s=this.options.cursorScrollMargin)):typeof l=="number"?l={from:b(l,0),to:null}:l.from==null&&(l={from:l,to:null}),l.to||(l.to=l.from),l.margin=s||0,l.from.line!=null?ay(this,l):vh(this,l.from,l.to,l.margin)}),setSize:It(function(l,s){var f=this,d=function(v){return typeof v=="number"||/^\d+$/.test(String(v))?v+"px":v};l!=null&&(this.display.wrapper.style.width=d(l)),s!=null&&(this.display.wrapper.style.height=d(s)),this.options.lineWrapping&&rh(this);var h=this.display.viewFrom;this.doc.iter(h,this.display.viewTo,function(v){if(v.widgets){for(var m=0;m<v.widgets.length;m++)if(v.widgets[m].noHScroll){Rr(f,h,"widget");break}}++h}),this.curOp.forceUpdate=!0,Ue(this,"refresh",this)}),operation:function(l){return tn(this,l)},startOperation:function(){return wi(this)},endOperation:function(){return xi(this)},refresh:It(function(){var l=this.display.cachedTextHeight;$t(this),this.curOp.forceUpdate=!0,Ql(this),Yl(this,this.doc.scrollLeft,this.doc.scrollTop),Ua(this.display),(l==null||Math.abs(l-ji(this.display))>.5||this.options.lineWrapping)&&Da(this),Ue(this,"refresh",this)}),swapDoc:It(function(l){var s=this.doc;return s.cm=null,this.state.selectingText&&this.state.selectingText(),bh(this,l),Ql(this),this.display.input.reset(),Yl(this,l.scrollLeft,l.scrollTop),this.curOp.forceScroll=!0,vt(this,"swapDoc",this,s),s}),phrase:function(l){var s=this.options.phrases;return s&&Object.prototype.hasOwnProperty.call(s,l)?s[l]:l},getInputField:function(){return this.display.input.getField()},getWrapperElement:function(){return this.display.wrapper},getScrollerElement:function(){return this.display.scroller},getGutterElement:function(){return this.display.gutters}},zt(e),e.registerHelper=function(l,s,f){o.hasOwnProperty(l)||(o[l]=e[l]={_global:[]}),o[l][s]=f},e.registerGlobalHelper=function(l,s,f,d){e.registerHelper(l,s,d),o[l]._global.push({pred:f,val:d})}}function of(e,r,o,l,s){var f=r,d=o,h=ce(e,r.line),v=s&&e.direction=="rtl"?-o:o;function m(){var le=r.line+v;return le<e.first||le>=e.first+e.size?!1:(r=new b(le,r.ch,r.sticky),h=ce(e,le))}function x(le){var te;if(l=="codepoint"){var se=h.text.charCodeAt(r.ch+(o>0?0:-1));if(isNaN(se))te=null;else{var ve=o>0?se>=55296&&se<56320:se>=56320&&se<57343;te=new b(r.line,Math.max(0,Math.min(h.text.length,r.ch+o*(ve?2:1))),-o)}}else s?te=Gy(e.cm,h,r,o):te=Za(h,r,o);if(te==null)if(!le&&m())r=Ja(s,e.cm,h,r.line,v);else return!1;else r=te;return!0}if(l=="char"||l=="codepoint")x();else if(l=="column")x(!0);else if(l=="word"||l=="group")for(var T=null,z=l=="group",P=e.cm&&e.cm.getHelper(r,"wordChars"),H=!0;!(o<0&&!x(!H));H=!1){var j=h.text.charAt(r.ch)||`
`,V=ar(j,P)?"w":z&&j==`
`?"n":!z||/\s/.test(j)?null:"p";if(z&&!H&&!V&&(V="s"),T&&T!=V){o<0&&(o=1,x(),r.sticky="after");break}if(V&&(T=V),o>0&&!x(!H))break}var ee=zs(e,r,f,d,!0);return xe(f,ee)&&(ee.hitSide=!0),ee}function Cp(e,r,o,l){var s=e.doc,f=r.left,d;if(l=="page"){var h=Math.min(e.display.wrapper.clientHeight,Be(e).innerHeight||s(e).documentElement.clientHeight),v=Math.max(h-.5*ji(e.display),3);d=(o>0?r.bottom:r.top)+o*v}else l=="line"&&(d=o>0?r.bottom+3:r.top-3);for(var m;m=ba(e,f,d),!!m.outside;){if(o<0?d<=0:d>=s.height){m.hitSide=!0;break}d+=o*5}return m}var Fe=function(e){this.cm=e,this.lastAnchorNode=this.lastAnchorOffset=this.lastFocusNode=this.lastFocusOffset=null,this.polling=new Ce,this.composing=null,this.gracePeriod=!1,this.readDOMTimeout=null};Fe.prototype.init=function(e){var r=this,o=this,l=o.cm,s=o.div=e.lineDiv;s.contentEditable=!0,Sp(s,l.options.spellcheck,l.options.autocorrect,l.options.autocapitalize);function f(h){for(var v=h.target;v;v=v.parentNode){if(v==s)return!0;if(/\bCodeMirror-(?:line)?widget\b/.test(v.className))break}return!1}re(s,"paste",function(h){!f(h)||Xe(l,h)||yp(h,l)||S<=11&&setTimeout(gt(l,function(){return r.updateFromDOM()}),20)}),re(s,"compositionstart",function(h){r.composing={data:h.data,done:!1}}),re(s,"compositionupdate",function(h){r.composing||(r.composing={data:h.data,done:!1})}),re(s,"compositionend",function(h){r.composing&&(h.data!=r.composing.data&&r.readFromDOMSoon(),r.composing.done=!0)}),re(s,"touchstart",function(){return o.forceCompositionEnd()}),re(s,"input",function(){r.composing||r.readFromDOMSoon()});function d(h){if(!(!f(h)||Xe(l,h))){if(l.somethingSelected())Hs({lineWise:!1,text:l.getSelections()}),h.type=="cut"&&l.replaceSelection("",null,"cut");else if(l.options.lineWiseCopyCut){var v=xp(l);Hs({lineWise:!0,text:v.text}),h.type=="cut"&&l.operation(function(){l.setSelections(v.ranges,0,je),l.replaceSelection("",null,"cut")})}else return;if(h.clipboardData){h.clipboardData.clearData();var m=Rn.text.join(`
`);if(h.clipboardData.setData("Text",m),h.clipboardData.getData("Text")==m){h.preventDefault();return}}var x=kp(),T=x.firstChild;l.display.lineSpace.insertBefore(x,l.display.lineSpace.firstChild),T.value=Rn.text.join(`
`);var z=E(s.ownerDocument);ue(T),setTimeout(function(){l.display.lineSpace.removeChild(x),z.focus(),z==s&&o.showPrimarySelection()},50)}}re(s,"copy",d),re(s,"cut",d)},Fe.prototype.screenReaderLabelChanged=function(e){e?this.div.setAttribute("aria-label",e):this.div.removeAttribute("aria-label")},Fe.prototype.prepareSelection=function(){var e=dh(this.cm,!1);return e.focus=E(this.div.ownerDocument)==this.div,e},Fe.prototype.showSelection=function(e,r){!e||!this.cm.display.view.length||((e.focus||r)&&this.showPrimarySelection(),this.showMultipleSelections(e))},Fe.prototype.getSelection=function(){return this.cm.display.wrapper.ownerDocument.getSelection()},Fe.prototype.showPrimarySelection=function(){var e=this.getSelection(),r=this.cm,o=r.doc.sel.primary(),l=o.from(),s=o.to();if(r.display.viewTo==r.display.viewFrom||l.line>=r.display.viewTo||s.line<r.display.viewFrom){e.removeAllRanges();return}var f=Bs(r,e.anchorNode,e.anchorOffset),d=Bs(r,e.focusNode,e.focusOffset);if(!(f&&!f.bad&&d&&!d.bad&&R(ct(f,d),l)==0&&R(Oe(f,d),s)==0)){var h=r.display.view,v=l.line>=r.display.viewFrom&&Tp(r,l)||{node:h[0].measure.map[2],offset:0},m=s.line<r.display.viewTo&&Tp(r,s);if(!m){var x=h[h.length-1].measure,T=x.maps?x.maps[x.maps.length-1]:x.map;m={node:T[T.length-1],offset:T[T.length-2]-T[T.length-3]}}if(!v||!m){e.removeAllRanges();return}var z=e.rangeCount&&e.getRangeAt(0),P;try{P=X(v.node,v.offset,m.offset,m.node)}catch{}P&&(!a&&r.state.focused?(e.collapse(v.node,v.offset),P.collapsed||(e.removeAllRanges(),e.addRange(P))):(e.removeAllRanges(),e.addRange(P)),z&&e.anchorNode==null?e.addRange(z):a&&this.startGracePeriod()),this.rememberSelection()}},Fe.prototype.startGracePeriod=function(){var e=this;clearTimeout(this.gracePeriod),this.gracePeriod=setTimeout(function(){e.gracePeriod=!1,e.selectionChanged()&&e.cm.operation(function(){return e.cm.curOp.selectionChanged=!0})},20)},Fe.prototype.showMultipleSelections=function(e){ne(this.cm.display.cursorDiv,e.cursors),ne(this.cm.display.selectionDiv,e.selection)},Fe.prototype.rememberSelection=function(){var e=this.getSelection();this.lastAnchorNode=e.anchorNode,this.lastAnchorOffset=e.anchorOffset,this.lastFocusNode=e.focusNode,this.lastFocusOffset=e.focusOffset},Fe.prototype.selectionInEditor=function(){var e=this.getSelection();if(!e.rangeCount)return!1;var r=e.getRangeAt(0).commonAncestorContainer;return k(this.div,r)},Fe.prototype.focus=function(){this.cm.options.readOnly!="nocursor"&&((!this.selectionInEditor()||E(this.div.ownerDocument)!=this.div)&&this.showSelection(this.prepareSelection(),!0),this.div.focus())},Fe.prototype.blur=function(){this.div.blur()},Fe.prototype.getField=function(){return this.div},Fe.prototype.supportsTouch=function(){return!0},Fe.prototype.receivedFocus=function(){var e=this,r=this;this.selectionInEditor()?setTimeout(function(){return e.pollSelection()},20):tn(this.cm,function(){return r.cm.curOp.selectionChanged=!0});function o(){r.cm.state.focused&&(r.pollSelection(),r.polling.set(r.cm.options.pollInterval,o))}this.polling.set(this.cm.options.pollInterval,o)},Fe.prototype.selectionChanged=function(){var e=this.getSelection();return e.anchorNode!=this.lastAnchorNode||e.anchorOffset!=this.lastAnchorOffset||e.focusNode!=this.lastFocusNode||e.focusOffset!=this.lastFocusOffset},Fe.prototype.pollSelection=function(){if(!(this.readDOMTimeout!=null||this.gracePeriod||!this.selectionChanged())){var e=this.getSelection(),r=this.cm;if(D&&O&&this.cm.display.gutterSpecs.length&&d1(e.anchorNode)){this.cm.triggerOnKeyDown({type:"keydown",keyCode:8,preventDefault:Math.abs}),this.blur(),this.focus();return}if(!this.composing){this.rememberSelection();var o=Bs(r,e.anchorNode,e.anchorOffset),l=Bs(r,e.focusNode,e.focusOffset);o&&l&&tn(r,function(){Mt(r.doc,Hr(o,l),je),(o.bad||l.bad)&&(r.curOp.selectionChanged=!0)})}}},Fe.prototype.pollContent=function(){this.readDOMTimeout!=null&&(clearTimeout(this.readDOMTimeout),this.readDOMTimeout=null);var e=this.cm,r=e.display,o=e.doc.sel.primary(),l=o.from(),s=o.to();if(l.ch==0&&l.line>e.firstLine()&&(l=b(l.line-1,ce(e.doc,l.line-1).length)),s.ch==ce(e.doc,s.line).text.length&&s.line<e.lastLine()&&(s=b(s.line+1,0)),l.line<r.viewFrom||s.line>r.viewTo-1)return!1;var f,d,h;l.line==r.viewFrom||(f=gi(e,l.line))==0?(d=ze(r.view[0].line),h=r.view[0].node):(d=ze(r.view[f].line),h=r.view[f-1].node.nextSibling);var v=gi(e,s.line),m,x;if(v==r.view.length-1?(m=r.viewTo-1,x=r.lineDiv.lastChild):(m=ze(r.view[v+1].line)-1,x=r.view[v+1].node.previousSibling),!h)return!1;for(var T=e.doc.splitLines(h1(e,h,x,d,m)),z=hr(e.doc,b(d,0),b(m,ce(e.doc,m).text.length));T.length>1&&z.length>1;)if(Ne(T)==Ne(z))T.pop(),z.pop(),m--;else if(T[0]==z[0])T.shift(),z.shift(),d++;else break;for(var P=0,H=0,j=T[0],V=z[0],ee=Math.min(j.length,V.length);P<ee&&j.charCodeAt(P)==V.charCodeAt(P);)++P;for(var le=Ne(T),te=Ne(z),se=Math.min(le.length-(T.length==1?P:0),te.length-(z.length==1?P:0));H<se&&le.charCodeAt(le.length-H-1)==te.charCodeAt(te.length-H-1);)++H;if(T.length==1&&z.length==1&&d==l.line)for(;P&&P>l.ch&&le.charCodeAt(le.length-H-1)==te.charCodeAt(te.length-H-1);)P--,H++;T[T.length-1]=le.slice(0,le.length-H).replace(/^\u200b+/,""),T[0]=T[0].slice(P).replace(/\u200b+$/,"");var ve=b(d,P),pe=b(m,z.length?Ne(z).length-H:0);if(T.length>1||T[0]||R(ve,pe))return Xi(e.doc,T,ve,pe,"+input"),!0},Fe.prototype.ensurePolled=function(){this.forceCompositionEnd()},Fe.prototype.reset=function(){this.forceCompositionEnd()},Fe.prototype.forceCompositionEnd=function(){this.composing&&(clearTimeout(this.readDOMTimeout),this.composing=null,this.updateFromDOM(),this.div.blur(),this.div.focus())},Fe.prototype.readFromDOMSoon=function(){var e=this;this.readDOMTimeout==null&&(this.readDOMTimeout=setTimeout(function(){if(e.readDOMTimeout=null,e.composing)if(e.composing.done)e.composing=null;else return;e.updateFromDOM()},80))},Fe.prototype.updateFromDOM=function(){var e=this;(this.cm.isReadOnly()||!this.pollContent())&&tn(this.cm,function(){return $t(e.cm)})},Fe.prototype.setUneditable=function(e){e.contentEditable="false"},Fe.prototype.onKeyPress=function(e){e.charCode==0||this.composing||(e.preventDefault(),this.cm.isReadOnly()||gt(this.cm,lf)(this.cm,String.fromCharCode(e.charCode==null?e.keyCode:e.charCode),0))},Fe.prototype.readOnlyChanged=function(e){this.div.contentEditable=String(e!="nocursor")},Fe.prototype.onContextMenu=function(){},Fe.prototype.resetPosition=function(){},Fe.prototype.needsContentAttribute=!0;function Tp(e,r){var o=Ea(e,r.line);if(!o||o.hidden)return null;var l=ce(e.doc,r.line),s=Zd(o,l,r.line),f=Cn(l,e.doc.direction),d="left";if(f){var h=kn(f,r.ch);d=h%2?"right":"left"}var v=th(s.map,r.ch,d);return v.offset=v.collapse=="right"?v.end:v.start,v}function d1(e){for(var r=e;r;r=r.parentNode)if(/CodeMirror-gutter-wrapper/.test(r.className))return!0;return!1}function nl(e,r){return r&&(e.bad=!0),e}function h1(e,r,o,l,s){var f="",d=!1,h=e.doc.lineSeparator(),v=!1;function m(P){return function(H){return H.id==P}}function x(){d&&(f+=h,v&&(f+=h),d=v=!1)}function T(P){P&&(x(),f+=P)}function z(P){if(P.nodeType==1){var H=P.getAttribute("cm-text");if(H){T(H);return}var j=P.getAttribute("cm-marker"),V;if(j){var ee=e.findMarks(b(l,0),b(s+1,0),m(+j));ee.length&&(V=ee[0].find(0))&&T(hr(e.doc,V.from,V.to).join(h));return}if(P.getAttribute("contenteditable")=="false")return;var le=/^(pre|div|p|li|table|br)$/i.test(P.nodeName);if(!/^br$/i.test(P.nodeName)&&P.textContent.length==0)return;le&&x();for(var te=0;te<P.childNodes.length;te++)z(P.childNodes[te]);/^(pre|p)$/i.test(P.nodeName)&&(v=!0),le&&(d=!0)}else P.nodeType==3&&T(P.nodeValue.replace(/\u200b/g,"").replace(/\u00a0/g," "))}for(;z(r),r!=o;)r=r.nextSibling,v=!1;return f}function Bs(e,r,o){var l;if(r==e.display.lineDiv){if(l=e.display.lineDiv.childNodes[o],!l)return nl(e.clipPos(b(e.display.viewTo-1)),!0);r=null,o=0}else for(l=r;;l=l.parentNode){if(!l||l==e.display.lineDiv)return null;if(l.parentNode&&l.parentNode==e.display.lineDiv)break}for(var s=0;s<e.display.view.length;s++){var f=e.display.view[s];if(f.node==l)return p1(f,r,o)}}function p1(e,r,o){var l=e.text.firstChild,s=!1;if(!r||!k(l,r))return nl(b(ze(e.line),0),!0);if(r==l&&(s=!0,r=l.childNodes[o],o=0,!r)){var f=e.rest?Ne(e.rest):e.line;return nl(b(ze(f),f.text.length),s)}var d=r.nodeType==3?r:null,h=r;for(!d&&r.childNodes.length==1&&r.firstChild.nodeType==3&&(d=r.firstChild,o&&(o=d.nodeValue.length));h.parentNode!=l;)h=h.parentNode;var v=e.measure,m=v.maps;function x(V,ee,le){for(var te=-1;te<(m?m.length:0);te++)for(var se=te<0?v.map:m[te],ve=0;ve<se.length;ve+=3){var pe=se[ve+2];if(pe==V||pe==ee){var Ee=ze(te<0?e.line:e.rest[te]),He=se[ve]+le;return(le<0||pe!=V)&&(He=se[ve+(le?1:0)]),b(Ee,He)}}}var T=x(d,h,o);if(T)return nl(T,s);for(var z=h.nextSibling,P=d?d.nodeValue.length-o:0;z;z=z.nextSibling){if(T=x(z,z.firstChild,0),T)return nl(b(T.line,T.ch-P),s);P+=z.textContent.length}for(var H=h.previousSibling,j=o;H;H=H.previousSibling){if(T=x(H,H.firstChild,-1),T)return nl(b(T.line,T.ch+j),s);j+=H.textContent.length}}var it=function(e){this.cm=e,this.prevInput="",this.pollingFast=!1,this.polling=new Ce,this.hasSelection=!1,this.composing=null,this.resetting=!1};it.prototype.init=function(e){var r=this,o=this,l=this.cm;this.createField(e);var s=this.textarea;e.wrapper.insertBefore(this.wrapper,e.wrapper.firstChild),C&&(s.style.width="0px"),re(s,"input",function(){y&&S>=9&&r.hasSelection&&(r.hasSelection=null),o.poll()}),re(s,"paste",function(d){Xe(l,d)||yp(d,l)||(l.state.pasteIncoming=+new Date,o.fastPoll())});function f(d){if(!Xe(l,d)){if(l.somethingSelected())Hs({lineWise:!1,text:l.getSelections()});else if(l.options.lineWiseCopyCut){var h=xp(l);Hs({lineWise:!0,text:h.text}),d.type=="cut"?l.setSelections(h.ranges,null,je):(o.prevInput="",s.value=h.text.join(`
`),ue(s))}else return;d.type=="cut"&&(l.state.cutIncoming=+new Date)}}re(s,"cut",f),re(s,"copy",f),re(e.scroller,"paste",function(d){if(!(gr(e,d)||Xe(l,d))){if(!s.dispatchEvent){l.state.pasteIncoming=+new Date,o.focus();return}var h=new Event("paste");h.clipboardData=d.clipboardData,s.dispatchEvent(h)}}),re(e.lineSpace,"selectstart",function(d){gr(e,d)||Nt(d)}),re(s,"compositionstart",function(){var d=l.getCursor("from");o.composing&&o.composing.range.clear(),o.composing={start:d,range:l.markText(d,l.getCursor("to"),{className:"CodeMirror-composing"})}}),re(s,"compositionend",function(){o.composing&&(o.poll(),o.composing.range.clear(),o.composing=null)})},it.prototype.createField=function(e){this.wrapper=kp(),this.textarea=this.wrapper.firstChild},it.prototype.screenReaderLabelChanged=function(e){e?this.textarea.setAttribute("aria-label",e):this.textarea.removeAttribute("aria-label")},it.prototype.prepareSelection=function(){var e=this.cm,r=e.display,o=e.doc,l=dh(e);if(e.options.moveInputWithCursor){var s=An(e,o.sel.primary().head,"div"),f=r.wrapper.getBoundingClientRect(),d=r.lineDiv.getBoundingClientRect();l.teTop=Math.max(0,Math.min(r.wrapper.clientHeight-10,s.top+d.top-f.top)),l.teLeft=Math.max(0,Math.min(r.wrapper.clientWidth-10,s.left+d.left-f.left))}return l},it.prototype.showSelection=function(e){var r=this.cm,o=r.display;ne(o.cursorDiv,e.cursors),ne(o.selectionDiv,e.selection),e.teTop!=null&&(this.wrapper.style.top=e.teTop+"px",this.wrapper.style.left=e.teLeft+"px")},it.prototype.reset=function(e){if(!(this.contextMenuPending||this.composing&&e)){var r=this.cm;if(this.resetting=!0,r.somethingSelected()){this.prevInput="";var o=r.getSelection();this.textarea.value=o,r.state.focused&&ue(this.textarea),y&&S>=9&&(this.hasSelection=o)}else e||(this.prevInput=this.textarea.value="",y&&S>=9&&(this.hasSelection=null));this.resetting=!1}},it.prototype.getField=function(){return this.textarea},it.prototype.supportsTouch=function(){return!1},it.prototype.focus=function(){if(this.cm.options.readOnly!="nocursor"&&(!$||E(this.textarea.ownerDocument)!=this.textarea))try{this.textarea.focus()}catch{}},it.prototype.blur=function(){this.textarea.blur()},it.prototype.resetPosition=function(){this.wrapper.style.top=this.wrapper.style.left=0},it.prototype.receivedFocus=function(){this.slowPoll()},it.prototype.slowPoll=function(){var e=this;this.pollingFast||this.polling.set(this.cm.options.pollInterval,function(){e.poll(),e.cm.state.focused&&e.slowPoll()})},it.prototype.fastPoll=function(){var e=!1,r=this;r.pollingFast=!0;function o(){var l=r.poll();!l&&!e?(e=!0,r.polling.set(60,o)):(r.pollingFast=!1,r.slowPoll())}r.polling.set(20,o)},it.prototype.poll=function(){var e=this,r=this.cm,o=this.textarea,l=this.prevInput;if(this.contextMenuPending||this.resetting||!r.state.focused||Dr(o)&&!l&&!this.composing||r.isReadOnly()||r.options.disableInput||r.state.keySeq)return!1;var s=o.value;if(s==l&&!r.somethingSelected())return!1;if(y&&S>=9&&this.hasSelection===s||Y&&/[\uf700-\uf7ff]/.test(s))return r.display.input.reset(),!1;if(r.doc.sel==r.display.selForContextMenu){var f=s.charCodeAt(0);if(f==8203&&!l&&(l="​"),f==8666)return this.reset(),this.cm.execCommand("undo")}for(var d=0,h=Math.min(l.length,s.length);d<h&&l.charCodeAt(d)==s.charCodeAt(d);)++d;return tn(r,function(){lf(r,s.slice(d),l.length-d,null,e.composing?"*compose":null),s.length>1e3||s.indexOf(`
`)>-1?o.value=e.prevInput="":e.prevInput=s,e.composing&&(e.composing.range.clear(),e.composing.range=r.markText(e.composing.start,r.getCursor("to"),{className:"CodeMirror-composing"}))}),!0},it.prototype.ensurePolled=function(){this.pollingFast&&this.poll()&&(this.pollingFast=!1)},it.prototype.onKeyPress=function(){y&&S>=9&&(this.hasSelection=null),this.fastPoll()},it.prototype.onContextMenu=function(e){var r=this,o=r.cm,l=o.display,s=r.textarea;r.contextMenuPending&&r.contextMenuPending();var f=vi(o,e),d=l.scroller.scrollTop;if(!f||B)return;var h=o.options.resetSelectionOnContextMenu;h&&o.doc.sel.contains(f)==-1&&gt(o,Mt)(o.doc,Hr(f),je);var v=s.style.cssText,m=r.wrapper.style.cssText,x=r.wrapper.offsetParent.getBoundingClientRect();r.wrapper.style.cssText="position: static",s.style.cssText=`position: absolute; width: 30px; height: 30px;
      top: `+(e.clientY-x.top-5)+"px; left: "+(e.clientX-x.left-5)+`px;
      z-index: 1000; background: `+(y?"rgba(255, 255, 255, .05)":"transparent")+`;
      outline: none; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);`;var T;N&&(T=s.ownerDocument.defaultView.scrollY),l.input.focus(),N&&s.ownerDocument.defaultView.scrollTo(null,T),l.input.reset(),o.somethingSelected()||(s.value=r.prevInput=" "),r.contextMenuPending=P,l.selForContextMenu=o.doc.sel,clearTimeout(l.detectingSelectAll);function z(){if(s.selectionStart!=null){var j=o.somethingSelected(),V="​"+(j?s.value:"");s.value="⇚",s.value=V,r.prevInput=j?"":"​",s.selectionStart=1,s.selectionEnd=V.length,l.selForContextMenu=o.doc.sel}}function P(){if(r.contextMenuPending==P&&(r.contextMenuPending=!1,r.wrapper.style.cssText=m,s.style.cssText=v,y&&S<9&&l.scrollbars.setScrollTop(l.scroller.scrollTop=d),s.selectionStart!=null)){(!y||y&&S<9)&&z();var j=0,V=function(){l.selForContextMenu==o.doc.sel&&s.selectionStart==0&&s.selectionEnd>0&&r.prevInput=="​"?gt(o,Uh)(o):j++<10?l.detectingSelectAll=setTimeout(V,500):(l.selForContextMenu=null,l.input.reset())};l.detectingSelectAll=setTimeout(V,200)}}if(y&&S>=9&&z(),me){fr(e);var H=function(){Et(window,"mouseup",H),setTimeout(P,20)};re(window,"mouseup",H)}else setTimeout(P,50)},it.prototype.readOnlyChanged=function(e){e||this.reset(),this.textarea.disabled=e=="nocursor",this.textarea.readOnly=!!e},it.prototype.setUneditable=function(){},it.prototype.needsContentAttribute=!1;function v1(e,r){if(r=r?he(r):{},r.value=e.value,!r.tabindex&&e.tabIndex&&(r.tabindex=e.tabIndex),!r.placeholder&&e.placeholder&&(r.placeholder=e.placeholder),r.autofocus==null){var o=E(e.ownerDocument);r.autofocus=o==e||e.getAttribute("autofocus")!=null&&o==document.body}function l(){e.value=h.getValue()}var s;if(e.form&&(re(e.form,"submit",l),!r.leaveSubmitMethodAlone)){var f=e.form;s=f.submit;try{var d=f.submit=function(){l(),f.submit=s,f.submit(),f.submit=d}}catch{}}r.finishInit=function(v){v.save=l,v.getTextArea=function(){return e},v.toTextArea=function(){v.toTextArea=isNaN,l(),e.parentNode.removeChild(v.getWrapperElement()),e.style.display="",e.form&&(Et(e.form,"submit",l),!r.leaveSubmitMethodAlone&&typeof e.form.submit=="function"&&(e.form.submit=s))}},e.style.display="none";var h=Ke(function(v){return e.parentNode.insertBefore(v,e.nextSibling)},r);return h}function g1(e){e.off=Et,e.on=re,e.wheelEventPixels=Cy,e.Doc=Kt,e.splitLines=Hl,e.countColumn=fe,e.findColumn=xn,e.isWordChar=Or,e.Pass=Ae,e.signal=Ue,e.Line=Hi,e.changeEnd=Br,e.scrollbarModel=yh,e.Pos=b,e.cmpPos=R,e.modes=zn,e.mimeModes=zr,e.resolveMode=en,e.getMode=cr,e.modeExtensions=Ir,e.extendMode=ds,e.copyState=dr,e.startState=Bl,e.innerMode=Ar,e.commands=fo,e.keyMap=yr,e.keyName=ip,e.isModifierKey=np,e.lookupKey=Ji,e.normalizeKeyMap=Ky,e.StringStream=Ze,e.SharedTextMarker=so,e.TextMarker=jr,e.LineWidget=oo,e.e_preventDefault=Nt,e.e_stopPropagation=di,e.e_stop=fr,e.addClass=F,e.contains=k,e.rmClass=K,e.keyNames=$r}s1(Ke),c1(Ke);var m1="iter insert remove copy getEditor constructor".split(" ");for(var Us in Kt.prototype)Kt.prototype.hasOwnProperty(Us)&&Le(m1,Us)<0&&(Ke.prototype[Us]=function(e){return function(){return e.apply(this.doc,arguments)}}(Kt.prototype[Us]));return zt(Kt),Ke.inputStyles={textarea:it,contenteditable:Fe},Ke.defineMode=function(e){!Ke.defaults.mode&&e!="null"&&(Ke.defaults.mode=e),cs.apply(this,arguments)},Ke.defineMIME=Ri,Ke.defineMode("null",function(){return{token:function(e){return e.skipToEnd()}}}),Ke.defineMIME("text/plain","null"),Ke.defineExtension=function(e,r){Ke.prototype[e]=r},Ke.defineDocExtension=function(e,r){Kt.prototype[e]=r},Ke.fromTextArea=v1,g1(Ke),Ke.version="5.65.9",Ke})})(Qx);const Vx=_l;(function(t,n){(function(i){i(_l)})(function(i){i.defineMode("javascript",function(u,a){var c=u.indentUnit,p=a.statementIndent,g=a.jsonld,y=a.json||g,S=a.trackScope!==!1,N=a.typescript,W=a.wordCharacters||/[\w$\xa1-\uffff]/,O=function(){function w(ct){return{type:ct,style:"keyword"}}var L=w("keyword a"),b=w("keyword b"),R=w("keyword c"),xe=w("keyword d"),be=w("operator"),Oe={type:"atom",style:"atom"};return{if:w("if"),while:L,with:L,else:b,do:b,try:b,finally:b,return:xe,break:xe,continue:xe,new:w("new"),delete:R,void:R,throw:R,debugger:w("debugger"),var:w("var"),const:w("var"),let:w("var"),function:w("function"),catch:w("catch"),for:w("for"),switch:w("switch"),case:w("case"),default:w("default"),in:be,typeof:be,instanceof:be,true:Oe,false:Oe,null:Oe,undefined:Oe,NaN:Oe,Infinity:Oe,this:w("this"),class:w("class"),super:w("atom"),yield:R,export:w("export"),import:w("import"),extends:R,await:R}}(),U=/[+\-*&%=<>!?|~^@]/,B=/^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)"/;function oe(w){for(var L=!1,b,R=!1;(b=w.next())!=null;){if(!L){if(b=="/"&&!R)return;b=="["?R=!0:R&&b=="]"&&(R=!1)}L=!L&&b=="\\"}}var G,_;function C(w,L,b){return G=w,_=b,L}function D(w,L){var b=w.next();if(b=='"'||b=="'")return L.tokenize=$(b),L.tokenize(w,L);if(b=="."&&w.match(/^\d[\d_]*(?:[eE][+\-]?[\d_]+)?/))return C("number","number");if(b=="."&&w.match(".."))return C("spread","meta");if(/[\[\]{}\(\),;\:\.]/.test(b))return C(b);if(b=="="&&w.eat(">"))return C("=>","operator");if(b=="0"&&w.match(/^(?:x[\dA-Fa-f_]+|o[0-7_]+|b[01_]+)n?/))return C("number","number");if(/\d/.test(b))return w.match(/^[\d_]*(?:n|(?:\.[\d_]*)?(?:[eE][+\-]?[\d_]+)?)?/),C("number","number");if(b=="/")return w.eat("*")?(L.tokenize=Y,Y(w,L)):w.eat("/")?(w.skipToEnd(),C("comment","comment")):Xn(w,L,1)?(oe(w),w.match(/^\b(([gimyus])(?![gimyus]*\2))+\b/),C("regexp","string-2")):(w.eat("="),C("operator","operator",w.current()));if(b=="`")return L.tokenize=ie,ie(w,L);if(b=="#"&&w.peek()=="!")return w.skipToEnd(),C("meta","meta");if(b=="#"&&w.eatWhile(W))return C("variable","property");if(b=="<"&&w.match("!--")||b=="-"&&w.match("->")&&!/\S/.test(w.string.slice(0,w.start)))return w.skipToEnd(),C("comment","comment");if(U.test(b))return(b!=">"||!L.lexical||L.lexical.type!=">")&&(w.eat("=")?(b=="!"||b=="=")&&w.eat("="):/[<>*+\-|&?]/.test(b)&&(w.eat(b),b==">"&&w.eat(b))),b=="?"&&w.eat(".")?C("."):C("operator","operator",w.current());if(W.test(b)){w.eatWhile(W);var R=w.current();if(L.lastType!="."){if(O.propertyIsEnumerable(R)){var xe=O[R];return C(xe.type,xe.style,R)}if(R=="async"&&w.match(/^(\s|\/\*([^*]|\*(?!\/))*?\*\/)*[\[\(\w]/,!1))return C("async","keyword",R)}return C("variable","variable",R)}}function $(w){return function(L,b){var R=!1,xe;if(g&&L.peek()=="@"&&L.match(B))return b.tokenize=D,C("jsonld-keyword","meta");for(;(xe=L.next())!=null&&!(xe==w&&!R);)R=!R&&xe=="\\";return R||(b.tokenize=D),C("string","string")}}function Y(w,L){for(var b=!1,R;R=w.next();){if(R=="/"&&b){L.tokenize=D;break}b=R=="*"}return C("comment","comment")}function ie(w,L){for(var b=!1,R;(R=w.next())!=null;){if(!b&&(R=="`"||R=="$"&&w.eat("{"))){L.tokenize=D;break}b=!b&&R=="\\"}return C("quasi","string-2",w.current())}var I="([{}])";function Z(w,L){L.fatArrowAt&&(L.fatArrowAt=null);var b=w.string.indexOf("=>",w.start);if(!(b<0)){if(N){var R=/:\s*(?:\w+(?:<[^>]*>|\[\])?|\{[^}]*\})\s*$/.exec(w.string.slice(w.start,b));R&&(b=R.index)}for(var xe=0,be=!1,Oe=b-1;Oe>=0;--Oe){var ct=w.string.charAt(Oe),hn=I.indexOf(ct);if(hn>=0&&hn<3){if(!xe){++Oe;break}if(--xe==0){ct=="("&&(be=!0);break}}else if(hn>=3&&hn<6)++xe;else if(W.test(ct))be=!0;else if(/["'\/`]/.test(ct))for(;;--Oe){if(Oe==0)return;var Se=w.string.charAt(Oe-1);if(Se==ct&&w.string.charAt(Oe-2)!="\\"){Oe--;break}}else if(be&&!xe){++Oe;break}}be&&!xe&&(L.fatArrowAt=Oe)}}var ke={atom:!0,number:!0,variable:!0,string:!0,regexp:!0,this:!0,import:!0,"jsonld-keyword":!0};function me(w,L,b,R,xe,be){this.indented=w,this.column=L,this.type=b,this.prev=xe,this.info=be,R!=null&&(this.align=R)}function Re(w,L){if(!S)return!1;for(var b=w.localVars;b;b=b.next)if(b.name==L)return!0;for(var R=w.context;R;R=R.prev)for(var b=R.vars;b;b=b.next)if(b.name==L)return!0}function K(w,L,b,R,xe){var be=w.cc;for(A.state=w,A.stream=xe,A.marked=null,A.cc=be,A.style=L,w.lexical.hasOwnProperty("align")||(w.lexical.align=!0);;){var Oe=be.length?be.pop():y?Ae:Le;if(Oe(b,R)){for(;be.length&&be[be.length-1].lex;)be.pop()();return A.marked?A.marked:b=="variable"&&Re(w,R)?"variable-2":L}}}var A={state:null,column:null,marked:null,cc:null};function ne(){for(var w=arguments.length-1;w>=0;w--)A.cc.push(arguments[w])}function M(){return ne.apply(null,arguments),!0}function Q(w,L){for(var b=L;b;b=b.next)if(b.name==w)return!0;return!1}function X(w){var L=A.state;if(A.marked="def",!!S){if(L.context){if(L.lexical.info=="var"&&L.context&&L.context.block){var b=k(w,L.context);if(b!=null){L.context=b;return}}else if(!Q(w,L.localVars)){L.localVars=new J(w,L.localVars);return}}a.globalVars&&!Q(w,L.globalVars)&&(L.globalVars=new J(w,L.globalVars))}}function k(w,L){if(L)if(L.block){var b=k(w,L.prev);return b?b==L.prev?L:new F(b,L.vars,!0):null}else return Q(w,L.vars)?L:new F(L.prev,new J(w,L.vars),!1);else return null}function E(w){return w=="public"||w=="private"||w=="protected"||w=="abstract"||w=="readonly"}function F(w,L,b){this.prev=w,this.vars=L,this.block=b}function J(w,L){this.name=w,this.next=L}var ue=new J("this",new J("arguments",null));function ye(){A.state.context=new F(A.state.context,A.state.localVars,!1),A.state.localVars=ue}function Be(){A.state.context=new F(A.state.context,A.state.localVars,!0),A.state.localVars=null}ye.lex=Be.lex=!0;function Ve(){A.state.localVars=A.state.context.vars,A.state.context=A.state.context.prev}Ve.lex=!0;function he(w,L){var b=function(){var R=A.state,xe=R.indented;if(R.lexical.type=="stat")xe=R.lexical.indented;else for(var be=R.lexical;be&&be.type==")"&&be.align;be=be.prev)xe=be.indented;R.lexical=new me(xe,A.stream.column(),w,null,R.lexical,L)};return b.lex=!0,b}function fe(){var w=A.state;w.lexical.prev&&(w.lexical.type==")"&&(w.indented=w.lexical.indented),w.lexical=w.lexical.prev)}fe.lex=!0;function Ce(w){function L(b){return b==w?M():w==";"||b=="}"||b==")"||b=="]"?ne():M(L)}return L}function Le(w,L){return w=="var"?M(he("vardef",L),fr,Ce(";"),fe):w=="keyword a"?M(he("form"),Qn,Le,fe):w=="keyword b"?M(he("form"),Le,fe):w=="keyword d"?A.stream.match(/^\s*$/,!1)?M():M(he("stat"),xn,Ce(";"),fe):w=="debugger"?M(Ce(";")):w=="{"?M(he("}"),Be,Sn,fe,Ve):w==";"?M():w=="if"?(A.state.lexical.info=="else"&&A.state.cc[A.state.cc.length-1]==fe&&A.state.cc.pop()(),M(he("form"),Qn,Le,fe,Fi)):w=="function"?M(Yn):w=="for"?M(he("form"),Be,as,Le,Ve,fe):w=="class"||N&&L=="interface"?(A.marked="keyword",M(he("form",w=="class"?w:L),cs,fe)):w=="variable"?N&&L=="declare"?(A.marked="keyword",M(Le)):N&&(L=="module"||L=="enum"||L=="type")&&A.stream.match(/^\s*\w/,!1)?(A.marked="keyword",L=="enum"?M(Wi):L=="type"?M(fs,Ce("operator"),re,Ce(";")):M(he("form"),jt,Ce("{"),he("}"),Sn,fe,fe)):N&&L=="namespace"?(A.marked="keyword",M(he("form"),Ae,Le,fe)):N&&L=="abstract"?(A.marked="keyword",M(Le)):M(he("stat"),ar):w=="switch"?M(he("form"),Qn,Ce("{"),he("}","switch"),Be,Sn,fe,fe,Ve):w=="case"?M(Ae,Ce(":")):w=="default"?M(Ce(":")):w=="catch"?M(he("form"),ye,Ai,Le,fe,Ve):w=="export"?M(he("stat"),Ir,fe):w=="import"?M(he("stat"),dr,fe):w=="async"?M(Le):L=="@"?M(Ae,Le):ne(he("stat"),Ae,Ce(";"),fe)}function Ai(w){if(w=="(")return M(zn,Ce(")"))}function Ae(w,L){return Vn(w,L,!1)}function je(w,L){return Vn(w,L,!0)}function Qn(w){return w!="("?ne():M(he(")"),xn,Ce(")"),fe)}function Vn(w,L,b){if(A.state.fatArrowAt==A.stream.start){var R=b?ur:ft;if(w=="(")return M(ye,he(")"),Te(zn,")"),fe,Ce("=>"),R,Ve);if(w=="variable")return ne(ye,jt,Ce("=>"),R,Ve)}var xe=b?cn:Jt;return ke.hasOwnProperty(w)?M(xe):w=="function"?M(Yn,xe):w=="class"||N&&L=="interface"?(A.marked="keyword",M(he("form"),zr,fe)):w=="keyword c"||w=="async"?M(b?je:Ae):w=="("?M(he(")"),xn,Ce(")"),fe,xe):w=="operator"||w=="spread"?M(b?je:Ae):w=="["?M(he("]"),hr,fe,xe):w=="{"?dn(we,"}",null,xe):w=="quasi"?ne(Ne,xe):w=="new"?M(ci(b)):M()}function xn(w){return w.match(/[;\}\)\],]/)?ne():ne(Ae)}function Jt(w,L){return w==","?M(xn):cn(w,L,!1)}function cn(w,L,b){var R=b==!1?Jt:cn,xe=b==!1?Ae:je;if(w=="=>")return M(ye,b?ur:ft,Ve);if(w=="operator")return/\+\+|--/.test(L)||N&&L=="!"?M(R):N&&L=="<"&&A.stream.match(/^([^<>]|<[^<>]*>)*>\s*\(/,!1)?M(he(">"),Te(re,">"),fe,R):L=="?"?M(Ae,Ce(":"),xe):M(xe);if(w=="quasi")return ne(Ne,R);if(w!=";"){if(w=="(")return dn(je,")","call",R);if(w==".")return M(ge,R);if(w=="[")return M(he("]"),xn,Ce("]"),fe,R);if(N&&L=="as")return A.marked="keyword",M(re,R);if(w=="regexp")return A.state.lastType=A.marked="operator",A.stream.backUp(A.stream.pos-A.stream.start-1),M(xe)}}function Ne(w,L){return w!="quasi"?ne():L.slice(L.length-2)!="${"?M(Ne):M(xn,sr)}function sr(w){if(w=="}")return A.marked="string-2",A.state.tokenize=ie,M(Ne)}function ft(w){return Z(A.stream,A.state),ne(w=="{"?Le:Ae)}function ur(w){return Z(A.stream,A.state),ne(w=="{"?Le:je)}function ci(w){return function(L){return L=="."?M(w?Or:Al):L=="variable"&&N?M(Nt,w?cn:Jt):ne(w?je:Ae)}}function Al(w,L){if(L=="target")return A.marked="keyword",M(Jt)}function Or(w,L){if(L=="target")return A.marked="keyword",M(cn)}function ar(w){return w==":"?M(fe,Le):ne(Jt,Ce(";"),fe)}function ge(w){if(w=="variable")return A.marked="property",M()}function we(w,L){if(w=="async")return A.marked="property",M(we);if(w=="variable"||A.style=="keyword"){if(A.marked="property",L=="get"||L=="set")return M(de);var b;return N&&A.state.fatArrowAt==A.stream.start&&(b=A.stream.match(/^\s*:\s*/,!1))&&(A.state.fatArrowAt=A.stream.pos+b[0].length),M(Me)}else{if(w=="number"||w=="string")return A.marked=g?"property":A.style+" property",M(Me);if(w=="jsonld-keyword")return M(Me);if(N&&E(L))return A.marked="keyword",M(we);if(w=="[")return M(Ae,kn,Ce("]"),Me);if(w=="spread")return M(je,Me);if(L=="*")return A.marked="keyword",M(we);if(w==":")return ne(Me)}}function de(w){return w!="variable"?ne(Me):(A.marked="property",M(Yn))}function Me(w){if(w==":")return M(je);if(w=="(")return ne(Yn)}function Te(w,L,b){function R(xe,be){if(b?b.indexOf(xe)>-1:xe==","){var Oe=A.state.lexical;return Oe.info=="call"&&(Oe.pos=(Oe.pos||0)+1),M(function(ct,hn){return ct==L||hn==L?ne():ne(w)},R)}return xe==L||be==L?M():b&&b.indexOf(";")>-1?ne(w):M(Ce(L))}return function(xe,be){return xe==L||be==L?M():ne(w,R)}}function dn(w,L,b){for(var R=3;R<arguments.length;R++)A.cc.push(arguments[R]);return M(he(L,b),Te(w,L),fe)}function Sn(w){return w=="}"?M():ne(Le,Sn)}function kn(w,L){if(N){if(w==":")return M(re);if(L=="?")return M(kn)}}function ha(w,L){if(N&&(w==":"||L=="in"))return M(re)}function Cn(w){if(N&&w==":")return A.stream.match(/^\s*\w+\s+is\b/,!1)?M(Ae,ss,re):M(re)}function ss(w,L){if(L=="is")return A.marked="keyword",M()}function re(w,L){if(L=="keyof"||L=="typeof"||L=="infer"||L=="readonly")return A.marked="keyword",M(L=="typeof"?je:re);if(w=="variable"||L=="void")return A.marked="type",M(zt);if(L=="|"||L=="&")return M(re);if(w=="string"||w=="number"||w=="atom")return M(zt);if(w=="[")return M(he("]"),Te(re,"]",","),fe,zt);if(w=="{")return M(he("}"),Et,fe,zt);if(w=="(")return M(Te(Dt,")"),Fl,zt);if(w=="<")return M(Te(re,">"),re);if(w=="quasi")return ne(Xe,zt)}function Fl(w){if(w=="=>")return M(re)}function Et(w){return w.match(/[\}\)\]]/)?M():w==","||w==";"?M(Et):ne(Ue,Et)}function Ue(w,L){if(w=="variable"||A.style=="keyword")return A.marked="property",M(Ue);if(L=="?"||w=="number"||w=="string")return M(Ue);if(w==":")return M(re);if(w=="[")return M(Ce("variable"),ha,Ce("]"),Ue);if(w=="(")return ne(qn,Ue);if(!w.match(/[;\}\)\],]/))return M()}function Xe(w,L){return w!="quasi"?ne():L.slice(L.length-2)!="${"?M(Xe):M(re,us)}function us(w){if(w=="}")return A.marked="string-2",A.state.tokenize=ie,M(Xe)}function Dt(w,L){return w=="variable"&&A.stream.match(/^\s*[?:]/,!1)||L=="?"?M(Dt):w==":"?M(re):w=="spread"?M(Dt):ne(re)}function zt(w,L){if(L=="<")return M(he(">"),Te(re,">"),fe,zt);if(L=="|"||w=="."||L=="&")return M(re);if(w=="[")return M(re,Ce("]"),zt);if(L=="extends"||L=="implements")return A.marked="keyword",M(re);if(L=="?")return M(re,Ce(":"),re)}function Nt(w,L){if(L=="<")return M(he(">"),Te(re,">"),fe,zt)}function di(){return ne(re,Rl)}function Rl(w,L){if(L=="=")return M(re)}function fr(w,L){return L=="enum"?(A.marked="keyword",M(Wi)):ne(jt,kn,Tn,va)}function jt(w,L){if(N&&E(L))return A.marked="keyword",M(jt);if(w=="variable")return X(L),M();if(w=="spread")return M(jt);if(w=="[")return dn(pa,"]");if(w=="{")return dn(Wl,"}")}function Wl(w,L){return w=="variable"&&!A.stream.match(/^\s*:/,!1)?(X(L),M(Tn)):(w=="variable"&&(A.marked="property"),w=="spread"?M(jt):w=="}"?ne():w=="["?M(Ae,Ce("]"),Ce(":"),Wl):M(Ce(":"),jt,Tn))}function pa(){return ne(jt,Tn)}function Tn(w,L){if(L=="=")return M(je)}function va(w){if(w==",")return M(fr)}function Fi(w,L){if(w=="keyword b"&&L=="else")return M(he("form","else"),Le,fe)}function as(w,L){if(L=="await")return M(as);if(w=="(")return M(he(")"),Hl,fe)}function Hl(w){return w=="var"?M(fr,Dr):w=="variable"?M(Dr):ne(Dr)}function Dr(w,L){return w==")"?M():w==";"?M(Dr):L=="in"||L=="of"?(A.marked="keyword",M(Ae,Dr)):ne(Ae,Dr)}function Yn(w,L){if(L=="*")return A.marked="keyword",M(Yn);if(w=="variable")return X(L),M(Yn);if(w=="(")return M(ye,he(")"),Te(zn,")"),fe,Cn,Le,Ve);if(N&&L=="<")return M(he(">"),Te(di,">"),fe,Yn)}function qn(w,L){if(L=="*")return A.marked="keyword",M(qn);if(w=="variable")return X(L),M(qn);if(w=="(")return M(ye,he(")"),Te(zn,")"),fe,Cn,Ve);if(N&&L=="<")return M(he(">"),Te(di,">"),fe,qn)}function fs(w,L){if(w=="keyword"||w=="variable")return A.marked="type",M(fs);if(L=="<")return M(he(">"),Te(di,">"),fe)}function zn(w,L){return L=="@"&&M(Ae,zn),w=="spread"?M(zn):N&&E(L)?(A.marked="keyword",M(zn)):N&&w=="this"?M(kn,Tn):ne(jt,kn,Tn)}function zr(w,L){return w=="variable"?cs(w,L):Ri(w,L)}function cs(w,L){if(w=="variable")return X(L),M(Ri)}function Ri(w,L){if(L=="<")return M(he(">"),Te(di,">"),fe,Ri);if(L=="extends"||L=="implements"||N&&w==",")return L=="implements"&&(A.marked="keyword"),M(N?re:Ae,Ri);if(w=="{")return M(he("}"),en,fe)}function en(w,L){if(w=="async"||w=="variable"&&(L=="static"||L=="get"||L=="set"||N&&E(L))&&A.stream.match(/^\s+[\w$\xa1-\uffff]/,!1))return A.marked="keyword",M(en);if(w=="variable"||A.style=="keyword")return A.marked="property",M(cr,en);if(w=="number"||w=="string")return M(cr,en);if(w=="[")return M(Ae,kn,Ce("]"),cr,en);if(L=="*")return A.marked="keyword",M(en);if(N&&w=="(")return ne(qn,en);if(w==";"||w==",")return M(en);if(w=="}")return M();if(L=="@")return M(Ae,en)}function cr(w,L){if(L=="!"||L=="?")return M(cr);if(w==":")return M(re,Tn);if(L=="=")return M(je);var b=A.state.lexical.prev,R=b&&b.info=="interface";return ne(R?qn:Yn)}function Ir(w,L){return L=="*"?(A.marked="keyword",M(ce,Ce(";"))):L=="default"?(A.marked="keyword",M(Ae,Ce(";"))):w=="{"?M(Te(ds,"}"),ce,Ce(";")):ne(Le)}function ds(w,L){if(L=="as")return A.marked="keyword",M(Ce("variable"));if(w=="variable")return ne(je,ds)}function dr(w){return w=="string"?M():w=="("?ne(Ae):w=="."?ne(Jt):ne(Ar,Bl,ce)}function Ar(w,L){return w=="{"?dn(Ar,"}"):(w=="variable"&&X(L),L=="*"&&(A.marked="keyword"),M(Ze))}function Bl(w){if(w==",")return M(Ar,Bl)}function Ze(w,L){if(L=="as")return A.marked="keyword",M(Ar)}function ce(w,L){if(L=="from")return A.marked="keyword",M(Ae)}function hr(w){return w=="]"?M():ne(Te(je,"]"))}function Wi(){return ne(he("form"),jt,Ce("{"),he("}"),Te(Ln,"}"),fe,fe)}function Ln(){return ne(jt,Tn)}function ze(w,L){return w.lastType=="operator"||w.lastType==","||U.test(L.charAt(0))||/[,.]/.test(L.charAt(0))}function Xn(w,L,b){return L.tokenize==D&&/^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\[{}\(,;:]|=>)$/.test(L.lastType)||L.lastType=="quasi"&&/\{\s*$/.test(w.string.slice(0,w.pos-(b||0)))}return{startState:function(w){var L={tokenize:D,lastType:"sof",cc:[],lexical:new me((w||0)-c,0,"block",!1),localVars:a.localVars,context:a.localVars&&new F(null,null,!1),indented:w||0};return a.globalVars&&typeof a.globalVars=="object"&&(L.globalVars=a.globalVars),L},token:function(w,L){if(w.sol()&&(L.lexical.hasOwnProperty("align")||(L.lexical.align=!1),L.indented=w.indentation(),Z(w,L)),L.tokenize!=Y&&w.eatSpace())return null;var b=L.tokenize(w,L);return G=="comment"?b:(L.lastType=G=="operator"&&(_=="++"||_=="--")?"incdec":G,K(L,b,G,_,w))},indent:function(w,L){if(w.tokenize==Y||w.tokenize==ie)return i.Pass;if(w.tokenize!=D)return 0;var b=L&&L.charAt(0),R=w.lexical,xe;if(!/^\s*else\b/.test(L))for(var be=w.cc.length-1;be>=0;--be){var Oe=w.cc[be];if(Oe==fe)R=R.prev;else if(Oe!=Fi&&Oe!=Ve)break}for(;(R.type=="stat"||R.type=="form")&&(b=="}"||(xe=w.cc[w.cc.length-1])&&(xe==Jt||xe==cn)&&!/^[,\.=+\-*:?[\(]/.test(L));)R=R.prev;p&&R.type==")"&&R.prev.type=="stat"&&(R=R.prev);var ct=R.type,hn=b==ct;return ct=="vardef"?R.indented+(w.lastType=="operator"||w.lastType==","?R.info.length+1:0):ct=="form"&&b=="{"?R.indented:ct=="form"?R.indented+c:ct=="stat"?R.indented+(ze(w,L)?p||c:0):R.info=="switch"&&!hn&&a.doubleIndentSwitch!=!1?R.indented+(/^(?:case|default)\b/.test(L)?c:2*c):R.align?R.column+(hn?0:1):R.indented+(hn?0:c)},electricInput:/^\s*(?:case .*?:|default:|\{|\})$/,blockCommentStart:y?null:"/*",blockCommentEnd:y?null:"*/",blockCommentContinue:y?null:" * ",lineComment:y?null:"//",fold:"brace",closeBrackets:"()[]{}''\"\"``",helperType:y?"json":"javascript",jsonldMode:g,jsonMode:y,expressionAllowed:Xn,skipExpression:function(w){K(w,"atom","atom","true",new i.StringStream("",2,null))}}}),i.registerHelper("wordChars","javascript",/[\w$]/),i.defineMIME("text/javascript","javascript"),i.defineMIME("text/ecmascript","javascript"),i.defineMIME("application/javascript","javascript"),i.defineMIME("application/x-javascript","javascript"),i.defineMIME("application/ecmascript","javascript"),i.defineMIME("application/json",{name:"javascript",json:!0}),i.defineMIME("application/x-json",{name:"javascript",json:!0}),i.defineMIME("application/manifest+json",{name:"javascript",json:!0}),i.defineMIME("application/ld+json",{name:"javascript",jsonld:!0}),i.defineMIME("text/typescript",{name:"javascript",typescript:!0}),i.defineMIME("application/typescript",{name:"javascript",typescript:!0})})})();(function(t,n){(function(i){i(_l)})(function(i){function u(S){return new RegExp("^(("+S.join(")|(")+"))\\b")}var a=u(["and","or","not","is"]),c=["as","assert","break","class","continue","def","del","elif","else","except","finally","for","from","global","if","import","lambda","pass","raise","return","try","while","with","yield","in"],p=["abs","all","any","bin","bool","bytearray","callable","chr","classmethod","compile","complex","delattr","dict","dir","divmod","enumerate","eval","filter","float","format","frozenset","getattr","globals","hasattr","hash","help","hex","id","input","int","isinstance","issubclass","iter","len","list","locals","map","max","memoryview","min","next","object","oct","open","ord","pow","property","range","repr","reversed","round","set","setattr","slice","sorted","staticmethod","str","sum","super","tuple","type","vars","zip","__import__","NotImplemented","Ellipsis","__debug__"];i.registerHelper("hintWords","python",c.concat(p).concat(["exec","print"]));function g(S){return S.scopes[S.scopes.length-1]}i.defineMode("python",function(S,N){for(var W="error",O=N.delimiters||N.singleDelimiters||/^[\(\)\[\]\{\}@,:`=;\.\\]/,U=[N.singleOperators,N.doubleOperators,N.doubleDelimiters,N.tripleDelimiters,N.operators||/^([-+*/%\/&|^]=?|[<>=]+|\/\/=?|\*\*=?|!=|[~!@]|\.\.\.)/],B=0;B<U.length;B++)U[B]||U.splice(B--,1);var oe=N.hangingIndent||S.indentUnit,G=c,_=p;N.extra_keywords!=null&&(G=G.concat(N.extra_keywords)),N.extra_builtins!=null&&(_=_.concat(N.extra_builtins));var C=!(N.version&&Number(N.version)<3);if(C){var D=N.identifiers||/^[_A-Za-z\u00A1-\uFFFF][_A-Za-z0-9\u00A1-\uFFFF]*/;G=G.concat(["nonlocal","False","True","None","async","await"]),_=_.concat(["ascii","bytes","exec","print"]);var $=new RegExp(`^(([rbuf]|(br)|(rb)|(fr)|(rf))?('{3}|"{3}|['"]))`,"i")}else{var D=N.identifiers||/^[_A-Za-z][_A-Za-z0-9]*/;G=G.concat(["exec","print"]),_=_.concat(["apply","basestring","buffer","cmp","coerce","execfile","file","intern","long","raw_input","reduce","reload","unichr","unicode","xrange","False","True","None"]);var $=new RegExp(`^(([rubf]|(ur)|(br))?('{3}|"{3}|['"]))`,"i")}var Y=u(G),ie=u(_);function I(Q,X){var k=Q.sol()&&X.lastToken!="\\";if(k&&(X.indent=Q.indentation()),k&&g(X).type=="py"){var E=g(X).offset;if(Q.eatSpace()){var F=Q.indentation();return F>E?Re(X):F<E&&A(Q,X)&&Q.peek()!="#"&&(X.errorToken=!0),null}else{var J=Z(Q,X);return E>0&&A(Q,X)&&(J+=" "+W),J}}return Z(Q,X)}function Z(Q,X,k){if(Q.eatSpace())return null;if(!k&&Q.match(/^#.*/))return"comment";if(Q.match(/^[0-9\.]/,!1)){var E=!1;if(Q.match(/^[\d_]*\.\d+(e[\+\-]?\d+)?/i)&&(E=!0),Q.match(/^[\d_]+\.\d*/)&&(E=!0),Q.match(/^\.\d+/)&&(E=!0),E)return Q.eat(/J/i),"number";var F=!1;if(Q.match(/^0x[0-9a-f_]+/i)&&(F=!0),Q.match(/^0b[01_]+/i)&&(F=!0),Q.match(/^0o[0-7_]+/i)&&(F=!0),Q.match(/^[1-9][\d_]*(e[\+\-]?[\d_]+)?/)&&(Q.eat(/J/i),F=!0),Q.match(/^0(?![\dx])/i)&&(F=!0),F)return Q.eat(/L/i),"number"}if(Q.match($)){var J=Q.current().toLowerCase().indexOf("f")!==-1;return J?(X.tokenize=ke(Q.current(),X.tokenize),X.tokenize(Q,X)):(X.tokenize=me(Q.current(),X.tokenize),X.tokenize(Q,X))}for(var ue=0;ue<U.length;ue++)if(Q.match(U[ue]))return"operator";return Q.match(O)?"punctuation":X.lastToken=="."&&Q.match(D)?"property":Q.match(Y)||Q.match(a)?"keyword":Q.match(ie)?"builtin":Q.match(/^(self|cls)\b/)?"variable-2":Q.match(D)?X.lastToken=="def"||X.lastToken=="class"?"def":"variable":(Q.next(),k?null:W)}function ke(Q,X){for(;"rubf".indexOf(Q.charAt(0).toLowerCase())>=0;)Q=Q.substr(1);var k=Q.length==1,E="string";function F(ue){return function(ye,Be){var Ve=Z(ye,Be,!0);return Ve=="punctuation"&&(ye.current()=="{"?Be.tokenize=F(ue+1):ye.current()=="}"&&(ue>1?Be.tokenize=F(ue-1):Be.tokenize=J)),Ve}}function J(ue,ye){for(;!ue.eol();)if(ue.eatWhile(/[^'"\{\}\\]/),ue.eat("\\")){if(ue.next(),k&&ue.eol())return E}else{if(ue.match(Q))return ye.tokenize=X,E;if(ue.match("{{"))return E;if(ue.match("{",!1))return ye.tokenize=F(0),ue.current()?E:ye.tokenize(ue,ye);if(ue.match("}}"))return E;if(ue.match("}"))return W;ue.eat(/['"]/)}if(k){if(N.singleLineStringErrors)return W;ye.tokenize=X}return E}return J.isString=!0,J}function me(Q,X){for(;"rubf".indexOf(Q.charAt(0).toLowerCase())>=0;)Q=Q.substr(1);var k=Q.length==1,E="string";function F(J,ue){for(;!J.eol();)if(J.eatWhile(/[^'"\\]/),J.eat("\\")){if(J.next(),k&&J.eol())return E}else{if(J.match(Q))return ue.tokenize=X,E;J.eat(/['"]/)}if(k){if(N.singleLineStringErrors)return W;ue.tokenize=X}return E}return F.isString=!0,F}function Re(Q){for(;g(Q).type!="py";)Q.scopes.pop();Q.scopes.push({offset:g(Q).offset+S.indentUnit,type:"py",align:null})}function K(Q,X,k){var E=Q.match(/^[\s\[\{\(]*(?:#|$)/,!1)?null:Q.column()+1;X.scopes.push({offset:X.indent+oe,type:k,align:E})}function A(Q,X){for(var k=Q.indentation();X.scopes.length>1&&g(X).offset>k;){if(g(X).type!="py")return!0;X.scopes.pop()}return g(X).offset!=k}function ne(Q,X){Q.sol()&&(X.beginningOfLine=!0,X.dedent=!1);var k=X.tokenize(Q,X),E=Q.current();if(X.beginningOfLine&&E=="@")return Q.match(D,!1)?"meta":C?"operator":W;if(/\S/.test(E)&&(X.beginningOfLine=!1),(k=="variable"||k=="builtin")&&X.lastToken=="meta"&&(k="meta"),(E=="pass"||E=="return")&&(X.dedent=!0),E=="lambda"&&(X.lambda=!0),E==":"&&!X.lambda&&g(X).type=="py"&&Q.match(/^\s*(?:#|$)/,!1)&&Re(X),E.length==1&&!/string|comment/.test(k)){var F="[({".indexOf(E);if(F!=-1&&K(Q,X,"])}".slice(F,F+1)),F="])}".indexOf(E),F!=-1)if(g(X).type==E)X.indent=X.scopes.pop().offset-oe;else return W}return X.dedent&&Q.eol()&&g(X).type=="py"&&X.scopes.length>1&&X.scopes.pop(),k}var M={startState:function(Q){return{tokenize:I,scopes:[{offset:Q||0,type:"py",align:null}],indent:Q||0,lastToken:null,lambda:!1,dedent:0}},token:function(Q,X){var k=X.errorToken;k&&(X.errorToken=!1);var E=ne(Q,X);return E&&E!="comment"&&(X.lastToken=E=="keyword"||E=="punctuation"?Q.current():E),E=="punctuation"&&(E=null),Q.eol()&&X.lambda&&(X.lambda=!1),k?E+" "+W:E},indent:function(Q,X){if(Q.tokenize!=I)return Q.tokenize.isString?i.Pass:0;var k=g(Q),E=k.type==X.charAt(0)||k.type=="py"&&!Q.dedent&&/^(else:|elif |except |finally:)/.test(X);return k.align!=null?k.align-(E?1:0):k.offset-(E?oe:0)},electricInput:/^\s*([\}\]\)]|else:|elif |except |finally:)$/,closeBrackets:{triples:`'"`},lineComment:"#",fold:"indent"};return M}),i.defineMIME("text/x-python","python");var y=function(S){return S.split(" ")};i.defineMIME("text/x-cython",{name:"python",extra_keywords:y("by cdef cimport cpdef ctypedef enum except extern gil include nogil property public readonly struct union DEF IF ELIF ELSE")})})})();(function(t,n){(function(i){i(_l)})(function(i){function u(k,E,F,J,ue,ye){this.indented=k,this.column=E,this.type=F,this.info=J,this.align=ue,this.prev=ye}function a(k,E,F,J){var ue=k.indented;return k.context&&k.context.type=="statement"&&F!="statement"&&(ue=k.context.indented),k.context=new u(ue,E,F,J,null,k.context)}function c(k){var E=k.context.type;return(E==")"||E=="]"||E=="}")&&(k.indented=k.context.indented),k.context=k.context.prev}function p(k,E,F){if(E.prevToken=="variable"||E.prevToken=="type"||/\S(?:[^- ]>|[*\]])\s*$|\*$/.test(k.string.slice(0,F))||E.typeAtEndOfLine&&k.column()==k.indentation())return!0}function g(k){for(;;){if(!k||k.type=="top")return!0;if(k.type=="}"&&k.prev.info!="namespace")return!1;k=k.prev}}i.defineMode("clike",function(k,E){var F=k.indentUnit,J=E.statementIndentUnit||F,ue=E.dontAlignCalls,ye=E.keywords||{},Be=E.types||{},Ve=E.builtin||{},he=E.blockKeywords||{},fe=E.defKeywords||{},Ce=E.atoms||{},Le=E.hooks||{},Ai=E.multiLineStrings,Ae=E.indentStatements!==!1,je=E.indentSwitch!==!1,Qn=E.namespaceSeparator,Vn=E.isPunctuationChar||/[\[\]{}\(\),;\:\.]/,xn=E.numberStart||/[\d\.]/,Jt=E.number||/^(?:0x[a-f\d]+|0b[01]+|(?:\d+\.?\d*|\.\d+)(?:e[-+]?\d+)?)(u|ll?|l|f)?/i,cn=E.isOperatorChar||/[+\-*&%=<>!?|\/]/,Ne=E.isIdentifierChar||/[\w\$_\xa1-\uffff]/,sr=E.isReservedIdentifier||!1,ft,ur;function ci(ge,we){var de=ge.next();if(Le[de]){var Me=Le[de](ge,we);if(Me!==!1)return Me}if(de=='"'||de=="'")return we.tokenize=Al(de),we.tokenize(ge,we);if(xn.test(de)){if(ge.backUp(1),ge.match(Jt))return"number";ge.next()}if(Vn.test(de))return ft=de,null;if(de=="/"){if(ge.eat("*"))return we.tokenize=Or,Or(ge,we);if(ge.eat("/"))return ge.skipToEnd(),"comment"}if(cn.test(de)){for(;!ge.match(/^\/[\/*]/,!1)&&ge.eat(cn););return"operator"}if(ge.eatWhile(Ne),Qn)for(;ge.match(Qn);)ge.eatWhile(Ne);var Te=ge.current();return S(ye,Te)?(S(he,Te)&&(ft="newstatement"),S(fe,Te)&&(ur=!0),"keyword"):S(Be,Te)?"type":S(Ve,Te)||sr&&sr(Te)?(S(he,Te)&&(ft="newstatement"),"builtin"):S(Ce,Te)?"atom":"variable"}function Al(ge){return function(we,de){for(var Me=!1,Te,dn=!1;(Te=we.next())!=null;){if(Te==ge&&!Me){dn=!0;break}Me=!Me&&Te=="\\"}return(dn||!(Me||Ai))&&(de.tokenize=null),"string"}}function Or(ge,we){for(var de=!1,Me;Me=ge.next();){if(Me=="/"&&de){we.tokenize=null;break}de=Me=="*"}return"comment"}function ar(ge,we){E.typeFirstDefinitions&&ge.eol()&&g(we.context)&&(we.typeAtEndOfLine=p(ge,we,ge.pos))}return{startState:function(ge){return{tokenize:null,context:new u((ge||0)-F,0,"top",null,!1),indented:0,startOfLine:!0,prevToken:null}},token:function(ge,we){var de=we.context;if(ge.sol()&&(de.align==null&&(de.align=!1),we.indented=ge.indentation(),we.startOfLine=!0),ge.eatSpace())return ar(ge,we),null;ft=ur=null;var Me=(we.tokenize||ci)(ge,we);if(Me=="comment"||Me=="meta")return Me;if(de.align==null&&(de.align=!0),ft==";"||ft==":"||ft==","&&ge.match(/^\s*(?:\/\/.*)?$/,!1))for(;we.context.type=="statement";)c(we);else if(ft=="{")a(we,ge.column(),"}");else if(ft=="[")a(we,ge.column(),"]");else if(ft=="(")a(we,ge.column(),")");else if(ft=="}"){for(;de.type=="statement";)de=c(we);for(de.type=="}"&&(de=c(we));de.type=="statement";)de=c(we)}else ft==de.type?c(we):Ae&&((de.type=="}"||de.type=="top")&&ft!=";"||de.type=="statement"&&ft=="newstatement")&&a(we,ge.column(),"statement",ge.current());if(Me=="variable"&&(we.prevToken=="def"||E.typeFirstDefinitions&&p(ge,we,ge.start)&&g(we.context)&&ge.match(/^\s*\(/,!1))&&(Me="def"),Le.token){var Te=Le.token(ge,we,Me);Te!==void 0&&(Me=Te)}return Me=="def"&&E.styleDefs===!1&&(Me="variable"),we.startOfLine=!1,we.prevToken=ur?"def":Me||ft,ar(ge,we),Me},indent:function(ge,we){if(ge.tokenize!=ci&&ge.tokenize!=null||ge.typeAtEndOfLine)return i.Pass;var de=ge.context,Me=we&&we.charAt(0),Te=Me==de.type;if(de.type=="statement"&&Me=="}"&&(de=de.prev),E.dontIndentStatements)for(;de.type=="statement"&&E.dontIndentStatements.test(de.info);)de=de.prev;if(Le.indent){var dn=Le.indent(ge,de,we,F);if(typeof dn=="number")return dn}var Sn=de.prev&&de.prev.info=="switch";if(E.allmanIndentation&&/[{(]/.test(Me)){for(;de.type!="top"&&de.type!="}";)de=de.prev;return de.indented}return de.type=="statement"?de.indented+(Me=="{"?0:J):de.align&&(!ue||de.type!=")")?de.column+(Te?0:1):de.type==")"&&!Te?de.indented+J:de.indented+(Te?0:F)+(!Te&&Sn&&!/^(?:case|default)\b/.test(we)?F:0)},electricInput:je?/^\s*(?:case .*?:|default:|\{\}?|\})$/:/^\s*[{}]$/,blockCommentStart:"/*",blockCommentEnd:"*/",blockCommentContinue:" * ",lineComment:"//",fold:"brace"}});function y(k){for(var E={},F=k.split(" "),J=0;J<F.length;++J)E[F[J]]=!0;return E}function S(k,E){return typeof k=="function"?k(E):k.propertyIsEnumerable(E)}var N="auto if break case register continue return default do sizeof static else struct switch extern typedef union for goto while enum const volatile inline restrict asm fortran",W="alignas alignof and and_eq audit axiom bitand bitor catch class compl concept constexpr const_cast decltype delete dynamic_cast explicit export final friend import module mutable namespace new noexcept not not_eq operator or or_eq override private protected public reinterpret_cast requires static_assert static_cast template this thread_local throw try typeid typename using virtual xor xor_eq",O="bycopy byref in inout oneway out self super atomic nonatomic retain copy readwrite readonly strong weak assign typeof nullable nonnull null_resettable _cmd @interface @implementation @end @protocol @encode @property @synthesize @dynamic @class @public @package @private @protected @required @optional @try @catch @finally @import @selector @encode @defs @synchronized @autoreleasepool @compatibility_alias @available",U="FOUNDATION_EXPORT FOUNDATION_EXTERN NS_INLINE NS_FORMAT_FUNCTION  NS_RETURNS_RETAINEDNS_ERROR_ENUM NS_RETURNS_NOT_RETAINED NS_RETURNS_INNER_POINTER NS_DESIGNATED_INITIALIZER NS_ENUM NS_OPTIONS NS_REQUIRES_NIL_TERMINATION NS_ASSUME_NONNULL_BEGIN NS_ASSUME_NONNULL_END NS_SWIFT_NAME NS_REFINED_FOR_SWIFT",B=y("int long char short double float unsigned signed void bool"),oe=y("SEL instancetype id Class Protocol BOOL");function G(k){return S(B,k)||/.+_t$/.test(k)}function _(k){return G(k)||S(oe,k)}var C="case do else for if switch while struct enum union",D="struct enum union";function $(k,E){if(!E.startOfLine)return!1;for(var F,J=null;F=k.peek();){if(F=="\\"&&k.match(/^.$/)){J=$;break}else if(F=="/"&&k.match(/^\/[\/\*]/,!1))break;k.next()}return E.tokenize=J,"meta"}function Y(k,E){return E.prevToken=="type"?"type":!1}function ie(k){return!k||k.length<2||k[0]!="_"?!1:k[1]=="_"||k[1]!==k[1].toLowerCase()}function I(k){return k.eatWhile(/[\w\.']/),"number"}function Z(k,E){if(k.backUp(1),k.match(/^(?:R|u8R|uR|UR|LR)/)){var F=k.match(/^"([^\s\\()]{0,16})\(/);return F?(E.cpp11RawStringDelim=F[1],E.tokenize=Re,Re(k,E)):!1}return k.match(/^(?:u8|u|U|L)/)?k.match(/^["']/,!1)?"string":!1:(k.next(),!1)}function ke(k){var E=/(\w+)::~?(\w+)$/.exec(k);return E&&E[1]==E[2]}function me(k,E){for(var F;(F=k.next())!=null;)if(F=='"'&&!k.eat('"')){E.tokenize=null;break}return"string"}function Re(k,E){var F=E.cpp11RawStringDelim.replace(/[^\w\s]/g,"\\$&"),J=k.match(new RegExp(".*?\\)"+F+'"'));return J?E.tokenize=null:k.skipToEnd(),"string"}function K(k,E){typeof k=="string"&&(k=[k]);var F=[];function J(ye){if(ye)for(var Be in ye)ye.hasOwnProperty(Be)&&F.push(Be)}J(E.keywords),J(E.types),J(E.builtin),J(E.atoms),F.length&&(E.helperType=k[0],i.registerHelper("hintWords",k[0],F));for(var ue=0;ue<k.length;++ue)i.defineMIME(k[ue],E)}K(["text/x-csrc","text/x-c","text/x-chdr"],{name:"clike",keywords:y(N),types:G,blockKeywords:y(C),defKeywords:y(D),typeFirstDefinitions:!0,atoms:y("NULL true false"),isReservedIdentifier:ie,hooks:{"#":$,"*":Y},modeProps:{fold:["brace","include"]}}),K(["text/x-c++src","text/x-c++hdr"],{name:"clike",keywords:y(N+" "+W),types:G,blockKeywords:y(C+" class try catch"),defKeywords:y(D+" class namespace"),typeFirstDefinitions:!0,atoms:y("true false NULL nullptr"),dontIndentStatements:/^template$/,isIdentifierChar:/[\w\$_~\xa1-\uffff]/,isReservedIdentifier:ie,hooks:{"#":$,"*":Y,u:Z,U:Z,L:Z,R:Z,0:I,1:I,2:I,3:I,4:I,5:I,6:I,7:I,8:I,9:I,token:function(k,E,F){if(F=="variable"&&k.peek()=="("&&(E.prevToken==";"||E.prevToken==null||E.prevToken=="}")&&ke(k.current()))return"def"}},namespaceSeparator:"::",modeProps:{fold:["brace","include"]}}),K("text/x-java",{name:"clike",keywords:y("abstract assert break case catch class const continue default do else enum extends final finally for goto if implements import instanceof interface native new package private protected public return static strictfp super switch synchronized this throw throws transient try volatile while @interface"),types:y("var byte short int long float double boolean char void Boolean Byte Character Double Float Integer Long Number Object Short String StringBuffer StringBuilder Void"),blockKeywords:y("catch class do else finally for if switch try while"),defKeywords:y("class interface enum @interface"),typeFirstDefinitions:!0,atoms:y("true false null"),number:/^(?:0x[a-f\d_]+|0b[01_]+|(?:[\d_]+\.?\d*|\.\d+)(?:e[-+]?[\d_]+)?)(u|ll?|l|f)?/i,hooks:{"@":function(k){return k.match("interface",!1)?!1:(k.eatWhile(/[\w\$_]/),"meta")},'"':function(k,E){return k.match(/""$/)?(E.tokenize=A,E.tokenize(k,E)):!1}},modeProps:{fold:["brace","import"]}}),K("text/x-csharp",{name:"clike",keywords:y("abstract as async await base break case catch checked class const continue default delegate do else enum event explicit extern finally fixed for foreach goto if implicit in interface internal is lock namespace new operator out override params private protected public readonly ref return sealed sizeof stackalloc static struct switch this throw try typeof unchecked unsafe using virtual void volatile while add alias ascending descending dynamic from get global group into join let orderby partial remove select set value var yield"),types:y("Action Boolean Byte Char DateTime DateTimeOffset Decimal Double Func Guid Int16 Int32 Int64 Object SByte Single String Task TimeSpan UInt16 UInt32 UInt64 bool byte char decimal double short int long object sbyte float string ushort uint ulong"),blockKeywords:y("catch class do else finally for foreach if struct switch try while"),defKeywords:y("class interface namespace struct var"),typeFirstDefinitions:!0,atoms:y("true false null"),hooks:{"@":function(k,E){return k.eat('"')?(E.tokenize=me,me(k,E)):(k.eatWhile(/[\w\$_]/),"meta")}}});function A(k,E){for(var F=!1;!k.eol();){if(!F&&k.match('"""')){E.tokenize=null;break}F=k.next()=="\\"&&!F}return"string"}function ne(k){return function(E,F){for(var J;J=E.next();)if(J=="*"&&E.eat("/"))if(k==1){F.tokenize=null;break}else return F.tokenize=ne(k-1),F.tokenize(E,F);else if(J=="/"&&E.eat("*"))return F.tokenize=ne(k+1),F.tokenize(E,F);return"comment"}}K("text/x-scala",{name:"clike",keywords:y("abstract case catch class def do else extends final finally for forSome if implicit import lazy match new null object override package private protected return sealed super this throw trait try type val var while with yield _ assert assume require print println printf readLine readBoolean readByte readShort readChar readInt readLong readFloat readDouble"),types:y("AnyVal App Application Array BufferedIterator BigDecimal BigInt Char Console Either Enumeration Equiv Error Exception Fractional Function IndexedSeq Int Integral Iterable Iterator List Map Numeric Nil NotNull Option Ordered Ordering PartialFunction PartialOrdering Product Proxy Range Responder Seq Serializable Set Specializable Stream StringBuilder StringContext Symbol Throwable Traversable TraversableOnce Tuple Unit Vector Boolean Byte Character CharSequence Class ClassLoader Cloneable Comparable Compiler Double Exception Float Integer Long Math Number Object Package Pair Process Runtime Runnable SecurityManager Short StackTraceElement StrictMath String StringBuffer System Thread ThreadGroup ThreadLocal Throwable Triple Void"),multiLineStrings:!0,blockKeywords:y("catch class enum do else finally for forSome if match switch try while"),defKeywords:y("class enum def object package trait type val var"),atoms:y("true false null"),indentStatements:!1,indentSwitch:!1,isOperatorChar:/[+\-*&%=<>!?|\/#:@]/,hooks:{"@":function(k){return k.eatWhile(/[\w\$_]/),"meta"},'"':function(k,E){return k.match('""')?(E.tokenize=A,E.tokenize(k,E)):!1},"'":function(k){return k.eatWhile(/[\w\$_\xa1-\uffff]/),"atom"},"=":function(k,E){var F=E.context;return F.type=="}"&&F.align&&k.eat(">")?(E.context=new u(F.indented,F.column,F.type,F.info,null,F.prev),"operator"):!1},"/":function(k,E){return k.eat("*")?(E.tokenize=ne(1),E.tokenize(k,E)):!1}},modeProps:{closeBrackets:{pairs:'()[]{}""',triples:'"'}}});function M(k){return function(E,F){for(var J=!1,ue,ye=!1;!E.eol();){if(!k&&!J&&E.match('"')){ye=!0;break}if(k&&E.match('"""')){ye=!0;break}ue=E.next(),!J&&ue=="$"&&E.match("{")&&E.skipTo("}"),J=!J&&ue=="\\"&&!k}return(ye||!k)&&(F.tokenize=null),"string"}}K("text/x-kotlin",{name:"clike",keywords:y("package as typealias class interface this super val operator var fun for is in This throw return annotation break continue object if else while do try when !in !is as? file import where by get set abstract enum open inner override private public internal protected catch finally out final vararg reified dynamic companion constructor init sealed field property receiver param sparam lateinit data inline noinline tailrec external annotation crossinline const operator infix suspend actual expect setparam value"),types:y("Boolean Byte Character CharSequence Class ClassLoader Cloneable Comparable Compiler Double Exception Float Integer Long Math Number Object Package Pair Process Runtime Runnable SecurityManager Short StackTraceElement StrictMath String StringBuffer System Thread ThreadGroup ThreadLocal Throwable Triple Void Annotation Any BooleanArray ByteArray Char CharArray DeprecationLevel DoubleArray Enum FloatArray Function Int IntArray Lazy LazyThreadSafetyMode LongArray Nothing ShortArray Unit"),intendSwitch:!1,indentStatements:!1,multiLineStrings:!0,number:/^(?:0x[a-f\d_]+|0b[01_]+|(?:[\d_]+(\.\d+)?|\.\d+)(?:e[-+]?[\d_]+)?)(u|ll?|l|f)?/i,blockKeywords:y("catch class do else finally for if where try while enum"),defKeywords:y("class val var object interface fun"),atoms:y("true false null this"),hooks:{"@":function(k){return k.eatWhile(/[\w\$_]/),"meta"},"*":function(k,E){return E.prevToken=="."?"variable":"operator"},'"':function(k,E){return E.tokenize=M(k.match('""')),E.tokenize(k,E)},"/":function(k,E){return k.eat("*")?(E.tokenize=ne(1),E.tokenize(k,E)):!1},indent:function(k,E,F,J){var ue=F&&F.charAt(0);if((k.prevToken=="}"||k.prevToken==")")&&F=="")return k.indented;if(k.prevToken=="operator"&&F!="}"&&k.context.type!="}"||k.prevToken=="variable"&&ue=="."||(k.prevToken=="}"||k.prevToken==")")&&ue==".")return J*2+E.indented;if(E.align&&E.type=="}")return E.indented+(k.context.type==(F||"").charAt(0)?0:J)}},modeProps:{closeBrackets:{triples:'"'}}}),K(["x-shader/x-vertex","x-shader/x-fragment"],{name:"clike",keywords:y("sampler1D sampler2D sampler3D samplerCube sampler1DShadow sampler2DShadow const attribute uniform varying break continue discard return for while do if else struct in out inout"),types:y("float int bool void vec2 vec3 vec4 ivec2 ivec3 ivec4 bvec2 bvec3 bvec4 mat2 mat3 mat4"),blockKeywords:y("for while do if else struct"),builtin:y("radians degrees sin cos tan asin acos atan pow exp log exp2 sqrt inversesqrt abs sign floor ceil fract mod min max clamp mix step smoothstep length distance dot cross normalize ftransform faceforward reflect refract matrixCompMult lessThan lessThanEqual greaterThan greaterThanEqual equal notEqual any all not texture1D texture1DProj texture1DLod texture1DProjLod texture2D texture2DProj texture2DLod texture2DProjLod texture3D texture3DProj texture3DLod texture3DProjLod textureCube textureCubeLod shadow1D shadow2D shadow1DProj shadow2DProj shadow1DLod shadow2DLod shadow1DProjLod shadow2DProjLod dFdx dFdy fwidth noise1 noise2 noise3 noise4"),atoms:y("true false gl_FragColor gl_SecondaryColor gl_Normal gl_Vertex gl_MultiTexCoord0 gl_MultiTexCoord1 gl_MultiTexCoord2 gl_MultiTexCoord3 gl_MultiTexCoord4 gl_MultiTexCoord5 gl_MultiTexCoord6 gl_MultiTexCoord7 gl_FogCoord gl_PointCoord gl_Position gl_PointSize gl_ClipVertex gl_FrontColor gl_BackColor gl_FrontSecondaryColor gl_BackSecondaryColor gl_TexCoord gl_FogFragCoord gl_FragCoord gl_FrontFacing gl_FragData gl_FragDepth gl_ModelViewMatrix gl_ProjectionMatrix gl_ModelViewProjectionMatrix gl_TextureMatrix gl_NormalMatrix gl_ModelViewMatrixInverse gl_ProjectionMatrixInverse gl_ModelViewProjectionMatrixInverse gl_TextureMatrixTranspose gl_ModelViewMatrixInverseTranspose gl_ProjectionMatrixInverseTranspose gl_ModelViewProjectionMatrixInverseTranspose gl_TextureMatrixInverseTranspose gl_NormalScale gl_DepthRange gl_ClipPlane gl_Point gl_FrontMaterial gl_BackMaterial gl_LightSource gl_LightModel gl_FrontLightModelProduct gl_BackLightModelProduct gl_TextureColor gl_EyePlaneS gl_EyePlaneT gl_EyePlaneR gl_EyePlaneQ gl_FogParameters gl_MaxLights gl_MaxClipPlanes gl_MaxTextureUnits gl_MaxTextureCoords gl_MaxVertexAttribs gl_MaxVertexUniformComponents gl_MaxVaryingFloats gl_MaxVertexTextureImageUnits gl_MaxTextureImageUnits gl_MaxFragmentUniformComponents gl_MaxCombineTextureImageUnits gl_MaxDrawBuffers"),indentSwitch:!1,hooks:{"#":$},modeProps:{fold:["brace","include"]}}),K("text/x-nesc",{name:"clike",keywords:y(N+" as atomic async call command component components configuration event generic implementation includes interface module new norace nx_struct nx_union post provides signal task uses abstract extends"),types:G,blockKeywords:y(C),atoms:y("null true false"),hooks:{"#":$},modeProps:{fold:["brace","include"]}}),K("text/x-objectivec",{name:"clike",keywords:y(N+" "+O),types:_,builtin:y(U),blockKeywords:y(C+" @synthesize @try @catch @finally @autoreleasepool @synchronized"),defKeywords:y(D+" @interface @implementation @protocol @class"),dontIndentStatements:/^@.*$/,typeFirstDefinitions:!0,atoms:y("YES NO NULL Nil nil true false nullptr"),isReservedIdentifier:ie,hooks:{"#":$,"*":Y},modeProps:{fold:["brace","include"]}}),K("text/x-objectivec++",{name:"clike",keywords:y(N+" "+O+" "+W),types:_,builtin:y(U),blockKeywords:y(C+" @synthesize @try @catch @finally @autoreleasepool @synchronized class try catch"),defKeywords:y(D+" @interface @implementation @protocol @class class namespace"),dontIndentStatements:/^@.*$|^template$/,typeFirstDefinitions:!0,atoms:y("YES NO NULL Nil nil true false nullptr"),isReservedIdentifier:ie,hooks:{"#":$,"*":Y,u:Z,U:Z,L:Z,R:Z,0:I,1:I,2:I,3:I,4:I,5:I,6:I,7:I,8:I,9:I,token:function(k,E,F){if(F=="variable"&&k.peek()=="("&&(E.prevToken==";"||E.prevToken==null||E.prevToken=="}")&&ke(k.current()))return"def"}},namespaceSeparator:"::",modeProps:{fold:["brace","include"]}}),K("text/x-squirrel",{name:"clike",keywords:y("base break clone continue const default delete enum extends function in class foreach local resume return this throw typeof yield constructor instanceof static"),types:G,blockKeywords:y("case catch class else for foreach if switch try while"),defKeywords:y("function local class"),typeFirstDefinitions:!0,atoms:y("true false null"),hooks:{"#":$},modeProps:{fold:["brace","include"]}});var Q=null;function X(k){return function(E,F){for(var J=!1,ue,ye=!1;!E.eol();){if(!J&&E.match('"')&&(k=="single"||E.match('""'))){ye=!0;break}if(!J&&E.match("``")){Q=X(k),ye=!0;break}ue=E.next(),J=k=="single"&&!J&&ue=="\\"}return ye&&(F.tokenize=null),"string"}}K("text/x-ceylon",{name:"clike",keywords:y("abstracts alias assembly assert assign break case catch class continue dynamic else exists extends finally for function given if import in interface is let module new nonempty object of out outer package return satisfies super switch then this throw try value void while"),types:function(k){var E=k.charAt(0);return E===E.toUpperCase()&&E!==E.toLowerCase()},blockKeywords:y("case catch class dynamic else finally for function if interface module new object switch try while"),defKeywords:y("class dynamic function interface module object package value"),builtin:y("abstract actual aliased annotation by default deprecated doc final formal late license native optional sealed see serializable shared suppressWarnings tagged throws variable"),isPunctuationChar:/[\[\]{}\(\),;\:\.`]/,isOperatorChar:/[+\-*&%=<>!?|^~:\/]/,numberStart:/[\d#$]/,number:/^(?:#[\da-fA-F_]+|\$[01_]+|[\d_]+[kMGTPmunpf]?|[\d_]+\.[\d_]+(?:[eE][-+]?\d+|[kMGTPmunpf]|)|)/i,multiLineStrings:!0,typeFirstDefinitions:!0,atoms:y("true false null larger smaller equal empty finished"),indentSwitch:!1,styleDefs:!1,hooks:{"@":function(k){return k.eatWhile(/[\w\$_]/),"meta"},'"':function(k,E){return E.tokenize=X(k.match('""')?"triple":"single"),E.tokenize(k,E)},"`":function(k,E){return!Q||!k.match("`")?!1:(E.tokenize=Q,Q=null,E.tokenize(k,E))},"'":function(k){return k.eatWhile(/[\w\$_\xa1-\uffff]/),"atom"},token:function(k,E,F){if((F=="variable"||F=="type")&&E.prevToken==".")return"variable-2"}},modeProps:{fold:["brace","import"],closeBrackets:{triples:'"'}}})})})();const Y0=({text:t,language:n,readOnly:i,highlight:u=[],revealLine:a,lineNumbers:c,focusOnChange:p,wrapLines:g,onChange:y})=>{const S=tt.createRef(),[N,W]=tt.useState();return tt.useEffect(()=>{let O;if(n==="javascript"&&(O="javascript"),n==="python"&&(O="python"),n==="java"&&(O="text/x-java"),n==="csharp"&&(O="text/x-csharp"),N&&N.getOption("mode")===O||!S.current)return;N&&N.getWrapperElement().remove();const U=Vx(S.current,{value:"",mode:O,readOnly:i,lineNumbers:c,lineWrapping:g});y&&U.on("change",()=>y(U.getValue())),W(U),Av(U,t,u,a,p)},[N,S,t,n,u,a,p,c,g,i,y]),N&&Av(N,t,u,a,p),Pe("div",{className:"cm-wrapper",ref:S})};function Av(t,n,i,u,a){t.getValue()!==n&&(t.setValue(n),a&&(t.execCommand("selectAll"),t.focus()));for(let c=0;c<t.lineCount();++c)t.removeLineClass(c,"wrap");for(const c of i)t.addLineClass(c.line-1,"wrap",`source-line-${c.type}`);u&&t.scrollIntoView({line:u-1,ch:0},50)}const Yx=({text:t,language:n,highlight:i=[],revealLine:u})=>Pe(Y0,{text:t,language:n,readOnly:!0,highlight:i,revealLine:u,lineNumbers:!0});const zf=50,qx=({sidebarSize:t,sidebarHidden:n=!1,sidebarIsFirst:i=!1,orientation:u="vertical",children:a})=>{const[c,p]=tt.useState(Math.max(zf,t)),[g,y]=tt.useState(null),S=tt.Children.toArray(a);document.body.style.userSelect=g?"none":"inherit";let N={};return u==="vertical"?i?N={top:g?0:c-4,bottom:g?0:void 0,height:g?"initial":8}:N={bottom:g?0:c-4,top:g?0:void 0,height:g?"initial":8}:i?N={left:g?0:c-4,right:g?0:void 0,width:g?"initial":8}:N={right:g?0:c-4,left:g?0:void 0,width:g?"initial":8},jn("div",{className:"split-view "+u+(i?" sidebar-first":""),children:[Pe("div",{className:"split-view-main",children:S[0]}),!n&&Pe("div",{style:{flexBasis:c},className:"split-view-sidebar",children:S[1]}),!n&&Pe("div",{style:N,className:"split-view-resizer",onMouseDown:W=>y({offset:u==="vertical"?W.clientY:W.clientX,size:c}),onMouseUp:()=>y(null),onMouseMove:W=>{if(!W.buttons)y(null);else if(g){const U=(u==="vertical"?W.clientY:W.clientX)-g.offset,B=i?g.size+U:g.size-U,G=W.target.parentElement.getBoundingClientRect(),_=Math.min(Math.max(zf,B),(u==="vertical"?G.height:G.width)-zf);p(_)}}})]})};const Fv=({children:t})=>Pe("div",{className:"toolbar",children:t});const xr=({children:t,title:n="",icon:i="",disabled:u=!1,toggled:a=!1,onClick:c=()=>{}})=>{let p=`toolbar-button ${i}`;return a&&(p+=" toggled"),jn("button",{className:p,onClick:c,title:n,disabled:!!u,children:[Pe("span",{className:`codicon codicon-${i}`,style:t?{marginRight:5}:{}}),t]})};function Xx(t){if(!isFinite(t))return"-";if(t===0)return"0";if(t<1e3)return t.toFixed(0)+"ms";const n=t/1e3;if(n<60)return n.toFixed(1)+"s";const i=n/60;if(i<60)return i.toFixed(1)+"m";const u=i/60;return u<24?u.toFixed(1)+"h":(u/24).toFixed(1)+"d"}function da(t,n="'"){const i=JSON.stringify(t),u=i.substring(1,i.length-1).replace(/\\"/g,'"');if(n==="'")return n+u.replace(/[']/g,"\\'")+n;if(n==='"')return n+u.replace(/["]/g,'\\"')+n;if(n==="`")return n+u.replace(/[`]/g,"`")+n;throw new Error("Invalid escape char")}function Yu(t){return t.charAt(0).toUpperCase()+t.substring(1)}function q0(t){return t.replace(/([a-z0-9])([A-Z])/g,"$1_$2").replace(/([A-Z])([A-Z][a-z])/g,"$1_$2").toLowerCase()}const ht=function(t,n,i){return t>=n&&t<=i};function rn(t){return ht(t,48,57)}function Rv(t){return rn(t)||ht(t,65,70)||ht(t,97,102)}function Zx(t){return ht(t,65,90)}function Jx(t){return ht(t,97,122)}function eS(t){return Zx(t)||Jx(t)}function tS(t){return t>=128}function yu(t){return eS(t)||tS(t)||t===95}function Wv(t){return yu(t)||rn(t)||t===45}function nS(t){return ht(t,0,8)||t===11||ht(t,14,31)||t===127}function wu(t){return t===10}function Sr(t){return wu(t)||t===9||t===32}const rS=1114111;class Ld extends Error{constructor(n){super(n),this.name="InvalidCharacterError"}}function iS(t){const n=[];for(let i=0;i<t.length;i++){let u=t.charCodeAt(i);if(u===13&&t.charCodeAt(i+1)===10&&(u=10,i++),(u===13||u===12)&&(u=10),u===0&&(u=65533),ht(u,55296,56319)&&ht(t.charCodeAt(i+1),56320,57343)){const a=u-55296,c=t.charCodeAt(i+1)-56320;u=Math.pow(2,16)+a*Math.pow(2,10)+c,i++}n.push(u)}return n}function xt(t){if(t<=65535)return String.fromCharCode(t);t-=Math.pow(2,16);const n=Math.floor(t/Math.pow(2,10))+55296,i=t%Math.pow(2,10)+56320;return String.fromCharCode(n)+String.fromCharCode(i)}function lS(t){const n=iS(t);let i=-1;const u=[];let a;const c=function(K){return K>=n.length?-1:n[K]},p=function(K){if(K===void 0&&(K=1),K>3)throw"Spec Error: no more than three codepoints of lookahead.";return c(i+K)},g=function(K){return K===void 0&&(K=1),i+=K,a=c(i),!0},y=function(){return i-=1,!0},S=function(K){return K===void 0&&(K=a),K===-1},N=function(){if(W(),g(),Sr(a)){for(;Sr(p());)g();return new _c}else{if(a===34)return B();if(a===35)if(Wv(p())||_(p(1),p(2))){const K=new fm("");return D(p(1),p(2),p(3))&&(K.type="id"),K.value=I(),K}else return new Wt(a);else return a===36?p()===61?(g(),new fS):new Wt(a):a===39?B():a===40?new oS:a===41?new om:a===42?p()===61?(g(),new cS):new Wt(a):a===43?ie()?(y(),O()):new Wt(a):a===44?new nm:a===45?ie()?(y(),O()):p(1)===45&&p(2)===62?(g(2),new J0):$()?(y(),U()):new Wt(a):a===46?ie()?(y(),O()):new Wt(a):a===58?new em:a===59?new tm:a===60?p(1)===33&&p(2)===45&&p(3)===45?(g(3),new Z0):new Wt(a):a===64?D(p(1),p(2),p(3))?new am(I()):new Wt(a):a===91?new lm:a===92?C()?(y(),U()):new Wt(a):a===93?new Pc:a===94?p()===61?(g(),new aS):new Wt(a):a===123?new rm:a===124?p()===61?(g(),new uS):p()===124?(g(),new sm):new Wt(a):a===125?new im:a===126?p()===61?(g(),new sS):new Wt(a):rn(a)?(y(),O()):yu(a)?(y(),U()):S()?new Su:new Wt(a)}},W=function(){for(;p(1)===47&&p(2)===42;)for(g(2);;)if(g(),a===42&&p()===47){g();break}else if(S())return},O=function(){const K=Z();if(D(p(1),p(2),p(3))){const A=new dS;return A.value=K.value,A.repr=K.repr,A.type=K.type,A.unit=I(),A}else if(p()===37){g();const A=new pm;return A.value=K.value,A.repr=K.repr,A}else{const A=new hm;return A.value=K.value,A.repr=K.repr,A.type=K.type,A}},U=function(){const K=I();if(K.toLowerCase()==="url"&&p()===40){for(g();Sr(p(1))&&Sr(p(2));)g();return p()===34||p()===39?new ku(K):Sr(p())&&(p(2)===34||p(2)===39)?new ku(K):oe()}else return p()===40?(g(),new ku(K)):new um(K)},B=function(K){K===void 0&&(K=a);let A="";for(;g();){if(a===K||S())return new cm(A);if(wu(a))return y(),new X0;a===92?S(p())||(wu(p())?g():A+=xt(G())):A+=xt(a)}throw new Error("Internal error")},oe=function(){const K=new dm("");for(;Sr(p());)g();if(S(p()))return K;for(;g();){if(a===41||S())return K;if(Sr(a)){for(;Sr(p());)g();return p()===41||S(p())?(g(),K):(me(),new xu)}else{if(a===34||a===39||a===40||nS(a))return me(),new xu;if(a===92)if(C())K.value+=xt(G());else return me(),new xu;else K.value+=xt(a)}}throw new Error("Internal error")},G=function(){if(g(),Rv(a)){const K=[a];for(let ne=0;ne<5&&Rv(p());ne++)g(),K.push(a);Sr(p())&&g();let A=parseInt(K.map(function(ne){return String.fromCharCode(ne)}).join(""),16);return A>rS&&(A=65533),A}else return S()?65533:a},_=function(K,A){return!(K!==92||wu(A))},C=function(){return _(a,p())},D=function(K,A,ne){return K===45?yu(A)||A===45||_(A,ne):yu(K)?!0:K===92?_(K,A):!1},$=function(){return D(a,p(1),p(2))},Y=function(K,A,ne){return K===43||K===45?!!(rn(A)||A===46&&rn(ne)):K===46?!!rn(A):!!rn(K)},ie=function(){return Y(a,p(1),p(2))},I=function(){let K="";for(;g();)if(Wv(a))K+=xt(a);else if(C())K+=xt(G());else return y(),K;throw new Error("Internal parse error")},Z=function(){let K="",A="integer";for((p()===43||p()===45)&&(g(),K+=xt(a));rn(p());)g(),K+=xt(a);if(p(1)===46&&rn(p(2)))for(g(),K+=xt(a),g(),K+=xt(a),A="number";rn(p());)g(),K+=xt(a);const ne=p(1),M=p(2),Q=p(3);if((ne===69||ne===101)&&rn(M))for(g(),K+=xt(a),g(),K+=xt(a),A="number";rn(p());)g(),K+=xt(a);else if((ne===69||ne===101)&&(M===43||M===45)&&rn(Q))for(g(),K+=xt(a),g(),K+=xt(a),g(),K+=xt(a),A="number";rn(p());)g(),K+=xt(a);const X=ke(K);return{type:A,value:X,repr:K}},ke=function(K){return+K},me=function(){for(;g();){if(a===41||S())return;C()&&G()}};let Re=0;for(;!S(p());)if(u.push(N()),Re++,Re>n.length*2)throw new Error("I'm infinite-looping!");return u}class at{constructor(){this.tokenType=""}toJSON(){return{token:this.tokenType}}toString(){return this.tokenType}toSource(){return""+this}}class X0 extends at{constructor(){super(...arguments),this.tokenType="BADSTRING"}}class xu extends at{constructor(){super(...arguments),this.tokenType="BADURL"}}class _c extends at{constructor(){super(...arguments),this.tokenType="WHITESPACE"}toString(){return"WS"}toSource(){return" "}}class Z0 extends at{constructor(){super(...arguments),this.tokenType="CDO"}toSource(){return"<!--"}}class J0 extends at{constructor(){super(...arguments),this.tokenType="CDC"}toSource(){return"-->"}}class em extends at{constructor(){super(...arguments),this.tokenType=":"}}class tm extends at{constructor(){super(...arguments),this.tokenType=";"}}class nm extends at{constructor(){super(...arguments),this.tokenType=","}}class zl extends at{constructor(){super(...arguments),this.value="",this.mirror=""}}class rm extends zl{constructor(){super(),this.tokenType="{",this.value="{",this.mirror="}"}}class im extends zl{constructor(){super(),this.tokenType="}",this.value="}",this.mirror="{"}}class lm extends zl{constructor(){super(),this.tokenType="[",this.value="[",this.mirror="]"}}class Pc extends zl{constructor(){super(),this.tokenType="]",this.value="]",this.mirror="["}}class oS extends zl{constructor(){super(),this.tokenType="(",this.value="(",this.mirror=")"}}class om extends zl{constructor(){super(),this.tokenType=")",this.value=")",this.mirror="("}}class sS extends at{constructor(){super(...arguments),this.tokenType="~="}}class uS extends at{constructor(){super(...arguments),this.tokenType="|="}}class aS extends at{constructor(){super(...arguments),this.tokenType="^="}}class fS extends at{constructor(){super(...arguments),this.tokenType="$="}}class cS extends at{constructor(){super(...arguments),this.tokenType="*="}}class sm extends at{constructor(){super(...arguments),this.tokenType="||"}}class Su extends at{constructor(){super(...arguments),this.tokenType="EOF"}toSource(){return""}}class Wt extends at{constructor(n){super(),this.tokenType="DELIM",this.value="",this.value=xt(n)}toString(){return"DELIM("+this.value+")"}toJSON(){const n=this.constructor.prototype.constructor.prototype.toJSON.call(this);return n.value=this.value,n}toSource(){return this.value==="\\"?`\\
`:this.value}}class Il extends at{constructor(){super(...arguments),this.value=""}ASCIIMatch(n){return this.value.toLowerCase()===n.toLowerCase()}toJSON(){const n=this.constructor.prototype.constructor.prototype.toJSON.call(this);return n.value=this.value,n}}class um extends Il{constructor(n){super(),this.tokenType="IDENT",this.value=n}toString(){return"IDENT("+this.value+")"}toSource(){return os(this.value)}}class ku extends Il{constructor(n){super(),this.tokenType="FUNCTION",this.value=n,this.mirror=")"}toString(){return"FUNCTION("+this.value+")"}toSource(){return os(this.value)+"("}}class am extends Il{constructor(n){super(),this.tokenType="AT-KEYWORD",this.value=n}toString(){return"AT("+this.value+")"}toSource(){return"@"+os(this.value)}}class fm extends Il{constructor(n){super(),this.tokenType="HASH",this.value=n,this.type="unrestricted"}toString(){return"HASH("+this.value+")"}toJSON(){const n=this.constructor.prototype.constructor.prototype.toJSON.call(this);return n.value=this.value,n.type=this.type,n}toSource(){return this.type==="id"?"#"+os(this.value):"#"+hS(this.value)}}class cm extends Il{constructor(n){super(),this.tokenType="STRING",this.value=n}toString(){return'"'+vm(this.value)+'"'}}class dm extends Il{constructor(n){super(),this.tokenType="URL",this.value=n}toString(){return"URL("+this.value+")"}toSource(){return'url("'+vm(this.value)+'")'}}class hm extends at{constructor(){super(),this.tokenType="NUMBER",this.type="integer",this.repr=""}toString(){return this.type==="integer"?"INT("+this.value+")":"NUMBER("+this.value+")"}toJSON(){const n=super.toJSON();return n.value=this.value,n.type=this.type,n.repr=this.repr,n}toSource(){return this.repr}}class pm extends at{constructor(){super(),this.tokenType="PERCENTAGE",this.repr=""}toString(){return"PERCENTAGE("+this.value+")"}toJSON(){const n=this.constructor.prototype.constructor.prototype.toJSON.call(this);return n.value=this.value,n.repr=this.repr,n}toSource(){return this.repr+"%"}}class dS extends at{constructor(){super(),this.tokenType="DIMENSION",this.type="integer",this.repr="",this.unit=""}toString(){return"DIM("+this.value+","+this.unit+")"}toJSON(){const n=this.constructor.prototype.constructor.prototype.toJSON.call(this);return n.value=this.value,n.type=this.type,n.repr=this.repr,n.unit=this.unit,n}toSource(){const n=this.repr;let i=os(this.unit);return i[0].toLowerCase()==="e"&&(i[1]==="-"||ht(i.charCodeAt(1),48,57))&&(i="\\65 "+i.slice(1,i.length)),n+i}}function os(t){t=""+t;let n="";const i=t.charCodeAt(0);for(let u=0;u<t.length;u++){const a=t.charCodeAt(u);if(a===0)throw new Ld("Invalid character: the input contains U+0000.");ht(a,1,31)||a===127||u===0&&ht(a,48,57)||u===1&&ht(a,48,57)&&i===45?n+="\\"+a.toString(16)+" ":a>=128||a===45||a===95||ht(a,48,57)||ht(a,65,90)||ht(a,97,122)?n+=t[u]:n+="\\"+t[u]}return n}function hS(t){t=""+t;let n="";for(let i=0;i<t.length;i++){const u=t.charCodeAt(i);if(u===0)throw new Ld("Invalid character: the input contains U+0000.");u>=128||u===45||u===95||ht(u,48,57)||ht(u,65,90)||ht(u,97,122)?n+=t[i]:n+="\\"+u.toString(16)+" "}return n}function vm(t){t=""+t;let n="";for(let i=0;i<t.length;i++){const u=t.charCodeAt(i);if(u===0)throw new Ld("Invalid character: the input contains U+0000.");ht(u,1,31)||u===127?n+="\\"+u.toString(16)+" ":u===34||u===92?n+="\\"+t[i]:n+=t[i]}return n}class Bt extends Error{}function pS(t,n){let i;try{i=lS(t),i[i.length-1]instanceof Su||i.push(new Su)}catch(I){const Z=I.message+` while parsing selector "${t}"`,ke=(I.stack||"").indexOf(I.message);throw ke!==-1&&(I.stack=I.stack.substring(0,ke)+Z+I.stack.substring(ke+I.message.length)),I.message=Z,I}const u=i.find(I=>I instanceof am||I instanceof X0||I instanceof xu||I instanceof sm||I instanceof Z0||I instanceof J0||I instanceof tm||I instanceof rm||I instanceof im||I instanceof dm||I instanceof pm);if(u)throw new Bt(`Unsupported token "${u.toSource()}" while parsing selector "${t}"`);let a=0;const c=new Set;function p(){return new Bt(`Unexpected token "${i[a].toSource()}" while parsing selector "${t}"`)}function g(){for(;i[a]instanceof _c;)a++}function y(I=a){return i[I]instanceof um}function S(I=a){return i[I]instanceof cm}function N(I=a){return i[I]instanceof hm}function W(I=a){return i[I]instanceof nm}function O(I=a){return i[I]instanceof om}function U(I=a){return i[I]instanceof Wt&&i[I].value==="*"}function B(I=a){return i[I]instanceof Su}function oe(I=a){return i[I]instanceof Wt&&[">","+","~"].includes(i[I].value)}function G(I=a){return W(I)||O(I)||B(I)||oe(I)||i[I]instanceof _c}function _(){const I=[C()];for(;g(),!!W();)a++,I.push(C());return I}function C(){return g(),N()||S()?i[a++].value:D()}function D(){const I={simples:[]};for(g(),oe()?I.simples.push({selector:{functions:[{name:"scope",args:[]}]},combinator:""}):I.simples.push({selector:$(),combinator:""});;){if(g(),oe())I.simples[I.simples.length-1].combinator=i[a++].value,g();else if(G())break;I.simples.push({combinator:"",selector:$()})}return I}function $(){let I="";const Z=[];for(;!G();)if(y()||U())I+=i[a++].toSource();else if(i[a]instanceof fm)I+=i[a++].toSource();else if(i[a]instanceof Wt&&i[a].value===".")if(a++,y())I+="."+i[a++].toSource();else throw p();else if(i[a]instanceof em)if(a++,y())if(!n.has(i[a].value.toLowerCase()))I+=":"+i[a++].toSource();else{const ke=i[a++].value.toLowerCase();Z.push({name:ke,args:[]}),c.add(ke)}else if(i[a]instanceof ku){const ke=i[a++].value.toLowerCase();if(n.has(ke)?(Z.push({name:ke,args:_()}),c.add(ke)):I+=`:${ke}(${Y()})`,g(),!O())throw p();a++}else throw p();else if(i[a]instanceof lm){for(I+="[",a++;!(i[a]instanceof Pc)&&!B();)I+=i[a++].toSource();if(!(i[a]instanceof Pc))throw p();I+="]",a++}else throw p();if(!I&&!Z.length)throw p();return{css:I||void 0,functions:Z}}function Y(){let I="";for(;!O()&&!B();)I+=i[a++].toSource();return I}const ie=_();if(!B())throw new Bt(`Error while parsing selector "${t}"`);if(ie.some(I=>typeof I!="object"||!("simples"in I)))throw new Bt(`Error while parsing selector "${t}"`);return{selector:ie,names:Array.from(c)}}const Hv=new Set(["internal:has","left-of","right-of","above","below","near"]),vS=new Set(["left-of","right-of","above","below","near"]),gS=new Set(["not","is","where","has","scope","light","visible","text","text-matches","text-is","has-text","above","below","right-of","left-of","near","nth-match"]);function gm(t){const n=yS(t),i=n.parts.map(u=>{if(u.name==="css"||u.name==="css:light")return u.name==="css:light"&&(u.body=":light("+u.body+")"),{name:"css",body:pS(u.body,gS).selector,source:u.body};if(Hv.has(u.name)){let a,c;try{const g=JSON.parse("["+u.body+"]");if(!Array.isArray(g)||g.length<1||g.length>2||typeof g[0]!="string")throw new Bt(`Malformed selector: ${u.name}=`+u.body);if(a=g[0],g.length===2){if(typeof g[1]!="number"||!vS.has(u.name))throw new Bt(`Malformed selector: ${u.name}=`+u.body);c=g[1]}}catch{throw new Bt(`Malformed selector: ${u.name}=`+u.body)}const p={name:u.name,source:u.body,body:{parsed:gm(a),distance:c}};if(p.body.parsed.parts.some(g=>g.name==="internal:control"&&g.body==="enter-frame"))throw new Bt(`Frames are not allowed inside "${u.name}" selectors`);return p}return{...u,source:u.body}});if(Hv.has(i[0].name))throw new Bt(`"${i[0].name}" selector cannot be first`);return{capture:n.capture,parts:i}}function mS(t){return typeof t=="string"?t:t.parts.map((n,i)=>{const u=n.name==="css"?"":n.name+"=";return`${i===t.capture?"*":""}${u}${n.source}`}).join(" >> ")}function yS(t){let n=0,i,u=0;const a={parts:[]},c=()=>{const g=t.substring(u,n).trim(),y=g.indexOf("=");let S,N;y!==-1&&g.substring(0,y).trim().match(/^[a-zA-Z_0-9-+:*]+$/)?(S=g.substring(0,y).trim(),N=g.substring(y+1)):g.length>1&&g[0]==='"'&&g[g.length-1]==='"'||g.length>1&&g[0]==="'"&&g[g.length-1]==="'"?(S="text",N=g):/^\(*\/\//.test(g)||g.startsWith("..")?(S="xpath",N=g):(S="css",N=g);let W=!1;if(S[0]==="*"&&(W=!0,S=S.substring(1)),a.parts.push({name:S,body:N}),W){if(a.capture!==void 0)throw new Bt("Only one of the selectors can capture using * modifier");a.capture=a.parts.length-1}};if(!t.includes(">>"))return n=t.length,c(),a;const p=()=>{const y=t.substring(u,n).match(/^\s*text\s*=(.*)$/);return!!y&&!!y[1]};for(;n<t.length;){const g=t[n];g==="\\"&&n+1<t.length?n+=2:g===i?(i=void 0,n++):!i&&(g==='"'||g==="'"||g==="`")&&!p()?(i=g,n++):!i&&g===">"&&t[n+1]===">"?(c(),n+=2,u=n):n++}return c(),a}function If(t,n){let i=0,u=t.length===0;const a=()=>t[i]||"",c=()=>{const G=a();return++i,u=i>=t.length,G},p=G=>{throw u?new Bt(`Unexpected end of selector while parsing selector \`${t}\``):new Bt(`Error while parsing selector \`${t}\` - unexpected symbol "${a()}" at position ${i}`+(G?" during "+G:""))};function g(){for(;!u&&/\s/.test(a());)c()}function y(G){return G>=""||G>="0"&&G<="9"||G>="A"&&G<="Z"||G>="a"&&G<="z"||G>="0"&&G<="9"||G==="_"||G==="-"}function S(){let G="";for(g();!u&&y(a());)G+=c();return G}function N(G){let _=c();for(_!==G&&p("parsing quoted string");!u&&a()!==G;)a()==="\\"&&c(),_+=c();return a()!==G&&p("parsing quoted string"),_+=c(),_}function W(){c()!=="/"&&p("parsing regular expression");let G="",_=!1;for(;!u;){if(a()==="\\")G+=c(),u&&p("parsing regular expressiion");else if(_&&a()==="]")_=!1;else if(!_&&a()==="[")_=!0;else if(!_&&a()==="/")break;G+=c()}c()!=="/"&&p("parsing regular expression");let C="";for(;!u&&a().match(/[dgimsuy]/);)C+=c();try{return new RegExp(G,C)}catch(D){throw new Bt(`Error while parsing selector \`${t}\`: ${D.message}`)}}function O(){let G="";return g(),a()==="'"||a()==='"'?G=N(a()).slice(1,-1):G=S(),G||p("parsing property path"),G}function U(){g();let G="";return u||(G+=c()),!u&&G!=="="&&(G+=c()),["=","*=","^=","$=","|=","~="].includes(G)||p("parsing operator"),G}function B(){c();const G=[];for(G.push(O()),g();a()===".";)c(),G.push(O()),g();if(a()==="]")return c(),{name:G.join("."),jsonPath:G,op:"<truthy>",value:null,caseSensitive:!1};const _=U();let C,D=!0;if(g(),a()==="/"){if(_!=="=")throw new Bt(`Error while parsing selector \`${t}\` - cannot use ${_} in attribute with regular expression`);C=W()}else if(a()==="'"||a()==='"')C=N(a()).slice(1,-1),g(),a()==="i"||a()==="I"?(D=!1,c()):(a()==="s"||a()==="S")&&(D=!0,c());else{for(C="";!u&&(y(a())||a()==="+"||a()===".");)C+=c();C==="true"?C=!0:C==="false"?C=!1:n||(C=+C,Number.isNaN(C)&&p("parsing attribute value"))}if(g(),a()!=="]"&&p("parsing attribute value"),c(),_!=="="&&typeof C!="string")throw new Bt(`Error while parsing selector \`${t}\` - cannot use ${_} in attribute with non-string matching value - ${C}`);return{name:G.join("."),jsonPath:G,op:_,value:C,caseSensitive:D}}const oe={name:"",attributes:[]};for(oe.name=S(),g();a()==="[";)oe.attributes.push(B()),g();if(u||p(void 0),!oe.name&&!oe.attributes.length)throw new Bt(`Error while parsing selector \`${t}\` - selector cannot be empty`);return oe}function mm(t,n,i=!1){return ym(CS[t],gm(n),i)}function ym(t,n,i=!1){const u=[...n.parts];for(let p=0;p<u.length-1;p++)if(u[p].name==="nth"&&u[p+1].name==="internal:control"&&u[p+1].body==="enter-frame"){const[g]=u.splice(p,1);u.splice(p+1,0,g)}const a=[];let c=i?"frame-locator":"page";for(let p=0;p<u.length;p++){const g=u[p],y=c;if(c="locator",g.name==="nth"){g.body==="0"?a.push(t.generateLocator(y,"first","")):g.body==="-1"?a.push(t.generateLocator(y,"last","")):a.push(t.generateLocator(y,"nth",g.body));continue}if(g.name==="internal:text"){const{exact:O,text:U}=Af(g.body);a.push(t.generateLocator(y,"text",U,{exact:O}));continue}if(g.name==="internal:has-text"){const{exact:O,text:U}=Af(g.body);a.push(t.generateLocator(y,"has-text",U,{exact:O}));continue}if(g.name==="internal:has"){const O=ym(t,g.body.parsed);a.push(t.generateLocator(y,"has",O));continue}if(g.name==="internal:label"){const{exact:O,text:U}=Af(g.body);a.push(t.generateLocator(y,"label",U,{exact:O}));continue}if(g.name==="internal:role"){const O=If(g.body,!0),U={attrs:[]};for(const B of O.attributes)B.name==="name"?(U.exact=B.caseSensitive,U.name=B.value):(B.name==="level"&&typeof B.value=="string"&&(B.value=+B.value),U.attrs.push({name:B.name==="include-hidden"?"includeHidden":B.name,value:B.value}));a.push(t.generateLocator(y,"role",O.name,U));continue}if(g.name==="internal:testid"){const O=If(g.body,!0),{value:U}=O.attributes[0];a.push(t.generateLocator(y,"test-id",U));continue}if(g.name==="internal:attr"){const O=If(g.body,!0),{name:U,value:B,caseSensitive:oe}=O.attributes[0],G=B,_=!!oe;if(U==="placeholder"){a.push(t.generateLocator(y,"placeholder",G,{exact:_}));continue}if(U==="alt"){a.push(t.generateLocator(y,"alt",G,{exact:_}));continue}if(U==="title"){a.push(t.generateLocator(y,"title",G,{exact:_}));continue}}let S="default";const N=u[p+1];N&&N.name==="internal:control"&&N.body==="enter-frame"&&(S="frame",c="frame-locator",p++);const W={parts:[g]};a.push(t.generateLocator(y,S,mS(W)))}return a.join(".")}function Af(t){let n=!1;const i=t.match(/^\/(.*)\/([igm]*)$/);return i?{text:new RegExp(i[1],i[2])}:(t.endsWith('"')?(t=JSON.parse(t),n=!0):t.endsWith('"s')?(t=JSON.parse(t.substring(0,t.length-1)),n=!0):t.endsWith('"i')&&(t=JSON.parse(t.substring(0,t.length-1)),n=!1),{exact:n,text:t})}class wS{generateLocator(n,i,u,a={}){switch(i){case"default":return`locator(${this.quote(u)})`;case"frame":return`frameLocator(${this.quote(u)})`;case"nth":return`nth(${u})`;case"first":return"first()";case"last":return"last()";case"role":const c=[];Pn(a.name)?c.push(`name: ${a.name}`):typeof a.name=="string"&&(c.push(`name: ${this.quote(a.name)}`),a.exact&&c.push("exact: true"));for(const{name:g,value:y}of a.attrs)c.push(`${g}: ${typeof y=="string"?this.quote(y):y}`);const p=c.length?`, { ${c.join(", ")} }`:"";return`getByRole(${this.quote(u)}${p})`;case"has-text":return`filter({ hasText: ${this.toHasText(u)} })`;case"has":return`filter({ has: ${u} })`;case"test-id":return`getByTestId(${this.quote(u)})`;case"text":return this.toCallWithExact("getByText",u,!!a.exact);case"alt":return this.toCallWithExact("getByAltText",u,!!a.exact);case"placeholder":return this.toCallWithExact("getByPlaceholder",u,!!a.exact);case"label":return this.toCallWithExact("getByLabel",u,!!a.exact);case"title":return this.toCallWithExact("getByTitle",u,!!a.exact);default:throw new Error("Unknown selector kind "+i)}}toCallWithExact(n,i,u){return Pn(i)?`${n}(${i})`:u?`${n}(${this.quote(i)}, { exact: true })`:`${n}(${this.quote(i)})`}toHasText(n){return Pn(n)?String(n):this.quote(n)}quote(n){return da(n,"'")}}class xS{generateLocator(n,i,u,a={}){switch(i){case"default":return`locator(${this.quote(u)})`;case"frame":return`frame_locator(${this.quote(u)})`;case"nth":return`nth(${u})`;case"first":return"first";case"last":return"last";case"role":const c=[];Pn(a.name)?c.push(`name=${this.regexToString(a.name)}`):typeof a.name=="string"&&(c.push(`name=${this.quote(a.name)}`),a.exact&&c.push("exact=True"));for(const{name:g,value:y}of a.attrs){let S=typeof y=="string"?this.quote(y):y;typeof y=="boolean"&&(S=y?"True":"False"),c.push(`${q0(g)}=${S}`)}const p=c.length?`, ${c.join(", ")}`:"";return`get_by_role(${this.quote(u)}${p})`;case"has-text":return`filter(has_text=${this.toHasText(u)})`;case"has":return`filter(has=${u})`;case"test-id":return`get_by_test_id(${this.quote(u)})`;case"text":return this.toCallWithExact("get_by_text",u,!!a.exact);case"alt":return this.toCallWithExact("get_by_alt_text",u,!!a.exact);case"placeholder":return this.toCallWithExact("get_by_placeholder",u,!!a.exact);case"label":return this.toCallWithExact("get_by_label",u,!!a.exact);case"title":return this.toCallWithExact("get_by_title",u,!!a.exact);default:throw new Error("Unknown selector kind "+i)}}regexToString(n){const i=n.flags.includes("i")?", re.IGNORECASE":"";return`re.compile(r"${n.source.replace(/\\\//,"/").replace(/"/g,'\\"')}"${i})`}toCallWithExact(n,i,u){return Pn(i)?`${n}(${this.regexToString(i)})`:u?`${n}(${this.quote(i)}, exact=True)`:`${n}(${this.quote(i)})`}toHasText(n){return Pn(n)?this.regexToString(n):`${this.quote(n)}`}quote(n){return da(n,'"')}}class SS{generateLocator(n,i,u,a={}){let c;switch(n){case"page":c="Page";break;case"frame-locator":c="FrameLocator";break;case"locator":c="Locator";break}switch(i){case"default":return`locator(${this.quote(u)})`;case"frame":return`frameLocator(${this.quote(u)})`;case"nth":return`nth(${u})`;case"first":return"first()";case"last":return"last()";case"role":const p=[];Pn(a.name)?p.push(`.setName(${this.regexToString(a.name)})`):typeof a.name=="string"&&(p.push(`.setName(${this.quote(a.name)})`),a.exact&&p.push(".setExact(true)"));for(const{name:y,value:S}of a.attrs)p.push(`.set${Yu(y)}(${typeof S=="string"?this.quote(S):S})`);const g=p.length?`, new ${c}.GetByRoleOptions()${p.join("")}`:"";return`getByRole(AriaRole.${q0(u).toUpperCase()}${g})`;case"has-text":return`filter(new ${c}.FilterOptions().setHasText(${this.toHasText(u)}))`;case"has":return`filter(new ${c}.FilterOptions().setHas(${u}))`;case"test-id":return`getByTestId(${this.quote(u)})`;case"text":return this.toCallWithExact(c,"getByText",u,!!a.exact);case"alt":return this.toCallWithExact(c,"getByAltText",u,!!a.exact);case"placeholder":return this.toCallWithExact(c,"getByPlaceholder",u,!!a.exact);case"label":return this.toCallWithExact(c,"getByLabel",u,!!a.exact);case"title":return this.toCallWithExact(c,"getByTitle",u,!!a.exact);default:throw new Error("Unknown selector kind "+i)}}regexToString(n){const i=n.flags.includes("i")?", Pattern.CASE_INSENSITIVE":"";return`Pattern.compile(${this.quote(n.source)}${i})`}toCallWithExact(n,i,u,a){return Pn(u)?`${i}(${this.regexToString(u)})`:a?`${i}(${this.quote(u)}, new ${n}.${Yu(i)}Options().setExact(true))`:`${i}(${this.quote(u)})`}toHasText(n){return Pn(n)?this.regexToString(n):this.quote(n)}quote(n){return da(n,'"')}}class kS{generateLocator(n,i,u,a={}){switch(i){case"default":return`Locator(${this.quote(u)})`;case"frame":return`FrameLocator(${this.quote(u)})`;case"nth":return`Nth(${u})`;case"first":return"First";case"last":return"Last";case"role":const c=[];Pn(a.name)?c.push(`NameRegex = ${this.regexToString(a.name)}`):typeof a.name=="string"&&(c.push(`Name = ${this.quote(a.name)}`),a.exact&&c.push("Exact = true"));for(const{name:g,value:y}of a.attrs)c.push(`${Yu(g)} = ${typeof y=="string"?this.quote(y):y}`);const p=c.length?`, new() { ${c.join(", ")} }`:"";return`GetByRole(AriaRole.${Yu(u)}${p})`;case"has-text":return`Filter(new() { ${this.toHasText(u)} })`;case"has":return`Filter(new() { Has = ${u} })`;case"test-id":return`GetByTestId(${this.quote(u)})`;case"text":return this.toCallWithExact("GetByText",u,!!a.exact);case"alt":return this.toCallWithExact("GetByAltText",u,!!a.exact);case"placeholder":return this.toCallWithExact("GetByPlaceholder",u,!!a.exact);case"label":return this.toCallWithExact("GetByLabel",u,!!a.exact);case"title":return this.toCallWithExact("GetByTitle",u,!!a.exact);default:throw new Error("Unknown selector kind "+i)}}regexToString(n){const i=n.flags.includes("i")?", RegexOptions.IgnoreCase":"";return`new Regex(${this.quote(n.source)}${i})`}toCallWithExact(n,i,u){return Pn(i)?`${n}(${this.regexToString(i)})`:u?`${n}(${this.quote(i)}, new() { Exact = true })`:`${n}(${this.quote(i)})`}toHasText(n){return Pn(n)?`HasTextRegex = ${this.regexToString(n)}`:`HasText = ${this.quote(n)}`}quote(n){return da(n,'"')}}const CS={javascript:new wS,python:new xS,java:new SS,csharp:new kS};function Pn(t){return t instanceof RegExp}const TS=({language:t,log:n})=>{const i=tt.createRef(),[u,a]=tt.useState(new Map);return tt.useLayoutEffect(()=>{var c;n.find(p=>p.reveal)&&((c=i.current)==null||c.scrollIntoView({block:"center",inline:"nearest"}))},[i,n]),jn("div",{className:"call-log",style:{flex:"auto"},children:[n.map(c=>{const p=u.get(c.id),g=typeof p=="boolean"?p:c.status!=="done",y=c.params.selector?mm(t,c.params.selector,!1):null,S=`page.${y}`;let N=c.title,W="";return c.title.startsWith("expect.to")||c.title.startsWith("expect.not.to")?(N="expect(",W=`).${c.title.substring(7)}()`):c.title.startsWith("locator.")?(N="",W=`.${c.title.substring(8)}()`):(y||c.params.url)&&(N=c.title+"(",W=")"),jn("div",{className:`call-log-call ${c.status}`,children:[jn("div",{className:"call-log-call-header",children:[Pe("span",{className:`codicon codicon-chevron-${g?"down":"right"}`,style:{cursor:"pointer"},onClick:()=>{const O=new Map(u);O.set(c.id,!g),a(O)}}),N,c.params.url?Pe("span",{className:"call-log-details",children:Pe("span",{className:"call-log-url",title:c.params.url,children:c.params.url})}):void 0,y?Pe("span",{className:"call-log-details",children:Pe("span",{className:"call-log-selector",title:S,children:S})}):void 0,W,Pe("span",{className:"codicon "+LS(c)}),typeof c.duration=="number"?jn("span",{className:"call-log-time",children:["— ",Xx(c.duration)]}):void 0]}),(g?c.messages:[]).map((O,U)=>Pe("div",{className:"call-log-message",children:O.trim()},U)),!!c.error&&Pe("div",{className:"call-log-message error",hidden:!g,children:c.error})]},c.id)}),Pe("div",{ref:i})]})};function LS(t){switch(t.status){case"done":return"codicon-check";case"in-progress":return"codicon-clock";case"paused":return"codicon-debug-pause";case"error":return"codicon-error"}}const ES=({sources:t,paused:n,log:i,mode:u})=>{const[a,c]=tt.useState();tt.useEffect(()=>{!a&&t.length>0&&c(t[0].id)},[a,t]);const p=t.find(N=>N.id===a)||{id:"default",isRecorded:!1,text:"",language:"javascript",label:"",highlight:[]},[g,y]=tt.useState("");window.playwrightSetSelector=(N,W)=>{const O=p.language;y(mm(O,N))},window.playwrightSetFileIfNeeded=N=>{const W=t.find(O=>O.id===N);(W&&!W.isRecorded||!p.isRecorded)&&c(N)};const S=tt.createRef();return tt.useLayoutEffect(()=>{var N;(N=S.current)==null||N.scrollIntoView({block:"center",inline:"nearest"})},[S]),tt.useEffect(()=>{const N=W=>{switch(W.key){case"F8":W.preventDefault(),n?window.dispatch({event:"resume"}):window.dispatch({event:"pause"});break;case"F10":W.preventDefault(),n&&window.dispatch({event:"step"});break}};return document.addEventListener("keydown",N),()=>document.removeEventListener("keydown",N)},[n]),jn("div",{className:"recorder",children:[jn(Fv,{children:[Pe(xr,{icon:"record",title:"Record",toggled:u==="recording",onClick:()=>{window.dispatch({event:"setMode",params:{mode:u==="recording"?"none":"recording"}})},children:"Record"}),Pe(xr,{icon:"files",title:"Copy",disabled:!p||!p.text,onClick:()=>{Bv(p.text)}}),Pe(xr,{icon:"debug-continue",title:"Resume (F8)",disabled:!n,onClick:()=>{window.dispatch({event:"resume"})}}),Pe(xr,{icon:"debug-pause",title:"Pause (F8)",disabled:n,onClick:()=>{window.dispatch({event:"pause"})}}),Pe(xr,{icon:"debug-step-over",title:"Step over (F10)",disabled:!n,onClick:()=>{window.dispatch({event:"step"})}}),Pe("div",{style:{flex:"auto"}}),Pe("div",{children:"Target:"}),Pe("select",{className:"recorder-chooser",hidden:!t.length,value:a,onChange:N=>{c(N.target.selectedOptions[0].value),window.dispatch({event:"fileChanged",params:{file:N.target.selectedOptions[0].value}})},children:NS(t)}),Pe(xr,{icon:"clear-all",title:"Clear",disabled:!p||!p.text,onClick:()=>{window.dispatch({event:"clear"})}}),Pe(xr,{icon:"color-mode",title:"Toggle color mode",toggled:!1,onClick:()=>j1()})]}),jn(qx,{sidebarSize:200,sidebarHidden:u==="recording",children:[Pe(Yx,{text:p.text,language:p.language,highlight:p.highlight,revealLine:p.revealLine}),jn("div",{className:"vbox",children:[jn(Fv,{children:[Pe(xr,{icon:"microscope",title:"Pick locator",toggled:u==="inspecting",onClick:()=>{window.dispatch({event:"setMode",params:{mode:u==="inspecting"?"none":"inspecting"}}).catch(()=>{})},children:"Pick locator"}),Pe(Y0,{text:g,language:p.language,readOnly:!1,focusOnChange:!0,wrapLines:!0,onChange:N=>{y(N),window.dispatch({event:"selectorUpdated",params:{selector:N,language:p.language}})}}),Pe(xr,{icon:"files",title:"Copy",onClick:()=>{Bv(g)}})]}),Pe(TS,{language:p.language,log:Array.from(i.values())})]})]})]})};function NS(t){const n=a=>a.replace(/.*[/\\]([^/\\]+)/,"$1"),i=a=>Pe("option",{value:a.id,children:n(a.label)},a.id);if(t.some(a=>a.group)){const a=new Set(t.map(c=>c.group));return Array.from(a).map(c=>Pe("optgroup",{label:c,children:t.filter(p=>p.group===c).map(p=>i(p))},c))}return t.map(a=>i(a))}function Bv(t){const n=document.createElement("textarea");n.style.position="absolute",n.style.zIndex="-1000",n.value=t,document.body.appendChild(n),n.select(),document.execCommand("copy"),n.remove()}const MS=({})=>{const[t,n]=tt.useState([]),[i,u]=tt.useState(!1),[a,c]=tt.useState(new Map),[p,g]=tt.useState("none");return window.playwrightSetMode=g,window.playwrightSetSources=n,window.playwrightSetPaused=u,window.playwrightUpdateLogs=y=>{const S=new Map(a);for(const N of y)N.reveal=!a.has(N.id),S.set(N.id,N);c(S)},window.playwrightSourcesEchoForTest=t,Pe(ES,{sources:t,paused:i,log:a,mode:p})};(async()=>(U1(),Ff.render(Pe(MS,{}),document.querySelector("#root"))))();
