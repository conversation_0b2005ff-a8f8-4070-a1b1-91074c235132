process.argv = [process.argv[0], ...process.argv.slice(3)];

		import __esrun_url from 'url';

		import { createRequire as __esrun_createRequire } from "module";

		const __esrun_fileUrl = __esrun_url.pathToFileURL("C:\Users\<USER>\OneDrive\Desktop\scrapers13-07\Woolworths\esrun-1753262796000.tmp.mjs");

		const require = __esrun_createRequire(__esrun_fileUrl);
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __require = /* @__PURE__ */ ((x) => typeof require !== "undefined" ? require : typeof Proxy !== "undefined" ? new Proxy(x, {
  get: (a, b) => (typeof require !== "undefined" ? require : a)[b]
}) : x)(function(x) {
  if (typeof require !== "undefined") return require.apply(this, arguments);
  throw Error('Dynamic require of "' + x + '" is not supported');
});
var __commonJS = (cb, mod) => function __require2() {
  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));

// src/typings.js
var require_typings = __commonJS({
  "src/typings.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// src/utilities.js
var require_utilities = __commonJS({
  "src/utilities.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.validCategories = exports.colour = void 0;
    exports.log = log4;
    exports.logError = logError4;
    exports.logProductRow = logProductRow2;
    exports.logTableHeader = logTableHeader2;
    exports.readLinesFromTextFile = readLinesFromTextFile2;
    exports.getTimeElapsedSince = getTimeElapsedSince2;
    exports.toTitleCase = toTitleCase2;
    var fs_1 = __require("fs");
    var tableIDWidth = 6;
    var tableNameWidth = 60;
    var tableSizeWidth = 17;
    exports.colour = {
      red: "\x1B[31m",
      green: "\x1B[32m",
      yellow: "\x1B[33m",
      blue: "\x1B[38;5;117m",
      magenta: "\x1B[35m",
      cyan: "\x1B[36m",
      white: "\x1B[37m",
      crimson: "\x1B[38m",
      grey: "\x1B[90m",
      orange: "\x1B[38;5;214m",
      sky: "\x1B[38;5;153m"
    };
    function log4(colour4, text) {
      const clear = "\x1B[0m";
      console.log(`${colour4}%s${clear}`, text);
    }
    function logError4(text) {
      log4(exports.colour.red, text);
    }
    function logProductRow2(product) {
      var _a;
      const unitPriceString = product.unitPrice ? `$${product.unitPrice.toFixed(2)} /${product.unitName}` : ``;
      log4(getAlternatingRowColour(exports.colour.sky, exports.colour.white), `${product.id.padStart(tableIDWidth)} | ${product.name.slice(0, tableNameWidth).padEnd(tableNameWidth)} | ${(_a = product.size) === null || _a === void 0 ? void 0 : _a.slice(0, tableSizeWidth).padEnd(tableSizeWidth)} | $ ${product.currentPrice.toFixed(2).padStart(4).padEnd(5)} | ` + unitPriceString);
    }
    function logTableHeader2() {
      log4(exports.colour.yellow, `${"ID".padStart(tableIDWidth)} | ${"Name".padEnd(tableNameWidth)} | ${"Size".padEnd(tableSizeWidth)} | ${"Price".padEnd(7)} | Unit Price`);
      let headerLine = "";
      for (let i = 0; i < 111; i++) {
        headerLine += "-";
      }
      log4(exports.colour.yellow, headerLine);
    }
    var alternatingRowColour = false;
    function getAlternatingRowColour(colourA, colourB) {
      alternatingRowColour = alternatingRowColour ? false : true;
      return alternatingRowColour ? colourA : colourB;
    }
    function readLinesFromTextFile2(filename) {
      try {
        const file = (0, fs_1.readFileSync)(filename, "utf-8");
        const result = file.split(/\r?\n/).filter((line) => {
          if (line.trim().length > 0)
            return true;
          else
            return false;
        });
        return result;
      } catch (error) {
        throw "Error reading " + filename;
      }
    }
    function getTimeElapsedSince2(startTime2) {
      let elapsedTimeSeconds = (Date.now() - startTime2) / 1e3;
      let elapsedTimeString = Math.floor(elapsedTimeSeconds).toString();
      if (elapsedTimeSeconds >= 60) {
        return Math.floor(elapsedTimeSeconds / 60) + ":" + Math.floor(elapsedTimeSeconds % 60).toString().padStart(2, "0");
      } else
        return elapsedTimeString + "s";
    }
    exports.validCategories = [
      // freshCategory
      "eggs",
      "fruit",
      "fresh-vegetables",
      "salads-coleslaw",
      "bread",
      "bread-rolls",
      "specialty-bread",
      "bakery-cakes",
      "bakery-desserts",
      // chilledCategory
      "milk",
      "long-life-milk",
      "sour-cream",
      "cream",
      "yoghurt",
      "butter",
      "cheese",
      "cheese-slices",
      "salami",
      "other-deli-foods",
      // meatCategory
      "beef-lamb",
      "chicken",
      "ham",
      "bacon",
      "pork",
      "patties-meatballs",
      "sausages",
      "deli-meats",
      "meat-alternatives",
      "seafood",
      "salmon",
      // frozenCategory
      "ice-cream",
      "ice-blocks",
      "pastries-cheesecake",
      "frozen-chips",
      "frozen-vegetables",
      "frozen-fruit",
      "frozen-seafood",
      "pies-sausage-rolls",
      "pizza",
      "other-savouries",
      // pantryCategory
      "rice",
      "noodles",
      "pasta",
      "beans-spaghetti",
      "canned-fish",
      "canned-meat",
      "soup",
      "cereal",
      "spreads",
      "baking",
      "sauces",
      "oils-vinegars",
      "world-foods",
      // snacksCategory
      "chocolate",
      "boxed-chocolate",
      "chips",
      "crackers",
      "biscuits",
      "muesli-bars",
      "nuts-bulk-mix",
      "sweets-lollies",
      "other-snacks",
      // drinksCategory
      "black-tea",
      "green-tea",
      "herbal-tea",
      "drinking-chocolate",
      "coffee",
      "soft-drinks",
      "energy-drinks",
      "juice",
      // petsCategory
      "cat-food",
      "cat-treats",
      "dog-food",
      "dog-treats"
    ];
    function toTitleCase2(str) {
      return str.replace(/\w\S*/g, function(txt) {
        return txt.charAt(0).toUpperCase() + txt.substring(1).toLowerCase();
      });
    }
  }
});

// src/product-overrides.js
var require_product_overrides = __commonJS({
  "src/product-overrides.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.productOverrides = void 0;
    exports.productOverrides = [
      { id: "206889", size: "180g" },
      { id: "196996", size: "300g" },
      { id: "137967", size: "420g" },
      { id: "125856", size: "450g" },
      { id: "189268", size: "1.13kg" },
      { id: "189150", size: "1.2kg" },
      { id: "190454", size: "2.1kg" },
      { id: "189078", size: "1.3kg" },
      { id: "189136", size: "1.2kg" },
      { id: "755237", size: "931g" },
      { id: "755304", size: "1.1kg" },
      { id: "755246", size: "1020g" },
      { id: "755245", size: "1.2kg" },
      { id: "112273", size: "865ml" },
      { id: "269514", size: "584ml" },
      { id: "269515", size: "584ml" },
      { id: "116518", size: "440ml" },
      { id: "151191", size: "570ml" },
      { id: "279904", size: "575ml" },
      { id: "146149", size: "1000ml" },
      { id: "791925", size: "525g" },
      { id: "774216", size: "525g" },
      { id: "784406", size: "525g" },
      { id: "791916", size: "525g" },
      { id: "306624", size: "185g" },
      { id: "156824", size: "180g" },
      { id: "9023", size: "375g" },
      { id: "266962", category: "sweets-lollies" },
      { id: "171524", size: "230ml", category: "baking" },
      { id: "170021", category: "ice-blocks" },
      { id: "71164", category: "sausages" },
      { id: "71174", category: "sausages" },
      { id: "71168", category: "sausages" },
      { id: "71165", category: "sausages" },
      { id: "331560", category: "specialty-bread" },
      { id: "679412", category: "herbal-tea" },
      { id: "790129", category: "herbal-tea" },
      { id: "267492", category: "herbal-tea" },
      { id: "267485", category: "herbal-tea" },
      { id: "413302", category: "herbal-tea" },
      { id: "267488", category: "herbal-tea" },
      { id: "760872", category: "herbal-tea" },
      { id: "681177", category: "herbal-tea" },
      { id: "95091", category: "herbal-tea" },
      { id: "761093", category: "black-tea" },
      { id: "721661", category: "green-tea" },
      { id: "790129", category: "herbal-tea" },
      { id: "267492", category: "herbal-tea" },
      { id: "267485", category: "herbal-tea" },
      { id: "721034", category: "herbal-tea" },
      { id: "413302", category: "herbal-tea" },
      { id: "267488", category: "herbal-tea" },
      { id: "760872", category: "herbal-tea" },
      { id: "681177", category: "herbal-tea" },
      { id: "95091.", category: "herbal-tea" },
      { id: "184090", category: "herbal-tea" },
      { id: "761093", category: "black-tea" },
      { id: "721661", category: "green-tea" },
      { id: "690093", category: "green-tea" },
      { id: "780922", category: "sauces" },
      { id: "780921", category: "sauces" },
      { id: "72618", category: "black-tea" },
      { id: "6053", category: "black-tea" },
      { id: "72617", category: "black-tea" },
      { id: "168068", category: "black-tea" },
      { id: "6052", category: "black-tea" },
      { id: "761436", category: "black-tea" }
    ];
  }
});

// src/index.ts
import * as dotenv3 from "./node_modules/dotenv/lib/main.js";
import playwright from "./node_modules/playwright/index.mjs";
import * as cheerio from "./node_modules/cheerio/dist/esm/index.js";
import _ from "./node_modules/lodash/lodash.js";
import { setTimeout } from "timers/promises";

// src/mongodb.ts
var import_typings = __toESM(require_typings());
var import_utilities = __toESM(require_utilities());
import { MongoClient, GridFSBucket, ObjectId } from "./node_modules/mongodb/lib/index.js";
import * as dotenv from "./node_modules/dotenv/lib/main.js";
dotenv.config();
dotenv.config({ path: `.env.local`, override: true });
var client;
var db;
var gridFS;
var storeId;
var storesCollection;
var brandsCollection;
var consolidatedProductsCollection;
var priceHistoryCollection;
var categoryHierarchyCollection;
async function establishMongoDB() {
  const connectionString = process.env.MONGODB_CONNECTION_STRING;
  const databaseName = process.env.MONGODB_DATABASE_NAME || "nz-supermarket-scraper";
  if (!connectionString) {
    throw Error("MONGODB_CONNECTION_STRING not set in env");
  }
  try {
    client = new MongoClient(connectionString);
    await client.connect();
    db = client.db(databaseName);
    storesCollection = db.collection("stores");
    brandsCollection = db.collection("brands");
    consolidatedProductsCollection = db.collection("consolidatedProducts");
    priceHistoryCollection = db.collection("priceHistory");
    categoryHierarchyCollection = db.collection("categoryHierarchy");
    gridFS = new GridFSBucket(db, { bucketName: "productImages" });
    (0, import_utilities.log)(import_utilities.colour.green, "\u2705 MongoDB connection established");
    await createIndexes();
  } catch (error) {
    (0, import_utilities.logError)(`Failed to connect to MongoDB: ${error.message}`);
    throw error;
  }
}
async function createIndexes() {
  try {
    await consolidatedProductsCollection.createIndex({
      "displayName": "text",
      "normalizedName": "text",
      "variants.storeName": "text"
    });
    await consolidatedProductsCollection.createIndex({ "categoryId": 1 });
    await consolidatedProductsCollection.createIndex({ "brandId": 1 });
    await consolidatedProductsCollection.createIndex({
      "variants.storeProductId": 1,
      "variants.storeId": 1
    });
    await priceHistoryCollection.createIndex({
      "consolidatedProductId": 1,
      "recordedAt": -1
    });
    await priceHistoryCollection.createIndex({ "year": 1, "month": 1 });
    await categoryHierarchyCollection.createIndex({ "parentId": 1, "sortOrder": 1 });
    await categoryHierarchyCollection.createIndex({ "level": 1, "sortOrder": 1 });
    (0, import_utilities.log)(import_utilities.colour.blue, "\u2705 MongoDB indexes created");
  } catch (error) {
    (0, import_utilities.logError)(`Failed to create indexes: ${error.message}`);
  }
}
async function ensureStoreRow() {
  if (storeId !== void 0) return storeId;
  try {
    const existingStore = await storesCollection.findOne({ storeId: "woolworths" });
    if (existingStore) {
      storeId = existingStore._id;
      return storeId;
    }
    const insertResult = await storesCollection.insertOne({
      storeId: "woolworths",
      name: "Woolworths",
      logoUrl: null,
      createdAt: /* @__PURE__ */ new Date(),
      updatedAt: /* @__PURE__ */ new Date()
    });
    storeId = insertResult.insertedId;
    return storeId;
  } catch (error) {
    (0, import_utilities.logError)(`Failed to ensure store row: ${error.message}`);
    throw error;
  }
}
async function upsertProductToMongoDB(scraped) {
  if (!db) throw Error("MongoDB client not initialised");
  try {
    const sId = await ensureStoreRow();
    const now = /* @__PURE__ */ new Date();
    let consolidatedProduct = await consolidatedProductsCollection.findOne({
      "variants.storeProductId": scraped.id,
      "variants.storeId": sId
    });
    if (!consolidatedProduct) {
      const normalizedName = normalizeProductName(scraped.name, scraped.size);
      (0, import_utilities.log)(import_utilities.colour.cyan, `\u{1F50D} Processing: ${scraped.name} -> normalized: ${normalizedName}`);
      const matchResult = await findBestMatch(normalizedName, scraped.size, scraped.name);
      if (matchResult && matchResult.confidence >= 0.85 && matchResult.product) {
        consolidatedProduct = matchResult.product;
        if (matchResult.matchType !== "exact" && consolidatedProduct) {
          await addProductAlias(consolidatedProduct._id, scraped.name);
        }
        if (consolidatedProduct) {
          const newVariant = {
            storeProductId: scraped.id,
            storeId: sId,
            storeName: scraped.name,
            storeSize: scraped.size || null,
            storeUnitPrice: scraped.unitPrice || null,
            storeUnitName: scraped.unitName || null,
            lastSeen: now,
            isActive: true,
            imageUrl: null
          };
          await consolidatedProductsCollection.updateOne(
            { _id: consolidatedProduct._id },
            {
              $push: { variants: newVariant },
              $set: { updatedAt: now }
            }
          );
          (0, import_utilities.log)(import_utilities.colour.green, `\u2705 Added variant to existing product: ${consolidatedProduct.displayName} (confidence: ${matchResult.confidence.toFixed(3)}, type: ${matchResult.matchType})`);
        }
      } else {
        const newProduct = {
          normalizedName,
          displayName: scraped.name,
          primarySize: scraped.size,
          categoryId: null,
          // TODO: Implement category mapping
          brandId: null,
          // TODO: Implement brand extraction
          matchConfidence: matchResult ? Math.round(matchResult.confidence * 100) : 100,
          manualMatch: false,
          aliases: [],
          // Initialize empty aliases array
          variants: [{
            storeProductId: scraped.id,
            storeId: sId,
            storeName: scraped.name,
            storeSize: scraped.size || null,
            storeUnitPrice: scraped.unitPrice || null,
            storeUnitName: scraped.unitName || null,
            lastSeen: now,
            isActive: true,
            imageUrl: null
          }],
          sizeVariants: scraped.size ? [{
            sizeName: scraped.size,
            sizeWeightGrams: null,
            sizeVolumeMl: null,
            isPrimarySize: true
          }] : [],
          currentPrices: [{
            storeId: sId,
            price: scraped.currentPrice,
            isSpecial: false,
            wasAvailable: true,
            recordedAt: now
          }],
          createdAt: now,
          updatedAt: now
        };
        const insertResult = await consolidatedProductsCollection.insertOne(newProduct);
        consolidatedProduct = { _id: insertResult.insertedId, ...newProduct };
        if (matchResult && matchResult.confidence >= 0.7) {
          (0, import_utilities.log)(import_utilities.colour.blue, `\u{1F195} Created new product (matches below threshold): ${scraped.name} (Best match: ${matchResult.confidence.toFixed(3)})`);
        } else {
          (0, import_utilities.log)(import_utilities.colour.blue, `\u{1F195} Created new consolidated product: ${scraped.name}`);
        }
      }
    } else {
      await consolidatedProductsCollection.updateOne(
        { _id: consolidatedProduct._id },
        {
          $set: {
            "variants.$[variant].lastSeen": now,
            "variants.$[variant].storeUnitPrice": scraped.unitPrice,
            "variants.$[variant].storeUnitName": scraped.unitName,
            "currentPrices.$[price].price": scraped.currentPrice,
            "currentPrices.$[price].recordedAt": now,
            updatedAt: now
          }
        },
        {
          arrayFilters: [
            { "variant.storeProductId": scraped.id },
            { "price.storeId": sId }
          ]
        }
      );
    }
    if (consolidatedProduct) {
      await priceHistoryCollection.insertOne({
        consolidatedProductId: consolidatedProduct._id,
        storeId: sId,
        price: scraped.currentPrice,
        isSpecial: false,
        wasAvailable: true,
        recordedAt: now,
        year: now.getFullYear(),
        month: now.getMonth() + 1
      });
    }
    (0, import_utilities.log)(import_utilities.colour.green, `  Upserted: ${scraped.name.slice(0, 45).padEnd(45)} | $${scraped.currentPrice}`);
    return import_typings.UpsertResponse.PriceChanged;
  } catch (error) {
    (0, import_utilities.logError)(`MongoDB upsert failed: ${error.message}`);
    return import_typings.UpsertResponse.Failed;
  }
}
async function uploadImageToMongoDB(imageUrl, product) {
  if (!gridFS) throw Error("MongoDB GridFS not initialised");
  try {
    const sId = await ensureStoreRow();
    const imageResponse = await fetch(imageUrl);
    if (!imageResponse.ok) {
      (0, import_utilities.logError)(`Failed to download image from ${imageUrl}: ${imageResponse.statusText}`);
      return false;
    }
    const imageBuffer = Buffer.from(await imageResponse.arrayBuffer());
    const existingFiles = await gridFS.find({
      "metadata.productId": product.id
    }).toArray();
    for (const file of existingFiles) {
      await gridFS.delete(file._id);
    }
    const filename = `${product.id}.jpg`;
    const uploadStream = gridFS.openUploadStream(filename, {
      contentType: "image/jpeg",
      metadata: {
        productId: product.id,
        storeId: sId,
        originalUrl: imageUrl,
        uploadedAt: /* @__PURE__ */ new Date()
      }
    });
    await new Promise((resolve, reject) => {
      uploadStream.end(imageBuffer, (error) => {
        if (error) reject(error);
        else resolve(uploadStream.id);
      });
    });
    const imageUrl_gridfs = `gridfs://productImages/${uploadStream.id}`;
    await consolidatedProductsCollection.updateOne(
      { "variants.storeProductId": product.id },
      {
        $set: {
          "variants.$[variant].imageUrl": imageUrl_gridfs
        }
      },
      {
        arrayFilters: [{ "variant.storeProductId": product.id }]
      }
    );
    (0, import_utilities.log)(import_utilities.colour.blue, `  Image uploaded: ${product.id} -> GridFS ${uploadStream.id}`);
    return true;
  } catch (err) {
    (0, import_utilities.logError)(`Image upload error: ${err.message}`);
    return false;
  }
}
var storeDescriptors = [
  "woolworths",
  "countdown",
  "new world",
  "paknsave",
  "pak n save",
  "select",
  "premium",
  "value",
  "budget",
  "signature",
  "essentials",
  "pams",
  "homebrand",
  "signature range",
  "fresh choice",
  "macro",
  "organic",
  "free range",
  "grass fed",
  "natural",
  "artisan",
  "gourmet",
  "deluxe",
  "finest",
  "choice",
  "quality",
  "fresh",
  "pure",
  "real",
  "authentic",
  "traditional",
  "classic"
];
var sizeNormalizations = {
  "grams": "g",
  "gram": "g",
  "kilograms": "kg",
  "kilogram": "kg",
  "kg": "kg",
  "litres": "l",
  "litre": "l",
  "ltr": "l",
  "millilitres": "ml",
  "millilitre": "ml",
  "pieces": "pc",
  "piece": "pc",
  "each": "ea",
  "pack": "pk",
  "packet": "pk"
};
function removeStoreDescriptors(productName) {
  let cleaned = productName.toLowerCase();
  for (const descriptor of storeDescriptors) {
    const regex = new RegExp(`\\b${descriptor}\\b`, "gi");
    cleaned = cleaned.replace(regex, "").trim();
  }
  return cleaned.replace(/\s+/g, " ").trim();
}
function normalizeSize(size) {
  if (!size) return "";
  let normalized = size.toLowerCase().replace(/[^\w\d]/g, "");
  for (const [original, replacement] of Object.entries(sizeNormalizations)) {
    const regex = new RegExp(original, "gi");
    normalized = normalized.replace(regex, replacement);
  }
  return normalized;
}
function calculateSizeSimilarity(size1, size2) {
  if (!size1 || !size2) return 0;
  const normalized1 = normalizeSize(size1);
  const normalized2 = normalizeSize(size2);
  if (normalized1 === normalized2) return 1;
  const num1 = parseFloat(normalized1.replace(/[^\d.]/g, ""));
  const num2 = parseFloat(normalized2.replace(/[^\d.]/g, ""));
  if (!isNaN(num1) && !isNaN(num2)) {
    const ratio = Math.min(num1, num2) / Math.max(num1, num2);
    return ratio > 0.8 ? ratio : 0;
  }
  return calculateSimilarity(normalized1, normalized2);
}
function normalizeProductName(name, size) {
  if (!name) return "";
  let normalized = name.toLowerCase().replace(/'/g, "").replace(/"/g, "").replace(/-/g, " ").replace(/\s+/g, " ").trim();
  normalized = removeStoreDescriptors(normalized);
  const nzBrandMappings = getNZBrandMappings();
  for (const [canonical, variations] of Object.entries(nzBrandMappings)) {
    if (normalized.includes(canonical)) {
      normalized = normalized.replace(canonical, canonical);
      break;
    }
    for (const variation of variations) {
      if (normalized.includes(variation)) {
        normalized = normalized.replace(variation, canonical);
        break;
      }
    }
  }
  if (size) {
    const normalizedSize = normalizeSize(size);
    if (normalizedSize) {
      normalized += ` ${normalizedSize}`;
    }
  }
  return normalized.trim();
}
function getNZBrandMappings() {
  return {
    // === SUPERMARKET PRIVATE LABELS ===
    "pams": ["pam", "pams brand", "pams select"],
    "essentials": ["essentials brand", "countdown essentials"],
    "macro": ["macro brand", "macro organic"],
    "woolworths": ["woolworths brand", "woolworths select"],
    "countdown": ["countdown brand", "countdown select"],
    "homebrand": ["home brand", "homebrand select"],
    "signature": ["signature range", "signature brand"],
    "value": ["value brand", "budget"],
    "fresh choice": ["freshchoice", "fresh choice brand"],
    // === DAIRY BRANDS ===
    "anchor": ["anchor brand", "anchor dairy", "anchor milk", "anchor butter", "anchor cheese"],
    "mainland": ["mainland cheese", "mainland dairy", "mainland brand"],
    "meadowfresh": ["meadow fresh", "meadowfresh milk", "meadow fresh milk"],
    "lewis road": ["lewis road creamery", "lewis road milk", "lewis road butter"],
    "kapiti": ["kapiti cheese", "kapiti ice cream", "kapiti brand"],
    "fernleaf": ["fernleaf milk", "fernleaf powder"],
    "tararua": ["tararua cheese", "tararua dairy"],
    "rolling meadow": ["rolling meadow cheese", "rolling meadow dairy"],
    "whitestone": ["whitestone cheese", "whitestone dairy"],
    "mercer": ["mercer cheese", "mercer dairy"],
    "epicure": ["epicure cheese", "epicure dairy"],
    "kikorangi": ["kikorangi cheese", "kikorangi blue"],
    // === MEAT BRANDS ===
    "tegel": ["tegel chicken", "tegel poultry", "tegel brand"],
    "inghams": ["ingham", "inghams chicken", "inghams poultry"],
    "turks": ["turks poultry", "turks chicken"],
    "brinks": ["brinks chicken", "brinks poultry"],
    "hellers": ["heller", "hellers bacon", "hellers sausages", "hellers smallgoods"],
    "beehive": ["beehive bacon", "beehive ham", "beehive smallgoods"],
    "farmland": ["farmland bacon", "farmland ham"],
    "primo": ["primo smallgoods", "primo bacon"],
    "hans": ["hans smallgoods", "hans continental"],
    "continental deli": ["continental smallgoods", "continental deli"],
    // === BREAD & BAKERY BRANDS ===
    "tip top": ["tiptop", "tip top bread", "tiptop bread"],
    "molenberg": ["molenberg bread", "molenburg", "molenberg wholemeal"],
    "vogels": ["vogel", "vogels bread", "vogel bread", "vogels original"],
    "freyas": ["freya", "freyas bread", "freya bread"],
    "natures fresh": ["nature fresh", "natures fresh bread"],
    "burgen": ["burgen bread", "burgen soy lin"],
    "ploughmans": ["ploughman", "ploughmans bread"],
    "golden": ["golden bread", "golden bakery"],
    "bakers delight": ["bakersdelight", "bakers delight bread"],
    // === BEVERAGE BRANDS ===
    "coke": ["coca cola", "coca-cola", "coke classic", "coca cola classic"],
    "coke zero": ["coca cola zero", "coke zero sugar", "coca cola zero sugar"],
    "diet coke": ["coca cola diet", "diet coca cola"],
    "pepsi": ["pepsi cola", "pepsi classic", "pepsi original"],
    "pepsi max": ["pepsi maximum taste", "pepsi max no sugar"],
    "fanta": ["fanta orange", "fanta grape"],
    "sprite": ["sprite lemon", "sprite lime"],
    "l&p": ["lemon paeroa", "lemon and paeroa", "l and p"],
    "just juice": ["justjuice", "just juice brand"],
    "fresh up": ["freshup", "fresh up juice"],
    "keri": ["keri juice", "keri fresh"],
    "charlies": ["charlie", "charlies juice", "charlies honest"],
    "phoenix": ["phoenix organic", "phoenix juice"],
    "pump": ["pump water", "pump brand"],
    "powerade": ["powerade sports drink"],
    "gatorade": ["gatorade sports drink"],
    // === CEREAL & BREAKFAST BRANDS ===
    "sanitarium": ["sanitarium weetbix", "sanitarium so good"],
    "weetbix": ["weet bix", "wheat biscuits", "sanitarium weetbix"],
    "uncle tobys": ["uncle toby", "uncle tobys oats", "uncle tobys muesli"],
    "kelloggs": ["kellogg", "kelloggs cornflakes", "kelloggs special k"],
    "cornflakes": ["corn flakes", "kelloggs cornflakes"],
    "nutrigrain": ["nutri grain", "kelloggs nutrigrain"],
    "special k": ["specialk", "kelloggs special k"],
    "hubbards": ["hubbards cereal", "hubbards muesli"],
    // === SNACK & CONFECTIONERY BRANDS ===
    "cadbury": ["cadburys", "cadbury chocolate"],
    "whittakers": ["whittaker", "whittakers chocolate"],
    "nestle": ["nestl\xE9", "nestle chocolate"],
    "mars": ["mars bar", "mars chocolate"],
    "snickers": ["snickers bar", "snickers chocolate"],
    "kit kat": ["kitkat", "kit-kat"],
    "twix": ["twix bar", "twix chocolate"],
    "moro": ["moro bar", "moro chocolate"],
    "picnic": ["picnic bar", "picnic chocolate"],
    "crunchie": ["crunchie bar", "crunchie chocolate"],
    "bluebird": ["bluebird chips", "bluebird snacks"],
    "eta": ["eta chips", "eta snacks", "eta peanut butter"],
    "proper": ["proper chips", "proper crisps"],
    "heartland": ["heartland chips", "heartland snacks"],
    "griffins": ["griffin", "griffins biscuits", "griffin biscuits"],
    "arnott": ["arnotts", "arnott biscuits", "arnotts biscuits"],
    "toffee pops": ["toffeepops", "griffins toffee pops"],
    "mallowpuffs": ["mallow puffs", "griffins mallowpuffs"],
    "pics": ["pic", "pic peanut butter", "pics peanut butter"],
    "cottees": ["cottee", "cottees jam", "cottee jam"],
    // === PANTRY & COOKING BRANDS ===
    "watties": ["wattie", "watties tomatoes", "watties sauce"],
    "heinz": ["heinz beans", "heinz tomato sauce", "heinz soup"],
    "maggi": ["maggi noodles", "maggi soup", "maggi instant"],
    "continental soup": ["continental soup", "continental pasta"],
    "mccains": ["mccain", "mccains chips", "mccains frozen"],
    "edgell": ["edgell vegetables", "edgell canned"],
    "greggs": ["greggs coffee", "greggs instant"],
    "nescafe": ["nescaf\xE9", "nescafe coffee"],
    "moccona": ["moccona coffee", "moccona instant"],
    "robert harris": ["robertharris", "robert harris coffee"],
    "bell tea": ["bell tea bags", "bell black tea"],
    "dilmah": ["dilmah tea", "dilmah ceylon"],
    "olivani": ["olivani oil", "olivani olive oil"],
    "bertolli": ["bertolli oil", "bertolli olive oil"],
    "praise": ["praise mayonnaise", "praise dressing"],
    "best foods": ["bestfoods", "best foods mayo"],
    "masterfoods": ["master foods", "masterfoods sauce"],
    "fountain": ["fountain sauce", "fountain tomato sauce"],
    // === FROZEN FOOD BRANDS ===
    "birds eye": ["birdseye", "birds eye vegetables", "birds eye fish"],
    "talley": ["talleys", "talley vegetables", "talley frozen"],
    // === CLEANING & HOUSEHOLD BRANDS ===
    "janola": ["janola bleach", "janola cleaning"],
    "earthwise": ["earthwise cleaning", "earthwise eco"],
    "finish": ["finish dishwasher", "finish tablets"],
    "ajax": ["ajax spray", "ajax cleaning"],
    "jif": ["jif cream cleanser", "jif bathroom"],
    "domestos": ["domestos bleach", "domestos toilet"],
    "toilet duck": ["toiletduck", "toilet duck cleaner"],
    "mr muscle": ["mrmuscle", "mr muscle bathroom"],
    "windex": ["windex glass", "windex cleaner"],
    "persil": ["persil washing powder", "persil liquid"],
    "surf": ["surf washing powder", "surf liquid"],
    "omo": ["omo washing powder", "omo liquid"],
    "cold power": ["coldpower", "cold power liquid"],
    "dynamo": ["dynamo washing liquid"],
    "sorbent": ["sorbent toilet paper", "sorbent tissues"],
    "kleenex": ["kleenex tissues", "kleenex toilet paper"],
    "quilton": ["quilton toilet paper", "quilton tissues"],
    "treasures": ["treasures toilet paper", "treasures tissues"],
    // === HEALTH & BEAUTY BRANDS ===
    "colgate": ["colgate toothpaste", "colgate toothbrush"],
    "oral b": ["oral-b", "oralb", "oral b toothbrush"],
    "sensodyne": ["sensodyne toothpaste"],
    "macleans": ["macleans toothpaste"],
    "head shoulders": ["head and shoulders", "head & shoulders"],
    "pantene": ["pantene shampoo", "pantene conditioner"],
    "herbal essences": ["herbal essence", "herbal essences shampoo"],
    "dove": ["dove soap", "dove body wash"],
    "nivea": ["nivea cream", "nivea body"],
    "vaseline": ["vaseline petroleum jelly"],
    // === BABY & PERSONAL CARE ===
    "huggies": ["huggies nappies", "huggies diapers"],
    "pampers": ["pampers nappies", "pampers diapers"],
    "johnson": ["johnsons", "johnson baby", "johnsons baby"],
    "bepanthen": ["bepanthen cream", "bepanthen nappy"],
    // === PET FOOD BRANDS ===
    "pedigree": ["pedigree dog food", "pedigree dry"],
    "whiskas": ["whiskas cat food", "whiskas wet"],
    "fancy feast": ["fancyfeast", "fancy feast cat"],
    "royal canin": ["royalcanin", "royal canin dog"],
    "hills": ["hills pet food", "hills science diet"],
    "eukanuba": ["eukanuba dog food"],
    "iams": ["iams pet food", "iams dog"],
    "optimum": ["optimum dog food", "optimum pet"],
    "tux": ["tux cat food", "tux pet"],
    "champ": ["champ dog food", "champ pet"],
    // === ADDITIONAL NZ SPECIFIC BRANDS ===
    "tip top ice cream": ["tiptop ice cream", "tip top icecream"],
    "new zealand natural": ["nz natural", "new zealand natural ice cream"],
    "deep south": ["deep south ice cream", "deep south icecream"],
    "barkers": ["barkers jam", "barkers preserves"],
    "san remo": ["sanremo", "san remo pasta"],
    "latina": ["latina pasta", "latina fresh"],
    "uncle bens": ["uncle ben", "uncle bens rice"],
    "sunrice": ["sun rice", "sunrice brand"],
    "campbells": ["campbell", "campbells soup", "campbell soup"],
    "delmaine": ["delmaine vegetables", "delmaine canned"],
    "john west": ["johnwest", "john west tuna", "johnwest tuna"],
    "sirena": ["sirena tuna", "sirena seafood"],
    "edmonds": ["edmonds flour", "edmonds baking"],
    "champion": ["champion flour", "champion baking"],
    "chelsea": ["chelsea sugar", "chelsea baking"]
  };
}
function levenshteinDistance(s1, s2) {
  if (!s1) return s2 ? s2.length : 0;
  if (!s2) return s1.length;
  const matrix = [];
  for (let i = 0; i <= s2.length; i++) {
    matrix[i] = [i];
  }
  for (let j = 0; j <= s1.length; j++) {
    matrix[0][j] = j;
  }
  for (let i = 1; i <= s2.length; i++) {
    for (let j = 1; j <= s1.length; j++) {
      const cost = s1[j - 1] === s2[i - 1] ? 0 : 1;
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,
        matrix[i][j - 1] + 1,
        matrix[i - 1][j - 1] + cost
      );
    }
  }
  return matrix[s2.length][s1.length];
}
function jaccardSimilarity(str1, str2) {
  const set1 = new Set(str1.toLowerCase().split(/\s+/).filter((w) => w.length > 1));
  const set2 = new Set(str2.toLowerCase().split(/\s+/).filter((w) => w.length > 1));
  const intersection = new Set([...set1].filter((x) => set2.has(x)));
  const union = /* @__PURE__ */ new Set([...set1, ...set2]);
  return union.size === 0 ? 0 : intersection.size / union.size;
}
function calculateSimilarity(name1, name2, brand1, brand2) {
  if (!name1 || !name2) return 0;
  if (name1 === name2) return 1;
  const normalized1 = normalizeProductName(name1, void 0);
  const normalized2 = normalizeProductName(name2, void 0);
  if (normalized1 === normalized2) return 1;
  const nzBrandMappings = getNZBrandMappings();
  let mapping1 = null;
  let mapping2 = null;
  for (const [canonical, variations] of Object.entries(nzBrandMappings)) {
    if (normalized1.includes(canonical) || variations.some((v) => normalized1.includes(v))) {
      mapping1 = canonical;
    }
    if (normalized2.includes(canonical) || variations.some((v) => normalized2.includes(v))) {
      mapping2 = canonical;
    }
  }
  if (mapping1 && mapping2 && mapping1 === mapping2) return 0.95;
  const distance = levenshteinDistance(normalized1, normalized2);
  const maxLength = Math.max(normalized1.length, normalized2.length);
  const levenshteinSim = maxLength === 0 ? 1 : Math.max(0, 1 - distance / maxLength);
  const jaccardSim = jaccardSimilarity(normalized1, normalized2);
  let combinedSimilarity = levenshteinSim * 0.6 + jaccardSim * 0.4;
  const words1 = normalized1.split(" ").filter((w) => w.length > 2);
  const words2 = normalized2.split(" ").filter((w) => w.length > 2);
  const commonWords = words1.filter((w) => words2.includes(w));
  if (commonWords.length > 0) {
    const wordBoost = commonWords.length / Math.max(words1.length, words2.length) * 0.2;
    combinedSimilarity = Math.min(1, combinedSimilarity + wordBoost);
  }
  const lengthRatio = Math.min(normalized1.length, normalized2.length) / Math.max(normalized1.length, normalized2.length);
  if (lengthRatio < 0.5) {
    combinedSimilarity *= 0.8;
  }
  return combinedSimilarity;
}
async function findBestMatch(normalizedName, size, originalName) {
  try {
    (0, import_utilities.log)(import_utilities.colour.cyan, `\u{1F50D} Finding match for: "${originalName}" -> normalized: "${normalizedName}"`);
    const exactMatch = await consolidatedProductsCollection.findOne({
      normalizedName
    });
    if (exactMatch) {
      (0, import_utilities.log)(import_utilities.colour.green, `\u2705 Exact match found: ${exactMatch.displayName}`);
      return { product: exactMatch, confidence: 1, matchType: "exact" };
    }
    const nzBrandMappings = getNZBrandMappings();
    for (const [canonical, variations] of Object.entries(nzBrandMappings)) {
      if (normalizedName.includes(canonical) || variations.some((v) => normalizedName.includes(v))) {
        const manualMatch = await consolidatedProductsCollection.findOne({
          normalizedName: canonical
        });
        if (manualMatch) {
          (0, import_utilities.log)(import_utilities.colour.green, `\u2705 Manual mapping match found: ${manualMatch.displayName}`);
          return { product: manualMatch, confidence: 0.95, matchType: "manual" };
        }
      }
    }
    const allProducts = await consolidatedProductsCollection.find({}).limit(1e3).toArray();
    let bestMatch = null;
    let bestScore = 0;
    let matchType = "fuzzy";
    const threshold = 0.8;
    for (const product of allProducts) {
      let score = calculateSimilarity(normalizedName, product.normalizedName);
      if (product.aliases && Array.isArray(product.aliases)) {
        for (const alias of product.aliases) {
          const aliasScore = calculateSimilarity(normalizedName, alias);
          if (aliasScore > score) {
            score = aliasScore;
            matchType = "alias";
          }
        }
      }
      if (size && product.primarySize) {
        const sizeScore = calculateSizeSimilarity(size, product.primarySize);
        if (sizeScore > 0.8) {
          score += 0.05;
        }
      }
      if (score > bestScore && score >= threshold) {
        bestScore = score;
        bestMatch = product;
      }
    }
    if (bestMatch) {
      (0, import_utilities.log)(import_utilities.colour.yellow, `\u2705 Fuzzy match found: ${bestMatch.displayName} (confidence: ${bestScore.toFixed(3)}, type: ${matchType})`);
      return {
        product: bestMatch,
        confidence: Math.min(1, bestScore),
        matchType,
        existingConfidence: bestMatch.matchConfidence
      };
    }
    (0, import_utilities.log)(import_utilities.colour.red, `\u274C No match found for: ${originalName}`);
    return null;
  } catch (error) {
    (0, import_utilities.logError)(`\u274C Error finding match: ${error.message}`);
    return null;
  }
}
async function addProductAlias(consolidatedProductId, newAlias) {
  try {
    const normalizedAlias = normalizeProductName(newAlias);
    if (!normalizedAlias || normalizedAlias.trim() === "") return;
    (0, import_utilities.log)(import_utilities.colour.cyan, `\u{1F4DD} Attempting to add alias: "${newAlias}" -> normalized: "${normalizedAlias}"`);
    const product = await consolidatedProductsCollection.findOne({ _id: consolidatedProductId });
    if (!product) {
      (0, import_utilities.log)(import_utilities.colour.red, `\u274C Product not found for alias addition: ${consolidatedProductId}`);
      return;
    }
    const currentAliases = product.aliases || [];
    if (normalizedAlias === product.normalizedName) {
      (0, import_utilities.log)(import_utilities.colour.yellow, `\u26A0\uFE0F Alias matches normalized name, skipping: ${normalizedAlias}`);
      return;
    }
    if (currentAliases.includes(normalizedAlias)) {
      (0, import_utilities.log)(import_utilities.colour.yellow, `\u26A0\uFE0F Alias already exists, skipping: ${normalizedAlias}`);
      return;
    }
    currentAliases.push(normalizedAlias);
    await consolidatedProductsCollection.updateOne(
      { _id: consolidatedProductId },
      {
        $set: {
          aliases: currentAliases,
          updatedAt: /* @__PURE__ */ new Date()
        }
      }
    );
    (0, import_utilities.log)(import_utilities.colour.cyan, `\u2705 Added alias '${normalizedAlias}' to product: ${product.displayName}`);
  } catch (error) {
    (0, import_utilities.logError)(`\u274C Error adding alias: ${error.message}`);
  }
}
async function closeMongoDB() {
  if (client) {
    await client.close();
    (0, import_utilities.log)(import_utilities.colour.blue, "MongoDB connection closed");
  }
}

// src/consolidated-products-mongodb.ts
var import_utilities2 = __toESM(require_utilities());
import { MongoClient as MongoClient2, ObjectId as ObjectId2 } from "./node_modules/mongodb/lib/index.js";
import * as dotenv2 from "./node_modules/dotenv/lib/main.js";
dotenv2.config();
var client2;
var db2;
var consolidatedProductsCollection2;
var brandsCollection2;
var categoryHierarchyCollection2;
async function initializeConsolidatedProductsMongoDB() {
  const connectionString = process.env.MONGODB_CONNECTION_STRING;
  const databaseName = process.env.MONGODB_DATABASE_NAME || "nz-supermarket-scraper";
  if (!connectionString) {
    throw Error("MONGODB_CONNECTION_STRING not set in env");
  }
  try {
    client2 = new MongoClient2(connectionString);
    await client2.connect();
    db2 = client2.db(databaseName);
    consolidatedProductsCollection2 = db2.collection("consolidatedProducts");
    brandsCollection2 = db2.collection("brands");
    categoryHierarchyCollection2 = db2.collection("categoryHierarchy");
    (0, import_utilities2.log)(import_utilities2.colour.green, "\u2705 Consolidated products MongoDB initialized");
    await ensureBasicCategories();
  } catch (error) {
    (0, import_utilities2.logError)(`Failed to initialize consolidated products MongoDB: ${error.message}`);
    throw error;
  }
}
async function ensureBasicCategories() {
  try {
    const existingCategories = await categoryHierarchyCollection2.countDocuments();
    if (existingCategories === 0) {
      const mainCategories = [
        { name: "Fresh Foods", parentId: null, level: 0, sortOrder: 1 },
        { name: "Chilled & Frozen", parentId: null, level: 0, sortOrder: 2 },
        { name: "Pantry & Dry Goods", parentId: null, level: 0, sortOrder: 3 },
        { name: "Beverages", parentId: null, level: 0, sortOrder: 4 },
        { name: "Health & Household", parentId: null, level: 0, sortOrder: 5 }
      ];
      const insertResult = await categoryHierarchyCollection2.insertMany(mainCategories);
      (0, import_utilities2.log)(import_utilities2.colour.blue, `\u2705 Created ${insertResult.insertedCount} main categories`);
      const freshFoodsId = Object.values(insertResult.insertedIds).find(async (id) => {
        const cat = await categoryHierarchyCollection2.findOne({ _id: id });
        return cat?.name === "Fresh Foods";
      });
      (0, import_utilities2.log)(import_utilities2.colour.blue, "\u2705 Basic category structure created");
    }
  } catch (error) {
    (0, import_utilities2.logError)(`Failed to ensure basic categories: ${error.message}`);
  }
}
async function processConsolidatedProductMongoDB(scrapedProduct) {
  if (!consolidatedProductsCollection2) {
    (0, import_utilities2.logError)("Consolidated products collection not initialized");
    return null;
  }
  try {
    const normalizedName = normalizeProductName2(scrapedProduct.name, scrapedProduct.size);
    const now = /* @__PURE__ */ new Date();
    const existingProduct = await consolidatedProductsCollection2.findOne({
      "variants.storeProductId": scrapedProduct.id
    });
    if (existingProduct) {
      await consolidatedProductsCollection2.updateOne(
        { _id: existingProduct._id },
        {
          $set: {
            "variants.$[variant].lastSeen": now,
            "variants.$[variant].storeUnitPrice": scrapedProduct.unitPrice,
            "variants.$[variant].storeUnitName": scrapedProduct.unitName,
            updatedAt: now
          }
        },
        {
          arrayFilters: [{ "variant.storeProductId": scrapedProduct.id }]
        }
      );
      (0, import_utilities2.log)(import_utilities2.colour.cyan, `  Updated consolidated product: ${scrapedProduct.name.slice(0, 40)}`);
      return existingProduct._id.toString();
    } else {
      const newProduct = {
        normalizedName,
        displayName: scrapedProduct.name,
        primarySize: scrapedProduct.size,
        categoryId: null,
        // TODO: Implement category mapping
        brandId: null,
        // TODO: Implement brand extraction
        matchConfidence: 100,
        manualMatch: false,
        variants: [{
          storeProductId: scrapedProduct.id,
          storeId: "woolworths",
          // Store identifier
          storeName: scrapedProduct.name,
          storeSize: scrapedProduct.size,
          storeUnitPrice: scrapedProduct.unitPrice,
          storeUnitName: scrapedProduct.unitName,
          lastSeen: now,
          isActive: true,
          imageUrl: null
        }],
        sizeVariants: scrapedProduct.size ? [{
          sizeName: scrapedProduct.size,
          sizeWeightGrams: null,
          sizeVolumeMl: null,
          isPrimarySize: true
        }] : [],
        currentPrices: [{
          storeId: "woolworths",
          price: scrapedProduct.currentPrice,
          isSpecial: false,
          wasAvailable: true,
          recordedAt: now
        }],
        createdAt: now,
        updatedAt: now
      };
      const insertResult = await consolidatedProductsCollection2.insertOne(newProduct);
      (0, import_utilities2.log)(import_utilities2.colour.cyan, `  Created consolidated product: ${scrapedProduct.name.slice(0, 40)}`);
      return insertResult.insertedId.toString();
    }
  } catch (error) {
    (0, import_utilities2.logError)(`Failed to process consolidated product: ${error.message}`);
    return null;
  }
}
function normalizeProductName2(name, size) {
  let normalized = name.toLowerCase().replace(/[^a-z0-9\s]/g, " ").replace(/\s+/g, "_").trim();
  if (size) {
    const normalizedSize = size.toLowerCase().replace(/[^a-z0-9]/g, "").trim();
    normalized += "_" + normalizedSize;
  }
  return normalized;
}
async function closeConsolidatedProductsMongoDB() {
  try {
    if (client2) {
      await client2.close();
    }
    (0, import_utilities2.log)(import_utilities2.colour.blue, "\u2705 Consolidated products MongoDB connections closed");
  } catch (error) {
    (0, import_utilities2.logError)(`Failed to close MongoDB connections: ${error.message}`);
  }
}

// src/index.ts
var import_product_overrides = __toESM(require_product_overrides());
var import_utilities3 = __toESM(require_utilities());
dotenv3.config();
dotenv3.config({ path: `.env.local`, override: true });
var pageLoadDelaySeconds = 7;
var productLogDelayMilliSeconds = 20;
var startTime = Date.now();
var categorisedUrls = loadUrlsFile();
var databaseMode = false;
var uploadImagesMode = false;
var headlessMode = true;
categorisedUrls = await handleArguments(categorisedUrls);
if (databaseMode) {
  await establishMongoDB();
  await initializeConsolidatedProductsMongoDB();
}
var browser;
var page;
browser = await establishPlaywrightPage(headlessMode);
if (process.env.SKIP_STORE_SELECTION !== "true") {
  try {
    await selectStoreByLocationName();
  } catch (error) {
    (0, import_utilities3.logError)(`Store selection failed: ${error}`);
    (0, import_utilities3.log)(import_utilities3.colour.yellow, "Continuing with default store location...");
  }
} else {
  (0, import_utilities3.log)(import_utilities3.colour.yellow, "Store selection skipped (SKIP_STORE_SELECTION=true)");
}
await scrapeAllPageURLs();
await browser.close();
if (databaseMode) {
  await closeConsolidatedProductsMongoDB();
  await closeMongoDB();
}
(0, import_utilities3.log)(
  import_utilities3.colour.sky,
  `
All Pages Completed = Total Time Elapsed ${(0, import_utilities3.getTimeElapsedSince)(startTime)} 
`
);
function loadUrlsFile(filePath = "src/urls.txt") {
  const rawLinesFromFile = (0, import_utilities3.readLinesFromTextFile)(filePath);
  let categorisedUrls2 = [];
  rawLinesFromFile.map((line) => {
    let parsedUrls = parseAndCategoriseURL(line);
    if (parsedUrls !== void 0) {
      categorisedUrls2 = [...categorisedUrls2, ...parsedUrls];
    }
  });
  return categorisedUrls2;
}
async function scrapeAllPageURLs() {
  (0, import_utilities3.log)(
    import_utilities3.colour.yellow,
    `${categorisedUrls.length} pages to be scraped`.padEnd(35) + `${pageLoadDelaySeconds}s delay between scrapes`.padEnd(35) + (databaseMode ? "(Database Mode)" : "(Dry Run Mode)")
  );
  for (let i = 0; i < categorisedUrls.length; i++) {
    const categorisedUrl = categorisedUrls[i];
    let url = categorisedUrls[i].url;
    const shortUrl = url.replace("https://", "");
    (0, import_utilities3.log)(
      import_utilities3.colour.yellow,
      `
[${i + 1}/${categorisedUrls.length}] ${shortUrl}`
    );
    try {
      await page.goto(url);
      for (let pageDown = 0; pageDown < 5; pageDown++) {
        const timeBetweenPgDowns = Math.random() * 1e3 + 500;
        await page.waitForTimeout(timeBetweenPgDowns);
        await page.keyboard.press("PageDown");
      }
      await page.setDefaultTimeout(3e4);
      await page.waitForSelector("product-price h3");
      const html = await page.innerHTML("product-grid");
      const $ = cheerio.load(html);
      const allProductEntries = $("cdx-card product-stamp-grid div.product-entry");
      const advertisementEntries = $("div.carousel-track div cdx-card product-stamp-grid div.product-entry");
      const adHrefs = advertisementEntries.map((index, element) => {
        return $(element).find("a").first().attr("href");
      }).toArray();
      const productEntries = allProductEntries.filter((index, element) => {
        const productHref = $(element).find("a").first().attr("href");
        return !adHrefs.includes(productHref);
      });
      (0, import_utilities3.log)(
        import_utilities3.colour.yellow,
        `${productEntries.length} product entries found`.padEnd(35) + `Time Elapsed: ${(0, import_utilities3.getTimeElapsedSince)(startTime)}`.padEnd(35) + `Category: ${_.startCase(categorisedUrl.categories.join(" - "))}`
      );
      if (!databaseMode) (0, import_utilities3.logTableHeader)();
      let perPageLogStats = {
        newProducts: 0,
        priceChanged: 0,
        infoUpdated: 0,
        alreadyUpToDate: 0
      };
      perPageLogStats = await processFoundProductEntries(categorisedUrl, productEntries, perPageLogStats);
      if (databaseMode) {
        (0, import_utilities3.log)(
          import_utilities3.colour.blue,
          `Supabase: ${perPageLogStats.newProducts} new products, ${perPageLogStats.priceChanged} updated prices, ${perPageLogStats.infoUpdated} updated info, ${perPageLogStats.alreadyUpToDate} already up-to-date`
        );
      }
      await setTimeout(pageLoadDelaySeconds * 1e3);
    } catch (error) {
      if (typeof error === "string") {
        if (error.includes("NS_ERROR_CONNECTION_REFUSED")) {
          (0, import_utilities3.logError)("Connection Failed - Check Firewall\n" + error);
          return;
        }
      }
      (0, import_utilities3.logError)(
        "Page Timeout after 30 seconds - Skipping this page\n" + error
      );
    }
  }
}
async function processFoundProductEntries(categorisedUrl, productEntries, perPageLogStats) {
  for (let i = 0; i < productEntries.length; i++) {
    const productEntryElement = productEntries[i];
    const product = playwrightElementToProduct(
      productEntryElement,
      categorisedUrl.categories
    );
    if (databaseMode && product !== void 0) {
      const response = await upsertProductToMongoDB(product);
      const consolidatedProductId = await processConsolidatedProductMongoDB(product);
      switch (response) {
        case 3 /* AlreadyUpToDate */:
          perPageLogStats.alreadyUpToDate++;
          break;
        case 2 /* InfoChanged */:
          perPageLogStats.infoUpdated++;
          break;
        case 0 /* NewProduct */:
          perPageLogStats.newProducts++;
          break;
        case 1 /* PriceChanged */:
          perPageLogStats.priceChanged++;
          break;
        default:
          break;
      }
      if (uploadImagesMode) {
        const imageUrlBase = "https://assets.woolworths.com.au/images/2010/";
        const imageUrlExtensionAndQueryParams = ".jpg?impolicy=wowcdxwbjbx&w=900&h=900";
        const imageUrl = imageUrlBase + product.id + imageUrlExtensionAndQueryParams;
        await uploadImageToMongoDB(imageUrl, product);
      }
    } else if (!databaseMode && product !== void 0) {
      (0, import_utilities3.logProductRow)(product);
    }
    await setTimeout(productLogDelayMilliSeconds);
  }
  return perPageLogStats;
}
function handleArguments(categorisedUrls2) {
  if (process.argv.length > 2) {
    const userArgs = process.argv.slice(2, process.argv.length);
    let potentialUrl = "";
    userArgs.forEach(async (arg) => {
      if (arg === "db") databaseMode = true;
      else if (arg === "images") uploadImagesMode = true;
      else if (arg === "headless") headlessMode = true;
      else if (arg === "headed") headlessMode = false;
      else if (arg.includes(".co.nz")) potentialUrl += arg;
      else if (arg === "reverse") categorisedUrls2 = categorisedUrls2.reverse();
    });
    const parsedUrl = parseAndCategoriseURL(potentialUrl);
    if (parsedUrl !== void 0) categorisedUrls2 = [parsedUrl];
  }
  return categorisedUrls2;
}
async function establishPlaywrightPage(headless = true) {
  (0, import_utilities3.log)(
    import_utilities3.colour.yellow,
    "Launching Browser.. " + (process.argv.length > 2 ? "(" + (process.argv.length - 2) + " arguments found)" : "")
  );
  browser = await playwright.firefox.launch({
    headless
  });
  page = await browser.newPage();
  await routePlaywrightExclusions();
  return browser;
}
async function selectStoreByLocationName(locationName = "") {
  if (locationName === "") {
    if (process.env.STORE_NAME) locationName = process.env.STORE_NAME;
    else {
      (0, import_utilities3.log)(import_utilities3.colour.yellow, "No store location specified - using default location");
      return;
    }
  }
  (0, import_utilities3.log)(import_utilities3.colour.yellow, `Attempting to select store location: ${locationName}`);
  try {
    await page.setDefaultTimeout(15e3);
    await page.goto("https://www.woolworths.co.nz/shop/browse", {
      waitUntil: "domcontentloaded"
    });
    await page.waitForTimeout(2e3);
    const possibleSelectors = [
      'button[data-testid="store-selector"]',
      'button[aria-label*="store"]',
      'button[aria-label*="location"]',
      '[data-testid="change-store"]',
      'button:has-text("Change store")',
      'button:has-text("Select store")',
      ".store-selector button",
      '[class*="store"] button',
      "fieldset div div p button"
    ];
    let storeButton = null;
    for (const selector of possibleSelectors) {
      try {
        await page.waitForSelector(selector, { timeout: 3e3 });
        storeButton = page.locator(selector).first();
        (0, import_utilities3.log)(import_utilities3.colour.green, `Found store selector: ${selector}`);
        break;
      } catch {
      }
    }
    if (!storeButton) {
      (0, import_utilities3.log)(import_utilities3.colour.yellow, "No store selector found - proceeding with default location");
      return;
    }
    await storeButton.click();
    await page.waitForTimeout(1e3);
    const inputSelectors = [
      'input[placeholder*="suburb"]',
      'input[placeholder*="location"]',
      'input[placeholder*="address"]',
      'input[type="text"]',
      "form-suburb-autocomplete form-input input",
      '[data-testid="location-input"]',
      ".location-input input",
      'input[aria-label*="location"]'
    ];
    let locationInput = null;
    for (const selector of inputSelectors) {
      try {
        await page.waitForSelector(selector, { timeout: 3e3 });
        locationInput = page.locator(selector).first();
        (0, import_utilities3.log)(import_utilities3.colour.green, `Found location input: ${selector}`);
        break;
      } catch {
      }
    }
    if (!locationInput) {
      (0, import_utilities3.log)(import_utilities3.colour.yellow, "No location input found - proceeding with default location");
      return;
    }
    await locationInput.clear();
    await locationInput.fill(locationName);
    await page.waitForTimeout(2e3);
    try {
      const suggestionSelectors = [
        '[role="option"]:first-child',
        ".suggestion:first-child",
        ".autocomplete-item:first-child",
        "li:first-child",
        '[data-testid="suggestion"]:first-child'
      ];
      let suggestionFound = false;
      for (const selector of suggestionSelectors) {
        try {
          await page.waitForSelector(selector, { timeout: 2e3 });
          await page.locator(selector).click();
          suggestionFound = true;
          (0, import_utilities3.log)(import_utilities3.colour.green, `Selected suggestion using: ${selector}`);
          break;
        } catch {
        }
      }
      if (!suggestionFound) {
        await page.keyboard.press("ArrowDown");
        await page.waitForTimeout(300);
        await page.keyboard.press("Enter");
        (0, import_utilities3.log)(import_utilities3.colour.yellow, "Used keyboard navigation to select location");
      }
      await page.waitForTimeout(1e3);
      const saveSelectors = [
        'button:has-text("Save")',
        'button:has-text("Continue")',
        'button:has-text("Confirm")',
        'button:has-text("Select")',
        '[data-testid="save-location"]',
        ".save-button",
        'button[type="submit"]'
      ];
      for (const selector of saveSelectors) {
        try {
          await page.waitForSelector(selector, { timeout: 2e3 });
          await page.locator(selector).click();
          (0, import_utilities3.log)(import_utilities3.colour.green, `Clicked save button: ${selector}`);
          break;
        } catch {
        }
      }
      await page.waitForTimeout(2e3);
      (0, import_utilities3.log)(import_utilities3.colour.green, `Successfully changed location to: ${locationName}`);
    } catch (error) {
      (0, import_utilities3.log)(import_utilities3.colour.yellow, `Could not select location suggestion - using typed location: ${error}`);
    }
  } catch (error) {
    (0, import_utilities3.logError)(`Store location selection failed: ${error}`);
    (0, import_utilities3.log)(import_utilities3.colour.yellow, "Proceeding with default location");
  }
}
function playwrightElementToProduct(element, categories) {
  const $ = cheerio.load(element);
  let idNameSizeH3 = $(element).find("h3").filter((i, element2) => {
    if ($(element2).attr("id")?.includes("-title")) {
      return true;
    } else return false;
  });
  let product = {
    // ID
    // -------
    // Extract product ID from h3 id attribute, and remove non-numbers
    id: idNameSizeH3.attr("id")?.replace(/\D/g, ""),
    // Source Site - set where the source of information came from
    sourceSite: "countdown.co.nz",
    // use countdown for consistency with old data
    // Categories
    category: categories,
    // already obtained from url/text file
    // Store today's date
    lastChecked: /* @__PURE__ */ new Date(),
    lastUpdated: /* @__PURE__ */ new Date(),
    // These values will later be overwritten
    name: "",
    priceHistory: [],
    currentPrice: 0
  };
  let rawNameAndSize = idNameSizeH3.text().trim();
  rawNameAndSize = rawNameAndSize.toLowerCase().replace("  ", " ").replace("fresh fruit", "").replace("fresh vegetable", "").trim();
  let tryMatchSize = rawNameAndSize.match(/(tray\s\d+)|(\d+(\.\d+)?(\-\d+\.\d+)?\s?(g|kg|l|ml|pack))\b/g);
  if (!tryMatchSize) {
    product.name = (0, import_utilities3.toTitleCase)(rawNameAndSize);
    product.size = "";
  } else {
    let indexOfSizeSection = rawNameAndSize.indexOf(tryMatchSize[0]);
    product.name = (0, import_utilities3.toTitleCase)(rawNameAndSize.slice(0, indexOfSizeSection)).trim();
    let cleanedSize = rawNameAndSize.slice(indexOfSizeSection).trim();
    if (cleanedSize.match(/\d+l\b/)) {
      cleanedSize = cleanedSize.replace("l", "L");
    }
    cleanedSize.replace("tray", "Tray");
    product.size = cleanedSize;
  }
  const dollarString = $(element).find("product-price div h3 em").text().trim();
  let centString = $(element).find("product-price div h3 span").text().trim();
  if (centString.includes("kg")) product.size = "per kg";
  centString = centString.replace(/\D/g, "");
  product.currentPrice = Number(dollarString + "." + centString);
  const today = /* @__PURE__ */ new Date();
  today.setMinutes(0);
  today.setSeconds(0);
  const todaysDatedPrice = {
    date: today,
    price: product.currentPrice
  };
  product.priceHistory = [todaysDatedPrice];
  const rawUnitPrice = $(element).find("span.cupPrice").text().trim();
  if (rawUnitPrice) {
    const unitPriceString = rawUnitPrice.split("/")[0].replace("$", "").trim();
    let unitPrice = Number.parseFloat(unitPriceString);
    const amountAndUnit = rawUnitPrice.split("/")[1].trim();
    let amount = Number.parseInt(amountAndUnit.match(/\d+/g)?.[0] || "");
    let unit = amountAndUnit.match(/\w+/g)?.[0] || "";
    if (amountAndUnit == "100g") {
      amount = amount * 10;
      unitPrice = unitPrice * 10;
      unit = "kg";
    } else if (amountAndUnit == "100mL") {
      amount = amount * 10;
      unitPrice = unitPrice * 10;
      unit = "L";
    }
    unit = unit.replace("1kg", "kg");
    unit = unit.replace("1L", "L");
    product.unitPrice = unitPrice;
    product.unitName = unit;
  }
  import_product_overrides.productOverrides.forEach((override) => {
    if (override.id === product.id) {
      if (override.size !== void 0) {
        product.size = override.size;
      }
      if (override.category !== void 0) {
        product.category = [override.category];
      }
    }
  });
  if (validateProduct(product)) return product;
  else {
    try {
      (0, import_utilities3.logError)(
        `  Unable to Scrape: ${product.id.padStart(6)} | ${product.name} | $${product.currentPrice}`
      );
    } catch {
      (0, import_utilities3.logError)("  Unable to Scrape ID from product");
    }
    return void 0;
  }
}
function validateProduct(product) {
  try {
    if (product.name.match(/\$\s\d+/)) return false;
    if (product.name.length < 4 || product.name.length > 100) return false;
    if (product.id.length < 2 || product.id.length > 20) return false;
    if (product.currentPrice <= 0 || product.currentPrice === null || product.currentPrice === void 0 || Number.isNaN(product.currentPrice) || product.currentPrice > 999) {
      return false;
    }
    return true;
  } catch (error) {
    return false;
  }
}
function parseAndCategoriseURL(line) {
  let baseCategorisedURL = { url: "", categories: [] };
  let parsedUrls = [];
  let numPagesPerURL = 1;
  if (!line.includes("woolworths.co.nz")) {
    return void 0;
  } else if (line.includes("?search=")) {
    parsedUrls.push({ url: line, categories: [] });
  } else {
    line.split(" ").forEach((section) => {
      if (section.includes("woolworths.co.nz")) {
        baseCategorisedURL.url = section;
        if (!baseCategorisedURL.url.startsWith("http"))
          baseCategorisedURL.url = "https://" + section;
        if (section.includes("?")) {
          baseCategorisedURL.url = line.substring(0, line.indexOf("?"));
        }
        baseCategorisedURL.url += "?page=1";
      } else if (section.startsWith("categories=")) {
        let splitCategories = [section.replace("categories=", "")];
        if (section.includes(","))
          splitCategories = section.replace("categories=", "").split(",");
        baseCategorisedURL.categories = splitCategories;
      } else if (section.startsWith("pages=")) {
        numPagesPerURL = Number.parseInt(section.split("=")[1]);
      }
      if (baseCategorisedURL.categories.length === 0) {
        const baseUrl = baseCategorisedURL.url.split("?")[0];
        let slashSections = baseUrl.split("/");
        baseCategorisedURL.categories = [slashSections[slashSections.length - 1]];
      }
    });
    for (let i = 1; i <= numPagesPerURL; i++) {
      let pagedUrl = {
        url: baseCategorisedURL.url.replace("page=1", "page=" + i),
        categories: baseCategorisedURL.categories
      };
      parsedUrls.push(pagedUrl);
    }
  }
  return parsedUrls;
}
async function routePlaywrightExclusions() {
  let typeExclusions = ["image", "media", "font"];
  let urlExclusions = [
    "googleoptimize.com",
    "gtm.js",
    "visitoridentification.js",
    "js-agent.newrelic.com",
    "cquotient.com",
    "googletagmanager.com",
    "cloudflareinsights.com",
    "dwanalytics",
    "facebook.net",
    "chatWidget",
    "edge.adobedc.net",
    "\u200B/Content/Banners/",
    "go-mpulse.net"
  ];
  await page.route("**/*", async (route) => {
    const req = route.request();
    let excludeThisRequest = false;
    urlExclusions.forEach((excludedURL) => {
      if (req.url().includes(excludedURL)) excludeThisRequest = true;
    });
    typeExclusions.forEach((excludedType) => {
      if (req.resourceType() === excludedType) excludeThisRequest = true;
    });
    if (excludeThisRequest) {
      await route.abort();
    } else {
      await route.continue();
    }
  });
  return;
}
export {
  databaseMode,
  parseAndCategoriseURL,
  playwrightElementToProduct,
  uploadImagesMode
};
//# sourceMappingURL=data:application/json;base64,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

	