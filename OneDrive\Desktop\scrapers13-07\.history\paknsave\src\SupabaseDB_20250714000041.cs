using Npgsql;
using Dapper;
using Microsoft.Extensions.Configuration;
using static Scraper.Utilities;

namespace Scraper
{
    public static class SupabaseDB
    {
        private static string? _connString;
        private static int? _storeId;

        public static void Initialise(IConfiguration config)
        {
            // Debug: Print all configuration keys
            Log("All configuration keys:");
            foreach (var kvp in config.AsEnumerable())
            {
                Log($"  {kvp.Key} = {kvp.Value}");
            }
            
            _connString = config.GetSection("POSTGRES_CONNECTION").Value;
            Log($"Retrieved POSTGRES_CONNECTION: '{_connString ?? "NULL"}'");
            if (string.IsNullOrWhiteSpace(_connString))
                throw new Exception("POSTGRES_CONNECTION connection string not found in appsettings.json or environment variables");
        }

        private static async Task<NpgsqlConnection> GetOpenConnectionAsync()
        {
            if (_connString == null) throw new Exception("SupabaseDB not initialised");
            var conn = new NpgsqlConnection(_connString);
            await conn.OpenAsync();
            return conn;
        }

        public enum UpsertResponse
        {
            NewProduct,
            PriceUpdated,
            InfoUpdated,
            AlreadyUpToDate,
            Failed
        }

        public static async Task<UpsertResponse> UpsertProduct(Program.Product p, int storeId)
        {
            try
            {
                using var conn = await GetOpenConnectionAsync();

                // upsert product
                string productSql = @"insert into products (
                        id, name, size, unit_price, unit_name, original_unit_qty, source_site, last_updated, last_checked)
                    values (@id,@name,@size,@unit_price,@unit_name,@original_unit_qty,@source_site,@last_updated,@last_checked)
                    on conflict(id) do update set
                      name = excluded.name,
                      size = excluded.size,
                      unit_price = excluded.unit_price,
                      unit_name = excluded.unit_name,
                      original_unit_qty = excluded.original_unit_qty,
                      source_site = excluded.source_site,
                      last_checked = excluded.last_checked
                    returning xmax = 0 as inserted;";

                bool inserted = await conn.ExecuteScalarAsync<bool>(productSql, new
                {
                    id = p.id,
                    name = p.name,
                    size = p.size,
                    unit_price = p.unitPrice,
                    unit_name = p.unitName,
                    original_unit_qty = p.originalUnitQuantity,
                    source_site = p.sourceSite,
                    last_updated = p.lastUpdated,
                    last_checked = p.lastChecked
                });

                // insert price history row always
                string priceSql = "insert into prices(product_id, store_id, price, recorded_at) values (@pid,@sid,@price,@ts)";
                await conn.ExecuteAsync(priceSql, new { pid = p.id, sid = storeId, price = p.currentPrice, ts = p.lastUpdated });

                return inserted ? UpsertResponse.NewProduct : UpsertResponse.PriceUpdated;
            }
            catch (Exception e)
            {
                LogError("Postgres upsert failed: " + e.Message);
                return UpsertResponse.Failed;
            }
        }

        public static async Task<int> EnsureStoreRow(string storeName)
        {
            if (_storeId != null) return _storeId.Value;
            using var conn = await GetOpenConnectionAsync();
            var id = await conn.QueryFirstOrDefaultAsync<int?>("select id from stores where name=@n", new { n = storeName });
            if (id == null)
            {
                id = await conn.ExecuteScalarAsync<int>("insert into stores(name) values(@n) returning id", new { n = storeName });
            }
            _storeId = id;
            return id.Value;
        }
    }
} 