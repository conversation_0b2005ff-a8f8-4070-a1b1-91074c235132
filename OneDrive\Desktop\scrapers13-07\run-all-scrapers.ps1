param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("dev", "db", "db-images")]
    [string]$Mode
)

Write-Host "Running all scrapers in $Mode mode..." -ForegroundColor Green
Write-Host ""

# Check if MongoDB is running
Write-Host "Starting MongoDB check..." -ForegroundColor Yellow
$mongoRunning = docker ps --format "table {{.Names}}" | Select-String "mongodb"
if (-not $mongoRunning) {
    Write-Host "MongoDB container not running. Starting it..." -ForegroundColor Yellow
    docker run -d -p 27017:27017 --name mongodb mongo:latest
    Start-Sleep -Seconds 5
}

# Create logs directory if it doesn't exist
if (-not (Test-Path "logs")) {
    New-Item -ItemType Directory -Path "logs" | Out-Null
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Starting Woolworths Scraper" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Set-Location "Woolworths"
switch ($Mode) {
    "dev" { 
        Start-Process -FilePath "npm" -ArgumentList "run", "dev" -RedirectStandardOutput "..\logs\woolworths.log" -RedirectStandardError "..\logs\woolworths.log" -NoNewWindow
    }
    "db" { 
        Start-Process -FilePath "npm" -ArgumentList "run", "db" -RedirectStandardOutput "..\logs\woolworths.log" -RedirectStandardError "..\logs\woolworths.log" -NoNewWindow
    }
    "db-images" { 
        Start-Process -FilePath "npm" -ArgumentList "run", "db-images" -RedirectStandardOutput "..\logs\woolworths.log" -RedirectStandardError "..\logs\woolworths.log" -NoNewWindow
    }
}
Set-Location ".."

Start-Sleep -Seconds 2

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Starting New World Scraper" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Set-Location "new-world\src"
switch ($Mode) {
    "dev" { 
        Start-Process -FilePath "dotnet" -ArgumentList "run" -RedirectStandardOutput "..\..\logs\newworld.log" -RedirectStandardError "..\..\logs\newworld.log" -NoNewWindow
    }
    "db" { 
        Start-Process -FilePath "dotnet" -ArgumentList "run", "db" -RedirectStandardOutput "..\..\logs\newworld.log" -RedirectStandardError "..\..\logs\newworld.log" -NoNewWindow
    }
    "db-images" { 
        Start-Process -FilePath "dotnet" -ArgumentList "run", "db", "images" -RedirectStandardOutput "..\..\logs\newworld.log" -RedirectStandardError "..\..\logs\newworld.log" -NoNewWindow
    }
}
Set-Location "..\.."

Start-Sleep -Seconds 2

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Starting PakNSave Scraper" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Set-Location "paknsave\src"
switch ($Mode) {
    "dev" { 
        Start-Process -FilePath "dotnet" -ArgumentList "run" -RedirectStandardOutput "..\..\logs\paknsave.log" -RedirectStandardError "..\..\logs\paknsave.log" -NoNewWindow
    }
    "db" { 
        Start-Process -FilePath "dotnet" -ArgumentList "run", "db" -RedirectStandardOutput "..\..\logs\paknsave.log" -RedirectStandardError "..\..\logs\paknsave.log" -NoNewWindow
    }
    "db-images" { 
        Start-Process -FilePath "dotnet" -ArgumentList "run", "db", "images" -RedirectStandardOutput "..\..\logs\paknsave.log" -RedirectStandardError "..\..\logs\paknsave.log" -NoNewWindow
    }
}
Set-Location "..\.."

Write-Host ""
Write-Host "All scrapers started!" -ForegroundColor Green
Write-Host ""
Write-Host "Log files:" -ForegroundColor Yellow
Write-Host "  Woolworths: logs\woolworths.log" -ForegroundColor White
Write-Host "  New World:  logs\newworld.log" -ForegroundColor White
Write-Host "  PakNSave:   logs\paknsave.log" -ForegroundColor White
Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
