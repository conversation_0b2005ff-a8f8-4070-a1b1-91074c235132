using MongoDB.Driver;
using MongoDB.Bson;
using MongoDB.Driver.GridFS;
using Microsoft.Extensions.Configuration;
using static Scraper.Utilities;

namespace Scraper
{
    public static class MongoDB
    {
        private static IMongoClient? _client;
        private static IMongoDatabase? _database;
        private static IGridFSBucket? _gridFS;
        private static ObjectId? _storeId;
        
        // Collections
        private static IMongoCollection<BsonDocument>? _storesCollection;
        private static IMongoCollection<BsonDocument>? _brandsCollection;
        private static IMongoCollection<BsonDocument>? _consolidatedProductsCollection;
        private static IMongoCollection<BsonDocument>? _priceHistoryCollection;
        private static IMongoCollection<BsonDocument>? _categoryHierarchyCollection;

        public static void Initialise(IConfiguration config)
        {
            var connectionString = config.GetSection("MONGODB_CONNECTION_STRING").Value;
            var databaseName = config.GetSection("MONGODB_DATABASE_NAME").Value ?? "nz-supermarket-scraper";
            
            if (string.IsNullOrWhiteSpace(connectionString))
                throw new Exception("MONGODB_CONNECTION_STRING not configured");

            try
            {
                _client = new MongoClient(connectionString);
                _database = _client.GetDatabase(databaseName);
                
                // Initialize collections
                _storesCollection = _database.GetCollection<BsonDocument>("stores");
                _brandsCollection = _database.GetCollection<BsonDocument>("brands");
                _consolidatedProductsCollection = _database.GetCollection<BsonDocument>("consolidatedProducts");
                _priceHistoryCollection = _database.GetCollection<BsonDocument>("priceHistory");
                _categoryHierarchyCollection = _database.GetCollection<BsonDocument>("categoryHierarchy");
                
                // Initialize GridFS for image storage
                _gridFS = new GridFSBucket(_database, new GridFSBucketOptions
                {
                    BucketName = "productImages"
                });
                
                // Create indexes for performance
                CreateIndexes();
                
                Utilities.Log("✅ MongoDB connection established", ConsoleColor.Green);
            }
            catch (Exception ex)
            {
                LogError($"Failed to connect to MongoDB: {ex.Message}");
                throw;
            }
        }

        private static void CreateIndexes()
        {
            try
            {
                // Consolidated products indexes
                var consolidatedIndexes = new[]
                {
                    new CreateIndexModel<BsonDocument>(
                        Builders<BsonDocument>.IndexKeys.Text("displayName").Text("normalizedName").Text("variants.storeName")
                    ),
                    new CreateIndexModel<BsonDocument>(
                        Builders<BsonDocument>.IndexKeys.Ascending("categoryId")
                    ),
                    new CreateIndexModel<BsonDocument>(
                        Builders<BsonDocument>.IndexKeys.Ascending("brandId")
                    ),
                    new CreateIndexModel<BsonDocument>(
                        Builders<BsonDocument>.IndexKeys.Ascending("variants.storeProductId").Ascending("variants.storeId")
                    )
                };
                _consolidatedProductsCollection?.Indexes.CreateMany(consolidatedIndexes);
                
                // Price history indexes
                var priceIndexes = new[]
                {
                    new CreateIndexModel<BsonDocument>(
                        Builders<BsonDocument>.IndexKeys.Ascending("consolidatedProductId").Descending("recordedAt")
                    ),
                    new CreateIndexModel<BsonDocument>(
                        Builders<BsonDocument>.IndexKeys.Ascending("year").Ascending("month")
                    )
                };
                _priceHistoryCollection?.Indexes.CreateMany(priceIndexes);
                
                Utilities.Log("✅ MongoDB indexes created", ConsoleColor.Blue);
            }
            catch (Exception ex)
            {
                LogError($"Failed to create indexes: {ex.Message}");
            }
        }

        public enum UpsertResponse
        {
            NewProduct,
            PriceUpdated,
            InfoUpdated,
            AlreadyUpToDate,
            Failed
        }

        public static async Task<ObjectId> EnsureStoreRow(string name)
        {
            if (_storeId != null) return _storeId.Value;
            
            if (_storesCollection == null) throw new Exception("MongoDB not initialised");
            
            try
            {
                var filter = Builders<BsonDocument>.Filter.Eq("storeId", name.ToLower());
                var existingStore = await _storesCollection.Find(filter).FirstOrDefaultAsync();
                
                if (existingStore != null)
                {
                    _storeId = existingStore["_id"].AsObjectId;
                    return _storeId.Value;
                }

                // Insert new store
                var newStore = new BsonDocument
                {
                    { "storeId", name.ToLower() },
                    { "name", name },
                    { "logoUrl", BsonNull.Value },
                    { "createdAt", DateTime.UtcNow },
                    { "updatedAt", DateTime.UtcNow }
                };
                
                await _storesCollection.InsertOneAsync(newStore);
                _storeId = newStore["_id"].AsObjectId;
                return _storeId.Value;
            }
            catch (Exception ex)
            {
                LogError($"Failed to ensure store row: {ex.Message}");
                throw;
            }
        }

        public static async Task<UpsertResponse> UpsertProduct(Program.Product p, ObjectId storeId)
        {
            if (_consolidatedProductsCollection == null || _priceHistoryCollection == null)
                throw new Exception("MongoDB not initialised");

            try
            {
                var now = DateTime.UtcNow;
                
                // Find or create consolidated product
                var filter = Builders<BsonDocument>.Filter.And(
                    Builders<BsonDocument>.Filter.Eq("variants.storeProductId", p.id),
                    Builders<BsonDocument>.Filter.Eq("variants.storeId", storeId)
                );
                
                var existingProduct = await _consolidatedProductsCollection.Find(filter).FirstOrDefaultAsync();
                bool isNewProduct = false;
                ObjectId consolidatedProductId;
                
                if (existingProduct == null)
                {
                    // Create new consolidated product
                    var normalizedName = NormalizeProductName(p.name, p.size);
                    
                    var newProduct = new BsonDocument
                    {
                        { "normalizedName", normalizedName },
                        { "displayName", p.name },
                        { "primarySize", (BsonValue)(p.size ?? BsonNull.Value) },
                        { "categoryId", BsonNull.Value }, // TODO: Implement category mapping
                        { "brandId", BsonNull.Value }, // TODO: Implement brand extraction
                        { "matchConfidence", 100 },
                        { "manualMatch", false },
                        { "variants", new BsonArray {
                            new BsonDocument {
                                { "storeProductId", p.id },
                                { "storeId", storeId },
                                { "storeName", p.name },
                                { "storeSize", (BsonValue)(p.size ?? BsonNull.Value) },
                                { "storeUnitPrice", p.unitPrice },
                                { "storeUnitName", (BsonValue)(p.unitName ?? BsonNull.Value) },
                                { "lastSeen", now },
                                { "isActive", true },
                                { "imageUrl", BsonNull.Value }
                            }
                        }},
                        { "sizeVariants", !string.IsNullOrEmpty(p.size) ? new BsonArray {
                            new BsonDocument {
                                { "sizeName", p.size },
                                { "sizeWeightGrams", BsonNull.Value },
                                { "sizeVolumeMl", BsonNull.Value },
                                { "isPrimarySize", true }
                            }
                        } : new BsonArray()},
                        { "currentPrices", new BsonArray {
                            new BsonDocument {
                                { "storeId", storeId },
                                { "price", p.currentPrice },
                                { "isSpecial", false },
                                { "wasAvailable", true },
                                { "recordedAt", now }
                            }
                        }},
                        { "createdAt", now },
                        { "updatedAt", now }
                    };
                    
                    await _consolidatedProductsCollection.InsertOneAsync(newProduct);
                    consolidatedProductId = newProduct["_id"].AsObjectId;
                    isNewProduct = true;
                }
                else
                {
                    // Update existing consolidated product
                    consolidatedProductId = existingProduct["_id"].AsObjectId;
                    
                    var variantFilter = Builders<BsonDocument>.Filter.And(
                        Builders<BsonDocument>.Filter.Eq("_id", consolidatedProductId),
                        Builders<BsonDocument>.Filter.ElemMatch("variants", 
                            Builders<BsonDocument>.Filter.Eq("storeProductId", p.id))
                    );
                    
                    var variantUpdate = Builders<BsonDocument>.Update
                        .Set("variants.$.lastSeen", now)
                        .Set("variants.$.storeUnitPrice", p.unitPrice)
                        .Set("variants.$.storeUnitName", (BsonValue)(p.unitName ?? BsonNull.Value))
                        .Set("updatedAt", now);
                    
                    await _consolidatedProductsCollection.UpdateOneAsync(variantFilter, variantUpdate);
                    
                    // Update current price
                    var priceFilter = Builders<BsonDocument>.Filter.And(
                        Builders<BsonDocument>.Filter.Eq("_id", consolidatedProductId),
                        Builders<BsonDocument>.Filter.ElemMatch("currentPrices", 
                            Builders<BsonDocument>.Filter.Eq("storeId", storeId))
                    );
                    
                    var priceUpdate = Builders<BsonDocument>.Update
                        .Set("currentPrices.$.price", p.currentPrice)
                        .Set("currentPrices.$.recordedAt", now);
                    
                    await _consolidatedProductsCollection.UpdateOneAsync(priceFilter, priceUpdate);
                }
                
                // Insert price history record
                var priceHistoryDoc = new BsonDocument
                {
                    { "consolidatedProductId", consolidatedProductId },
                    { "storeId", storeId },
                    { "price", p.currentPrice },
                    { "isSpecial", false },
                    { "wasAvailable", true },
                    { "recordedAt", now },
                    { "year", now.Year },
                    { "month", now.Month }
                };
                
                await _priceHistoryCollection.InsertOneAsync(priceHistoryDoc);
                
                Utilities.Log($"  Upserted: {p.name.Substring(0, Math.Min(45, p.name.Length)).PadRight(45)} | ${p.currentPrice}", ConsoleColor.Green);
                
                return isNewProduct ? UpsertResponse.NewProduct : UpsertResponse.PriceUpdated;
            }
            catch (Exception ex)
            {
                LogError($"MongoDB upsert failed: {ex.Message}");
                return UpsertResponse.Failed;
            }
        }

        // Upload product image to MongoDB GridFS
        public static async Task<bool> UploadImageToGridFS(string imageUrl, Program.Product product, ObjectId storeId)
        {
            if (_gridFS == null || _consolidatedProductsCollection == null)
            {
                LogError("MongoDB GridFS or consolidated products collection not initialised");
                return false;
            }

            try
            {
                // Download image
                using var httpClient = new HttpClient();
                var imageResponse = await httpClient.GetAsync(imageUrl);
                
                if (!imageResponse.IsSuccessStatusCode)
                {
                    LogError($"Failed to download image from {imageUrl}: {imageResponse.StatusCode}");
                    return false;
                }

                var imageBytes = await imageResponse.Content.ReadAsByteArrayAsync();
                
                // Check if image already exists and delete it
                var existingFiles = await _gridFS.FindAsync(
                    Builders<GridFSFileInfo>.Filter.Eq("metadata.productId", product.id));
                
                await existingFiles.ForEachAsync(async file =>
                {
                    await _gridFS.DeleteAsync(file.Id);
                });
                
                // Upload new image
                var filename = $"{product.id}.jpg";
                var metadata = new BsonDocument
                {
                    { "productId", product.id },
                    { "storeId", storeId },
                    { "originalUrl", imageUrl },
                    { "uploadedAt", DateTime.UtcNow }
                };
                
                var options = new GridFSUploadOptions
                {
                    Metadata = metadata
                };
                
                var fileId = await _gridFS.UploadFromBytesAsync(filename, imageBytes, options);
                
                // Update product with image URL reference
                var imageUrlGridFS = $"gridfs://productImages/{fileId}";
                
                var filter = Builders<BsonDocument>.Filter.ElemMatch("variants", 
                    Builders<BsonDocument>.Filter.Eq("storeProductId", product.id));
                var update = Builders<BsonDocument>.Update.Set("variants.$.imageUrl", imageUrlGridFS);
                
                await _consolidatedProductsCollection.UpdateOneAsync(filter, update);
                
                Utilities.Log($"  Image uploaded: {product.id} -> GridFS {fileId}", ConsoleColor.Blue);
                return true;
            }
            catch (Exception ex)
            {
                LogError($"Image upload error: {ex.Message}");
                return false;
            }
        }

        // Helper function to normalize product names for matching
        private static string NormalizeProductName(string name, string? size = null)
        {
            var normalized = name.ToLower()
                .Replace("[^a-z0-9\\s]", " ")
                .Replace("\\s+", "_")
                .Trim();
            
            if (!string.IsNullOrEmpty(size))
            {
                var normalizedSize = size.ToLower()
                    .Replace("[^a-z0-9]", "")
                    .Trim();
                normalized += "_" + normalizedSize;
            }
            
            return normalized;
        }

        // Health check
        public static async Task<bool> HealthCheck()
        {
            try
            {
                if (_database == null) return false;
                await _database.RunCommandAsync<BsonDocument>(new BsonDocument("ping", 1));
                return true;
            }
            catch
            {
                return false;
            }
        }

        // Close connection
        public static void Close()
        {
            // MongoDB driver handles connection pooling automatically
            Utilities.Log("MongoDB connection closed", ConsoleColor.Blue);
        }
    }
}