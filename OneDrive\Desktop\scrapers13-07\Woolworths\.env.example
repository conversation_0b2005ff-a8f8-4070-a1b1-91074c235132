# Woolworths Scraper Configuration
# Copy this file to .env and fill in your values

# MongoDB Configuration (Required for database mode)
MONGODB_CONNECTION_STRING=mongodb://localhost:27017/nz-supermarket-scraper
# For MongoDB Atlas: mongodb+srv://user:<EMAIL>/
MONGODB_DATABASE_NAME=nz-supermarket-scraper

# Optional Configuration
STORE_NAME=Auckland Central

# Instructions:
# 1. Set up MongoDB locally or create a MongoDB Atlas cluster
# 2. Replace the connection string above with your MongoDB connection details
# 3. For local MongoDB: mongodb://localhost:27017/nz-supermarket-scraper
# 4. For MongoDB Atlas: mongodb+srv://username:<EMAIL>/
# 5. Save this file as .env (remove .example)
# 6. Ensure your MongoDB instance is running before starting the scraper