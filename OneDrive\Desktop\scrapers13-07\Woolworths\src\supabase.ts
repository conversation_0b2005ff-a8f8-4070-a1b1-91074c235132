import { createClient, SupabaseClient } from "@supabase/supabase-js";
import * as dotenv from "dotenv";
dotenv.config();
dotenv.config({ path: `.env.local`, override: true });

import { Product, UpsertResponse, ProductResponse, DatedPrice } from "./typings.js";
import { colour, log, logError } from "./utilities.js";

let supabase: SupabaseClient;
let storeId: number | undefined; // Woolworths store row id

export function establishSupabase() {
  const url = process.env.SUPABASE_URL;
  const key = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;
  if (!url || !key) {
    throw Error("SUPABASE_URL or SUPABASE_ANON_KEY not set in env");
  }
  supabase = createClient(url, key, { auth: { persistSession: false } });
}

async function ensureStoreRow(): Promise<number> {
  if (storeId !== undefined) return storeId;
  const { data, error } = await supabase
    .from("stores")
    .select("id")
    .eq("name", "woolworths")
    .single();

  if (error && error.code !== "PGRST116") {
    throw error;
  }

  if (data?.id) {
    storeId = data.id;
    return storeId!;
  }

  // Insert store row if not exists
  const { data: insertData, error: insertErr } = await supabase
    .from("stores")
    .insert({ name: "woolworths" })
    .select("id")
    .single();
  if (insertErr) throw insertErr;
  storeId = insertData!.id;
  return storeId!;
}

export async function upsertProductToSupabase(scraped: Product): Promise<UpsertResponse> {
  if (!supabase) throw Error("Supabase client not initialised");
  const sId = await ensureStoreRow();

  // Upsert into products
  const productPayload: any = {
    id: scraped.id,
    name: scraped.name,
    size: scraped.size,
    unit_price: scraped.unitPrice,
    unit_name: scraped.unitName,
    original_unit_qty: scraped.originalUnitQuantity,
    source_site: scraped.sourceSite,
    last_checked: scraped.lastChecked.toISOString(),
    last_updated: scraped.lastUpdated.toISOString()
  };

  const { error: prodErr } = await supabase.from("products").upsert(productPayload, { onConflict: "id" });
  if (prodErr) {
    logError("Supabase upsert product failed: " + prodErr.message);
    return UpsertResponse.Failed;
  }

  // Insert price row (always) – logic to omit duplicates can be added later
  const pricePayload = {
    product_id: scraped.id,
    store_id: sId,
    price: scraped.currentPrice,
    recorded_at: scraped.lastUpdated.toISOString()
  };
  const { error: priceErr } = await supabase.from("prices").insert(pricePayload);
  if (priceErr) {
    logError("Supabase insert price failed: " + priceErr.message);
    return UpsertResponse.Failed;
  }

  log(colour.green, `  Upserted: ${scraped.name.slice(0, 45).padEnd(45)} | $${scraped.currentPrice}`);
  return UpsertResponse.PriceChanged; // coarse; can refine later
}

// Upload product image to Supabase Storage
export async function uploadImageToSupabase(imageUrl: string, product: Product): Promise<boolean> {
  if (!supabase) throw Error("Supabase client not initialised");

  try {
    // Download image from Woolworths
    const imageResponse = await fetch(imageUrl);
    if (!imageResponse.ok) {
      const statusCode = imageResponse.status;
      const statusText = imageResponse.statusText;

      if (statusCode === 403) {
        logError(`HTTP 403 Forbidden - Access denied for image: ${imageUrl}`);
      } else if (statusCode === 404) {
        logError(`HTTP 404 Not Found - Image not found: ${imageUrl}`);
      } else if (statusCode === 429) {
        logError(`HTTP 429 Too Many Requests - Rate limited for image: ${imageUrl}`);
      } else if (statusCode >= 500) {
        logError(`HTTP ${statusCode} Server Error - Server issue for image: ${imageUrl}`);
      } else {
        logError(`Failed to download image from ${imageUrl}: ${statusCode} ${statusText}`);
      }
      return false;
    }

    const imageBuffer = await imageResponse.arrayBuffer();
    const imageBlob = new Blob([imageBuffer], { type: 'image/jpeg' });
    
    // Generate file path: products/{productId}.jpg
    const filePath = `products/${product.id}.jpg`;
    
    // Upload to Supabase Storage
    const { data, error } = await supabase.storage
      .from('product-images')
      .upload(filePath, imageBlob, {
        contentType: 'image/jpeg',
        upsert: true // Overwrite if exists
      });

    if (error) {
      logError(`Supabase storage upload failed: ${error.message}`);
      return false;
    }

    // Get public URL for the uploaded image
    const { data: publicUrlData } = supabase.storage
      .from('product-images')
      .getPublicUrl(filePath);

    log(colour.blue, `  Image uploaded: ${product.id} -> ${publicUrlData.publicUrl}`);
    return true;

  } catch (err: any) {
    logError(`Image upload error: ${err.message}`);
    return false;
  }
} 