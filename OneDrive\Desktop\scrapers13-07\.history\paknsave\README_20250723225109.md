# PakNSave New Zealand Scraper

A .NET/C# web scraper for collecting product data from PakNSave New Zealand (paknsave.co.nz) with advanced cross-store product matching and MongoDB integration.

## 🎯 **Overview**

This scraper collects comprehensive product information including:
- **Product Data**: Names, prices, sizes, unit pricing (per kg, per L, etc.)
- **Categories**: Product categories and subcategories
- **Images**: Product images stored in MongoDB GridFS
- **Cross-Store Matching**: Advanced fuzzy matching with 152 NZ brand mappings
- **Price History**: Historical price tracking for trend analysis

## 🚀 **Quick Start**

### **Prerequisites**
- **.NET 6 SDK** or higher - [Download](https://dotnet.microsoft.com/download)
- **MongoDB** (running on localhost:27017) - Use Docker: `docker run -d -p 27017:27017 --name mongodb mongo:latest`
- **Internet connection** for accessing PakNSave website

### **Installation**

1. **Navigate to PakNSave directory:**
   ```bash
   cd paknsave/src
   ```

2. **Restore dependencies:**
   ```bash
   dotnet restore
   ```

3. **Build the project:**
   ```bash
   dotnet build
   ```

4. **Configure environment** (optional):
   ```bash
   # Create .env file in paknsave directory
   echo "MONGODB_CONNECTION_STRING=mongodb://localhost:27017/nz-supermarket-scraper" > ../.env
   echo "STORE_LOCATION=Auckland" >> ../.env
   ```

If using Supabase, set the PostgreSQL connection string using the format:

```json
{
  "POSTGRES_CONNECTION": "Host=db.your-project.supabase.co;Port=5432;Database=postgres;Username=postgres;Password=your-password"
}
```

To override the default store location with a specific location, set geolocation co-ordinates in Long/Lat format. Long/Lat co-ordinates can be obtained from resources such as google maps.
The closest store location to the co-ordinates will be selected when running the scraper.

```json
{
    "GEOLOCATION_LONG": "174.91",
    "GEOLOCATION_LAT": "-41.21",
}
```

## Command-Line Usage

To dry run the scraper, logging each product to the console:

```powershell
dotnet run
```

To run the scraper with both logging and storing of each product to the database:

```powershell
dotnet run db
```

## Sample Dry Run Output

```cmd
       ID | Name                                    | Size   | Price  | Unit Price
----------------------------------------------------------------------------------
 P1234567 | Coconut Supreme Slice                   | 350g   | $ 5.89 | $16.83 /kg
 P5345284 | Gluten Free Delicious Choc Chip Cookie  | 250g   | $ 4.89 | $19.56 /kg
 P5678287 | Gluten Free Delicious Macadamia Cookie  | 250g   | $ 4.89 | $19.56 /kg
 P3457825 | Belgium Slice                           | Each   | $ 5.89 | 
 P5789285 | Gluten Free Delicious Double Choc Chip  | 250g   | $ 4.89 | $19.56 /kg
 P2356288 | Bakery Crunchy Bran With Sultanas       | 230g   | $ 4.49 | $19.52 /kg
 P2765307 | Sanniu Evergreen Variant Biscuits       | 4x132g | $ 6.36 | $12.05 /kg
```

## Sample Product Stored in Supabase

This sample was re-run on multiple days to capture changing prices.

```json
{
    "id": "P1234567",
    "name": "Coconut Supreme Slice",
    "size": "350g",
    "currentPrice": 5.89,
    "category": [
        "biscuits"
    ],
    "priceHistory": [
        {
            "date": "2023-05-04T01:00:00",
            "price": 5.89
        }
        {
            "date": "2023-01-02T01:00:00",
            "price": 5.49
        }
    ],
    "unitPrice": 16.83,
    "unitName": "kg",
}
```
