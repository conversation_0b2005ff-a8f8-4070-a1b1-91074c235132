const { CosmosClient } = require("@azure/cosmos");
const fs = require("fs").promises;
const path = require("path");

// Configuration - update these values
const COSMOS_ENDPOINT = process.env.COSMOS_ENDPOINT || "YOUR_COSMOS_ENDPOINT";
const COSMOS_KEY = process.env.COSMOS_KEY || "YOUR_COSMOS_KEY";
const DATABASE_ID = "supermarket-prices";
const CONTAINER_ID = "products";
const OUTPUT_DIR = "./cosmos-export";

async function exportCosmosData() {
  console.log("Starting CosmosDB export...");
  
  try {
    // Create output directory
    await fs.mkdir(OUTPUT_DIR, { recursive: true });
    
    // Initialize Cosmos client
    const client = new CosmosClient({
      endpoint: COSMOS_ENDPOINT,
      key: COSMOS_KEY
    });
    
    const database = client.database(DATABASE_ID);
    const container = database.container(CONTAINER_ID);
    
    // Query all items
    const querySpec = {
      query: "SELECT * FROM c"
    };
    
    const { resources: items } = await container.items
      .query(querySpec)
      .fetchAll();
    
    console.log(`Found ${items.length} items to export`);
    
    // Group by source site
    const grouped = {};
    items.forEach(item => {
      const site = item.sourceSite || "unknown";
      if (!grouped[site]) grouped[site] = [];
      grouped[site].push(item);
    });
    
    // Save to separate files
    for (const [site, products] of Object.entries(grouped)) {
      const filename = path.join(OUTPUT_DIR, `products_${site.replace(/\./g, "_")}.json`);
      await fs.writeFile(filename, JSON.stringify(products, null, 2));
      console.log(`Exported ${products.length} products from ${site} to ${filename}`);
    }
    
    // Also save all products in one file
    await fs.writeFile(
      path.join(OUTPUT_DIR, "products_all.json"),
      JSON.stringify(items, null, 2)
    );
    
    console.log(`Export complete! Total products: ${items.length}`);
    
  } catch (error) {
    console.error("Export failed:", error);
    process.exit(1);
  }
}

// Run export
exportCosmosData(); 