/**
 * Advanced Product Matching Algorithm for Cross-Store Product Identification
 *
 * This module provides functions to match products across different supermarket stores
 * by comparing product names, sizes, brands, and other attributes using:
 * - String similarity algorithms (Levenshtein distance, Jaccard similarity)
 * - Manual mapping rules for common product variations
 * - Alias lookup tables for known product variations
 * - Confidence scoring and manual override support
 */

import { createClient } from '@supabase/supabase-js';

// Supabase client (should be initialized with your credentials)
let supabase = null;

export function initializeSupabase(url, key) {
    supabase = createClient(url, key);
}

// Manual mapping rules for common product variations
const manualMappings = {
    // Coca Cola variations
    'coke zero': ['coca cola zero', 'coke zero sugar', 'coca cola zero sugar', 'coke no sugar'],
    'coke': ['coca cola', 'coca cola classic', 'coke classic', 'coca cola original'],
    'diet coke': ['coca cola diet', 'diet coca cola', 'coke diet'],

    // Pepsi variations
    'pepsi': ['pepsi cola', 'pepsi original', 'pepsi classic'],
    'pepsi max': ['pepsi maximum taste', 'pepsi max no sugar'],

    // Bread variations
    'tip top bread': ['tiptop bread', 'tip top', 'tiptop'],
    'molenberg bread': ['molenberg', 'molenburg bread', 'molenberg wholemeal'],
    'vogels bread': ['vogel bread', 'vogels original', 'vogel original'],

    // Milk variations
    'anchor milk': ['anchor blue milk', 'anchor standard milk', 'anchor whole milk'],
    'meadowfresh milk': ['meadow fresh milk', 'meadowfresh standard', 'meadow fresh standard'],
    'lewis road milk': ['lewis road creamery milk', 'lewis road creamery'],

    // Meat variations
    'chicken breast': ['chicken breast fillets', 'chicken breast fillet', 'free range chicken breast'],
    'beef mince': ['beef mince premium', 'premium beef mince', 'lean beef mince'],
    'lamb leg': ['lamb leg roast', 'leg of lamb', 'lamb leg bone in'],

    // Vegetable variations
    'potatoes': ['potato', 'agria potatoes', 'red potatoes', 'white potatoes', 'new potatoes'],
    'onions': ['onion', 'brown onions', 'red onions', 'white onions', 'spanish onions'],
    'carrots': ['carrot', 'baby carrots', 'loose carrots', 'bagged carrots'],

    // Fruit variations
    'apples': ['apple', 'royal gala apples', 'braeburn apples', 'granny smith apples'],
    'bananas': ['banana', 'cavendish bananas', 'organic bananas'],
    'oranges': ['orange', 'navel oranges', 'valencia oranges'],

    // Dairy variations
    'butter': ['salted butter', 'unsalted butter', 'spreadable butter'],
    'cheese': ['tasty cheese', 'mild cheese', 'vintage cheese'],
    'yoghurt': ['yogurt', 'greek yoghurt', 'natural yoghurt'],

    // Cereal variations
    'cornflakes': ['corn flakes', 'kelloggs cornflakes', 'kellogg cornflakes'],
    'weetbix': ['weet bix', 'sanitarium weetbix', 'wheat biscuits'],

    // Cleaning variations
    'dishwashing liquid': ['dish liquid', 'dishwash liquid', 'washing up liquid'],
    'laundry powder': ['washing powder', 'laundry detergent powder'],
    'toilet paper': ['toilet tissue', 'bathroom tissue', 'loo paper']
};

// Get manual mapping for a product name
function getManualMapping(normalizedName) {
    for (const [canonical, variations] of Object.entries(manualMappings)) {
        // Check if normalized name matches canonical form
        if (normalizedName.includes(canonical) || canonical.includes(normalizedName)) {
            return canonical;
        }

        // Check if normalized name matches any variation
        for (const variation of variations) {
            if (normalizedName.includes(variation) || variation.includes(normalizedName)) {
                return canonical;
            }
        }
    }
    return null;
}

/**
 * Normalize product name for matching with enhanced manual mapping support
 * Removes brand names, common descriptors, and standardizes format
 */
export function normalizeProductName(name, brandName = '', size = null) {
    if (!name) return '';

    let normalized = name.toLowerCase().trim();

    // Remove brand name if provided
    if (brandName) {
        const brandRegex = new RegExp(brandName.toLowerCase(), 'gi');
        normalized = normalized.replace(brandRegex, '').trim();
    }

    // Remove common store-specific descriptors
    const descriptorsToRemove = [
        'countdown', 'woolworths', 'new world', 'paknsave', 'pak n save',
        'select', 'premium', 'value', 'budget', 'signature', 'essentials',
        'organic', 'free range', 'grass fed', 'natural', 'home brand',
        'pams', 'homebrand', 'signature range', 'fresh choice'
    ];

    for (const descriptor of descriptorsToRemove) {
        const regex = new RegExp(`\\b${descriptor}\\b`, 'gi');
        normalized = normalized.replace(regex, '').trim();
    }

    // Apply common brand substitutions for consistency
    const brandSubstitutions = {
        'coca cola': 'coke',
        'coca-cola': 'coke',
        'pepsi cola': 'pepsi',
        'cadbury': 'cadburys',
        'mccains': 'mccain',
        'watties': 'wattie',
        'sanitarium': 'sanitarium',
        'mainland': 'mainland',
        'anchor': 'anchor',
        'meadow fresh': 'meadowfresh',
        'lewis road creamery': 'lewis road',
        'tegel': 'tegel',
        'hellers': 'heller'
    };

    // Apply brand substitutions
    for (const [original, replacement] of Object.entries(brandSubstitutions)) {
        const regex = new RegExp(`\\b${original}\\b`, 'gi');
        normalized = normalized.replace(regex, replacement);
    }

    // Remove extra whitespace and special characters
    normalized = normalized
        .replace(/[^\w\s]/g, ' ')          // Replace special chars with spaces
        .replace(/\s+/g, ' ')             // Collapse multiple spaces
        .replace(/\b(the|and|or|with|in|of|for|by|per)\b/g, '') // Remove common words
        .trim();

    // Check for manual mapping
    const manualMapping = getManualMapping(normalized);
    if (manualMapping) {
        normalized = manualMapping;
    }

    // Include size information if available for better matching
    if (size) {
        const normalizedSize = normalizeSize(size);
        if (normalizedSize) {
            normalized += ` ${normalizedSize}`;
        }
    }

    return normalized;
}

/**
 * Normalize size information for better matching
 */
function normalizeSize(size) {
    if (!size) return '';

    return size.toLowerCase()
        .replace(/[^\w\d]/g, '')  // Remove special characters
        .replace(/\s+/g, '')      // Remove spaces
        .replace(/grams?/g, 'g')  // Standardize grams
        .replace(/kilograms?/g, 'kg') // Standardize kilograms
        .replace(/litres?/g, 'l')     // Standardize litres
        .replace(/millilitres?/g, 'ml') // Standardize millilitres
        .replace(/pieces?/g, 'pc')    // Standardize pieces
        .replace(/each/g, 'ea');      // Standardize each
}

/**
 * Normalize size format for comparison
 */
export function normalizeSize(size) {
    if (!size) return '';
    
    let normalized = size.toLowerCase().trim();
    
    // Standardize units
    normalized = normalized
        .replace(/\bmls?\b/g, 'ml')
        .replace(/\blitres?\b/g, 'l')
        .replace(/\bliters?\b/g, 'l')
        .replace(/\bgrams?\b/g, 'g')
        .replace(/\bkilos?\b/g, 'kg')
        .replace(/\bkilograms?\b/g, 'kg')
        .replace(/\bpacks?\b/g, 'pk')
        .replace(/\beach\b/g, 'ea')
        .replace(/\bper\s+kg\b/g, 'per kg')
        .replace(/\s+/g, '');
        
    return normalized;
}

/**
 * Convert size to comparable numeric value for sorting/grouping
 */
export function sizeToComparableValue(size) {
    if (!size) return 0;
    
    const normalized = normalizeSize(size);
    const match = normalized.match(/^(\d+(?:\.\d+)?)(ml|l|g|kg|pk|ea)?/);
    
    if (!match) return 0;
    
    const value = parseFloat(match[1]);
    const unit = match[2] || '';
    
    // Convert to base units for comparison
    switch (unit) {
        case 'l': return value * 1000; // Convert to ml
        case 'kg': return value * 1000; // Convert to g
        case 'pk':
        case 'ea': return value;
        case 'ml':
        case 'g':
        default: return value;
    }
}

/**
 * Calculate Jaccard similarity between two strings based on word sets
 */
function jaccardSimilarity(str1, str2) {
    const set1 = new Set(str1.toLowerCase().split(/\s+/).filter(w => w.length > 1));
    const set2 = new Set(str2.toLowerCase().split(/\s+/).filter(w => w.length > 1));

    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);

    return union.size === 0 ? 0 : intersection.size / union.size;
}

/**
 * Enhanced similarity calculation combining multiple algorithms
 */
export function calculateNameSimilarity(name1, name2, brand1 = '', brand2 = '') {
    const normalized1 = normalizeProductName(name1, brand1);
    const normalized2 = normalizeProductName(name2, brand2);

    if (normalized1 === normalized2) return 100;
    if (!normalized1 || !normalized2) return 0;

    // Check for manual mapping match first
    const mapping1 = getManualMapping(normalized1);
    const mapping2 = getManualMapping(normalized2);
    if (mapping1 && mapping2 && mapping1 === mapping2) return 95;

    // Calculate Levenshtein similarity
    const distance = levenshteinDistance(normalized1, normalized2);
    const maxLength = Math.max(normalized1.length, normalized2.length);
    const levenshteinSim = maxLength === 0 ? 1 : Math.max(0, 1 - distance / maxLength);

    // Calculate Jaccard similarity
    const jaccardSim = jaccardSimilarity(normalized1, normalized2);

    // Weighted combination: 60% Levenshtein, 40% Jaccard
    let combinedSimilarity = (levenshteinSim * 0.6) + (jaccardSim * 0.4);

    // Boost score for exact word matches
    const words1 = normalized1.split(' ').filter(w => w.length > 2);
    const words2 = normalized2.split(' ').filter(w => w.length > 2);
    const commonWords = words1.filter(w => words2.includes(w));

    if (commonWords.length > 0) {
        const wordBoost = (commonWords.length / Math.max(words1.length, words2.length)) * 0.2;
        combinedSimilarity = Math.min(1, combinedSimilarity + wordBoost);
    }

    // Penalty for significant length differences
    const lengthRatio = Math.min(normalized1.length, normalized2.length) /
                       Math.max(normalized1.length, normalized2.length);
    if (lengthRatio < 0.5) {
        combinedSimilarity *= 0.8;
    }

    return Math.round(combinedSimilarity * 100);
}

/**
 * Calculate Levenshtein distance between two strings
 */
function levenshteinDistance(str1, str2) {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
        matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
        matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
        for (let j = 1; j <= str1.length; j++) {
            if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                matrix[i][j] = matrix[i - 1][j - 1];
            } else {
                matrix[i][j] = Math.min(
                    matrix[i - 1][j - 1] + 1,
                    matrix[i][j - 1] + 1,
                    matrix[i - 1][j] + 1
                );
            }
        }
    }
    
    return matrix[str2.length][str1.length];
}

/**
 * Calculate overall product match score
 */
export function calculateProductMatchScore(product1, product2) {
    const nameScore = calculateNameSimilarity(product1.name, product2.name);
    
    // Size similarity (exact match = 30 points, similar = 15 points)
    let sizeScore = 0;
    if (product1.size && product2.size) {
        const normalizedSize1 = normalizeSize(product1.size);
        const normalizedSize2 = normalizeSize(product2.size);
        
        if (normalizedSize1 === normalizedSize2) {
            sizeScore = 30;
        } else {
            const value1 = sizeToComparableValue(product1.size);
            const value2 = sizeToComparableValue(product2.size);
            const sizeDiff = Math.abs(value1 - value2) / Math.max(value1, value2);
            if (sizeDiff < 0.1) sizeScore = 20; // Very similar sizes
            else if (sizeDiff < 0.3) sizeScore = 10; // Somewhat similar sizes
        }
    }
    
    // Brand similarity (if available)
    let brandScore = 0;
    if (product1.brand && product2.brand) {
        if (product1.brand.toLowerCase() === product2.brand.toLowerCase()) {
            brandScore = 20;
        }
    }
    
    // Calculate weighted total (name is most important)
    const totalScore = (nameScore * 0.5) + (sizeScore * 0.3) + (brandScore * 0.2);
    
    return Math.round(totalScore);
}

/**
 * Find potential matches for a product in the database with enhanced alias support
 */
export async function findPotentialMatches(product, minScore = 70) {
    if (!supabase) {
        throw new Error('Supabase client not initialized');
    }

    const normalizedName = normalizeProductName(product.name, product.brand, product.size);
    console.log(`🔍 Finding matches for: "${product.name}" -> normalized: "${normalizedName}"`);

    // First, try exact match with normalized name
    const { data: exactMatch } = await supabase
        .from('consolidated_products')
        .select(`
            id,
            display_name,
            normalized_name,
            primary_size,
            aliases,
            brand_id,
            brands(name),
            match_confidence
        `)
        .eq('normalized_name', normalizedName)
        .single();

    if (exactMatch) {
        console.log(`✅ Exact match found: ${exactMatch.display_name}`);
        return [{
            consolidatedProductId: exactMatch.id,
            displayName: exactMatch.display_name,
            score: 100,
            confidence: 'high',
            matchType: 'exact'
        }];
    }

    // Get all products for fuzzy matching (with reasonable limit)
    const { data: candidates, error } = await supabase
        .from('consolidated_products')
        .select(`
            id,
            display_name,
            normalized_name,
            primary_size,
            aliases,
            brand_id,
            brands(name),
            match_confidence
        `)
        .limit(1000);

    if (error) {
        console.error('Error searching for product matches:', error);
        return [];
    }

    // Score each candidate including aliases
    const matches = [];
    for (const candidate of candidates || []) {
        let bestScore = 0;
        let matchType = 'fuzzy';

        // Check against normalized name
        const nameScore = calculateNameSimilarity(
            normalizedName,
            candidate.normalized_name,
            product.brand,
            candidate.brands?.name
        );
        bestScore = Math.max(bestScore, nameScore);

        // Check against aliases if they exist
        if (candidate.aliases && Array.isArray(candidate.aliases)) {
            for (const alias of candidate.aliases) {
                const aliasScore = calculateNameSimilarity(normalizedName, alias);
                if (aliasScore > bestScore) {
                    bestScore = aliasScore;
                    matchType = 'alias';
                }
            }
        }

        // Size similarity bonus
        if (product.size && candidate.primary_size) {
            const sizeScore = calculateSizeSimilarity(product.size, candidate.primary_size);
            if (sizeScore > 80) {
                bestScore += 5; // Small bonus for size match
            }
        }

        if (bestScore >= minScore) {
            matches.push({
                consolidatedProductId: candidate.id,
                displayName: candidate.display_name,
                normalizedName: candidate.normalized_name,
                score: Math.min(100, bestScore),
                confidence: bestScore >= 90 ? 'high' : bestScore >= 80 ? 'medium' : 'low',
                matchType: matchType,
                existingConfidence: candidate.match_confidence
            });
        }
    }

    // Sort by score descending
    const sortedMatches = matches.sort((a, b) => b.score - a.score);

    if (sortedMatches.length > 0) {
        console.log(`✅ Found ${sortedMatches.length} potential matches, best: ${sortedMatches[0].displayName} (${sortedMatches[0].score})`);
    } else {
        console.log(`❌ No matches found above threshold ${minScore}`);
    }

    return sortedMatches;
}

/**
 * Create or link a product to consolidated products
 */
export async function consolidateProduct(storeProduct, storeId) {
    if (!supabase) {
        throw new Error('Supabase client not initialized');
    }
    
    // Find potential matches
    const matches = await findPotentialMatches(storeProduct, 70);
    
    let consolidatedProductId;
    
    if (matches.length > 0 && matches[0].score >= 85) {
        // High confidence match - use existing consolidated product
        consolidatedProductId = matches[0].consolidatedProductId;
        console.log(`Linked ${storeProduct.name} to existing consolidated product (score: ${matches[0].score})`);
    } else {
        // Create new consolidated product
        const normalizedName = normalizeProductName(storeProduct.name);
        
        // Determine category from store product category
        const categoryId = await mapStoreCategory(storeProduct.category);
        
        const { data: newProduct, error } = await supabase
            .from('consolidated_products')
            .insert({
                normalized_name: normalizedName,
                display_name: storeProduct.name,
                primary_size: storeProduct.size,
                category_id: categoryId,
                match_confidence: matches.length > 0 ? matches[0].score : 100
            })
            .select()
            .single();
            
        if (error) {
            console.error('Error creating consolidated product:', error);
            return null;
        }
        
        consolidatedProductId = newProduct.id;
        console.log(`Created new consolidated product for ${storeProduct.name}`);
    }
    
    // Link store product to consolidated product
    await linkStoreProduct(storeProduct, storeId, consolidatedProductId);
    
    return consolidatedProductId;
}

/**
 * Link store-specific product to consolidated product
 */
async function linkStoreProduct(storeProduct, storeId, consolidatedProductId) {
    const { error } = await supabase
        .from('product_variants')
        .upsert({
            consolidated_product_id: consolidatedProductId,
            store_product_id: storeProduct.id,
            store_id: storeId,
            store_name: storeProduct.name,
            store_size: storeProduct.size,
            store_unit_price: storeProduct.unitPrice,
            store_unit_name: storeProduct.unitName,
            last_seen: new Date().toISOString(),
            is_active: true
        }, {
            onConflict: 'store_product_id,store_id'
        });

    if (error) {
        console.error('❌ Error linking store product:', error);
    }
}

/**
 * Add alias to consolidated product if it doesn't already exist
 */
async function addProductAlias(consolidatedProductId, newAlias) {
    if (!newAlias || newAlias.trim() === '') return;

    const normalizedAlias = normalizeProductName(newAlias);

    // Get current product data
    const { data: product, error: fetchError } = await supabase
        .from('consolidated_products')
        .select('aliases, normalized_name')
        .eq('id', consolidatedProductId)
        .single();

    if (fetchError) {
        console.error('❌ Error fetching product for alias update:', fetchError);
        return;
    }

    // Don't add if it's the same as normalized name
    if (normalizedAlias === product.normalized_name) return;

    // Get current aliases or initialize empty array
    const currentAliases = product.aliases || [];

    // Don't add if alias already exists
    if (currentAliases.includes(normalizedAlias)) return;

    // Add new alias
    const updatedAliases = [...currentAliases, normalizedAlias];

    const { error: updateError } = await supabase
        .from('consolidated_products')
        .update({
            aliases: updatedAliases,
            updated_at: new Date().toISOString()
        })
        .eq('id', consolidatedProductId);

    if (updateError) {
        console.error('❌ Error updating product aliases:', updateError);
    } else {
        console.log(`📝 Added alias "${normalizedAlias}" to product ${consolidatedProductId}`);
    }
}

/**
 * Map store categories to unified category hierarchy
 */
async function mapStoreCategory(storeCategories) {
    if (!storeCategories || storeCategories.length === 0) return null;
    
    const categoryMappings = {
        'fruit-veg': 'Fruit & Vegetables',
        'fresh-foods-and-bakery': 'Fresh Foods',
        'meat-poultry': 'Meat & Poultry', 
        'fish-seafood': 'Fish & Seafood',
        'bakery': 'Bakery',
        'fridge-deli': 'Dairy & Deli',
        'chilled-frozen-and-desserts': 'Chilled & Frozen',
        'frozen': 'Frozen Foods',
        'pantry': 'Pantry & Dry Goods',
        'drinks': 'Cold Drinks',
        'hot-and-cold-drinks': 'Beverages',
        'beer-wine': 'Beer, Wine & Cider',
        'beer-wine-and-cider': 'Beer, Wine & Cider',
        'health-body': 'Health & Body',
        'health-and-body': 'Health & Body',
        'household': 'Household & Cleaning',
        'household-and-cleaning': 'Household & Cleaning',
        'baby-child': 'Baby & Toddler',
        'baby-and-toddler': 'Baby & Toddler',
        'pet': 'Pet Supplies',
        'pets': 'Pet Supplies'
    };
    
    // Try to map first category
    const firstCategory = storeCategories[0].toLowerCase().replace(/\s+/g, '-');
    const mappedCategory = categoryMappings[firstCategory];
    
    if (mappedCategory) {
        const { data } = await supabase
            .from('category_hierarchy')
            .select('id')
            .eq('name', mappedCategory)
            .single();
            
        return data?.id;
    }
    
    return null;
}

/**
 * Store consolidated price data
 */
export async function storeConsolidatedPrice(consolidatedProductId, storeId, price, isSpecial = false) {
    if (!supabase) {
        throw new Error('Supabase client not initialized');
    }
    
    const { error } = await supabase
        .from('consolidated_prices')
        .insert({
            consolidated_product_id: consolidatedProductId,
            store_id: storeId,
            price: price,
            is_special: isSpecial,
            was_available: true,
            recorded_at: new Date().toISOString()
        });
        
    if (error) {
        console.error('Error storing consolidated price:', error);
    }
}