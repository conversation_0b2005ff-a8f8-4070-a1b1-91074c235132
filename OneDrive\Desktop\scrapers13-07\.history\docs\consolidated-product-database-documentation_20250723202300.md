# New Zealand Supermarket Scrapers - Consolidated Product Database Documentation

## Table of Contents
1. [Database Schema Documentation](#database-schema-documentation)
2. [Product Matching Algorithm Specification](#product-matching-algorithm-specification)
3. [New Zealand Brand Mappings Reference](#new-zealand-brand-mappings-reference)
4. [API Integration Guide](#api-integration-guide)
5. [Data Structure Examples](#data-structure-examples)
6. [Migration Notes](#migration-notes)

---

## Database Schema Documentation

### MongoDB Collections Overview

The consolidated product database consists of three main collections:

- **`consolidated_products`**: Master product catalog with normalized data
- **`price_history`**: Historical price tracking across all stores
- **`productImages`** (GridFS): Product images stored as binary data

### Consolidated Products Collection Schema

```javascript
{
  _id: ObjectId,                    // MongoDB unique identifier
  normalizedName: String,           // Normalized product name for matching (required, indexed)
  displayName: String,              // Human-readable product name (required)
  primarySize: String,              // Primary size/quantity (optional)
  categoryId: ObjectId,             // Reference to product category (optional)
  brandId: ObjectId,                // Reference to brand (optional)
  aliases: [String],                // Array of alternative product names (indexed)
  matchConfidence: Number,          // Confidence score (0-100) for automatic matching
  manualMatch: Boolean,             // Whether this was manually verified
  
  // Store-specific variants
  variants: [{
    storeProductId: String,         // Original store product ID (required)
    storeId: Number,                // Store identifier (1=Woolworths, 2=NewWorld, 3=PakNSave)
    storeName: String,              // Store-specific product name
    storeSize: String,              // Store-specific size information
    storeUnitPrice: Number,         // Price per unit ($/kg, $/L, etc.)
    storeUnitName: String,          // Unit name (kg, L, each, etc.)
    lastSeen: Date,                 // Last time this variant was scraped
    isActive: Boolean,              // Whether product is currently available
    imageUrl: String                // GridFS reference or external URL
  }],
  
  // Size variations
  sizeVariants: [{
    sizeName: String,               // Size description (e.g., "500g", "1L")
    sizeWeightGrams: Number,        // Weight in grams (if applicable)
    sizeVolumeMl: Number,           // Volume in milliliters (if applicable)
    isPrimarySize: Boolean          // Whether this is the main size
  }],
  
  // Current pricing across stores
  currentPrices: [{
    storeId: Number,                // Store identifier
    price: Number,                  // Current price in NZD
    isSpecial: Boolean,             // Whether on special/sale
    wasAvailable: Boolean,          // Whether in stock during last scrape
    recordedAt: Date                // When this price was recorded
  }],
  
  // Metadata
  createdAt: Date,                  // When product was first added
  updatedAt: Date                   // Last modification timestamp
}
```

### Price History Collection Schema

```javascript
{
  _id: ObjectId,
  consolidatedProductId: ObjectId,  // Reference to consolidated product (required, indexed)
  storeId: Number,                  // Store identifier (required, indexed)
  price: Number,                    // Price in NZD (required)
  timestamp: Date,                  // When price was recorded (required, indexed)
  year: Number,                     // Year for efficient querying (indexed)
  month: Number,                    // Month for efficient querying (indexed)
  isSpecial: Boolean,               // Whether product was on special
  wasAvailable: Boolean             // Whether product was in stock
}
```

### GridFS Product Images Schema

```javascript
// GridFS files collection
{
  _id: ObjectId,
  filename: String,                 // Format: "product_{storeProductId}_{storeId}.jpg"
  uploadDate: Date,
  length: Number,                   // File size in bytes
  chunkSize: Number,                // GridFS chunk size
  md5: String,                      // File hash for integrity
  metadata: {
    productId: String,              // Store product ID
    storeId: Number,                // Store identifier
    originalUrl: String,            // Original image URL
    uploadedAt: Date,               // Upload timestamp
    contentType: String             // MIME type (usually "image/jpeg")
  }
}
```

### Database Indexes

```javascript
// Consolidated Products Indexes
db.consolidated_products.createIndex({ "normalizedName": 1 })
db.consolidated_products.createIndex({ "aliases": 1 })
db.consolidated_products.createIndex({ "variants.storeProductId": 1, "variants.storeId": 1 })
db.consolidated_products.createIndex({ "createdAt": 1 })
db.consolidated_products.createIndex({ "updatedAt": 1 })

// Price History Indexes
db.price_history.createIndex({ "consolidatedProductId": 1, "timestamp": -1 })
db.price_history.createIndex({ "storeId": 1, "timestamp": -1 })
db.price_history.createIndex({ "year": 1, "month": 1 })
db.price_history.createIndex({ "timestamp": -1 })
```

---

## Product Matching Algorithm Specification

### Overview

The enhanced product matching system uses a multi-stage approach to consolidate products across different supermarket stores with high accuracy (90%+).

### Matching Pipeline

```
1. Input Product → 2. Normalize Name → 3. Check Exact Matches → 4. Fuzzy Search → 5. Confidence Scoring → 6. Decision
```

### Stage 1: Product Name Normalization

#### Brand Mapping Application
- **Input**: Raw product name (e.g., "Coca-Cola Classic 330ml")
- **Process**: Apply comprehensive NZ brand mappings
- **Output**: Canonical brand name (e.g., "coke classic 330ml")

#### Store Descriptor Removal
```javascript
const storeDescriptors = [
  'woolworths', 'countdown', 'new world', 'paknsave', 'pak n save',
  'select', 'premium', 'value', 'budget', 'signature', 'essentials',
  'pams', 'homebrand', 'macro', 'organic', 'free range', 'natural'
];
```

#### Size Normalization
```javascript
const sizeNormalizations = {
  'grams': 'g', 'gram': 'g', 'kilograms': 'kg', 'kilogram': 'kg',
  'litres': 'l', 'litre': 'l', 'millilitres': 'ml', 'millilitre': 'ml',
  'pieces': 'pc', 'piece': 'pc', 'each': 'ea', 'pack': 'pk'
};
```

### Stage 2: String Similarity Algorithms

#### Levenshtein Distance
- **Purpose**: Character-level similarity measurement
- **Weight**: 60% of final similarity score
- **Formula**: `similarity = 1 - (distance / maxLength)`

#### Jaccard Similarity
- **Purpose**: Word-set based semantic matching
- **Weight**: 40% of final similarity score
- **Formula**: `similarity = intersection_size / union_size`

#### Combined Similarity Score
```javascript
finalScore = (levenshteinSimilarity * 0.6) + (jaccardSimilarity * 0.4)
```

### Stage 3: Confidence Scoring & Thresholds

| Match Type | Confidence Score | Action |
|------------|------------------|---------|
| Exact Match | 100% | Automatic consolidation |
| Manual Mapping | 95% | Automatic consolidation |
| High Confidence | 85-94% | Automatic consolidation + alias addition |
| Medium Confidence | 70-84% | Create new product, log potential matches |
| Low Confidence | <70% | Create new product |

### Stage 4: Alias Management System

#### Automatic Alias Addition
- When products match with 85%+ confidence but different names
- Prevents duplicate aliases (checks existing aliases array)
- Normalizes alias before adding to prevent redundancy

#### Alias Search Priority
1. **Exact normalized name match** (highest priority)
2. **Manual mapping match** (high priority)
3. **Alias array search** (medium priority)
4. **Fuzzy similarity search** (lowest priority)

### Algorithm Implementation Examples

#### C# (.NET) Implementation
```csharp
private static async Task<BsonDocument> FindBestMatch(string normalizedName, string size, string originalName)
{
    // 1. Try exact match
    var exactMatch = await _consolidatedProductsCollection
        .Find(Builders<BsonDocument>.Filter.Eq("normalizedName", normalizedName))
        .FirstOrDefaultAsync();
    
    if (exactMatch != null) return exactMatch;
    
    // 2. Try fuzzy matching with aliases
    var allProducts = await _consolidatedProductsCollection.Find({}).Limit(1000).ToListAsync();
    
    foreach (var product in allProducts) {
        var score = CalculateSimilarity(normalizedName, product["normalizedName"]);
        
        // Check aliases
        if (product.Contains("aliases")) {
            foreach (var alias in product["aliases"].AsBsonArray) {
                score = Math.Max(score, CalculateSimilarity(normalizedName, alias.AsString));
            }
        }
        
        if (score >= 0.8) return product;
    }
    
    return null;
}
```

#### TypeScript Implementation
```typescript
async function findBestMatch(normalizedName: string, size: string, originalName: string) {
    // 1. Exact match
    const exactMatch = await consolidatedProductsCollection.findOne({
        normalizedName: normalizedName
    });
    
    if (exactMatch) return exactMatch;
    
    // 2. Fuzzy matching
    const allProducts = await consolidatedProductsCollection.find({}).limit(1000).toArray();
    
    for (const product of allProducts) {
        let score = calculateSimilarity(normalizedName, product.normalizedName);
        
        // Check aliases
        if (product.aliases) {
            for (const alias of product.aliases) {
                score = Math.max(score, calculateSimilarity(normalizedName, alias));
            }
        }
        
        if (score >= 0.8) return product;
    }
    
    return null;
}
```

---

## New Zealand Brand Mappings Reference

### Supermarket Private Labels (15 brands)

| Canonical Name | Variations |
|----------------|------------|
| `pams` | pam, pams brand, pams select |
| `essentials` | essentials brand, countdown essentials |
| `homebrand` | home brand, homebrand select |
| `signature` | signature range, signature brand |
| `macro` | macro brand, macro organic |
| `woolworths` | woolworths brand, woolworths select |
| `countdown` | countdown brand, countdown select |
| `value` | value brand, budget |
| `fresh choice` | freshchoice, fresh choice brand |

### Dairy Brands (12 brands)

| Canonical Name | Variations |
|----------------|------------|
| `anchor` | anchor brand, anchor dairy, anchor milk, anchor butter, anchor cheese |
| `mainland` | mainland cheese, mainland dairy, mainland brand |
| `meadowfresh` | meadow fresh, meadowfresh milk, meadow fresh milk |
| `lewis road` | lewis road creamery, lewis road milk, lewis road butter |
| `kapiti` | kapiti cheese, kapiti ice cream, kapiti brand |
| `fernleaf` | fernleaf milk, fernleaf powder |
| `tararua` | tararua cheese, tararua dairy |
| `rolling meadow` | rolling meadow cheese, rolling meadow dairy |
| `whitestone` | whitestone cheese, whitestone dairy |
| `mercer` | mercer cheese, mercer dairy |
| `epicure` | epicure cheese, epicure dairy |
| `kikorangi` | kikorangi cheese, kikorangi blue |

### Meat Brands (10 brands)

| Canonical Name | Variations |
|----------------|------------|
| `tegel` | tegel chicken, tegel poultry, tegel brand |
| `inghams` | ingham, inghams chicken, inghams poultry |
| `turks` | turks poultry, turks chicken |
| `brinks` | brinks chicken, brinks poultry |
| `hellers` | heller, hellers bacon, hellers sausages, hellers smallgoods |
| `beehive` | beehive bacon, beehive ham, beehive smallgoods |
| `farmland` | farmland bacon, farmland ham |
| `primo` | primo smallgoods, primo bacon |
| `hans` | hans smallgoods, hans continental |
| `continental` | continental smallgoods, continental deli |
