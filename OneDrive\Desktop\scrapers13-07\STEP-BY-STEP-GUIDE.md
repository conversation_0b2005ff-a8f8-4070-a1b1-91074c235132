# Step-by-Step Guide: Running New Zealand Supermarket Scrapers

A complete guide to setting up and running the NZ supermarket scrapers for Woolworths, New World, and PakNSave.

## 📋 **Table of Contents**

1. [Prerequisites Installation](#prerequisites-installation)
2. [MongoDB Setup](#mongodb-setup)
3. [Project Setup](#project-setup)
4. [Running Individual Scrapers](#running-individual-scrapers)
5. [Running All Scrapers Together](#running-all-scrapers-together)
6. [Monitoring Progress](#monitoring-progress)
7. [Viewing Results](#viewing-results)
8. [Troubleshooting](#troubleshooting)

---

## 1. Prerequisites Installation

### **Step 1.1: Install Node.js**
1. Go to [nodejs.org](https://nodejs.org/)
2. Download the **LTS version** (v18 or higher)
3. Run the installer and follow the setup wizard
4. Verify installation:
   ```cmd
   node --version
   npm --version
   ```

### **Step 1.2: Install .NET SDK**
1. Go to [dotnet.microsoft.com/download](https://dotnet.microsoft.com/download)
2. Download **.NET 6 SDK** or higher
3. Run the installer and follow the setup wizard
4. Verify installation:
   ```cmd
   dotnet --version
   ```

### **Step 1.3: Install Docker Desktop**
1. Go to [docker.com/products/docker-desktop](https://www.docker.com/products/docker-desktop)
2. Download Docker Desktop for Windows
3. Run the installer and follow the setup wizard
4. Start Docker Desktop
5. Verify installation:
   ```cmd
   docker --version
   ```

---

## 2. MongoDB Setup

### **Step 2.1: Start MongoDB Container**
```cmd
# Start MongoDB in Docker
docker run -d -p 27017:27017 --name mongodb mongo:latest

# Verify MongoDB is running
docker ps
```

### **Step 2.2: Verify MongoDB Connection**
```cmd
# Check if MongoDB is accessible
docker exec -it mongodb mongosh --eval "db.adminCommand('ismaster')"
```

**Expected Output:**
```
{
  ismaster: true,
  maxBsonObjectSize: 16777216,
  ...
}
```

---

## 3. Project Setup

### **Step 3.1: Download/Clone Project**
```cmd
# If using Git
git clone [repository-url]
cd scrapers13-07

# Or extract downloaded ZIP file and navigate to folder
cd scrapers13-07
```

### **Step 3.2: Install Woolworths Dependencies**
```cmd
cd Woolworths
npm install
npx playwright install
cd ..
```

### **Step 3.3: Install New World Dependencies**
```cmd
cd new-world/src
dotnet restore
dotnet build
cd ../..
```

### **Step 3.4: Install PakNSave Dependencies**
```cmd
cd paknsave/src
dotnet restore
dotnet build
cd ../..
```

### **Step 3.5: Create Log Directory**
```cmd
mkdir logs
```

---

## 4. Running Individual Scrapers

### **Step 4.1: Test Woolworths Scraper**

#### **Dry Run (Testing)**
```cmd
cd Woolworths
npm run dev
```

#### **Database Mode**
```cmd
cd Woolworths
npm run db
```

#### **Database + Images Mode**
```cmd
cd Woolworths
npm run db-images
```

### **Step 4.2: Test New World Scraper**

#### **Dry Run (Testing)**
```cmd
cd new-world/src
dotnet run
```

#### **Database Mode**
```cmd
cd new-world/src
dotnet run db
```

#### **Database + Images Mode**
```cmd
cd new-world/src
dotnet run db images
```

### **Step 4.3: Test PakNSave Scraper**

#### **Dry Run (Testing)**
```cmd
cd paknsave/src
dotnet run
```

#### **Database Mode**
```cmd
cd paknsave/src
dotnet run db
```

#### **Database + Images Mode**
```cmd
cd paknsave/src
dotnet run db images
```

---

## 5. Running All Scrapers Together

### **Step 5.1: Using Batch File (Recommended)**

#### **Dry Run Mode (Testing)**
```cmd
# From scrapers13-07 directory
run-all-scrapers.bat dev
```

#### **Database Mode**
```cmd
# From scrapers13-07 directory
run-all-scrapers.bat db
```

#### **Database + Images Mode (Full Collection)**
```cmd
# From scrapers13-07 directory
run-all-scrapers.bat db-images
```

### **Step 5.2: Using PowerShell Script (Alternative)**

#### **Dry Run Mode**
```powershell
# From scrapers13-07 directory
.\run-all-scrapers.ps1 -Mode dev
```

#### **Database Mode**
```powershell
# From scrapers13-07 directory
.\run-all-scrapers.ps1 -Mode db
```

#### **Database + Images Mode**
```powershell
# From scrapers13-07 directory
.\run-all-scrapers.ps1 -Mode db-images
```

---

## 6. Monitoring Progress

### **Step 6.1: Watch Log Files**

#### **Real-time Monitoring**
```cmd
# Open separate command windows for each:

# Woolworths progress
type logs\woolworths.log

# New World progress
type logs\newworld.log

# PakNSave progress
type logs\paknsave.log
```

#### **Continuous Monitoring (PowerShell)**
```powershell
# Watch Woolworths log
Get-Content logs\woolworths.log -Wait

# Watch New World log
Get-Content logs\newworld.log -Wait

# Watch PakNSave log
Get-Content logs\paknsave.log -Wait
```

### **Step 6.2: Expected Progress Indicators**

#### **Woolworths (~30 minutes)**
```
✅ MongoDB connection established
🟡 Attempting to select store location: Auckland Central
260 pages to be scraped            7s delay between scrapes
[1/260] www.woolworths.co.nz/shop/browse/fruit-veg?page=1
49 product entries found           Time Elapsed: 42s
```

#### **New World (~15 minutes)**
```
✅ MongoDB connection established
✅ Enhanced NZ brand mappings loaded (152 brands)
86 pages to be scraped            7s delay between scrapes
[1/86] https://www.newworld.co.nz/shop/category/fresh-foods?page=1
46 product entries found          Time Elapsed: 42s
```

#### **PakNSave (~6 minutes)**
```
✅ MongoDB connection established
✅ Enhanced NZ brand mappings loaded (152 brands)
30 pages to be scraped            7s delay between scrapes
[1/30] https://www.paknsave.co.nz/shop/category/fresh-foods?page=1
50 product entries found          Time Elapsed: 42s
```

---

## 7. Viewing Results

### **Step 7.1: Connect to MongoDB**

#### **Using MongoDB Compass (GUI)**
1. Download [MongoDB Compass](https://www.mongodb.com/products/compass)
2. Connect to: `mongodb://localhost:27017`
3. Select database: `nz-supermarket-scraper`

#### **Using Command Line**
```cmd
# Connect to MongoDB shell
docker exec -it mongodb mongosh

# Switch to database
use nz-supermarket-scraper

# View collections
show collections
```

### **Step 7.2: Query Product Data**

#### **Count Total Products**
```javascript
// In MongoDB shell
db.consolidated_products.countDocuments()
```

#### **View Sample Products**
```javascript
// View first 5 products
db.consolidated_products.find().limit(5).pretty()
```

#### **Find Cross-Store Products**
```javascript
// Products available in multiple stores
db.consolidated_products.find({"variants.1": {$exists: true}}).limit(10)
```

#### **Search by Brand**
```javascript
// Find Anchor products
db.consolidated_products.find({normalizedName: {$regex: "anchor", $options: "i"}})
```

### **Step 7.3: Price History Analysis**
```javascript
// View price history
db.price_history.find().sort({timestamp: -1}).limit(10)

// Count price records
db.price_history.countDocuments()
```

---

## 8. Troubleshooting

### **Step 8.1: Common Issues**

#### **MongoDB Connection Failed**
```
Error: MongoDB connection failed
```
**Solution:**
```cmd
# Check if MongoDB is running
docker ps | findstr mongodb

# Restart MongoDB if needed
docker restart mongodb

# Check MongoDB logs
docker logs mongodb
```

#### **Port Already in Use**
```
Error: Port 27017 is already in use
```
**Solution:**
```cmd
# Stop existing MongoDB
docker stop mongodb
docker rm mongodb

# Start fresh MongoDB
docker run -d -p 27017:27017 --name mongodb mongo:latest
```

#### **Node.js/npm Errors**
```
Error: Cannot find module
```
**Solution:**
```cmd
cd Woolworths
npm install
npx playwright install
```

#### **.NET Build Errors**
```
Error: The project file could not be loaded
```
**Solution:**
```cmd
cd new-world/src
dotnet clean
dotnet restore
dotnet build
```

### **Step 8.2: Performance Issues**

#### **Slow Scraping**
- **Cause**: Network delays or rate limiting
- **Solution**: Scrapers include 7-second delays by default (respectful scraping)

#### **High Memory Usage**
- **Cause**: Large number of products being processed
- **Solution**: This is normal; scrapers are designed to handle large datasets

#### **Browser Crashes**
- **Cause**: Insufficient system resources
- **Solution**: Close other applications, ensure sufficient RAM (4GB+ recommended)

### **Step 8.3: Verification Steps**

#### **Check All Prerequisites**
```cmd
# Verify all installations
node --version     # Should show v16+
npm --version      # Should show 8+
dotnet --version   # Should show 6.0+
docker --version   # Should show 20+
```

#### **Test MongoDB Connection**
```cmd
# Test MongoDB is accessible
docker exec -it mongodb mongosh --eval "db.runCommand({ping: 1})"
```

#### **Check Log Files**
```cmd
# Ensure log files are being created
dir logs
```

---

## 🎉 **Success Indicators**

### **Scraping Complete**
- All log files show "Scraping completed" messages
- MongoDB contains consolidated_products collection
- Price history records are being created
- Product images are stored in GridFS (if using db-images mode)

### **Expected Database Size**
- **Products**: ~8,000 consolidated products
- **Price Records**: ~17,500 individual price points
- **Images**: ~12,000 product images (if enabled)
- **Database Size**: ~100MB total

### **Cross-Store Matching Working**
- Products with multiple variants (different stores)
- Consistent product names across stores
- Alias arrays populated with store-specific names

---

**Congratulations!** You now have a fully operational New Zealand supermarket price comparison system with comprehensive product data from all three major chains! 🇳🇿

For additional help, refer to the individual scraper README files or the main project documentation.
