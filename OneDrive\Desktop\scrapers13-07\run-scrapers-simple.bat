@echo off
setlocal enabledelayedexpansion

echo Running scrapers in %1 mode...
echo.

if "%1"=="" (
    echo Usage: run-scrapers-simple.bat [dev^|db^|db-images]
    echo.
    echo   dev       - Dry run mode
    echo   db        - Save to database  
    echo   db-images - Save to database with images
    echo.
    goto :end
)

REM Create logs directory
if not exist "logs" mkdir logs

REM Check MongoDB
echo Checking MongoDB...
docker ps | findstr mongodb >nul
if !ERRORLEVEL! NEQ 0 (
    echo Starting MongoDB container...
    docker run -d -p 27017:27017 --name mongodb mongo:latest
    timeout /t 3 >nul
)

echo.
echo ========================================
echo Starting Woolworths Scraper
echo ========================================
cd /d "C:\Users\<USER>\OneDrive\Desktop\scrapers13-07\Woolworths"

if "%1"=="dev" (
    start "Woolworths" cmd /k "npm run dev"
) else if "%1"=="db" (
    start "Woolworths" cmd /k "npm run db"
) else if "%1"=="db-images" (
    start "Woolworths" cmd /k "npm run db images"
)

timeout /t 2 >nul

echo.
echo ========================================  
echo Starting New World Scraper
echo ========================================
cd /d "C:\Users\<USER>\OneDrive\Desktop\scrapers13-07\new-world\src"

if "%1"=="dev" (
    start "New World" cmd /k "dotnet run"
) else if "%1"=="db" (
    start "New World" cmd /k "dotnet run db"
) else if "%1"=="db-images" (
    start "New World" cmd /k "dotnet run db images"
)

timeout /t 2 >nul

echo.
echo ========================================
echo Starting PakNSave Scraper  
echo ========================================
cd /d "C:\Users\<USER>\OneDrive\Desktop\scrapers13-07\paknsave\src"

if "%1"=="dev" (
    start "PakNSave" cmd /k "dotnet run"
) else if "%1"=="db" (
    start "PakNSave" cmd /k "dotnet run db" 
) else if "%1"=="db-images" (
    start "PakNSave" cmd /k "dotnet run db images"
)

echo.
echo All scrapers started in separate windows!
echo Each scraper is running in its own command prompt window.
echo.

:end