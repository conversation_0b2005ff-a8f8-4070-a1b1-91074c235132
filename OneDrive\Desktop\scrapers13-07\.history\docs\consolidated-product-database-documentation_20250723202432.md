# New Zealand Supermarket Scrapers - Consolidated Product Database Documentation

## Table of Contents
1. [Database Schema Documentation](#database-schema-documentation)
2. [Product Matching Algorithm Specification](#product-matching-algorithm-specification)
3. [New Zealand Brand Mappings Reference](#new-zealand-brand-mappings-reference)
4. [API Integration Guide](#api-integration-guide)
5. [Data Structure Examples](#data-structure-examples)
6. [Migration Notes](#migration-notes)

---

## Database Schema Documentation

### MongoDB Collections Overview

The consolidated product database consists of three main collections:

- **`consolidated_products`**: Master product catalog with normalized data
- **`price_history`**: Historical price tracking across all stores
- **`productImages`** (GridFS): Product images stored as binary data

### Consolidated Products Collection Schema

```javascript
{
  _id: ObjectId,                    // MongoDB unique identifier
  normalizedName: String,           // Normalized product name for matching (required, indexed)
  displayName: String,              // Human-readable product name (required)
  primarySize: String,              // Primary size/quantity (optional)
  categoryId: ObjectId,             // Reference to product category (optional)
  brandId: ObjectId,                // Reference to brand (optional)
  aliases: [String],                // Array of alternative product names (indexed)
  matchConfidence: Number,          // Confidence score (0-100) for automatic matching
  manualMatch: Boolean,             // Whether this was manually verified
  
  // Store-specific variants
  variants: [{
    storeProductId: String,         // Original store product ID (required)
    storeId: Number,                // Store identifier (1=Woolworths, 2=NewWorld, 3=PakNSave)
    storeName: String,              // Store-specific product name
    storeSize: String,              // Store-specific size information
    storeUnitPrice: Number,         // Price per unit ($/kg, $/L, etc.)
    storeUnitName: String,          // Unit name (kg, L, each, etc.)
    lastSeen: Date,                 // Last time this variant was scraped
    isActive: Boolean,              // Whether product is currently available
    imageUrl: String                // GridFS reference or external URL
  }],
  
  // Size variations
  sizeVariants: [{
    sizeName: String,               // Size description (e.g., "500g", "1L")
    sizeWeightGrams: Number,        // Weight in grams (if applicable)
    sizeVolumeMl: Number,           // Volume in milliliters (if applicable)
    isPrimarySize: Boolean          // Whether this is the main size
  }],
  
  // Current pricing across stores
  currentPrices: [{
    storeId: Number,                // Store identifier
    price: Number,                  // Current price in NZD
    isSpecial: Boolean,             // Whether on special/sale
    wasAvailable: Boolean,          // Whether in stock during last scrape
    recordedAt: Date                // When this price was recorded
  }],
  
  // Metadata
  createdAt: Date,                  // When product was first added
  updatedAt: Date                   // Last modification timestamp
}
```

### Price History Collection Schema

```javascript
{
  _id: ObjectId,
  consolidatedProductId: ObjectId,  // Reference to consolidated product (required, indexed)
  storeId: Number,                  // Store identifier (required, indexed)
  price: Number,                    // Price in NZD (required)
  timestamp: Date,                  // When price was recorded (required, indexed)
  year: Number,                     // Year for efficient querying (indexed)
  month: Number,                    // Month for efficient querying (indexed)
  isSpecial: Boolean,               // Whether product was on special
  wasAvailable: Boolean             // Whether product was in stock
}
```

### GridFS Product Images Schema

```javascript
// GridFS files collection
{
  _id: ObjectId,
  filename: String,                 // Format: "product_{storeProductId}_{storeId}.jpg"
  uploadDate: Date,
  length: Number,                   // File size in bytes
  chunkSize: Number,                // GridFS chunk size
  md5: String,                      // File hash for integrity
  metadata: {
    productId: String,              // Store product ID
    storeId: Number,                // Store identifier
    originalUrl: String,            // Original image URL
    uploadedAt: Date,               // Upload timestamp
    contentType: String             // MIME type (usually "image/jpeg")
  }
}
```

### Database Indexes

```javascript
// Consolidated Products Indexes
db.consolidated_products.createIndex({ "normalizedName": 1 })
db.consolidated_products.createIndex({ "aliases": 1 })
db.consolidated_products.createIndex({ "variants.storeProductId": 1, "variants.storeId": 1 })
db.consolidated_products.createIndex({ "createdAt": 1 })
db.consolidated_products.createIndex({ "updatedAt": 1 })

// Price History Indexes
db.price_history.createIndex({ "consolidatedProductId": 1, "timestamp": -1 })
db.price_history.createIndex({ "storeId": 1, "timestamp": -1 })
db.price_history.createIndex({ "year": 1, "month": 1 })
db.price_history.createIndex({ "timestamp": -1 })
```

---

## Product Matching Algorithm Specification

### Overview

The enhanced product matching system uses a multi-stage approach to consolidate products across different supermarket stores with high accuracy (90%+).

### Matching Pipeline

```
1. Input Product → 2. Normalize Name → 3. Check Exact Matches → 4. Fuzzy Search → 5. Confidence Scoring → 6. Decision
```

### Stage 1: Product Name Normalization

#### Brand Mapping Application
- **Input**: Raw product name (e.g., "Coca-Cola Classic 330ml")
- **Process**: Apply comprehensive NZ brand mappings
- **Output**: Canonical brand name (e.g., "coke classic 330ml")

#### Store Descriptor Removal
```javascript
const storeDescriptors = [
  'woolworths', 'countdown', 'new world', 'paknsave', 'pak n save',
  'select', 'premium', 'value', 'budget', 'signature', 'essentials',
  'pams', 'homebrand', 'macro', 'organic', 'free range', 'natural'
];
```

#### Size Normalization
```javascript
const sizeNormalizations = {
  'grams': 'g', 'gram': 'g', 'kilograms': 'kg', 'kilogram': 'kg',
  'litres': 'l', 'litre': 'l', 'millilitres': 'ml', 'millilitre': 'ml',
  'pieces': 'pc', 'piece': 'pc', 'each': 'ea', 'pack': 'pk'
};
```

### Stage 2: String Similarity Algorithms

#### Levenshtein Distance
- **Purpose**: Character-level similarity measurement
- **Weight**: 60% of final similarity score
- **Formula**: `similarity = 1 - (distance / maxLength)`

#### Jaccard Similarity
- **Purpose**: Word-set based semantic matching
- **Weight**: 40% of final similarity score
- **Formula**: `similarity = intersection_size / union_size`

#### Combined Similarity Score
```javascript
finalScore = (levenshteinSimilarity * 0.6) + (jaccardSimilarity * 0.4)
```

### Stage 3: Confidence Scoring & Thresholds

| Match Type | Confidence Score | Action |
|------------|------------------|---------|
| Exact Match | 100% | Automatic consolidation |
| Manual Mapping | 95% | Automatic consolidation |
| High Confidence | 85-94% | Automatic consolidation + alias addition |
| Medium Confidence | 70-84% | Create new product, log potential matches |
| Low Confidence | <70% | Create new product |

### Stage 4: Alias Management System

#### Automatic Alias Addition
- When products match with 85%+ confidence but different names
- Prevents duplicate aliases (checks existing aliases array)
- Normalizes alias before adding to prevent redundancy

#### Alias Search Priority
1. **Exact normalized name match** (highest priority)
2. **Manual mapping match** (high priority)
3. **Alias array search** (medium priority)
4. **Fuzzy similarity search** (lowest priority)

### Algorithm Implementation Examples

#### C# (.NET) Implementation
```csharp
private static async Task<BsonDocument> FindBestMatch(string normalizedName, string size, string originalName)
{
    // 1. Try exact match
    var exactMatch = await _consolidatedProductsCollection
        .Find(Builders<BsonDocument>.Filter.Eq("normalizedName", normalizedName))
        .FirstOrDefaultAsync();
    
    if (exactMatch != null) return exactMatch;
    
    // 2. Try fuzzy matching with aliases
    var allProducts = await _consolidatedProductsCollection.Find({}).Limit(1000).ToListAsync();
    
    foreach (var product in allProducts) {
        var score = CalculateSimilarity(normalizedName, product["normalizedName"]);
        
        // Check aliases
        if (product.Contains("aliases")) {
            foreach (var alias in product["aliases"].AsBsonArray) {
                score = Math.Max(score, CalculateSimilarity(normalizedName, alias.AsString));
            }
        }
        
        if (score >= 0.8) return product;
    }
    
    return null;
}
```

#### TypeScript Implementation
```typescript
async function findBestMatch(normalizedName: string, size: string, originalName: string) {
    // 1. Exact match
    const exactMatch = await consolidatedProductsCollection.findOne({
        normalizedName: normalizedName
    });
    
    if (exactMatch) return exactMatch;
    
    // 2. Fuzzy matching
    const allProducts = await consolidatedProductsCollection.find({}).limit(1000).toArray();
    
    for (const product of allProducts) {
        let score = calculateSimilarity(normalizedName, product.normalizedName);
        
        // Check aliases
        if (product.aliases) {
            for (const alias of product.aliases) {
                score = Math.max(score, calculateSimilarity(normalizedName, alias));
            }
        }
        
        if (score >= 0.8) return product;
    }
    
    return null;
}
```

---

## New Zealand Brand Mappings Reference

### Supermarket Private Labels (15 brands)

| Canonical Name | Variations |
|----------------|------------|
| `pams` | pam, pams brand, pams select |
| `essentials` | essentials brand, countdown essentials |
| `homebrand` | home brand, homebrand select |
| `signature` | signature range, signature brand |
| `macro` | macro brand, macro organic |
| `woolworths` | woolworths brand, woolworths select |
| `countdown` | countdown brand, countdown select |
| `value` | value brand, budget |
| `fresh choice` | freshchoice, fresh choice brand |

### Dairy Brands (12 brands)

| Canonical Name | Variations |
|----------------|------------|
| `anchor` | anchor brand, anchor dairy, anchor milk, anchor butter, anchor cheese |
| `mainland` | mainland cheese, mainland dairy, mainland brand |
| `meadowfresh` | meadow fresh, meadowfresh milk, meadow fresh milk |
| `lewis road` | lewis road creamery, lewis road milk, lewis road butter |
| `kapiti` | kapiti cheese, kapiti ice cream, kapiti brand |
| `fernleaf` | fernleaf milk, fernleaf powder |
| `tararua` | tararua cheese, tararua dairy |
| `rolling meadow` | rolling meadow cheese, rolling meadow dairy |
| `whitestone` | whitestone cheese, whitestone dairy |
| `mercer` | mercer cheese, mercer dairy |
| `epicure` | epicure cheese, epicure dairy |
| `kikorangi` | kikorangi cheese, kikorangi blue |

### Meat Brands (10 brands)

| Canonical Name | Variations |
|----------------|------------|
| `tegel` | tegel chicken, tegel poultry, tegel brand |
| `inghams` | ingham, inghams chicken, inghams poultry |
| `turks` | turks poultry, turks chicken |
| `brinks` | brinks chicken, brinks poultry |
| `hellers` | heller, hellers bacon, hellers sausages, hellers smallgoods |
| `beehive` | beehive bacon, beehive ham, beehive smallgoods |
| `farmland` | farmland bacon, farmland ham |
| `primo` | primo smallgoods, primo bacon |
| `hans` | hans smallgoods, hans continental |
| `continental` | continental smallgoods, continental deli |

### Bread & Bakery Brands (9 brands)

| Canonical Name | Variations |
|----------------|------------|
| `tip top` | tiptop, tip top bread, tiptop bread |
| `molenberg` | molenberg bread, molenburg, molenberg wholemeal |
| `vogels` | vogel, vogels bread, vogel bread, vogels original |
| `freyas` | freya, freyas bread, freya bread |
| `natures fresh` | nature fresh, natures fresh bread |
| `burgen` | burgen bread, burgen soy lin |
| `ploughmans` | ploughman, ploughmans bread |
| `golden` | golden bread, golden bakery |
| `bakers delight` | bakersdelight, bakers delight bread |

### Beverage Brands (15 brands)

| Canonical Name | Variations |
|----------------|------------|
| `coke` | coca cola, coca-cola, coke classic, coca cola classic |
| `coke zero` | coca cola zero, coke zero sugar, coca cola zero sugar |
| `diet coke` | coca cola diet, diet coca cola |
| `pepsi` | pepsi cola, pepsi classic, pepsi original |
| `pepsi max` | pepsi maximum taste, pepsi max no sugar |
| `fanta` | fanta orange, fanta grape |
| `sprite` | sprite lemon, sprite lime |
| `l&p` | lemon paeroa, lemon and paeroa, l and p |
| `just juice` | justjuice, just juice brand |
| `fresh up` | freshup, fresh up juice |
| `keri` | keri juice, keri fresh |
| `charlies` | charlie, charlies juice, charlies honest |
| `phoenix` | phoenix organic, phoenix juice |
| `pump` | pump water, pump brand |
| `powerade` | powerade sports drink |

### Cereal & Breakfast Brands (8 brands)

| Canonical Name | Variations |
|----------------|------------|
| `sanitarium` | sanitarium weetbix, sanitarium so good |
| `weetbix` | weet bix, wheat biscuits, sanitarium weetbix |
| `uncle tobys` | uncle toby, uncle tobys oats, uncle tobys muesli |
| `kelloggs` | kellogg, kelloggs cornflakes, kelloggs special k |
| `cornflakes` | corn flakes, kelloggs cornflakes |
| `nutrigrain` | nutri grain, kelloggs nutrigrain |
| `special k` | specialk, kelloggs special k |
| `hubbards` | hubbards cereal, hubbards muesli |

### Snack & Confectionery Brands (20 brands)

| Canonical Name | Variations |
|----------------|------------|
| `cadbury` | cadburys, cadbury chocolate |
| `whittakers` | whittaker, whittakers chocolate |
| `nestle` | nestlé, nestle chocolate |
| `mars` | mars bar, mars chocolate |
| `snickers` | snickers bar, snickers chocolate |
| `kit kat` | kitkat, kit-kat |
| `twix` | twix bar, twix chocolate |
| `moro` | moro bar, moro chocolate |
| `picnic` | picnic bar, picnic chocolate |
| `crunchie` | crunchie bar, crunchie chocolate |
| `bluebird` | bluebird chips, bluebird snacks |
| `eta` | eta chips, eta snacks, eta peanut butter |
| `proper` | proper chips, proper crisps |
| `heartland` | heartland chips, heartland snacks |
| `griffins` | griffin, griffins biscuits, griffin biscuits |
| `arnott` | arnotts, arnott biscuits, arnotts biscuits |
| `toffee pops` | toffeepops, griffins toffee pops |
| `mallowpuffs` | mallow puffs, griffins mallowpuffs |
| `pics` | pic, pic peanut butter, pics peanut butter |
| `cottees` | cottee, cottees jam, cottee jam |

### Pantry & Cooking Brands (18 brands)

| Canonical Name | Variations |
|----------------|------------|
| `watties` | wattie, watties tomatoes, watties sauce |
| `heinz` | heinz beans, heinz tomato sauce, heinz soup |
| `maggi` | maggi noodles, maggi soup, maggi instant |
| `continental` | continental soup, continental pasta |
| `mccains` | mccain, mccains chips, mccains frozen |
| `edgell` | edgell vegetables, edgell canned |
| `greggs` | greggs coffee, greggs instant |
| `nescafe` | nescafé, nescafe coffee |
| `moccona` | moccona coffee, moccona instant |
| `robert harris` | robertharris, robert harris coffee |
| `bell tea` | bell tea bags, bell black tea |
| `dilmah` | dilmah tea, dilmah ceylon |
| `olivani` | olivani oil, olivani olive oil |
| `bertolli` | bertolli oil, bertolli olive oil |
| `praise` | praise mayonnaise, praise dressing |
| `best foods` | bestfoods, best foods mayo |
| `masterfoods` | master foods, masterfoods sauce |
| `fountain` | fountain sauce, fountain tomato sauce |

### Cleaning & Household Brands (15 brands)

| Canonical Name | Variations |
|----------------|------------|
| `janola` | janola bleach, janola cleaning |
| `earthwise` | earthwise cleaning, earthwise eco |
| `finish` | finish dishwasher, finish tablets |
| `ajax` | ajax spray, ajax cleaning |
| `jif` | jif cream cleanser, jif bathroom |
| `domestos` | domestos bleach, domestos toilet |
| `toilet duck` | toiletduck, toilet duck cleaner |
| `mr muscle` | mrmuscle, mr muscle bathroom |
| `windex` | windex glass, windex cleaner |
| `persil` | persil washing powder, persil liquid |
| `surf` | surf washing powder, surf liquid |
| `omo` | omo washing powder, omo liquid |
| `cold power` | coldpower, cold power liquid |
| `sorbent` | sorbent toilet paper, sorbent tissues |
| `kleenex` | kleenex tissues, kleenex toilet paper |

### Health & Beauty Brands (10 brands)

| Canonical Name | Variations |
|----------------|------------|
| `colgate` | colgate toothpaste, colgate toothbrush |
| `oral b` | oral-b, oralb, oral b toothbrush |
| `sensodyne` | sensodyne toothpaste |
| `macleans` | macleans toothpaste |
| `head shoulders` | head and shoulders, head & shoulders |
| `pantene` | pantene shampoo, pantene conditioner |
| `herbal essences` | herbal essence, herbal essences shampoo |
| `dove` | dove soap, dove body wash |
| `nivea` | nivea cream, nivea body |
| `vaseline` | vaseline petroleum jelly |

### Pet Food Brands (10 brands)

| Canonical Name | Variations |
|----------------|------------|
| `pedigree` | pedigree dog food, pedigree dry |
| `whiskas` | whiskas cat food, whiskas wet |
| `fancy feast` | fancyfeast, fancy feast cat |
| `royal canin` | royalcanin, royal canin dog |
| `hills` | hills pet food, hills science diet |
| `eukanuba` | eukanuba dog food |
| `iams` | iams pet food, iams dog |
| `optimum` | optimum dog food, optimum pet |
| `tux` | tux cat food, tux pet |
| `champ` | champ dog food, champ pet |

**Total Brand Mappings**: 152 brands with 500+ variations

---

## API Integration Guide

### Database Connection

#### MongoDB Connection String
```
mongodb://localhost:27017/nz-supermarket-scraper
```

#### Collections
- `consolidated_products` - Main product catalog
- `price_history` - Historical pricing data
- `productImages.files` - GridFS image metadata
- `productImages.chunks` - GridFS image data

### Query Patterns

#### 1. Cross-Store Price Comparison

```javascript
// Find all stores selling a specific product
db.consolidated_products.aggregate([
  { $match: { normalizedName: "anchor milk 2l" } },
  { $unwind: "$currentPrices" },
  { $lookup: {
      from: "stores",
      localField: "currentPrices.storeId",
      foreignField: "storeId",
      as: "store"
  }},
  { $project: {
      productName: "$displayName",
      storeName: { $arrayElemAt: ["$store.name", 0] },
      price: "$currentPrices.price",
      isSpecial: "$currentPrices.isSpecial",
      lastUpdated: "$currentPrices.recordedAt"
  }}
])
```

#### 2. Product Search with Aliases

```javascript
// Search products by name (including aliases)
db.consolidated_products.find({
  $or: [
    { normalizedName: { $regex: "milk", $options: "i" } },
    { aliases: { $regex: "milk", $options: "i" } },
    { displayName: { $regex: "milk", $options: "i" } }
  ]
})
```

#### 3. Price History Analysis

```javascript
// Get price trends for a product over time
db.price_history.aggregate([
  { $match: {
      consolidatedProductId: ObjectId("..."),
      timestamp: { $gte: new Date("2024-01-01") }
  }},
  { $group: {
      _id: {
        year: "$year",
        month: "$month",
        storeId: "$storeId"
      },
      avgPrice: { $avg: "$price" },
      minPrice: { $min: "$price" },
      maxPrice: { $max: "$price" },
      priceCount: { $sum: 1 }
  }},
  { $sort: { "_id.year": 1, "_id.month": 1 } }
])
```

### Handling Product Variants

#### Accessing Store-Specific Information
```javascript
// Get product variants for all stores
const product = await db.consolidated_products.findOne({
  normalizedName: "anchor milk 2l"
});

product.variants.forEach(variant => {
  console.log(`Store ${variant.storeId}: ${variant.storeName} - $${variant.storeUnitPrice}/${variant.storeUnitName}`);
});
```

#### Working with Aliases
```javascript
// Add new alias to existing product
await db.consolidated_products.updateOne(
  { _id: productId },
  {
    $addToSet: { aliases: newAlias },
    $set: { updatedAt: new Date() }
  }
);
```

### Image Retrieval from GridFS

#### Node.js Example
```javascript
const { GridFSBucket } = require('mongodb');

async function getProductImage(imageId) {
  const bucket = new GridFSBucket(db, { bucketName: 'productImages' });

  try {
    const downloadStream = bucket.openDownloadStream(new ObjectId(imageId));
    return downloadStream;
  } catch (error) {
    console.error('Image not found:', error);
    return null;
  }
}
```

#### C# Example
```csharp
public static async Task<Stream> GetProductImage(string imageId)
{
    var bucket = new GridFSBucket(_database, new GridFSBucketOptions
    {
        BucketName = "productImages"
    });

    try
    {
        var objectId = new ObjectId(imageId);
        return await bucket.OpenDownloadStreamAsync(objectId);
    }
    catch (GridFSFileNotFoundException)
    {
        return null;
    }
}
```

### Price History Access

#### Get Recent Price Changes
```javascript
// Find products with significant price changes in last 7 days
db.price_history.aggregate([
  { $match: {
      timestamp: { $gte: new Date(Date.now() - 7*24*60*60*1000) }
  }},
  { $group: {
      _id: "$consolidatedProductId",
      prices: { $push: { price: "$price", timestamp: "$timestamp" } }
  }},
  { $match: { "prices.1": { $exists: true } } }, // At least 2 price points
  { $addFields: {
      priceChange: {
        $subtract: [
          { $arrayElemAt: ["$prices.price", -1] },
          { $arrayElemAt: ["$prices.price", 0] }
        ]
      }
  }},
  { $match: { priceChange: { $ne: 0 } } },
  { $lookup: {
      from: "consolidated_products",
      localField: "_id",
      foreignField: "_id",
      as: "product"
  }}
])
```

### Performance Optimization

#### Recommended Indexes
```javascript
// Essential indexes for performance
db.consolidated_products.createIndex({ "normalizedName": 1 })
db.consolidated_products.createIndex({ "aliases": 1 })
db.consolidated_products.createIndex({ "variants.storeProductId": 1, "variants.storeId": 1 })
db.price_history.createIndex({ "consolidatedProductId": 1, "timestamp": -1 })
db.price_history.createIndex({ "storeId": 1, "timestamp": -1 })
```

#### Query Optimization Tips
1. **Use projection** to limit returned fields
2. **Limit results** when possible
3. **Use compound indexes** for multi-field queries
4. **Cache frequently accessed data** in application layer

---

## Data Structure Examples

### Sample Consolidated Product Document

```json
{
  "_id": ObjectId("65f1a2b3c4d5e6f7a8b9c0d1"),
  "normalizedName": "anchor milk 2l",
  "displayName": "Anchor Milk 2L",
  "primarySize": "2L",
  "categoryId": null,
  "brandId": null,
  "aliases": [
    "anchor blue milk 2l",
    "anchor standard milk 2l",
    "anchor whole milk 2l"
  ],
  "matchConfidence": 95,
  "manualMatch": false,
  "variants": [
    {
      "storeProductId": "WOW123456",
      "storeId": 1,
      "storeName": "Anchor Blue Milk 2L",
      "storeSize": "2L",
      "storeUnitPrice": 3.25,
      "storeUnitName": "per litre",
      "lastSeen": "2024-01-15T10:30:00Z",
      "isActive": true,
      "imageUrl": "gridfs://productImages/65f1a2b3c4d5e6f7a8b9c0d2"
    },
    {
      "storeProductId": "NW789012",
      "storeId": 2,
      "storeName": "Anchor Milk Standard 2 Litre",
      "storeSize": "2 Litre",
      "storeUnitPrice": 3.20,
      "storeUnitName": "per L",
      "lastSeen": "2024-01-15T11:45:00Z",
      "isActive": true,
      "imageUrl": "gridfs://productImages/65f1a2b3c4d5e6f7a8b9c0d3"
    },
    {
      "storeProductId": "PNS345678",
      "storeId": 3,
      "storeName": "Anchor Brand Milk 2L",
      "storeSize": "2L",
      "storeUnitPrice": 3.15,
      "storeUnitName": "per litre",
      "lastSeen": "2024-01-15T09:15:00Z",
      "isActive": true,
      "imageUrl": "gridfs://productImages/65f1a2b3c4d5e6f7a8b9c0d4"
    }
  ],
  "sizeVariants": [
    {
      "sizeName": "2L",
      "sizeWeightGrams": null,
      "sizeVolumeMl": 2000,
      "isPrimarySize": true
    }
  ],
  "currentPrices": [
    {
      "storeId": 1,
      "price": 6.50,
      "isSpecial": false,
      "wasAvailable": true,
      "recordedAt": "2024-01-15T10:30:00Z"
    },
    {
      "storeId": 2,
      "price": 6.40,
      "isSpecial": true,
      "wasAvailable": true,
      "recordedAt": "2024-01-15T11:45:00Z"
    },
    {
      "storeId": 3,
      "price": 6.30,
      "isSpecial": false,
      "wasAvailable": true,
      "recordedAt": "2024-01-15T09:15:00Z"
    }
  ],
  "createdAt": "2024-01-10T08:00:00Z",
  "updatedAt": "2024-01-15T11:45:00Z"
}
```

### Sample Price History Document

```json
{
  "_id": ObjectId("65f1a2b3c4d5e6f7a8b9c0d5"),
  "consolidatedProductId": ObjectId("65f1a2b3c4d5e6f7a8b9c0d1"),
  "storeId": 2,
  "price": 6.40,
  "timestamp": "2024-01-15T11:45:00Z",
  "year": 2024,
  "month": 1,
  "isSpecial": true,
  "wasAvailable": true
}
```

### Sample GridFS Image Document

```json
{
  "_id": ObjectId("65f1a2b3c4d5e6f7a8b9c0d2"),
  "filename": "product_WOW123456_1.jpg",
  "uploadDate": "2024-01-15T10:30:00Z",
  "length": 45678,
  "chunkSize": 261120,
  "md5": "d41d8cd98f00b204e9800998ecf8427e",
  "metadata": {
    "productId": "WOW123456",
    "storeId": 1,
    "originalUrl": "https://cdn.woolworths.com.au/wowproductimages/large/123456.jpg",
    "uploadedAt": "2024-01-15T10:30:00Z",
    "contentType": "image/jpeg"
  }
}
```

---

## Migration Notes

### Changes from Previous Structure

#### New Fields Added
- `aliases` array for alternative product names
- `matchConfidence` for tracking automatic matching quality
- `manualMatch` boolean for manual verification tracking
- Enhanced `variants` structure with more detailed store information
- `sizeVariants` array for multiple size options
- `currentPrices` array for real-time pricing across stores

#### Modified Fields
- `normalizedName` now uses enhanced normalization with NZ brand mappings
- `variants.imageUrl` now supports GridFS references
- Price tracking moved to separate `price_history` collection for better performance

#### Deprecated Fields
- Individual store price fields (moved to `currentPrices` array)
- Simple product matching (replaced with enhanced algorithm)

### Migration Script Example

```javascript
// Migration script for existing products
db.consolidated_products.find({}).forEach(function(product) {
  // Add missing fields
  if (!product.aliases) {
    db.consolidated_products.updateOne(
      { _id: product._id },
      {
        $set: {
          aliases: [],
          matchConfidence: 100,
          manualMatch: false
        }
      }
    );
  }

  // Migrate old price structure to currentPrices array
  if (product.woolworthsPrice || product.newWorldPrice || product.paknsavePrice) {
    const currentPrices = [];

    if (product.woolworthsPrice) {
      currentPrices.push({
        storeId: 1,
        price: product.woolworthsPrice,
        isSpecial: false,
        wasAvailable: true,
        recordedAt: product.updatedAt || new Date()
      });
    }

    // Similar for other stores...

    db.consolidated_products.updateOne(
      { _id: product._id },
      {
        $set: { currentPrices: currentPrices },
        $unset: {
          woolworthsPrice: "",
          newWorldPrice: "",
          paknsavePrice: ""
        }
      }
    );
  }
});
```

### Backward Compatibility

#### API Compatibility
- Old query patterns will continue to work for basic product searches
- Price fields have been maintained in `currentPrices` array for compatibility
- Image URLs maintain backward compatibility with external URL format

#### Data Integrity
- All existing product data is preserved during migration
- Enhanced matching algorithm improves accuracy without losing existing matches
- Price history is maintained and enhanced with new tracking capabilities

---

## Conclusion

This consolidated product database provides a robust foundation for cross-store price comparison and product matching across New Zealand's major supermarket chains. The enhanced matching algorithm with comprehensive NZ brand mappings ensures high accuracy product consolidation while maintaining flexibility for future expansion.

For technical support or questions about implementation, refer to the individual scraper documentation or contact the development team.

**Last Updated**: January 2024
**Version**: 2.0
**Compatibility**: MongoDB 4.4+, Node.js 16+, .NET 6+
