var En=Object.defineProperty;var Sn=(R,h,c)=>h in R?En(R,h,{enumerable:!0,configurable:!0,writable:!0,value:c}):R[h]=c;var $=(R,h,c)=>(Sn(R,typeof h!="symbol"?h+"":h,c),c);class An{constructor(){$(this,"_map");this._map=new Map}set(h,c){let m=this._map.get(h);m||(m=[],this._map.set(h,m)),m.push(c)}get(h){return this._map.get(h)||[]}has(h){return this._map.has(h)}delete(h,c){const m=this._map.get(h);m&&m.includes(c)&&this._map.set(h,m.filter(I=>c!==I))}deleteAll(h){this._map.delete(h)}hasValue(h,c){const m=this._map.get(h);return m?m.includes(c):!1}get size(){return this._map.size}[Symbol.iterator](){return this._map[Symbol.iterator]()}keys(){return this._map.keys()}values(){const h=[];for(const c of this.keys())h.push(...this.get(c));return h}clear(){this._map.clear()}}class Rn{constructor(h,c,m){$(this,"_snapshots");$(this,"_index");$(this,"snapshotName");$(this,"_resources");$(this,"_snapshot");this._resources=h,this._snapshots=c,this._index=m,this._snapshot=c[m],this.snapshotName=c[m].snapshotName}snapshot(){return this._snapshots[this._index]}viewport(){return this._snapshots[this._index].viewport}render(){const h=(M,B,H)=>{if(typeof M=="string"){const F=Cn(M);return H==="STYLE"||H==="style"?In(F):F}if(!M._string)if(Array.isArray(M[0])){const F=B-M[0][0];if(F>=0&&F<=B){const G=zn(this._snapshots[F]),T=M[0][1];T>=0&&T<G.length&&(M._string=h(G[T],F,H))}}else if(typeof M[0]=="string"){const F=[];F.push("<",M[0]);const G=M[0]==="IFRAME"||M[0]==="FRAME";for(const[T,Q]of Object.entries(M[1]||{})){const dt=G&&T.toLowerCase()==="src"?"__playwright_src__":T,rt=T.toLowerCase()==="href"||T.toLowerCase()==="src"?Vt(Q):Q;F.push(" ",dt,'="',Tn(rt),'"')}F.push(">");for(let T=2;T<M.length;T++)F.push(h(M[T],B,M[0]));Un.has(M[0])||F.push("</",M[0],">"),M._string=F.join("")}else M._string="";return M._string},c=this._snapshot;let m=h(c.html,this._index,void 0);return m?(m=(c.doctype?`<!DOCTYPE ${c.doctype}>`:"")+["<style>*,*::before,*::after { visibility: hidden }</style>",`<style>*[__playwright_target__="${this.snapshotName}"] { background-color: #6fa8dc7f; }</style>`,`<script>${Dn()}<\/script>`].join("")+m,{html:m,pageId:c.pageId,frameId:c.frameId,index:this._index}):{html:"",pageId:c.pageId,frameId:c.frameId,index:this._index}}resourceByUrl(h){const c=this._snapshot;let m;for(const I of this._resources){if(typeof I._monotonicTime=="number"&&I._monotonicTime>=c.timestamp)break;if(I._frameref===c.frameId&&I.request.url===h){m=I;break}}if(!m)for(const I of this._resources){if(typeof I._monotonicTime=="number"&&I._monotonicTime>=c.timestamp)break;if(I.request.url===h)return I}if(m){for(const I of c.resourceOverrides)if(h===I.url&&I.sha1){m={...m,response:{...m.response,content:{...m.response.content,_sha1:I.sha1}}};break}}return m}}const Un=new Set(["AREA","BASE","BR","COL","COMMAND","EMBED","HR","IMG","INPUT","KEYGEN","LINK","MENUITEM","META","PARAM","SOURCE","TRACK","WBR"]),Ze={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function Tn(R){return R.replace(/[&<>"']/ug,h=>Ze[h])}function Cn(R){return R.replace(/[&<]/ug,h=>Ze[h])}function zn(R){if(!R._nodes){const h=[],c=m=>{if(typeof m=="string")h.push(m);else if(typeof m[0]=="string"){for(let I=2;I<m.length;I++)c(m[I]);h.push(m)}};c(R.html),R._nodes=h}return R._nodes}function Dn(){function R(h){const c=[],m=[],I=H=>{for(const F of H.querySelectorAll("[__playwright_scroll_top_]"))c.push(F);for(const F of H.querySelectorAll("[__playwright_scroll_left_]"))m.push(F);for(const F of H.querySelectorAll("[__playwright_value_]"))F.value=F.getAttribute("__playwright_value_"),F.removeAttribute("__playwright_value_");for(const F of H.querySelectorAll("[__playwright_checked_]"))F.checked=F.getAttribute("__playwright_checked_")==="true",F.removeAttribute("__playwright_checked_");for(const F of H.querySelectorAll("[__playwright_selected_]"))F.selected=F.getAttribute("__playwright_selected_")==="true",F.removeAttribute("__playwright_selected_");for(const F of H.querySelectorAll("iframe, frame")){const G=F.getAttribute("__playwright_src__");if(!G)F.setAttribute("src",'data:text/html,<body style="background: #ddd"></body>');else{const T=new URL(h(window.location.href));T.searchParams.delete("pointX"),T.searchParams.delete("pointY");const Q=T.pathname.lastIndexOf("/snapshot/");Q!==-1&&(T.pathname=T.pathname.substring(0,Q+1)),T.pathname+=G.substring(1),F.setAttribute("src",T.toString())}}for(const F of H.querySelectorAll("template[__playwright_shadow_root_]")){const G=F,T=G.parentElement.attachShadow({mode:"open"});T.appendChild(G.content),G.remove(),I(T)}if("adoptedStyleSheets"in H){const F=[...H.adoptedStyleSheets];for(const G of H.querySelectorAll("template[__playwright_style_sheet_]")){const T=G,Q=new CSSStyleSheet;Q.replaceSync(T.getAttribute("__playwright_style_sheet_")),F.push(Q)}H.adoptedStyleSheets=F}},M=()=>{window.removeEventListener("load",M);for(const T of c)T.scrollTop=+T.getAttribute("__playwright_scroll_top_"),T.removeAttribute("__playwright_scroll_top_");for(const T of m)T.scrollLeft=+T.getAttribute("__playwright_scroll_left_"),T.removeAttribute("__playwright_scroll_left_");const H=new URL(window.location.href).searchParams,F=H.get("pointX"),G=H.get("pointY");if(F){const T=document.createElement("x-pw-pointer");T.style.position="fixed",T.style.backgroundColor="#f44336",T.style.width="20px",T.style.height="20px",T.style.borderRadius="10px",T.style.margin="-10px 0 0 -10px",T.style.zIndex="2147483647",T.style.left=F+"px",T.style.top=G+"px",document.documentElement.appendChild(T)}document.styleSheets[0].disabled=!0},B=()=>I(document);window.addEventListener("load",M),window.addEventListener("DOMContentLoaded",B)}return`
(${R.toString()})(${Gt.toString()})`}const Ye=["about:","blob:","data:","file:","ftp:","http:","https:","mailto:","sftp:","ws:","wss:"],Ke="http://playwright.bloburl/#";function Vt(R){R.startsWith(Ke)&&(R=R.substring(Ke.length));try{const h=new URL(R);if(h.protocol==="javascript:"||h.protocol==="vbscript:")return"javascript:void(0)";if(!(h.protocol==="blob:")&&Ye.includes(h.protocol))return R;const m="pw-"+h.protocol.slice(0,h.protocol.length-1);return h.protocol="https:",h.hostname=h.hostname?`${m}--${h.hostname}`:m,h.toString()}catch{return R}}const Fn=/url\(['"]?([\w-]+:)\/\//ig;function In(R){return R.replace(Fn,(h,c)=>!(c==="blob:")&&Ye.includes(c)?h:h.replace(c+"//",`https://pw-${c.slice(0,-1)}--`))}function Gt(R){const h=new URL(R);return h.pathname.endsWith("/popout.html")?h.searchParams.get("r"):R}class Mn{constructor(h){$(this,"_snapshotStorage");$(this,"_snapshotIds",new Map);this._snapshotStorage=h}serveSnapshot(h,c,m){const I=this._snapshot(h.substring(9),c);if(!I)return new Response(null,{status:404});const M=I.render();return this._snapshotIds.set(m,I),new Response(M.html,{status:200,headers:{"Content-Type":"text/html"}})}serveSnapshotInfo(h,c){const m=this._snapshot(h.substring(13),c);return this._respondWithJson(m?{viewport:m.viewport(),url:m.snapshot().frameUrl}:{error:"No snapshot found"})}_snapshot(h,c){const m=c.get("name");return this._snapshotStorage.snapshotByName(h.slice(1),m)}_respondWithJson(h){return new Response(JSON.stringify(h),{status:200,headers:{"Cache-Control":"public, max-age=31536000","Content-Type":"application/json"}})}async serveResource(h,c){const m=this._snapshotIds.get(c),I=Ln(h),M=m==null?void 0:m.resourceByUrl(I);if(!M)return new Response(null,{status:404});const B=M.response.content._sha1,H=B?await this._snapshotStorage.resourceContent(B)||new Blob([]):new Blob([]);let F=M.response.content.mimeType;/^text\/|^application\/(javascript|json)/.test(F)&&!F.includes("charset")&&(F=`${F}; charset=utf-8`);const T=new Headers;T.set("Content-Type",F);for(const{name:rt,value:mt}of M.response.headers)T.set(rt,mt);T.delete("Content-Encoding"),T.delete("Access-Control-Allow-Origin"),T.set("Access-Control-Allow-Origin","*"),T.delete("Content-Length"),T.set("Content-Length",String(H.size)),T.set("Cache-Control","public, max-age=31536000");const{status:Q}=M.response,dt=Q===101||Q===204||Q===205||Q===304;return new Response(dt?null:H,{headers:T,status:M.response.status,statusText:M.response.statusText})}}function Ln(R){try{const h=new URL(R);return h.hash="",h.toString()}catch{return R}}var Wn=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function On(R){return R&&R.__esModule&&Object.prototype.hasOwnProperty.call(R,"default")?R.default:R}var Rt={},Bn={get exports(){return Rt},set exports(R){Rt=R}};(function(R,h){(function(c,m){m(h)})(Wn,function(c){const B=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535],H=[96,7,256,0,8,80,0,8,16,84,8,115,82,7,31,0,8,112,0,8,48,0,9,192,80,7,10,0,8,96,0,8,32,0,9,160,0,8,0,0,8,128,0,8,64,0,9,224,80,7,6,0,8,88,0,8,24,0,9,144,83,7,59,0,8,120,0,8,56,0,9,208,81,7,17,0,8,104,0,8,40,0,9,176,0,8,8,0,8,136,0,8,72,0,9,240,80,7,4,0,8,84,0,8,20,85,8,227,83,7,43,0,8,116,0,8,52,0,9,200,81,7,13,0,8,100,0,8,36,0,9,168,0,8,4,0,8,132,0,8,68,0,9,232,80,7,8,0,8,92,0,8,28,0,9,152,84,7,83,0,8,124,0,8,60,0,9,216,82,7,23,0,8,108,0,8,44,0,9,184,0,8,12,0,8,140,0,8,76,0,9,248,80,7,3,0,8,82,0,8,18,85,8,163,83,7,35,0,8,114,0,8,50,0,9,196,81,7,11,0,8,98,0,8,34,0,9,164,0,8,2,0,8,130,0,8,66,0,9,228,80,7,7,0,8,90,0,8,26,0,9,148,84,7,67,0,8,122,0,8,58,0,9,212,82,7,19,0,8,106,0,8,42,0,9,180,0,8,10,0,8,138,0,8,74,0,9,244,80,7,5,0,8,86,0,8,22,192,8,0,83,7,51,0,8,118,0,8,54,0,9,204,81,7,15,0,8,102,0,8,38,0,9,172,0,8,6,0,8,134,0,8,70,0,9,236,80,7,9,0,8,94,0,8,30,0,9,156,84,7,99,0,8,126,0,8,62,0,9,220,82,7,27,0,8,110,0,8,46,0,9,188,0,8,14,0,8,142,0,8,78,0,9,252,96,7,256,0,8,81,0,8,17,85,8,131,82,7,31,0,8,113,0,8,49,0,9,194,80,7,10,0,8,97,0,8,33,0,9,162,0,8,1,0,8,129,0,8,65,0,9,226,80,7,6,0,8,89,0,8,25,0,9,146,83,7,59,0,8,121,0,8,57,0,9,210,81,7,17,0,8,105,0,8,41,0,9,178,0,8,9,0,8,137,0,8,73,0,9,242,80,7,4,0,8,85,0,8,21,80,8,258,83,7,43,0,8,117,0,8,53,0,9,202,81,7,13,0,8,101,0,8,37,0,9,170,0,8,5,0,8,133,0,8,69,0,9,234,80,7,8,0,8,93,0,8,29,0,9,154,84,7,83,0,8,125,0,8,61,0,9,218,82,7,23,0,8,109,0,8,45,0,9,186,0,8,13,0,8,141,0,8,77,0,9,250,80,7,3,0,8,83,0,8,19,85,8,195,83,7,35,0,8,115,0,8,51,0,9,198,81,7,11,0,8,99,0,8,35,0,9,166,0,8,3,0,8,131,0,8,67,0,9,230,80,7,7,0,8,91,0,8,27,0,9,150,84,7,67,0,8,123,0,8,59,0,9,214,82,7,19,0,8,107,0,8,43,0,9,182,0,8,11,0,8,139,0,8,75,0,9,246,80,7,5,0,8,87,0,8,23,192,8,0,83,7,51,0,8,119,0,8,55,0,9,206,81,7,15,0,8,103,0,8,39,0,9,174,0,8,7,0,8,135,0,8,71,0,9,238,80,7,9,0,8,95,0,8,31,0,9,158,84,7,99,0,8,127,0,8,63,0,9,222,82,7,27,0,8,111,0,8,47,0,9,190,0,8,15,0,8,143,0,8,79,0,9,254,96,7,256,0,8,80,0,8,16,84,8,115,82,7,31,0,8,112,0,8,48,0,9,193,80,7,10,0,8,96,0,8,32,0,9,161,0,8,0,0,8,128,0,8,64,0,9,225,80,7,6,0,8,88,0,8,24,0,9,145,83,7,59,0,8,120,0,8,56,0,9,209,81,7,17,0,8,104,0,8,40,0,9,177,0,8,8,0,8,136,0,8,72,0,9,241,80,7,4,0,8,84,0,8,20,85,8,227,83,7,43,0,8,116,0,8,52,0,9,201,81,7,13,0,8,100,0,8,36,0,9,169,0,8,4,0,8,132,0,8,68,0,9,233,80,7,8,0,8,92,0,8,28,0,9,153,84,7,83,0,8,124,0,8,60,0,9,217,82,7,23,0,8,108,0,8,44,0,9,185,0,8,12,0,8,140,0,8,76,0,9,249,80,7,3,0,8,82,0,8,18,85,8,163,83,7,35,0,8,114,0,8,50,0,9,197,81,7,11,0,8,98,0,8,34,0,9,165,0,8,2,0,8,130,0,8,66,0,9,229,80,7,7,0,8,90,0,8,26,0,9,149,84,7,67,0,8,122,0,8,58,0,9,213,82,7,19,0,8,106,0,8,42,0,9,181,0,8,10,0,8,138,0,8,74,0,9,245,80,7,5,0,8,86,0,8,22,192,8,0,83,7,51,0,8,118,0,8,54,0,9,205,81,7,15,0,8,102,0,8,38,0,9,173,0,8,6,0,8,134,0,8,70,0,9,237,80,7,9,0,8,94,0,8,30,0,9,157,84,7,99,0,8,126,0,8,62,0,9,221,82,7,27,0,8,110,0,8,46,0,9,189,0,8,14,0,8,142,0,8,78,0,9,253,96,7,256,0,8,81,0,8,17,85,8,131,82,7,31,0,8,113,0,8,49,0,9,195,80,7,10,0,8,97,0,8,33,0,9,163,0,8,1,0,8,129,0,8,65,0,9,227,80,7,6,0,8,89,0,8,25,0,9,147,83,7,59,0,8,121,0,8,57,0,9,211,81,7,17,0,8,105,0,8,41,0,9,179,0,8,9,0,8,137,0,8,73,0,9,243,80,7,4,0,8,85,0,8,21,80,8,258,83,7,43,0,8,117,0,8,53,0,9,203,81,7,13,0,8,101,0,8,37,0,9,171,0,8,5,0,8,133,0,8,69,0,9,235,80,7,8,0,8,93,0,8,29,0,9,155,84,7,83,0,8,125,0,8,61,0,9,219,82,7,23,0,8,109,0,8,45,0,9,187,0,8,13,0,8,141,0,8,77,0,9,251,80,7,3,0,8,83,0,8,19,85,8,195,83,7,35,0,8,115,0,8,51,0,9,199,81,7,11,0,8,99,0,8,35,0,9,167,0,8,3,0,8,131,0,8,67,0,9,231,80,7,7,0,8,91,0,8,27,0,9,151,84,7,67,0,8,123,0,8,59,0,9,215,82,7,19,0,8,107,0,8,43,0,9,183,0,8,11,0,8,139,0,8,75,0,9,247,80,7,5,0,8,87,0,8,23,192,8,0,83,7,51,0,8,119,0,8,55,0,9,207,81,7,15,0,8,103,0,8,39,0,9,175,0,8,7,0,8,135,0,8,71,0,9,239,80,7,9,0,8,95,0,8,31,0,9,159,84,7,99,0,8,127,0,8,63,0,9,223,82,7,27,0,8,111,0,8,47,0,9,191,0,8,15,0,8,143,0,8,79,0,9,255],F=[80,5,1,87,5,257,83,5,17,91,5,4097,81,5,5,89,5,1025,85,5,65,93,5,16385,80,5,3,88,5,513,84,5,33,92,5,8193,82,5,9,90,5,2049,86,5,129,192,5,24577,80,5,2,87,5,385,83,5,25,91,5,6145,81,5,7,89,5,1537,85,5,97,93,5,24577,80,5,4,88,5,769,84,5,49,92,5,12289,82,5,13,90,5,3073,86,5,193,192,5,24577],G=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],T=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,112,112],Q=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],dt=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],rt=15;function mt(){let n,e,t,r,a,l;function d(y,g,v,C,N,W,s,p,i,o,u){let w,E,k,b,f,x,z,U,A,S,D,O,q,L,j;S=0,f=v;do t[y[g+S]]++,S++,f--;while(f!==0);if(t[0]==v)return s[0]=-1,p[0]=0,0;for(U=p[0],x=1;x<=rt&&t[x]===0;x++);for(z=x,U<x&&(U=x),f=rt;f!==0&&t[f]===0;f--);for(k=f,U>f&&(U=f),p[0]=U,L=1<<x;x<f;x++,L<<=1)if((L-=t[x])<0)return-3;if((L-=t[f])<0)return-3;for(t[f]+=L,l[1]=x=0,S=1,q=2;--f!=0;)l[q]=x+=t[S],q++,S++;f=0,S=0;do(x=y[g+S])!==0&&(u[l[x]++]=f),S++;while(++f<v);for(v=l[k],l[0]=f=0,S=0,b=-1,O=-U,a[0]=0,D=0,j=0;z<=k;z++)for(w=t[z];w--!=0;){for(;z>O+U;){if(b++,O+=U,j=k-O,j=j>U?U:j,(E=1<<(x=z-O))>w+1&&(E-=w+1,q=z,x<j))for(;++x<j&&!((E<<=1)<=t[++q]);)E-=t[q];if(j=1<<x,o[0]+j>1440)return-3;a[b]=D=o[0],o[0]+=j,b!==0?(l[b]=f,r[0]=x,r[1]=U,x=f>>>O-U,r[2]=D-a[b-1]-x,i.set(r,3*(a[b-1]+x))):s[0]=D}for(r[1]=z-O,S>=v?r[0]=192:u[S]<C?(r[0]=u[S]<256?0:96,r[2]=u[S++]):(r[0]=W[u[S]-C]+16+64,r[2]=N[u[S++]-C]),E=1<<z-O,x=f>>>O;x<j;x+=E)i.set(r,3*(D+x));for(x=1<<z-1;f&x;x>>>=1)f^=x;for(f^=x,A=(1<<O)-1;(f&A)!=l[b];)b--,O-=U,A=(1<<O)-1}return L!==0&&k!=1?-5:0}function _(y){let g;for(n||(n=[],e=[],t=new Int32Array(16),r=[],a=new Int32Array(rt),l=new Int32Array(16)),e.length<y&&(e=[]),g=0;g<y;g++)e[g]=0;for(g=0;g<16;g++)t[g]=0;for(g=0;g<3;g++)r[g]=0;a.set(t.subarray(0,rt),0),l.set(t.subarray(0,16),0)}this.inflate_trees_bits=function(y,g,v,C,N){let W;return _(19),n[0]=0,W=d(y,0,19,19,null,null,v,g,C,n,e),W==-3?N.msg="oversubscribed dynamic bit lengths tree":W!=-5&&g[0]!==0||(N.msg="incomplete dynamic bit lengths tree",W=-3),W},this.inflate_trees_dynamic=function(y,g,v,C,N,W,s,p,i){let o;return _(288),n[0]=0,o=d(v,0,y,257,G,T,W,C,p,n,e),o!=0||C[0]===0?(o==-3?i.msg="oversubscribed literal/length tree":o!=-4&&(i.msg="incomplete literal/length tree",o=-3),o):(_(288),o=d(v,y,g,0,Q,dt,s,N,p,n,e),o!=0||N[0]===0&&y>257?(o==-3?i.msg="oversubscribed distance tree":o==-5?(i.msg="incomplete distance tree",o=-3):o!=-4&&(i.msg="empty distance tree with lengths",o=-3),o):0)}}mt.inflate_trees_fixed=function(n,e,t,r){return n[0]=9,e[0]=5,t[0]=H,r[0]=F,0};function Xe(){const n=this;let e,t,r,a,l=0,d=0,_=0,y=0,g=0,v=0,C=0,N=0,W=0,s=0;function p(i,o,u,w,E,k,b,f){let x,z,U,A,S,D,O,q,L,j,ct,lt,P,wt,V,K;O=f.next_in_index,q=f.avail_in,S=b.bitb,D=b.bitk,L=b.write,j=L<b.read?b.read-L-1:b.end-L,ct=B[i],lt=B[o];do{for(;D<20;)q--,S|=(255&f.read_byte(O++))<<D,D+=8;if(x=S&ct,z=u,U=w,K=3*(U+x),(A=z[K])!==0)for(;;){if(S>>=z[K+1],D-=z[K+1],(16&A)!=0){for(A&=15,P=z[K+2]+(S&B[A]),S>>=A,D-=A;D<15;)q--,S|=(255&f.read_byte(O++))<<D,D+=8;for(x=S&lt,z=E,U=k,K=3*(U+x),A=z[K];;){if(S>>=z[K+1],D-=z[K+1],(16&A)!=0){for(A&=15;D<A;)q--,S|=(255&f.read_byte(O++))<<D,D+=8;if(wt=z[K+2]+(S&B[A]),S>>=A,D-=A,j-=P,L>=wt)V=L-wt,L-V>0&&2>L-V?(b.window[L++]=b.window[V++],b.window[L++]=b.window[V++],P-=2):(b.window.set(b.window.subarray(V,V+2),L),L+=2,V+=2,P-=2);else{V=L-wt;do V+=b.end;while(V<0);if(A=b.end-V,P>A){if(P-=A,L-V>0&&A>L-V)do b.window[L++]=b.window[V++];while(--A!=0);else b.window.set(b.window.subarray(V,V+A),L),L+=A,V+=A,A=0;V=0}}if(L-V>0&&P>L-V)do b.window[L++]=b.window[V++];while(--P!=0);else b.window.set(b.window.subarray(V,V+P),L),L+=P,V+=P,P=0;break}if(64&A)return f.msg="invalid distance code",P=f.avail_in-q,P=D>>3<P?D>>3:P,q+=P,O-=P,D-=P<<3,b.bitb=S,b.bitk=D,f.avail_in=q,f.total_in+=O-f.next_in_index,f.next_in_index=O,b.write=L,-3;x+=z[K+2],x+=S&B[A],K=3*(U+x),A=z[K]}break}if(64&A)return 32&A?(P=f.avail_in-q,P=D>>3<P?D>>3:P,q+=P,O-=P,D-=P<<3,b.bitb=S,b.bitk=D,f.avail_in=q,f.total_in+=O-f.next_in_index,f.next_in_index=O,b.write=L,1):(f.msg="invalid literal/length code",P=f.avail_in-q,P=D>>3<P?D>>3:P,q+=P,O-=P,D-=P<<3,b.bitb=S,b.bitk=D,f.avail_in=q,f.total_in+=O-f.next_in_index,f.next_in_index=O,b.write=L,-3);if(x+=z[K+2],x+=S&B[A],K=3*(U+x),(A=z[K])===0){S>>=z[K+1],D-=z[K+1],b.window[L++]=z[K+2],j--;break}}else S>>=z[K+1],D-=z[K+1],b.window[L++]=z[K+2],j--}while(j>=258&&q>=10);return P=f.avail_in-q,P=D>>3<P?D>>3:P,q+=P,O-=P,D-=P<<3,b.bitb=S,b.bitk=D,f.avail_in=q,f.total_in+=O-f.next_in_index,f.next_in_index=O,b.write=L,0}n.init=function(i,o,u,w,E,k){e=0,C=i,N=o,r=u,W=w,a=E,s=k,t=null},n.proc=function(i,o,u){let w,E,k,b,f,x,z,U=0,A=0,S=0;for(S=o.next_in_index,b=o.avail_in,U=i.bitb,A=i.bitk,f=i.write,x=f<i.read?i.read-f-1:i.end-f;;)switch(e){case 0:if(x>=258&&b>=10&&(i.bitb=U,i.bitk=A,o.avail_in=b,o.total_in+=S-o.next_in_index,o.next_in_index=S,i.write=f,u=p(C,N,r,W,a,s,i,o),S=o.next_in_index,b=o.avail_in,U=i.bitb,A=i.bitk,f=i.write,x=f<i.read?i.read-f-1:i.end-f,u!=0)){e=u==1?7:9;break}_=C,t=r,d=W,e=1;case 1:for(w=_;A<w;){if(b===0)return i.bitb=U,i.bitk=A,o.avail_in=b,o.total_in+=S-o.next_in_index,o.next_in_index=S,i.write=f,i.inflate_flush(o,u);u=0,b--,U|=(255&o.read_byte(S++))<<A,A+=8}if(E=3*(d+(U&B[w])),U>>>=t[E+1],A-=t[E+1],k=t[E],k===0){y=t[E+2],e=6;break}if(16&k){g=15&k,l=t[E+2],e=2;break}if(!(64&k)){_=k,d=E/3+t[E+2];break}if(32&k){e=7;break}return e=9,o.msg="invalid literal/length code",u=-3,i.bitb=U,i.bitk=A,o.avail_in=b,o.total_in+=S-o.next_in_index,o.next_in_index=S,i.write=f,i.inflate_flush(o,u);case 2:for(w=g;A<w;){if(b===0)return i.bitb=U,i.bitk=A,o.avail_in=b,o.total_in+=S-o.next_in_index,o.next_in_index=S,i.write=f,i.inflate_flush(o,u);u=0,b--,U|=(255&o.read_byte(S++))<<A,A+=8}l+=U&B[w],U>>=w,A-=w,_=N,t=a,d=s,e=3;case 3:for(w=_;A<w;){if(b===0)return i.bitb=U,i.bitk=A,o.avail_in=b,o.total_in+=S-o.next_in_index,o.next_in_index=S,i.write=f,i.inflate_flush(o,u);u=0,b--,U|=(255&o.read_byte(S++))<<A,A+=8}if(E=3*(d+(U&B[w])),U>>=t[E+1],A-=t[E+1],k=t[E],(16&k)!=0){g=15&k,v=t[E+2],e=4;break}if(!(64&k)){_=k,d=E/3+t[E+2];break}return e=9,o.msg="invalid distance code",u=-3,i.bitb=U,i.bitk=A,o.avail_in=b,o.total_in+=S-o.next_in_index,o.next_in_index=S,i.write=f,i.inflate_flush(o,u);case 4:for(w=g;A<w;){if(b===0)return i.bitb=U,i.bitk=A,o.avail_in=b,o.total_in+=S-o.next_in_index,o.next_in_index=S,i.write=f,i.inflate_flush(o,u);u=0,b--,U|=(255&o.read_byte(S++))<<A,A+=8}v+=U&B[w],U>>=w,A-=w,e=5;case 5:for(z=f-v;z<0;)z+=i.end;for(;l!==0;){if(x===0&&(f==i.end&&i.read!==0&&(f=0,x=f<i.read?i.read-f-1:i.end-f),x===0&&(i.write=f,u=i.inflate_flush(o,u),f=i.write,x=f<i.read?i.read-f-1:i.end-f,f==i.end&&i.read!==0&&(f=0,x=f<i.read?i.read-f-1:i.end-f),x===0)))return i.bitb=U,i.bitk=A,o.avail_in=b,o.total_in+=S-o.next_in_index,o.next_in_index=S,i.write=f,i.inflate_flush(o,u);i.window[f++]=i.window[z++],x--,z==i.end&&(z=0),l--}e=0;break;case 6:if(x===0&&(f==i.end&&i.read!==0&&(f=0,x=f<i.read?i.read-f-1:i.end-f),x===0&&(i.write=f,u=i.inflate_flush(o,u),f=i.write,x=f<i.read?i.read-f-1:i.end-f,f==i.end&&i.read!==0&&(f=0,x=f<i.read?i.read-f-1:i.end-f),x===0)))return i.bitb=U,i.bitk=A,o.avail_in=b,o.total_in+=S-o.next_in_index,o.next_in_index=S,i.write=f,i.inflate_flush(o,u);u=0,i.window[f++]=y,x--,e=0;break;case 7:if(A>7&&(A-=8,b++,S--),i.write=f,u=i.inflate_flush(o,u),f=i.write,x=f<i.read?i.read-f-1:i.end-f,i.read!=i.write)return i.bitb=U,i.bitk=A,o.avail_in=b,o.total_in+=S-o.next_in_index,o.next_in_index=S,i.write=f,i.inflate_flush(o,u);e=8;case 8:return u=1,i.bitb=U,i.bitk=A,o.avail_in=b,o.total_in+=S-o.next_in_index,o.next_in_index=S,i.write=f,i.inflate_flush(o,u);case 9:return u=-3,i.bitb=U,i.bitk=A,o.avail_in=b,o.total_in+=S-o.next_in_index,o.next_in_index=S,i.write=f,i.inflate_flush(o,u);default:return u=-2,i.bitb=U,i.bitk=A,o.avail_in=b,o.total_in+=S-o.next_in_index,o.next_in_index=S,i.write=f,i.inflate_flush(o,u)}},n.free=function(){}}const Kt=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];function Je(n,e){const t=this;let r,a=0,l=0,d=0,_=0;const y=[0],g=[0],v=new Xe;let C=0,N=new Int32Array(4320);const W=new mt;t.bitk=0,t.bitb=0,t.window=new Uint8Array(e),t.end=e,t.read=0,t.write=0,t.reset=function(s,p){p&&(p[0]=0),a==6&&v.free(s),a=0,t.bitk=0,t.bitb=0,t.read=t.write=0},t.reset(n,null),t.inflate_flush=function(s,p){let i,o,u;return o=s.next_out_index,u=t.read,i=(u<=t.write?t.write:t.end)-u,i>s.avail_out&&(i=s.avail_out),i!==0&&p==-5&&(p=0),s.avail_out-=i,s.total_out+=i,s.next_out.set(t.window.subarray(u,u+i),o),o+=i,u+=i,u==t.end&&(u=0,t.write==t.end&&(t.write=0),i=t.write-u,i>s.avail_out&&(i=s.avail_out),i!==0&&p==-5&&(p=0),s.avail_out-=i,s.total_out+=i,s.next_out.set(t.window.subarray(u,u+i),o),o+=i,u+=i),s.next_out_index=o,t.read=u,p},t.proc=function(s,p){let i,o,u,w,E,k,b,f;for(w=s.next_in_index,E=s.avail_in,o=t.bitb,u=t.bitk,k=t.write,b=k<t.read?t.read-k-1:t.end-k;;){let x,z,U,A,S,D,O,q;switch(a){case 0:for(;u<3;){if(E===0)return t.bitb=o,t.bitk=u,s.avail_in=E,s.total_in+=w-s.next_in_index,s.next_in_index=w,t.write=k,t.inflate_flush(s,p);p=0,E--,o|=(255&s.read_byte(w++))<<u,u+=8}switch(i=7&o,C=1&i,i>>>1){case 0:o>>>=3,u-=3,i=7&u,o>>>=i,u-=i,a=1;break;case 1:x=[],z=[],U=[[]],A=[[]],mt.inflate_trees_fixed(x,z,U,A),v.init(x[0],z[0],U[0],0,A[0],0),o>>>=3,u-=3,a=6;break;case 2:o>>>=3,u-=3,a=3;break;case 3:return o>>>=3,u-=3,a=9,s.msg="invalid block type",p=-3,t.bitb=o,t.bitk=u,s.avail_in=E,s.total_in+=w-s.next_in_index,s.next_in_index=w,t.write=k,t.inflate_flush(s,p)}break;case 1:for(;u<32;){if(E===0)return t.bitb=o,t.bitk=u,s.avail_in=E,s.total_in+=w-s.next_in_index,s.next_in_index=w,t.write=k,t.inflate_flush(s,p);p=0,E--,o|=(255&s.read_byte(w++))<<u,u+=8}if((~o>>>16&65535)!=(65535&o))return a=9,s.msg="invalid stored block lengths",p=-3,t.bitb=o,t.bitk=u,s.avail_in=E,s.total_in+=w-s.next_in_index,s.next_in_index=w,t.write=k,t.inflate_flush(s,p);l=65535&o,o=u=0,a=l!==0?2:C!==0?7:0;break;case 2:if(E===0||b===0&&(k==t.end&&t.read!==0&&(k=0,b=k<t.read?t.read-k-1:t.end-k),b===0&&(t.write=k,p=t.inflate_flush(s,p),k=t.write,b=k<t.read?t.read-k-1:t.end-k,k==t.end&&t.read!==0&&(k=0,b=k<t.read?t.read-k-1:t.end-k),b===0)))return t.bitb=o,t.bitk=u,s.avail_in=E,s.total_in+=w-s.next_in_index,s.next_in_index=w,t.write=k,t.inflate_flush(s,p);if(p=0,i=l,i>E&&(i=E),i>b&&(i=b),t.window.set(s.read_buf(w,i),k),w+=i,E-=i,k+=i,b-=i,(l-=i)!=0)break;a=C!==0?7:0;break;case 3:for(;u<14;){if(E===0)return t.bitb=o,t.bitk=u,s.avail_in=E,s.total_in+=w-s.next_in_index,s.next_in_index=w,t.write=k,t.inflate_flush(s,p);p=0,E--,o|=(255&s.read_byte(w++))<<u,u+=8}if(d=i=16383&o,(31&i)>29||(i>>5&31)>29)return a=9,s.msg="too many length or distance symbols",p=-3,t.bitb=o,t.bitk=u,s.avail_in=E,s.total_in+=w-s.next_in_index,s.next_in_index=w,t.write=k,t.inflate_flush(s,p);if(i=258+(31&i)+(i>>5&31),!r||r.length<i)r=[];else for(f=0;f<i;f++)r[f]=0;o>>>=14,u-=14,_=0,a=4;case 4:for(;_<4+(d>>>10);){for(;u<3;){if(E===0)return t.bitb=o,t.bitk=u,s.avail_in=E,s.total_in+=w-s.next_in_index,s.next_in_index=w,t.write=k,t.inflate_flush(s,p);p=0,E--,o|=(255&s.read_byte(w++))<<u,u+=8}r[Kt[_++]]=7&o,o>>>=3,u-=3}for(;_<19;)r[Kt[_++]]=0;if(y[0]=7,i=W.inflate_trees_bits(r,y,g,N,s),i!=0)return(p=i)==-3&&(r=null,a=9),t.bitb=o,t.bitk=u,s.avail_in=E,s.total_in+=w-s.next_in_index,s.next_in_index=w,t.write=k,t.inflate_flush(s,p);_=0,a=5;case 5:for(;i=d,!(_>=258+(31&i)+(i>>5&31));){let L,j;for(i=y[0];u<i;){if(E===0)return t.bitb=o,t.bitk=u,s.avail_in=E,s.total_in+=w-s.next_in_index,s.next_in_index=w,t.write=k,t.inflate_flush(s,p);p=0,E--,o|=(255&s.read_byte(w++))<<u,u+=8}if(i=N[3*(g[0]+(o&B[i]))+1],j=N[3*(g[0]+(o&B[i]))+2],j<16)o>>>=i,u-=i,r[_++]=j;else{for(f=j==18?7:j-14,L=j==18?11:3;u<i+f;){if(E===0)return t.bitb=o,t.bitk=u,s.avail_in=E,s.total_in+=w-s.next_in_index,s.next_in_index=w,t.write=k,t.inflate_flush(s,p);p=0,E--,o|=(255&s.read_byte(w++))<<u,u+=8}if(o>>>=i,u-=i,L+=o&B[f],o>>>=f,u-=f,f=_,i=d,f+L>258+(31&i)+(i>>5&31)||j==16&&f<1)return r=null,a=9,s.msg="invalid bit length repeat",p=-3,t.bitb=o,t.bitk=u,s.avail_in=E,s.total_in+=w-s.next_in_index,s.next_in_index=w,t.write=k,t.inflate_flush(s,p);j=j==16?r[f-1]:0;do r[f++]=j;while(--L!=0);_=f}}if(g[0]=-1,S=[],D=[],O=[],q=[],S[0]=9,D[0]=6,i=d,i=W.inflate_trees_dynamic(257+(31&i),1+(i>>5&31),r,S,D,O,q,N,s),i!=0)return i==-3&&(r=null,a=9),p=i,t.bitb=o,t.bitk=u,s.avail_in=E,s.total_in+=w-s.next_in_index,s.next_in_index=w,t.write=k,t.inflate_flush(s,p);v.init(S[0],D[0],N,O[0],N,q[0]),a=6;case 6:if(t.bitb=o,t.bitk=u,s.avail_in=E,s.total_in+=w-s.next_in_index,s.next_in_index=w,t.write=k,(p=v.proc(t,s,p))!=1)return t.inflate_flush(s,p);if(p=0,v.free(s),w=s.next_in_index,E=s.avail_in,o=t.bitb,u=t.bitk,k=t.write,b=k<t.read?t.read-k-1:t.end-k,C===0){a=0;break}a=7;case 7:if(t.write=k,p=t.inflate_flush(s,p),k=t.write,b=k<t.read?t.read-k-1:t.end-k,t.read!=t.write)return t.bitb=o,t.bitk=u,s.avail_in=E,s.total_in+=w-s.next_in_index,s.next_in_index=w,t.write=k,t.inflate_flush(s,p);a=8;case 8:return p=1,t.bitb=o,t.bitk=u,s.avail_in=E,s.total_in+=w-s.next_in_index,s.next_in_index=w,t.write=k,t.inflate_flush(s,p);case 9:return p=-3,t.bitb=o,t.bitk=u,s.avail_in=E,s.total_in+=w-s.next_in_index,s.next_in_index=w,t.write=k,t.inflate_flush(s,p);default:return p=-2,t.bitb=o,t.bitk=u,s.avail_in=E,s.total_in+=w-s.next_in_index,s.next_in_index=w,t.write=k,t.inflate_flush(s,p)}}},t.free=function(s){t.reset(s,null),t.window=null,N=null},t.set_dictionary=function(s,p,i){t.window.set(s.subarray(p,p+i),0),t.read=t.write=i},t.sync_point=function(){return a==1?1:0}}const it=13,tn=[0,0,255,255];function en(){const n=this;function e(t){return t&&t.istate?(t.total_in=t.total_out=0,t.msg=null,t.istate.mode=7,t.istate.blocks.reset(t,null),0):-2}n.mode=0,n.method=0,n.was=[0],n.need=0,n.marker=0,n.wbits=0,n.inflateEnd=function(t){return n.blocks&&n.blocks.free(t),n.blocks=null,0},n.inflateInit=function(t,r){return t.msg=null,n.blocks=null,r<8||r>15?(n.inflateEnd(t),-2):(n.wbits=r,t.istate.blocks=new Je(t,1<<r),e(t),0)},n.inflate=function(t,r){let a,l;if(!t||!t.istate||!t.next_in)return-2;const d=t.istate;for(r=r==4?-5:0,a=-5;;)switch(d.mode){case 0:if(t.avail_in===0)return a;if(a=r,t.avail_in--,t.total_in++,(15&(d.method=t.read_byte(t.next_in_index++)))!=8){d.mode=it,t.msg="unknown compression method",d.marker=5;break}if(8+(d.method>>4)>d.wbits){d.mode=it,t.msg="invalid window size",d.marker=5;break}d.mode=1;case 1:if(t.avail_in===0)return a;if(a=r,t.avail_in--,t.total_in++,l=255&t.read_byte(t.next_in_index++),((d.method<<8)+l)%31!=0){d.mode=it,t.msg="incorrect header check",d.marker=5;break}if(!(32&l)){d.mode=7;break}d.mode=2;case 2:if(t.avail_in===0)return a;a=r,t.avail_in--,t.total_in++,d.need=(255&t.read_byte(t.next_in_index++))<<24&4278190080,d.mode=3;case 3:if(t.avail_in===0)return a;a=r,t.avail_in--,t.total_in++,d.need+=(255&t.read_byte(t.next_in_index++))<<16&16711680,d.mode=4;case 4:if(t.avail_in===0)return a;a=r,t.avail_in--,t.total_in++,d.need+=(255&t.read_byte(t.next_in_index++))<<8&65280,d.mode=5;case 5:return t.avail_in===0?a:(a=r,t.avail_in--,t.total_in++,d.need+=255&t.read_byte(t.next_in_index++),d.mode=6,2);case 6:return d.mode=it,t.msg="need dictionary",d.marker=0,-2;case 7:if(a=d.blocks.proc(t,a),a==-3){d.mode=it,d.marker=0;break}if(a==0&&(a=r),a!=1)return a;a=r,d.blocks.reset(t,d.was),d.mode=12;case 12:return 1;case it:return-3;default:return-2}},n.inflateSetDictionary=function(t,r,a){let l=0,d=a;if(!t||!t.istate||t.istate.mode!=6)return-2;const _=t.istate;return d>=1<<_.wbits&&(d=(1<<_.wbits)-1,l=a-d),_.blocks.set_dictionary(r,l,d),_.mode=7,0},n.inflateSync=function(t){let r,a,l,d,_;if(!t||!t.istate)return-2;const y=t.istate;if(y.mode!=it&&(y.mode=it,y.marker=0),(r=t.avail_in)===0)return-5;for(a=t.next_in_index,l=y.marker;r!==0&&l<4;)t.read_byte(a)==tn[l]?l++:l=t.read_byte(a)!==0?0:4-l,a++,r--;return t.total_in+=a-t.next_in_index,t.next_in_index=a,t.avail_in=r,y.marker=l,l!=4?-3:(d=t.total_in,_=t.total_out,e(t),t.total_in=d,t.total_out=_,y.mode=7,0)},n.inflateSyncPoint=function(t){return t&&t.istate&&t.istate.blocks?t.istate.blocks.sync_point():-2}}function Qt(){}Qt.prototype={inflateInit:function(n){const e=this;return e.istate=new en,n||(n=15),e.istate.inflateInit(e,n)},inflate:function(n){const e=this;return e.istate?e.istate.inflate(e,n):-2},inflateEnd:function(){const n=this;if(!n.istate)return-2;const e=n.istate.inflateEnd(n);return n.istate=null,e},inflateSync:function(){const n=this;return n.istate?n.istate.inflateSync(n):-2},inflateSetDictionary:function(n,e){const t=this;return t.istate?t.istate.inflateSetDictionary(t,n,e):-2},read_byte:function(n){return this.next_in[n]},read_buf:function(n,e){return this.next_in.subarray(n,n+e)}};const nn={chunkSize:524288,maxWorkers:typeof navigator<"u"&&navigator.hardwareConcurrency||2,terminateWorkerTimeout:5e3,useWebWorkers:!0,workerScripts:void 0},X=Object.assign({},nn);function Zt(n){if(n.baseURL!==void 0&&(X.baseURL=n.baseURL),n.chunkSize!==void 0&&(X.chunkSize=n.chunkSize),n.maxWorkers!==void 0&&(X.maxWorkers=n.maxWorkers),n.terminateWorkerTimeout!==void 0&&(X.terminateWorkerTimeout=n.terminateWorkerTimeout),n.useWebWorkers!==void 0&&(X.useWebWorkers=n.useWebWorkers),n.Deflate!==void 0&&(X.Deflate=n.Deflate),n.Inflate!==void 0&&(X.Inflate=n.Inflate),n.workerScripts!==void 0){if(n.workerScripts.deflate){if(!Array.isArray(n.workerScripts.deflate))throw new Error("workerScripts.deflate must be an array");X.workerScripts||(X.workerScripts={}),X.workerScripts.deflate=n.workerScripts.deflate}if(n.workerScripts.inflate){if(!Array.isArray(n.workerScripts.inflate))throw new Error("workerScripts.inflate must be an array");X.workerScripts||(X.workerScripts={}),X.workerScripts.inflate=n.workerScripts.inflate}}}const Yt="Abort error";function Ut(n,e){if(n&&n.aborted)throw e.flush(),new Error(Yt)}async function Xt(n,e){return e.length&&await n.writeUint8Array(e),e.length}const Jt="HTTP error ",Tt="HTTP Range not supported",Ct="text/plain",zt="GET",rn="bytes";class te{constructor(){this.size=0}init(){this.initialized=!0}}class st extends te{}class bt extends te{writeUint8Array(e){this.size+=e.length}}class ee extends st{constructor(e){super(),this.blob=e,this.size=e.size}async readUint8Array(e,t){if(this.blob.arrayBuffer)return new Uint8Array(await this.blob.slice(e,e+t).arrayBuffer());{const r=new FileReader;return new Promise((a,l)=>{r.onload=d=>a(new Uint8Array(d.target.result)),r.onerror=()=>l(r.error),r.readAsArrayBuffer(this.blob.slice(e,e+t))})}}}class sn extends st{constructor(e,t){super(),this.url=e,this.preventHeadRequest=t.preventHeadRequest,this.useRangeHeader=t.useRangeHeader,this.forceRangeRequests=t.forceRangeRequests,this.options=Object.assign({},t),delete this.options.preventHeadRequest,delete this.options.useRangeHeader,delete this.options.forceRangeRequests,delete this.options.useXHR}async init(){super.init(),await ne(this,Ft,se)}async readUint8Array(e,t){return re(this,e,t,Ft,se)}}class an extends st{constructor(e,t){super(),this.url=e,this.preventHeadRequest=t.preventHeadRequest,this.useRangeHeader=t.useRangeHeader,this.forceRangeRequests=t.forceRangeRequests,this.options=t}async init(){super.init(),await ne(this,It,ae)}async readUint8Array(e,t){return re(this,e,t,It,ae)}}async function ne(n,e,t){if(function(r){if(typeof document<"u"){const a=document.createElement("a");return a.href=r,a.protocol=="http:"||a.protocol=="https:"}return/^https?:\/\//i.test(r)}(n.url)&&(n.useRangeHeader||n.forceRangeRequests)){const r=await e(zt,n,ie(n));if(!n.forceRangeRequests&&r.headers.get("Accept-Ranges")!=rn)throw new Error(Tt);{let a;const l=r.headers.get("Content-Range");if(l){const d=l.trim().split(/\s*\/\s*/);if(d.length){const _=d[1];_&&_!="*"&&(a=Number(_))}}a===void 0?await ce(n,e,t):n.size=a}}else await ce(n,e,t)}async function re(n,e,t,r,a){if(n.useRangeHeader||n.forceRangeRequests){const l=await r(zt,n,ie(n,e,t));if(l.status!=206)throw new Error(Tt);return new Uint8Array(await l.arrayBuffer())}return n.data||await a(n,n.options),new Uint8Array(n.data.subarray(e,e+t))}function ie(n,e=0,t=1){return Object.assign({},Dt(n),{Range:"bytes="+e+"-"+(e+t-1)})}function Dt(n){let e=n.options.headers;if(e)return Symbol.iterator in e?Object.fromEntries(e):e}async function se(n){await oe(n,Ft)}async function ae(n){await oe(n,It)}async function oe(n,e){const t=await e(zt,n,Dt(n));n.data=new Uint8Array(await t.arrayBuffer()),n.size||(n.size=n.data.length)}async function ce(n,e,t){if(n.preventHeadRequest)await t(n,n.options);else{const r=(await e("HEAD",n,Dt(n))).headers.get("Content-Length");r?n.size=Number(r):await t(n,n.options)}}async function Ft(n,{options:e,url:t},r){const a=await fetch(t,Object.assign({},e,{method:n,headers:r}));if(a.status<400)return a;throw new Error(Jt+(a.statusText||a.status))}function It(n,{url:e},t){return new Promise((r,a)=>{const l=new XMLHttpRequest;if(l.addEventListener("load",()=>{if(l.status<400){const d=[];l.getAllResponseHeaders().trim().split(/[\r\n]+/).forEach(_=>{const y=_.trim().split(/\s*:\s*/);y[0]=y[0].trim().replace(/^[a-z]|-[a-z]/g,g=>g.toUpperCase()),d.push(y)}),r({status:l.status,arrayBuffer:()=>l.response,headers:new Map(d)})}else a(new Error(Jt+(l.statusText||l.status)))},!1),l.addEventListener("error",d=>a(d.detail.error),!1),l.open(n,e),t)for(const d of Object.entries(t))l.setRequestHeader(d[0],d[1]);l.responseType="arraybuffer",l.send()})}class le extends st{constructor(e,t={}){super(),this.url=e,t.useXHR?this.reader=new an(e,t):this.reader=new sn(e,t)}set size(e){}get size(){return this.reader.size}async init(){super.init(),await this.reader.init()}async readUint8Array(e,t){return this.reader.readUint8Array(e,t)}}const vt=4294967295,de=33639248,ue=101075792,he=[];for(let n=0;n<256;n++){let e=n;for(let t=0;t<8;t++)1&e?e=e>>>1^3988292384:e>>>=1;he[n]=e}class yt{constructor(e){this.crc=e||-1}append(e){let t=0|this.crc;for(let r=0,a=0|e.length;r<a;r++)t=t>>>8^he[255&(t^e[r])];this.crc=t}get(){return~this.crc}}const J={concat(n,e){if(n.length===0||e.length===0)return n.concat(e);const t=n[n.length-1],r=J.getPartial(t);return r===32?n.concat(e):J._shiftRight(e,r,0|t,n.slice(0,n.length-1))},bitLength(n){const e=n.length;if(e===0)return 0;const t=n[e-1];return 32*(e-1)+J.getPartial(t)},clamp(n,e){if(32*n.length<e)return n;const t=(n=n.slice(0,Math.ceil(e/32))).length;return e&=31,t>0&&e&&(n[t-1]=J.partial(e,n[t-1]&2147483648>>e-1,1)),n},partial:(n,e,t)=>n===32?e:(t?0|e:e<<32-n)+1099511627776*n,getPartial:n=>Math.round(n/1099511627776)||32,_shiftRight(n,e,t,r){for(r===void 0&&(r=[]);e>=32;e-=32)r.push(t),t=0;if(e===0)return r.concat(n);for(let d=0;d<n.length;d++)r.push(t|n[d]>>>e),t=n[d]<<32-e;const a=n.length?n[n.length-1]:0,l=J.getPartial(a);return r.push(J.partial(e+l&31,e+l>32?t:r.pop(),1)),r}},fe={bytes:{fromBits(n){const e=J.bitLength(n)/8,t=new Uint8Array(e);let r;for(let a=0;a<e;a++)!(3&a)&&(r=n[a/4]),t[a]=r>>>24,r<<=8;return t},toBits(n){const e=[];let t,r=0;for(t=0;t<n.length;t++)r=r<<8|n[t],(3&t)==3&&(e.push(r),r=0);return 3&t&&e.push(J.partial(8*(3&t),r)),e}}},pe={sha1:function(n){n?(this._h=n._h.slice(0),this._buffer=n._buffer.slice(0),this._length=n._length):this.reset()}};pe.sha1.prototype={blockSize:512,reset:function(){const n=this;return n._h=this._init.slice(0),n._buffer=[],n._length=0,n},update:function(n){const e=this;typeof n=="string"&&(n=fe.utf8String.toBits(n));const t=e._buffer=J.concat(e._buffer,n),r=e._length,a=e._length=r+J.bitLength(n);if(a>9007199254740991)throw new Error("Cannot hash more than 2^53 - 1 bits");const l=new Uint32Array(t);let d=0;for(let _=e.blockSize+r-(e.blockSize+r&e.blockSize-1);_<=a;_+=e.blockSize)e._block(l.subarray(16*d,16*(d+1))),d+=1;return t.splice(0,16*d),e},finalize:function(){const n=this;let e=n._buffer;const t=n._h;e=J.concat(e,[J.partial(1,1)]);for(let r=e.length+2;15&r;r++)e.push(0);for(e.push(Math.floor(n._length/4294967296)),e.push(0|n._length);e.length;)n._block(e.splice(0,16));return n.reset(),t},_init:[1732584193,4023233417,2562383102,271733878,3285377520],_key:[1518500249,1859775393,2400959708,3395469782],_f:function(n,e,t,r){return n<=19?e&t|~e&r:n<=39?e^t^r:n<=59?e&t|e&r|t&r:n<=79?e^t^r:void 0},_S:function(n,e){return e<<n|e>>>32-n},_block:function(n){const e=this,t=e._h,r=Array(80);for(let g=0;g<16;g++)r[g]=n[g];let a=t[0],l=t[1],d=t[2],_=t[3],y=t[4];for(let g=0;g<=79;g++){g>=16&&(r[g]=e._S(1,r[g-3]^r[g-8]^r[g-14]^r[g-16]));const v=e._S(5,a)+e._f(g,l,d,_)+y+r[g]+e._key[Math.floor(g/20)]|0;y=_,_=d,d=e._S(30,l),l=a,a=v}t[0]=t[0]+a|0,t[1]=t[1]+l|0,t[2]=t[2]+d|0,t[3]=t[3]+_|0,t[4]=t[4]+y|0}};const on={aes:class{constructor(n){const e=this;e._tables=[[[],[],[],[],[]],[[],[],[],[],[]]],e._tables[0][0][0]||e._precompute();const t=e._tables[0][4],r=e._tables[1],a=n.length;let l,d,_,y=1;if(a!==4&&a!==6&&a!==8)throw new Error("invalid aes key size");for(e._key=[d=n.slice(0),_=[]],l=a;l<4*a+28;l++){let g=d[l-1];(l%a==0||a===8&&l%a==4)&&(g=t[g>>>24]<<24^t[g>>16&255]<<16^t[g>>8&255]<<8^t[255&g],l%a==0&&(g=g<<8^g>>>24^y<<24,y=y<<1^283*(y>>7))),d[l]=d[l-a]^g}for(let g=0;l;g++,l--){const v=d[3&g?l:l-4];_[g]=l<=4||g<4?v:r[0][t[v>>>24]]^r[1][t[v>>16&255]]^r[2][t[v>>8&255]]^r[3][t[255&v]]}}encrypt(n){return this._crypt(n,0)}decrypt(n){return this._crypt(n,1)}_precompute(){const n=this._tables[0],e=this._tables[1],t=n[4],r=e[4],a=[],l=[];let d,_,y,g;for(let v=0;v<256;v++)l[(a[v]=v<<1^283*(v>>7))^v]=v;for(let v=d=0;!t[v];v^=_||1,d=l[d]||1){let C=d^d<<1^d<<2^d<<3^d<<4;C=C>>8^255&C^99,t[v]=C,r[C]=v,g=a[y=a[_=a[v]]];let N=16843009*g^65537*y^257*_^16843008*v,W=257*a[C]^16843008*C;for(let s=0;s<4;s++)n[s][v]=W=W<<24^W>>>8,e[s][C]=N=N<<24^N>>>8}for(let v=0;v<5;v++)n[v]=n[v].slice(0),e[v]=e[v].slice(0)}_crypt(n,e){if(n.length!==4)throw new Error("invalid aes block size");const t=this._key[e],r=t.length/4-2,a=[0,0,0,0],l=this._tables[e],d=l[0],_=l[1],y=l[2],g=l[3],v=l[4];let C,N,W,s=n[0]^t[0],p=n[e?3:1]^t[1],i=n[2]^t[2],o=n[e?1:3]^t[3],u=4;for(let w=0;w<r;w++)C=d[s>>>24]^_[p>>16&255]^y[i>>8&255]^g[255&o]^t[u],N=d[p>>>24]^_[i>>16&255]^y[o>>8&255]^g[255&s]^t[u+1],W=d[i>>>24]^_[o>>16&255]^y[s>>8&255]^g[255&p]^t[u+2],o=d[o>>>24]^_[s>>16&255]^y[p>>8&255]^g[255&i]^t[u+3],u+=4,s=C,p=N,i=W;for(let w=0;w<4;w++)a[e?3&-w:w]=v[s>>>24]<<24^v[p>>16&255]<<16^v[i>>8&255]<<8^v[255&o]^t[u++],C=s,s=p,p=i,i=o,o=C;return a}}},cn={ctrGladman:class{constructor(n,e){this._prf=n,this._initIv=e,this._iv=e}reset(){this._iv=this._initIv}update(n){return this.calculate(this._prf,n,this._iv)}incWord(n){if((n>>24&255)==255){let e=n>>16&255,t=n>>8&255,r=255&n;e===255?(e=0,t===255?(t=0,r===255?r=0:++r):++t):++e,n=0,n+=e<<16,n+=t<<8,n+=r}else n+=1<<24;return n}incCounter(n){(n[0]=this.incWord(n[0]))===0&&(n[1]=this.incWord(n[1]))}calculate(n,e,t){let r;if(!(r=e.length))return[];const a=J.bitLength(e);for(let l=0;l<r;l+=4){this.incCounter(t);const d=n.encrypt(t);e[l]^=d[0],e[l+1]^=d[1],e[l+2]^=d[2],e[l+3]^=d[3]}return J.clamp(e,a)}}},ln={hmacSha1:class{constructor(n){const e=this,t=e._hash=pe.sha1,r=[[],[]],a=t.prototype.blockSize/32;e._baseHash=[new t,new t],n.length>a&&(n=t.hash(n));for(let l=0;l<a;l++)r[0][l]=909522486^n[l],r[1][l]=1549556828^n[l];e._baseHash[0].update(r[0]),e._baseHash[1].update(r[1]),e._resultHash=new t(e._baseHash[0])}reset(){const n=this;n._resultHash=new n._hash(n._baseHash[0]),n._updated=!1}update(n){this._updated=!0,this._resultHash.update(n)}digest(){const n=this,e=n._resultHash.finalize(),t=new n._hash(n._baseHash[1]).update(e).finalize();return n.reset(),t}}},Mt="Invalid pasword",ft=16,_e={name:"PBKDF2"},dn=Object.assign({hash:{name:"HMAC"}},_e),un=Object.assign({iterations:1e3,hash:{name:"SHA-1"}},_e),hn=["deriveBits"],xt=[8,12,16],kt=[16,24,32],at=10,we=[0,0,0,0],nt=fe.bytes,ge=on.aes,me=cn.ctrGladman,be=ln.hmacSha1;class fn{constructor(e,t,r){Object.assign(this,{password:e,signed:t,strength:r-1,pendingInput:new Uint8Array(0)})}async append(e){const t=this;if(t.password){const r=tt(e,0,xt[t.strength]+2);await async function(a,l,d){await xe(a,d,tt(l,0,xt[a.strength]));const _=tt(l,xt[a.strength]),y=a.keys.passwordVerification;if(y[0]!=_[0]||y[1]!=_[1])throw new Error(Mt)}(t,r,t.password),t.password=null,t.aesCtrGladman=new me(new ge(t.keys.key),Array.from(we)),t.hmac=new be(t.keys.authentication),e=tt(e,xt[t.strength]+2)}return ye(t,e,new Uint8Array(e.length-at-(e.length-at)%ft),0,at,!0)}flush(){const e=this,t=e.pendingInput,r=tt(t,0,t.length-at),a=tt(t,t.length-at);let l=new Uint8Array(0);if(r.length){const _=nt.toBits(r);e.hmac.update(_);const y=e.aesCtrGladman.update(_);l=nt.fromBits(y)}let d=!0;if(e.signed){const _=tt(nt.fromBits(e.hmac.digest()),0,at);for(let y=0;y<at;y++)_[y]!=a[y]&&(d=!1)}return{valid:d,data:l}}}class pn{constructor(e,t){Object.assign(this,{password:e,strength:t-1,pendingInput:new Uint8Array(0)})}async append(e){const t=this;let r=new Uint8Array(0);t.password&&(r=await async function(l,d){const _=crypto.getRandomValues(new Uint8Array(xt[l.strength]));return await xe(l,d,_),Lt(_,l.keys.passwordVerification)}(t,t.password),t.password=null,t.aesCtrGladman=new me(new ge(t.keys.key),Array.from(we)),t.hmac=new be(t.keys.authentication));const a=new Uint8Array(r.length+e.length-e.length%ft);return a.set(r,0),ye(t,e,a,r.length,0)}flush(){const e=this;let t=new Uint8Array(0);if(e.pendingInput.length){const a=e.aesCtrGladman.update(nt.toBits(e.pendingInput));e.hmac.update(a),t=nt.fromBits(a)}const r=tt(nt.fromBits(e.hmac.digest()),0,at);return{data:Lt(t,r),signature:r}}}function ye(n,e,t,r,a,l){const d=e.length-a;let _;for(n.pendingInput.length&&(e=Lt(n.pendingInput,e),t=function(y,g){if(g&&g>y.length){const v=y;(y=new Uint8Array(g)).set(v,0)}return y}(t,d-d%ft)),_=0;_<=d-ft;_+=ft){const y=nt.toBits(tt(e,_,_+ft));l&&n.hmac.update(y);const g=n.aesCtrGladman.update(y);l||n.hmac.update(g),t.set(nt.fromBits(g),_+r)}return n.pendingInput=tt(e,_),t}async function xe(n,e,t){const r=function(_){if(typeof TextEncoder>"u"){_=unescape(encodeURIComponent(_));const y=new Uint8Array(_.length);for(let g=0;g<y.length;g++)y[g]=_.charCodeAt(g);return y}return new TextEncoder().encode(_)}(e),a=await crypto.subtle.importKey("raw",r,dn,!1,hn),l=await crypto.subtle.deriveBits(Object.assign({salt:t},un),a,8*(2*kt[n.strength]+2)),d=new Uint8Array(l);n.keys={key:nt.toBits(tt(d,0,kt[n.strength])),authentication:nt.toBits(tt(d,kt[n.strength],2*kt[n.strength])),passwordVerification:tt(d,2*kt[n.strength])}}function Lt(n,e){let t=n;return n.length+e.length&&(t=new Uint8Array(n.length+e.length),t.set(n,0),t.set(e,n.length)),t}function tt(n,e,t){return n.subarray(e,t)}const Et=12;class _n{constructor(e,t){Object.assign(this,{password:e,passwordVerification:t}),Ee(this,e)}append(e){const t=this;if(t.password){const r=ke(t,e.subarray(0,Et));if(t.password=null,r[11]!=t.passwordVerification)throw new Error(Mt);e=e.subarray(Et)}return ke(t,e)}flush(){return{valid:!0,data:new Uint8Array(0)}}}class wn{constructor(e,t){Object.assign(this,{password:e,passwordVerification:t}),Ee(this,e)}append(e){const t=this;let r,a;if(t.password){t.password=null;const l=crypto.getRandomValues(new Uint8Array(Et));l[11]=t.passwordVerification,r=new Uint8Array(e.length+l.length),r.set(ve(t,l),0),a=Et}else r=new Uint8Array(e.length),a=0;return r.set(ve(t,e),a),r}flush(){return{data:new Uint8Array(0)}}}function ke(n,e){const t=new Uint8Array(e.length);for(let r=0;r<e.length;r++)t[r]=Se(n)^e[r],Wt(n,t[r]);return t}function ve(n,e){const t=new Uint8Array(e.length);for(let r=0;r<e.length;r++)t[r]=Se(n)^e[r],Wt(n,e[r]);return t}function Ee(n,e){n.keys=[305419896,591751049,878082192],n.crcKey0=new yt(n.keys[0]),n.crcKey2=new yt(n.keys[2]);for(let t=0;t<e.length;t++)Wt(n,e.charCodeAt(t))}function Wt(n,e){n.crcKey0.append([e]),n.keys[0]=~n.crcKey0.get(),n.keys[1]=Re(n.keys[1]+Ae(n.keys[0])),n.keys[1]=Re(Math.imul(n.keys[1],134775813)+1),n.crcKey2.append([n.keys[1]>>>24]),n.keys[2]=~n.crcKey2.get()}function Se(n){const e=2|n.keys[2];return Ae(Math.imul(e,1^e)>>>8)}function Ae(n){return 255&n}function Re(n){return 4294967295&n}const Ue="inflate",Ot="Invalid signature";class gn{constructor(e,{signature:t,password:r,signed:a,compressed:l,zipCrypto:d,passwordVerification:_,encryptionStrength:y},{chunkSize:g}){const v=Boolean(r);Object.assign(this,{signature:t,encrypted:v,signed:a,compressed:l,inflate:l&&new e({chunkSize:g}),crc32:a&&new yt,zipCrypto:d,decrypt:v&&d?new _n(r,_):new fn(r,a,y)})}async append(e){const t=this;return t.encrypted&&e.length&&(e=await t.decrypt.append(e)),t.compressed&&e.length&&(e=await t.inflate.append(e)),(!t.encrypted||t.zipCrypto)&&t.signed&&e.length&&t.crc32.append(e),e}async flush(){const e=this;let t,r=new Uint8Array(0);if(e.encrypted){const a=e.decrypt.flush();if(!a.valid)throw new Error(Ot);r=a.data}if((!e.encrypted||e.zipCrypto)&&e.signed){const a=new DataView(new Uint8Array(4).buffer);if(t=e.crc32.get(),a.setUint32(0,t),e.signature!=a.getUint32(0,!1))throw new Error(Ot)}return e.compressed&&(r=await e.inflate.append(r)||new Uint8Array(0),await e.inflate.flush()),{data:r,signature:t}}}class mn{constructor(e,{encrypted:t,signed:r,compressed:a,level:l,zipCrypto:d,password:_,passwordVerification:y,encryptionStrength:g},{chunkSize:v}){Object.assign(this,{encrypted:t,signed:r,compressed:a,deflate:a&&new e({level:l||5,chunkSize:v}),crc32:r&&new yt,zipCrypto:d,encrypt:t&&d?new wn(_,y):new pn(_,g)})}async append(e){const t=this;let r=e;return t.compressed&&e.length&&(r=await t.deflate.append(e)),t.encrypted&&r.length&&(r=await t.encrypt.append(r)),(!t.encrypted||t.zipCrypto)&&t.signed&&e.length&&t.crc32.append(e),r}async flush(){const e=this;let t,r=new Uint8Array(0);if(e.compressed&&(r=await e.deflate.flush()||new Uint8Array(0)),e.encrypted){r=await e.encrypt.append(r);const a=e.encrypt.flush();t=a.signature;const l=new Uint8Array(r.length+a.data.length);l.set(r,0),l.set(a.data,r.length),r=l}return e.encrypted&&!e.zipCrypto||!e.signed||(t=e.crc32.get()),{data:r,signature:t}}}const Te="init",Ce="append",Bt="flush",bn="message";let ze=!0;var Nt=(n,e,t,r,a,l,d)=>(Object.assign(n,{busy:!0,codecConstructor:e,options:Object.assign({},t),scripts:d,terminate(){n.worker&&!n.busy&&(n.worker.terminate(),n.interface=null)},onTaskFinished(){n.busy=!1,a(n)}}),l?function(_,y){let g;const v={type:"module"};if(!_.interface){if(ze)try{_.worker=C({},y.baseURL)}catch{ze=!1,_.worker=C(v,y.baseURL)}else _.worker=C(v,y.baseURL);_.worker.addEventListener(bn,s,!1),_.interface={append:p=>N({type:Ce,data:p}),flush:()=>N({type:Bt})}}return _.interface;function C(p,i){let o;try{o=new URL(_.scripts[0],i)}catch{o=_.scripts[0]}return new Worker(o,p)}async function N(p){if(!g){const i=_.options,o=_.scripts.slice(1);await W({scripts:o,type:Te,options:i,config:{chunkSize:y.chunkSize}})}return W(p)}function W(p){const i=_.worker,o=new Promise((u,w)=>g={resolve:u,reject:w});try{if(p.data)try{p.data=p.data.buffer,i.postMessage(p,[p.data])}catch{i.postMessage(p)}else i.postMessage(p)}catch(u){g.reject(u),g=null,_.onTaskFinished()}return o}function s(p){const i=p.data;if(g){const o=i.error,u=i.type;if(o){const w=new Error(o.message);w.stack=o.stack,g.reject(w),g=null,_.onTaskFinished()}else if(u==Te||u==Bt||u==Ce){const w=i.data;u==Bt?(g.resolve({data:new Uint8Array(w),signature:i.signature}),g=null,_.onTaskFinished()):g.resolve(w&&new Uint8Array(w))}}}}(n,r):function(_,y){const g=function(v,C,N){return C.codecType.startsWith("deflate")?new mn(v,C,N):C.codecType.startsWith(Ue)?new gn(v,C,N):void 0}(_.codecConstructor,_.options,y);return{async append(v){try{return await g.append(v)}catch(C){throw _.onTaskFinished(),C}},async flush(){try{return await g.flush()}finally{_.onTaskFinished()}}}}(n,r));let ut=[],Pt=[];function De(n){n.terminateTimeout&&(clearTimeout(n.terminateTimeout),n.terminateTimeout=null)}const yn="\0☺☻♥♦♣♠•◘○◙♂♀♪♫☼►◄↕‼¶§▬↨↑↓→←∟↔▲▼ !\"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}~⌂ÇüéâäàåçêëèïîìÄÅÉæÆôöòûùÿÖÜ¢£¥₧ƒáíóúñÑªº¿⌐¬½¼¡«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ ".split("");async function Ht(n,e){if(e&&e.trim().toLowerCase()=="cp437")return(t=>{let r="";for(let a=0;a<t.length;a++)r+=yn[t[a]];return r})(n);if(typeof TextDecoder>"u"){const t=new FileReader;return new Promise((r,a)=>{t.onload=l=>r(l.target.result),t.onerror=()=>a(t.error),t.readAsText(new Blob([n]))})}return new TextDecoder(e).decode(n)}const xn=["filename","rawFilename","directory","encrypted","compressedSize","uncompressedSize","lastModDate","rawLastModDate","comment","rawComment","signature","extraField","rawExtraField","bitFlag","extraFieldZip64","extraFieldUnicodePath","extraFieldUnicodeComment","extraFieldAES","filenameUTF8","commentUTF8","offset","zip64","compressionMethod","extraFieldNTFS","lastAccessDate","creationDate","extraFieldExtendedTimestamp","version","versionMadeBy","msDosCompatible","internalFileAttribute","externalFileAttribute"];class Fe{constructor(e){xn.forEach(t=>this[t]=e[t])}}const St="File format is not recognized",Ie="End of central directory not found",Me="End of Zip64 central directory not found",Le="End of Zip64 central directory locator not found",We="Central directory header not found",Oe="Local file header not found",Be="Zip64 extra field not found",Ne="File contains encrypted entry",Pe="Encryption method not supported",jt="Compression method not supported",He="utf-8",je="cp437",qe=["uncompressedSize","compressedSize","offset"];class kn{constructor(e,t,r){Object.assign(this,{reader:e,config:t,options:r})}async getData(e,t,r={}){const a=this,{reader:l,offset:d,extraFieldAES:_,compressionMethod:y,config:g,bitFlag:v,signature:C,rawLastModDate:N,compressedSize:W}=a,s=a.localDirectory={};l.initialized||await l.init();let p=await ot(l,d,30);const i=Y(p);let o=pt(a,r,"password");if(o=o&&o.length&&o,_&&_.originalCompressionMethod!=99)throw new Error(jt);if(y!=0&&y!=8)throw new Error(jt);if(Z(i,0)!=67324752)throw new Error(Oe);Ve(s,i,4),p=await ot(l,d,30+s.filenameLength+s.extraFieldLength),s.rawExtraField=p.subarray(30+s.filenameLength),await Ge(a,s,i,4),t.lastAccessDate=s.lastAccessDate,t.creationDate=s.creationDate;const u=a.encrypted&&s.encrypted,w=u&&!_;if(u){if(!w&&_.strength===void 0)throw new Error(Pe);if(!o)throw new Error(Ne)}const E=await function(f,x,z){const U=!(!x.compressed&&!x.signed&&!x.encrypted)&&(x.useWebWorkers||x.useWebWorkers===void 0&&z.useWebWorkers),A=U&&z.workerScripts?z.workerScripts[x.codecType]:[];if(ut.length<z.maxWorkers){const D={};return ut.push(D),Nt(D,f,x,z,S,U,A)}{const D=ut.find(O=>!O.busy);return D?(De(D),Nt(D,f,x,z,S,U,A)):new Promise(O=>Pt.push({resolve:O,codecConstructor:f,options:x,webWorker:U,scripts:A}))}function S(D){if(Pt.length){const[{resolve:O,codecConstructor:q,options:L,webWorker:j,scripts:ct}]=Pt.splice(0,1);O(Nt(D,q,L,z,S,j,ct))}else D.worker?(De(D),Number.isFinite(z.terminateWorkerTimeout)&&z.terminateWorkerTimeout>=0&&(D.terminateTimeout=setTimeout(()=>{ut=ut.filter(O=>O!=D),D.terminate()},z.terminateWorkerTimeout))):ut=ut.filter(O=>O!=D)}}(g.Inflate,{codecType:Ue,password:o,zipCrypto:w,encryptionStrength:_&&_.strength,signed:pt(a,r,"checkSignature"),passwordVerification:w&&(v.dataDescriptor?N>>>8&255:C>>>24&255),signature:C,compressed:y!=0,encrypted:u,useWebWorkers:pt(a,r,"useWebWorkers")},g);e.initialized||await e.init();const k=pt(a,r,"signal"),b=d+30+s.filenameLength+s.extraFieldLength;return await async function(f,x,z,U,A,S,D){const O=Math.max(S.chunkSize,64);return async function q(L=0,j=0){const ct=D.signal;if(L<A){Ut(ct,f);const lt=await x.readUint8Array(L+U,Math.min(O,A-L)),P=lt.length;Ut(ct,f);const wt=await f.append(lt);if(Ut(ct,f),j+=await Xt(z,wt),D.onprogress)try{D.onprogress(L+P,A)}catch{}return q(L+O,j)}{const lt=await f.flush();return j+=await Xt(z,lt.data),{signature:lt.signature,length:j}}}()}(E,l,e,b,W,g,{onprogress:r.onprogress,signal:k}),e.getData()}}function Ve(n,e,t){const r=n.rawBitFlag=et(e,t+2),a=(1&r)==1,l=Z(e,t+6);Object.assign(n,{encrypted:a,version:et(e,t),bitFlag:{level:(6&r)>>1,dataDescriptor:(8&r)==8,languageEncodingFlag:(2048&r)==2048},rawLastModDate:l,lastModDate:vn(l),filenameLength:et(e,t+22),extraFieldLength:et(e,t+24)})}async function Ge(n,e,t,r){const a=e.rawExtraField,l=e.extraField=new Map,d=Y(new Uint8Array(a));let _=0;try{for(;_<a.length;){const p=et(d,_),i=et(d,_+2);l.set(p,{type:p,data:a.slice(_+4,_+4+i)}),_+=4+i}}catch{}const y=et(t,r+4);e.signature=Z(t,r+10),e.uncompressedSize=Z(t,r+18),e.compressedSize=Z(t,r+14);const g=l.get(1);g&&(function(p,i){i.zip64=!0;const o=Y(p.data);p.values=[];for(let w=0;w<Math.floor(p.data.length/8);w++)p.values.push(At(o,0+8*w));const u=qe.filter(w=>i[w]==vt);for(let w=0;w<u.length;w++)p[u[w]]=p.values[w];qe.forEach(w=>{if(i[w]==vt){if(p[w]===void 0)throw new Error(Be);i[w]=p[w]}})}(g,e),e.extraFieldZip64=g);const v=l.get(28789);v&&(await $e(v,"filename","rawFilename",e,n),e.extraFieldUnicodePath=v);const C=l.get(25461);C&&(await $e(C,"comment","rawComment",e,n),e.extraFieldUnicodeComment=C);const N=l.get(39169);N?(function(p,i,o){const u=Y(p.data);p.vendorVersion=_t(u,0),p.vendorId=_t(u,2);const w=_t(u,4);p.strength=w,p.originalCompressionMethod=o,i.compressionMethod=p.compressionMethod=et(u,5)}(N,e,y),e.extraFieldAES=N):e.compressionMethod=y;const W=l.get(10);W&&(function(p,i){const o=Y(p.data);let u,w=4;try{for(;w<p.data.length&&!u;){const E=et(o,w),k=et(o,w+2);E==1&&(u=p.data.slice(w+4,w+4+k)),w+=4+k}}catch{}try{if(u&&u.length==24){const E=Y(u),k=E.getBigUint64(0,!0),b=E.getBigUint64(8,!0),f=E.getBigUint64(16,!0);Object.assign(p,{rawLastModDate:k,rawLastAccessDate:b,rawCreationDate:f});const x=qt(k),z=qt(b),U={lastModDate:x,lastAccessDate:z,creationDate:qt(f)};Object.assign(p,U),Object.assign(i,U)}}catch{}}(W,e),e.extraFieldNTFS=W);const s=l.get(21589);s&&(function(p,i){const o=Y(p.data),u=_t(o,0),w=[],E=[];(1&u)==1&&(w.push("lastModDate"),E.push("rawLastModDate")),(2&u)==2&&(w.push("lastAccessDate"),E.push("rawLastAccessDate")),(4&u)==4&&(w.push("creationDate"),E.push("rawCreationDate"));let k=1;w.forEach((b,f)=>{if(p.data.length>=k+4){const x=Z(o,k);i[b]=p[b]=new Date(1e3*x);const z=E[f];p[z]=x}k+=4})}(s,e),e.extraFieldExtendedTimestamp=s)}async function $e(n,e,t,r,a){const l=Y(n.data);n.version=_t(l,0),n.signature=Z(l,1);const d=new yt;d.append(a[t]);const _=Y(new Uint8Array(4));_.setUint32(0,d.get(),!0),n[e]=await Ht(n.data.subarray(5)),n.valid=!a.bitFlag.languageEncodingFlag&&n.signature==Z(_,0),n.valid&&(r[e]=n[e],r[e+"UTF8"]=!0)}function pt(n,e,t){return e[t]===void 0?n.options[t]:e[t]}function vn(n){const e=(4294901760&n)>>16,t=65535&n;try{return new Date(1980+((65024&e)>>9),((480&e)>>5)-1,31&e,(63488&t)>>11,(2016&t)>>5,2*(31&t),0)}catch{}}function qt(n){return new Date(Number(n/BigInt(1e4)-BigInt(116444736e5)))}function _t(n,e){return n.getUint8(e)}function et(n,e){return n.getUint16(e,!0)}function Z(n,e){return n.getUint32(e,!0)}function At(n,e){return Number(n.getBigUint64(e,!0))}function Y(n){return new DataView(n.buffer)}function ot(n,e,t){return n.readUint8Array(e,t)}Zt({Inflate:function(n){const e=new Qt,t=n&&n.chunkSize?Math.floor(2*n.chunkSize):131072,r=new Uint8Array(t);let a=!1;e.inflateInit(),e.next_out=r,this.append=function(l,d){const _=[];let y,g,v=0,C=0,N=0;if(l.length!==0){e.next_in_index=0,e.next_in=l,e.avail_in=l.length;do{if(e.next_out_index=0,e.avail_out=t,e.avail_in!==0||a||(e.next_in_index=0,a=!0),y=e.inflate(0),a&&y===-5){if(e.avail_in!==0)throw new Error("inflating: bad input")}else if(y!==0&&y!==1)throw new Error("inflating: "+e.msg);if((a||y===1)&&e.avail_in===l.length)throw new Error("inflating: bad input");e.next_out_index&&(e.next_out_index===t?_.push(new Uint8Array(r)):_.push(r.slice(0,e.next_out_index))),N+=e.next_out_index,d&&e.next_in_index>0&&e.next_in_index!=v&&(d(e.next_in_index),v=e.next_in_index)}while(e.avail_in>0||e.avail_out===0);return _.length>1?(g=new Uint8Array(N),_.forEach(function(W){g.set(W,C),C+=W.length})):g=_[0]||new Uint8Array(0),g}},this.flush=function(){e.inflateEnd()}}}),c.BlobReader=ee,c.BlobWriter=class extends bt{constructor(n){super(),this.contentType=n,this.arrayBuffers=[]}async writeUint8Array(n){super.writeUint8Array(n),this.arrayBuffers.push(n.buffer)}getData(){return this.blob||(this.blob=new Blob(this.arrayBuffers,{type:this.contentType})),this.blob}},c.Data64URIReader=class extends st{constructor(n){super(),this.dataURI=n;let e=n.length;for(;n.charAt(e-1)=="=";)e--;this.dataStart=n.indexOf(",")+1,this.size=Math.floor(.75*(e-this.dataStart))}async readUint8Array(n,e){const t=new Uint8Array(e),r=4*Math.floor(n/3),a=atob(this.dataURI.substring(r+this.dataStart,4*Math.ceil((n+e)/3)+this.dataStart)),l=n-3*Math.floor(r/4);for(let d=l;d<l+e;d++)t[d-l]=a.charCodeAt(d);return t}},c.Data64URIWriter=class extends bt{constructor(n){super(),this.data="data:"+(n||"")+";base64,",this.pending=[]}async writeUint8Array(n){super.writeUint8Array(n);let e=0,t=this.pending;const r=this.pending.length;for(this.pending="",e=0;e<3*Math.floor((r+n.length)/3)-r;e++)t+=String.fromCharCode(n[e]);for(;e<n.length;e++)this.pending+=String.fromCharCode(n[e]);t.length>2?this.data+=btoa(t):this.pending=t}getData(){return this.data+btoa(this.pending)}},c.ERR_ABORT=Yt,c.ERR_BAD_FORMAT=St,c.ERR_CENTRAL_DIRECTORY_NOT_FOUND=We,c.ERR_ENCRYPTED=Ne,c.ERR_EOCDR_LOCATOR_ZIP64_NOT_FOUND=Le,c.ERR_EOCDR_NOT_FOUND=Ie,c.ERR_EOCDR_ZIP64_NOT_FOUND=Me,c.ERR_EXTRAFIELD_ZIP64_NOT_FOUND=Be,c.ERR_HTTP_RANGE=Tt,c.ERR_INVALID_PASSWORD=Mt,c.ERR_INVALID_SIGNATURE=Ot,c.ERR_LOCAL_FILE_HEADER_NOT_FOUND=Oe,c.ERR_UNSUPPORTED_COMPRESSION=jt,c.ERR_UNSUPPORTED_ENCRYPTION=Pe,c.HttpRangeReader=class extends le{constructor(n,e={}){e.useRangeHeader=!0,super(n,e)}},c.HttpReader=le,c.Reader=st,c.TextReader=class extends st{constructor(n){super(),this.blobReader=new ee(new Blob([n],{type:Ct}))}async init(){super.init(),this.blobReader.init(),this.size=this.blobReader.size}async readUint8Array(n,e){return this.blobReader.readUint8Array(n,e)}},c.TextWriter=class extends bt{constructor(n){super(),this.encoding=n,this.blob=new Blob([],{type:Ct})}async writeUint8Array(n){super.writeUint8Array(n),this.blob=new Blob([this.blob,n.buffer],{type:Ct})}getData(){if(this.blob.text)return this.blob.text();{const n=new FileReader;return new Promise((e,t)=>{n.onload=r=>e(r.target.result),n.onerror=()=>t(n.error),n.readAsText(this.blob,this.encoding)})}}},c.Uint8ArrayReader=class extends st{constructor(n){super(),this.array=n,this.size=n.length}async readUint8Array(n,e){return this.array.slice(n,n+e)}},c.Uint8ArrayWriter=class extends bt{constructor(){super(),this.array=new Uint8Array(0)}async writeUint8Array(n){super.writeUint8Array(n);const e=this.array;this.array=new Uint8Array(e.length+n.length),this.array.set(e),this.array.set(n,e.length)}getData(){return this.array}},c.Writer=bt,c.ZipReader=class{constructor(n,e={}){Object.assign(this,{reader:n,options:e,config:X})}async getEntries(n={}){const e=this,t=e.reader;if(t.initialized||await t.init(),t.size<22)throw new Error(St);const r=await async function(W,s,p,i,o){const u=new Uint8Array(4);(function(k,b,f){k.setUint32(b,f,!0)})(Y(u),0,s);const w=i+o;return await E(i)||await E(Math.min(w,p));async function E(k){const b=p-k,f=await ot(W,b,k);for(let x=f.length-i;x>=0;x--)if(f[x]==u[0]&&f[x+1]==u[1]&&f[x+2]==u[2]&&f[x+3]==u[3])return{offset:b+x,buffer:f.slice(x,x+i).buffer}}}(t,101010256,t.size,22,1048560);if(!r)throw new Error(Ie);const a=Y(r);let l=Z(a,12),d=Z(a,16),_=et(a,8),y=0;if(d==vt||l==vt||_==65535){const W=Y(await ot(t,r.offset-20,20));if(Z(W,0)!=117853008)throw new Error(Me);d=At(W,8);let s=await ot(t,d,56),p=Y(s);const i=r.offset-20-56;if(Z(p,0)!=ue&&d!=i){const o=d;d=i,y=d-o,s=await ot(t,d,56),p=Y(s)}if(Z(p,0)!=ue)throw new Error(Le);_=At(p,32),l=At(p,40),d-=l}if(d<0||d>=t.size)throw new Error(St);let g=0,v=await ot(t,d,l),C=Y(v);if(l){const W=r.offset-l;if(Z(C,g)!=de&&d!=W){const s=d;d=W,y=d-s,v=await ot(t,d,l),C=Y(v)}}if(d<0||d>=t.size)throw new Error(St);const N=[];for(let W=0;W<_;W++){const s=new kn(t,e.config,e.options);if(Z(C,g)!=de)throw new Error(We);Ve(s,C,g+6);const p=Boolean(s.bitFlag.languageEncodingFlag),i=g+46,o=i+s.filenameLength,u=o+s.extraFieldLength,w=et(C,g+4),E=(0&w)==0;Object.assign(s,{versionMadeBy:w,msDosCompatible:E,compressedSize:0,uncompressedSize:0,commentLength:et(C,g+32),directory:E&&(16&_t(C,g+38))==16,offset:Z(C,g+42)+y,internalFileAttribute:Z(C,g+34),externalFileAttribute:Z(C,g+38),rawFilename:v.subarray(i,o),filenameUTF8:p,commentUTF8:p,rawExtraField:v.subarray(o,u)});const k=u+s.commentLength;s.rawComment=v.subarray(u,k);const b=pt(e,n,"filenameEncoding"),f=pt(e,n,"commentEncoding"),[x,z]=await Promise.all([Ht(s.rawFilename,s.filenameUTF8?He:b||je),Ht(s.rawComment,s.commentUTF8?He:f||je)]);s.filename=x,s.comment=z,!s.directory&&s.filename.endsWith("/")&&(s.directory=!0),await Ge(s,s,C,g+6);const U=new Fe(s);if(U.getData=(A,S)=>s.getData(A,U,S),N.push(U),g=k,n.onprogress)try{n.onprogress(W+1,_,new Fe(s))}catch{}}return N}async close(){}},c.configure=Zt,c.getMimeType=function(){return"application/octet-stream"},Object.defineProperty(c,"__esModule",{value:!0})})})(Bn,Rt);const Nn=On(Rt);function Pn(){return{traceUrl:"",startTime:Number.MAX_SAFE_INTEGER,endTime:0,browserName:"",options:{deviceScaleFactor:1,isMobile:!1,viewport:{width:1280,height:800}},pages:[],resources:[],actions:[],events:[],objects:{},hasSource:!1}}var Qe;(R=>{function h(c){for(const m of c.splice(0))m.dispose()}R.disposeAll=h})(Qe||(Qe={}));class Hn{constructor(){$(this,"event");$(this,"_deliveryQueue");$(this,"_listeners",new Set);this.event=(h,c)=>{this._listeners.add(h);let m=!1;const I=this,M={dispose(){m||(m=!0,I._listeners.delete(h))}};return c&&c.push(M),M}}fire(h){const c=!this._deliveryQueue;this._deliveryQueue||(this._deliveryQueue=[]);for(const m of this._listeners)this._deliveryQueue.push({listener:m,event:h});if(c){for(let m=0;m<this._deliveryQueue.length;m++){const{listener:I,event:M}=this._deliveryQueue[m];I.call(null,M)}this._deliveryQueue=void 0}}dispose(){this._listeners.clear(),this._deliveryQueue&&(this._deliveryQueue=[])}}class jn{constructor(){$(this,"_resources",[]);$(this,"_frameSnapshots",new Map);$(this,"_didSnapshot",new Hn);$(this,"onSnapshotEvent",this._didSnapshot.event)}clear(){this._resources=[],this._frameSnapshots.clear()}addResource(h){h.request.url=Vt(h.request.url),this._resources.push(h)}addFrameSnapshot(h){for(const I of h.resourceOverrides)I.url=Vt(I.url);let c=this._frameSnapshots.get(h.frameId);c||(c={raw:[],renderer:[]},this._frameSnapshots.set(h.frameId,c),h.isMainFrame&&this._frameSnapshots.set(h.pageId,c)),c.raw.push(h);const m=new Rn(this._resources,c.raw,c.raw.length-1);c.renderer.push(m),this._didSnapshot.fire(m)}resources(){return this._resources.slice()}snapshotByName(h,c){const m=this._frameSnapshots.get(h);return m==null?void 0:m.renderer.find(I=>I.snapshotName===c)}snapshotByIndex(h,c){const m=this._frameSnapshots.get(h);return m==null?void 0:m.renderer[c]}}const gt=Nn;class qn{constructor(){$(this,"contextEntry");$(this,"pageEntries",new Map);$(this,"_snapshotStorage");$(this,"_entries",new Map);$(this,"_version");$(this,"_zipReader");this.contextEntry=Pn()}_formatUrl(h){let c=h.startsWith("http")||h.startsWith("blob")?h:`file?path=${h}`;return c.startsWith("https://www.dropbox.com/")&&(c="https://dl.dropboxusercontent.com/"+c.substring(24)),c}async load(h,c){this.contextEntry.traceUrl=h,this._zipReader=new gt.ZipReader(new gt.HttpReader(this._formatUrl(h),{mode:"cors",preventHeadRequest:!0}),{useWebWorkers:!1});let m,I;for(const B of await this._zipReader.getEntries({onprogress:c}))B.filename.endsWith(".trace")&&(m=B),B.filename.endsWith(".network")&&(I=B),B.filename.includes("src@")&&(this.contextEntry.hasSource=!0),this._entries.set(B.filename,B);if(!m)throw new Error("Cannot find .trace file");this._snapshotStorage=new Vn(this._entries);const M=new gt.TextWriter;await m.getData(M);for(const B of(await M.getData()).split(`
`))this.appendEvent(B);if(I){const B=new gt.TextWriter;await I.getData(B);for(const H of(await B.getData()).split(`
`))this.appendEvent(H)}this._build()}async hasEntry(h){if(!this._zipReader)return!1;for(const c of await this._zipReader.getEntries())if(c.filename===h)return!0;return!1}async resourceForSha1(h){const c=this._entries.get("resources/"+h);if(!c)return;const m=new gt.BlobWriter;return await c.getData(m),await m.getData()}storage(){return this._snapshotStorage}_build(){this.contextEntry.actions.sort((h,c)=>h.metadata.startTime-c.metadata.startTime),this.contextEntry.resources=this._snapshotStorage.resources()}_pageEntry(h){let c=this.pageEntries.get(h);return c||(c={screencastFrames:[]},this.pageEntries.set(h,c),this.contextEntry.pages.push(c)),c}appendEvent(h){if(!h)return;const c=this._modernize(JSON.parse(h));switch(c.type){case"context-options":{this.contextEntry.browserName=c.browserName,this.contextEntry.title=c.title,this.contextEntry.platform=c.platform,this.contextEntry.wallTime=c.wallTime,this.contextEntry.sdkLanguage=c.sdkLanguage,this.contextEntry.options=c.options;break}case"screencast-frame":{this._pageEntry(c.pageId).screencastFrames.push(c);break}case"action":{!Gn(c.metadata)&&(!c.metadata.internal||c.metadata.apiName)&&(c.metadata.apiName||(c.metadata.apiName=c.metadata.type+"."+c.metadata.method),this.contextEntry.actions.push(c));break}case"event":{const m=c.metadata;m.pageId&&(m.method==="__create__"?this.contextEntry.objects[m.params.guid]=m.params.initializer:this.contextEntry.events.push(c));break}case"resource-snapshot":this._snapshotStorage.addResource(c.snapshot);break;case"frame-snapshot":this._snapshotStorage.addFrameSnapshot(c.snapshot);break}(c.type==="action"||c.type==="event")&&(this.contextEntry.startTime=Math.min(this.contextEntry.startTime,c.metadata.startTime),this.contextEntry.endTime=Math.max(this.contextEntry.endTime,c.metadata.endTime)),c.type==="screencast-frame"&&(this.contextEntry.startTime=Math.min(this.contextEntry.startTime,c.timestamp),this.contextEntry.endTime=Math.max(this.contextEntry.endTime,c.timestamp))}_modernize(h){if(this._version===void 0)return h;for(let c=this._version;c<3;++c)h=this[`_modernize_${c}_to_${c+1}`].call(this,h);return h}_modernize_0_to_1(h){return h.type==="action"&&typeof h.metadata.error=="string"&&(h.metadata.error={error:{name:"Error",message:h.metadata.error}}),h}_modernize_1_to_2(h){return h.type==="frame-snapshot"&&h.snapshot.isMainFrame&&(h.snapshot.viewport=this.contextEntry.options.viewport||{width:1280,height:720}),h}_modernize_2_to_3(h){if(h.type==="resource-snapshot"&&!h.snapshot.request){const c=h.snapshot;h.snapshot={_frameref:c.frameId,request:{url:c.url,method:c.method,headers:c.requestHeaders,postData:c.requestSha1?{_sha1:c.requestSha1}:void 0},response:{status:c.status,headers:c.responseHeaders,content:{mimeType:c.contentType,_sha1:c.responseSha1}},_monotonicTime:c.timestamp}}return h}}class Vn extends jn{constructor(c){super();$(this,"_entries");this._entries=c}async resourceContent(c){const m=this._entries.get("resources/"+c),I=new gt.BlobWriter;return await m.getData(I),I.getData()}}function Gn(R){return R.method.startsWith("tracing")}self.addEventListener("install",function(R){self.skipWaiting()});self.addEventListener("activate",function(R){R.waitUntil(self.clients.claim())});const $n=new URL(self.registration.scope).pathname,ht=new Map,$t=new An;async function Kn(R,h,c,m){var H;const I=ht.get(R);if($t.set(c,R),I)return I.traceModel;const M=new qn;try{await M.load(R,m)}catch(F){throw console.error(F),(H=F==null?void 0:F.message)!=null&&H.includes("Cannot find .trace file")&&await M.hasEntry("index.html")?new Error("Could not load trace. Did you upload a Playwright HTML report instead? Make sure to extract the archive first and then double-click the index.html file or put it on a web server."):h?new Error(`Could not load trace from ${h}. Make sure to upload a valid Playwright trace.`):new Error(`Could not load trace from ${R}. Make sure a valid Playwright Trace is accessible over this url.`)}const B=new Mn(M.storage());return ht.set(R,{traceModel:M,snapshotServer:B}),M}async function Qn(R){const h=R.request,c=await self.clients.get(R.clientId);if(h.url.startsWith(self.registration.scope)){const B=new URL(Gt(h.url)),H=B.pathname.substring($n.length-1);if(H==="/ping")return await Zn(),new Response(null,{status:200});const F=B.searchParams.get("trace"),{snapshotServer:G}=ht.get(F)||{};if(H==="/context")try{const T=await Kn(F,B.searchParams.get("traceFileName"),R.clientId,(Q,dt)=>{c.postMessage({method:"progress",params:{done:Q,total:dt}})});return new Response(JSON.stringify(T.contextEntry),{status:200,headers:{"Content-Type":"application/json"}})}catch(T){return new Response(JSON.stringify({error:T==null?void 0:T.message}),{status:500,headers:{"Content-Type":"application/json"}})}if(H.startsWith("/snapshotInfo/"))return G?G.serveSnapshotInfo(H,B.searchParams):new Response(null,{status:404});if(H.startsWith("/snapshot/"))return G?G.serveSnapshot(H,B.searchParams,B.href):new Response(null,{status:404});if(H.startsWith("/sha1/")){for(const{traceModel:T}of ht.values()){const Q=await T.resourceForSha1(H.slice(6));if(Q)return new Response(Q,{status:200})}return new Response(null,{status:404})}return fetch(R.request)}const m=Gt(c.url),I=new URL(m).searchParams.get("trace"),{snapshotServer:M}=ht.get(I)||{};return M?M.serveResource(h.url,m):new Response(null,{status:404})}async function Zn(){const R=await self.clients.matchAll(),h=new Set;for(const[c,m]of $t)R.find(I=>I.id===c)?m.forEach(I=>h.add(I)):$t.deleteAll(c);for(const c of ht.keys())h.has(c)||ht.delete(c)}self.addEventListener("fetch",function(R){R.respondWith(Qn(R))});
