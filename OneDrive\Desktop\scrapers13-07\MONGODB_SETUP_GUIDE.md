# MongoDB Setup Guide for NZ Supermarket Price Scraper

## Overview
This guide covers setting up MongoDB for the NZ Supermarket Price Scraper system after migrating from Supabase PostgreSQL.

## Prerequisites
- MongoDB 6.0 or later (local installation or MongoDB Atlas)
- Node.js 18+ (for Woolworths scraper)
- .NET 6+ (for New World and PakNSave scrapers)

## Option 1: Local MongoDB Installation

### Windows
1. Download MongoDB Community Server from https://www.mongodb.com/try/download/community
2. Install MongoDB with default settings
3. MongoDB will run on `mongodb://localhost:27017` by default

### macOS (using Homebrew)
```bash
brew tap mongodb/brew
brew install mongodb-community
brew services start mongodb-community
```

### Linux (Ubuntu/Debian)
```bash
# Import MongoDB public key
curl -fsSL https://pgp.mongodb.com/server-6.0.asc | sudo gpg -o /usr/share/keyrings/mongodb-server-6.0.gpg --dearmor

# Add MongoDB repository
echo "deb [ arch=amd64,arm64 signed-by=/usr/share/keyrings/mongodb-server-6.0.gpg ] https://repo.mongodb.org/apt/ubuntu jammy/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list

# Install MongoDB
sudo apt-get update
sudo apt-get install -y mongodb-org

# Start MongoDB service
sudo systemctl start mongod
sudo systemctl enable mongod
```

## Option 2: MongoDB Atlas (Cloud)

1. Go to https://www.mongodb.com/atlas
2. Create a free account and cluster
3. Configure network access (add your IP address)
4. Create a database user with read/write permissions
5. Get your connection string (looks like `mongodb+srv://username:<EMAIL>/`)

## Configuration

### Environment Variables

#### Woolworths Scraper (.env)
```env
MONGODB_CONNECTION_STRING=mongodb://localhost:27017/nz-supermarket-scraper
# For Atlas: mongodb+srv://username:<EMAIL>/
MONGODB_DATABASE_NAME=nz-supermarket-scraper
STORE_NAME=Auckland Central
```

#### New World & PakNSave (appsettings.json)
```json
{
  "MONGODB_CONNECTION_STRING": "mongodb://localhost:27017/nz-supermarket-scraper",
  "MONGODB_DATABASE_NAME": "nz-supermarket-scraper",
  "GEOLOCATION_LONG": "-174.91",
  "GEOLOCATION_LAT": "-41.21"
}
```

## Database Schema

The MongoDB database uses the following collections:

- **stores** - Store information (Woolworths, New World, PakNSave)
- **brands** - Product brands
- **categoryHierarchy** - Hierarchical product categories
- **consolidatedProducts** - Unified products across all stores
- **priceHistory** - Historical price data (partitioned by year/month)
- **productImages** (GridFS) - Product images stored in GridFS

## Running the Scrapers

### Install Dependencies

#### Woolworths (Node.js)
```bash
cd Woolworths
npm install
npx playwright install
```

#### New World
```bash
cd new-world/src
dotnet restore
pwsh bin/Debug/net6.0/playwright.ps1 install chromium
```

#### PakNSave
```bash
cd paknsave/src
dotnet restore
pwsh bin/Debug/net6.0/playwright.ps1 install chromium
```

### Run Individual Scrapers

#### Woolworths
```bash
# Dry run (no database)
npm run dev

# With database storage
npm run db

# With database + image upload
npm run db images
```

#### New World / PakNSave
```bash
# Dry run (no database)
dotnet run

# With database storage
dotnet run db

# With database + image upload
dotnet run db images
```

### Run All Scrapers
```bash
# Dry run all scrapers
node start-scrapers.js dev

# Run all with database
node start-scrapers.js db

# Run all with database + images
node start-scrapers.js db-images

# Run specific scrapers only
node start-scrapers.js db woolworths newworld
```

## Database Initialization

The scrapers will automatically:
1. Create indexes for optimal performance
2. Initialize basic category hierarchies
3. Create store records as needed

## Migration from Supabase

If you have existing data in Supabase that you want to migrate:

1. Export your existing data from Supabase
2. Use the migration utilities in `scripts/mongodb-migration.js`
3. Run the migration script to transform and import data

## Performance Considerations

### Indexing
The scrapers automatically create these indexes:
- Text search index on product names
- Category and brand indexes
- Price history time-series indexes
- Store-specific product variant indexes

### Optimization Tips
1. **Connection Pooling**: MongoDB driver handles this automatically
2. **Batch Operations**: Large data operations use batch inserts
3. **GridFS**: Images stored efficiently in GridFS with metadata
4. **Partitioning**: Price history partitioned by year/month for better performance

## Monitoring and Maintenance

### Database Size Management
- Price history can grow large over time
- Consider archiving old price data (older than 2 years)
- Monitor GridFS storage for images

### Performance Monitoring
```javascript
// Check database stats
db.stats()

// Check collection sizes
db.consolidatedProducts.stats()
db.priceHistory.stats()

// Check index usage
db.consolidatedProducts.aggregate([{$indexStats: {}}])
```

### Backup Strategy
```bash
# Full database backup
mongodump --uri="mongodb://localhost:27017/nz-supermarket-scraper" --out=/path/to/backup

# Restore from backup
mongorestore --uri="mongodb://localhost:27017/nz-supermarket-scraper" /path/to/backup/nz-supermarket-scraper
```

## Troubleshooting

### Common Issues

#### Connection Failed
```
MongoNetworkError: connect ECONNREFUSED 127.0.0.1:27017
```
**Solution**: Ensure MongoDB service is running
```bash
# Windows
net start MongoDB

# macOS
brew services start mongodb-community

# Linux
sudo systemctl start mongod
```

#### Authentication Failed (Atlas)
```
MongoServerError: Authentication failed
```
**Solution**: Check username, password, and network access in Atlas dashboard

#### Out of Disk Space
```
MongoServerError: No space left on device
```
**Solution**: 
1. Clean up old data
2. Archive price history
3. Check GridFS image storage

#### Slow Queries
**Solution**:
1. Check if indexes are being used
2. Consider adding compound indexes for complex queries
3. Monitor slow query log

## Advanced Features

### GridFS Image Management
```javascript
// List all images
db.productImages.files.find()

// Find images for specific product
db.productImages.files.find({"metadata.productId": "W123456"})

// Clean up orphaned images
// (Run periodically to remove images for deleted products)
```

### Custom Aggregation Queries
```javascript
// Price comparison across stores
db.consolidatedProducts.aggregate([
  {
    $lookup: {
      from: "stores",
      localField: "currentPrices.storeId",
      foreignField: "_id",
      as: "storeInfo"
    }
  },
  {
    $project: {
      displayName: 1,
      priceComparison: "$currentPrices",
      stores: "$storeInfo.name"
    }
  }
])
```

## Security Considerations

1. **Network Security**: Configure MongoDB to only accept connections from authorized IPs
2. **Authentication**: Always use authentication in production
3. **Encryption**: Enable encryption at rest and in transit for production
4. **Access Control**: Use role-based access control for different users

## Support

For issues related to:
- **MongoDB Setup**: Check MongoDB official documentation
- **Scraper Issues**: Check scraper logs and error messages
- **Performance**: Monitor database metrics and query performance
- **Data Migration**: Use the provided migration utilities