# ============================================================================
# NZ Supermarket Scrapers - Multi-Window Launcher (PowerShell Version)
# ============================================================================
# This script launches all 3 scrapers in separate PowerShell windows
# Works with Windows PowerShell, PowerShell Core, and Windows Terminal
# ============================================================================

# Set execution policy for current session (if needed)
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force

# Colors
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Blue"
$Cyan = "Cyan"
$Magenta = "Magenta"

# Script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path

Write-Host ""
Write-Host "========================================" -ForegroundColor $Cyan
Write-Host "  NZ SUPERMARKET SCRAPERS LAUNCHER" -ForegroundColor $Cyan
Write-Host "========================================" -ForegroundColor $Cyan
Write-Host ""

# Function to check if command exists
function Test-Command {
    param($Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# Function to check MongoDB status
function Test-MongoDB {
    Write-Host "[1/4] Checking MongoDB status..." -ForegroundColor $Yellow
    
    if (Test-Command docker) {
        $mongoContainer = docker ps --filter "name=mongodb" --format "{{.Names}}"
        if ($mongoContainer -eq "mongodb") {
            Write-Host "✓ MongoDB is already running" -ForegroundColor $Green
        }
        else {
            Write-Host "⚠ MongoDB not found. Starting MongoDB..." -ForegroundColor $Yellow
            docker run -d -p 27017:27017 --name mongodb mongo:latest | Out-Null
            Start-Sleep -Seconds 5
            Write-Host "✓ MongoDB started successfully" -ForegroundColor $Green
        }
    }
    else {
        Write-Host "✗ Docker not found. Please install Docker Desktop first." -ForegroundColor $Red
        exit 1
    }
    Write-Host ""
}

# Function to launch scraper in new window
function Start-Scraper {
    param(
        [string]$Name,
        [string]$Directory,
        [string]$Command,
        [string]$Step
    )
    
    Write-Host "[$Step] Launching $Name scraper..." -ForegroundColor $Yellow
    
    $FullPath = Join-Path $ScriptDir $Directory
    $WindowTitle = "$Name Scraper"
    
    # Try Windows Terminal first, then fall back to regular PowerShell
    if (Test-Command wt) {
        # Windows Terminal
        $wtArgs = @(
            "new-tab"
            "--title", $WindowTitle
            "--startingDirectory", $FullPath
            "powershell.exe"
            "-NoExit"
            "-Command", "Write-Host '=== $Name SCRAPER ===' -ForegroundColor Cyan; Write-Host 'Starting scraper...' -ForegroundColor Yellow; $Command"
        )
        Start-Process wt -ArgumentList $wtArgs
    }
    elseif (Test-Command pwsh) {
        # PowerShell Core
        $psArgs = @(
            "-NoExit"
            "-Command"
            "Set-Location '$FullPath'; Write-Host '=== $Name SCRAPER ===' -ForegroundColor Cyan; Write-Host 'Starting scraper...' -ForegroundColor Yellow; $Command"
        )
        Start-Process pwsh -ArgumentList $psArgs -WindowStyle Normal
    }
    else {
        # Windows PowerShell
        $psArgs = @(
            "-NoExit"
            "-Command"
            "Set-Location '$FullPath'; Write-Host '=== $Name SCRAPER ===' -ForegroundColor Cyan; Write-Host 'Starting scraper...' -ForegroundColor Yellow; $Command"
        )
        Start-Process powershell -ArgumentList $psArgs -WindowStyle Normal
    }
    
    Start-Sleep -Seconds 3
    Write-Host "✓ $Name scraper launched" -ForegroundColor $Green
    Write-Host ""
}

# Function to show status
function Show-Status {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor $Cyan
    Write-Host "  ALL SCRAPERS LAUNCHED SUCCESSFULLY!" -ForegroundColor $Cyan
    Write-Host "========================================" -ForegroundColor $Cyan
    Write-Host ""
    Write-Host "Three command windows should now be open:" -ForegroundColor $Green
    Write-Host "  1. Woolworths Scraper (Node.js/TypeScript)" -ForegroundColor $Blue
    Write-Host "  2. Pak'n Save Scraper (.NET/C#)" -ForegroundColor $Blue
    Write-Host "  3. New World Scraper (.NET/C#)" -ForegroundColor $Blue
    Write-Host ""
    Write-Host "Each window shows real-time scraping progress." -ForegroundColor $Yellow
    Write-Host "Close individual windows to stop specific scrapers." -ForegroundColor $Yellow
    Write-Host ""
    Write-Host "MongoDB Details:" -ForegroundColor $Magenta
    Write-Host "  • Host: localhost:27017" -ForegroundColor $Blue
    Write-Host "  • Database: nz-supermarket-scraper" -ForegroundColor $Blue
    Write-Host ""
}

# Function to show monitoring commands
function Show-Monitoring {
    Write-Host "MONITORING COMMANDS:" -ForegroundColor $Cyan
    Write-Host "• View MongoDB data: " -NoNewline -ForegroundColor $Blue
    Write-Host "docker exec -it mongodb mongosh" -ForegroundColor $Yellow
    Write-Host "• Export data: " -NoNewline -ForegroundColor $Blue
    Write-Host "cd Woolworths; node export-data.js" -ForegroundColor $Yellow
    Write-Host "• Query data: " -NoNewline -ForegroundColor $Blue
    Write-Host "cd Woolworths; node query-data.js summary" -ForegroundColor $Yellow
    Write-Host "• Stop MongoDB: " -NoNewline -ForegroundColor $Blue
    Write-Host "docker stop mongodb" -ForegroundColor $Yellow
    Write-Host ""
}

# Main execution
try {
    # Check prerequisites
    if (-not (Test-Command docker)) {
        Write-Host "✗ Docker is required but not installed." -ForegroundColor $Red
        Write-Host "Please install Docker Desktop first." -ForegroundColor $Yellow
        exit 1
    }
    
    # Check MongoDB
    Test-MongoDB
    
    # Launch scrapers
    Start-Scraper "WOOLWORTHS" "Woolworths" "npm run db" "2/4"
    Start-Scraper "PAK'N SAVE" "paknsave\src" "dotnet run" "3/4"
    Start-Scraper "NEW WORLD" "new-world\src" "dotnet run" "4/4"
    
    # Show status
    Show-Status
    Show-Monitoring
    
    Write-Host "🚀 All scrapers are now running!" -ForegroundColor $Green
    Write-Host "Press any key to exit this launcher (scrapers will continue running)..." -ForegroundColor $Yellow
    
    # Wait for user input
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}
catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor $Red
    Write-Host "Press any key to exit..." -ForegroundColor $Yellow
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    exit 1
}
