# New World New Zealand Scraper

A .NET/C# web scraper for collecting product data from New World New Zealand (newworld.co.nz) with advanced cross-store product matching and MongoDB integration.

## 🎯 **Overview**

This scraper collects comprehensive product information including:
- **Product Data**: Names, prices, sizes, unit pricing (per kg, per L, etc.)
- **Categories**: Product categories and subcategories  
- **Images**: Product images stored in MongoDB GridFS
- **Cross-Store Matching**: Advanced fuzzy matching with 152 NZ brand mappings
- **Price History**: Historical price tracking for trend analysis

## 🚀 **Quick Start**

### **Prerequisites**
- **.NET 6 SDK** or higher - [Download](https://dotnet.microsoft.com/download)
- **MongoDB** (running on localhost:27017) - Use Docker: `docker run -d -p 27017:27017 --name mongodb mongo:latest`
- **Internet connection** for accessing New World website

### **Installation**

1. **Navigate to New World directory:**
   ```bash
   cd new-world/src
   ```

2. **Restore dependencies:**
   ```bash
   dotnet restore
   ```

3. **Build the project:**
   ```bash
   dotnet build
   ```

4. **Configure environment** (optional):
   ```bash
   # Create .env file in new-world directory
   echo "MONGODB_CONNECTION_STRING=mongodb://localhost:27017/nz-supermarket-scraper" > ../.env
   echo "STORE_LOCATION=Auckland" >> ../.env
   ```

### **Running the Scraper**

#### **Development Mode (Dry Run)**
```bash
dotnet run
```
- Scrapes products but doesn't save to database
- Outputs results to console
- Perfect for testing and development

#### **Database Mode**
```bash
dotnet run db
```
- Scrapes products and saves to MongoDB
- Creates consolidated product records
- Tracks price history

#### **Database + Images Mode**
```bash
dotnet run db images
```
- Scrapes products and saves to MongoDB
- Downloads and stores product images in GridFS
- Full data collection mode

## ⚙️ **Configuration**

### **Environment Variables (.env)**

Create a `.env` file in the `new-world` directory:

```env
# MongoDB Configuration
MONGODB_CONNECTION_STRING=mongodb://localhost:27017/nz-supermarket-scraper

# Store Configuration
STORE_LOCATION=Auckland
MAX_PAGES=0

# Scraping Configuration
SCRAPE_DELAY=7000
HEADLESS_MODE=true

# Debug Configuration
DEBUG_MODE=false
VERBOSE_LOGGING=false
```

### **Configuration Options**

| Variable | Description | Default |
|----------|-------------|---------|
| `MONGODB_CONNECTION_STRING` | MongoDB connection URL | `mongodb://localhost:27017/nz-supermarket-scraper` |
| `STORE_LOCATION` | Default store location | `Auckland` |
| `MAX_PAGES` | Maximum pages to scrape (0 = all) | `0` |
| `SCRAPE_DELAY` | Delay between page requests (ms) | `7000` |
| `HEADLESS_MODE` | Run browser in headless mode | `true` |

## 📊 **Scraping Process**

### **1. Store Selection**
- Automatically selects store location based on `STORE_LOCATION`
- Falls back to default location if selection fails
- Handles New World's location-based pricing

### **2. Category Discovery**
- Discovers all product categories automatically
- Scrapes from main navigation menu
- Covers all departments: Fresh, Grocery, Frozen, etc.

### **3. Product Collection**
- Scrapes ~86 pages across all categories
- Collects ~4,000 individual products
- Processes ~46 products per page on average

### **4. Data Processing**
- Normalizes product names using NZ brand mappings
- Applies advanced fuzzy matching algorithms
- Consolidates products across stores
- Stores images in MongoDB GridFS

## 🎯 **Advanced Features**

### **Enhanced Product Matching**
- **152 NZ Brand Mappings**: Recognizes major NZ brands and variations
- **Fuzzy Matching**: 90%+ accuracy using Levenshtein + Jaccard algorithms
- **Store Descriptor Removal**: Removes "New World", "Premium", etc.
- **Size Normalization**: Standardizes units (grams→g, litres→l)

### **Cross-Store Consolidation**
```csharp
// Example: These products get consolidated
"New World Premium Milk 2L"        // Original
"milk 2l"                          // Normalized
// Matches with Woolworths: "Anchor Blue Milk 2L"
// Result: Single consolidated product with multiple store variants
```

### **Robust Error Handling**
- **Website Changes**: Adaptive selectors for different page layouts
- **Network Issues**: Automatic retry with exponential backoff
- **Rate Limiting**: Respectful delays between requests
- **Graceful Degradation**: Continues scraping even if some products fail

## 📁 **Project Structure**

```
new-world/
├── README.md                   # This file
├── .env                       # Environment configuration
├── src/
│   ├── Program.cs             # Main scraper entry point
│   ├── MongoDbService.cs      # Database operations and matching
│   ├── ProductScraper.cs      # Web scraping logic
│   ├── Models/
│   │   ├── Product.cs         # Product data models
│   │   └── ScrapedProduct.cs  # Scraped data models
│   └── new-world.csproj       # Project file
└── logs/                      # Log files (when run via batch)
```

## 🔍 **Monitoring & Debugging**

### **Console Output**
The scraper provides detailed console output with color coding:
- 🟢 **Green**: Successful operations
- 🟡 **Yellow**: Warnings and information
- 🔵 **Blue**: New product creation
- 🔴 **Red**: Errors and failures
- 🟦 **Cyan**: Database operations

### **Sample Output**
```
✅ MongoDB connection established
✅ Enhanced NZ brand mappings loaded (152 brands)
🟡 Starting New World scraper...
86 pages to be scraped            7s delay between scrapes

[1/86] https://www.newworld.co.nz/shop/category/fresh-foods?page=1
46 product entries found          Time Elapsed: 42s
🔍 Processing: Fresh Bananas -> normalized: bananas per kg
🆕 Created new consolidated product: Fresh Bananas
```

### **Log Files**
When run via batch scripts, output is logged to:
```
../../logs/newworld.log
```

## 🛠️ **Troubleshooting**

### **Common Issues**

#### **MongoDB Connection Failed**
```
Error: MongoDB connection failed
```
**Solutions**:
1. Ensure MongoDB is running: `docker ps | grep mongodb`
2. Check connection string in `.env`
3. Restart MongoDB: `docker restart mongodb`

#### **.NET Build Errors**
```
Error: The project file could not be loaded
```
**Solutions**:
1. Verify .NET SDK installation: `dotnet --version`
2. Restore packages: `dotnet restore`
3. Clean and rebuild: `dotnet clean && dotnet build`

#### **Browser Launch Failed**
```
Error: Could not find browser executable
```
**Solutions**:
1. Install required browser dependencies
2. Check system requirements for Playwright
3. Try running with `HEADLESS_MODE=false`

#### **Rate Limiting / Blocked Requests**
```
Error: HTTP 429 Too Many Requests
```
**Solutions**:
1. Increase `SCRAPE_DELAY` in `.env`
2. Check if IP is temporarily blocked
3. Try running at different times

### **Debug Mode**
Enable detailed debugging:
```env
DEBUG_MODE=true
VERBOSE_LOGGING=true
HEADLESS_MODE=false
```

## 📈 **Performance**

### **Expected Metrics**
- **Pages**: ~86 pages across all categories
- **Products**: ~4,000 individual products
- **Time**: ~15 minutes (with 7-second delays)
- **Memory**: ~300MB peak usage
- **Database**: ~15MB per full scrape

### **Optimization Tips**
1. **Reduce Delay**: Lower `SCRAPE_DELAY` for faster scraping (risk of blocking)
2. **Limit Pages**: Set `MAX_PAGES` for testing smaller datasets
3. **Skip Images**: Use `dotnet run db` instead of `dotnet run db images`
4. **Headless Mode**: Keep `HEADLESS_MODE=true` for better performance

## 🧪 **Testing**

### **Manual Testing**
```bash
# Test with limited pages
MAX_PAGES=5 dotnet run

# Test specific store location
STORE_LOCATION="Wellington" dotnet run

# Test database mode
dotnet run db
```

## 🔄 **Integration**

### **Database Schema**
Products are stored in MongoDB with the following structure:
```csharp
public class ConsolidatedProduct
{
    public ObjectId Id { get; set; }
    public string NormalizedName { get; set; }
    public string DisplayName { get; set; }
    public List<string> Aliases { get; set; }
    public List<ProductVariant> Variants { get; set; }
    public int MatchConfidence { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}
```

### **API Integration**
Query consolidated products:
```csharp
// Find all New World variants
var newWorldProducts = await collection.Find(
    Builders<BsonDocument>.Filter.ElemMatch("variants", 
        Builders<BsonDocument>.Filter.Eq("storeId", 2))
).ToListAsync();

// Cross-store price comparison
var crossStoreProducts = await collection.Find(
    Builders<BsonDocument>.Filter.Size("variants", 2)
).ToListAsync();
```

## 📚 **Additional Resources**

- **[Main Project README](../README.md)** - Overall system documentation
- **[Database Documentation](../docs/consolidated-product-database-documentation.md)** - Complete schema reference
- **[Batch File Usage](../README-BATCH-FILES.md)** - Automation scripts

## 🤝 **Contributing**

1. Follow C# coding standards
2. Add unit tests for new functionality
3. Update documentation
4. Respect website rate limits
5. Test thoroughly before submitting PRs

---

**Last Updated**: January 2024  
**Version**: 2.0  
**Technology**: C#, .NET 6, Playwright, MongoDB
