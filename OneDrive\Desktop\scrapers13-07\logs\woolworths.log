
> cd-scraper@1.0.0 db-images
> npx esrun src/index.ts db images

[dotenv@17.2.0] injecting env (7) from .env (tip: ⚙️  enable debug logging with { debug: true })
[dotenv@17.2.0] injecting env (0) from .env.local (tip: ⚙️  enable debug logging with { debug: true })
[dotenv@17.2.0] injecting env (0) from .env (tip: ⚙️  enable debug logging with { debug: true })
[dotenv@17.2.0] injecting env (0) from .env (tip: 🛠️  run anywhere with `dotenvx run -- yourcommand`)
[dotenv@17.2.0] injecting env (0) from .env.local (tip: ⚙️  load multiple .env files with { path: ['.env.local', '.env'] })
[33m🔄 MongoDB connection attempt 1/5...[0m
[31m❌ MongoDB connection attempt 1 failed: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017[0m
[33m⏳ Retrying in 2 seconds...[0m
[33m🔄 MongoDB connection attempt 2/5...[0m
^C^C