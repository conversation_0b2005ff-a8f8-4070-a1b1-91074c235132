#!/usr/bin/env node

/**
 * Test Supabase Connection and Schema
 * This script tests the Supabase connection and checks if tables exist
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();
dotenv.config({ path: '.env.local', override: true });

const COLORS = {
    reset: '\x1b[0m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    red: '\x1b[31m',
    cyan: '\x1b[36m',
    blue: '\x1b[34m'
};

function log(color, message) {
    console.log(`${color}${message}${COLORS.reset}`);
}

async function testConnection() {
    log(COLORS.cyan, '\n=== Testing Supabase Connection ===');
    
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;
    
    if (!supabaseUrl || !supabaseKey) {
        log(COLORS.red, '❌ Missing Supabase credentials');
        return null;
    }
    
    try {
        const supabase = createClient(supabaseUrl, supabaseKey);
        
        // Test basic connection
        const { data, error } = await supabase
            .from('stores')
            .select('count')
            .limit(1);
            
        if (error) {
            log(COLORS.red, `❌ Connection failed: ${error.message}`);
            return null;
        }
        
        log(COLORS.green, '✅ Supabase connection successful');
        return supabase;
        
    } catch (error) {
        log(COLORS.red, `❌ Connection error: ${error.message}`);
        return null;
    }
}

async function checkExistingTables(supabase) {
    log(COLORS.cyan, '\n=== Checking Existing Tables ===');
    
    const tablesToCheck = [
        'stores',
        'brands', 
        'categories',
        'products',
        'prices'
    ];
    
    for (const table of tablesToCheck) {
        try {
            const { data, error } = await supabase
                .from(table)
                .select('*')
                .limit(1);
                
            if (error) {
                log(COLORS.red, `❌ ${table}: ${error.message}`);
            } else {
                log(COLORS.green, `✅ ${table}: exists`);
            }
        } catch (error) {
            log(COLORS.red, `❌ ${table}: ${error.message}`);
        }
    }
}

async function checkNewTables(supabase) {
    log(COLORS.cyan, '\n=== Checking New Consolidated Tables ===');
    
    const newTables = [
        'category_hierarchy',
        'consolidated_products',
        'product_variants',
        'consolidated_prices',
        'product_size_variants'
    ];
    
    let allTablesExist = true;
    
    for (const table of newTables) {
        try {
            const { data, error } = await supabase
                .from(table)
                .select('*')
                .limit(1);
                
            if (error) {
                log(COLORS.red, `❌ ${table}: ${error.message}`);
                allTablesExist = false;
            } else {
                log(COLORS.green, `✅ ${table}: exists`);
            }
        } catch (error) {
            log(COLORS.red, `❌ ${table}: ${error.message}`);
            allTablesExist = false;
        }
    }
    
    return allTablesExist;
}

async function checkCategoryHierarchy(supabase) {
    log(COLORS.cyan, '\n=== Checking Category Hierarchy Data ===');
    
    try {
        const { data, error } = await supabase
            .from('category_hierarchy')
            .select('*')
            .order('level, sort_order');
            
        if (error) {
            log(COLORS.red, `❌ Error fetching categories: ${error.message}`);
            return false;
        }
        
        if (!data || data.length === 0) {
            log(COLORS.yellow, '⚠️  Category hierarchy table exists but is empty');
            return false;
        }
        
        log(COLORS.green, `✅ Found ${data.length} categories`);
        
        // Show some categories
        const mainCategories = data.filter(cat => cat.level === 0);
        const subCategories = data.filter(cat => cat.level === 1);
        
        log(COLORS.blue, `   Main categories: ${mainCategories.length}`);
        log(COLORS.blue, `   Sub categories: ${subCategories.length}`);
        
        // Show first few categories
        if (mainCategories.length > 0) {
            log(COLORS.blue, '   Examples:');
            mainCategories.slice(0, 3).forEach(cat => {
                log(COLORS.blue, `   - ${cat.name}`);
            });
        }
        
        return true;
        
    } catch (error) {
        log(COLORS.red, `❌ Error checking categories: ${error.message}`);
        return false;
    }
}

async function checkStoreData(supabase) {
    log(COLORS.cyan, '\n=== Checking Store Data ===');
    
    try {
        const { data, error } = await supabase
            .from('stores')
            .select('*');
            
        if (error) {
            log(COLORS.red, `❌ Error fetching stores: ${error.message}`);
            return;
        }
        
        if (!data || data.length === 0) {
            log(COLORS.yellow, '⚠️  No stores found in database');
            return;
        }
        
        log(COLORS.green, `✅ Found ${data.length} stores:`);
        data.forEach(store => {
            log(COLORS.blue, `   - ${store.name} (ID: ${store.id})`);
        });
        
    } catch (error) {
        log(COLORS.red, `❌ Error checking stores: ${error.message}`);
    }
}

async function checkExistingProducts(supabase) {
    log(COLORS.cyan, '\n=== Checking Existing Product Data ===');
    
    try {
        const { data, error } = await supabase
            .from('products')
            .select('id, name, source_site')
            .limit(5);
            
        if (error) {
            log(COLORS.red, `❌ Error fetching products: ${error.message}`);
            return;
        }
        
        if (!data || data.length === 0) {
            log(COLORS.yellow, '⚠️  No products found in database');
            return;
        }
        
        log(COLORS.green, `✅ Found existing products (showing first 5):`);
        data.forEach(product => {
            log(COLORS.blue, `   - ${product.id}: ${product.name} (${product.source_site})`);
        });
        
        // Get total count
        const { count } = await supabase
            .from('products')
            .select('*', { count: 'exact', head: true });
            
        log(COLORS.green, `✅ Total products in database: ${count}`);
        
    } catch (error) {
        log(COLORS.red, `❌ Error checking products: ${error.message}`);
    }
}

async function testProductInsertion(supabase) {
    log(COLORS.cyan, '\n=== Testing Product Insertion ===');
    
    try {
        // Test inserting a sample consolidated product
        const testProduct = {
            normalized_name: 'test product',
            display_name: 'Test Product 123g',
            primary_size: '123g',
            match_confidence: 100
        };
        
        const { data, error } = await supabase
            .from('consolidated_products')
            .insert(testProduct)
            .select()
            .single();
            
        if (error) {
            log(COLORS.red, `❌ Failed to insert test product: ${error.message}`);
            return false;
        }
        
        log(COLORS.green, `✅ Successfully inserted test product: ${data.id}`);
        
        // Clean up - delete the test product
        await supabase
            .from('consolidated_products')
            .delete()
            .eq('id', data.id);
            
        log(COLORS.green, '✅ Test product cleaned up');
        return true;
        
    } catch (error) {
        log(COLORS.red, `❌ Error testing insertion: ${error.message}`);
        return false;
    }
}

async function main() {
    log(COLORS.cyan, '🔍 Supabase Database Test');
    
    // Test connection
    const supabase = await testConnection();
    if (!supabase) {
        log(COLORS.red, '\n❌ Cannot proceed without database connection');
        return;
    }
    
    // Check existing tables
    await checkExistingTables(supabase);
    
    // Check new tables
    const newTablesExist = await checkNewTables(supabase);
    
    if (!newTablesExist) {
        log(COLORS.yellow, '\n⚠️  New consolidated tables not found. Run the migration first:');
        log(COLORS.blue, '   1. Go to Supabase Dashboard → SQL Editor');
        log(COLORS.blue, '   2. Run: supabase/migrations/20240724_002_consolidated_products.sql');
        return;
    }
    
    // Check category data
    const categoriesExist = await checkCategoryHierarchy(supabase);
    
    // Check store data
    await checkStoreData(supabase);
    
    // Check existing products
    await checkExistingProducts(supabase);
    
    // Test insertion
    const insertionWorks = await testProductInsertion(supabase);
    
    // Final summary
    log(COLORS.cyan, '\n=== Summary ===');
    
    if (newTablesExist && categoriesExist && insertionWorks) {
        log(COLORS.green, '🎉 Your Supabase database is ready for consolidated products!');
        log(COLORS.blue, '\nNext steps:');
        log(COLORS.blue, '1. cd Woolworths && npm run db');
        log(COLORS.blue, '2. Check for new entries in consolidated_products table');
    } else {
        log(COLORS.yellow, '⚠️  Some setup steps are still needed. Check the errors above.');
    }
}

main().catch(console.error);