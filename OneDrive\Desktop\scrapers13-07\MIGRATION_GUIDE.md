# CosmosDB to PostgreSQL/Supabase Migration Guide

This guide will help you migrate your supermarket price data from Azure CosmosDB to PostgreSQL using Supabase.

## Overview

The migration involves:
1. Setting up a new PostgreSQL database on Supabase
2. Creating the required tables and indexes
3. Exporting data from CosmosDB
4. Importing data into PostgreSQL
5. Updating scrapers to use the new database

## Prerequisites

- Node.js v16+ installed
- .NET 6 SDK installed (for C# scrapers)
- Supabase account created
- Access to your existing CosmosDB credentials

## Step 1: Set up Supabase

1. Create a new Supabase project at https://supabase.com
2. Once created, get your project URL and keys from Settings > API
3. Note down:
   - Project URL (e.g., `https://xyzcompany.supabase.co`)
   - Anon/Public key
   - Service Role key (for server-side operations)
   - Database password (from Settings > Database)

## Step 2: Create Database Schema

1. Go to Supabase SQL Editor
2. Run the migration script located at `supabase/migrations/20240713_001_init.sql`
3. This creates:
   - `brands` table
   - `categories` table  
   - `products` table
   - `stores` table
   - `prices` table
   - Required indexes and Row Level Security policies

## Step 3: Configure Environment Variables

### For Node.js (Woolworths) scraper:

Create `.env` file in `Woolworths/` directory:
```env
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

### For C# scrapers (PakNSave, New World):

Update `appsettings.json` in each project:
```json
{
  "POSTGRES_CONNECTION": "Host=db.your-project.supabase.co;Port=5432;Database=postgres;Username=postgres;Password=your-password"
}
```

## Step 4: Export Data from CosmosDB

1. Install dependencies:
   ```bash
   cd scripts
   npm install @azure/cosmos
   ```

2. Set CosmosDB credentials:
   ```bash
   export COSMOS_ENDPOINT=https://your-cosmos.documents.azure.com:443/
   export COSMOS_KEY=your-cosmos-key
   ```

3. Run export:
   ```bash
   node export-from-cosmos.js
   ```

This creates JSON files in `cosmos-export/` directory.

## Step 5: Import Data to Supabase

1. Install Supabase client:
   ```bash
   npm install @supabase/supabase-js
   ```

2. Set Supabase credentials:
   ```bash
   export SUPABASE_URL=https://your-project.supabase.co
   export SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
   ```

3. Run import:
   ```bash
   node import-to-supabase.js cosmos-export/products_all.json
   ```

## Step 6: Update Scrapers

### Node.js (Woolworths):
```bash
cd Woolworths
npm install
npm run db  # Runs scraper with database mode
```

### C# Scrapers:
```bash
cd paknsave
dotnet restore
dotnet run db  # Runs scraper with database mode
```

## Step 7: Verify Migration

Run these SQL queries in Supabase to verify:

```sql
-- Check product count
SELECT COUNT(*) FROM products;

-- Check price history count
SELECT COUNT(*) FROM prices;

-- View latest prices per store
SELECT 
  p.name,
  s.name as store,
  pr.price,
  pr.recorded_at
FROM prices pr
JOIN products p ON p.id = pr.product_id
JOIN stores s ON s.id = pr.store_id
WHERE pr.recorded_at > NOW() - INTERVAL '7 days'
ORDER BY pr.recorded_at DESC
LIMIT 100;
```

## Common SQL Queries

### Find cheapest price for a product across stores:
```sql
SELECT DISTINCT ON (p.id)
  p.id,
  p.name,
  pr.price,
  s.name as store
FROM products p
JOIN prices pr ON pr.product_id = p.id
JOIN stores s ON s.id = pr.store_id
ORDER BY p.id, pr.price ASC, pr.recorded_at DESC;
```

### Get price history for a specific product:
```sql
SELECT 
  price,
  recorded_at,
  s.name as store
FROM prices pr
JOIN stores s ON s.id = pr.store_id  
WHERE product_id = 'YOUR_PRODUCT_ID'
ORDER BY recorded_at DESC;
```

### Compare prices between stores:
```sql
WITH latest_prices AS (
  SELECT DISTINCT ON (product_id, store_id)
    product_id,
    store_id,
    price,
    recorded_at
  FROM prices
  ORDER BY product_id, store_id, recorded_at DESC
)
SELECT 
  p.name,
  p.size,
  MAX(CASE WHEN s.name = 'paknsave' THEN lp.price END) as paknsave_price,
  MAX(CASE WHEN s.name = 'woolworths' THEN lp.price END) as woolworths_price,
  MAX(CASE WHEN s.name = 'newworld' THEN lp.price END) as newworld_price
FROM products p
JOIN latest_prices lp ON lp.product_id = p.id
JOIN stores s ON s.id = lp.store_id
GROUP BY p.id, p.name, p.size
HAVING COUNT(DISTINCT s.name) > 1
ORDER BY p.name;
```

## Real-time Subscriptions (for mobile app)

```javascript
// Subscribe to price changes
const subscription = supabase
  .channel('price-changes')
  .on(
    'postgres_changes',
    { 
      event: 'INSERT', 
      schema: 'public', 
      table: 'prices' 
    },
    (payload) => {
      console.log('New price:', payload.new);
    }
  )
  .subscribe();
```

## Troubleshooting

### Connection Issues
- Ensure your IP is whitelisted in Supabase (Settings > Database > Connection Pooling)
- Check that SSL mode is enabled for production connections

### Performance
- Add composite indexes for common queries:
  ```sql
  CREATE INDEX idx_prices_product_store_date 
  ON prices(product_id, store_id, recorded_at DESC);
  ```

### Data Integrity
- The schema uses foreign key constraints
- Duplicate price entries are prevented at the application level
- Consider adding a unique constraint if needed:
  ```sql
  ALTER TABLE prices 
  ADD CONSTRAINT unique_price_per_product_store_time 
  UNIQUE (product_id, store_id, recorded_at);
  ```

## Cleanup

After successful migration:
1. Remove CosmosDB connection strings from environment files
2. Cancel/delete your CosmosDB instance to avoid charges
3. Remove `@azure/cosmos` dependencies from package.json files

## Support

For issues:
- Check Supabase logs in Dashboard > Logs
- Review scraper console output for detailed errors
- Ensure all environment variables are correctly set 