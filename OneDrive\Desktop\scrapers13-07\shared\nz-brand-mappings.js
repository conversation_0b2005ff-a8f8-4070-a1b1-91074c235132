/**
 * Comprehensive New Zealand Brand Mappings for Product Matching
 * 
 * This file contains extensive mappings of New Zealand supermarket brands,
 * private labels, and product variations to enable accurate cross-store
 * product consolidation and matching.
 * 
 * Research sources:
 * - Major NZ supermarket chains (Woolworths/Countdown, New World, PakNSave)
 * - Food industry reports and brand databases
 * - Product recall databases and food composition tables
 * - Supermarket websites and product catalogs
 */

// Comprehensive New Zealand Brand Mappings
export const nzBrandMappings = {
    // === SUPERMARKET PRIVATE LABELS ===
    // Woolworths/Countdown Brands
    'pams': ['pam', 'pams brand', 'pams select'],
    'essentials': ['essentials brand', 'countdown essentials'],
    'macro': ['macro brand', 'macro organic'],
    'woolworths': ['woolworths brand', 'woolworths select'],
    'countdown': ['countdown brand', 'countdown select'],
    
    // Foodstuffs Brands (New World/PakNSave)
    'homebrand': ['home brand', 'homebrand select'],
    'signature range': ['signature', 'signature brand'],
    'value': ['value brand', 'budget'],
    'fresh choice': ['freshchoice', 'fresh choice brand'],
    
    // === DAIRY BRANDS ===
    // Major Dairy Brands
    'anchor': ['anchor brand', 'anchor dairy', 'anchor milk', 'anchor butter', 'anchor cheese'],
    'mainland': ['mainland cheese', 'mainland dairy', 'mainland brand'],
    'meadowfresh': ['meadow fresh', 'meadowfresh milk', 'meadow fresh milk'],
    'lewis road creamery': ['lewis road', 'lewis road milk', 'lewis road butter'],
    'kapiti': ['kapiti cheese', 'kapiti ice cream', 'kapiti brand'],
    'fernleaf': ['fernleaf milk', 'fernleaf powder'],
    'tararua': ['tararua cheese', 'tararua dairy'],
    'rolling meadow': ['rolling meadow cheese', 'rolling meadow dairy'],
    'whitestone': ['whitestone cheese', 'whitestone dairy'],
    'mercer': ['mercer cheese', 'mercer dairy'],
    'epicure': ['epicure cheese', 'epicure dairy'],
    'kikorangi': ['kikorangi cheese', 'kikorangi blue'],
    
    // === MEAT BRANDS ===
    // Poultry
    'tegel': ['tegel chicken', 'tegel poultry', 'tegel brand'],
    'inghams': ['ingham', 'inghams chicken', 'inghams poultry'],
    'turks': ['turks poultry', 'turks chicken'],
    'brinks': ['brinks chicken', 'brinks poultry'],
    
    // Processed Meats
    'hellers': ['heller', 'hellers bacon', 'hellers sausages', 'hellers smallgoods'],
    'beehive': ['beehive bacon', 'beehive ham', 'beehive smallgoods'],
    'farmland': ['farmland bacon', 'farmland ham'],
    'primo': ['primo smallgoods', 'primo bacon'],
    'hans': ['hans smallgoods', 'hans continental'],
    'continental': ['continental smallgoods', 'continental deli'],
    
    // === BREAD & BAKERY BRANDS ===
    'tip top': ['tiptop', 'tip top bread', 'tiptop bread'],
    'molenberg': ['molenberg bread', 'molenburg', 'molenberg wholemeal'],
    'vogels': ['vogel', 'vogels bread', 'vogel bread', 'vogels original'],
    'freyas': ['freya', 'freyas bread', 'freya bread'],
    'natures fresh': ['nature fresh', 'natures fresh bread'],
    'burgen': ['burgen bread', 'burgen soy lin'],
    'ploughmans': ['ploughman', 'ploughmans bread'],
    'golden': ['golden bread', 'golden bakery'],
    'bakers delight': ['bakersdelight', 'bakers delight bread'],
    
    // === BEVERAGE BRANDS ===
    // Soft Drinks
    'coca cola': ['coke', 'coca-cola', 'cocacola'],
    'coke': ['coca cola', 'coca-cola', 'coke classic'],
    'coke zero': ['coca cola zero', 'coke zero sugar', 'coca cola zero sugar'],
    'diet coke': ['coca cola diet', 'diet coca cola'],
    'pepsi': ['pepsi cola', 'pepsi classic'],
    'pepsi max': ['pepsi maximum taste', 'pepsi max no sugar'],
    'fanta': ['fanta orange', 'fanta grape'],
    'sprite': ['sprite lemon', 'sprite lime'],
    'l&p': ['lemon paeroa', 'lemon and paeroa', 'l and p'],
    
    // Juices
    'just juice': ['justjuice', 'just juice brand'],
    'fresh up': ['freshup', 'fresh up juice'],
    'keri': ['keri juice', 'keri fresh'],
    'charlies': ['charlie', 'charlies juice', 'charlies honest'],
    'phoenix': ['phoenix organic', 'phoenix juice'],
    
    // Water & Sports Drinks
    'pump': ['pump water', 'pump brand'],
    'powerade': ['powerade sports drink'],
    'gatorade': ['gatorade sports drink'],
    
    // === CEREAL & BREAKFAST BRANDS ===
    'sanitarium': ['sanitarium weetbix', 'sanitarium so good'],
    'weetbix': ['weet bix', 'wheat biscuits', 'sanitarium weetbix'],
    'uncle tobys': ['uncle toby', 'uncle tobys oats', 'uncle tobys muesli'],
    'kelloggs': ['kellogg', 'kelloggs cornflakes', 'kelloggs special k'],
    'cornflakes': ['corn flakes', 'kelloggs cornflakes'],
    'nutrigrain': ['nutri grain', 'kelloggs nutrigrain'],
    'special k': ['specialk', 'kelloggs special k'],
    'hubbards': ['hubbards cereal', 'hubbards muesli'],
    
    // === SNACK & CONFECTIONERY BRANDS ===
    'cadbury': ['cadburys', 'cadbury chocolate'],
    'whittakers': ['whittaker', 'whittakers chocolate'],
    'nestle': ['nestlé', 'nestle chocolate'],
    'mars': ['mars bar', 'mars chocolate'],
    'snickers': ['snickers bar', 'snickers chocolate'],
    'kit kat': ['kitkat', 'kit-kat'],
    'twix': ['twix bar', 'twix chocolate'],
    'moro': ['moro bar', 'moro chocolate'],
    'picnic': ['picnic bar', 'picnic chocolate'],
    'crunchie': ['crunchie bar', 'crunchie chocolate'],
    
    // Chips & Snacks
    'bluebird': ['bluebird chips', 'bluebird snacks'],
    'eta': ['eta chips', 'eta snacks'],
    'proper': ['proper chips', 'proper crisps'],
    'heartland': ['heartland chips', 'heartland snacks'],

    // === PANTRY & COOKING BRANDS ===
    'watties': ['wattie', 'watties tomatoes', 'watties sauce'],
    'heinz': ['heinz beans', 'heinz tomato sauce', 'heinz soup'],
    'maggi': ['maggi noodles', 'maggi soup'],
    'continental': ['continental soup', 'continental pasta'],
    'mccains': ['mccain', 'mccains chips', 'mccains frozen'],
    'edgell': ['edgell vegetables', 'edgell canned'],
    'greggs': ['greggs coffee', 'greggs instant'],
    'nescafe': ['nescafé', 'nescafe coffee'],
    'moccona': ['moccona coffee', 'moccona instant'],
    'robert harris': ['robertharris', 'robert harris coffee'],
    'bell tea': ['bell tea bags', 'bell black tea'],
    'dilmah': ['dilmah tea', 'dilmah ceylon'],

    // Cooking Oils & Condiments
    'olivani': ['olivani oil', 'olivani olive oil'],
    'bertolli': ['bertolli oil', 'bertolli olive oil'],
    'praise': ['praise mayonnaise', 'praise dressing'],
    'best foods': ['bestfoods', 'best foods mayo'],
    'masterfoods': ['master foods', 'masterfoods sauce'],
    'fountain': ['fountain sauce', 'fountain tomato sauce'],

    // === FROZEN FOOD BRANDS ===
    'mccains': ['mccain', 'mccains chips', 'mccains vegetables'],
    'birds eye': ['birdseye', 'birds eye vegetables', 'birds eye fish'],
    'talley': ['talleys', 'talley vegetables', 'talley frozen'],
    'pams frozen': ['pams vegetables', 'pams frozen range'],

    // === CLEANING & HOUSEHOLD BRANDS ===
    'janola': ['janola bleach', 'janola cleaning'],
    'earthwise': ['earthwise cleaning', 'earthwise eco'],
    'finish': ['finish dishwasher', 'finish tablets'],
    'ajax': ['ajax spray', 'ajax cleaning'],
    'jif': ['jif cream cleanser', 'jif bathroom'],
    'domestos': ['domestos bleach', 'domestos toilet'],
    'toilet duck': ['toiletduck', 'toilet duck cleaner'],
    'mr muscle': ['mrmuscle', 'mr muscle bathroom'],
    'windex': ['windex glass', 'windex cleaner'],

    // Laundry
    'persil': ['persil washing powder', 'persil liquid'],
    'surf': ['surf washing powder', 'surf liquid'],
    'omo': ['omo washing powder', 'omo liquid'],
    'cold power': ['coldpower', 'cold power liquid'],
    'dynamo': ['dynamo washing liquid'],

    // Paper Products
    'sorbent': ['sorbent toilet paper', 'sorbent tissues'],
    'kleenex': ['kleenex tissues', 'kleenex toilet paper'],
    'quilton': ['quilton toilet paper', 'quilton tissues'],
    'treasures': ['treasures toilet paper', 'treasures tissues'],

    // === HEALTH & BEAUTY BRANDS ===
    'colgate': ['colgate toothpaste', 'colgate toothbrush'],
    'oral b': ['oral-b', 'oralb', 'oral b toothbrush'],
    'sensodyne': ['sensodyne toothpaste'],
    'macleans': ['macleans toothpaste'],
    'head shoulders': ['head and shoulders', 'head & shoulders'],
    'pantene': ['pantene shampoo', 'pantene conditioner'],
    'herbal essences': ['herbal essence', 'herbal essences shampoo'],
    'dove': ['dove soap', 'dove body wash'],
    'nivea': ['nivea cream', 'nivea body'],
    'vaseline': ['vaseline petroleum jelly'],

    // === BABY & PERSONAL CARE ===
    'huggies': ['huggies nappies', 'huggies diapers'],
    'pampers': ['pampers nappies', 'pampers diapers'],
    'johnson': ['johnsons', 'johnson baby', 'johnsons baby'],
    'bepanthen': ['bepanthen cream', 'bepanthen nappy'],

    // === PET FOOD BRANDS ===
    'pedigree': ['pedigree dog food', 'pedigree dry'],
    'whiskas': ['whiskas cat food', 'whiskas wet'],
    'fancy feast': ['fancyfeast', 'fancy feast cat'],
    'royal canin': ['royalcanin', 'royal canin dog'],
    'hills': ['hills pet food', 'hills science diet'],
    'eukanuba': ['eukanuba dog food'],
    'iams': ['iams pet food', 'iams dog'],
    'optimum': ['optimum dog food', 'optimum pet'],
    'tux': ['tux cat food', 'tux pet'],
    'champ': ['champ dog food', 'champ pet'],

    // === ADDITIONAL NZ SPECIFIC BRANDS ===
    // Ice Cream
    'tip top ice cream': ['tiptop ice cream', 'tip top icecream'],
    'kapiti ice cream': ['kapiti icecream', 'kapiti gelato'],
    'new zealand natural': ['nz natural', 'new zealand natural ice cream'],
    'deep south': ['deep south ice cream', 'deep south icecream'],

    // Biscuits & Cookies
    'griffins': ['griffin', 'griffins biscuits', 'griffin biscuits'],
    'arnott': ['arnotts', 'arnott biscuits', 'arnotts biscuits'],
    'toffee pops': ['toffeepops', 'griffins toffee pops'],
    'mallowpuffs': ['mallow puffs', 'griffins mallowpuffs'],

    // Spreads & Jams
    'pic': ['pics', 'pic peanut butter', 'pics peanut butter'],
    'eta': ['eta peanut butter', 'eta spreads'],
    'sanitarium': ['sanitarium peanut butter', 'sanitarium spreads'],
    'cottees': ['cottee', 'cottees jam', 'cottee jam'],
    'barkers': ['barkers jam', 'barkers preserves'],

    // Pasta & Rice
    'san remo': ['sanremo', 'san remo pasta'],
    'latina': ['latina pasta', 'latina fresh'],
    'uncle bens': ['uncle ben', 'uncle bens rice'],
    'sunrice': ['sun rice', 'sunrice brand'],

    // Soup & Canned Goods
    'campbells': ['campbell', 'campbells soup', 'campbell soup'],
    'delmaine': ['delmaine vegetables', 'delmaine canned'],
    'john west': ['johnwest', 'john west tuna', 'johnwest tuna'],
    'sirena': ['sirena tuna', 'sirena seafood'],

    // Flour & Baking
    'edmonds': ['edmonds flour', 'edmonds baking'],
    'champion': ['champion flour', 'champion baking'],
    'chelsea': ['chelsea sugar', 'chelsea baking'],
    'tararua': ['tararua flour', 'tararua baking']
};

// Store descriptor removal patterns
export const storeDescriptors = [
    'woolworths', 'countdown', 'new world', 'paknsave', 'pak n save',
    'select', 'premium', 'value', 'budget', 'signature', 'essentials',
    'pams', 'homebrand', 'signature range', 'fresh choice', 'macro',
    'organic', 'free range', 'grass fed', 'natural', 'artisan',
    'gourmet', 'deluxe', 'finest', 'choice', 'quality', 'fresh',
    'pure', 'real', 'authentic', 'traditional', 'classic'
];

// Size normalization patterns
export const sizeNormalizations = {
    'grams': 'g',
    'gram': 'g',
    'kilograms': 'kg',
    'kilogram': 'kg',
    'kg': 'kg',
    'litres': 'l',
    'litre': 'l',
    'ltr': 'l',
    'millilitres': 'ml',
    'millilitre': 'ml',
    'pieces': 'pc',
    'piece': 'pc',
    'each': 'ea',
    'pack': 'pk',
    'packet': 'pk'
};

// Common product category mappings
export const categoryMappings = {
    'milk': ['dairy', 'fresh', 'refrigerated'],
    'bread': ['bakery', 'fresh', 'baked goods'],
    'chicken': ['meat', 'poultry', 'fresh'],
    'beef': ['meat', 'red meat', 'fresh'],
    'cheese': ['dairy', 'refrigerated'],
    'yoghurt': ['dairy', 'refrigerated'],
    'butter': ['dairy', 'refrigerated'],
    'eggs': ['dairy', 'fresh'],
    'bacon': ['meat', 'smallgoods', 'refrigerated'],
    'sausages': ['meat', 'smallgoods', 'refrigerated'],
    'chips': ['snacks', 'pantry'],
    'chocolate': ['confectionery', 'snacks'],
    'cereal': ['breakfast', 'pantry'],
    'juice': ['beverages', 'refrigerated'],
    'soft drink': ['beverages', 'pantry'],
    'water': ['beverages', 'pantry'],
    'toilet paper': ['household', 'paper products'],
    'cleaning': ['household', 'cleaning products']
};

// Export combined mappings for backward compatibility
export const manualMappings = {
    ...nzBrandMappings,
    // Additional legacy mappings
    'coke zero': ['coca cola zero', 'coke zero sugar', 'coca cola zero sugar', 'coke no sugar'],
    'diet coke': ['coca cola diet', 'diet coca cola', 'coke diet'],
    'tip top bread': ['tiptop bread', 'tip top', 'tiptop'],
    'molenberg bread': ['molenberg', 'molenburg bread', 'molenberg wholemeal'],
    'vogels bread': ['vogel bread', 'vogels original', 'vogel original'],
    'anchor milk': ['anchor blue milk', 'anchor standard milk', 'anchor whole milk'],
    'meadowfresh milk': ['meadow fresh milk', 'meadowfresh standard', 'meadow fresh standard'],
    'lewis road milk': ['lewis road creamery milk', 'lewis road creamery'],
    'chicken breast': ['chicken breast fillets', 'chicken breast fillet', 'free range chicken breast'],
    'beef mince': ['beef mince premium', 'premium beef mince', 'lean beef mince'],
    'potatoes': ['potato', 'agria potatoes', 'red potatoes', 'white potatoes', 'new potatoes'],
    'onions': ['onion', 'brown onions', 'red onions', 'white onions', 'spanish onions'],
    'apples': ['apple', 'royal gala apples', 'braeburn apples', 'granny smith apples'],
    'bananas': ['banana', 'cavendish bananas', 'organic bananas'],
    'cornflakes': ['corn flakes', 'kelloggs cornflakes', 'kellogg cornflakes'],
    'weetbix': ['weet bix', 'sanitarium weetbix', 'wheat biscuits'],
    'dishwashing liquid': ['dish liquid', 'dishwash liquid', 'washing up liquid'],
    'toilet paper': ['toilet tissue', 'bathroom tissue', 'loo paper']
};

// Helper function to get brand mapping
export function getBrandMapping(productName) {
    const normalized = productName.toLowerCase();
    
    for (const [canonical, variations] of Object.entries(nzBrandMappings)) {
        if (normalized.includes(canonical)) {
            return canonical;
        }
        
        for (const variation of variations) {
            if (normalized.includes(variation)) {
                return canonical;
            }
        }
    }
    
    return null;
}

// Helper function to remove store descriptors
export function removeStoreDescriptors(productName) {
    let cleaned = productName.toLowerCase();
    
    for (const descriptor of storeDescriptors) {
        const regex = new RegExp(`\\b${descriptor}\\b`, 'gi');
        cleaned = cleaned.replace(regex, '').trim();
    }
    
    return cleaned.replace(/\s+/g, ' ').trim();
}

// Helper function to normalize size
export function normalizeSize(size) {
    if (!size) return '';
    
    let normalized = size.toLowerCase().replace(/[^\w\d]/g, '');
    
    for (const [original, replacement] of Object.entries(sizeNormalizations)) {
        normalized = normalized.replace(original, replacement);
    }
    
    return normalized;
}
