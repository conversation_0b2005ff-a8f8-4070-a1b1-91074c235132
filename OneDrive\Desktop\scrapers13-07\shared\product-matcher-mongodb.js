/**
 * Product Matching Algorithm for Cross-Store Product Identification (MongoDB Version)
 * 
 * This module provides functions to match products across different supermarket stores
 * by comparing product names, sizes, brands, and other attributes using MongoDB.
 */

import { MongoClient, ObjectId } from 'mongodb';

// MongoDB client (should be initialized with your connection string)
let client = null;
let db = null;
let consolidatedProductsCollection = null;

export async function initializeMongoDB(connectionString, databaseName = 'nz-supermarket-scraper') {
    client = new MongoClient(connectionString);
    await client.connect();
    db = client.db(databaseName);
    consolidatedProductsCollection = db.collection('consolidatedProducts');
    
    // Ensure text index exists for search
    await consolidatedProductsCollection.createIndex({
        "displayName": "text",
        "normalizedName": "text",
        "variants.storeName": "text"
    });
}

/**
 * Close MongoDB connection
 */
export async function closeMongoDB() {
    if (client) {
        await client.close();
    }
}

/**
 * Normalize product name for matching
 * Removes brand names, common descriptors, and standardizes format
 */
export function normalizeProductName(name, brandName = '') {
    let normalized = name.toLowerCase().trim();
    
    // Remove brand name if provided
    if (brandName) {
        const brandRegex = new RegExp(brandName.toLowerCase(), 'gi');
        normalized = normalized.replace(brandRegex, '').trim();
    }
    
    // Remove common store-specific descriptors
    const descriptorsToRemove = [
        'countdown', 'woolworths', 'new world', 'paknsave', 'pak n save',
        'select', 'premium', 'value', 'budget', 'signature', 'essentials',
        'organic', 'free range', 'grass fed', 'natural', 'home brand'
    ];
    
    for (const descriptor of descriptorsToRemove) {
        const regex = new RegExp(`\\b${descriptor}\\b`, 'gi');
        normalized = normalized.replace(regex, '').trim();
    }
    
    // Remove extra whitespace and special characters
    normalized = normalized
        .replace(/[^\w\s]/g, ' ')          // Replace special chars with spaces
        .replace(/\s+/g, ' ')             // Collapse multiple spaces
        .replace(/\b(the|and|or|with|in|of|for)\b/g, '') // Remove common words
        .trim();
    
    return normalized;
}

/**
 * Normalize product size for comparison
 * Converts units and standardizes format
 */
export function normalizeSize(size) {
    if (!size) return '';
    
    let normalized = size.toLowerCase().trim();
    
    // Convert common unit variations
    const unitConversions = {
        'ml': 'ml',
        'millilitres': 'ml',
        'milliliters': 'ml',
        'l': 'l',
        'litres': 'l',
        'liters': 'l',
        'g': 'g',
        'grams': 'g',
        'kg': 'kg',
        'kilograms': 'kg',
        'oz': 'oz',
        'ounces': 'oz',
        'lb': 'lb',
        'pounds': 'lb'
    };
    
    // Extract number and unit
    const match = normalized.match(/(\d+(?:\.\d+)?)\s*([a-z]+)/);
    if (match) {
        let [, number, unit] = match;
        
        // Convert to standard units
        if (unitConversions[unit]) {
            unit = unitConversions[unit];
        }
        
        // Convert to larger units where appropriate
        const numValue = parseFloat(number);
        if (unit === 'ml' && numValue >= 1000) {
            return `${numValue / 1000}l`;
        }
        if (unit === 'g' && numValue >= 1000) {
            return `${numValue / 1000}kg`;
        }
        
        return `${number}${unit}`;
    }
    
    return normalized;
}

/**
 * Calculate similarity score between two strings using Levenshtein distance
 * Returns a score between 0 and 1, where 1 is identical
 */
export function calculateSimilarity(str1, str2) {
    if (str1 === str2) return 1;
    if (!str1 || !str2) return 0;
    
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1;
    
    const distance = levenshteinDistance(longer, shorter);
    return (longer.length - distance) / longer.length;
}

/**
 * Calculate Levenshtein distance between two strings
 */
function levenshteinDistance(str1, str2) {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
        matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
        matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
        for (let j = 1; j <= str1.length; j++) {
            if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                matrix[i][j] = matrix[i - 1][j - 1];
            } else {
                matrix[i][j] = Math.min(
                    matrix[i - 1][j - 1] + 1,
                    matrix[i][j - 1] + 1,
                    matrix[i - 1][j] + 1
                );
            }
        }
    }
    
    return matrix[str2.length][str1.length];
}

/**
 * Calculate overall product match score
 * Combines name similarity, size match, and brand match
 */
export function calculateProductMatchScore(product1, product2) {
    const weights = {
        name: 0.5,
        size: 0.3,
        brand: 0.2
    };
    
    // Name similarity
    const nameScore = calculateSimilarity(
        normalizeProductName(product1.name),
        normalizeProductName(product2.name)
    );
    
    // Size similarity
    const size1 = normalizeSize(product1.size);
    const size2 = normalizeSize(product2.size);
    const sizeScore = size1 && size2 ? (size1 === size2 ? 1 : 0) : 0.5; // Neutral if size missing
    
    // Brand similarity (if available)
    const brand1 = product1.brand || '';
    const brand2 = product2.brand || '';
    const brandScore = brand1 && brand2 ? calculateSimilarity(brand1, brand2) : 0.5;
    
    return (nameScore * weights.name) + (sizeScore * weights.size) + (brandScore * weights.brand);
}

/**
 * Find matching consolidated product for a new product
 * Returns the best match or null if no good match found
 */
export async function findMatchingConsolidatedProduct(product, minScore = 0.75) {
    if (!consolidatedProductsCollection) {
        throw new Error('MongoDB not initialized');
    }
    
    const normalizedName = normalizeProductName(product.name);
    const normalizedSize = normalizeSize(product.size);
    
    // First, try exact normalized name match
    const exactMatch = await consolidatedProductsCollection.findOne({
        normalizedName: normalizedName
    });
    
    if (exactMatch) {
        return { product: exactMatch, score: 1.0 };
    }
    
    // If no exact match, use text search for similar products
    const searchTerms = normalizedName
        .split(/\s+/)
        .filter(term => term.length > 2)
        .slice(0, 5) // Use first 5 significant terms
        .join(' ');
    
    if (!searchTerms) {
        return null;
    }
    
    const candidates = await consolidatedProductsCollection.find({
        $text: { $search: searchTerms }
    }).limit(10).toArray();
    
    let bestMatch = null;
    let bestScore = 0;
    
    for (const candidate of candidates) {
        const score = calculateProductMatchScore(product, {
            name: candidate.displayName,
            size: candidate.primarySize,
            brand: candidate.brandName // Assuming brand is populated
        });
        
        if (score > bestScore && score >= minScore) {
            bestScore = score;
            bestMatch = candidate;
        }
    }
    
    return bestMatch ? { product: bestMatch, score: bestScore } : null;
}

/**
 * Create a new consolidated product from a single product
 */
export async function createConsolidatedProduct(product, storeId) {
    if (!consolidatedProductsCollection) {
        throw new Error('MongoDB not initialized');
    }
    
    const normalizedName = normalizeProductName(product.name);
    const now = new Date();
    
    const consolidatedProduct = {
        normalizedName,
        displayName: product.name,
        primarySize: product.size,
        categoryId: null, // TODO: Implement category mapping
        brandId: null, // TODO: Implement brand extraction
        matchConfidence: 100,
        manualMatch: false,
        variants: [{
            storeProductId: product.id,
            storeId: new ObjectId(storeId),
            storeName: product.name,
            storeSize: product.size,
            storeUnitPrice: product.unitPrice,
            storeUnitName: product.unitName,
            lastSeen: now,
            isActive: true,
            imageUrl: null
        }],
        sizeVariants: product.size ? [{
            sizeName: product.size,
            sizeWeightGrams: null,
            sizeVolumeMl: null,
            isPrimarySize: true
        }] : [],
        currentPrices: [{
            storeId: new ObjectId(storeId),
            price: product.currentPrice,
            isSpecial: false,
            wasAvailable: true,
            recordedAt: now
        }],
        createdAt: now,
        updatedAt: now
    };
    
    const result = await consolidatedProductsCollection.insertOne(consolidatedProduct);
    return result.insertedId;
}

/**
 * Add a variant to an existing consolidated product
 */
export async function addVariantToConsolidatedProduct(consolidatedProductId, product, storeId) {
    if (!consolidatedProductsCollection) {
        throw new Error('MongoDB not initialized');
    }
    
    const now = new Date();
    
    // Check if this variant already exists
    const existingVariant = await consolidatedProductsCollection.findOne({
        _id: new ObjectId(consolidatedProductId),
        "variants.storeProductId": product.id
    });
    
    if (existingVariant) {
        // Update existing variant
        await consolidatedProductsCollection.updateOne(
            { _id: new ObjectId(consolidatedProductId) },
            {
                $set: {
                    "variants.$[variant].lastSeen": now,
                    "variants.$[variant].storeUnitPrice": product.unitPrice,
                    "variants.$[variant].storeUnitName": product.unitName,
                    updatedAt: now
                }
            },
            {
                arrayFilters: [{ "variant.storeProductId": product.id }]
            }
        );
    } else {
        // Add new variant
        await consolidatedProductsCollection.updateOne(
            { _id: new ObjectId(consolidatedProductId) },
            {
                $push: {
                    variants: {
                        storeProductId: product.id,
                        storeId: new ObjectId(storeId),
                        storeName: product.name,
                        storeSize: product.size,
                        storeUnitPrice: product.unitPrice,
                        storeUnitName: product.unitName,
                        lastSeen: now,
                        isActive: true,
                        imageUrl: null
                    }
                },
                $set: { updatedAt: now }
            }
        );
    }
    
    // Update current price for this store
    await consolidatedProductsCollection.updateOne(
        { _id: new ObjectId(consolidatedProductId) },
        {
            $set: {
                "currentPrices.$[price].price": product.currentPrice,
                "currentPrices.$[price].recordedAt": now
            }
        },
        {
            arrayFilters: [{ "price.storeId": new ObjectId(storeId) }],
            upsert: false
        }
    );
    
    // If no price exists for this store, add it
    const hasPrice = await consolidatedProductsCollection.findOne({
        _id: new ObjectId(consolidatedProductId),
        "currentPrices.storeId": new ObjectId(storeId)
    });
    
    if (!hasPrice) {
        await consolidatedProductsCollection.updateOne(
            { _id: new ObjectId(consolidatedProductId) },
            {
                $push: {
                    currentPrices: {
                        storeId: new ObjectId(storeId),
                        price: product.currentPrice,
                        isSpecial: false,
                        wasAvailable: true,
                        recordedAt: now
                    }
                }
            }
        );
    }
}

/**
 * Main function to process a product for consolidation
 * Returns the consolidated product ID
 */
export async function processProductForConsolidation(product, storeId, minMatchScore = 0.75) {
    try {
        // Try to find matching consolidated product
        const match = await findMatchingConsolidatedProduct(product, minMatchScore);
        
        if (match) {
            // Add as variant to existing consolidated product
            await addVariantToConsolidatedProduct(match.product._id, product, storeId);
            return match.product._id.toString();
        } else {
            // Create new consolidated product
            const consolidatedId = await createConsolidatedProduct(product, storeId);
            return consolidatedId.toString();
        }
    } catch (error) {
        console.error('Error processing product for consolidation:', error);
        return null;
    }
}

// Export for testing
export { levenshteinDistance };