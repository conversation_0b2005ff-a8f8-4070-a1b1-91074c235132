@echo off
echo Starting MongoDB for the scraper system...
echo.

REM Check if MongoDB is installed on Windows
where mongod >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo MongoDB is not installed or not in PATH.
    echo.
    echo Please install MongoDB Community Server from:
    echo https://www.mongodb.com/try/download/community
    echo.
    echo Or use Docker:
    echo docker run -d -p 27017:27017 --name mongodb mongo:latest
    echo.
    pause
    exit /b 1
)

REM Create data directory if it doesn't exist
if not exist "C:\data\db" (
    echo Creating MongoDB data directory...
    mkdir "C:\data\db"
)

REM Start MongoDB
echo Starting MongoDB on port 27017...
start /B mongod --dbpath "C:\data\db" --port 27017

echo.
echo MongoDB should now be running on localhost:27017
echo You can now run the scrapers with:
echo   - Woolworths: cd Woolworths && npm run db
echo   - New World: cd new-world/src && dotnet run db  
echo   - PakNSave: cd paknsave/src && dotnet run db
echo.
pause