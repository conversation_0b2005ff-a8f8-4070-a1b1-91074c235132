# MongoDB Schema Design for NZ Supermarket Price Scraper

## Overview
This document outlines the MongoDB collections and document structures to replace the existing Supabase PostgreSQL database.

## Collection Design

### 1. stores
```javascript
{
  _id: ObjectId(),
  storeId: "woolworths", // Short identifier
  name: "Woolworths",
  logoUrl: "https://...",
  createdAt: ISODate(),
  updatedAt: ISODate()
}
```

### 2. brands
```javascript
{
  _id: ObjectId(),
  name: "Coca Cola",
  normalizedName: "coca_cola", // For search optimization
  createdAt: ISODate()
}
```

### 3. categoryHierarchy
```javascript
{
  _id: ObjectId(),
  name: "Fresh Foods",
  parentId: ObjectId() | null, // null for main categories
  level: 0, // 0=main, 1=subcategory
  sortOrder: 1,
  createdAt: ISODate()
}
```

### 4. consolidatedProducts
```javascript
{
  _id: ObjectId(),
  normalizedName: "coca_cola_original_1_5l",
  displayName: "Coca Cola Original 1.5L",
  primarySize: "1.5L",
  categoryId: ObjectId(),
  brandId: ObjectId(),
  
  // Matching metadata
  matchConfidence: 100,
  manualMatch: false,
  
  // Store variants embedded
  variants: [
    {
      storeProductId: "W123456",
      storeId: ObjectId(),
      storeName: "Coca Cola Original 1.5L",
      storeSize: "1.5L",
      storeUnitPrice: 3.50,
      storeUnitName: "per bottle",
      lastSeen: ISODate(),
      isActive: true,
      imageUrl: "gridfs://product-images/W123456.jpg"
    }
  ],
  
  // Size variants
  sizeVariants: [
    {
      sizeName: "1.5L",
      sizeWeightGrams: null,
      sizeVolumeMl: 1500,
      isPrimarySize: true
    }
  ],
  
  // Latest prices from each store
  currentPrices: [
    {
      storeId: ObjectId(),
      price: 3.50,
      isSpecial: false,
      wasAvailable: true,
      recordedAt: ISODate()
    }
  ],
  
  createdAt: ISODate(),
  updatedAt: ISODate()
}
```

### 5. priceHistory
```javascript
{
  _id: ObjectId(),
  consolidatedProductId: ObjectId(),
  storeId: ObjectId(),
  price: 3.50,
  isSpecial: false,
  wasAvailable: true,
  recordedAt: ISODate(),
  
  // Partitioning fields for performance
  year: 2025,
  month: 1
}
```

### 6. productImages (GridFS metadata)
```javascript
// GridFS files collection
{
  _id: ObjectId(),
  filename: "W123456.jpg",
  uploadDate: ISODate(),
  length: 45231,
  chunkSize: 261120,
  md5: "d41d8cd98f00b204e9800998ecf8427e",
  contentType: "image/jpeg",
  metadata: {
    productId: "W123456",
    storeId: ObjectId(),
    originalUrl: "https://cdn.woolworths.com.au/..."
  }
}
```

## Index Strategy

### consolidatedProducts
```javascript
// Text search index
db.consolidatedProducts.createIndex({
  "displayName": "text",
  "normalizedName": "text",
  "variants.storeName": "text"
})

// Category and brand filtering
db.consolidatedProducts.createIndex({ "categoryId": 1 })
db.consolidatedProducts.createIndex({ "brandId": 1 })

// Store-specific lookups
db.consolidatedProducts.createIndex({ "variants.storeProductId": 1, "variants.storeId": 1 })

// Price sorting
db.consolidatedProducts.createIndex({ "currentPrices.storeId": 1, "currentPrices.price": 1 })
```

### priceHistory
```javascript
// Time-based queries
db.priceHistory.createIndex({ "consolidatedProductId": 1, "recordedAt": -1 })
db.priceHistory.createIndex({ "year": 1, "month": 1 })

// Store-specific price history
db.priceHistory.createIndex({ "storeId": 1, "recordedAt": -1 })
```

### categoryHierarchy
```javascript
db.categoryHierarchy.createIndex({ "parentId": 1, "sortOrder": 1 })
db.categoryHierarchy.createIndex({ "level": 1, "sortOrder": 1 })
```

## Query Patterns

### Get all products with current prices
```javascript
db.consolidatedProducts.aggregate([
  {
    $lookup: {
      from: "brands",
      localField: "brandId",
      foreignField: "_id",
      as: "brand"
    }
  },
  {
    $lookup: {
      from: "categoryHierarchy",
      localField: "categoryId", 
      foreignField: "_id",
      as: "category"
    }
  },
  {
    $unwind: { path: "$brand", preserveNullAndEmptyArrays: true }
  },
  {
    $unwind: { path: "$category", preserveNullAndEmptyArrays: true }
  }
])
```

### Search products
```javascript
db.consolidatedProducts.find({
  $text: { $search: "coca cola" }
}).sort({ score: { $meta: "textScore" } })
```

### Get price history for a product
```javascript
db.priceHistory.find({
  consolidatedProductId: ObjectId("..."),
  recordedAt: { $gte: new Date(Date.now() - 30*24*60*60*1000) }
}).sort({ recordedAt: -1 })
```

## Migration Considerations

### Data Migration Strategy
1. Export data from Supabase to JSON
2. Transform relational data to document structure
3. Import into MongoDB collections
4. Create indexes for performance
5. Validate data integrity

### Performance Optimizations
1. Embed frequently accessed related data (variants, current prices)
2. Use GridFS for image storage with proper metadata
3. Partition price history by year/month
4. Implement text search for product discovery
5. Cache frequently accessed category hierarchies

### Consistency Patterns
1. Use transactions for multi-collection updates
2. Implement optimistic locking for concurrent updates
3. Use change streams for real-time updates
4. Maintain referential integrity through application logic