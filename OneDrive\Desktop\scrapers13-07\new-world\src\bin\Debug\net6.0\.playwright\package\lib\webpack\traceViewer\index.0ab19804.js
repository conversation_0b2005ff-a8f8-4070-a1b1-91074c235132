var $w=Object.defineProperty;var Kw=(e,n,i)=>n in e?$w(e,n,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[n]=i;var gn=(e,n,i)=>(Kw(e,typeof n!="symbol"?n+"":n,i),i);(function(){const n=document.createElement("link").relList;if(n&&n.supports&&n.supports("modulepreload"))return;for(const u of document.querySelectorAll('link[rel="modulepreload"]'))a(u);new MutationObserver(u=>{for(const c of u)if(c.type==="childList")for(const h of c.addedNodes)h.tagName==="LINK"&&h.rel==="modulepreload"&&a(h)}).observe(document,{childList:!0,subtree:!0});function i(u){const c={};return u.integrity&&(c.integrity=u.integrity),u.referrerpolicy&&(c.referrerPolicy=u.referrerpolicy),u.crossorigin==="use-credentials"?c.credentials="include":u.crossorigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function a(u){if(u.ep)return;u.ep=!0;const c=i(u);fetch(u.href,c)}})();var Gw=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},Ho={},qw={get exports(){return Ho},set exports(e){Ho=e}},tu={},ye={},Vw={get exports(){return ye},set exports(e){ye=e}},Oe={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ls=Symbol.for("react.element"),Qw=Symbol.for("react.portal"),Yw=Symbol.for("react.fragment"),Xw=Symbol.for("react.strict_mode"),Zw=Symbol.for("react.profiler"),Jw=Symbol.for("react.provider"),e1=Symbol.for("react.context"),t1=Symbol.for("react.forward_ref"),n1=Symbol.for("react.suspense"),r1=Symbol.for("react.memo"),i1=Symbol.for("react.lazy"),Op=Symbol.iterator;function l1(e){return e===null||typeof e!="object"?null:(e=Op&&e[Op]||e["@@iterator"],typeof e=="function"?e:null)}var rg={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},ig=Object.assign,lg={};function Ol(e,n,i){this.props=e,this.context=n,this.refs=lg,this.updater=i||rg}Ol.prototype.isReactComponent={};Ol.prototype.setState=function(e,n){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,n,"setState")};Ol.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function og(){}og.prototype=Ol.prototype;function Wc(e,n,i){this.props=e,this.context=n,this.refs=lg,this.updater=i||rg}var Bc=Wc.prototype=new og;Bc.constructor=Wc;ig(Bc,Ol.prototype);Bc.isPureReactComponent=!0;var zp=Array.isArray,sg=Object.prototype.hasOwnProperty,Hc={current:null},ag={key:!0,ref:!0,__self:!0,__source:!0};function ug(e,n,i){var a,u={},c=null,h=null;if(n!=null)for(a in n.ref!==void 0&&(h=n.ref),n.key!==void 0&&(c=""+n.key),n)sg.call(n,a)&&!ag.hasOwnProperty(a)&&(u[a]=n[a]);var v=arguments.length-2;if(v===1)u.children=i;else if(1<v){for(var m=Array(v),x=0;x<v;x++)m[x]=arguments[x+2];u.children=m}if(e&&e.defaultProps)for(a in v=e.defaultProps,v)u[a]===void 0&&(u[a]=v[a]);return{$$typeof:ls,type:e,key:c,ref:h,props:u,_owner:Hc.current}}function o1(e,n){return{$$typeof:ls,type:e.type,key:n,ref:e.ref,props:e.props,_owner:e._owner}}function Uc(e){return typeof e=="object"&&e!==null&&e.$$typeof===ls}function s1(e){var n={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(i){return n[i]})}var Ip=/\/+/g;function pf(e,n){return typeof e=="object"&&e!==null&&e.key!=null?s1(""+e.key):n.toString(36)}function ca(e,n,i,a,u){var c=typeof e;(c==="undefined"||c==="boolean")&&(e=null);var h=!1;if(e===null)h=!0;else switch(c){case"string":case"number":h=!0;break;case"object":switch(e.$$typeof){case ls:case Qw:h=!0}}if(h)return h=e,u=u(h),e=a===""?"."+pf(h,0):a,zp(u)?(i="",e!=null&&(i=e.replace(Ip,"$&/")+"/"),ca(u,n,i,"",function(x){return x})):u!=null&&(Uc(u)&&(u=o1(u,i+(!u.key||h&&h.key===u.key?"":(""+u.key).replace(Ip,"$&/")+"/")+e)),n.push(u)),1;if(h=0,a=a===""?".":a+":",zp(e))for(var v=0;v<e.length;v++){c=e[v];var m=a+pf(c,v);h+=ca(c,n,i,m,u)}else if(m=l1(e),typeof m=="function")for(e=m.call(e),v=0;!(c=e.next()).done;)c=c.value,m=a+pf(c,v++),h+=ca(c,n,i,m,u);else if(c==="object")throw n=String(e),Error("Objects are not valid as a React child (found: "+(n==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":n)+"). If you meant to render a collection of children, use an array instead.");return h}function qs(e,n,i){if(e==null)return e;var a=[],u=0;return ca(e,a,"","",function(c){return n.call(i,c,u++)}),a}function a1(e){if(e._status===-1){var n=e._result;n=n(),n.then(function(i){(e._status===0||e._status===-1)&&(e._status=1,e._result=i)},function(i){(e._status===0||e._status===-1)&&(e._status=2,e._result=i)}),e._status===-1&&(e._status=0,e._result=n)}if(e._status===1)return e._result.default;throw e._result}var Zt={current:null},da={transition:null},u1={ReactCurrentDispatcher:Zt,ReactCurrentBatchConfig:da,ReactCurrentOwner:Hc};Oe.Children={map:qs,forEach:function(e,n,i){qs(e,function(){n.apply(this,arguments)},i)},count:function(e){var n=0;return qs(e,function(){n++}),n},toArray:function(e){return qs(e,function(n){return n})||[]},only:function(e){if(!Uc(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};Oe.Component=Ol;Oe.Fragment=Yw;Oe.Profiler=Zw;Oe.PureComponent=Wc;Oe.StrictMode=Xw;Oe.Suspense=n1;Oe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=u1;Oe.cloneElement=function(e,n,i){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=ig({},e.props),u=e.key,c=e.ref,h=e._owner;if(n!=null){if(n.ref!==void 0&&(c=n.ref,h=Hc.current),n.key!==void 0&&(u=""+n.key),e.type&&e.type.defaultProps)var v=e.type.defaultProps;for(m in n)sg.call(n,m)&&!ag.hasOwnProperty(m)&&(a[m]=n[m]===void 0&&v!==void 0?v[m]:n[m])}var m=arguments.length-2;if(m===1)a.children=i;else if(1<m){v=Array(m);for(var x=0;x<m;x++)v[x]=arguments[x+2];a.children=v}return{$$typeof:ls,type:e.type,key:u,ref:c,props:a,_owner:h}};Oe.createContext=function(e){return e={$$typeof:e1,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Jw,_context:e},e.Consumer=e};Oe.createElement=ug;Oe.createFactory=function(e){var n=ug.bind(null,e);return n.type=e,n};Oe.createRef=function(){return{current:null}};Oe.forwardRef=function(e){return{$$typeof:t1,render:e}};Oe.isValidElement=Uc;Oe.lazy=function(e){return{$$typeof:i1,_payload:{_status:-1,_result:e},_init:a1}};Oe.memo=function(e,n){return{$$typeof:r1,type:e,compare:n===void 0?null:n}};Oe.startTransition=function(e){var n=da.transition;da.transition={};try{e()}finally{da.transition=n}};Oe.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")};Oe.useCallback=function(e,n){return Zt.current.useCallback(e,n)};Oe.useContext=function(e){return Zt.current.useContext(e)};Oe.useDebugValue=function(){};Oe.useDeferredValue=function(e){return Zt.current.useDeferredValue(e)};Oe.useEffect=function(e,n){return Zt.current.useEffect(e,n)};Oe.useId=function(){return Zt.current.useId()};Oe.useImperativeHandle=function(e,n,i){return Zt.current.useImperativeHandle(e,n,i)};Oe.useInsertionEffect=function(e,n){return Zt.current.useInsertionEffect(e,n)};Oe.useLayoutEffect=function(e,n){return Zt.current.useLayoutEffect(e,n)};Oe.useMemo=function(e,n){return Zt.current.useMemo(e,n)};Oe.useReducer=function(e,n,i){return Zt.current.useReducer(e,n,i)};Oe.useRef=function(e){return Zt.current.useRef(e)};Oe.useState=function(e){return Zt.current.useState(e)};Oe.useSyncExternalStore=function(e,n,i){return Zt.current.useSyncExternalStore(e,n,i)};Oe.useTransition=function(){return Zt.current.useTransition()};Oe.version="18.1.0";(function(e){e.exports=Oe})(Vw);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var f1=ye,c1=Symbol.for("react.element"),d1=Symbol.for("react.fragment"),h1=Object.prototype.hasOwnProperty,p1=f1.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,v1={key:!0,ref:!0,__self:!0,__source:!0};function fg(e,n,i){var a,u={},c=null,h=null;i!==void 0&&(c=""+i),n.key!==void 0&&(c=""+n.key),n.ref!==void 0&&(h=n.ref);for(a in n)h1.call(n,a)&&!v1.hasOwnProperty(a)&&(u[a]=n[a]);if(e&&e.defaultProps)for(a in n=e.defaultProps,n)u[a]===void 0&&(u[a]=n[a]);return{$$typeof:c1,type:e,key:c,ref:h,props:u,_owner:p1.current}}tu.Fragment=d1;tu.jsx=fg;tu.jsxs=fg;(function(e){e.exports=tu})(qw);const cg=Ho.Fragment,H=Ho.jsx,ge=Ho.jsxs;var jf={},g1={get exports(){return jf},set exports(e){jf=e}},xn={},$f={},m1={get exports(){return $f},set exports(e){$f=e}},dg={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function n(C,b){var U=C.length;C.push(b);e:for(;0<U;){var ne=U-1>>>1,ue=C[ne];if(0<u(ue,b))C[ne]=b,C[U]=ue,U=ne;else break e}}function i(C){return C.length===0?null:C[0]}function a(C){if(C.length===0)return null;var b=C[0],U=C.pop();if(U!==b){C[0]=U;e:for(var ne=0,ue=C.length,Se=ue>>>1;ne<Se;){var je=2*(ne+1)-1,Ye=C[je],pe=je+1,ce=C[pe];if(0>u(Ye,U))pe<ue&&0>u(ce,Ye)?(C[ne]=ce,C[pe]=U,ne=pe):(C[ne]=Ye,C[je]=U,ne=je);else if(pe<ue&&0>u(ce,U))C[ne]=ce,C[pe]=U,ne=pe;else break e}}return b}function u(C,b){var U=C.sortIndex-b.sortIndex;return U!==0?U:C.id-b.id}if(typeof performance=="object"&&typeof performance.now=="function"){var c=performance;e.unstable_now=function(){return c.now()}}else{var h=Date,v=h.now();e.unstable_now=function(){return h.now()-v}}var m=[],x=[],L=1,R=null,P=3,W=!1,B=!1,Y=!1,K=typeof setTimeout=="function"?setTimeout:null,T=typeof clearTimeout=="function"?clearTimeout:null,S=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function M(C){for(var b=i(x);b!==null;){if(b.callback===null)a(x);else if(b.startTime<=C)a(x),b.sortIndex=b.expirationTime,n(m,b);else break;b=i(x)}}function A(C){if(Y=!1,M(C),!B)if(i(m)!==null)B=!0,X(G);else{var b=i(x);b!==null&&ee(A,b.startTime-C)}}function G(C,b){B=!1,Y&&(Y=!1,T(Z),Z=-1),W=!0;var U=P;try{for(M(b),R=i(m);R!==null&&(!(R.expirationTime>b)||C&&!ze());){var ne=R.callback;if(typeof ne=="function"){R.callback=null,P=R.priorityLevel;var ue=ne(R.expirationTime<=b);b=e.unstable_now(),typeof ue=="function"?R.callback=ue:R===i(m)&&a(m),M(b)}else a(m);R=i(m)}if(R!==null)var Se=!0;else{var je=i(x);je!==null&&ee(A,je.startTime-b),Se=!1}return Se}finally{R=null,P=U,W=!1}}var Q=!1,z=null,Z=-1,Ce=5,we=-1;function ze(){return!(e.unstable_now()-we<Ce)}function V(){if(z!==null){var C=e.unstable_now();we=C;var b=!0;try{b=z(!0,C)}finally{b?F():(Q=!1,z=null)}}else Q=!1}var F;if(typeof S=="function")F=function(){S(V)};else if(typeof MessageChannel<"u"){var le=new MessageChannel,_=le.port2;le.port1.onmessage=V,F=function(){_.postMessage(null)}}else F=function(){K(V,0)};function X(C){z=C,Q||(Q=!0,F())}function ee(C,b){Z=K(function(){C(e.unstable_now())},b)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(C){C.callback=null},e.unstable_continueExecution=function(){B||W||(B=!0,X(G))},e.unstable_forceFrameRate=function(C){0>C||125<C?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Ce=0<C?Math.floor(1e3/C):5},e.unstable_getCurrentPriorityLevel=function(){return P},e.unstable_getFirstCallbackNode=function(){return i(m)},e.unstable_next=function(C){switch(P){case 1:case 2:case 3:var b=3;break;default:b=P}var U=P;P=b;try{return C()}finally{P=U}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(C,b){switch(C){case 1:case 2:case 3:case 4:case 5:break;default:C=3}var U=P;P=C;try{return b()}finally{P=U}},e.unstable_scheduleCallback=function(C,b,U){var ne=e.unstable_now();switch(typeof U=="object"&&U!==null?(U=U.delay,U=typeof U=="number"&&0<U?ne+U:ne):U=ne,C){case 1:var ue=-1;break;case 2:ue=250;break;case 5:ue=**********;break;case 4:ue=1e4;break;default:ue=5e3}return ue=U+ue,C={id:L++,callback:b,priorityLevel:C,startTime:U,expirationTime:ue,sortIndex:-1},U>ne?(C.sortIndex=U,n(x,C),i(m)===null&&C===i(x)&&(Y?(T(Z),Z=-1):Y=!0,ee(A,U-ne))):(C.sortIndex=ue,n(m,C),B||W||(B=!0,X(G))),C},e.unstable_shouldYield=ze,e.unstable_wrapCallback=function(C){var b=P;return function(){var U=P;P=b;try{return C.apply(this,arguments)}finally{P=U}}}})(dg);(function(e){e.exports=dg})(m1);/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var hg=ye,wn=$f;function te(e){for(var n="https://reactjs.org/docs/error-decoder.html?invariant="+e,i=1;i<arguments.length;i++)n+="&args[]="+encodeURIComponent(arguments[i]);return"Minified React error #"+e+"; visit "+n+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var pg=new Set,Uo={};function Ii(e,n){Ll(e,n),Ll(e+"Capture",n)}function Ll(e,n){for(Uo[e]=n,e=0;e<n.length;e++)pg.add(n[e])}var br=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Kf=Object.prototype.hasOwnProperty,y1=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Ap={},Rp={};function w1(e){return Kf.call(Rp,e)?!0:Kf.call(Ap,e)?!1:y1.test(e)?Rp[e]=!0:(Ap[e]=!0,!1)}function x1(e,n,i,a){if(i!==null&&i.type===0)return!1;switch(typeof n){case"function":case"symbol":return!0;case"boolean":return a?!1:i!==null?!i.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function S1(e,n,i,a){if(n===null||typeof n>"u"||x1(e,n,i,a))return!0;if(a)return!1;if(i!==null)switch(i.type){case 3:return!n;case 4:return n===!1;case 5:return isNaN(n);case 6:return isNaN(n)||1>n}return!1}function Jt(e,n,i,a,u,c,h){this.acceptsBooleans=n===2||n===3||n===4,this.attributeName=a,this.attributeNamespace=u,this.mustUseProperty=i,this.propertyName=e,this.type=n,this.sanitizeURL=c,this.removeEmptyString=h}var Ot={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Ot[e]=new Jt(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var n=e[0];Ot[n]=new Jt(n,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Ot[e]=new Jt(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Ot[e]=new Jt(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Ot[e]=new Jt(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Ot[e]=new Jt(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Ot[e]=new Jt(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Ot[e]=new Jt(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Ot[e]=new Jt(e,5,!1,e.toLowerCase(),null,!1,!1)});var jc=/[\-:]([a-z])/g;function $c(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var n=e.replace(jc,$c);Ot[n]=new Jt(n,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var n=e.replace(jc,$c);Ot[n]=new Jt(n,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var n=e.replace(jc,$c);Ot[n]=new Jt(n,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Ot[e]=new Jt(e,1,!1,e.toLowerCase(),null,!1,!1)});Ot.xlinkHref=new Jt("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Ot[e]=new Jt(e,1,!1,e.toLowerCase(),null,!0,!0)});function Kc(e,n,i,a){var u=Ot.hasOwnProperty(n)?Ot[n]:null;(u!==null?u.type!==0:a||!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(S1(n,i,u,a)&&(i=null),a||u===null?w1(n)&&(i===null?e.removeAttribute(n):e.setAttribute(n,""+i)):u.mustUseProperty?e[u.propertyName]=i===null?u.type===3?!1:"":i:(n=u.attributeName,a=u.attributeNamespace,i===null?e.removeAttribute(n):(u=u.type,i=u===3||u===4&&i===!0?"":""+i,a?e.setAttributeNS(a,n,i):e.setAttribute(n,i))))}var Dr=hg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Vs=Symbol.for("react.element"),sl=Symbol.for("react.portal"),al=Symbol.for("react.fragment"),Gc=Symbol.for("react.strict_mode"),Gf=Symbol.for("react.profiler"),vg=Symbol.for("react.provider"),gg=Symbol.for("react.context"),qc=Symbol.for("react.forward_ref"),qf=Symbol.for("react.suspense"),Vf=Symbol.for("react.suspense_list"),Vc=Symbol.for("react.memo"),qr=Symbol.for("react.lazy"),mg=Symbol.for("react.offscreen"),Fp=Symbol.iterator;function yo(e){return e===null||typeof e!="object"?null:(e=Fp&&e[Fp]||e["@@iterator"],typeof e=="function"?e:null)}var it=Object.assign,vf;function Eo(e){if(vf===void 0)try{throw Error()}catch(i){var n=i.stack.trim().match(/\n( *(at )?)/);vf=n&&n[1]||""}return`
`+vf+e}var gf=!1;function mf(e,n){if(!e||gf)return"";gf=!0;var i=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(n)if(n=function(){throw Error()},Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(n,[])}catch(x){var a=x}Reflect.construct(e,[],n)}else{try{n.call()}catch(x){a=x}e.call(n.prototype)}else{try{throw Error()}catch(x){a=x}e()}}catch(x){if(x&&a&&typeof x.stack=="string"){for(var u=x.stack.split(`
`),c=a.stack.split(`
`),h=u.length-1,v=c.length-1;1<=h&&0<=v&&u[h]!==c[v];)v--;for(;1<=h&&0<=v;h--,v--)if(u[h]!==c[v]){if(h!==1||v!==1)do if(h--,v--,0>v||u[h]!==c[v]){var m=`
`+u[h].replace(" at new "," at ");return e.displayName&&m.includes("<anonymous>")&&(m=m.replace("<anonymous>",e.displayName)),m}while(1<=h&&0<=v);break}}}finally{gf=!1,Error.prepareStackTrace=i}return(e=e?e.displayName||e.name:"")?Eo(e):""}function k1(e){switch(e.tag){case 5:return Eo(e.type);case 16:return Eo("Lazy");case 13:return Eo("Suspense");case 19:return Eo("SuspenseList");case 0:case 2:case 15:return e=mf(e.type,!1),e;case 11:return e=mf(e.type.render,!1),e;case 1:return e=mf(e.type,!0),e;default:return""}}function Qf(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case al:return"Fragment";case sl:return"Portal";case Gf:return"Profiler";case Gc:return"StrictMode";case qf:return"Suspense";case Vf:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case gg:return(e.displayName||"Context")+".Consumer";case vg:return(e._context.displayName||"Context")+".Provider";case qc:var n=e.render;return e=e.displayName,e||(e=n.displayName||n.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Vc:return n=e.displayName||null,n!==null?n:Qf(e.type)||"Memo";case qr:n=e._payload,e=e._init;try{return Qf(e(n))}catch{}}return null}function C1(e){var n=e.type;switch(e.tag){case 24:return"Cache";case 9:return(n.displayName||"Context")+".Consumer";case 10:return(n._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=n.render,e=e.displayName||e.name||"",n.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return n;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Qf(n);case 8:return n===Gc?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof n=="function")return n.displayName||n.name||null;if(typeof n=="string")return n}return null}function li(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function yg(e){var n=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(n==="checkbox"||n==="radio")}function T1(e){var n=yg(e)?"checked":"value",i=Object.getOwnPropertyDescriptor(e.constructor.prototype,n),a=""+e[n];if(!e.hasOwnProperty(n)&&typeof i<"u"&&typeof i.get=="function"&&typeof i.set=="function"){var u=i.get,c=i.set;return Object.defineProperty(e,n,{configurable:!0,get:function(){return u.call(this)},set:function(h){a=""+h,c.call(this,h)}}),Object.defineProperty(e,n,{enumerable:i.enumerable}),{getValue:function(){return a},setValue:function(h){a=""+h},stopTracking:function(){e._valueTracker=null,delete e[n]}}}}function Qs(e){e._valueTracker||(e._valueTracker=T1(e))}function wg(e){if(!e)return!1;var n=e._valueTracker;if(!n)return!0;var i=n.getValue(),a="";return e&&(a=yg(e)?e.checked?"true":"false":e.value),e=a,e!==i?(n.setValue(e),!0):!1}function ba(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Yf(e,n){var i=n.checked;return it({},n,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:i??e._wrapperState.initialChecked})}function Wp(e,n){var i=n.defaultValue==null?"":n.defaultValue,a=n.checked!=null?n.checked:n.defaultChecked;i=li(n.value!=null?n.value:i),e._wrapperState={initialChecked:a,initialValue:i,controlled:n.type==="checkbox"||n.type==="radio"?n.checked!=null:n.value!=null}}function xg(e,n){n=n.checked,n!=null&&Kc(e,"checked",n,!1)}function Xf(e,n){xg(e,n);var i=li(n.value),a=n.type;if(i!=null)a==="number"?(i===0&&e.value===""||e.value!=i)&&(e.value=""+i):e.value!==""+i&&(e.value=""+i);else if(a==="submit"||a==="reset"){e.removeAttribute("value");return}n.hasOwnProperty("value")?Zf(e,n.type,i):n.hasOwnProperty("defaultValue")&&Zf(e,n.type,li(n.defaultValue)),n.checked==null&&n.defaultChecked!=null&&(e.defaultChecked=!!n.defaultChecked)}function Bp(e,n,i){if(n.hasOwnProperty("value")||n.hasOwnProperty("defaultValue")){var a=n.type;if(!(a!=="submit"&&a!=="reset"||n.value!==void 0&&n.value!==null))return;n=""+e._wrapperState.initialValue,i||n===e.value||(e.value=n),e.defaultValue=n}i=e.name,i!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,i!==""&&(e.name=i)}function Zf(e,n,i){(n!=="number"||ba(e.ownerDocument)!==e)&&(i==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+i&&(e.defaultValue=""+i))}var bo=Array.isArray;function wl(e,n,i,a){if(e=e.options,n){n={};for(var u=0;u<i.length;u++)n["$"+i[u]]=!0;for(i=0;i<e.length;i++)u=n.hasOwnProperty("$"+e[i].value),e[i].selected!==u&&(e[i].selected=u),u&&a&&(e[i].defaultSelected=!0)}else{for(i=""+li(i),n=null,u=0;u<e.length;u++){if(e[u].value===i){e[u].selected=!0,a&&(e[u].defaultSelected=!0);return}n!==null||e[u].disabled||(n=e[u])}n!==null&&(n.selected=!0)}}function Jf(e,n){if(n.dangerouslySetInnerHTML!=null)throw Error(te(91));return it({},n,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Hp(e,n){var i=n.value;if(i==null){if(i=n.children,n=n.defaultValue,i!=null){if(n!=null)throw Error(te(92));if(bo(i)){if(1<i.length)throw Error(te(93));i=i[0]}n=i}n==null&&(n=""),i=n}e._wrapperState={initialValue:li(i)}}function Sg(e,n){var i=li(n.value),a=li(n.defaultValue);i!=null&&(i=""+i,i!==e.value&&(e.value=i),n.defaultValue==null&&e.defaultValue!==i&&(e.defaultValue=i)),a!=null&&(e.defaultValue=""+a)}function Up(e){var n=e.textContent;n===e._wrapperState.initialValue&&n!==""&&n!==null&&(e.value=n)}function kg(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ec(e,n){return e==null||e==="http://www.w3.org/1999/xhtml"?kg(n):e==="http://www.w3.org/2000/svg"&&n==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Ys,Cg=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(n,i,a,u){MSApp.execUnsafeLocalFunction(function(){return e(n,i,a,u)})}:e}(function(e,n){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=n;else{for(Ys=Ys||document.createElement("div"),Ys.innerHTML="<svg>"+n.valueOf().toString()+"</svg>",n=Ys.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;n.firstChild;)e.appendChild(n.firstChild)}});function jo(e,n){if(n){var i=e.firstChild;if(i&&i===e.lastChild&&i.nodeType===3){i.nodeValue=n;return}}e.textContent=n}var Po={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},L1=["Webkit","ms","Moz","O"];Object.keys(Po).forEach(function(e){L1.forEach(function(n){n=n+e.charAt(0).toUpperCase()+e.substring(1),Po[n]=Po[e]})});function Tg(e,n,i){return n==null||typeof n=="boolean"||n===""?"":i||typeof n!="number"||n===0||Po.hasOwnProperty(e)&&Po[e]?(""+n).trim():n+"px"}function Lg(e,n){e=e.style;for(var i in n)if(n.hasOwnProperty(i)){var a=i.indexOf("--")===0,u=Tg(i,n[i],a);i==="float"&&(i="cssFloat"),a?e.setProperty(i,u):e[i]=u}}var N1=it({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function tc(e,n){if(n){if(N1[e]&&(n.children!=null||n.dangerouslySetInnerHTML!=null))throw Error(te(137,e));if(n.dangerouslySetInnerHTML!=null){if(n.children!=null)throw Error(te(60));if(typeof n.dangerouslySetInnerHTML!="object"||!("__html"in n.dangerouslySetInnerHTML))throw Error(te(61))}if(n.style!=null&&typeof n.style!="object")throw Error(te(62))}}function nc(e,n){if(e.indexOf("-")===-1)return typeof n.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var rc=null;function Qc(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ic=null,xl=null,Sl=null;function jp(e){if(e=as(e)){if(typeof ic!="function")throw Error(te(280));var n=e.stateNode;n&&(n=ou(n),ic(e.stateNode,e.type,n))}}function Ng(e){xl?Sl?Sl.push(e):Sl=[e]:xl=e}function Eg(){if(xl){var e=xl,n=Sl;if(Sl=xl=null,jp(e),n)for(e=0;e<n.length;e++)jp(n[e])}}function bg(e,n){return e(n)}function Mg(){}var yf=!1;function _g(e,n,i){if(yf)return e(n,i);yf=!0;try{return bg(e,n,i)}finally{yf=!1,(xl!==null||Sl!==null)&&(Mg(),Eg())}}function $o(e,n){var i=e.stateNode;if(i===null)return null;var a=ou(i);if(a===null)return null;i=a[n];e:switch(n){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(e=e.type,a=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!a;break e;default:e=!1}if(e)return null;if(i&&typeof i!="function")throw Error(te(231,n,typeof i));return i}var lc=!1;if(br)try{var wo={};Object.defineProperty(wo,"passive",{get:function(){lc=!0}}),window.addEventListener("test",wo,wo),window.removeEventListener("test",wo,wo)}catch{lc=!1}function E1(e,n,i,a,u,c,h,v,m){var x=Array.prototype.slice.call(arguments,3);try{n.apply(i,x)}catch(L){this.onError(L)}}var Oo=!1,Ma=null,_a=!1,oc=null,b1={onError:function(e){Oo=!0,Ma=e}};function M1(e,n,i,a,u,c,h,v,m){Oo=!1,Ma=null,E1.apply(b1,arguments)}function _1(e,n,i,a,u,c,h,v,m){if(M1.apply(this,arguments),Oo){if(Oo){var x=Ma;Oo=!1,Ma=null}else throw Error(te(198));_a||(_a=!0,oc=x)}}function Ai(e){var n=e,i=e;if(e.alternate)for(;n.return;)n=n.return;else{e=n;do n=e,n.flags&4098&&(i=n.return),e=n.return;while(e)}return n.tag===3?i:null}function Dg(e){if(e.tag===13){var n=e.memoizedState;if(n===null&&(e=e.alternate,e!==null&&(n=e.memoizedState)),n!==null)return n.dehydrated}return null}function $p(e){if(Ai(e)!==e)throw Error(te(188))}function D1(e){var n=e.alternate;if(!n){if(n=Ai(e),n===null)throw Error(te(188));return n!==e?null:e}for(var i=e,a=n;;){var u=i.return;if(u===null)break;var c=u.alternate;if(c===null){if(a=u.return,a!==null){i=a;continue}break}if(u.child===c.child){for(c=u.child;c;){if(c===i)return $p(u),e;if(c===a)return $p(u),n;c=c.sibling}throw Error(te(188))}if(i.return!==a.return)i=u,a=c;else{for(var h=!1,v=u.child;v;){if(v===i){h=!0,i=u,a=c;break}if(v===a){h=!0,a=u,i=c;break}v=v.sibling}if(!h){for(v=c.child;v;){if(v===i){h=!0,i=c,a=u;break}if(v===a){h=!0,a=c,i=u;break}v=v.sibling}if(!h)throw Error(te(189))}}if(i.alternate!==a)throw Error(te(190))}if(i.tag!==3)throw Error(te(188));return i.stateNode.current===i?e:n}function Pg(e){return e=D1(e),e!==null?Og(e):null}function Og(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var n=Og(e);if(n!==null)return n;e=e.sibling}return null}var zg=wn.unstable_scheduleCallback,Kp=wn.unstable_cancelCallback,P1=wn.unstable_shouldYield,O1=wn.unstable_requestPaint,ut=wn.unstable_now,z1=wn.unstable_getCurrentPriorityLevel,Yc=wn.unstable_ImmediatePriority,Ig=wn.unstable_UserBlockingPriority,Da=wn.unstable_NormalPriority,I1=wn.unstable_LowPriority,Ag=wn.unstable_IdlePriority,nu=null,or=null;function A1(e){if(or&&typeof or.onCommitFiberRoot=="function")try{or.onCommitFiberRoot(nu,e,void 0,(e.current.flags&128)===128)}catch{}}var Gn=Math.clz32?Math.clz32:W1,R1=Math.log,F1=Math.LN2;function W1(e){return e>>>=0,e===0?32:31-(R1(e)/F1|0)|0}var Xs=64,Zs=4194304;function Mo(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Pa(e,n){var i=e.pendingLanes;if(i===0)return 0;var a=0,u=e.suspendedLanes,c=e.pingedLanes,h=i&268435455;if(h!==0){var v=h&~u;v!==0?a=Mo(v):(c&=h,c!==0&&(a=Mo(c)))}else h=i&~u,h!==0?a=Mo(h):c!==0&&(a=Mo(c));if(a===0)return 0;if(n!==0&&n!==a&&!(n&u)&&(u=a&-a,c=n&-n,u>=c||u===16&&(c&4194240)!==0))return n;if(a&4&&(a|=i&16),n=e.entangledLanes,n!==0)for(e=e.entanglements,n&=a;0<n;)i=31-Gn(n),u=1<<i,a|=e[i],n&=~u;return a}function B1(e,n){switch(e){case 1:case 2:case 4:return n+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return n+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function H1(e,n){for(var i=e.suspendedLanes,a=e.pingedLanes,u=e.expirationTimes,c=e.pendingLanes;0<c;){var h=31-Gn(c),v=1<<h,m=u[h];m===-1?(!(v&i)||v&a)&&(u[h]=B1(v,n)):m<=n&&(e.expiredLanes|=v),c&=~v}}function sc(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Rg(){var e=Xs;return Xs<<=1,!(Xs&4194240)&&(Xs=64),e}function wf(e){for(var n=[],i=0;31>i;i++)n.push(e);return n}function os(e,n,i){e.pendingLanes|=n,n!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,n=31-Gn(n),e[n]=i}function U1(e,n){var i=e.pendingLanes&~n;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=n,e.mutableReadLanes&=n,e.entangledLanes&=n,n=e.entanglements;var a=e.eventTimes;for(e=e.expirationTimes;0<i;){var u=31-Gn(i),c=1<<u;n[u]=0,a[u]=-1,e[u]=-1,i&=~c}}function Xc(e,n){var i=e.entangledLanes|=n;for(e=e.entanglements;i;){var a=31-Gn(i),u=1<<a;u&n|e[a]&n&&(e[a]|=n),i&=~u}}var He=0;function Fg(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Wg,Zc,Bg,Hg,Ug,ac=!1,Js=[],Jr=null,ei=null,ti=null,Ko=new Map,Go=new Map,Qr=[],j1="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Gp(e,n){switch(e){case"focusin":case"focusout":Jr=null;break;case"dragenter":case"dragleave":ei=null;break;case"mouseover":case"mouseout":ti=null;break;case"pointerover":case"pointerout":Ko.delete(n.pointerId);break;case"gotpointercapture":case"lostpointercapture":Go.delete(n.pointerId)}}function xo(e,n,i,a,u,c){return e===null||e.nativeEvent!==c?(e={blockedOn:n,domEventName:i,eventSystemFlags:a,nativeEvent:c,targetContainers:[u]},n!==null&&(n=as(n),n!==null&&Zc(n)),e):(e.eventSystemFlags|=a,n=e.targetContainers,u!==null&&n.indexOf(u)===-1&&n.push(u),e)}function $1(e,n,i,a,u){switch(n){case"focusin":return Jr=xo(Jr,e,n,i,a,u),!0;case"dragenter":return ei=xo(ei,e,n,i,a,u),!0;case"mouseover":return ti=xo(ti,e,n,i,a,u),!0;case"pointerover":var c=u.pointerId;return Ko.set(c,xo(Ko.get(c)||null,e,n,i,a,u)),!0;case"gotpointercapture":return c=u.pointerId,Go.set(c,xo(Go.get(c)||null,e,n,i,a,u)),!0}return!1}function jg(e){var n=Li(e.target);if(n!==null){var i=Ai(n);if(i!==null){if(n=i.tag,n===13){if(n=Dg(i),n!==null){e.blockedOn=n,Ug(e.priority,function(){Bg(i)});return}}else if(n===3&&i.stateNode.current.memoizedState.isDehydrated){e.blockedOn=i.tag===3?i.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ha(e){if(e.blockedOn!==null)return!1;for(var n=e.targetContainers;0<n.length;){var i=uc(e.domEventName,e.eventSystemFlags,n[0],e.nativeEvent);if(i===null){i=e.nativeEvent;var a=new i.constructor(i.type,i);rc=a,i.target.dispatchEvent(a),rc=null}else return n=as(i),n!==null&&Zc(n),e.blockedOn=i,!1;n.shift()}return!0}function qp(e,n,i){ha(e)&&i.delete(n)}function K1(){ac=!1,Jr!==null&&ha(Jr)&&(Jr=null),ei!==null&&ha(ei)&&(ei=null),ti!==null&&ha(ti)&&(ti=null),Ko.forEach(qp),Go.forEach(qp)}function So(e,n){e.blockedOn===n&&(e.blockedOn=null,ac||(ac=!0,wn.unstable_scheduleCallback(wn.unstable_NormalPriority,K1)))}function qo(e){function n(u){return So(u,e)}if(0<Js.length){So(Js[0],e);for(var i=1;i<Js.length;i++){var a=Js[i];a.blockedOn===e&&(a.blockedOn=null)}}for(Jr!==null&&So(Jr,e),ei!==null&&So(ei,e),ti!==null&&So(ti,e),Ko.forEach(n),Go.forEach(n),i=0;i<Qr.length;i++)a=Qr[i],a.blockedOn===e&&(a.blockedOn=null);for(;0<Qr.length&&(i=Qr[0],i.blockedOn===null);)jg(i),i.blockedOn===null&&Qr.shift()}var kl=Dr.ReactCurrentBatchConfig,Oa=!0;function G1(e,n,i,a){var u=He,c=kl.transition;kl.transition=null;try{He=1,Jc(e,n,i,a)}finally{He=u,kl.transition=c}}function q1(e,n,i,a){var u=He,c=kl.transition;kl.transition=null;try{He=4,Jc(e,n,i,a)}finally{He=u,kl.transition=c}}function Jc(e,n,i,a){if(Oa){var u=uc(e,n,i,a);if(u===null)Mf(e,n,a,za,i),Gp(e,a);else if($1(u,e,n,i,a))a.stopPropagation();else if(Gp(e,a),n&4&&-1<j1.indexOf(e)){for(;u!==null;){var c=as(u);if(c!==null&&Wg(c),c=uc(e,n,i,a),c===null&&Mf(e,n,a,za,i),c===u)break;u=c}u!==null&&a.stopPropagation()}else Mf(e,n,a,null,i)}}var za=null;function uc(e,n,i,a){if(za=null,e=Qc(a),e=Li(e),e!==null)if(n=Ai(e),n===null)e=null;else if(i=n.tag,i===13){if(e=Dg(n),e!==null)return e;e=null}else if(i===3){if(n.stateNode.current.memoizedState.isDehydrated)return n.tag===3?n.stateNode.containerInfo:null;e=null}else n!==e&&(e=null);return za=e,null}function $g(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(z1()){case Yc:return 1;case Ig:return 4;case Da:case I1:return 16;case Ag:return 536870912;default:return 16}default:return 16}}var Xr=null,ed=null,pa=null;function Kg(){if(pa)return pa;var e,n=ed,i=n.length,a,u="value"in Xr?Xr.value:Xr.textContent,c=u.length;for(e=0;e<i&&n[e]===u[e];e++);var h=i-e;for(a=1;a<=h&&n[i-a]===u[c-a];a++);return pa=u.slice(e,1<a?1-a:void 0)}function va(e){var n=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&n===13&&(e=13)):e=n,e===10&&(e=13),32<=e||e===13?e:0}function ea(){return!0}function Vp(){return!1}function Sn(e){function n(i,a,u,c,h){this._reactName=i,this._targetInst=u,this.type=a,this.nativeEvent=c,this.target=h,this.currentTarget=null;for(var v in e)e.hasOwnProperty(v)&&(i=e[v],this[v]=i?i(c):c[v]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?ea:Vp,this.isPropagationStopped=Vp,this}return it(n.prototype,{preventDefault:function(){this.defaultPrevented=!0;var i=this.nativeEvent;i&&(i.preventDefault?i.preventDefault():typeof i.returnValue!="unknown"&&(i.returnValue=!1),this.isDefaultPrevented=ea)},stopPropagation:function(){var i=this.nativeEvent;i&&(i.stopPropagation?i.stopPropagation():typeof i.cancelBubble!="unknown"&&(i.cancelBubble=!0),this.isPropagationStopped=ea)},persist:function(){},isPersistent:ea}),n}var zl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},td=Sn(zl),ss=it({},zl,{view:0,detail:0}),V1=Sn(ss),xf,Sf,ko,ru=it({},ss,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:nd,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ko&&(ko&&e.type==="mousemove"?(xf=e.screenX-ko.screenX,Sf=e.screenY-ko.screenY):Sf=xf=0,ko=e),xf)},movementY:function(e){return"movementY"in e?e.movementY:Sf}}),Qp=Sn(ru),Q1=it({},ru,{dataTransfer:0}),Y1=Sn(Q1),X1=it({},ss,{relatedTarget:0}),kf=Sn(X1),Z1=it({},zl,{animationName:0,elapsedTime:0,pseudoElement:0}),J1=Sn(Z1),ex=it({},zl,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),tx=Sn(ex),nx=it({},zl,{data:0}),Yp=Sn(nx),rx={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},ix={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},lx={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function ox(e){var n=this.nativeEvent;return n.getModifierState?n.getModifierState(e):(e=lx[e])?!!n[e]:!1}function nd(){return ox}var sx=it({},ss,{key:function(e){if(e.key){var n=rx[e.key]||e.key;if(n!=="Unidentified")return n}return e.type==="keypress"?(e=va(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?ix[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:nd,charCode:function(e){return e.type==="keypress"?va(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?va(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),ax=Sn(sx),ux=it({},ru,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Xp=Sn(ux),fx=it({},ss,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:nd}),cx=Sn(fx),dx=it({},zl,{propertyName:0,elapsedTime:0,pseudoElement:0}),hx=Sn(dx),px=it({},ru,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),vx=Sn(px),gx=[9,13,27,32],rd=br&&"CompositionEvent"in window,zo=null;br&&"documentMode"in document&&(zo=document.documentMode);var mx=br&&"TextEvent"in window&&!zo,Gg=br&&(!rd||zo&&8<zo&&11>=zo),Zp=String.fromCharCode(32),Jp=!1;function qg(e,n){switch(e){case"keyup":return gx.indexOf(n.keyCode)!==-1;case"keydown":return n.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Vg(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var ul=!1;function yx(e,n){switch(e){case"compositionend":return Vg(n);case"keypress":return n.which!==32?null:(Jp=!0,Zp);case"textInput":return e=n.data,e===Zp&&Jp?null:e;default:return null}}function wx(e,n){if(ul)return e==="compositionend"||!rd&&qg(e,n)?(e=Kg(),pa=ed=Xr=null,ul=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(n.ctrlKey||n.altKey||n.metaKey)||n.ctrlKey&&n.altKey){if(n.char&&1<n.char.length)return n.char;if(n.which)return String.fromCharCode(n.which)}return null;case"compositionend":return Gg&&n.locale!=="ko"?null:n.data;default:return null}}var xx={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function ev(e){var n=e&&e.nodeName&&e.nodeName.toLowerCase();return n==="input"?!!xx[e.type]:n==="textarea"}function Qg(e,n,i,a){Ng(a),n=Ia(n,"onChange"),0<n.length&&(i=new td("onChange","change",null,i,a),e.push({event:i,listeners:n}))}var Io=null,Vo=null;function Sx(e){om(e,0)}function iu(e){var n=dl(e);if(wg(n))return e}function kx(e,n){if(e==="change")return n}var Yg=!1;if(br){var Cf;if(br){var Tf="oninput"in document;if(!Tf){var tv=document.createElement("div");tv.setAttribute("oninput","return;"),Tf=typeof tv.oninput=="function"}Cf=Tf}else Cf=!1;Yg=Cf&&(!document.documentMode||9<document.documentMode)}function nv(){Io&&(Io.detachEvent("onpropertychange",Xg),Vo=Io=null)}function Xg(e){if(e.propertyName==="value"&&iu(Vo)){var n=[];Qg(n,Vo,e,Qc(e)),_g(Sx,n)}}function Cx(e,n,i){e==="focusin"?(nv(),Io=n,Vo=i,Io.attachEvent("onpropertychange",Xg)):e==="focusout"&&nv()}function Tx(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return iu(Vo)}function Lx(e,n){if(e==="click")return iu(n)}function Nx(e,n){if(e==="input"||e==="change")return iu(n)}function Ex(e,n){return e===n&&(e!==0||1/e===1/n)||e!==e&&n!==n}var qn=typeof Object.is=="function"?Object.is:Ex;function Qo(e,n){if(qn(e,n))return!0;if(typeof e!="object"||e===null||typeof n!="object"||n===null)return!1;var i=Object.keys(e),a=Object.keys(n);if(i.length!==a.length)return!1;for(a=0;a<i.length;a++){var u=i[a];if(!Kf.call(n,u)||!qn(e[u],n[u]))return!1}return!0}function rv(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function iv(e,n){var i=rv(e);e=0;for(var a;i;){if(i.nodeType===3){if(a=e+i.textContent.length,e<=n&&a>=n)return{node:i,offset:n-e};e=a}e:{for(;i;){if(i.nextSibling){i=i.nextSibling;break e}i=i.parentNode}i=void 0}i=rv(i)}}function Zg(e,n){return e&&n?e===n?!0:e&&e.nodeType===3?!1:n&&n.nodeType===3?Zg(e,n.parentNode):"contains"in e?e.contains(n):e.compareDocumentPosition?!!(e.compareDocumentPosition(n)&16):!1:!1}function Jg(){for(var e=window,n=ba();n instanceof e.HTMLIFrameElement;){try{var i=typeof n.contentWindow.location.href=="string"}catch{i=!1}if(i)e=n.contentWindow;else break;n=ba(e.document)}return n}function id(e){var n=e&&e.nodeName&&e.nodeName.toLowerCase();return n&&(n==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||n==="textarea"||e.contentEditable==="true")}function bx(e){var n=Jg(),i=e.focusedElem,a=e.selectionRange;if(n!==i&&i&&i.ownerDocument&&Zg(i.ownerDocument.documentElement,i)){if(a!==null&&id(i)){if(n=a.start,e=a.end,e===void 0&&(e=n),"selectionStart"in i)i.selectionStart=n,i.selectionEnd=Math.min(e,i.value.length);else if(e=(n=i.ownerDocument||document)&&n.defaultView||window,e.getSelection){e=e.getSelection();var u=i.textContent.length,c=Math.min(a.start,u);a=a.end===void 0?c:Math.min(a.end,u),!e.extend&&c>a&&(u=a,a=c,c=u),u=iv(i,c);var h=iv(i,a);u&&h&&(e.rangeCount!==1||e.anchorNode!==u.node||e.anchorOffset!==u.offset||e.focusNode!==h.node||e.focusOffset!==h.offset)&&(n=n.createRange(),n.setStart(u.node,u.offset),e.removeAllRanges(),c>a?(e.addRange(n),e.extend(h.node,h.offset)):(n.setEnd(h.node,h.offset),e.addRange(n)))}}for(n=[],e=i;e=e.parentNode;)e.nodeType===1&&n.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof i.focus=="function"&&i.focus(),i=0;i<n.length;i++)e=n[i],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Mx=br&&"documentMode"in document&&11>=document.documentMode,fl=null,fc=null,Ao=null,cc=!1;function lv(e,n,i){var a=i.window===i?i.document:i.nodeType===9?i:i.ownerDocument;cc||fl==null||fl!==ba(a)||(a=fl,"selectionStart"in a&&id(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),Ao&&Qo(Ao,a)||(Ao=a,a=Ia(fc,"onSelect"),0<a.length&&(n=new td("onSelect","select",null,n,i),e.push({event:n,listeners:a}),n.target=fl)))}function ta(e,n){var i={};return i[e.toLowerCase()]=n.toLowerCase(),i["Webkit"+e]="webkit"+n,i["Moz"+e]="moz"+n,i}var cl={animationend:ta("Animation","AnimationEnd"),animationiteration:ta("Animation","AnimationIteration"),animationstart:ta("Animation","AnimationStart"),transitionend:ta("Transition","TransitionEnd")},Lf={},em={};br&&(em=document.createElement("div").style,"AnimationEvent"in window||(delete cl.animationend.animation,delete cl.animationiteration.animation,delete cl.animationstart.animation),"TransitionEvent"in window||delete cl.transitionend.transition);function lu(e){if(Lf[e])return Lf[e];if(!cl[e])return e;var n=cl[e],i;for(i in n)if(n.hasOwnProperty(i)&&i in em)return Lf[e]=n[i];return e}var tm=lu("animationend"),nm=lu("animationiteration"),rm=lu("animationstart"),im=lu("transitionend"),lm=new Map,ov="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function ai(e,n){lm.set(e,n),Ii(n,[e])}for(var Nf=0;Nf<ov.length;Nf++){var Ef=ov[Nf],_x=Ef.toLowerCase(),Dx=Ef[0].toUpperCase()+Ef.slice(1);ai(_x,"on"+Dx)}ai(tm,"onAnimationEnd");ai(nm,"onAnimationIteration");ai(rm,"onAnimationStart");ai("dblclick","onDoubleClick");ai("focusin","onFocus");ai("focusout","onBlur");ai(im,"onTransitionEnd");Ll("onMouseEnter",["mouseout","mouseover"]);Ll("onMouseLeave",["mouseout","mouseover"]);Ll("onPointerEnter",["pointerout","pointerover"]);Ll("onPointerLeave",["pointerout","pointerover"]);Ii("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Ii("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Ii("onBeforeInput",["compositionend","keypress","textInput","paste"]);Ii("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Ii("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Ii("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var _o="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Px=new Set("cancel close invalid load scroll toggle".split(" ").concat(_o));function sv(e,n,i){var a=e.type||"unknown-event";e.currentTarget=i,_1(a,n,void 0,e),e.currentTarget=null}function om(e,n){n=(n&4)!==0;for(var i=0;i<e.length;i++){var a=e[i],u=a.event;a=a.listeners;e:{var c=void 0;if(n)for(var h=a.length-1;0<=h;h--){var v=a[h],m=v.instance,x=v.currentTarget;if(v=v.listener,m!==c&&u.isPropagationStopped())break e;sv(u,v,x),c=m}else for(h=0;h<a.length;h++){if(v=a[h],m=v.instance,x=v.currentTarget,v=v.listener,m!==c&&u.isPropagationStopped())break e;sv(u,v,x),c=m}}}if(_a)throw e=oc,_a=!1,oc=null,e}function Ve(e,n){var i=n[gc];i===void 0&&(i=n[gc]=new Set);var a=e+"__bubble";i.has(a)||(sm(n,e,2,!1),i.add(a))}function bf(e,n,i){var a=0;n&&(a|=4),sm(i,e,a,n)}var na="_reactListening"+Math.random().toString(36).slice(2);function Yo(e){if(!e[na]){e[na]=!0,pg.forEach(function(i){i!=="selectionchange"&&(Px.has(i)||bf(i,!1,e),bf(i,!0,e))});var n=e.nodeType===9?e:e.ownerDocument;n===null||n[na]||(n[na]=!0,bf("selectionchange",!1,n))}}function sm(e,n,i,a){switch($g(n)){case 1:var u=G1;break;case 4:u=q1;break;default:u=Jc}i=u.bind(null,n,i,e),u=void 0,!lc||n!=="touchstart"&&n!=="touchmove"&&n!=="wheel"||(u=!0),a?u!==void 0?e.addEventListener(n,i,{capture:!0,passive:u}):e.addEventListener(n,i,!0):u!==void 0?e.addEventListener(n,i,{passive:u}):e.addEventListener(n,i,!1)}function Mf(e,n,i,a,u){var c=a;if(!(n&1)&&!(n&2)&&a!==null)e:for(;;){if(a===null)return;var h=a.tag;if(h===3||h===4){var v=a.stateNode.containerInfo;if(v===u||v.nodeType===8&&v.parentNode===u)break;if(h===4)for(h=a.return;h!==null;){var m=h.tag;if((m===3||m===4)&&(m=h.stateNode.containerInfo,m===u||m.nodeType===8&&m.parentNode===u))return;h=h.return}for(;v!==null;){if(h=Li(v),h===null)return;if(m=h.tag,m===5||m===6){a=c=h;continue e}v=v.parentNode}}a=a.return}_g(function(){var x=c,L=Qc(i),R=[];e:{var P=lm.get(e);if(P!==void 0){var W=td,B=e;switch(e){case"keypress":if(va(i)===0)break e;case"keydown":case"keyup":W=ax;break;case"focusin":B="focus",W=kf;break;case"focusout":B="blur",W=kf;break;case"beforeblur":case"afterblur":W=kf;break;case"click":if(i.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":W=Qp;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":W=Y1;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":W=cx;break;case tm:case nm:case rm:W=J1;break;case im:W=hx;break;case"scroll":W=V1;break;case"wheel":W=vx;break;case"copy":case"cut":case"paste":W=tx;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":W=Xp}var Y=(n&4)!==0,K=!Y&&e==="scroll",T=Y?P!==null?P+"Capture":null:P;Y=[];for(var S=x,M;S!==null;){M=S;var A=M.stateNode;if(M.tag===5&&A!==null&&(M=A,T!==null&&(A=$o(S,T),A!=null&&Y.push(Xo(S,A,M)))),K)break;S=S.return}0<Y.length&&(P=new W(P,B,null,i,L),R.push({event:P,listeners:Y}))}}if(!(n&7)){e:{if(P=e==="mouseover"||e==="pointerover",W=e==="mouseout"||e==="pointerout",P&&i!==rc&&(B=i.relatedTarget||i.fromElement)&&(Li(B)||B[Mr]))break e;if((W||P)&&(P=L.window===L?L:(P=L.ownerDocument)?P.defaultView||P.parentWindow:window,W?(B=i.relatedTarget||i.toElement,W=x,B=B?Li(B):null,B!==null&&(K=Ai(B),B!==K||B.tag!==5&&B.tag!==6)&&(B=null)):(W=null,B=x),W!==B)){if(Y=Qp,A="onMouseLeave",T="onMouseEnter",S="mouse",(e==="pointerout"||e==="pointerover")&&(Y=Xp,A="onPointerLeave",T="onPointerEnter",S="pointer"),K=W==null?P:dl(W),M=B==null?P:dl(B),P=new Y(A,S+"leave",W,i,L),P.target=K,P.relatedTarget=M,A=null,Li(L)===x&&(Y=new Y(T,S+"enter",B,i,L),Y.target=M,Y.relatedTarget=K,A=Y),K=A,W&&B)t:{for(Y=W,T=B,S=0,M=Y;M;M=ol(M))S++;for(M=0,A=T;A;A=ol(A))M++;for(;0<S-M;)Y=ol(Y),S--;for(;0<M-S;)T=ol(T),M--;for(;S--;){if(Y===T||T!==null&&Y===T.alternate)break t;Y=ol(Y),T=ol(T)}Y=null}else Y=null;W!==null&&av(R,P,W,Y,!1),B!==null&&K!==null&&av(R,K,B,Y,!0)}}e:{if(P=x?dl(x):window,W=P.nodeName&&P.nodeName.toLowerCase(),W==="select"||W==="input"&&P.type==="file")var G=kx;else if(ev(P))if(Yg)G=Nx;else{G=Tx;var Q=Cx}else(W=P.nodeName)&&W.toLowerCase()==="input"&&(P.type==="checkbox"||P.type==="radio")&&(G=Lx);if(G&&(G=G(e,x))){Qg(R,G,i,L);break e}Q&&Q(e,P,x),e==="focusout"&&(Q=P._wrapperState)&&Q.controlled&&P.type==="number"&&Zf(P,"number",P.value)}switch(Q=x?dl(x):window,e){case"focusin":(ev(Q)||Q.contentEditable==="true")&&(fl=Q,fc=x,Ao=null);break;case"focusout":Ao=fc=fl=null;break;case"mousedown":cc=!0;break;case"contextmenu":case"mouseup":case"dragend":cc=!1,lv(R,i,L);break;case"selectionchange":if(Mx)break;case"keydown":case"keyup":lv(R,i,L)}var z;if(rd)e:{switch(e){case"compositionstart":var Z="onCompositionStart";break e;case"compositionend":Z="onCompositionEnd";break e;case"compositionupdate":Z="onCompositionUpdate";break e}Z=void 0}else ul?qg(e,i)&&(Z="onCompositionEnd"):e==="keydown"&&i.keyCode===229&&(Z="onCompositionStart");Z&&(Gg&&i.locale!=="ko"&&(ul||Z!=="onCompositionStart"?Z==="onCompositionEnd"&&ul&&(z=Kg()):(Xr=L,ed="value"in Xr?Xr.value:Xr.textContent,ul=!0)),Q=Ia(x,Z),0<Q.length&&(Z=new Yp(Z,e,null,i,L),R.push({event:Z,listeners:Q}),z?Z.data=z:(z=Vg(i),z!==null&&(Z.data=z)))),(z=mx?yx(e,i):wx(e,i))&&(x=Ia(x,"onBeforeInput"),0<x.length&&(L=new Yp("onBeforeInput","beforeinput",null,i,L),R.push({event:L,listeners:x}),L.data=z))}om(R,n)})}function Xo(e,n,i){return{instance:e,listener:n,currentTarget:i}}function Ia(e,n){for(var i=n+"Capture",a=[];e!==null;){var u=e,c=u.stateNode;u.tag===5&&c!==null&&(u=c,c=$o(e,i),c!=null&&a.unshift(Xo(e,c,u)),c=$o(e,n),c!=null&&a.push(Xo(e,c,u))),e=e.return}return a}function ol(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function av(e,n,i,a,u){for(var c=n._reactName,h=[];i!==null&&i!==a;){var v=i,m=v.alternate,x=v.stateNode;if(m!==null&&m===a)break;v.tag===5&&x!==null&&(v=x,u?(m=$o(i,c),m!=null&&h.unshift(Xo(i,m,v))):u||(m=$o(i,c),m!=null&&h.push(Xo(i,m,v)))),i=i.return}h.length!==0&&e.push({event:n,listeners:h})}var Ox=/\r\n?/g,zx=/\u0000|\uFFFD/g;function uv(e){return(typeof e=="string"?e:""+e).replace(Ox,`
`).replace(zx,"")}function ra(e,n,i){if(n=uv(n),uv(e)!==n&&i)throw Error(te(425))}function Aa(){}var dc=null,hc=null;function pc(e,n){return e==="textarea"||e==="noscript"||typeof n.children=="string"||typeof n.children=="number"||typeof n.dangerouslySetInnerHTML=="object"&&n.dangerouslySetInnerHTML!==null&&n.dangerouslySetInnerHTML.__html!=null}var vc=typeof setTimeout=="function"?setTimeout:void 0,Ix=typeof clearTimeout=="function"?clearTimeout:void 0,fv=typeof Promise=="function"?Promise:void 0,Ax=typeof queueMicrotask=="function"?queueMicrotask:typeof fv<"u"?function(e){return fv.resolve(null).then(e).catch(Rx)}:vc;function Rx(e){setTimeout(function(){throw e})}function _f(e,n){var i=n,a=0;do{var u=i.nextSibling;if(e.removeChild(i),u&&u.nodeType===8)if(i=u.data,i==="/$"){if(a===0){e.removeChild(u),qo(n);return}a--}else i!=="$"&&i!=="$?"&&i!=="$!"||a++;i=u}while(i);qo(n)}function Tr(e){for(;e!=null;e=e.nextSibling){var n=e.nodeType;if(n===1||n===3)break;if(n===8){if(n=e.data,n==="$"||n==="$!"||n==="$?")break;if(n==="/$")return null}}return e}function cv(e){e=e.previousSibling;for(var n=0;e;){if(e.nodeType===8){var i=e.data;if(i==="$"||i==="$!"||i==="$?"){if(n===0)return e;n--}else i==="/$"&&n++}e=e.previousSibling}return null}var Il=Math.random().toString(36).slice(2),lr="__reactFiber$"+Il,Zo="__reactProps$"+Il,Mr="__reactContainer$"+Il,gc="__reactEvents$"+Il,Fx="__reactListeners$"+Il,Wx="__reactHandles$"+Il;function Li(e){var n=e[lr];if(n)return n;for(var i=e.parentNode;i;){if(n=i[Mr]||i[lr]){if(i=n.alternate,n.child!==null||i!==null&&i.child!==null)for(e=cv(e);e!==null;){if(i=e[lr])return i;e=cv(e)}return n}e=i,i=e.parentNode}return null}function as(e){return e=e[lr]||e[Mr],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function dl(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(te(33))}function ou(e){return e[Zo]||null}var mc=[],hl=-1;function ui(e){return{current:e}}function Qe(e){0>hl||(e.current=mc[hl],mc[hl]=null,hl--)}function Ge(e,n){hl++,mc[hl]=e.current,e.current=n}var oi={},jt=ui(oi),un=ui(!1),_i=oi;function Nl(e,n){var i=e.type.contextTypes;if(!i)return oi;var a=e.stateNode;if(a&&a.__reactInternalMemoizedUnmaskedChildContext===n)return a.__reactInternalMemoizedMaskedChildContext;var u={},c;for(c in i)u[c]=n[c];return a&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=n,e.__reactInternalMemoizedMaskedChildContext=u),u}function fn(e){return e=e.childContextTypes,e!=null}function Ra(){Qe(un),Qe(jt)}function dv(e,n,i){if(jt.current!==oi)throw Error(te(168));Ge(jt,n),Ge(un,i)}function am(e,n,i){var a=e.stateNode;if(n=n.childContextTypes,typeof a.getChildContext!="function")return i;a=a.getChildContext();for(var u in a)if(!(u in n))throw Error(te(108,C1(e)||"Unknown",u));return it({},i,a)}function Fa(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||oi,_i=jt.current,Ge(jt,e),Ge(un,un.current),!0}function hv(e,n,i){var a=e.stateNode;if(!a)throw Error(te(169));i?(e=am(e,n,_i),a.__reactInternalMemoizedMergedChildContext=e,Qe(un),Qe(jt),Ge(jt,e)):Qe(un),Ge(un,i)}var Cr=null,su=!1,Df=!1;function um(e){Cr===null?Cr=[e]:Cr.push(e)}function Bx(e){su=!0,um(e)}function fi(){if(!Df&&Cr!==null){Df=!0;var e=0,n=He;try{var i=Cr;for(He=1;e<i.length;e++){var a=i[e];do a=a(!0);while(a!==null)}Cr=null,su=!1}catch(u){throw Cr!==null&&(Cr=Cr.slice(e+1)),zg(Yc,fi),u}finally{He=n,Df=!1}}return null}var Hx=Dr.ReactCurrentBatchConfig;function Un(e,n){if(e&&e.defaultProps){n=it({},n),e=e.defaultProps;for(var i in e)n[i]===void 0&&(n[i]=e[i]);return n}return n}var Wa=ui(null),Ba=null,pl=null,ld=null;function od(){ld=pl=Ba=null}function sd(e){var n=Wa.current;Qe(Wa),e._currentValue=n}function yc(e,n,i){for(;e!==null;){var a=e.alternate;if((e.childLanes&n)!==n?(e.childLanes|=n,a!==null&&(a.childLanes|=n)):a!==null&&(a.childLanes&n)!==n&&(a.childLanes|=n),e===i)break;e=e.return}}function Cl(e,n){Ba=e,ld=pl=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&n&&(an=!0),e.firstContext=null)}function zn(e){var n=e._currentValue;if(ld!==e)if(e={context:e,memoizedValue:n,next:null},pl===null){if(Ba===null)throw Error(te(308));pl=e,Ba.dependencies={lanes:0,firstContext:e}}else pl=pl.next=e;return n}var Kn=null,Vr=!1;function ad(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function fm(e,n){e=e.updateQueue,n.updateQueue===e&&(n.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Er(e,n){return{eventTime:e,lane:n,tag:0,payload:null,callback:null,next:null}}function ni(e,n){var i=e.updateQueue;i!==null&&(i=i.shared,Jm(e)?(e=i.interleaved,e===null?(n.next=n,Kn===null?Kn=[i]:Kn.push(i)):(n.next=e.next,e.next=n),i.interleaved=n):(e=i.pending,e===null?n.next=n:(n.next=e.next,e.next=n),i.pending=n))}function ga(e,n,i){if(n=n.updateQueue,n!==null&&(n=n.shared,(i&4194240)!==0)){var a=n.lanes;a&=e.pendingLanes,i|=a,n.lanes=i,Xc(e,i)}}function pv(e,n){var i=e.updateQueue,a=e.alternate;if(a!==null&&(a=a.updateQueue,i===a)){var u=null,c=null;if(i=i.firstBaseUpdate,i!==null){do{var h={eventTime:i.eventTime,lane:i.lane,tag:i.tag,payload:i.payload,callback:i.callback,next:null};c===null?u=c=h:c=c.next=h,i=i.next}while(i!==null);c===null?u=c=n:c=c.next=n}else u=c=n;i={baseState:a.baseState,firstBaseUpdate:u,lastBaseUpdate:c,shared:a.shared,effects:a.effects},e.updateQueue=i;return}e=i.lastBaseUpdate,e===null?i.firstBaseUpdate=n:e.next=n,i.lastBaseUpdate=n}function Ha(e,n,i,a){var u=e.updateQueue;Vr=!1;var c=u.firstBaseUpdate,h=u.lastBaseUpdate,v=u.shared.pending;if(v!==null){u.shared.pending=null;var m=v,x=m.next;m.next=null,h===null?c=x:h.next=x,h=m;var L=e.alternate;L!==null&&(L=L.updateQueue,v=L.lastBaseUpdate,v!==h&&(v===null?L.firstBaseUpdate=x:v.next=x,L.lastBaseUpdate=m))}if(c!==null){var R=u.baseState;h=0,L=x=m=null,v=c;do{var P=v.lane,W=v.eventTime;if((a&P)===P){L!==null&&(L=L.next={eventTime:W,lane:0,tag:v.tag,payload:v.payload,callback:v.callback,next:null});e:{var B=e,Y=v;switch(P=n,W=i,Y.tag){case 1:if(B=Y.payload,typeof B=="function"){R=B.call(W,R,P);break e}R=B;break e;case 3:B.flags=B.flags&-65537|128;case 0:if(B=Y.payload,P=typeof B=="function"?B.call(W,R,P):B,P==null)break e;R=it({},R,P);break e;case 2:Vr=!0}}v.callback!==null&&v.lane!==0&&(e.flags|=64,P=u.effects,P===null?u.effects=[v]:P.push(v))}else W={eventTime:W,lane:P,tag:v.tag,payload:v.payload,callback:v.callback,next:null},L===null?(x=L=W,m=R):L=L.next=W,h|=P;if(v=v.next,v===null){if(v=u.shared.pending,v===null)break;P=v,v=P.next,P.next=null,u.lastBaseUpdate=P,u.shared.pending=null}}while(1);if(L===null&&(m=R),u.baseState=m,u.firstBaseUpdate=x,u.lastBaseUpdate=L,n=u.shared.interleaved,n!==null){u=n;do h|=u.lane,u=u.next;while(u!==n)}else c===null&&(u.shared.lanes=0);Oi|=h,e.lanes=h,e.memoizedState=R}}function vv(e,n,i){if(e=n.effects,n.effects=null,e!==null)for(n=0;n<e.length;n++){var a=e[n],u=a.callback;if(u!==null){if(a.callback=null,a=i,typeof u!="function")throw Error(te(191,u));u.call(a)}}}var cm=new hg.Component().refs;function wc(e,n,i,a){n=e.memoizedState,i=i(a,n),i=i==null?n:it({},n,i),e.memoizedState=i,e.lanes===0&&(e.updateQueue.baseState=i)}var au={isMounted:function(e){return(e=e._reactInternals)?Ai(e)===e:!1},enqueueSetState:function(e,n,i){e=e._reactInternals;var a=Xt(),u=ii(e),c=Er(a,u);c.payload=n,i!=null&&(c.callback=i),ni(e,c),n=Pn(e,u,a),n!==null&&ga(n,e,u)},enqueueReplaceState:function(e,n,i){e=e._reactInternals;var a=Xt(),u=ii(e),c=Er(a,u);c.tag=1,c.payload=n,i!=null&&(c.callback=i),ni(e,c),n=Pn(e,u,a),n!==null&&ga(n,e,u)},enqueueForceUpdate:function(e,n){e=e._reactInternals;var i=Xt(),a=ii(e),u=Er(i,a);u.tag=2,n!=null&&(u.callback=n),ni(e,u),n=Pn(e,a,i),n!==null&&ga(n,e,a)}};function gv(e,n,i,a,u,c,h){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(a,c,h):n.prototype&&n.prototype.isPureReactComponent?!Qo(i,a)||!Qo(u,c):!0}function dm(e,n,i){var a=!1,u=oi,c=n.contextType;return typeof c=="object"&&c!==null?c=zn(c):(u=fn(n)?_i:jt.current,a=n.contextTypes,c=(a=a!=null)?Nl(e,u):oi),n=new n(i,c),e.memoizedState=n.state!==null&&n.state!==void 0?n.state:null,n.updater=au,e.stateNode=n,n._reactInternals=e,a&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=u,e.__reactInternalMemoizedMaskedChildContext=c),n}function mv(e,n,i,a){e=n.state,typeof n.componentWillReceiveProps=="function"&&n.componentWillReceiveProps(i,a),typeof n.UNSAFE_componentWillReceiveProps=="function"&&n.UNSAFE_componentWillReceiveProps(i,a),n.state!==e&&au.enqueueReplaceState(n,n.state,null)}function xc(e,n,i,a){var u=e.stateNode;u.props=i,u.state=e.memoizedState,u.refs=cm,ad(e);var c=n.contextType;typeof c=="object"&&c!==null?u.context=zn(c):(c=fn(n)?_i:jt.current,u.context=Nl(e,c)),u.state=e.memoizedState,c=n.getDerivedStateFromProps,typeof c=="function"&&(wc(e,n,c,i),u.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(n=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),n!==u.state&&au.enqueueReplaceState(u,u.state,null),Ha(e,i,u,a),u.state=e.memoizedState),typeof u.componentDidMount=="function"&&(e.flags|=4194308)}var vl=[],gl=0,Ua=null,ja=0,bn=[],Mn=0,Di=null,Lr=1,Nr="";function Ci(e,n){vl[gl++]=ja,vl[gl++]=Ua,Ua=e,ja=n}function hm(e,n,i){bn[Mn++]=Lr,bn[Mn++]=Nr,bn[Mn++]=Di,Di=e;var a=Lr;e=Nr;var u=32-Gn(a)-1;a&=~(1<<u),i+=1;var c=32-Gn(n)+u;if(30<c){var h=u-u%5;c=(a&(1<<h)-1).toString(32),a>>=h,u-=h,Lr=1<<32-Gn(n)+u|i<<u|a,Nr=c+e}else Lr=1<<c|i<<u|a,Nr=e}function ud(e){e.return!==null&&(Ci(e,1),hm(e,1,0))}function fd(e){for(;e===Ua;)Ua=vl[--gl],vl[gl]=null,ja=vl[--gl],vl[gl]=null;for(;e===Di;)Di=bn[--Mn],bn[Mn]=null,Nr=bn[--Mn],bn[Mn]=null,Lr=bn[--Mn],bn[Mn]=null}var yn=null,sn=null,Ze=!1,$n=null;function pm(e,n){var i=_n(5,null,null,0);i.elementType="DELETED",i.stateNode=n,i.return=e,n=e.deletions,n===null?(e.deletions=[i],e.flags|=16):n.push(i)}function yv(e,n){switch(e.tag){case 5:var i=e.type;return n=n.nodeType!==1||i.toLowerCase()!==n.nodeName.toLowerCase()?null:n,n!==null?(e.stateNode=n,yn=e,sn=Tr(n.firstChild),!0):!1;case 6:return n=e.pendingProps===""||n.nodeType!==3?null:n,n!==null?(e.stateNode=n,yn=e,sn=null,!0):!1;case 13:return n=n.nodeType!==8?null:n,n!==null?(i=Di!==null?{id:Lr,overflow:Nr}:null,e.memoizedState={dehydrated:n,treeContext:i,retryLane:1073741824},i=_n(18,null,null,0),i.stateNode=n,i.return=e,e.child=i,yn=e,sn=null,!0):!1;default:return!1}}function Sc(e){return(e.mode&1)!==0&&(e.flags&128)===0}function kc(e){if(Ze){var n=sn;if(n){var i=n;if(!yv(e,n)){if(Sc(e))throw Error(te(418));n=Tr(i.nextSibling);var a=yn;n&&yv(e,n)?pm(a,i):(e.flags=e.flags&-4097|2,Ze=!1,yn=e)}}else{if(Sc(e))throw Error(te(418));e.flags=e.flags&-4097|2,Ze=!1,yn=e}}}function wv(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;yn=e}function Co(e){if(e!==yn)return!1;if(!Ze)return wv(e),Ze=!0,!1;var n;if((n=e.tag!==3)&&!(n=e.tag!==5)&&(n=e.type,n=n!=="head"&&n!=="body"&&!pc(e.type,e.memoizedProps)),n&&(n=sn)){if(Sc(e)){for(e=sn;e;)e=Tr(e.nextSibling);throw Error(te(418))}for(;n;)pm(e,n),n=Tr(n.nextSibling)}if(wv(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(te(317));e:{for(e=e.nextSibling,n=0;e;){if(e.nodeType===8){var i=e.data;if(i==="/$"){if(n===0){sn=Tr(e.nextSibling);break e}n--}else i!=="$"&&i!=="$!"&&i!=="$?"||n++}e=e.nextSibling}sn=null}}else sn=yn?Tr(e.stateNode.nextSibling):null;return!0}function El(){sn=yn=null,Ze=!1}function cd(e){$n===null?$n=[e]:$n.push(e)}function To(e,n,i){if(e=i.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(i._owner){if(i=i._owner,i){if(i.tag!==1)throw Error(te(309));var a=i.stateNode}if(!a)throw Error(te(147,e));var u=a,c=""+e;return n!==null&&n.ref!==null&&typeof n.ref=="function"&&n.ref._stringRef===c?n.ref:(n=function(h){var v=u.refs;v===cm&&(v=u.refs={}),h===null?delete v[c]:v[c]=h},n._stringRef=c,n)}if(typeof e!="string")throw Error(te(284));if(!i._owner)throw Error(te(290,e))}return e}function ia(e,n){throw e=Object.prototype.toString.call(n),Error(te(31,e==="[object Object]"?"object with keys {"+Object.keys(n).join(", ")+"}":e))}function xv(e){var n=e._init;return n(e._payload)}function vm(e){function n(T,S){if(e){var M=T.deletions;M===null?(T.deletions=[S],T.flags|=16):M.push(S)}}function i(T,S){if(!e)return null;for(;S!==null;)n(T,S),S=S.sibling;return null}function a(T,S){for(T=new Map;S!==null;)S.key!==null?T.set(S.key,S):T.set(S.index,S),S=S.sibling;return T}function u(T,S){return T=si(T,S),T.index=0,T.sibling=null,T}function c(T,S,M){return T.index=M,e?(M=T.alternate,M!==null?(M=M.index,M<S?(T.flags|=2,S):M):(T.flags|=2,S)):(T.flags|=1048576,S)}function h(T){return e&&T.alternate===null&&(T.flags|=2),T}function v(T,S,M,A){return S===null||S.tag!==6?(S=Rf(M,T.mode,A),S.return=T,S):(S=u(S,M),S.return=T,S)}function m(T,S,M,A){var G=M.type;return G===al?L(T,S,M.props.children,A,M.key):S!==null&&(S.elementType===G||typeof G=="object"&&G!==null&&G.$$typeof===qr&&xv(G)===S.type)?(A=u(S,M.props),A.ref=To(T,S,M),A.return=T,A):(A=Sa(M.type,M.key,M.props,null,T.mode,A),A.ref=To(T,S,M),A.return=T,A)}function x(T,S,M,A){return S===null||S.tag!==4||S.stateNode.containerInfo!==M.containerInfo||S.stateNode.implementation!==M.implementation?(S=Ff(M,T.mode,A),S.return=T,S):(S=u(S,M.children||[]),S.return=T,S)}function L(T,S,M,A,G){return S===null||S.tag!==7?(S=bi(M,T.mode,A,G),S.return=T,S):(S=u(S,M),S.return=T,S)}function R(T,S,M){if(typeof S=="string"&&S!==""||typeof S=="number")return S=Rf(""+S,T.mode,M),S.return=T,S;if(typeof S=="object"&&S!==null){switch(S.$$typeof){case Vs:return M=Sa(S.type,S.key,S.props,null,T.mode,M),M.ref=To(T,null,S),M.return=T,M;case sl:return S=Ff(S,T.mode,M),S.return=T,S;case qr:var A=S._init;return R(T,A(S._payload),M)}if(bo(S)||yo(S))return S=bi(S,T.mode,M,null),S.return=T,S;ia(T,S)}return null}function P(T,S,M,A){var G=S!==null?S.key:null;if(typeof M=="string"&&M!==""||typeof M=="number")return G!==null?null:v(T,S,""+M,A);if(typeof M=="object"&&M!==null){switch(M.$$typeof){case Vs:return M.key===G?m(T,S,M,A):null;case sl:return M.key===G?x(T,S,M,A):null;case qr:return G=M._init,P(T,S,G(M._payload),A)}if(bo(M)||yo(M))return G!==null?null:L(T,S,M,A,null);ia(T,M)}return null}function W(T,S,M,A,G){if(typeof A=="string"&&A!==""||typeof A=="number")return T=T.get(M)||null,v(S,T,""+A,G);if(typeof A=="object"&&A!==null){switch(A.$$typeof){case Vs:return T=T.get(A.key===null?M:A.key)||null,m(S,T,A,G);case sl:return T=T.get(A.key===null?M:A.key)||null,x(S,T,A,G);case qr:var Q=A._init;return W(T,S,M,Q(A._payload),G)}if(bo(A)||yo(A))return T=T.get(M)||null,L(S,T,A,G,null);ia(S,A)}return null}function B(T,S,M,A){for(var G=null,Q=null,z=S,Z=S=0,Ce=null;z!==null&&Z<M.length;Z++){z.index>Z?(Ce=z,z=null):Ce=z.sibling;var we=P(T,z,M[Z],A);if(we===null){z===null&&(z=Ce);break}e&&z&&we.alternate===null&&n(T,z),S=c(we,S,Z),Q===null?G=we:Q.sibling=we,Q=we,z=Ce}if(Z===M.length)return i(T,z),Ze&&Ci(T,Z),G;if(z===null){for(;Z<M.length;Z++)z=R(T,M[Z],A),z!==null&&(S=c(z,S,Z),Q===null?G=z:Q.sibling=z,Q=z);return Ze&&Ci(T,Z),G}for(z=a(T,z);Z<M.length;Z++)Ce=W(z,T,Z,M[Z],A),Ce!==null&&(e&&Ce.alternate!==null&&z.delete(Ce.key===null?Z:Ce.key),S=c(Ce,S,Z),Q===null?G=Ce:Q.sibling=Ce,Q=Ce);return e&&z.forEach(function(ze){return n(T,ze)}),Ze&&Ci(T,Z),G}function Y(T,S,M,A){var G=yo(M);if(typeof G!="function")throw Error(te(150));if(M=G.call(M),M==null)throw Error(te(151));for(var Q=G=null,z=S,Z=S=0,Ce=null,we=M.next();z!==null&&!we.done;Z++,we=M.next()){z.index>Z?(Ce=z,z=null):Ce=z.sibling;var ze=P(T,z,we.value,A);if(ze===null){z===null&&(z=Ce);break}e&&z&&ze.alternate===null&&n(T,z),S=c(ze,S,Z),Q===null?G=ze:Q.sibling=ze,Q=ze,z=Ce}if(we.done)return i(T,z),Ze&&Ci(T,Z),G;if(z===null){for(;!we.done;Z++,we=M.next())we=R(T,we.value,A),we!==null&&(S=c(we,S,Z),Q===null?G=we:Q.sibling=we,Q=we);return Ze&&Ci(T,Z),G}for(z=a(T,z);!we.done;Z++,we=M.next())we=W(z,T,Z,we.value,A),we!==null&&(e&&we.alternate!==null&&z.delete(we.key===null?Z:we.key),S=c(we,S,Z),Q===null?G=we:Q.sibling=we,Q=we);return e&&z.forEach(function(V){return n(T,V)}),Ze&&Ci(T,Z),G}function K(T,S,M,A){if(typeof M=="object"&&M!==null&&M.type===al&&M.key===null&&(M=M.props.children),typeof M=="object"&&M!==null){switch(M.$$typeof){case Vs:e:{for(var G=M.key,Q=S;Q!==null;){if(Q.key===G){if(G=M.type,G===al){if(Q.tag===7){i(T,Q.sibling),S=u(Q,M.props.children),S.return=T,T=S;break e}}else if(Q.elementType===G||typeof G=="object"&&G!==null&&G.$$typeof===qr&&xv(G)===Q.type){i(T,Q.sibling),S=u(Q,M.props),S.ref=To(T,Q,M),S.return=T,T=S;break e}i(T,Q);break}else n(T,Q);Q=Q.sibling}M.type===al?(S=bi(M.props.children,T.mode,A,M.key),S.return=T,T=S):(A=Sa(M.type,M.key,M.props,null,T.mode,A),A.ref=To(T,S,M),A.return=T,T=A)}return h(T);case sl:e:{for(Q=M.key;S!==null;){if(S.key===Q)if(S.tag===4&&S.stateNode.containerInfo===M.containerInfo&&S.stateNode.implementation===M.implementation){i(T,S.sibling),S=u(S,M.children||[]),S.return=T,T=S;break e}else{i(T,S);break}else n(T,S);S=S.sibling}S=Ff(M,T.mode,A),S.return=T,T=S}return h(T);case qr:return Q=M._init,K(T,S,Q(M._payload),A)}if(bo(M))return B(T,S,M,A);if(yo(M))return Y(T,S,M,A);ia(T,M)}return typeof M=="string"&&M!==""||typeof M=="number"?(M=""+M,S!==null&&S.tag===6?(i(T,S.sibling),S=u(S,M),S.return=T,T=S):(i(T,S),S=Rf(M,T.mode,A),S.return=T,T=S),h(T)):i(T,S)}return K}var bl=vm(!0),gm=vm(!1),us={},sr=ui(us),Jo=ui(us),es=ui(us);function Ni(e){if(e===us)throw Error(te(174));return e}function dd(e,n){switch(Ge(es,n),Ge(Jo,e),Ge(sr,us),e=n.nodeType,e){case 9:case 11:n=(n=n.documentElement)?n.namespaceURI:ec(null,"");break;default:e=e===8?n.parentNode:n,n=e.namespaceURI||null,e=e.tagName,n=ec(n,e)}Qe(sr),Ge(sr,n)}function Ml(){Qe(sr),Qe(Jo),Qe(es)}function mm(e){Ni(es.current);var n=Ni(sr.current),i=ec(n,e.type);n!==i&&(Ge(Jo,e),Ge(sr,i))}function hd(e){Jo.current===e&&(Qe(sr),Qe(Jo))}var nt=ui(0);function $a(e){for(var n=e;n!==null;){if(n.tag===13){var i=n.memoizedState;if(i!==null&&(i=i.dehydrated,i===null||i.data==="$?"||i.data==="$!"))return n}else if(n.tag===19&&n.memoizedProps.revealOrder!==void 0){if(n.flags&128)return n}else if(n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return null;n=n.return}n.sibling.return=n.return,n=n.sibling}return null}var Pf=[];function pd(){for(var e=0;e<Pf.length;e++)Pf[e]._workInProgressVersionPrimary=null;Pf.length=0}var ma=Dr.ReactCurrentDispatcher,Of=Dr.ReactCurrentBatchConfig,Pi=0,rt=null,kt=null,Nt=null,Ka=!1,Ro=!1,ts=0,Ux=0;function Ft(){throw Error(te(321))}function vd(e,n){if(n===null)return!1;for(var i=0;i<n.length&&i<e.length;i++)if(!qn(e[i],n[i]))return!1;return!0}function gd(e,n,i,a,u,c){if(Pi=c,rt=n,n.memoizedState=null,n.updateQueue=null,n.lanes=0,ma.current=e===null||e.memoizedState===null?Gx:qx,e=i(a,u),Ro){c=0;do{if(Ro=!1,ts=0,25<=c)throw Error(te(301));c+=1,Nt=kt=null,n.updateQueue=null,ma.current=Vx,e=i(a,u)}while(Ro)}if(ma.current=Ga,n=kt!==null&&kt.next!==null,Pi=0,Nt=kt=rt=null,Ka=!1,n)throw Error(te(300));return e}function md(){var e=ts!==0;return ts=0,e}function ir(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Nt===null?rt.memoizedState=Nt=e:Nt=Nt.next=e,Nt}function In(){if(kt===null){var e=rt.alternate;e=e!==null?e.memoizedState:null}else e=kt.next;var n=Nt===null?rt.memoizedState:Nt.next;if(n!==null)Nt=n,kt=e;else{if(e===null)throw Error(te(310));kt=e,e={memoizedState:kt.memoizedState,baseState:kt.baseState,baseQueue:kt.baseQueue,queue:kt.queue,next:null},Nt===null?rt.memoizedState=Nt=e:Nt=Nt.next=e}return Nt}function ns(e,n){return typeof n=="function"?n(e):n}function zf(e){var n=In(),i=n.queue;if(i===null)throw Error(te(311));i.lastRenderedReducer=e;var a=kt,u=a.baseQueue,c=i.pending;if(c!==null){if(u!==null){var h=u.next;u.next=c.next,c.next=h}a.baseQueue=u=c,i.pending=null}if(u!==null){c=u.next,a=a.baseState;var v=h=null,m=null,x=c;do{var L=x.lane;if((Pi&L)===L)m!==null&&(m=m.next={lane:0,action:x.action,hasEagerState:x.hasEagerState,eagerState:x.eagerState,next:null}),a=x.hasEagerState?x.eagerState:e(a,x.action);else{var R={lane:L,action:x.action,hasEagerState:x.hasEagerState,eagerState:x.eagerState,next:null};m===null?(v=m=R,h=a):m=m.next=R,rt.lanes|=L,Oi|=L}x=x.next}while(x!==null&&x!==c);m===null?h=a:m.next=v,qn(a,n.memoizedState)||(an=!0),n.memoizedState=a,n.baseState=h,n.baseQueue=m,i.lastRenderedState=a}if(e=i.interleaved,e!==null){u=e;do c=u.lane,rt.lanes|=c,Oi|=c,u=u.next;while(u!==e)}else u===null&&(i.lanes=0);return[n.memoizedState,i.dispatch]}function If(e){var n=In(),i=n.queue;if(i===null)throw Error(te(311));i.lastRenderedReducer=e;var a=i.dispatch,u=i.pending,c=n.memoizedState;if(u!==null){i.pending=null;var h=u=u.next;do c=e(c,h.action),h=h.next;while(h!==u);qn(c,n.memoizedState)||(an=!0),n.memoizedState=c,n.baseQueue===null&&(n.baseState=c),i.lastRenderedState=c}return[c,a]}function ym(){}function wm(e,n){var i=rt,a=In(),u=n(),c=!qn(a.memoizedState,u);if(c&&(a.memoizedState=u,an=!0),a=a.queue,yd(km.bind(null,i,a,e),[e]),a.getSnapshot!==n||c||Nt!==null&&Nt.memoizedState.tag&1){if(i.flags|=2048,rs(9,Sm.bind(null,i,a,u,n),void 0,null),Tt===null)throw Error(te(349));Pi&30||xm(i,n,u)}return u}function xm(e,n,i){e.flags|=16384,e={getSnapshot:n,value:i},n=rt.updateQueue,n===null?(n={lastEffect:null,stores:null},rt.updateQueue=n,n.stores=[e]):(i=n.stores,i===null?n.stores=[e]:i.push(e))}function Sm(e,n,i,a){n.value=i,n.getSnapshot=a,Cm(n)&&Pn(e,1,-1)}function km(e,n,i){return i(function(){Cm(n)&&Pn(e,1,-1)})}function Cm(e){var n=e.getSnapshot;e=e.value;try{var i=n();return!qn(e,i)}catch{return!0}}function Sv(e){var n=ir();return typeof e=="function"&&(e=e()),n.memoizedState=n.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ns,lastRenderedState:e},n.queue=e,e=e.dispatch=Kx.bind(null,rt,e),[n.memoizedState,e]}function rs(e,n,i,a){return e={tag:e,create:n,destroy:i,deps:a,next:null},n=rt.updateQueue,n===null?(n={lastEffect:null,stores:null},rt.updateQueue=n,n.lastEffect=e.next=e):(i=n.lastEffect,i===null?n.lastEffect=e.next=e:(a=i.next,i.next=e,e.next=a,n.lastEffect=e)),e}function Tm(){return In().memoizedState}function ya(e,n,i,a){var u=ir();rt.flags|=e,u.memoizedState=rs(1|n,i,void 0,a===void 0?null:a)}function uu(e,n,i,a){var u=In();a=a===void 0?null:a;var c=void 0;if(kt!==null){var h=kt.memoizedState;if(c=h.destroy,a!==null&&vd(a,h.deps)){u.memoizedState=rs(n,i,c,a);return}}rt.flags|=e,u.memoizedState=rs(1|n,i,c,a)}function kv(e,n){return ya(8390656,8,e,n)}function yd(e,n){return uu(2048,8,e,n)}function Lm(e,n){return uu(4,2,e,n)}function Nm(e,n){return uu(4,4,e,n)}function Em(e,n){if(typeof n=="function")return e=e(),n(e),function(){n(null)};if(n!=null)return e=e(),n.current=e,function(){n.current=null}}function bm(e,n,i){return i=i!=null?i.concat([e]):null,uu(4,4,Em.bind(null,n,e),i)}function wd(){}function Mm(e,n){var i=In();n=n===void 0?null:n;var a=i.memoizedState;return a!==null&&n!==null&&vd(n,a[1])?a[0]:(i.memoizedState=[e,n],e)}function _m(e,n){var i=In();n=n===void 0?null:n;var a=i.memoizedState;return a!==null&&n!==null&&vd(n,a[1])?a[0]:(e=e(),i.memoizedState=[e,n],e)}function Dm(e,n,i){return Pi&21?(qn(i,n)||(i=Rg(),rt.lanes|=i,Oi|=i,e.baseState=!0),n):(e.baseState&&(e.baseState=!1,an=!0),e.memoizedState=i)}function jx(e,n){var i=He;He=i!==0&&4>i?i:4,e(!0);var a=Of.transition;Of.transition={};try{e(!1),n()}finally{He=i,Of.transition=a}}function Pm(){return In().memoizedState}function $x(e,n,i){var a=ii(e);i={lane:a,action:i,hasEagerState:!1,eagerState:null,next:null},Om(e)?zm(n,i):(Im(e,n,i),i=Xt(),e=Pn(e,a,i),e!==null&&Am(e,n,a))}function Kx(e,n,i){var a=ii(e),u={lane:a,action:i,hasEagerState:!1,eagerState:null,next:null};if(Om(e))zm(n,u);else{Im(e,n,u);var c=e.alternate;if(e.lanes===0&&(c===null||c.lanes===0)&&(c=n.lastRenderedReducer,c!==null))try{var h=n.lastRenderedState,v=c(h,i);if(u.hasEagerState=!0,u.eagerState=v,qn(v,h))return}catch{}finally{}i=Xt(),e=Pn(e,a,i),e!==null&&Am(e,n,a)}}function Om(e){var n=e.alternate;return e===rt||n!==null&&n===rt}function zm(e,n){Ro=Ka=!0;var i=e.pending;i===null?n.next=n:(n.next=i.next,i.next=n),e.pending=n}function Im(e,n,i){Jm(e)?(e=n.interleaved,e===null?(i.next=i,Kn===null?Kn=[n]:Kn.push(n)):(i.next=e.next,e.next=i),n.interleaved=i):(e=n.pending,e===null?i.next=i:(i.next=e.next,e.next=i),n.pending=i)}function Am(e,n,i){if(i&4194240){var a=n.lanes;a&=e.pendingLanes,i|=a,n.lanes=i,Xc(e,i)}}var Ga={readContext:zn,useCallback:Ft,useContext:Ft,useEffect:Ft,useImperativeHandle:Ft,useInsertionEffect:Ft,useLayoutEffect:Ft,useMemo:Ft,useReducer:Ft,useRef:Ft,useState:Ft,useDebugValue:Ft,useDeferredValue:Ft,useTransition:Ft,useMutableSource:Ft,useSyncExternalStore:Ft,useId:Ft,unstable_isNewReconciler:!1},Gx={readContext:zn,useCallback:function(e,n){return ir().memoizedState=[e,n===void 0?null:n],e},useContext:zn,useEffect:kv,useImperativeHandle:function(e,n,i){return i=i!=null?i.concat([e]):null,ya(4194308,4,Em.bind(null,n,e),i)},useLayoutEffect:function(e,n){return ya(4194308,4,e,n)},useInsertionEffect:function(e,n){return ya(4,2,e,n)},useMemo:function(e,n){var i=ir();return n=n===void 0?null:n,e=e(),i.memoizedState=[e,n],e},useReducer:function(e,n,i){var a=ir();return n=i!==void 0?i(n):n,a.memoizedState=a.baseState=n,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:n},a.queue=e,e=e.dispatch=$x.bind(null,rt,e),[a.memoizedState,e]},useRef:function(e){var n=ir();return e={current:e},n.memoizedState=e},useState:Sv,useDebugValue:wd,useDeferredValue:function(e){return ir().memoizedState=e},useTransition:function(){var e=Sv(!1),n=e[0];return e=jx.bind(null,e[1]),ir().memoizedState=e,[n,e]},useMutableSource:function(){},useSyncExternalStore:function(e,n,i){var a=rt,u=ir();if(Ze){if(i===void 0)throw Error(te(407));i=i()}else{if(i=n(),Tt===null)throw Error(te(349));Pi&30||xm(a,n,i)}u.memoizedState=i;var c={value:i,getSnapshot:n};return u.queue=c,kv(km.bind(null,a,c,e),[e]),a.flags|=2048,rs(9,Sm.bind(null,a,c,i,n),void 0,null),i},useId:function(){var e=ir(),n=Tt.identifierPrefix;if(Ze){var i=Nr,a=Lr;i=(a&~(1<<32-Gn(a)-1)).toString(32)+i,n=":"+n+"R"+i,i=ts++,0<i&&(n+="H"+i.toString(32)),n+=":"}else i=Ux++,n=":"+n+"r"+i.toString(32)+":";return e.memoizedState=n},unstable_isNewReconciler:!1},qx={readContext:zn,useCallback:Mm,useContext:zn,useEffect:yd,useImperativeHandle:bm,useInsertionEffect:Lm,useLayoutEffect:Nm,useMemo:_m,useReducer:zf,useRef:Tm,useState:function(){return zf(ns)},useDebugValue:wd,useDeferredValue:function(e){var n=In();return Dm(n,kt.memoizedState,e)},useTransition:function(){var e=zf(ns)[0],n=In().memoizedState;return[e,n]},useMutableSource:ym,useSyncExternalStore:wm,useId:Pm,unstable_isNewReconciler:!1},Vx={readContext:zn,useCallback:Mm,useContext:zn,useEffect:yd,useImperativeHandle:bm,useInsertionEffect:Lm,useLayoutEffect:Nm,useMemo:_m,useReducer:If,useRef:Tm,useState:function(){return If(ns)},useDebugValue:wd,useDeferredValue:function(e){var n=In();return kt===null?n.memoizedState=e:Dm(n,kt.memoizedState,e)},useTransition:function(){var e=If(ns)[0],n=In().memoizedState;return[e,n]},useMutableSource:ym,useSyncExternalStore:wm,useId:Pm,unstable_isNewReconciler:!1};function xd(e,n){try{var i="",a=n;do i+=k1(a),a=a.return;while(a);var u=i}catch(c){u=`
Error generating stack: `+c.message+`
`+c.stack}return{value:e,source:n,stack:u}}function Cc(e,n){try{console.error(n.value)}catch(i){setTimeout(function(){throw i})}}var Qx=typeof WeakMap=="function"?WeakMap:Map;function Rm(e,n,i){i=Er(-1,i),i.tag=3,i.payload={element:null};var a=n.value;return i.callback=function(){Va||(Va=!0,Pc=a),Cc(e,n)},i}function Fm(e,n,i){i=Er(-1,i),i.tag=3;var a=e.type.getDerivedStateFromError;if(typeof a=="function"){var u=n.value;i.payload=function(){return a(u)},i.callback=function(){Cc(e,n)}}var c=e.stateNode;return c!==null&&typeof c.componentDidCatch=="function"&&(i.callback=function(){Cc(e,n),typeof a!="function"&&(ri===null?ri=new Set([this]):ri.add(this));var h=n.stack;this.componentDidCatch(n.value,{componentStack:h!==null?h:""})}),i}function Cv(e,n,i){var a=e.pingCache;if(a===null){a=e.pingCache=new Qx;var u=new Set;a.set(n,u)}else u=a.get(n),u===void 0&&(u=new Set,a.set(n,u));u.has(i)||(u.add(i),e=aS.bind(null,e,n,i),n.then(e,e))}function Tv(e){do{var n;if((n=e.tag===13)&&(n=e.memoizedState,n=n!==null?n.dehydrated!==null:!0),n)return e;e=e.return}while(e!==null);return null}function Lv(e,n,i,a,u){return e.mode&1?(e.flags|=65536,e.lanes=u,e):(e===n?e.flags|=65536:(e.flags|=128,i.flags|=131072,i.flags&=-52805,i.tag===1&&(i.alternate===null?i.tag=17:(n=Er(-1,1),n.tag=2,ni(i,n))),i.lanes|=1),e)}var Wm,Tc,Bm,Hm;Wm=function(e,n){for(var i=n.child;i!==null;){if(i.tag===5||i.tag===6)e.appendChild(i.stateNode);else if(i.tag!==4&&i.child!==null){i.child.return=i,i=i.child;continue}if(i===n)break;for(;i.sibling===null;){if(i.return===null||i.return===n)return;i=i.return}i.sibling.return=i.return,i=i.sibling}};Tc=function(){};Bm=function(e,n,i,a){var u=e.memoizedProps;if(u!==a){e=n.stateNode,Ni(sr.current);var c=null;switch(i){case"input":u=Yf(e,u),a=Yf(e,a),c=[];break;case"select":u=it({},u,{value:void 0}),a=it({},a,{value:void 0}),c=[];break;case"textarea":u=Jf(e,u),a=Jf(e,a),c=[];break;default:typeof u.onClick!="function"&&typeof a.onClick=="function"&&(e.onclick=Aa)}tc(i,a);var h;i=null;for(x in u)if(!a.hasOwnProperty(x)&&u.hasOwnProperty(x)&&u[x]!=null)if(x==="style"){var v=u[x];for(h in v)v.hasOwnProperty(h)&&(i||(i={}),i[h]="")}else x!=="dangerouslySetInnerHTML"&&x!=="children"&&x!=="suppressContentEditableWarning"&&x!=="suppressHydrationWarning"&&x!=="autoFocus"&&(Uo.hasOwnProperty(x)?c||(c=[]):(c=c||[]).push(x,null));for(x in a){var m=a[x];if(v=u!=null?u[x]:void 0,a.hasOwnProperty(x)&&m!==v&&(m!=null||v!=null))if(x==="style")if(v){for(h in v)!v.hasOwnProperty(h)||m&&m.hasOwnProperty(h)||(i||(i={}),i[h]="");for(h in m)m.hasOwnProperty(h)&&v[h]!==m[h]&&(i||(i={}),i[h]=m[h])}else i||(c||(c=[]),c.push(x,i)),i=m;else x==="dangerouslySetInnerHTML"?(m=m?m.__html:void 0,v=v?v.__html:void 0,m!=null&&v!==m&&(c=c||[]).push(x,m)):x==="children"?typeof m!="string"&&typeof m!="number"||(c=c||[]).push(x,""+m):x!=="suppressContentEditableWarning"&&x!=="suppressHydrationWarning"&&(Uo.hasOwnProperty(x)?(m!=null&&x==="onScroll"&&Ve("scroll",e),c||v===m||(c=[])):(c=c||[]).push(x,m))}i&&(c=c||[]).push("style",i);var x=c;(n.updateQueue=x)&&(n.flags|=4)}};Hm=function(e,n,i,a){i!==a&&(n.flags|=4)};function Lo(e,n){if(!Ze)switch(e.tailMode){case"hidden":n=e.tail;for(var i=null;n!==null;)n.alternate!==null&&(i=n),n=n.sibling;i===null?e.tail=null:i.sibling=null;break;case"collapsed":i=e.tail;for(var a=null;i!==null;)i.alternate!==null&&(a=i),i=i.sibling;a===null?n||e.tail===null?e.tail=null:e.tail.sibling=null:a.sibling=null}}function Wt(e){var n=e.alternate!==null&&e.alternate.child===e.child,i=0,a=0;if(n)for(var u=e.child;u!==null;)i|=u.lanes|u.childLanes,a|=u.subtreeFlags&14680064,a|=u.flags&14680064,u.return=e,u=u.sibling;else for(u=e.child;u!==null;)i|=u.lanes|u.childLanes,a|=u.subtreeFlags,a|=u.flags,u.return=e,u=u.sibling;return e.subtreeFlags|=a,e.childLanes=i,n}function Yx(e,n,i){var a=n.pendingProps;switch(fd(n),n.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Wt(n),null;case 1:return fn(n.type)&&Ra(),Wt(n),null;case 3:return a=n.stateNode,Ml(),Qe(un),Qe(jt),pd(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(e===null||e.child===null)&&(Co(n)?n.flags|=4:e===null||e.memoizedState.isDehydrated&&!(n.flags&256)||(n.flags|=1024,$n!==null&&(Ic($n),$n=null))),Tc(e,n),Wt(n),null;case 5:hd(n);var u=Ni(es.current);if(i=n.type,e!==null&&n.stateNode!=null)Bm(e,n,i,a,u),e.ref!==n.ref&&(n.flags|=512,n.flags|=2097152);else{if(!a){if(n.stateNode===null)throw Error(te(166));return Wt(n),null}if(e=Ni(sr.current),Co(n)){a=n.stateNode,i=n.type;var c=n.memoizedProps;switch(a[lr]=n,a[Zo]=c,e=(n.mode&1)!==0,i){case"dialog":Ve("cancel",a),Ve("close",a);break;case"iframe":case"object":case"embed":Ve("load",a);break;case"video":case"audio":for(u=0;u<_o.length;u++)Ve(_o[u],a);break;case"source":Ve("error",a);break;case"img":case"image":case"link":Ve("error",a),Ve("load",a);break;case"details":Ve("toggle",a);break;case"input":Wp(a,c),Ve("invalid",a);break;case"select":a._wrapperState={wasMultiple:!!c.multiple},Ve("invalid",a);break;case"textarea":Hp(a,c),Ve("invalid",a)}tc(i,c),u=null;for(var h in c)if(c.hasOwnProperty(h)){var v=c[h];h==="children"?typeof v=="string"?a.textContent!==v&&(c.suppressHydrationWarning!==!0&&ra(a.textContent,v,e),u=["children",v]):typeof v=="number"&&a.textContent!==""+v&&(c.suppressHydrationWarning!==!0&&ra(a.textContent,v,e),u=["children",""+v]):Uo.hasOwnProperty(h)&&v!=null&&h==="onScroll"&&Ve("scroll",a)}switch(i){case"input":Qs(a),Bp(a,c,!0);break;case"textarea":Qs(a),Up(a);break;case"select":case"option":break;default:typeof c.onClick=="function"&&(a.onclick=Aa)}a=u,n.updateQueue=a,a!==null&&(n.flags|=4)}else{h=u.nodeType===9?u:u.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=kg(i)),e==="http://www.w3.org/1999/xhtml"?i==="script"?(e=h.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof a.is=="string"?e=h.createElement(i,{is:a.is}):(e=h.createElement(i),i==="select"&&(h=e,a.multiple?h.multiple=!0:a.size&&(h.size=a.size))):e=h.createElementNS(e,i),e[lr]=n,e[Zo]=a,Wm(e,n,!1,!1),n.stateNode=e;e:{switch(h=nc(i,a),i){case"dialog":Ve("cancel",e),Ve("close",e),u=a;break;case"iframe":case"object":case"embed":Ve("load",e),u=a;break;case"video":case"audio":for(u=0;u<_o.length;u++)Ve(_o[u],e);u=a;break;case"source":Ve("error",e),u=a;break;case"img":case"image":case"link":Ve("error",e),Ve("load",e),u=a;break;case"details":Ve("toggle",e),u=a;break;case"input":Wp(e,a),u=Yf(e,a),Ve("invalid",e);break;case"option":u=a;break;case"select":e._wrapperState={wasMultiple:!!a.multiple},u=it({},a,{value:void 0}),Ve("invalid",e);break;case"textarea":Hp(e,a),u=Jf(e,a),Ve("invalid",e);break;default:u=a}tc(i,u),v=u;for(c in v)if(v.hasOwnProperty(c)){var m=v[c];c==="style"?Lg(e,m):c==="dangerouslySetInnerHTML"?(m=m?m.__html:void 0,m!=null&&Cg(e,m)):c==="children"?typeof m=="string"?(i!=="textarea"||m!=="")&&jo(e,m):typeof m=="number"&&jo(e,""+m):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(Uo.hasOwnProperty(c)?m!=null&&c==="onScroll"&&Ve("scroll",e):m!=null&&Kc(e,c,m,h))}switch(i){case"input":Qs(e),Bp(e,a,!1);break;case"textarea":Qs(e),Up(e);break;case"option":a.value!=null&&e.setAttribute("value",""+li(a.value));break;case"select":e.multiple=!!a.multiple,c=a.value,c!=null?wl(e,!!a.multiple,c,!1):a.defaultValue!=null&&wl(e,!!a.multiple,a.defaultValue,!0);break;default:typeof u.onClick=="function"&&(e.onclick=Aa)}switch(i){case"button":case"input":case"select":case"textarea":a=!!a.autoFocus;break e;case"img":a=!0;break e;default:a=!1}}a&&(n.flags|=4)}n.ref!==null&&(n.flags|=512,n.flags|=2097152)}return Wt(n),null;case 6:if(e&&n.stateNode!=null)Hm(e,n,e.memoizedProps,a);else{if(typeof a!="string"&&n.stateNode===null)throw Error(te(166));if(i=Ni(es.current),Ni(sr.current),Co(n)){if(a=n.stateNode,i=n.memoizedProps,a[lr]=n,(c=a.nodeValue!==i)&&(e=yn,e!==null))switch(e.tag){case 3:ra(a.nodeValue,i,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&ra(a.nodeValue,i,(e.mode&1)!==0)}c&&(n.flags|=4)}else a=(i.nodeType===9?i:i.ownerDocument).createTextNode(a),a[lr]=n,n.stateNode=a}return Wt(n),null;case 13:if(Qe(nt),a=n.memoizedState,Ze&&sn!==null&&n.mode&1&&!(n.flags&128)){for(a=sn;a;)a=Tr(a.nextSibling);return El(),n.flags|=98560,n}if(a!==null&&a.dehydrated!==null){if(a=Co(n),e===null){if(!a)throw Error(te(318));if(a=n.memoizedState,a=a!==null?a.dehydrated:null,!a)throw Error(te(317));a[lr]=n}else El(),!(n.flags&128)&&(n.memoizedState=null),n.flags|=4;return Wt(n),null}return $n!==null&&(Ic($n),$n=null),n.flags&128?(n.lanes=i,n):(a=a!==null,i=!1,e===null?Co(n):i=e.memoizedState!==null,a!==i&&a&&(n.child.flags|=8192,n.mode&1&&(e===null||nt.current&1?Ct===0&&(Ct=3):Nd())),n.updateQueue!==null&&(n.flags|=4),Wt(n),null);case 4:return Ml(),Tc(e,n),e===null&&Yo(n.stateNode.containerInfo),Wt(n),null;case 10:return sd(n.type._context),Wt(n),null;case 17:return fn(n.type)&&Ra(),Wt(n),null;case 19:if(Qe(nt),c=n.memoizedState,c===null)return Wt(n),null;if(a=(n.flags&128)!==0,h=c.rendering,h===null)if(a)Lo(c,!1);else{if(Ct!==0||e!==null&&e.flags&128)for(e=n.child;e!==null;){if(h=$a(e),h!==null){for(n.flags|=128,Lo(c,!1),a=h.updateQueue,a!==null&&(n.updateQueue=a,n.flags|=4),n.subtreeFlags=0,a=i,i=n.child;i!==null;)c=i,e=a,c.flags&=14680066,h=c.alternate,h===null?(c.childLanes=0,c.lanes=e,c.child=null,c.subtreeFlags=0,c.memoizedProps=null,c.memoizedState=null,c.updateQueue=null,c.dependencies=null,c.stateNode=null):(c.childLanes=h.childLanes,c.lanes=h.lanes,c.child=h.child,c.subtreeFlags=0,c.deletions=null,c.memoizedProps=h.memoizedProps,c.memoizedState=h.memoizedState,c.updateQueue=h.updateQueue,c.type=h.type,e=h.dependencies,c.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),i=i.sibling;return Ge(nt,nt.current&1|2),n.child}e=e.sibling}c.tail!==null&&ut()>_l&&(n.flags|=128,a=!0,Lo(c,!1),n.lanes=4194304)}else{if(!a)if(e=$a(h),e!==null){if(n.flags|=128,a=!0,i=e.updateQueue,i!==null&&(n.updateQueue=i,n.flags|=4),Lo(c,!0),c.tail===null&&c.tailMode==="hidden"&&!h.alternate&&!Ze)return Wt(n),null}else 2*ut()-c.renderingStartTime>_l&&i!==1073741824&&(n.flags|=128,a=!0,Lo(c,!1),n.lanes=4194304);c.isBackwards?(h.sibling=n.child,n.child=h):(i=c.last,i!==null?i.sibling=h:n.child=h,c.last=h)}return c.tail!==null?(n=c.tail,c.rendering=n,c.tail=n.sibling,c.renderingStartTime=ut(),n.sibling=null,i=nt.current,Ge(nt,a?i&1|2:i&1),n):(Wt(n),null);case 22:case 23:return Ld(),a=n.memoizedState!==null,e!==null&&e.memoizedState!==null!==a&&(n.flags|=8192),a&&n.mode&1?mn&1073741824&&(Wt(n),n.subtreeFlags&6&&(n.flags|=8192)):Wt(n),null;case 24:return null;case 25:return null}throw Error(te(156,n.tag))}var Xx=Dr.ReactCurrentOwner,an=!1;function Yt(e,n,i,a){n.child=e===null?gm(n,null,i,a):bl(n,e.child,i,a)}function Nv(e,n,i,a,u){i=i.render;var c=n.ref;return Cl(n,u),a=gd(e,n,i,a,c,u),i=md(),e!==null&&!an?(n.updateQueue=e.updateQueue,n.flags&=-2053,e.lanes&=~u,_r(e,n,u)):(Ze&&i&&ud(n),n.flags|=1,Yt(e,n,a,u),n.child)}function Ev(e,n,i,a,u){if(e===null){var c=i.type;return typeof c=="function"&&!Ed(c)&&c.defaultProps===void 0&&i.compare===null&&i.defaultProps===void 0?(n.tag=15,n.type=c,Um(e,n,c,a,u)):(e=Sa(i.type,null,a,n,n.mode,u),e.ref=n.ref,e.return=n,n.child=e)}if(c=e.child,!(e.lanes&u)){var h=c.memoizedProps;if(i=i.compare,i=i!==null?i:Qo,i(h,a)&&e.ref===n.ref)return _r(e,n,u)}return n.flags|=1,e=si(c,a),e.ref=n.ref,e.return=n,n.child=e}function Um(e,n,i,a,u){if(e!==null){var c=e.memoizedProps;if(Qo(c,a)&&e.ref===n.ref)if(an=!1,n.pendingProps=a=c,(e.lanes&u)!==0)e.flags&131072&&(an=!0);else return n.lanes=e.lanes,_r(e,n,u)}return Lc(e,n,i,a,u)}function jm(e,n,i){var a=n.pendingProps,u=a.children,c=e!==null?e.memoizedState:null;if(a.mode==="hidden")if(!(n.mode&1))n.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ge(yl,mn),mn|=i;else if(i&1073741824)n.memoizedState={baseLanes:0,cachePool:null,transitions:null},a=c!==null?c.baseLanes:i,Ge(yl,mn),mn|=a;else return e=c!==null?c.baseLanes|i:i,n.lanes=n.childLanes=1073741824,n.memoizedState={baseLanes:e,cachePool:null,transitions:null},n.updateQueue=null,Ge(yl,mn),mn|=e,null;else c!==null?(a=c.baseLanes|i,n.memoizedState=null):a=i,Ge(yl,mn),mn|=a;return Yt(e,n,u,i),n.child}function $m(e,n){var i=n.ref;(e===null&&i!==null||e!==null&&e.ref!==i)&&(n.flags|=512,n.flags|=2097152)}function Lc(e,n,i,a,u){var c=fn(i)?_i:jt.current;return c=Nl(n,c),Cl(n,u),i=gd(e,n,i,a,c,u),a=md(),e!==null&&!an?(n.updateQueue=e.updateQueue,n.flags&=-2053,e.lanes&=~u,_r(e,n,u)):(Ze&&a&&ud(n),n.flags|=1,Yt(e,n,i,u),n.child)}function bv(e,n,i,a,u){if(fn(i)){var c=!0;Fa(n)}else c=!1;if(Cl(n,u),n.stateNode===null)e!==null&&(e.alternate=null,n.alternate=null,n.flags|=2),dm(n,i,a),xc(n,i,a,u),a=!0;else if(e===null){var h=n.stateNode,v=n.memoizedProps;h.props=v;var m=h.context,x=i.contextType;typeof x=="object"&&x!==null?x=zn(x):(x=fn(i)?_i:jt.current,x=Nl(n,x));var L=i.getDerivedStateFromProps,R=typeof L=="function"||typeof h.getSnapshotBeforeUpdate=="function";R||typeof h.UNSAFE_componentWillReceiveProps!="function"&&typeof h.componentWillReceiveProps!="function"||(v!==a||m!==x)&&mv(n,h,a,x),Vr=!1;var P=n.memoizedState;h.state=P,Ha(n,a,h,u),m=n.memoizedState,v!==a||P!==m||un.current||Vr?(typeof L=="function"&&(wc(n,i,L,a),m=n.memoizedState),(v=Vr||gv(n,i,v,a,P,m,x))?(R||typeof h.UNSAFE_componentWillMount!="function"&&typeof h.componentWillMount!="function"||(typeof h.componentWillMount=="function"&&h.componentWillMount(),typeof h.UNSAFE_componentWillMount=="function"&&h.UNSAFE_componentWillMount()),typeof h.componentDidMount=="function"&&(n.flags|=4194308)):(typeof h.componentDidMount=="function"&&(n.flags|=4194308),n.memoizedProps=a,n.memoizedState=m),h.props=a,h.state=m,h.context=x,a=v):(typeof h.componentDidMount=="function"&&(n.flags|=4194308),a=!1)}else{h=n.stateNode,fm(e,n),v=n.memoizedProps,x=n.type===n.elementType?v:Un(n.type,v),h.props=x,R=n.pendingProps,P=h.context,m=i.contextType,typeof m=="object"&&m!==null?m=zn(m):(m=fn(i)?_i:jt.current,m=Nl(n,m));var W=i.getDerivedStateFromProps;(L=typeof W=="function"||typeof h.getSnapshotBeforeUpdate=="function")||typeof h.UNSAFE_componentWillReceiveProps!="function"&&typeof h.componentWillReceiveProps!="function"||(v!==R||P!==m)&&mv(n,h,a,m),Vr=!1,P=n.memoizedState,h.state=P,Ha(n,a,h,u);var B=n.memoizedState;v!==R||P!==B||un.current||Vr?(typeof W=="function"&&(wc(n,i,W,a),B=n.memoizedState),(x=Vr||gv(n,i,x,a,P,B,m)||!1)?(L||typeof h.UNSAFE_componentWillUpdate!="function"&&typeof h.componentWillUpdate!="function"||(typeof h.componentWillUpdate=="function"&&h.componentWillUpdate(a,B,m),typeof h.UNSAFE_componentWillUpdate=="function"&&h.UNSAFE_componentWillUpdate(a,B,m)),typeof h.componentDidUpdate=="function"&&(n.flags|=4),typeof h.getSnapshotBeforeUpdate=="function"&&(n.flags|=1024)):(typeof h.componentDidUpdate!="function"||v===e.memoizedProps&&P===e.memoizedState||(n.flags|=4),typeof h.getSnapshotBeforeUpdate!="function"||v===e.memoizedProps&&P===e.memoizedState||(n.flags|=1024),n.memoizedProps=a,n.memoizedState=B),h.props=a,h.state=B,h.context=m,a=x):(typeof h.componentDidUpdate!="function"||v===e.memoizedProps&&P===e.memoizedState||(n.flags|=4),typeof h.getSnapshotBeforeUpdate!="function"||v===e.memoizedProps&&P===e.memoizedState||(n.flags|=1024),a=!1)}return Nc(e,n,i,a,c,u)}function Nc(e,n,i,a,u,c){$m(e,n);var h=(n.flags&128)!==0;if(!a&&!h)return u&&hv(n,i,!1),_r(e,n,c);a=n.stateNode,Xx.current=n;var v=h&&typeof i.getDerivedStateFromError!="function"?null:a.render();return n.flags|=1,e!==null&&h?(n.child=bl(n,e.child,null,c),n.child=bl(n,null,v,c)):Yt(e,n,v,c),n.memoizedState=a.state,u&&hv(n,i,!0),n.child}function Km(e){var n=e.stateNode;n.pendingContext?dv(e,n.pendingContext,n.pendingContext!==n.context):n.context&&dv(e,n.context,!1),dd(e,n.containerInfo)}function Mv(e,n,i,a,u){return El(),cd(u),n.flags|=256,Yt(e,n,i,a),n.child}var la={dehydrated:null,treeContext:null,retryLane:0};function oa(e){return{baseLanes:e,cachePool:null,transitions:null}}function _v(e,n){return{baseLanes:e.baseLanes|n,cachePool:null,transitions:e.transitions}}function Gm(e,n,i){var a=n.pendingProps,u=nt.current,c=!1,h=(n.flags&128)!==0,v;if((v=h)||(v=e!==null&&e.memoizedState===null?!1:(u&2)!==0),v?(c=!0,n.flags&=-129):(e===null||e.memoizedState!==null)&&(u|=1),Ge(nt,u&1),e===null)return kc(n),e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(n.mode&1?e.data==="$!"?n.lanes=8:n.lanes=1073741824:n.lanes=1,null):(u=a.children,e=a.fallback,c?(a=n.mode,c=n.child,u={mode:"hidden",children:u},!(a&1)&&c!==null?(c.childLanes=0,c.pendingProps=u):c=Xa(u,a,0,null),e=bi(e,a,i,null),c.return=n,e.return=n,c.sibling=e,n.child=c,n.child.memoizedState=oa(i),n.memoizedState=la,e):Ec(n,u));if(u=e.memoizedState,u!==null){if(v=u.dehydrated,v!==null){if(h)return n.flags&256?(n.flags&=-257,sa(e,n,i,Error(te(422)))):n.memoizedState!==null?(n.child=e.child,n.flags|=128,null):(c=a.fallback,u=n.mode,a=Xa({mode:"visible",children:a.children},u,0,null),c=bi(c,u,i,null),c.flags|=2,a.return=n,c.return=n,a.sibling=c,n.child=a,n.mode&1&&bl(n,e.child,null,i),n.child.memoizedState=oa(i),n.memoizedState=la,c);if(!(n.mode&1))n=sa(e,n,i,null);else if(v.data==="$!")n=sa(e,n,i,Error(te(419)));else if(a=(i&e.childLanes)!==0,an||a){if(a=Tt,a!==null){switch(i&-i){case 4:c=2;break;case 16:c=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:c=32;break;case 536870912:c=268435456;break;default:c=0}a=c&(a.suspendedLanes|i)?0:c,a!==0&&a!==u.retryLane&&(u.retryLane=a,Pn(e,a,-1))}Nd(),n=sa(e,n,i,Error(te(421)))}else v.data==="$?"?(n.flags|=128,n.child=e.child,n=uS.bind(null,e),v._reactRetry=n,n=null):(i=u.treeContext,sn=Tr(v.nextSibling),yn=n,Ze=!0,$n=null,i!==null&&(bn[Mn++]=Lr,bn[Mn++]=Nr,bn[Mn++]=Di,Lr=i.id,Nr=i.overflow,Di=n),n=Ec(n,n.pendingProps.children),n.flags|=4096);return n}return c?(a=Pv(e,n,a.children,a.fallback,i),c=n.child,u=e.child.memoizedState,c.memoizedState=u===null?oa(i):_v(u,i),c.childLanes=e.childLanes&~i,n.memoizedState=la,a):(i=Dv(e,n,a.children,i),n.memoizedState=null,i)}return c?(a=Pv(e,n,a.children,a.fallback,i),c=n.child,u=e.child.memoizedState,c.memoizedState=u===null?oa(i):_v(u,i),c.childLanes=e.childLanes&~i,n.memoizedState=la,a):(i=Dv(e,n,a.children,i),n.memoizedState=null,i)}function Ec(e,n){return n=Xa({mode:"visible",children:n},e.mode,0,null),n.return=e,e.child=n}function Dv(e,n,i,a){var u=e.child;return e=u.sibling,i=si(u,{mode:"visible",children:i}),!(n.mode&1)&&(i.lanes=a),i.return=n,i.sibling=null,e!==null&&(a=n.deletions,a===null?(n.deletions=[e],n.flags|=16):a.push(e)),n.child=i}function Pv(e,n,i,a,u){var c=n.mode;e=e.child;var h=e.sibling,v={mode:"hidden",children:i};return!(c&1)&&n.child!==e?(i=n.child,i.childLanes=0,i.pendingProps=v,n.deletions=null):(i=si(e,v),i.subtreeFlags=e.subtreeFlags&14680064),h!==null?a=si(h,a):(a=bi(a,c,u,null),a.flags|=2),a.return=n,i.return=n,i.sibling=a,n.child=i,a}function sa(e,n,i,a){return a!==null&&cd(a),bl(n,e.child,null,i),e=Ec(n,n.pendingProps.children),e.flags|=2,n.memoizedState=null,e}function Ov(e,n,i){e.lanes|=n;var a=e.alternate;a!==null&&(a.lanes|=n),yc(e.return,n,i)}function Af(e,n,i,a,u){var c=e.memoizedState;c===null?e.memoizedState={isBackwards:n,rendering:null,renderingStartTime:0,last:a,tail:i,tailMode:u}:(c.isBackwards=n,c.rendering=null,c.renderingStartTime=0,c.last=a,c.tail=i,c.tailMode=u)}function qm(e,n,i){var a=n.pendingProps,u=a.revealOrder,c=a.tail;if(Yt(e,n,a.children,i),a=nt.current,a&2)a=a&1|2,n.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=n.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Ov(e,i,n);else if(e.tag===19)Ov(e,i,n);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===n)break e;for(;e.sibling===null;){if(e.return===null||e.return===n)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}a&=1}if(Ge(nt,a),!(n.mode&1))n.memoizedState=null;else switch(u){case"forwards":for(i=n.child,u=null;i!==null;)e=i.alternate,e!==null&&$a(e)===null&&(u=i),i=i.sibling;i=u,i===null?(u=n.child,n.child=null):(u=i.sibling,i.sibling=null),Af(n,!1,u,i,c);break;case"backwards":for(i=null,u=n.child,n.child=null;u!==null;){if(e=u.alternate,e!==null&&$a(e)===null){n.child=u;break}e=u.sibling,u.sibling=i,i=u,u=e}Af(n,!0,i,null,c);break;case"together":Af(n,!1,null,null,void 0);break;default:n.memoizedState=null}return n.child}function _r(e,n,i){if(e!==null&&(n.dependencies=e.dependencies),Oi|=n.lanes,!(i&n.childLanes))return null;if(e!==null&&n.child!==e.child)throw Error(te(153));if(n.child!==null){for(e=n.child,i=si(e,e.pendingProps),n.child=i,i.return=n;e.sibling!==null;)e=e.sibling,i=i.sibling=si(e,e.pendingProps),i.return=n;i.sibling=null}return n.child}function Zx(e,n,i){switch(n.tag){case 3:Km(n),El();break;case 5:mm(n);break;case 1:fn(n.type)&&Fa(n);break;case 4:dd(n,n.stateNode.containerInfo);break;case 10:var a=n.type._context,u=n.memoizedProps.value;Ge(Wa,a._currentValue),a._currentValue=u;break;case 13:if(a=n.memoizedState,a!==null)return a.dehydrated!==null?(Ge(nt,nt.current&1),n.flags|=128,null):i&n.child.childLanes?Gm(e,n,i):(Ge(nt,nt.current&1),e=_r(e,n,i),e!==null?e.sibling:null);Ge(nt,nt.current&1);break;case 19:if(a=(i&n.childLanes)!==0,e.flags&128){if(a)return qm(e,n,i);n.flags|=128}if(u=n.memoizedState,u!==null&&(u.rendering=null,u.tail=null,u.lastEffect=null),Ge(nt,nt.current),a)break;return null;case 22:case 23:return n.lanes=0,jm(e,n,i)}return _r(e,n,i)}function Jx(e,n){switch(fd(n),n.tag){case 1:return fn(n.type)&&Ra(),e=n.flags,e&65536?(n.flags=e&-65537|128,n):null;case 3:return Ml(),Qe(un),Qe(jt),pd(),e=n.flags,e&65536&&!(e&128)?(n.flags=e&-65537|128,n):null;case 5:return hd(n),null;case 13:if(Qe(nt),e=n.memoizedState,e!==null&&e.dehydrated!==null){if(n.alternate===null)throw Error(te(340));El()}return e=n.flags,e&65536?(n.flags=e&-65537|128,n):null;case 19:return Qe(nt),null;case 4:return Ml(),null;case 10:return sd(n.type._context),null;case 22:case 23:return Ld(),null;case 24:return null;default:return null}}var aa=!1,Ht=!1,eS=typeof WeakSet=="function"?WeakSet:Set,fe=null;function ml(e,n){var i=e.ref;if(i!==null)if(typeof i=="function")try{i(null)}catch(a){ot(e,n,a)}else i.current=null}function bc(e,n,i){try{i()}catch(a){ot(e,n,a)}}var zv=!1;function tS(e,n){if(dc=Oa,e=Jg(),id(e)){if("selectionStart"in e)var i={start:e.selectionStart,end:e.selectionEnd};else e:{i=(i=e.ownerDocument)&&i.defaultView||window;var a=i.getSelection&&i.getSelection();if(a&&a.rangeCount!==0){i=a.anchorNode;var u=a.anchorOffset,c=a.focusNode;a=a.focusOffset;try{i.nodeType,c.nodeType}catch{i=null;break e}var h=0,v=-1,m=-1,x=0,L=0,R=e,P=null;t:for(;;){for(var W;R!==i||u!==0&&R.nodeType!==3||(v=h+u),R!==c||a!==0&&R.nodeType!==3||(m=h+a),R.nodeType===3&&(h+=R.nodeValue.length),(W=R.firstChild)!==null;)P=R,R=W;for(;;){if(R===e)break t;if(P===i&&++x===u&&(v=h),P===c&&++L===a&&(m=h),(W=R.nextSibling)!==null)break;R=P,P=R.parentNode}R=W}i=v===-1||m===-1?null:{start:v,end:m}}else i=null}i=i||{start:0,end:0}}else i=null;for(hc={focusedElem:e,selectionRange:i},Oa=!1,fe=n;fe!==null;)if(n=fe,e=n.child,(n.subtreeFlags&1028)!==0&&e!==null)e.return=n,fe=e;else for(;fe!==null;){n=fe;try{var B=n.alternate;if(n.flags&1024)switch(n.tag){case 0:case 11:case 15:break;case 1:if(B!==null){var Y=B.memoizedProps,K=B.memoizedState,T=n.stateNode,S=T.getSnapshotBeforeUpdate(n.elementType===n.type?Y:Un(n.type,Y),K);T.__reactInternalSnapshotBeforeUpdate=S}break;case 3:var M=n.stateNode.containerInfo;if(M.nodeType===1)M.textContent="";else if(M.nodeType===9){var A=M.body;A!=null&&(A.textContent="")}break;case 5:case 6:case 4:case 17:break;default:throw Error(te(163))}}catch(G){ot(n,n.return,G)}if(e=n.sibling,e!==null){e.return=n.return,fe=e;break}fe=n.return}return B=zv,zv=!1,B}function Fo(e,n,i){var a=n.updateQueue;if(a=a!==null?a.lastEffect:null,a!==null){var u=a=a.next;do{if((u.tag&e)===e){var c=u.destroy;u.destroy=void 0,c!==void 0&&bc(n,i,c)}u=u.next}while(u!==a)}}function fu(e,n){if(n=n.updateQueue,n=n!==null?n.lastEffect:null,n!==null){var i=n=n.next;do{if((i.tag&e)===e){var a=i.create;i.destroy=a()}i=i.next}while(i!==n)}}function Mc(e){var n=e.ref;if(n!==null){var i=e.stateNode;switch(e.tag){case 5:e=i;break;default:e=i}typeof n=="function"?n(e):n.current=e}}function Vm(e){var n=e.alternate;n!==null&&(e.alternate=null,Vm(n)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(n=e.stateNode,n!==null&&(delete n[lr],delete n[Zo],delete n[gc],delete n[Fx],delete n[Wx])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Qm(e){return e.tag===5||e.tag===3||e.tag===4}function Iv(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Qm(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function _c(e,n,i){var a=e.tag;if(a===5||a===6)e=e.stateNode,n?i.nodeType===8?i.parentNode.insertBefore(e,n):i.insertBefore(e,n):(i.nodeType===8?(n=i.parentNode,n.insertBefore(e,i)):(n=i,n.appendChild(e)),i=i._reactRootContainer,i!=null||n.onclick!==null||(n.onclick=Aa));else if(a!==4&&(e=e.child,e!==null))for(_c(e,n,i),e=e.sibling;e!==null;)_c(e,n,i),e=e.sibling}function Dc(e,n,i){var a=e.tag;if(a===5||a===6)e=e.stateNode,n?i.insertBefore(e,n):i.appendChild(e);else if(a!==4&&(e=e.child,e!==null))for(Dc(e,n,i),e=e.sibling;e!==null;)Dc(e,n,i),e=e.sibling}var Dt=null,jn=!1;function Gr(e,n,i){for(i=i.child;i!==null;)Ym(e,n,i),i=i.sibling}function Ym(e,n,i){if(or&&typeof or.onCommitFiberUnmount=="function")try{or.onCommitFiberUnmount(nu,i)}catch{}switch(i.tag){case 5:Ht||ml(i,n);case 6:var a=Dt,u=jn;Dt=null,Gr(e,n,i),Dt=a,jn=u,Dt!==null&&(jn?(e=Dt,i=i.stateNode,e.nodeType===8?e.parentNode.removeChild(i):e.removeChild(i)):Dt.removeChild(i.stateNode));break;case 18:Dt!==null&&(jn?(e=Dt,i=i.stateNode,e.nodeType===8?_f(e.parentNode,i):e.nodeType===1&&_f(e,i),qo(e)):_f(Dt,i.stateNode));break;case 4:a=Dt,u=jn,Dt=i.stateNode.containerInfo,jn=!0,Gr(e,n,i),Dt=a,jn=u;break;case 0:case 11:case 14:case 15:if(!Ht&&(a=i.updateQueue,a!==null&&(a=a.lastEffect,a!==null))){u=a=a.next;do{var c=u,h=c.destroy;c=c.tag,h!==void 0&&(c&2||c&4)&&bc(i,n,h),u=u.next}while(u!==a)}Gr(e,n,i);break;case 1:if(!Ht&&(ml(i,n),a=i.stateNode,typeof a.componentWillUnmount=="function"))try{a.props=i.memoizedProps,a.state=i.memoizedState,a.componentWillUnmount()}catch(v){ot(i,n,v)}Gr(e,n,i);break;case 21:Gr(e,n,i);break;case 22:i.mode&1?(Ht=(a=Ht)||i.memoizedState!==null,Gr(e,n,i),Ht=a):Gr(e,n,i);break;default:Gr(e,n,i)}}function Av(e){var n=e.updateQueue;if(n!==null){e.updateQueue=null;var i=e.stateNode;i===null&&(i=e.stateNode=new eS),n.forEach(function(a){var u=fS.bind(null,e,a);i.has(a)||(i.add(a),a.then(u,u))})}}function Hn(e,n){var i=n.deletions;if(i!==null)for(var a=0;a<i.length;a++){var u=i[a];try{var c=e,h=n,v=h;e:for(;v!==null;){switch(v.tag){case 5:Dt=v.stateNode,jn=!1;break e;case 3:Dt=v.stateNode.containerInfo,jn=!0;break e;case 4:Dt=v.stateNode.containerInfo,jn=!0;break e}v=v.return}if(Dt===null)throw Error(te(160));Ym(c,h,u),Dt=null,jn=!1;var m=u.alternate;m!==null&&(m.return=null),u.return=null}catch(x){ot(u,n,x)}}if(n.subtreeFlags&12854)for(n=n.child;n!==null;)Xm(n,e),n=n.sibling}function Xm(e,n){var i=e.alternate,a=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Hn(n,e),rr(e),a&4){try{Fo(3,e,e.return),fu(3,e)}catch(B){ot(e,e.return,B)}try{Fo(5,e,e.return)}catch(B){ot(e,e.return,B)}}break;case 1:Hn(n,e),rr(e),a&512&&i!==null&&ml(i,i.return);break;case 5:if(Hn(n,e),rr(e),a&512&&i!==null&&ml(i,i.return),e.flags&32){var u=e.stateNode;try{jo(u,"")}catch(B){ot(e,e.return,B)}}if(a&4&&(u=e.stateNode,u!=null)){var c=e.memoizedProps,h=i!==null?i.memoizedProps:c,v=e.type,m=e.updateQueue;if(e.updateQueue=null,m!==null)try{v==="input"&&c.type==="radio"&&c.name!=null&&xg(u,c),nc(v,h);var x=nc(v,c);for(h=0;h<m.length;h+=2){var L=m[h],R=m[h+1];L==="style"?Lg(u,R):L==="dangerouslySetInnerHTML"?Cg(u,R):L==="children"?jo(u,R):Kc(u,L,R,x)}switch(v){case"input":Xf(u,c);break;case"textarea":Sg(u,c);break;case"select":var P=u._wrapperState.wasMultiple;u._wrapperState.wasMultiple=!!c.multiple;var W=c.value;W!=null?wl(u,!!c.multiple,W,!1):P!==!!c.multiple&&(c.defaultValue!=null?wl(u,!!c.multiple,c.defaultValue,!0):wl(u,!!c.multiple,c.multiple?[]:"",!1))}u[Zo]=c}catch(B){ot(e,e.return,B)}}break;case 6:if(Hn(n,e),rr(e),a&4){if(e.stateNode===null)throw Error(te(162));x=e.stateNode,L=e.memoizedProps;try{x.nodeValue=L}catch(B){ot(e,e.return,B)}}break;case 3:if(Hn(n,e),rr(e),a&4&&i!==null&&i.memoizedState.isDehydrated)try{qo(n.containerInfo)}catch(B){ot(e,e.return,B)}break;case 4:Hn(n,e),rr(e);break;case 13:Hn(n,e),rr(e),x=e.child,x.flags&8192&&x.memoizedState!==null&&(x.alternate===null||x.alternate.memoizedState===null)&&(Cd=ut()),a&4&&Av(e);break;case 22:if(x=i!==null&&i.memoizedState!==null,e.mode&1?(Ht=(L=Ht)||x,Hn(n,e),Ht=L):Hn(n,e),rr(e),a&8192){L=e.memoizedState!==null;e:for(R=null,P=e;;){if(P.tag===5){if(R===null){R=P;try{u=P.stateNode,L?(c=u.style,typeof c.setProperty=="function"?c.setProperty("display","none","important"):c.display="none"):(v=P.stateNode,m=P.memoizedProps.style,h=m!=null&&m.hasOwnProperty("display")?m.display:null,v.style.display=Tg("display",h))}catch(B){ot(e,e.return,B)}}}else if(P.tag===6){if(R===null)try{P.stateNode.nodeValue=L?"":P.memoizedProps}catch(B){ot(e,e.return,B)}}else if((P.tag!==22&&P.tag!==23||P.memoizedState===null||P===e)&&P.child!==null){P.child.return=P,P=P.child;continue}if(P===e)break e;for(;P.sibling===null;){if(P.return===null||P.return===e)break e;R===P&&(R=null),P=P.return}R===P&&(R=null),P.sibling.return=P.return,P=P.sibling}if(L&&!x&&e.mode&1)for(fe=e,e=e.child;e!==null;){for(x=fe=e;fe!==null;){switch(L=fe,R=L.child,L.tag){case 0:case 11:case 14:case 15:Fo(4,L,L.return);break;case 1:if(ml(L,L.return),c=L.stateNode,typeof c.componentWillUnmount=="function"){P=L,W=L.return;try{u=P,c.props=u.memoizedProps,c.state=u.memoizedState,c.componentWillUnmount()}catch(B){ot(P,W,B)}}break;case 5:ml(L,L.return);break;case 22:if(L.memoizedState!==null){Fv(x);continue}}R!==null?(R.return=L,fe=R):Fv(x)}e=e.sibling}}break;case 19:Hn(n,e),rr(e),a&4&&Av(e);break;case 21:break;default:Hn(n,e),rr(e)}}function rr(e){var n=e.flags;if(n&2){try{e:{for(var i=e.return;i!==null;){if(Qm(i)){var a=i;break e}i=i.return}throw Error(te(160))}switch(a.tag){case 5:var u=a.stateNode;a.flags&32&&(jo(u,""),a.flags&=-33);var c=Iv(e);Dc(e,c,u);break;case 3:case 4:var h=a.stateNode.containerInfo,v=Iv(e);_c(e,v,h);break;default:throw Error(te(161))}}catch(m){ot(e,e.return,m)}e.flags&=-3}n&4096&&(e.flags&=-4097)}function nS(e,n,i){fe=e,Zm(e)}function Zm(e,n,i){for(var a=(e.mode&1)!==0;fe!==null;){var u=fe,c=u.child;if(u.tag===22&&a){var h=u.memoizedState!==null||aa;if(!h){var v=u.alternate,m=v!==null&&v.memoizedState!==null||Ht;v=aa;var x=Ht;if(aa=h,(Ht=m)&&!x)for(fe=u;fe!==null;)h=fe,m=h.child,h.tag===22&&h.memoizedState!==null?Wv(u):m!==null?(m.return=h,fe=m):Wv(u);for(;c!==null;)fe=c,Zm(c),c=c.sibling;fe=u,aa=v,Ht=x}Rv(e)}else u.subtreeFlags&8772&&c!==null?(c.return=u,fe=c):Rv(e)}}function Rv(e){for(;fe!==null;){var n=fe;if(n.flags&8772){var i=n.alternate;try{if(n.flags&8772)switch(n.tag){case 0:case 11:case 15:Ht||fu(5,n);break;case 1:var a=n.stateNode;if(n.flags&4&&!Ht)if(i===null)a.componentDidMount();else{var u=n.elementType===n.type?i.memoizedProps:Un(n.type,i.memoizedProps);a.componentDidUpdate(u,i.memoizedState,a.__reactInternalSnapshotBeforeUpdate)}var c=n.updateQueue;c!==null&&vv(n,c,a);break;case 3:var h=n.updateQueue;if(h!==null){if(i=null,n.child!==null)switch(n.child.tag){case 5:i=n.child.stateNode;break;case 1:i=n.child.stateNode}vv(n,h,i)}break;case 5:var v=n.stateNode;if(i===null&&n.flags&4){i=v;var m=n.memoizedProps;switch(n.type){case"button":case"input":case"select":case"textarea":m.autoFocus&&i.focus();break;case"img":m.src&&(i.src=m.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(n.memoizedState===null){var x=n.alternate;if(x!==null){var L=x.memoizedState;if(L!==null){var R=L.dehydrated;R!==null&&qo(R)}}}break;case 19:case 17:case 21:case 22:case 23:break;default:throw Error(te(163))}Ht||n.flags&512&&Mc(n)}catch(P){ot(n,n.return,P)}}if(n===e){fe=null;break}if(i=n.sibling,i!==null){i.return=n.return,fe=i;break}fe=n.return}}function Fv(e){for(;fe!==null;){var n=fe;if(n===e){fe=null;break}var i=n.sibling;if(i!==null){i.return=n.return,fe=i;break}fe=n.return}}function Wv(e){for(;fe!==null;){var n=fe;try{switch(n.tag){case 0:case 11:case 15:var i=n.return;try{fu(4,n)}catch(m){ot(n,i,m)}break;case 1:var a=n.stateNode;if(typeof a.componentDidMount=="function"){var u=n.return;try{a.componentDidMount()}catch(m){ot(n,u,m)}}var c=n.return;try{Mc(n)}catch(m){ot(n,c,m)}break;case 5:var h=n.return;try{Mc(n)}catch(m){ot(n,h,m)}}}catch(m){ot(n,n.return,m)}if(n===e){fe=null;break}var v=n.sibling;if(v!==null){v.return=n.return,fe=v;break}fe=n.return}}var rS=Math.ceil,qa=Dr.ReactCurrentDispatcher,Sd=Dr.ReactCurrentOwner,Dn=Dr.ReactCurrentBatchConfig,Ae=0,Tt=null,vt=null,Pt=0,mn=0,yl=ui(0),Ct=0,is=null,Oi=0,cu=0,kd=0,Wo=null,on=null,Cd=0,_l=1/0,kr=null,Va=!1,Pc=null,ri=null,ua=!1,Zr=null,Qa=0,Bo=0,Oc=null,wa=-1,xa=0;function Xt(){return Ae&6?ut():wa!==-1?wa:wa=ut()}function ii(e){return e.mode&1?Ae&2&&Pt!==0?Pt&-Pt:Hx.transition!==null?(xa===0&&(xa=Rg()),xa):(e=He,e!==0||(e=window.event,e=e===void 0?16:$g(e.type)),e):1}function Pn(e,n,i){if(50<Bo)throw Bo=0,Oc=null,Error(te(185));var a=du(e,n);return a===null?null:(os(a,n,i),(!(Ae&2)||a!==Tt)&&(a===Tt&&(!(Ae&2)&&(cu|=n),Ct===4&&Yr(a,Pt)),cn(a,i),n===1&&Ae===0&&!(e.mode&1)&&(_l=ut()+500,su&&fi())),a)}function du(e,n){e.lanes|=n;var i=e.alternate;for(i!==null&&(i.lanes|=n),i=e,e=e.return;e!==null;)e.childLanes|=n,i=e.alternate,i!==null&&(i.childLanes|=n),i=e,e=e.return;return i.tag===3?i.stateNode:null}function Jm(e){return(Tt!==null||Kn!==null)&&(e.mode&1)!==0&&(Ae&2)===0}function cn(e,n){var i=e.callbackNode;H1(e,n);var a=Pa(e,e===Tt?Pt:0);if(a===0)i!==null&&Kp(i),e.callbackNode=null,e.callbackPriority=0;else if(n=a&-a,e.callbackPriority!==n){if(i!=null&&Kp(i),n===1)e.tag===0?Bx(Bv.bind(null,e)):um(Bv.bind(null,e)),Ax(function(){Ae===0&&fi()}),i=null;else{switch(Fg(a)){case 1:i=Yc;break;case 4:i=Ig;break;case 16:i=Da;break;case 536870912:i=Ag;break;default:i=Da}i=s0(i,e0.bind(null,e))}e.callbackPriority=n,e.callbackNode=i}}function e0(e,n){if(wa=-1,xa=0,Ae&6)throw Error(te(327));var i=e.callbackNode;if(Tl()&&e.callbackNode!==i)return null;var a=Pa(e,e===Tt?Pt:0);if(a===0)return null;if(a&30||a&e.expiredLanes||n)n=Ya(e,a);else{n=a;var u=Ae;Ae|=2;var c=n0();(Tt!==e||Pt!==n)&&(kr=null,_l=ut()+500,Ei(e,n));do try{oS();break}catch(v){t0(e,v)}while(1);od(),qa.current=c,Ae=u,vt!==null?n=0:(Tt=null,Pt=0,n=Ct)}if(n!==0){if(n===2&&(u=sc(e),u!==0&&(a=u,n=zc(e,u))),n===1)throw i=is,Ei(e,0),Yr(e,a),cn(e,ut()),i;if(n===6)Yr(e,a);else{if(u=e.current.alternate,!(a&30)&&!iS(u)&&(n=Ya(e,a),n===2&&(c=sc(e),c!==0&&(a=c,n=zc(e,c))),n===1))throw i=is,Ei(e,0),Yr(e,a),cn(e,ut()),i;switch(e.finishedWork=u,e.finishedLanes=a,n){case 0:case 1:throw Error(te(345));case 2:Ti(e,on,kr);break;case 3:if(Yr(e,a),(a&130023424)===a&&(n=Cd+500-ut(),10<n)){if(Pa(e,0)!==0)break;if(u=e.suspendedLanes,(u&a)!==a){Xt(),e.pingedLanes|=e.suspendedLanes&u;break}e.timeoutHandle=vc(Ti.bind(null,e,on,kr),n);break}Ti(e,on,kr);break;case 4:if(Yr(e,a),(a&4194240)===a)break;for(n=e.eventTimes,u=-1;0<a;){var h=31-Gn(a);c=1<<h,h=n[h],h>u&&(u=h),a&=~c}if(a=u,a=ut()-a,a=(120>a?120:480>a?480:1080>a?1080:1920>a?1920:3e3>a?3e3:4320>a?4320:1960*rS(a/1960))-a,10<a){e.timeoutHandle=vc(Ti.bind(null,e,on,kr),a);break}Ti(e,on,kr);break;case 5:Ti(e,on,kr);break;default:throw Error(te(329))}}}return cn(e,ut()),e.callbackNode===i?e0.bind(null,e):null}function zc(e,n){var i=Wo;return e.current.memoizedState.isDehydrated&&(Ei(e,n).flags|=256),e=Ya(e,n),e!==2&&(n=on,on=i,n!==null&&Ic(n)),e}function Ic(e){on===null?on=e:on.push.apply(on,e)}function iS(e){for(var n=e;;){if(n.flags&16384){var i=n.updateQueue;if(i!==null&&(i=i.stores,i!==null))for(var a=0;a<i.length;a++){var u=i[a],c=u.getSnapshot;u=u.value;try{if(!qn(c(),u))return!1}catch{return!1}}}if(i=n.child,n.subtreeFlags&16384&&i!==null)i.return=n,n=i;else{if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return!0;n=n.return}n.sibling.return=n.return,n=n.sibling}}return!0}function Yr(e,n){for(n&=~kd,n&=~cu,e.suspendedLanes|=n,e.pingedLanes&=~n,e=e.expirationTimes;0<n;){var i=31-Gn(n),a=1<<i;e[i]=-1,n&=~a}}function Bv(e){if(Ae&6)throw Error(te(327));Tl();var n=Pa(e,0);if(!(n&1))return cn(e,ut()),null;var i=Ya(e,n);if(e.tag!==0&&i===2){var a=sc(e);a!==0&&(n=a,i=zc(e,a))}if(i===1)throw i=is,Ei(e,0),Yr(e,n),cn(e,ut()),i;if(i===6)throw Error(te(345));return e.finishedWork=e.current.alternate,e.finishedLanes=n,Ti(e,on,kr),cn(e,ut()),null}function Td(e,n){var i=Ae;Ae|=1;try{return e(n)}finally{Ae=i,Ae===0&&(_l=ut()+500,su&&fi())}}function zi(e){Zr!==null&&Zr.tag===0&&!(Ae&6)&&Tl();var n=Ae;Ae|=1;var i=Dn.transition,a=He;try{if(Dn.transition=null,He=1,e)return e()}finally{He=a,Dn.transition=i,Ae=n,!(Ae&6)&&fi()}}function Ld(){mn=yl.current,Qe(yl)}function Ei(e,n){e.finishedWork=null,e.finishedLanes=0;var i=e.timeoutHandle;if(i!==-1&&(e.timeoutHandle=-1,Ix(i)),vt!==null)for(i=vt.return;i!==null;){var a=i;switch(fd(a),a.tag){case 1:a=a.type.childContextTypes,a!=null&&Ra();break;case 3:Ml(),Qe(un),Qe(jt),pd();break;case 5:hd(a);break;case 4:Ml();break;case 13:Qe(nt);break;case 19:Qe(nt);break;case 10:sd(a.type._context);break;case 22:case 23:Ld()}i=i.return}if(Tt=e,vt=e=si(e.current,null),Pt=mn=n,Ct=0,is=null,kd=cu=Oi=0,on=Wo=null,Kn!==null){for(n=0;n<Kn.length;n++)if(i=Kn[n],a=i.interleaved,a!==null){i.interleaved=null;var u=a.next,c=i.pending;if(c!==null){var h=c.next;c.next=u,a.next=h}i.pending=a}Kn=null}return e}function t0(e,n){do{var i=vt;try{if(od(),ma.current=Ga,Ka){for(var a=rt.memoizedState;a!==null;){var u=a.queue;u!==null&&(u.pending=null),a=a.next}Ka=!1}if(Pi=0,Nt=kt=rt=null,Ro=!1,ts=0,Sd.current=null,i===null||i.return===null){Ct=1,is=n,vt=null;break}e:{var c=e,h=i.return,v=i,m=n;if(n=Pt,v.flags|=32768,m!==null&&typeof m=="object"&&typeof m.then=="function"){var x=m,L=v,R=L.tag;if(!(L.mode&1)&&(R===0||R===11||R===15)){var P=L.alternate;P?(L.updateQueue=P.updateQueue,L.memoizedState=P.memoizedState,L.lanes=P.lanes):(L.updateQueue=null,L.memoizedState=null)}var W=Tv(h);if(W!==null){W.flags&=-257,Lv(W,h,v,c,n),W.mode&1&&Cv(c,x,n),n=W,m=x;var B=n.updateQueue;if(B===null){var Y=new Set;Y.add(m),n.updateQueue=Y}else B.add(m);break e}else{if(!(n&1)){Cv(c,x,n),Nd();break e}m=Error(te(426))}}else if(Ze&&v.mode&1){var K=Tv(h);if(K!==null){!(K.flags&65536)&&(K.flags|=256),Lv(K,h,v,c,n),cd(m);break e}}c=m,Ct!==4&&(Ct=2),Wo===null?Wo=[c]:Wo.push(c),m=xd(m,v),v=h;do{switch(v.tag){case 3:v.flags|=65536,n&=-n,v.lanes|=n;var T=Rm(v,m,n);pv(v,T);break e;case 1:c=m;var S=v.type,M=v.stateNode;if(!(v.flags&128)&&(typeof S.getDerivedStateFromError=="function"||M!==null&&typeof M.componentDidCatch=="function"&&(ri===null||!ri.has(M)))){v.flags|=65536,n&=-n,v.lanes|=n;var A=Fm(v,c,n);pv(v,A);break e}}v=v.return}while(v!==null)}i0(i)}catch(G){n=G,vt===i&&i!==null&&(vt=i=i.return);continue}break}while(1)}function n0(){var e=qa.current;return qa.current=Ga,e===null?Ga:e}function Nd(){(Ct===0||Ct===3||Ct===2)&&(Ct=4),Tt===null||!(Oi&268435455)&&!(cu&268435455)||Yr(Tt,Pt)}function Ya(e,n){var i=Ae;Ae|=2;var a=n0();(Tt!==e||Pt!==n)&&(kr=null,Ei(e,n));do try{lS();break}catch(u){t0(e,u)}while(1);if(od(),Ae=i,qa.current=a,vt!==null)throw Error(te(261));return Tt=null,Pt=0,Ct}function lS(){for(;vt!==null;)r0(vt)}function oS(){for(;vt!==null&&!P1();)r0(vt)}function r0(e){var n=o0(e.alternate,e,mn);e.memoizedProps=e.pendingProps,n===null?i0(e):vt=n,Sd.current=null}function i0(e){var n=e;do{var i=n.alternate;if(e=n.return,n.flags&32768){if(i=Jx(i,n),i!==null){i.flags&=32767,vt=i;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Ct=6,vt=null;return}}else if(i=Yx(i,n,mn),i!==null){vt=i;return}if(n=n.sibling,n!==null){vt=n;return}vt=n=e}while(n!==null);Ct===0&&(Ct=5)}function Ti(e,n,i){var a=He,u=Dn.transition;try{Dn.transition=null,He=1,sS(e,n,i,a)}finally{Dn.transition=u,He=a}return null}function sS(e,n,i,a){do Tl();while(Zr!==null);if(Ae&6)throw Error(te(327));i=e.finishedWork;var u=e.finishedLanes;if(i===null)return null;if(e.finishedWork=null,e.finishedLanes=0,i===e.current)throw Error(te(177));e.callbackNode=null,e.callbackPriority=0;var c=i.lanes|i.childLanes;if(U1(e,c),e===Tt&&(vt=Tt=null,Pt=0),!(i.subtreeFlags&2064)&&!(i.flags&2064)||ua||(ua=!0,s0(Da,function(){return Tl(),null})),c=(i.flags&15990)!==0,i.subtreeFlags&15990||c){c=Dn.transition,Dn.transition=null;var h=He;He=1;var v=Ae;Ae|=4,Sd.current=null,tS(e,i),Xm(i,e),bx(hc),Oa=!!dc,hc=dc=null,e.current=i,nS(i),O1(),Ae=v,He=h,Dn.transition=c}else e.current=i;if(ua&&(ua=!1,Zr=e,Qa=u),c=e.pendingLanes,c===0&&(ri=null),A1(i.stateNode),cn(e,ut()),n!==null)for(a=e.onRecoverableError,i=0;i<n.length;i++)a(n[i]);if(Va)throw Va=!1,e=Pc,Pc=null,e;return Qa&1&&e.tag!==0&&Tl(),c=e.pendingLanes,c&1?e===Oc?Bo++:(Bo=0,Oc=e):Bo=0,fi(),null}function Tl(){if(Zr!==null){var e=Fg(Qa),n=Dn.transition,i=He;try{if(Dn.transition=null,He=16>e?16:e,Zr===null)var a=!1;else{if(e=Zr,Zr=null,Qa=0,Ae&6)throw Error(te(331));var u=Ae;for(Ae|=4,fe=e.current;fe!==null;){var c=fe,h=c.child;if(fe.flags&16){var v=c.deletions;if(v!==null){for(var m=0;m<v.length;m++){var x=v[m];for(fe=x;fe!==null;){var L=fe;switch(L.tag){case 0:case 11:case 15:Fo(8,L,c)}var R=L.child;if(R!==null)R.return=L,fe=R;else for(;fe!==null;){L=fe;var P=L.sibling,W=L.return;if(Vm(L),L===x){fe=null;break}if(P!==null){P.return=W,fe=P;break}fe=W}}}var B=c.alternate;if(B!==null){var Y=B.child;if(Y!==null){B.child=null;do{var K=Y.sibling;Y.sibling=null,Y=K}while(Y!==null)}}fe=c}}if(c.subtreeFlags&2064&&h!==null)h.return=c,fe=h;else e:for(;fe!==null;){if(c=fe,c.flags&2048)switch(c.tag){case 0:case 11:case 15:Fo(9,c,c.return)}var T=c.sibling;if(T!==null){T.return=c.return,fe=T;break e}fe=c.return}}var S=e.current;for(fe=S;fe!==null;){h=fe;var M=h.child;if(h.subtreeFlags&2064&&M!==null)M.return=h,fe=M;else e:for(h=S;fe!==null;){if(v=fe,v.flags&2048)try{switch(v.tag){case 0:case 11:case 15:fu(9,v)}}catch(G){ot(v,v.return,G)}if(v===h){fe=null;break e}var A=v.sibling;if(A!==null){A.return=v.return,fe=A;break e}fe=v.return}}if(Ae=u,fi(),or&&typeof or.onPostCommitFiberRoot=="function")try{or.onPostCommitFiberRoot(nu,e)}catch{}a=!0}return a}finally{He=i,Dn.transition=n}}return!1}function Hv(e,n,i){n=xd(i,n),n=Rm(e,n,1),ni(e,n),n=Xt(),e=du(e,1),e!==null&&(os(e,1,n),cn(e,n))}function ot(e,n,i){if(e.tag===3)Hv(e,e,i);else for(;n!==null;){if(n.tag===3){Hv(n,e,i);break}else if(n.tag===1){var a=n.stateNode;if(typeof n.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(ri===null||!ri.has(a))){e=xd(i,e),e=Fm(n,e,1),ni(n,e),e=Xt(),n=du(n,1),n!==null&&(os(n,1,e),cn(n,e));break}}n=n.return}}function aS(e,n,i){var a=e.pingCache;a!==null&&a.delete(n),n=Xt(),e.pingedLanes|=e.suspendedLanes&i,Tt===e&&(Pt&i)===i&&(Ct===4||Ct===3&&(Pt&130023424)===Pt&&500>ut()-Cd?Ei(e,0):kd|=i),cn(e,n)}function l0(e,n){n===0&&(e.mode&1?(n=Zs,Zs<<=1,!(Zs&130023424)&&(Zs=4194304)):n=1);var i=Xt();e=du(e,n),e!==null&&(os(e,n,i),cn(e,i))}function uS(e){var n=e.memoizedState,i=0;n!==null&&(i=n.retryLane),l0(e,i)}function fS(e,n){var i=0;switch(e.tag){case 13:var a=e.stateNode,u=e.memoizedState;u!==null&&(i=u.retryLane);break;case 19:a=e.stateNode;break;default:throw Error(te(314))}a!==null&&a.delete(n),l0(e,i)}var o0;o0=function(e,n,i){if(e!==null)if(e.memoizedProps!==n.pendingProps||un.current)an=!0;else{if(!(e.lanes&i)&&!(n.flags&128))return an=!1,Zx(e,n,i);an=!!(e.flags&131072)}else an=!1,Ze&&n.flags&1048576&&hm(n,ja,n.index);switch(n.lanes=0,n.tag){case 2:var a=n.type;e!==null&&(e.alternate=null,n.alternate=null,n.flags|=2),e=n.pendingProps;var u=Nl(n,jt.current);Cl(n,i),u=gd(null,n,a,e,u,i);var c=md();return n.flags|=1,typeof u=="object"&&u!==null&&typeof u.render=="function"&&u.$$typeof===void 0?(n.tag=1,n.memoizedState=null,n.updateQueue=null,fn(a)?(c=!0,Fa(n)):c=!1,n.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,ad(n),u.updater=au,n.stateNode=u,u._reactInternals=n,xc(n,a,e,i),n=Nc(null,n,a,!0,c,i)):(n.tag=0,Ze&&c&&ud(n),Yt(null,n,u,i),n=n.child),n;case 16:a=n.elementType;e:{switch(e!==null&&(e.alternate=null,n.alternate=null,n.flags|=2),e=n.pendingProps,u=a._init,a=u(a._payload),n.type=a,u=n.tag=dS(a),e=Un(a,e),u){case 0:n=Lc(null,n,a,e,i);break e;case 1:n=bv(null,n,a,e,i);break e;case 11:n=Nv(null,n,a,e,i);break e;case 14:n=Ev(null,n,a,Un(a.type,e),i);break e}throw Error(te(306,a,""))}return n;case 0:return a=n.type,u=n.pendingProps,u=n.elementType===a?u:Un(a,u),Lc(e,n,a,u,i);case 1:return a=n.type,u=n.pendingProps,u=n.elementType===a?u:Un(a,u),bv(e,n,a,u,i);case 3:e:{if(Km(n),e===null)throw Error(te(387));a=n.pendingProps,c=n.memoizedState,u=c.element,fm(e,n),Ha(n,a,null,i);var h=n.memoizedState;if(a=h.element,c.isDehydrated)if(c={element:a,isDehydrated:!1,cache:h.cache,pendingSuspenseBoundaries:h.pendingSuspenseBoundaries,transitions:h.transitions},n.updateQueue.baseState=c,n.memoizedState=c,n.flags&256){u=Error(te(423)),n=Mv(e,n,a,i,u);break e}else if(a!==u){u=Error(te(424)),n=Mv(e,n,a,i,u);break e}else for(sn=Tr(n.stateNode.containerInfo.firstChild),yn=n,Ze=!0,$n=null,i=gm(n,null,a,i),n.child=i;i;)i.flags=i.flags&-3|4096,i=i.sibling;else{if(El(),a===u){n=_r(e,n,i);break e}Yt(e,n,a,i)}n=n.child}return n;case 5:return mm(n),e===null&&kc(n),a=n.type,u=n.pendingProps,c=e!==null?e.memoizedProps:null,h=u.children,pc(a,u)?h=null:c!==null&&pc(a,c)&&(n.flags|=32),$m(e,n),Yt(e,n,h,i),n.child;case 6:return e===null&&kc(n),null;case 13:return Gm(e,n,i);case 4:return dd(n,n.stateNode.containerInfo),a=n.pendingProps,e===null?n.child=bl(n,null,a,i):Yt(e,n,a,i),n.child;case 11:return a=n.type,u=n.pendingProps,u=n.elementType===a?u:Un(a,u),Nv(e,n,a,u,i);case 7:return Yt(e,n,n.pendingProps,i),n.child;case 8:return Yt(e,n,n.pendingProps.children,i),n.child;case 12:return Yt(e,n,n.pendingProps.children,i),n.child;case 10:e:{if(a=n.type._context,u=n.pendingProps,c=n.memoizedProps,h=u.value,Ge(Wa,a._currentValue),a._currentValue=h,c!==null)if(qn(c.value,h)){if(c.children===u.children&&!un.current){n=_r(e,n,i);break e}}else for(c=n.child,c!==null&&(c.return=n);c!==null;){var v=c.dependencies;if(v!==null){h=c.child;for(var m=v.firstContext;m!==null;){if(m.context===a){if(c.tag===1){m=Er(-1,i&-i),m.tag=2;var x=c.updateQueue;if(x!==null){x=x.shared;var L=x.pending;L===null?m.next=m:(m.next=L.next,L.next=m),x.pending=m}}c.lanes|=i,m=c.alternate,m!==null&&(m.lanes|=i),yc(c.return,i,n),v.lanes|=i;break}m=m.next}}else if(c.tag===10)h=c.type===n.type?null:c.child;else if(c.tag===18){if(h=c.return,h===null)throw Error(te(341));h.lanes|=i,v=h.alternate,v!==null&&(v.lanes|=i),yc(h,i,n),h=c.sibling}else h=c.child;if(h!==null)h.return=c;else for(h=c;h!==null;){if(h===n){h=null;break}if(c=h.sibling,c!==null){c.return=h.return,h=c;break}h=h.return}c=h}Yt(e,n,u.children,i),n=n.child}return n;case 9:return u=n.type,a=n.pendingProps.children,Cl(n,i),u=zn(u),a=a(u),n.flags|=1,Yt(e,n,a,i),n.child;case 14:return a=n.type,u=Un(a,n.pendingProps),u=Un(a.type,u),Ev(e,n,a,u,i);case 15:return Um(e,n,n.type,n.pendingProps,i);case 17:return a=n.type,u=n.pendingProps,u=n.elementType===a?u:Un(a,u),e!==null&&(e.alternate=null,n.alternate=null,n.flags|=2),n.tag=1,fn(a)?(e=!0,Fa(n)):e=!1,Cl(n,i),dm(n,a,u),xc(n,a,u,i),Nc(null,n,a,!0,e,i);case 19:return qm(e,n,i);case 22:return jm(e,n,i)}throw Error(te(156,n.tag))};function s0(e,n){return zg(e,n)}function cS(e,n,i,a){this.tag=e,this.key=i,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=n,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function _n(e,n,i,a){return new cS(e,n,i,a)}function Ed(e){return e=e.prototype,!(!e||!e.isReactComponent)}function dS(e){if(typeof e=="function")return Ed(e)?1:0;if(e!=null){if(e=e.$$typeof,e===qc)return 11;if(e===Vc)return 14}return 2}function si(e,n){var i=e.alternate;return i===null?(i=_n(e.tag,n,e.key,e.mode),i.elementType=e.elementType,i.type=e.type,i.stateNode=e.stateNode,i.alternate=e,e.alternate=i):(i.pendingProps=n,i.type=e.type,i.flags=0,i.subtreeFlags=0,i.deletions=null),i.flags=e.flags&14680064,i.childLanes=e.childLanes,i.lanes=e.lanes,i.child=e.child,i.memoizedProps=e.memoizedProps,i.memoizedState=e.memoizedState,i.updateQueue=e.updateQueue,n=e.dependencies,i.dependencies=n===null?null:{lanes:n.lanes,firstContext:n.firstContext},i.sibling=e.sibling,i.index=e.index,i.ref=e.ref,i}function Sa(e,n,i,a,u,c){var h=2;if(a=e,typeof e=="function")Ed(e)&&(h=1);else if(typeof e=="string")h=5;else e:switch(e){case al:return bi(i.children,u,c,n);case Gc:h=8,u|=8;break;case Gf:return e=_n(12,i,n,u|2),e.elementType=Gf,e.lanes=c,e;case qf:return e=_n(13,i,n,u),e.elementType=qf,e.lanes=c,e;case Vf:return e=_n(19,i,n,u),e.elementType=Vf,e.lanes=c,e;case mg:return Xa(i,u,c,n);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case vg:h=10;break e;case gg:h=9;break e;case qc:h=11;break e;case Vc:h=14;break e;case qr:h=16,a=null;break e}throw Error(te(130,e==null?e:typeof e,""))}return n=_n(h,i,n,u),n.elementType=e,n.type=a,n.lanes=c,n}function bi(e,n,i,a){return e=_n(7,e,a,n),e.lanes=i,e}function Xa(e,n,i,a){return e=_n(22,e,a,n),e.elementType=mg,e.lanes=i,e.stateNode={},e}function Rf(e,n,i){return e=_n(6,e,null,n),e.lanes=i,e}function Ff(e,n,i){return n=_n(4,e.children!==null?e.children:[],e.key,n),n.lanes=i,n.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},n}function hS(e,n,i,a,u){this.tag=n,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=wf(0),this.expirationTimes=wf(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=wf(0),this.identifierPrefix=a,this.onRecoverableError=u,this.mutableSourceEagerHydrationData=null}function bd(e,n,i,a,u,c,h,v,m){return e=new hS(e,n,i,v,m),n===1?(n=1,c===!0&&(n|=8)):n=0,c=_n(3,null,null,n),e.current=c,c.stateNode=e,c.memoizedState={element:a,isDehydrated:i,cache:null,transitions:null,pendingSuspenseBoundaries:null},ad(c),e}function pS(e,n,i){var a=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:sl,key:a==null?null:""+a,children:e,containerInfo:n,implementation:i}}function a0(e){if(!e)return oi;e=e._reactInternals;e:{if(Ai(e)!==e||e.tag!==1)throw Error(te(170));var n=e;do{switch(n.tag){case 3:n=n.stateNode.context;break e;case 1:if(fn(n.type)){n=n.stateNode.__reactInternalMemoizedMergedChildContext;break e}}n=n.return}while(n!==null);throw Error(te(171))}if(e.tag===1){var i=e.type;if(fn(i))return am(e,i,n)}return n}function u0(e,n,i,a,u,c,h,v,m){return e=bd(i,a,!0,e,u,c,h,v,m),e.context=a0(null),i=e.current,a=Xt(),u=ii(i),c=Er(a,u),c.callback=n??null,ni(i,c),e.current.lanes=u,os(e,u,a),cn(e,a),e}function hu(e,n,i,a){var u=n.current,c=Xt(),h=ii(u);return i=a0(i),n.context===null?n.context=i:n.pendingContext=i,n=Er(c,h),n.payload={element:e},a=a===void 0?null:a,a!==null&&(n.callback=a),ni(u,n),e=Pn(u,h,c),e!==null&&ga(e,u,h),h}function Za(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Uv(e,n){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var i=e.retryLane;e.retryLane=i!==0&&i<n?i:n}}function Md(e,n){Uv(e,n),(e=e.alternate)&&Uv(e,n)}function vS(){return null}var f0=typeof reportError=="function"?reportError:function(e){console.error(e)};function _d(e){this._internalRoot=e}pu.prototype.render=_d.prototype.render=function(e){var n=this._internalRoot;if(n===null)throw Error(te(409));hu(e,n,null,null)};pu.prototype.unmount=_d.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var n=e.containerInfo;zi(function(){hu(null,e,null,null)}),n[Mr]=null}};function pu(e){this._internalRoot=e}pu.prototype.unstable_scheduleHydration=function(e){if(e){var n=Hg();e={blockedOn:null,target:e,priority:n};for(var i=0;i<Qr.length&&n!==0&&n<Qr[i].priority;i++);Qr.splice(i,0,e),i===0&&jg(e)}};function Dd(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function vu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function jv(){}function gS(e,n,i,a,u){if(u){if(typeof a=="function"){var c=a;a=function(){var x=Za(h);c.call(x)}}var h=u0(n,a,e,0,null,!1,!1,"",jv);return e._reactRootContainer=h,e[Mr]=h.current,Yo(e.nodeType===8?e.parentNode:e),zi(),h}for(;u=e.lastChild;)e.removeChild(u);if(typeof a=="function"){var v=a;a=function(){var x=Za(m);v.call(x)}}var m=bd(e,0,!1,null,null,!1,!1,"",jv);return e._reactRootContainer=m,e[Mr]=m.current,Yo(e.nodeType===8?e.parentNode:e),zi(function(){hu(n,m,i,a)}),m}function gu(e,n,i,a,u){var c=i._reactRootContainer;if(c){var h=c;if(typeof u=="function"){var v=u;u=function(){var m=Za(h);v.call(m)}}hu(n,h,e,u)}else h=gS(i,n,e,u,a);return Za(h)}Wg=function(e){switch(e.tag){case 3:var n=e.stateNode;if(n.current.memoizedState.isDehydrated){var i=Mo(n.pendingLanes);i!==0&&(Xc(n,i|1),cn(n,ut()),!(Ae&6)&&(_l=ut()+500,fi()))}break;case 13:var a=Xt();zi(function(){return Pn(e,1,a)}),Md(e,1)}};Zc=function(e){if(e.tag===13){var n=Xt();Pn(e,134217728,n),Md(e,134217728)}};Bg=function(e){if(e.tag===13){var n=Xt(),i=ii(e);Pn(e,i,n),Md(e,i)}};Hg=function(){return He};Ug=function(e,n){var i=He;try{return He=e,n()}finally{He=i}};ic=function(e,n,i){switch(n){case"input":if(Xf(e,i),n=i.name,i.type==="radio"&&n!=null){for(i=e;i.parentNode;)i=i.parentNode;for(i=i.querySelectorAll("input[name="+JSON.stringify(""+n)+'][type="radio"]'),n=0;n<i.length;n++){var a=i[n];if(a!==e&&a.form===e.form){var u=ou(a);if(!u)throw Error(te(90));wg(a),Xf(a,u)}}}break;case"textarea":Sg(e,i);break;case"select":n=i.value,n!=null&&wl(e,!!i.multiple,n,!1)}};bg=Td;Mg=zi;var mS={usingClientEntryPoint:!1,Events:[as,dl,ou,Ng,Eg,Td]},No={findFiberByHostInstance:Li,bundleType:0,version:"18.1.0",rendererPackageName:"react-dom"},yS={bundleType:No.bundleType,version:No.version,rendererPackageName:No.rendererPackageName,rendererConfig:No.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Dr.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Pg(e),e===null?null:e.stateNode},findFiberByHostInstance:No.findFiberByHostInstance||vS,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.1.0-next-22edb9f77-20220426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var fa=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!fa.isDisabled&&fa.supportsFiber)try{nu=fa.inject(yS),or=fa}catch{}}xn.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=mS;xn.createPortal=function(e,n){var i=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Dd(n))throw Error(te(200));return pS(e,n,null,i)};xn.createRoot=function(e,n){if(!Dd(e))throw Error(te(299));var i=!1,a="",u=f0;return n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(a=n.identifierPrefix),n.onRecoverableError!==void 0&&(u=n.onRecoverableError)),n=bd(e,1,!1,null,null,i,!1,a,u),e[Mr]=n.current,Yo(e.nodeType===8?e.parentNode:e),new _d(n)};xn.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var n=e._reactInternals;if(n===void 0)throw typeof e.render=="function"?Error(te(188)):(e=Object.keys(e).join(","),Error(te(268,e)));return e=Pg(n),e=e===null?null:e.stateNode,e};xn.flushSync=function(e){return zi(e)};xn.hydrate=function(e,n,i){if(!vu(n))throw Error(te(200));return gu(null,e,n,!0,i)};xn.hydrateRoot=function(e,n,i){if(!Dd(e))throw Error(te(405));var a=i!=null&&i.hydratedSources||null,u=!1,c="",h=f0;if(i!=null&&(i.unstable_strictMode===!0&&(u=!0),i.identifierPrefix!==void 0&&(c=i.identifierPrefix),i.onRecoverableError!==void 0&&(h=i.onRecoverableError)),n=u0(n,null,e,1,i??null,u,!1,c,h),e[Mr]=n.current,Yo(e),a)for(e=0;e<a.length;e++)i=a[e],u=i._getVersion,u=u(i._source),n.mutableSourceEagerHydrationData==null?n.mutableSourceEagerHydrationData=[i,u]:n.mutableSourceEagerHydrationData.push(i,u);return new pu(n)};xn.render=function(e,n,i){if(!vu(n))throw Error(te(200));return gu(null,e,n,!1,i)};xn.unmountComponentAtNode=function(e){if(!vu(e))throw Error(te(40));return e._reactRootContainer?(zi(function(){gu(null,null,e,!1,function(){e._reactRootContainer=null,e[Mr]=null})}),!0):!1};xn.unstable_batchedUpdates=Td;xn.unstable_renderSubtreeIntoContainer=function(e,n,i,a){if(!vu(i))throw Error(te(200));if(e==null||e._reactInternals===void 0)throw Error(te(38));return gu(e,n,i,!1,a)};xn.version="18.1.0-next-22edb9f77-20220426";(function(e){function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(i){console.error(i)}}n(),e.exports=xn})(g1);function wS(){if(document.playwrightThemeInitialized)return;document.playwrightThemeInitialized=!0,document.defaultView.addEventListener("focus",i=>{i.target.document.nodeType===Node.DOCUMENT_NODE&&document.body.classList.remove("inactive")},!1),document.defaultView.addEventListener("blur",i=>{document.body.classList.add("inactive")},!1);const e=localStorage.getItem("theme"),n=window.matchMedia("(prefers-color-scheme: dark)");(e==="dark-mode"||n.matches)&&document.body.classList.add("dark-mode")}function xS(){const e=localStorage.getItem("theme");let n;e==="dark-mode"?n="light-mode":n="dark-mode",e&&document.body.classList.remove(e),document.body.classList.add(n),localStorage.setItem("theme",n)}const Wf=50,Ac=({sidebarSize:e,sidebarHidden:n=!1,sidebarIsFirst:i=!1,orientation:a="vertical",children:u})=>{const[c,h]=ye.useState(Math.max(Wf,e)),[v,m]=ye.useState(null),x=ye.Children.toArray(u);document.body.style.userSelect=v?"none":"inherit";let L={};return a==="vertical"?i?L={top:v?0:c-4,bottom:v?0:void 0,height:v?"initial":8}:L={bottom:v?0:c-4,top:v?0:void 0,height:v?"initial":8}:i?L={left:v?0:c-4,right:v?0:void 0,width:v?"initial":8}:L={right:v?0:c-4,left:v?0:void 0,width:v?"initial":8},ge("div",{className:"split-view "+a+(i?" sidebar-first":""),children:[H("div",{className:"split-view-main",children:x[0]}),!n&&H("div",{style:{flexBasis:c},className:"split-view-sidebar",children:x[1]}),!n&&H("div",{style:L,className:"split-view-resizer",onMouseDown:R=>m({offset:a==="vertical"?R.clientY:R.clientX,size:c}),onMouseUp:()=>m(null),onMouseMove:R=>{if(!R.buttons)m(null);else if(v){const W=(a==="vertical"?R.clientY:R.clientX)-v.offset,B=i?v.size+W:v.size-W,K=R.target.parentElement.getBoundingClientRect(),T=Math.min(Math.max(Wf,B),(a==="vertical"?K.height:K.width)-Wf);h(T)}}})]})};function Mi(e){if(!isFinite(e))return"-";if(e===0)return"0";if(e<1e3)return e.toFixed(0)+"ms";const n=e/1e3;if(n<60)return n.toFixed(1)+"s";const i=n/60;if(i<60)return i.toFixed(1)+"m";const a=i/60;return a<24?a.toFixed(1)+"h":(a/24).toFixed(1)+"d"}function c0(e,n,i,a,u){let c=a||0,h=u!==void 0?u:e.length;for(;c<h;){const v=c+h>>1;i(n,e[v])>=0?c=v+1:h=v}return h}const SS=({children:e,title:n="",icon:i="",disabled:a=!1,toggled:u=!1,onClick:c=()=>{}})=>{let h=`toolbar-button ${i}`;return u&&(h+=" toggled"),ge("button",{className:h,onClick:c,title:n,disabled:!!a,children:[H("span",{className:`codicon codicon-${i}`,style:e?{marginRight:5}:{}}),e]})};const kS=({items:e=[],itemKey:n,itemRender:i,itemIcon:a,itemIndent:u,selectedItem:c,onAccepted:h,onSelected:v,onHighlighted:m,showNoItemsMessage:x})=>{const L=ye.createRef(),[R,P]=ye.useState();return H("div",{className:"list-view vbox",children:ge("div",{className:"list-view-content",tabIndex:0,onDoubleClick:()=>h==null?void 0:h(c),onKeyDown:W=>{var T;if(W.key==="Enter"){h==null||h(c);return}if(W.key!=="ArrowDown"&&W.key!=="ArrowUp")return;W.stopPropagation(),W.preventDefault();const B=c?e.indexOf(c):-1;let Y=B;W.key==="ArrowDown"&&(B===-1?Y=0:Y=Math.min(B+1,e.length-1)),W.key==="ArrowUp"&&(B===-1?Y=e.length-1:Y=Math.max(B-1,0));const K=(T=L.current)==null?void 0:T.children.item(Y);d0(K),v==null||v(e[Y])},ref:L,children:[x&&e.length===0&&H("div",{className:"list-view-empty",children:"No items"}),e.map(W=>H(CS,{icon:a==null?void 0:a(W),indent:u==null?void 0:u(W),isHighlighted:W===R,isSelected:W===c,onSelected:()=>v==null?void 0:v(W),onMouseEnter:()=>{P(W),m==null||m(W)},onMouseLeave:()=>{P(void 0),m==null||m(void 0)},children:i(W)},n(W)))]})})},CS=({key:e,icon:n,indent:i,onSelected:a,onMouseEnter:u,onMouseLeave:c,isHighlighted:h,isSelected:v,children:m})=>{const x=v?" selected":"",L=h?" highlighted":"",R=ye.useRef(null);return ye.useEffect(()=>{R.current&&v&&d0(R.current)},[v]),ge("div",{className:"list-view-entry"+x+L,onClick:a,onMouseEnter:u,onMouseLeave:c,ref:R,children:[i?H("div",{style:{minWidth:i*16}}):void 0,H("div",{className:"codicon "+n,style:{minWidth:16,marginRight:4}}),typeof m=="string"?H("div",{style:{textOverflow:"ellipsis",overflow:"hidden"},children:m}):m]},e)};function d0(e){e&&(e!=null&&e.scrollIntoViewIfNeeded?e.scrollIntoViewIfNeeded(!1):e==null||e.scrollIntoView())}const ka=Symbol("context"),h0=Symbol("next"),$v=Symbol("events"),Kv=Symbol("resources");class p0{constructor(n){gn(this,"startTime");gn(this,"endTime");gn(this,"browserName");gn(this,"platform");gn(this,"wallTime");gn(this,"title");gn(this,"options");gn(this,"pages");gn(this,"actions");gn(this,"events");gn(this,"hasSource");gn(this,"sdkLanguage");var i,a,u,c,h;n.forEach(v=>TS(v)),this.browserName=((i=n[0])==null?void 0:i.browserName)||"",this.sdkLanguage=(a=n[0])==null?void 0:a.sdkLanguage,this.platform=((u=n[0])==null?void 0:u.platform)||"",this.title=((c=n[0])==null?void 0:c.title)||"",this.options=((h=n[0])==null?void 0:h.options)||{},this.wallTime=n.map(v=>v.wallTime).reduce((v,m)=>Math.min(v||Number.MAX_VALUE,m),Number.MAX_VALUE),this.startTime=n.map(v=>v.startTime).reduce((v,m)=>Math.min(v,m),Number.MAX_VALUE),this.endTime=n.map(v=>v.endTime).reduce((v,m)=>Math.max(v,m),Number.MIN_VALUE),this.pages=[].concat(...n.map(v=>v.pages)),this.actions=[].concat(...n.map(v=>v.actions)),this.events=[].concat(...n.map(v=>v.events)),this.hasSource=n.some(v=>v.hasSource),this.actions.sort((v,m)=>v.metadata.startTime-m.metadata.startTime),this.events.sort((v,m)=>v.metadata.startTime-m.metadata.startTime)}}function TS(e){for(const n of e.pages)n[ka]=e;for(let n=0;n<e.actions.length;++n){const i=e.actions[n];i[ka]=e,i[h0]=e.actions[n+1]}for(const n of e.events)n[ka]=e}function Dl(e){return e[ka]}function v0(e){return e[h0]}function g0(e){var u;let n=0,i=0;const a=Dl(e);for(const c of m0(e)){if(c.metadata.method==="console"){const{guid:h}=c.metadata.params.message,v=(u=a.objects[h])==null?void 0:u.type;v==="warning"?++i:v==="error"&&++n}c.metadata.method==="pageError"&&++n}return{errors:n,warnings:i}}function m0(e){let n=e[$v];if(n)return n;const i=v0(e);return n=Dl(e).events.filter(a=>a.metadata.startTime>=e.metadata.startTime&&(!i||a.metadata.startTime<i.metadata.startTime)),e[$v]=n,n}function y0(e){let n=e[Kv];if(n)return n;const i=v0(e);return n=Dl(e).resources.filter(a=>typeof a._monotonicTime=="number"&&a._monotonicTime>e.metadata.startTime&&(!i||a._monotonicTime<i.metadata.startTime)),e[Kv]=n,n}function mu(e,n="'"){const i=JSON.stringify(e),a=i.substring(1,i.length-1).replace(/\\"/g,'"');if(n==="'")return n+a.replace(/[']/g,"\\'")+n;if(n==='"')return n+a.replace(/["]/g,'\\"')+n;if(n==="`")return n+a.replace(/[`]/g,"`")+n;throw new Error("Invalid escape char")}function Ja(e){return e.charAt(0).toUpperCase()+e.substring(1)}function w0(e){return e.replace(/([a-z0-9])([A-Z])/g,"$1_$2").replace(/([A-Z])([A-Z][a-z])/g,"$1_$2").toLowerCase()}const pt=function(e,n,i){return e>=n&&e<=i};function ln(e){return pt(e,48,57)}function Gv(e){return ln(e)||pt(e,65,70)||pt(e,97,102)}function LS(e){return pt(e,65,90)}function NS(e){return pt(e,97,122)}function ES(e){return LS(e)||NS(e)}function bS(e){return e>=128}function Ca(e){return ES(e)||bS(e)||e===95}function qv(e){return Ca(e)||ln(e)||e===45}function MS(e){return pt(e,0,8)||e===11||pt(e,14,31)||e===127}function Ta(e){return e===10}function Sr(e){return Ta(e)||e===9||e===32}const _S=1114111;class Pd extends Error{constructor(n){super(n),this.name="InvalidCharacterError"}}function DS(e){const n=[];for(let i=0;i<e.length;i++){let a=e.charCodeAt(i);if(a===13&&e.charCodeAt(i+1)===10&&(a=10,i++),(a===13||a===12)&&(a=10),a===0&&(a=65533),pt(a,55296,56319)&&pt(e.charCodeAt(i+1),56320,57343)){const u=a-55296,c=e.charCodeAt(i+1)-56320;a=Math.pow(2,16)+u*Math.pow(2,10)+c,i++}n.push(a)}return n}function St(e){if(e<=65535)return String.fromCharCode(e);e-=Math.pow(2,16);const n=Math.floor(e/Math.pow(2,10))+55296,i=e%Math.pow(2,10)+56320;return String.fromCharCode(n)+String.fromCharCode(i)}function PS(e){const n=DS(e);let i=-1;const a=[];let u;const c=function(V){return V>=n.length?-1:n[V]},h=function(V){if(V===void 0&&(V=1),V>3)throw"Spec Error: no more than three codepoints of lookahead.";return c(i+V)},v=function(V){return V===void 0&&(V=1),i+=V,u=c(i),!0},m=function(){return i-=1,!0},x=function(V){return V===void 0&&(V=u),V===-1},L=function(){if(R(),v(),Sr(u)){for(;Sr(h());)v();return new Rc}else{if(u===34)return B();if(u===35)if(qv(h())||T(h(1),h(2))){const V=new O0("");return M(h(1),h(2),h(3))&&(V.type="id"),V.value=z(),V}else return new Bt(u);else return u===36?h()===61?(v(),new RS):new Bt(u):u===39?B():u===40?new OS:u===41?new M0:u===42?h()===61?(v(),new FS):new Bt(u):u===43?Q()?(m(),P()):new Bt(u):u===44?new L0:u===45?Q()?(m(),P()):h(1)===45&&h(2)===62?(v(2),new k0):A()?(m(),W()):new Bt(u):u===46?Q()?(m(),P()):new Bt(u):u===58?new C0:u===59?new T0:u===60?h(1)===33&&h(2)===45&&h(3)===45?(v(3),new S0):new Bt(u):u===64?M(h(1),h(2),h(3))?new P0(z()):new Bt(u):u===91?new b0:u===92?S()?(m(),W()):new Bt(u):u===93?new Fc:u===94?h()===61?(v(),new AS):new Bt(u):u===123?new N0:u===124?h()===61?(v(),new IS):h()===124?(v(),new _0):new Bt(u):u===125?new E0:u===126?h()===61?(v(),new zS):new Bt(u):ln(u)?(m(),P()):Ca(u)?(m(),W()):x()?new Na:new Bt(u)}},R=function(){for(;h(1)===47&&h(2)===42;)for(v(2);;)if(v(),u===42&&h()===47){v();break}else if(x())return},P=function(){const V=Z();if(M(h(1),h(2),h(3))){const F=new WS;return F.value=V.value,F.repr=V.repr,F.type=V.type,F.unit=z(),F}else if(h()===37){v();const F=new R0;return F.value=V.value,F.repr=V.repr,F}else{const F=new A0;return F.value=V.value,F.repr=V.repr,F.type=V.type,F}},W=function(){const V=z();if(V.toLowerCase()==="url"&&h()===40){for(v();Sr(h(1))&&Sr(h(2));)v();return h()===34||h()===39?new Ea(V):Sr(h())&&(h(2)===34||h(2)===39)?new Ea(V):Y()}else return h()===40?(v(),new Ea(V)):new D0(V)},B=function(V){V===void 0&&(V=u);let F="";for(;v();){if(u===V||x())return new z0(F);if(Ta(u))return m(),new x0;u===92?x(h())||(Ta(h())?v():F+=St(K())):F+=St(u)}throw new Error("Internal error")},Y=function(){const V=new I0("");for(;Sr(h());)v();if(x(h()))return V;for(;v();){if(u===41||x())return V;if(Sr(u)){for(;Sr(h());)v();return h()===41||x(h())?(v(),V):(we(),new La)}else{if(u===34||u===39||u===40||MS(u))return we(),new La;if(u===92)if(S())V.value+=St(K());else return we(),new La;else V.value+=St(u)}}throw new Error("Internal error")},K=function(){if(v(),Gv(u)){const V=[u];for(let le=0;le<5&&Gv(h());le++)v(),V.push(u);Sr(h())&&v();let F=parseInt(V.map(function(le){return String.fromCharCode(le)}).join(""),16);return F>_S&&(F=65533),F}else return x()?65533:u},T=function(V,F){return!(V!==92||Ta(F))},S=function(){return T(u,h())},M=function(V,F,le){return V===45?Ca(F)||F===45||T(F,le):Ca(V)?!0:V===92?T(V,F):!1},A=function(){return M(u,h(1),h(2))},G=function(V,F,le){return V===43||V===45?!!(ln(F)||F===46&&ln(le)):V===46?!!ln(F):!!ln(V)},Q=function(){return G(u,h(1),h(2))},z=function(){let V="";for(;v();)if(qv(u))V+=St(u);else if(S())V+=St(K());else return m(),V;throw new Error("Internal parse error")},Z=function(){let V="",F="integer";for((h()===43||h()===45)&&(v(),V+=St(u));ln(h());)v(),V+=St(u);if(h(1)===46&&ln(h(2)))for(v(),V+=St(u),v(),V+=St(u),F="number";ln(h());)v(),V+=St(u);const le=h(1),_=h(2),X=h(3);if((le===69||le===101)&&ln(_))for(v(),V+=St(u),v(),V+=St(u),F="number";ln(h());)v(),V+=St(u);else if((le===69||le===101)&&(_===43||_===45)&&ln(X))for(v(),V+=St(u),v(),V+=St(u),v(),V+=St(u),F="number";ln(h());)v(),V+=St(u);const ee=Ce(V);return{type:F,value:ee,repr:V}},Ce=function(V){return+V},we=function(){for(;v();){if(u===41||x())return;S()&&K()}};let ze=0;for(;!x(h());)if(a.push(L()),ze++,ze>n.length*2)throw new Error("I'm infinite-looping!");return a}class ft{constructor(){this.tokenType=""}toJSON(){return{token:this.tokenType}}toString(){return this.tokenType}toSource(){return""+this}}class x0 extends ft{constructor(){super(...arguments),this.tokenType="BADSTRING"}}class La extends ft{constructor(){super(...arguments),this.tokenType="BADURL"}}class Rc extends ft{constructor(){super(...arguments),this.tokenType="WHITESPACE"}toString(){return"WS"}toSource(){return" "}}class S0 extends ft{constructor(){super(...arguments),this.tokenType="CDO"}toSource(){return"<!--"}}class k0 extends ft{constructor(){super(...arguments),this.tokenType="CDC"}toSource(){return"-->"}}class C0 extends ft{constructor(){super(...arguments),this.tokenType=":"}}class T0 extends ft{constructor(){super(...arguments),this.tokenType=";"}}class L0 extends ft{constructor(){super(...arguments),this.tokenType=","}}class Al extends ft{constructor(){super(...arguments),this.value="",this.mirror=""}}class N0 extends Al{constructor(){super(),this.tokenType="{",this.value="{",this.mirror="}"}}class E0 extends Al{constructor(){super(),this.tokenType="}",this.value="}",this.mirror="{"}}class b0 extends Al{constructor(){super(),this.tokenType="[",this.value="[",this.mirror="]"}}class Fc extends Al{constructor(){super(),this.tokenType="]",this.value="]",this.mirror="["}}class OS extends Al{constructor(){super(),this.tokenType="(",this.value="(",this.mirror=")"}}class M0 extends Al{constructor(){super(),this.tokenType=")",this.value=")",this.mirror="("}}class zS extends ft{constructor(){super(...arguments),this.tokenType="~="}}class IS extends ft{constructor(){super(...arguments),this.tokenType="|="}}class AS extends ft{constructor(){super(...arguments),this.tokenType="^="}}class RS extends ft{constructor(){super(...arguments),this.tokenType="$="}}class FS extends ft{constructor(){super(...arguments),this.tokenType="*="}}class _0 extends ft{constructor(){super(...arguments),this.tokenType="||"}}class Na extends ft{constructor(){super(...arguments),this.tokenType="EOF"}toSource(){return""}}class Bt extends ft{constructor(n){super(),this.tokenType="DELIM",this.value="",this.value=St(n)}toString(){return"DELIM("+this.value+")"}toJSON(){const n=this.constructor.prototype.constructor.prototype.toJSON.call(this);return n.value=this.value,n}toSource(){return this.value==="\\"?`\\
`:this.value}}class Rl extends ft{constructor(){super(...arguments),this.value=""}ASCIIMatch(n){return this.value.toLowerCase()===n.toLowerCase()}toJSON(){const n=this.constructor.prototype.constructor.prototype.toJSON.call(this);return n.value=this.value,n}}class D0 extends Rl{constructor(n){super(),this.tokenType="IDENT",this.value=n}toString(){return"IDENT("+this.value+")"}toSource(){return fs(this.value)}}class Ea extends Rl{constructor(n){super(),this.tokenType="FUNCTION",this.value=n,this.mirror=")"}toString(){return"FUNCTION("+this.value+")"}toSource(){return fs(this.value)+"("}}class P0 extends Rl{constructor(n){super(),this.tokenType="AT-KEYWORD",this.value=n}toString(){return"AT("+this.value+")"}toSource(){return"@"+fs(this.value)}}class O0 extends Rl{constructor(n){super(),this.tokenType="HASH",this.value=n,this.type="unrestricted"}toString(){return"HASH("+this.value+")"}toJSON(){const n=this.constructor.prototype.constructor.prototype.toJSON.call(this);return n.value=this.value,n.type=this.type,n}toSource(){return this.type==="id"?"#"+fs(this.value):"#"+BS(this.value)}}class z0 extends Rl{constructor(n){super(),this.tokenType="STRING",this.value=n}toString(){return'"'+F0(this.value)+'"'}}class I0 extends Rl{constructor(n){super(),this.tokenType="URL",this.value=n}toString(){return"URL("+this.value+")"}toSource(){return'url("'+F0(this.value)+'")'}}class A0 extends ft{constructor(){super(),this.tokenType="NUMBER",this.type="integer",this.repr=""}toString(){return this.type==="integer"?"INT("+this.value+")":"NUMBER("+this.value+")"}toJSON(){const n=super.toJSON();return n.value=this.value,n.type=this.type,n.repr=this.repr,n}toSource(){return this.repr}}class R0 extends ft{constructor(){super(),this.tokenType="PERCENTAGE",this.repr=""}toString(){return"PERCENTAGE("+this.value+")"}toJSON(){const n=this.constructor.prototype.constructor.prototype.toJSON.call(this);return n.value=this.value,n.repr=this.repr,n}toSource(){return this.repr+"%"}}class WS extends ft{constructor(){super(),this.tokenType="DIMENSION",this.type="integer",this.repr="",this.unit=""}toString(){return"DIM("+this.value+","+this.unit+")"}toJSON(){const n=this.constructor.prototype.constructor.prototype.toJSON.call(this);return n.value=this.value,n.type=this.type,n.repr=this.repr,n.unit=this.unit,n}toSource(){const n=this.repr;let i=fs(this.unit);return i[0].toLowerCase()==="e"&&(i[1]==="-"||pt(i.charCodeAt(1),48,57))&&(i="\\65 "+i.slice(1,i.length)),n+i}}function fs(e){e=""+e;let n="";const i=e.charCodeAt(0);for(let a=0;a<e.length;a++){const u=e.charCodeAt(a);if(u===0)throw new Pd("Invalid character: the input contains U+0000.");pt(u,1,31)||u===127||a===0&&pt(u,48,57)||a===1&&pt(u,48,57)&&i===45?n+="\\"+u.toString(16)+" ":u>=128||u===45||u===95||pt(u,48,57)||pt(u,65,90)||pt(u,97,122)?n+=e[a]:n+="\\"+e[a]}return n}function BS(e){e=""+e;let n="";for(let i=0;i<e.length;i++){const a=e.charCodeAt(i);if(a===0)throw new Pd("Invalid character: the input contains U+0000.");a>=128||a===45||a===95||pt(a,48,57)||pt(a,65,90)||pt(a,97,122)?n+=e[i]:n+="\\"+a.toString(16)+" "}return n}function F0(e){e=""+e;let n="";for(let i=0;i<e.length;i++){const a=e.charCodeAt(i);if(a===0)throw new Pd("Invalid character: the input contains U+0000.");pt(a,1,31)||a===127?n+="\\"+a.toString(16)+" ":a===34||a===92?n+="\\"+e[i]:n+=e[i]}return n}class Ut extends Error{}function HS(e,n){let i;try{i=PS(e),i[i.length-1]instanceof Na||i.push(new Na)}catch(z){const Z=z.message+` while parsing selector "${e}"`,Ce=(z.stack||"").indexOf(z.message);throw Ce!==-1&&(z.stack=z.stack.substring(0,Ce)+Z+z.stack.substring(Ce+z.message.length)),z.message=Z,z}const a=i.find(z=>z instanceof P0||z instanceof x0||z instanceof La||z instanceof _0||z instanceof S0||z instanceof k0||z instanceof T0||z instanceof N0||z instanceof E0||z instanceof I0||z instanceof R0);if(a)throw new Ut(`Unsupported token "${a.toSource()}" while parsing selector "${e}"`);let u=0;const c=new Set;function h(){return new Ut(`Unexpected token "${i[u].toSource()}" while parsing selector "${e}"`)}function v(){for(;i[u]instanceof Rc;)u++}function m(z=u){return i[z]instanceof D0}function x(z=u){return i[z]instanceof z0}function L(z=u){return i[z]instanceof A0}function R(z=u){return i[z]instanceof L0}function P(z=u){return i[z]instanceof M0}function W(z=u){return i[z]instanceof Bt&&i[z].value==="*"}function B(z=u){return i[z]instanceof Na}function Y(z=u){return i[z]instanceof Bt&&[">","+","~"].includes(i[z].value)}function K(z=u){return R(z)||P(z)||B(z)||Y(z)||i[z]instanceof Rc}function T(){const z=[S()];for(;v(),!!R();)u++,z.push(S());return z}function S(){return v(),L()||x()?i[u++].value:M()}function M(){const z={simples:[]};for(v(),Y()?z.simples.push({selector:{functions:[{name:"scope",args:[]}]},combinator:""}):z.simples.push({selector:A(),combinator:""});;){if(v(),Y())z.simples[z.simples.length-1].combinator=i[u++].value,v();else if(K())break;z.simples.push({combinator:"",selector:A()})}return z}function A(){let z="";const Z=[];for(;!K();)if(m()||W())z+=i[u++].toSource();else if(i[u]instanceof O0)z+=i[u++].toSource();else if(i[u]instanceof Bt&&i[u].value===".")if(u++,m())z+="."+i[u++].toSource();else throw h();else if(i[u]instanceof C0)if(u++,m())if(!n.has(i[u].value.toLowerCase()))z+=":"+i[u++].toSource();else{const Ce=i[u++].value.toLowerCase();Z.push({name:Ce,args:[]}),c.add(Ce)}else if(i[u]instanceof Ea){const Ce=i[u++].value.toLowerCase();if(n.has(Ce)?(Z.push({name:Ce,args:T()}),c.add(Ce)):z+=`:${Ce}(${G()})`,v(),!P())throw h();u++}else throw h();else if(i[u]instanceof b0){for(z+="[",u++;!(i[u]instanceof Fc)&&!B();)z+=i[u++].toSource();if(!(i[u]instanceof Fc))throw h();z+="]",u++}else throw h();if(!z&&!Z.length)throw h();return{css:z||void 0,functions:Z}}function G(){let z="";for(;!P()&&!B();)z+=i[u++].toSource();return z}const Q=T();if(!B())throw new Ut(`Error while parsing selector "${e}"`);if(Q.some(z=>typeof z!="object"||!("simples"in z)))throw new Ut(`Error while parsing selector "${e}"`);return{selector:Q,names:Array.from(c)}}const Vv=new Set(["internal:has","left-of","right-of","above","below","near"]),US=new Set(["left-of","right-of","above","below","near"]),jS=new Set(["not","is","where","has","scope","light","visible","text","text-matches","text-is","has-text","above","below","right-of","left-of","near","nth-match"]);function W0(e){const n=KS(e),i=n.parts.map(a=>{if(a.name==="css"||a.name==="css:light")return a.name==="css:light"&&(a.body=":light("+a.body+")"),{name:"css",body:HS(a.body,jS).selector,source:a.body};if(Vv.has(a.name)){let u,c;try{const v=JSON.parse("["+a.body+"]");if(!Array.isArray(v)||v.length<1||v.length>2||typeof v[0]!="string")throw new Ut(`Malformed selector: ${a.name}=`+a.body);if(u=v[0],v.length===2){if(typeof v[1]!="number"||!US.has(a.name))throw new Ut(`Malformed selector: ${a.name}=`+a.body);c=v[1]}}catch{throw new Ut(`Malformed selector: ${a.name}=`+a.body)}const h={name:a.name,source:a.body,body:{parsed:W0(u),distance:c}};if(h.body.parsed.parts.some(v=>v.name==="internal:control"&&v.body==="enter-frame"))throw new Ut(`Frames are not allowed inside "${a.name}" selectors`);return h}return{...a,source:a.body}});if(Vv.has(i[0].name))throw new Ut(`"${i[0].name}" selector cannot be first`);return{capture:n.capture,parts:i}}function $S(e){return typeof e=="string"?e:e.parts.map((n,i)=>{const a=n.name==="css"?"":n.name+"=";return`${i===e.capture?"*":""}${a}${n.source}`}).join(" >> ")}function KS(e){let n=0,i,a=0;const u={parts:[]},c=()=>{const v=e.substring(a,n).trim(),m=v.indexOf("=");let x,L;m!==-1&&v.substring(0,m).trim().match(/^[a-zA-Z_0-9-+:*]+$/)?(x=v.substring(0,m).trim(),L=v.substring(m+1)):v.length>1&&v[0]==='"'&&v[v.length-1]==='"'||v.length>1&&v[0]==="'"&&v[v.length-1]==="'"?(x="text",L=v):/^\(*\/\//.test(v)||v.startsWith("..")?(x="xpath",L=v):(x="css",L=v);let R=!1;if(x[0]==="*"&&(R=!0,x=x.substring(1)),u.parts.push({name:x,body:L}),R){if(u.capture!==void 0)throw new Ut("Only one of the selectors can capture using * modifier");u.capture=u.parts.length-1}};if(!e.includes(">>"))return n=e.length,c(),u;const h=()=>{const m=e.substring(a,n).match(/^\s*text\s*=(.*)$/);return!!m&&!!m[1]};for(;n<e.length;){const v=e[n];v==="\\"&&n+1<e.length?n+=2:v===i?(i=void 0,n++):!i&&(v==='"'||v==="'"||v==="`")&&!h()?(i=v,n++):!i&&v===">"&&e[n+1]===">"?(c(),n+=2,a=n):n++}return c(),u}function Bf(e,n){let i=0,a=e.length===0;const u=()=>e[i]||"",c=()=>{const K=u();return++i,a=i>=e.length,K},h=K=>{throw a?new Ut(`Unexpected end of selector while parsing selector \`${e}\``):new Ut(`Error while parsing selector \`${e}\` - unexpected symbol "${u()}" at position ${i}`+(K?" during "+K:""))};function v(){for(;!a&&/\s/.test(u());)c()}function m(K){return K>=""||K>="0"&&K<="9"||K>="A"&&K<="Z"||K>="a"&&K<="z"||K>="0"&&K<="9"||K==="_"||K==="-"}function x(){let K="";for(v();!a&&m(u());)K+=c();return K}function L(K){let T=c();for(T!==K&&h("parsing quoted string");!a&&u()!==K;)u()==="\\"&&c(),T+=c();return u()!==K&&h("parsing quoted string"),T+=c(),T}function R(){c()!=="/"&&h("parsing regular expression");let K="",T=!1;for(;!a;){if(u()==="\\")K+=c(),a&&h("parsing regular expressiion");else if(T&&u()==="]")T=!1;else if(!T&&u()==="[")T=!0;else if(!T&&u()==="/")break;K+=c()}c()!=="/"&&h("parsing regular expression");let S="";for(;!a&&u().match(/[dgimsuy]/);)S+=c();try{return new RegExp(K,S)}catch(M){throw new Ut(`Error while parsing selector \`${e}\`: ${M.message}`)}}function P(){let K="";return v(),u()==="'"||u()==='"'?K=L(u()).slice(1,-1):K=x(),K||h("parsing property path"),K}function W(){v();let K="";return a||(K+=c()),!a&&K!=="="&&(K+=c()),["=","*=","^=","$=","|=","~="].includes(K)||h("parsing operator"),K}function B(){c();const K=[];for(K.push(P()),v();u()===".";)c(),K.push(P()),v();if(u()==="]")return c(),{name:K.join("."),jsonPath:K,op:"<truthy>",value:null,caseSensitive:!1};const T=W();let S,M=!0;if(v(),u()==="/"){if(T!=="=")throw new Ut(`Error while parsing selector \`${e}\` - cannot use ${T} in attribute with regular expression`);S=R()}else if(u()==="'"||u()==='"')S=L(u()).slice(1,-1),v(),u()==="i"||u()==="I"?(M=!1,c()):(u()==="s"||u()==="S")&&(M=!0,c());else{for(S="";!a&&(m(u())||u()==="+"||u()===".");)S+=c();S==="true"?S=!0:S==="false"?S=!1:n||(S=+S,Number.isNaN(S)&&h("parsing attribute value"))}if(v(),u()!=="]"&&h("parsing attribute value"),c(),T!=="="&&typeof S!="string")throw new Ut(`Error while parsing selector \`${e}\` - cannot use ${T} in attribute with non-string matching value - ${S}`);return{name:K.join("."),jsonPath:K,op:T,value:S,caseSensitive:M}}const Y={name:"",attributes:[]};for(Y.name=x(),v();u()==="[";)Y.attributes.push(B()),v();if(a||h(void 0),!Y.name&&!Y.attributes.length)throw new Ut(`Error while parsing selector \`${e}\` - selector cannot be empty`);return Y}function B0(e,n,i=!1){return H0(YS[e],W0(n),i)}function H0(e,n,i=!1){const a=[...n.parts];for(let h=0;h<a.length-1;h++)if(a[h].name==="nth"&&a[h+1].name==="internal:control"&&a[h+1].body==="enter-frame"){const[v]=a.splice(h,1);a.splice(h+1,0,v)}const u=[];let c=i?"frame-locator":"page";for(let h=0;h<a.length;h++){const v=a[h],m=c;if(c="locator",v.name==="nth"){v.body==="0"?u.push(e.generateLocator(m,"first","")):v.body==="-1"?u.push(e.generateLocator(m,"last","")):u.push(e.generateLocator(m,"nth",v.body));continue}if(v.name==="internal:text"){const{exact:P,text:W}=Hf(v.body);u.push(e.generateLocator(m,"text",W,{exact:P}));continue}if(v.name==="internal:has-text"){const{exact:P,text:W}=Hf(v.body);u.push(e.generateLocator(m,"has-text",W,{exact:P}));continue}if(v.name==="internal:has"){const P=H0(e,v.body.parsed);u.push(e.generateLocator(m,"has",P));continue}if(v.name==="internal:label"){const{exact:P,text:W}=Hf(v.body);u.push(e.generateLocator(m,"label",W,{exact:P}));continue}if(v.name==="internal:role"){const P=Bf(v.body,!0),W={attrs:[]};for(const B of P.attributes)B.name==="name"?(W.exact=B.caseSensitive,W.name=B.value):(B.name==="level"&&typeof B.value=="string"&&(B.value=+B.value),W.attrs.push({name:B.name==="include-hidden"?"includeHidden":B.name,value:B.value}));u.push(e.generateLocator(m,"role",P.name,W));continue}if(v.name==="internal:testid"){const P=Bf(v.body,!0),{value:W}=P.attributes[0];u.push(e.generateLocator(m,"test-id",W));continue}if(v.name==="internal:attr"){const P=Bf(v.body,!0),{name:W,value:B,caseSensitive:Y}=P.attributes[0],K=B,T=!!Y;if(W==="placeholder"){u.push(e.generateLocator(m,"placeholder",K,{exact:T}));continue}if(W==="alt"){u.push(e.generateLocator(m,"alt",K,{exact:T}));continue}if(W==="title"){u.push(e.generateLocator(m,"title",K,{exact:T}));continue}}let x="default";const L=a[h+1];L&&L.name==="internal:control"&&L.body==="enter-frame"&&(x="frame",c="frame-locator",h++);const R={parts:[v]};u.push(e.generateLocator(m,x,$S(R)))}return u.join(".")}function Hf(e){let n=!1;const i=e.match(/^\/(.*)\/([igm]*)$/);return i?{text:new RegExp(i[1],i[2])}:(e.endsWith('"')?(e=JSON.parse(e),n=!0):e.endsWith('"s')?(e=JSON.parse(e.substring(0,e.length-1)),n=!0):e.endsWith('"i')&&(e=JSON.parse(e.substring(0,e.length-1)),n=!1),{exact:n,text:e})}class GS{generateLocator(n,i,a,u={}){switch(i){case"default":return`locator(${this.quote(a)})`;case"frame":return`frameLocator(${this.quote(a)})`;case"nth":return`nth(${a})`;case"first":return"first()";case"last":return"last()";case"role":const c=[];On(u.name)?c.push(`name: ${u.name}`):typeof u.name=="string"&&(c.push(`name: ${this.quote(u.name)}`),u.exact&&c.push("exact: true"));for(const{name:v,value:m}of u.attrs)c.push(`${v}: ${typeof m=="string"?this.quote(m):m}`);const h=c.length?`, { ${c.join(", ")} }`:"";return`getByRole(${this.quote(a)}${h})`;case"has-text":return`filter({ hasText: ${this.toHasText(a)} })`;case"has":return`filter({ has: ${a} })`;case"test-id":return`getByTestId(${this.quote(a)})`;case"text":return this.toCallWithExact("getByText",a,!!u.exact);case"alt":return this.toCallWithExact("getByAltText",a,!!u.exact);case"placeholder":return this.toCallWithExact("getByPlaceholder",a,!!u.exact);case"label":return this.toCallWithExact("getByLabel",a,!!u.exact);case"title":return this.toCallWithExact("getByTitle",a,!!u.exact);default:throw new Error("Unknown selector kind "+i)}}toCallWithExact(n,i,a){return On(i)?`${n}(${i})`:a?`${n}(${this.quote(i)}, { exact: true })`:`${n}(${this.quote(i)})`}toHasText(n){return On(n)?String(n):this.quote(n)}quote(n){return mu(n,"'")}}class qS{generateLocator(n,i,a,u={}){switch(i){case"default":return`locator(${this.quote(a)})`;case"frame":return`frame_locator(${this.quote(a)})`;case"nth":return`nth(${a})`;case"first":return"first";case"last":return"last";case"role":const c=[];On(u.name)?c.push(`name=${this.regexToString(u.name)}`):typeof u.name=="string"&&(c.push(`name=${this.quote(u.name)}`),u.exact&&c.push("exact=True"));for(const{name:v,value:m}of u.attrs){let x=typeof m=="string"?this.quote(m):m;typeof m=="boolean"&&(x=m?"True":"False"),c.push(`${w0(v)}=${x}`)}const h=c.length?`, ${c.join(", ")}`:"";return`get_by_role(${this.quote(a)}${h})`;case"has-text":return`filter(has_text=${this.toHasText(a)})`;case"has":return`filter(has=${a})`;case"test-id":return`get_by_test_id(${this.quote(a)})`;case"text":return this.toCallWithExact("get_by_text",a,!!u.exact);case"alt":return this.toCallWithExact("get_by_alt_text",a,!!u.exact);case"placeholder":return this.toCallWithExact("get_by_placeholder",a,!!u.exact);case"label":return this.toCallWithExact("get_by_label",a,!!u.exact);case"title":return this.toCallWithExact("get_by_title",a,!!u.exact);default:throw new Error("Unknown selector kind "+i)}}regexToString(n){const i=n.flags.includes("i")?", re.IGNORECASE":"";return`re.compile(r"${n.source.replace(/\\\//,"/").replace(/"/g,'\\"')}"${i})`}toCallWithExact(n,i,a){return On(i)?`${n}(${this.regexToString(i)})`:a?`${n}(${this.quote(i)}, exact=True)`:`${n}(${this.quote(i)})`}toHasText(n){return On(n)?this.regexToString(n):`${this.quote(n)}`}quote(n){return mu(n,'"')}}class VS{generateLocator(n,i,a,u={}){let c;switch(n){case"page":c="Page";break;case"frame-locator":c="FrameLocator";break;case"locator":c="Locator";break}switch(i){case"default":return`locator(${this.quote(a)})`;case"frame":return`frameLocator(${this.quote(a)})`;case"nth":return`nth(${a})`;case"first":return"first()";case"last":return"last()";case"role":const h=[];On(u.name)?h.push(`.setName(${this.regexToString(u.name)})`):typeof u.name=="string"&&(h.push(`.setName(${this.quote(u.name)})`),u.exact&&h.push(".setExact(true)"));for(const{name:m,value:x}of u.attrs)h.push(`.set${Ja(m)}(${typeof x=="string"?this.quote(x):x})`);const v=h.length?`, new ${c}.GetByRoleOptions()${h.join("")}`:"";return`getByRole(AriaRole.${w0(a).toUpperCase()}${v})`;case"has-text":return`filter(new ${c}.FilterOptions().setHasText(${this.toHasText(a)}))`;case"has":return`filter(new ${c}.FilterOptions().setHas(${a}))`;case"test-id":return`getByTestId(${this.quote(a)})`;case"text":return this.toCallWithExact(c,"getByText",a,!!u.exact);case"alt":return this.toCallWithExact(c,"getByAltText",a,!!u.exact);case"placeholder":return this.toCallWithExact(c,"getByPlaceholder",a,!!u.exact);case"label":return this.toCallWithExact(c,"getByLabel",a,!!u.exact);case"title":return this.toCallWithExact(c,"getByTitle",a,!!u.exact);default:throw new Error("Unknown selector kind "+i)}}regexToString(n){const i=n.flags.includes("i")?", Pattern.CASE_INSENSITIVE":"";return`Pattern.compile(${this.quote(n.source)}${i})`}toCallWithExact(n,i,a,u){return On(a)?`${i}(${this.regexToString(a)})`:u?`${i}(${this.quote(a)}, new ${n}.${Ja(i)}Options().setExact(true))`:`${i}(${this.quote(a)})`}toHasText(n){return On(n)?this.regexToString(n):this.quote(n)}quote(n){return mu(n,'"')}}class QS{generateLocator(n,i,a,u={}){switch(i){case"default":return`Locator(${this.quote(a)})`;case"frame":return`FrameLocator(${this.quote(a)})`;case"nth":return`Nth(${a})`;case"first":return"First";case"last":return"Last";case"role":const c=[];On(u.name)?c.push(`NameRegex = ${this.regexToString(u.name)}`):typeof u.name=="string"&&(c.push(`Name = ${this.quote(u.name)}`),u.exact&&c.push("Exact = true"));for(const{name:v,value:m}of u.attrs)c.push(`${Ja(v)} = ${typeof m=="string"?this.quote(m):m}`);const h=c.length?`, new() { ${c.join(", ")} }`:"";return`GetByRole(AriaRole.${Ja(a)}${h})`;case"has-text":return`Filter(new() { ${this.toHasText(a)} })`;case"has":return`Filter(new() { Has = ${a} })`;case"test-id":return`GetByTestId(${this.quote(a)})`;case"text":return this.toCallWithExact("GetByText",a,!!u.exact);case"alt":return this.toCallWithExact("GetByAltText",a,!!u.exact);case"placeholder":return this.toCallWithExact("GetByPlaceholder",a,!!u.exact);case"label":return this.toCallWithExact("GetByLabel",a,!!u.exact);case"title":return this.toCallWithExact("GetByTitle",a,!!u.exact);default:throw new Error("Unknown selector kind "+i)}}regexToString(n){const i=n.flags.includes("i")?", RegexOptions.IgnoreCase":"";return`new Regex(${this.quote(n.source)}${i})`}toCallWithExact(n,i,a){return On(i)?`${n}(${this.regexToString(i)})`:a?`${n}(${this.quote(i)}, new() { Exact = true })`:`${n}(${this.quote(i)})`}toHasText(n){return On(n)?`HasTextRegex = ${this.regexToString(n)}`:`HasText = ${this.quote(n)}`}quote(n){return mu(n,'"')}}const YS={javascript:new GS,python:new qS,java:new VS,csharp:new QS};function On(e){return e instanceof RegExp}const XS=({actions:e=[],selectedAction:n,sdkLanguage:i,onSelected:a=()=>{},onHighlighted:u=()=>{},setSelectedTab:c=()=>{}})=>H(kS,{items:e,selectedItem:n,onSelected:h=>a(h),onHighlighted:h=>u(h),itemKey:h=>h.metadata.id,itemRender:h=>ZS(h,i,c),showNoItemsMessage:!0}),ZS=(e,n,i)=>{var m,x;const{metadata:a}=e,u=(x=(m=a.error)==null?void 0:m.error)==null?void 0:x.message,{errors:c,warnings:h}=g0(e),v=a.params.selector?B0(n||"javascript",a.params.selector):void 0;return ge(cg,{children:[ge("div",{className:"action-title",children:[H("span",{children:a.apiName}),v&&H("div",{className:"action-selector",title:v,children:v}),a.method==="goto"&&a.params.url&&H("div",{className:"action-url",title:a.params.url,children:a.params.url})]}),H("div",{className:"action-duration",style:{flex:"none"},children:a.endTime?Mi(a.endTime-a.startTime):"Timed Out"}),ge("div",{className:"action-icons",onClick:()=>i("console"),children:[!!c&&ge("div",{className:"action-icon",children:[H("span",{className:"codicon codicon-error"}),H("span",{className:"action-icon-value",children:c})]}),!!h&&ge("div",{className:"action-icon",children:[H("span",{className:"codicon codicon-warning"}),H("span",{className:"action-icon-value",children:h})]})]}),u&&H("div",{className:"codicon codicon-issues",title:u})]})};const JS=({value:e})=>{const[n,i]=ye.useState("codicon-clippy"),a=ye.useCallback(()=>{navigator.clipboard.writeText(e).then(()=>{i("codicon-check"),setTimeout(()=>{i("codicon-clippy")},3e3)},()=>{i("codicon-close")})},[e]);return H("span",{className:`copy-icon codicon ${n}`,onClick:a})},ek=({action:e,sdkLanguage:n})=>{var m,x;if(!e)return null;const i=e.metadata.log,a=(x=(m=e.metadata.error)==null?void 0:m.error)==null?void 0:x.message,u={...e.metadata.params};delete u.info;const c=Object.keys(u),h=new Date(e.metadata.wallTime).toLocaleString(),v=e.metadata.endTime?Mi(e.metadata.endTime-e.metadata.startTime):"Timed Out";return ge("div",{className:"call-tab",children:[ge("div",{className:"call-error",hidden:!a,children:[H("div",{className:"codicon codicon-issues"}),a]},"error"),H("div",{className:"call-line",children:e.metadata.apiName}),ge(cg,{children:[H("div",{className:"call-section",children:"Time"}),e.metadata.wallTime&&ge("div",{className:"call-line",children:["wall time:",H("span",{className:"call-value datetime",title:h,children:h})]}),ge("div",{className:"call-line",children:["duration:",H("span",{className:"call-value datetime",title:v,children:v})]})]}),!!c.length&&H("div",{className:"call-section",children:"Parameters"}),!!c.length&&c.map((L,R)=>Qv(Yv(e.metadata,L,u[L],n),"param-"+R)),!!e.metadata.result&&H("div",{className:"call-section",children:"Return value"}),!!e.metadata.result&&Object.keys(e.metadata.result).map((L,R)=>Qv(Yv(e.metadata,L,e.metadata.result[L],n),"result-"+R)),H("div",{className:"call-section",children:"Log"}),i.map((L,R)=>H("div",{className:"call-line",children:L},R))]})};function Qv(e,n){let i=e.text.replace(/\n/g,"↵");return e.type==="string"&&(i=`"${i}"`),ge("div",{className:"call-line",children:[e.name,":",H("span",{className:`call-value ${e.type}`,title:e.text,children:i}),["string","number","object","locator"].includes(e.type)&&H(JS,{value:e.text})]},n)}function Yv(e,n,i,a){const u=e.method.includes("eval")||e.method==="waitForFunction";if((n==="eventInit"||n==="expectedValue"||n==="arg"&&u)&&(i=eu(i.value,new Array(10).fill({handle:"<handle>"}))),(n==="value"&&u||n==="received"&&e.method==="expect")&&(i=eu(i,new Array(10).fill({handle:"<handle>"}))),n==="selector")return{text:B0(a||"javascript",e.params.selector),type:"locator",name:"locator"};const c=typeof i;return c!=="object"||i===null?{text:String(i),type:c,name:n}:i.guid?{text:"<handle>",type:"handle",name:n}:{text:JSON.stringify(i),type:"object",name:n}}function eu(e,n){if(e.n!==void 0)return e.n;if(e.s!==void 0)return e.s;if(e.b!==void 0)return e.b;if(e.v!==void 0){if(e.v==="undefined")return;if(e.v==="null")return null;if(e.v==="NaN")return NaN;if(e.v==="Infinity")return 1/0;if(e.v==="-Infinity")return-1/0;if(e.v==="-0")return-0}if(e.d!==void 0)return new Date(e.d);if(e.r!==void 0)return new RegExp(e.r.p,e.r.f);if(e.a!==void 0)return e.a.map(i=>eu(i,n));if(e.o!==void 0){const i={};for(const{k:a,v:u}of e.o)i[a]=eu(u,n);return i}return e.h!==void 0?n===void 0?"<object>":n[e.h]:"<object>"}const tk=({action:e})=>{const n=ye.useMemo(()=>{if(!e)return[];const i=[],a=Dl(e);for(const u of m0(e))if(!(u.metadata.method!=="console"&&u.metadata.method!=="pageError")){if(u.metadata.method==="console"){const{guid:c}=u.metadata.params.message;i.push({message:a.objects[c]})}u.metadata.method==="pageError"&&i.push({error:u.metadata.params.error})}return i},[e]);return H("div",{className:"console-tab",children:n.map((i,a)=>{const{message:u,error:c}=i;if(u){const h=u.location.url,v=h?h.substring(h.lastIndexOf("/")+1):"<anonymous>";return ge("div",{className:"console-line "+u.type,children:[ge("span",{className:"console-location",children:[v,":",u.location.lineNumber]}),H("span",{className:"codicon codicon-"+nk(u)}),H("span",{className:"console-line-message",children:u.text})]},a)}if(c){const{error:h,value:v}=c;return h?ge("div",{className:"console-line error",children:[H("span",{className:"codicon codicon-error"}),H("span",{className:"console-line-message",children:h.message}),H("div",{className:"console-stack",children:h.stack})]},a):ge("div",{className:"console-line error",children:[H("span",{className:"codicon codicon-error"}),H("span",{className:"console-line-message",children:String(v)})]},a)}return null})})};function nk(e){switch(e.type){case"error":return"error";case"warning":return"warning"}return"blank"}const rk=({title:e,children:n,setExpanded:i,expanded:a,style:u})=>ge("div",{style:{...u,display:"flex",flexDirection:"column"},children:[ge("div",{style:{display:"flex",flexDirection:"row",alignItems:"center",whiteSpace:"nowrap"},children:[H("div",{className:"codicon codicon-"+(a?"chevron-down":"chevron-right"),style:{cursor:"pointer",color:"var(--vscode-foreground)",marginRight:"4px"},onClick:()=>i(!a)}),e]}),a&&H("div",{style:{display:"flex",flex:"auto",margin:"5px 0 5px 20px"},children:n})]});const ik=({resource:e,index:n,selected:i,setSelected:a})=>{const[u,c]=ye.useState(!1),[h,v]=ye.useState(null),[m,x]=ye.useState(null);ye.useEffect(()=>{c(!1),a(-1)},[e,a]),ye.useEffect(()=>{(async()=>{if(e.request.postData)if(e.request.postData._sha1){const A=await(await fetch(`sha1/${e.request.postData._sha1}`)).text();v(A)}else v(e.request.postData.text);if(e.response.content._sha1){const M=e.response.content.mimeType.includes("image"),A=await fetch(`sha1/${e.response.content._sha1}`);if(M){const G=await A.blob(),Q=new FileReader,z=new Promise(Z=>Q.onload=Z);Q.readAsDataURL(G),x({dataUrl:(await z).target.result})}else x({text:await A.text()})}})()},[u,e]);function L(S,M){if(S===null)return"Loading...";const A=S;if(A==="")return"<Empty>";if(M.includes("application/json"))try{return JSON.stringify(JSON.parse(A),null,2)}catch{return A}return M.includes("application/x-www-form-urlencoded")?decodeURIComponent(A):A}function R(S){return S>=200&&S<400?"status-success":S>=400?"status-failure":"status-neutral"}const P=e.request.headers.find(S=>S.name==="Content-Type"),W=P?P.value:"",B=e.request.url.substring(e.request.url.lastIndexOf("/")+1);let Y=e.response.content.mimeType;const K=Y.match(/^(.*);\s*charset=.*$/);K&&(Y=K[1]);const T=()=>e.response._failureText?ge("div",{className:"network-request-title",children:[H("div",{className:"network-request-title-status status-failure",children:e.response._failureText}),H("div",{className:"network-request-title-method",children:e.request.method}),H("div",{className:"network-request-title-url",children:e.request.url})]}):ge("div",{className:"network-request-title",children:[H("div",{className:"network-request-title-status "+R(e.response.status),children:e.response.status}),H("div",{className:"network-request-title-method",children:e.request.method}),H("div",{className:"network-request-title-url",children:B}),H("div",{className:"network-request-title-content-type",children:Y})]});return H("div",{className:"network-request "+(i?"selected":""),onClick:()=>a(n),children:H(rk,{expanded:u,setExpanded:c,style:{width:"100%"},title:T(),children:ge("div",{className:"network-request-details",children:[ge("div",{className:"network-request-details-time",children:[e.time,"ms"]}),H("div",{className:"network-request-details-header",children:"URL"}),H("div",{className:"network-request-details-url",children:e.request.url}),H("div",{className:"network-request-details-header",children:"Request Headers"}),H("div",{className:"network-request-headers",children:e.request.headers.map(S=>`${S.name}: ${S.value}`).join(`
`)}),H("div",{className:"network-request-details-header",children:"Response Headers"}),H("div",{className:"network-request-headers",children:e.response.headers.map(S=>`${S.name}: ${S.value}`).join(`
`)}),e.request.postData?H("div",{className:"network-request-details-header",children:"Request Body"}):"",e.request.postData?H("div",{className:"network-request-body",children:L(h,W)}):"",H("div",{className:"network-request-details-header",children:"Response Body"}),e.response.content._sha1?"":H("div",{className:"network-request-response-body",children:"Response body is not available for this request."}),m!==null&&m.dataUrl?H("img",{src:m.dataUrl}):"",m!==null&&m.text?H("div",{className:"network-request-response-body",children:L(m.text,e.response.content.mimeType)}):""]})})})};const lk=({action:e})=>{const[n,i]=ye.useState(0),a=e?y0(e):[];return H("div",{className:"network-tab",children:a.map((u,c)=>H(ik,{resource:u,index:c,selected:n===c,setSelected:i},c))})};function ok(e,n,i,a){const[u,c]=ye.useState(i);return ye.useEffect(()=>{let h=!1;return a!==void 0&&c(a),e().then(v=>{h||c(v)}),()=>{h=!0}},n),u}function Od(){const e=ye.useRef(null),[n,i]=ye.useState(new DOMRect(0,0,10,10));return ye.useLayoutEffect(()=>{const a=e.current;if(!a)return;const u=new ResizeObserver(c=>{const h=c[c.length-1];h&&h.contentRect&&i(h.contentRect)});return u.observe(a),()=>u.unobserve(a)},[e]),[n,e]}const sk=({action:e})=>{var A,G;const[n,i]=Od(),[a,u]=ye.useState(0),c=new Map;for(const Q of(e==null?void 0:e.metadata.snapshots)||[])c.set(Q.title,Q);const h=c.get("action")||c.get("after"),v=[h?{...h,title:"action"}:void 0,c.get("before"),c.get("after")].filter(Boolean);let m='data:text/html,<body style="background: #ddd"></body>',x,L,R,P;if(e){const Q=v[a];if(Q&&Q.snapshotName){const z=new URLSearchParams;z.set("trace",Dl(e).traceUrl),z.set("name",Q.snapshotName),m=new URL(`snapshot/${e.metadata.pageId}?${z.toString()}`,window.location.href).toString(),L=new URL(`snapshotInfo/${e.metadata.pageId}?${z.toString()}`,window.location.href).toString(),Q.snapshotName.includes("action")&&(R=(A=e.metadata.point)==null?void 0:A.x,P=(G=e.metadata.point)==null?void 0:G.y);const Z=new URLSearchParams;Z.set("r",m),Z.set("trace",Dl(e).traceUrl),x=new URL(`popout.html?${Z.toString()}`,window.location.href).toString()}}ye.useEffect(()=>{v.length>=1&&a>=v.length&&u(v.length-1)},[a,v]);const W=ye.useRef(null),[B,Y]=ye.useState({viewport:Xv,url:""});ye.useEffect(()=>{(async()=>{if(L){const z=await(await fetch(L)).json();z.error||Y(z)}else Y({viewport:Xv,url:""});if(W.current)try{W.current.src=m+(R===void 0?"":`&pointX=${R}&pointY=${P}`)}catch{}})()},[W,m,L,R,P]);const K=40,T={width:B.viewport.width,height:B.viewport.height+K},S=Math.min(n.width/T.width,n.height/T.height,1),M={x:(n.width-T.width)/2,y:(n.height-T.height)/2};return ge("div",{className:"snapshot-tab",tabIndex:0,onKeyDown:Q=>{Q.key==="ArrowRight"&&u(Math.min(a+1,v.length-1)),Q.key==="ArrowLeft"&&u(Math.max(a-1,0))},children:[H("div",{className:"tab-strip",children:v.map((Q,z)=>H("div",{className:"tab-element "+(a===z?" selected":""),onClick:()=>u(z),children:H("div",{className:"tab-label",children:ak(Q.title)})},Q.title))}),ge("div",{ref:i,className:"snapshot-wrapper",children:[H("a",{className:`popout-icon ${x?"":"popout-disabled"}`,href:x,target:"_blank",title:"Open snapshot in a new tab",children:H("span",{className:"codicon codicon-link-external"})}),v.length?ge("div",{className:"snapshot-container",style:{width:T.width+"px",height:T.height+"px",transform:`translate(${M.x}px, ${M.y}px) scale(${S})`},children:[ge("div",{className:"window-header",children:[ge("div",{style:{whiteSpace:"nowrap"},children:[H("span",{className:"window-dot",style:{backgroundColor:"rgb(242, 95, 88)"}}),H("span",{className:"window-dot",style:{backgroundColor:"rgb(251, 190, 60)"}}),H("span",{className:"window-dot",style:{backgroundColor:"rgb(88, 203, 66)"}})]}),H("div",{className:"window-address-bar",title:B.url,children:B.url}),H("div",{style:{marginLeft:"auto"},children:ge("div",{children:[H("span",{className:"window-menu-bar"}),H("span",{className:"window-menu-bar"}),H("span",{className:"window-menu-bar"})]})})]}),H("iframe",{ref:W,id:"snapshot",name:"snapshot"})]}):H("div",{className:"no-snapshot",children:"Action does not have snapshots"})]})]})};function ak(e){return e==="before"?"Before":e==="after"?"After":e==="action"?"Action":e}const Xv={width:1280,height:720};var Pl={},uk={get exports(){return Pl},set exports(e){Pl=e}};(function(e,n){(function(i,a){e.exports=a()})(Gw,function(){var i=navigator.userAgent,a=navigator.platform,u=/gecko\/\d/i.test(i),c=/MSIE \d/.test(i),h=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(i),v=/Edge\/(\d+)/.exec(i),m=c||h||v,x=m&&(c?document.documentMode||6:+(v||h)[1]),L=!v&&/WebKit\//.test(i),R=L&&/Qt\/\d+\.\d+/.test(i),P=!v&&/Chrome\/(\d+)/.exec(i),W=P&&+P[1],B=/Opera\//.test(i),Y=/Apple Computer/.test(navigator.vendor),K=/Mac OS X 1\d\D([8-9]|\d\d)\D/.test(i),T=/PhantomJS/.test(i),S=Y&&(/Mobile\/\w+/.test(i)||navigator.maxTouchPoints>2),M=/Android/.test(i),A=S||M||/webOS|BlackBerry|Opera Mini|Opera Mobi|IEMobile/i.test(i),G=S||/Mac/.test(a),Q=/\bCrOS\b/.test(i),z=/win/i.test(a),Z=B&&i.match(/Version\/(\d*\.\d*)/);Z&&(Z=Number(Z[1])),Z&&Z>=15&&(B=!1,L=!0);var Ce=G&&(R||B&&(Z==null||Z<12.11)),we=u||m&&x>=9;function ze(t){return new RegExp("(^|\\s)"+t+"(?:$|\\s)\\s*")}var V=function(t,r){var o=t.className,l=ze(r).exec(o);if(l){var s=o.slice(l.index+l[0].length);t.className=o.slice(0,l.index)+(s?l[1]+s:"")}};function F(t){for(var r=t.childNodes.length;r>0;--r)t.removeChild(t.firstChild);return t}function le(t,r){return F(t).appendChild(r)}function _(t,r,o,l){var s=document.createElement(t);if(o&&(s.className=o),l&&(s.style.cssText=l),typeof r=="string")s.appendChild(document.createTextNode(r));else if(r)for(var f=0;f<r.length;++f)s.appendChild(r[f]);return s}function X(t,r,o,l){var s=_(t,r,o,l);return s.setAttribute("role","presentation"),s}var ee;document.createRange?ee=function(t,r,o,l){var s=document.createRange();return s.setEnd(l||t,o),s.setStart(t,r),s}:ee=function(t,r,o){var l=document.body.createTextRange();try{l.moveToElementText(t.parentNode)}catch{return l}return l.collapse(!0),l.moveEnd("character",o),l.moveStart("character",r),l};function C(t,r){if(r.nodeType==3&&(r=r.parentNode),t.contains)return t.contains(r);do if(r.nodeType==11&&(r=r.host),r==t)return!0;while(r=r.parentNode)}function b(t){var r;try{r=t.activeElement}catch{r=t.body||null}for(;r&&r.shadowRoot&&r.shadowRoot.activeElement;)r=r.shadowRoot.activeElement;return r}function U(t,r){var o=t.className;ze(r).test(o)||(t.className+=(o?" ":"")+r)}function ne(t,r){for(var o=t.split(" "),l=0;l<o.length;l++)o[l]&&!ze(o[l]).test(r)&&(r+=" "+o[l]);return r}var ue=function(t){t.select()};S?ue=function(t){t.selectionStart=0,t.selectionEnd=t.value.length}:m&&(ue=function(t){try{t.select()}catch{}});function Se(t){return t.display.wrapper.ownerDocument}function je(t){return Se(t).defaultView}function Ye(t){var r=Array.prototype.slice.call(arguments,1);return function(){return t.apply(null,r)}}function pe(t,r,o){r||(r={});for(var l in t)t.hasOwnProperty(l)&&(o!==!1||!r.hasOwnProperty(l))&&(r[l]=t[l]);return r}function ce(t,r,o,l,s){r==null&&(r=t.search(/[^\s\u00a0]/),r==-1&&(r=t.length));for(var f=l||0,d=s||0;;){var p=t.indexOf("	",f);if(p<0||p>=r)return d+(r-f);d+=p-f,d+=o-d%o,f=p+1}}var Ne=function(){this.id=null,this.f=null,this.time=0,this.handler=Ye(this.onTimeout,this)};Ne.prototype.onTimeout=function(t){t.id=0,t.time<=+new Date?t.f():setTimeout(t.handler,t.time-+new Date)},Ne.prototype.set=function(t,r){this.f=r;var o=+new Date+t;(!this.id||o<this.time)&&(clearTimeout(this.id),this.id=setTimeout(this.handler,t),this.time=o)};function be(t,r){for(var o=0;o<t.length;++o)if(t[o]==r)return o;return-1}var Ri=50,We={toString:function(){return"CodeMirror.Pass"}},Ke={scroll:!1},Vn={origin:"*mouse"},Qn={origin:"+move"};function kn(t,r,o){for(var l=0,s=0;;){var f=t.indexOf("	",l);f==-1&&(f=t.length);var d=f-l;if(f==t.length||s+d>=r)return l+Math.min(d,r-s);if(s+=f-l,s+=o-s%o,l=f+1,s>=r)return l}}var en=[""];function dn(t){for(;en.length<=t;)en.push(_e(en)+" ");return en[t]}function _e(t){return t[t.length-1]}function ar(t,r){for(var o=[],l=0;l<t.length;l++)o[l]=r(t[l],l);return o}function ct(t,r,o){for(var l=0,s=o(r);l<t.length&&o(t[l])<=s;)l++;t.splice(l,0,r)}function ur(){}function ci(t,r){var o;return Object.create?o=Object.create(t):(ur.prototype=t,o=new ur),r&&pe(r,o),o}var Fl=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;function Pr(t){return/\w/.test(t)||t>""&&(t.toUpperCase()!=t.toLowerCase()||Fl.test(t))}function fr(t,r){return r?r.source.indexOf("\\w")>-1&&Pr(t)?!0:r.test(t):Pr(t)}function xe(t){for(var r in t)if(t.hasOwnProperty(r)&&t[r])return!1;return!0}var ke=/[\u0300-\u036f\u0483-\u0489\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u0610-\u061a\u064b-\u065e\u0670\u06d6-\u06dc\u06de-\u06e4\u06e7\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0900-\u0902\u093c\u0941-\u0948\u094d\u0951-\u0955\u0962\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2\u09e3\u0a01\u0a02\u0a3c\u0a41\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a70\u0a71\u0a75\u0a81\u0a82\u0abc\u0ac1-\u0ac5\u0ac7\u0ac8\u0acd\u0ae2\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c62\u0c63\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc\u0ccd\u0cd5\u0cd6\u0ce2\u0ce3\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb\u0ebc\u0ec8-\u0ecd\u0f18\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86\u0f87\u0f90-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039\u103a\u103d\u103e\u1058\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085\u1086\u108d\u109d\u135f\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193b\u1a17\u1a18\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80\u1b81\u1ba2-\u1ba5\u1ba8\u1ba9\u1c2c-\u1c33\u1c36\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1dc0-\u1de6\u1dfd-\u1dff\u200c\u200d\u20d0-\u20f0\u2cef-\u2cf1\u2de0-\u2dff\u302a-\u302f\u3099\u309a\ua66f-\ua672\ua67c\ua67d\ua6f0\ua6f1\ua802\ua806\ua80b\ua825\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\uaa29-\uaa2e\uaa31\uaa32\uaa35\uaa36\uaa43\uaa4c\uaab0\uaab2-\uaab4\uaab7\uaab8\uaabe\uaabf\uaac1\uabe5\uabe8\uabed\udc00-\udfff\ufb1e\ufe00-\ufe0f\ufe20-\ufe26\uff9e\uff9f]/;function he(t){return t.charCodeAt(0)>=768&&ke.test(t)}function De(t,r,o){for(;(o<0?r>0:r<t.length)&&he(t.charAt(r));)r+=o;return r}function Ee(t,r,o){for(var l=r>o?-1:1;;){if(r==o)return r;var s=(r+o)/2,f=l<0?Math.ceil(s):Math.floor(s);if(f==r)return t(f)?r:o;t(f)?o=f:r=f+l}}function hn(t,r,o,l){if(!t)return l(r,o,"ltr",0);for(var s=!1,f=0;f<t.length;++f){var d=t[f];(d.from<o&&d.to>r||r==o&&d.to==r)&&(l(Math.max(d.from,r),Math.min(d.to,o),d.level==1?"rtl":"ltr",f),s=!0)}s||l(r,o,"ltr")}var Cn=null;function Tn(t,r,o){var l;Cn=null;for(var s=0;s<t.length;++s){var f=t[s];if(f.from<r&&f.to>r)return s;f.to==r&&(f.from!=f.to&&o=="before"?l=s:Cn=s),f.from==r&&(f.from!=f.to&&o!="before"?l=s:Cn=s)}return l??Cn}var yu=function(){var t="bbbbbbbbbtstwsbbbbbbbbbbbbbbssstwNN%%%NNNNNN,N,N1111111111NNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNbbbbbbsbbbbbbbbbbbbbbbbbbbbbbbbbb,N%%%%NNNNLNNNNN%%11NLNNN1LNNNNNLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLN",r="nnnnnnNNr%%r,rNNmmmmmmmmmmmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmmmmmmmmnnnnnnnnnn%nnrrrmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmnNmmmmmmrrmmNmmmmrr1111111111";function o(y){return y<=247?t.charAt(y):1424<=y&&y<=1524?"R":1536<=y&&y<=1785?r.charAt(y-1536):1774<=y&&y<=2220?"r":8192<=y&&y<=8203?"w":y==8204?"b":"L"}var l=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/,s=/[stwN]/,f=/[LRr]/,d=/[Lb1n]/,p=/[1n]/;function g(y,k,N){this.level=y,this.from=k,this.to=N}return function(y,k){var N=k=="ltr"?"L":"R";if(y.length==0||k=="ltr"&&!l.test(y))return!1;for(var I=y.length,O=[],$=0;$<I;++$)O.push(o(y.charCodeAt($)));for(var q=0,J=N;q<I;++q){var re=O[q];re=="m"?O[q]=J:J=re}for(var se=0,ie=N;se<I;++se){var ae=O[se];ae=="1"&&ie=="r"?O[se]="n":f.test(ae)&&(ie=ae,ae=="r"&&(O[se]="R"))}for(var me=1,ve=O[0];me<I-1;++me){var Me=O[me];Me=="+"&&ve=="1"&&O[me+1]=="1"?O[me]="1":Me==","&&ve==O[me+1]&&(ve=="1"||ve=="n")&&(O[me]=ve),ve=Me}for(var Ue=0;Ue<I;++Ue){var wt=O[Ue];if(wt==",")O[Ue]="N";else if(wt=="%"){var Xe=void 0;for(Xe=Ue+1;Xe<I&&O[Xe]=="%";++Xe);for(var rn=Ue&&O[Ue-1]=="!"||Xe<I&&O[Xe]=="1"?"1":"N",qt=Ue;qt<Xe;++qt)O[qt]=rn;Ue=Xe-1}}for(var st=0,Vt=N;st<I;++st){var Lt=O[st];Vt=="L"&&Lt=="1"?O[st]="L":f.test(Lt)&&(Vt=Lt)}for(var ht=0;ht<I;++ht)if(s.test(O[ht])){var at=void 0;for(at=ht+1;at<I&&s.test(O[at]);++at);for(var tt=(ht?O[ht-1]:N)=="L",Qt=(at<I?O[at]:N)=="L",il=tt==Qt?tt?"L":"R":N,Kr=ht;Kr<at;++Kr)O[Kr]=il;ht=at-1}for(var _t=[],nr,xt=0;xt<I;)if(d.test(O[xt])){var df=xt;for(++xt;xt<I&&d.test(O[xt]);++xt);_t.push(new g(0,df,xt))}else{var xr=xt,Si=_t.length,ki=k=="rtl"?1:0;for(++xt;xt<I&&O[xt]!="L";++xt);for(var Rt=xr;Rt<xt;)if(p.test(O[Rt])){xr<Rt&&(_t.splice(Si,0,new g(1,xr,Rt)),Si+=ki);var ll=Rt;for(++Rt;Rt<xt&&p.test(O[Rt]);++Rt);_t.splice(Si,0,new g(2,ll,Rt)),Si+=ki,xr=Rt}else++Rt;xr<xt&&_t.splice(Si,0,new g(1,xr,xt))}return k=="ltr"&&(_t[0].level==1&&(nr=y.match(/^\s+/))&&(_t[0].from=nr[0].length,_t.unshift(new g(0,0,nr[0].length))),_e(_t).level==1&&(nr=y.match(/\s+$/))&&(_e(_t).to-=nr[0].length,_t.push(new g(0,I-nr[0].length,I)))),k=="rtl"?_t.reverse():_t}}();function Ln(t,r){var o=t.order;return o==null&&(o=t.order=yu(t.text,r)),o}var cs=[],oe=function(t,r,o){if(t.addEventListener)t.addEventListener(r,o,!1);else if(t.attachEvent)t.attachEvent("on"+r,o);else{var l=t._handlers||(t._handlers={});l[r]=(l[r]||cs).concat(o)}};function Wl(t,r){return t._handlers&&t._handlers[r]||cs}function Et(t,r,o){if(t.removeEventListener)t.removeEventListener(r,o,!1);else if(t.detachEvent)t.detachEvent("on"+r,o);else{var l=t._handlers,s=l&&l[r];if(s){var f=be(s,o);f>-1&&(l[r]=s.slice(0,f).concat(s.slice(f+1)))}}}function $e(t,r){var o=Wl(t,r);if(o.length)for(var l=Array.prototype.slice.call(arguments,2),s=0;s<o.length;++s)o[s].apply(null,l)}function Je(t,r,o){return typeof r=="string"&&(r={type:r,preventDefault:function(){this.defaultPrevented=!0}}),$e(t,o||r.type,t,r),Bl(r)||r.codemirrorIgnore}function ds(t){var r=t._handlers&&t._handlers.cursorActivity;if(r)for(var o=t.curOp.cursorActivityHandlers||(t.curOp.cursorActivityHandlers=[]),l=0;l<r.length;++l)be(o,r[l])==-1&&o.push(r[l])}function zt(t,r){return Wl(t,r).length>0}function It(t){t.prototype.on=function(r,o){oe(this,r,o)},t.prototype.off=function(r,o){Et(this,r,o)}}function bt(t){t.preventDefault?t.preventDefault():t.returnValue=!1}function di(t){t.stopPropagation?t.stopPropagation():t.cancelBubble=!0}function Bl(t){return t.defaultPrevented!=null?t.defaultPrevented:t.returnValue==!1}function cr(t){bt(t),di(t)}function $t(t){return t.target||t.srcElement}function Hl(t){var r=t.which;return r==null&&(t.button&1?r=1:t.button&2?r=3:t.button&4&&(r=2)),G&&t.ctrlKey&&r==1&&(r=3),r}var wu=function(){if(m&&x<9)return!1;var t=_("div");return"draggable"in t||"dragDrop"in t}(),Nn;function xu(t){if(Nn==null){var r=_("span","​");le(t,_("span",[r,document.createTextNode("x")])),t.firstChild.offsetHeight!=0&&(Nn=r.offsetWidth<=1&&r.offsetHeight>2&&!(m&&x<8))}var o=Nn?_("span","​"):_("span"," ",null,"display: inline-block; width: 1px; margin-right: -1px");return o.setAttribute("cm-text",""),o}var Fi;function hs(t){if(Fi!=null)return Fi;var r=le(t,document.createTextNode("AخA")),o=ee(r,0,1).getBoundingClientRect(),l=ee(r,1,2).getBoundingClientRect();return F(t),!o||o.left==o.right?!1:Fi=l.right-o.right<3}var Ul=`

b`.split(/\n/).length!=3?function(t){for(var r=0,o=[],l=t.length;r<=l;){var s=t.indexOf(`
`,r);s==-1&&(s=t.length);var f=t.slice(r,t.charAt(s-1)=="\r"?s-1:s),d=f.indexOf("\r");d!=-1?(o.push(f.slice(0,d)),r+=d+1):(o.push(f),r=s+1)}return o}:function(t){return t.split(/\r\n?|\n/)},Or=window.getSelection?function(t){try{return t.selectionStart!=t.selectionEnd}catch{return!1}}:function(t){var r;try{r=t.ownerDocument.selection.createRange()}catch{}return!r||r.parentElement()!=t?!1:r.compareEndPoints("StartToEnd",r)!=0},Yn=function(){var t=_("div");return"oncopy"in t?!0:(t.setAttribute("oncopy","return;"),typeof t.oncopy=="function")}(),Xn=null;function ps(t){if(Xn!=null)return Xn;var r=le(t,_("span","x")),o=r.getBoundingClientRect(),l=ee(r,0,1).getBoundingClientRect();return Xn=Math.abs(o.left-l.left)>1}var An={},zr={};function vs(t,r){arguments.length>2&&(r.dependencies=Array.prototype.slice.call(arguments,2)),An[t]=r}function Wi(t,r){zr[t]=r}function tn(t){if(typeof t=="string"&&zr.hasOwnProperty(t))t=zr[t];else if(t&&typeof t.name=="string"&&zr.hasOwnProperty(t.name)){var r=zr[t.name];typeof r=="string"&&(r={name:r}),t=ci(r,t),t.name=r.name}else{if(typeof t=="string"&&/^[\w\-]+\/[\w\-]+\+xml$/.test(t))return tn("application/xml");if(typeof t=="string"&&/^[\w\-]+\/[\w\-]+\+json$/.test(t))return tn("application/json")}return typeof t=="string"?{name:t}:t||{name:"null"}}function dr(t,r){r=tn(r);var o=An[r.name];if(!o)return dr(t,"text/plain");var l=o(t,r);if(Ir.hasOwnProperty(r.name)){var s=Ir[r.name];for(var f in s)s.hasOwnProperty(f)&&(l.hasOwnProperty(f)&&(l["_"+f]=l[f]),l[f]=s[f])}if(l.name=r.name,r.helperType&&(l.helperType=r.helperType),r.modeProps)for(var d in r.modeProps)l[d]=r.modeProps[d];return l}var Ir={};function gs(t,r){var o=Ir.hasOwnProperty(t)?Ir[t]:Ir[t]={};pe(r,o)}function hr(t,r){if(r===!0)return r;if(t.copyState)return t.copyState(r);var o={};for(var l in r){var s=r[l];s instanceof Array&&(s=s.concat([])),o[l]=s}return o}function Ar(t,r){for(var o;t.innerMode&&(o=t.innerMode(r),!(!o||o.mode==t));)r=o.state,t=o.mode;return o||{mode:t,state:r}}function jl(t,r,o){return t.startState?t.startState(r,o):!0}var et=function(t,r,o){this.pos=this.start=0,this.string=t,this.tabSize=r||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0,this.lineOracle=o};et.prototype.eol=function(){return this.pos>=this.string.length},et.prototype.sol=function(){return this.pos==this.lineStart},et.prototype.peek=function(){return this.string.charAt(this.pos)||void 0},et.prototype.next=function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},et.prototype.eat=function(t){var r=this.string.charAt(this.pos),o;if(typeof t=="string"?o=r==t:o=r&&(t.test?t.test(r):t(r)),o)return++this.pos,r},et.prototype.eatWhile=function(t){for(var r=this.pos;this.eat(t););return this.pos>r},et.prototype.eatSpace=function(){for(var t=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>t},et.prototype.skipToEnd=function(){this.pos=this.string.length},et.prototype.skipTo=function(t){var r=this.string.indexOf(t,this.pos);if(r>-1)return this.pos=r,!0},et.prototype.backUp=function(t){this.pos-=t},et.prototype.column=function(){return this.lastColumnPos<this.start&&(this.lastColumnValue=ce(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue-(this.lineStart?ce(this.string,this.lineStart,this.tabSize):0)},et.prototype.indentation=function(){return ce(this.string,null,this.tabSize)-(this.lineStart?ce(this.string,this.lineStart,this.tabSize):0)},et.prototype.match=function(t,r,o){if(typeof t=="string"){var l=function(d){return o?d.toLowerCase():d},s=this.string.substr(this.pos,t.length);if(l(s)==l(t))return r!==!1&&(this.pos+=t.length),!0}else{var f=this.string.slice(this.pos).match(t);return f&&f.index>0?null:(f&&r!==!1&&(this.pos+=f[0].length),f)}},et.prototype.current=function(){return this.string.slice(this.start,this.pos)},et.prototype.hideFirstChars=function(t,r){this.lineStart+=t;try{return r()}finally{this.lineStart-=t}},et.prototype.lookAhead=function(t){var r=this.lineOracle;return r&&r.lookAhead(t)},et.prototype.baseToken=function(){var t=this.lineOracle;return t&&t.baseToken(this.pos)};function de(t,r){if(r-=t.first,r<0||r>=t.size)throw new Error("There is no line "+(r+t.first)+" in the document.");for(var o=t;!o.lines;)for(var l=0;;++l){var s=o.children[l],f=s.chunkSize();if(r<f){o=s;break}r-=f}return o.lines[r]}function pr(t,r,o){var l=[],s=r.line;return t.iter(r.line,o.line+1,function(f){var d=f.text;s==o.line&&(d=d.slice(0,o.ch)),s==r.line&&(d=d.slice(r.ch)),l.push(d),++s}),l}function Bi(t,r,o){var l=[];return t.iter(r,o,function(s){l.push(s.text)}),l}function En(t,r){var o=r-t.height;if(o)for(var l=t;l;l=l.parent)l.height+=o}function Re(t){if(t.parent==null)return null;for(var r=t.parent,o=be(r.lines,t),l=r.parent;l;r=l,l=l.parent)for(var s=0;l.children[s]!=r;++s)o+=l.children[s].chunkSize();return o+r.first}function Zn(t,r){var o=t.first;e:do{for(var l=0;l<t.children.length;++l){var s=t.children[l],f=s.height;if(r<f){t=s;continue e}r-=f,o+=s.chunkSize()}return o}while(!t.lines);for(var d=0;d<t.lines.length;++d){var p=t.lines[d],g=p.height;if(r<g)break;r-=g}return o+d}function w(t,r){return r>=t.first&&r<t.first+t.size}function E(t,r){return String(t.lineNumberFormatter(r+t.firstLineNumber))}function D(t,r,o){if(o===void 0&&(o=null),!(this instanceof D))return new D(t,r,o);this.line=t,this.ch=r,this.sticky=o}function j(t,r){return t.line-r.line||t.ch-r.ch}function Te(t,r){return t.sticky==r.sticky&&j(t,r)==0}function Pe(t){return D(t.line,t.ch)}function Ie(t,r){return j(t,r)<0?r:t}function dt(t,r){return j(t,r)<0?t:r}function pn(t,r){return Math.max(t.first,Math.min(r,t.first+t.size-1))}function Le(t,r){if(r.line<t.first)return D(t.first,0);var o=t.first+t.size-1;return r.line>o?D(o,de(t,o).text.length):K0(r,de(t,r.line).text.length)}function K0(t,r){var o=t.ch;return o==null||o>r?D(t.line,r):o<0?D(t.line,0):t}function zd(t,r){for(var o=[],l=0;l<r.length;l++)o[l]=Le(t,r[l]);return o}var ms=function(t,r){this.state=t,this.lookAhead=r},Jn=function(t,r,o,l){this.state=r,this.doc=t,this.line=o,this.maxLookAhead=l||0,this.baseTokens=null,this.baseTokenPos=1};Jn.prototype.lookAhead=function(t){var r=this.doc.getLine(this.line+t);return r!=null&&t>this.maxLookAhead&&(this.maxLookAhead=t),r},Jn.prototype.baseToken=function(t){if(!this.baseTokens)return null;for(;this.baseTokens[this.baseTokenPos]<=t;)this.baseTokenPos+=2;var r=this.baseTokens[this.baseTokenPos+1];return{type:r&&r.replace(/( |^)overlay .*/,""),size:this.baseTokens[this.baseTokenPos]-t}},Jn.prototype.nextLine=function(){this.line++,this.maxLookAhead>0&&this.maxLookAhead--},Jn.fromSaved=function(t,r,o){return r instanceof ms?new Jn(t,hr(t.mode,r.state),o,r.lookAhead):new Jn(t,hr(t.mode,r),o)},Jn.prototype.save=function(t){var r=t!==!1?hr(this.doc.mode,this.state):this.state;return this.maxLookAhead>0?new ms(r,this.maxLookAhead):r};function Id(t,r,o,l){var s=[t.state.modeGen],f={};Hd(t,r.text,t.doc.mode,o,function(y,k){return s.push(y,k)},f,l);for(var d=o.state,p=function(y){o.baseTokens=s;var k=t.state.overlays[y],N=1,I=0;o.state=!0,Hd(t,r.text,k.mode,o,function(O,$){for(var q=N;I<O;){var J=s[N];J>O&&s.splice(N,1,O,s[N+1],J),N+=2,I=Math.min(O,J)}if($)if(k.opaque)s.splice(q,N-q,O,"overlay "+$),N=q+2;else for(;q<N;q+=2){var re=s[q+1];s[q+1]=(re?re+" ":"")+"overlay "+$}},f),o.state=d,o.baseTokens=null,o.baseTokenPos=1},g=0;g<t.state.overlays.length;++g)p(g);return{styles:s,classes:f.bgClass||f.textClass?f:null}}function Ad(t,r,o){if(!r.styles||r.styles[0]!=t.state.modeGen){var l=$l(t,Re(r)),s=r.text.length>t.options.maxHighlightLength&&hr(t.doc.mode,l.state),f=Id(t,r,l);s&&(l.state=s),r.stateAfter=l.save(!s),r.styles=f.styles,f.classes?r.styleClasses=f.classes:r.styleClasses&&(r.styleClasses=null),o===t.doc.highlightFrontier&&(t.doc.modeFrontier=Math.max(t.doc.modeFrontier,++t.doc.highlightFrontier))}return r.styles}function $l(t,r,o){var l=t.doc,s=t.display;if(!l.mode.startState)return new Jn(l,!0,r);var f=G0(t,r,o),d=f>l.first&&de(l,f-1).stateAfter,p=d?Jn.fromSaved(l,d,f):new Jn(l,jl(l.mode),f);return l.iter(f,r,function(g){Su(t,g.text,p);var y=p.line;g.stateAfter=y==r-1||y%5==0||y>=s.viewFrom&&y<s.viewTo?p.save():null,p.nextLine()}),o&&(l.modeFrontier=p.line),p}function Su(t,r,o,l){var s=t.doc.mode,f=new et(r,t.options.tabSize,o);for(f.start=f.pos=l||0,r==""&&Rd(s,o.state);!f.eol();)ku(s,f,o.state),f.start=f.pos}function Rd(t,r){if(t.blankLine)return t.blankLine(r);if(t.innerMode){var o=Ar(t,r);if(o.mode.blankLine)return o.mode.blankLine(o.state)}}function ku(t,r,o,l){for(var s=0;s<10;s++){l&&(l[0]=Ar(t,o).mode);var f=t.token(r,o);if(r.pos>r.start)return f}throw new Error("Mode "+t.name+" failed to advance stream.")}var Fd=function(t,r,o){this.start=t.start,this.end=t.pos,this.string=t.current(),this.type=r||null,this.state=o};function Wd(t,r,o,l){var s=t.doc,f=s.mode,d;r=Le(s,r);var p=de(s,r.line),g=$l(t,r.line,o),y=new et(p.text,t.options.tabSize,g),k;for(l&&(k=[]);(l||y.pos<r.ch)&&!y.eol();)y.start=y.pos,d=ku(f,y,g.state),l&&k.push(new Fd(y,d,hr(s.mode,g.state)));return l?k:new Fd(y,d,g.state)}function Bd(t,r){if(t)for(;;){var o=t.match(/(?:^|\s+)line-(background-)?(\S+)/);if(!o)break;t=t.slice(0,o.index)+t.slice(o.index+o[0].length);var l=o[1]?"bgClass":"textClass";r[l]==null?r[l]=o[2]:new RegExp("(?:^|\\s)"+o[2]+"(?:$|\\s)").test(r[l])||(r[l]+=" "+o[2])}return t}function Hd(t,r,o,l,s,f,d){var p=o.flattenSpans;p==null&&(p=t.options.flattenSpans);var g=0,y=null,k=new et(r,t.options.tabSize,l),N,I=t.options.addModeClass&&[null];for(r==""&&Bd(Rd(o,l.state),f);!k.eol();){if(k.pos>t.options.maxHighlightLength?(p=!1,d&&Su(t,r,l,k.pos),k.pos=r.length,N=null):N=Bd(ku(o,k,l.state,I),f),I){var O=I[0].name;O&&(N="m-"+(N?O+" "+N:O))}if(!p||y!=N){for(;g<k.start;)g=Math.min(k.start,g+5e3),s(g,y);y=N}k.start=k.pos}for(;g<k.pos;){var $=Math.min(k.pos,g+5e3);s($,y),g=$}}function G0(t,r,o){for(var l,s,f=t.doc,d=o?-1:r-(t.doc.mode.innerMode?1e3:100),p=r;p>d;--p){if(p<=f.first)return f.first;var g=de(f,p-1),y=g.stateAfter;if(y&&(!o||p+(y instanceof ms?y.lookAhead:0)<=f.modeFrontier))return p;var k=ce(g.text,null,t.options.tabSize);(s==null||l>k)&&(s=p-1,l=k)}return s}function q0(t,r){if(t.modeFrontier=Math.min(t.modeFrontier,r),!(t.highlightFrontier<r-10)){for(var o=t.first,l=r-1;l>o;l--){var s=de(t,l).stateAfter;if(s&&(!(s instanceof ms)||l+s.lookAhead<r)){o=l+1;break}}t.highlightFrontier=Math.min(t.highlightFrontier,o)}}var Ud=!1,vr=!1;function V0(){Ud=!0}function Q0(){vr=!0}function ys(t,r,o){this.marker=t,this.from=r,this.to=o}function Kl(t,r){if(t)for(var o=0;o<t.length;++o){var l=t[o];if(l.marker==r)return l}}function Y0(t,r){for(var o,l=0;l<t.length;++l)t[l]!=r&&(o||(o=[])).push(t[l]);return o}function X0(t,r,o){var l=o&&window.WeakSet&&(o.markedSpans||(o.markedSpans=new WeakSet));l&&t.markedSpans&&l.has(t.markedSpans)?t.markedSpans.push(r):(t.markedSpans=t.markedSpans?t.markedSpans.concat([r]):[r],l&&l.add(t.markedSpans)),r.marker.attachLine(t)}function Z0(t,r,o){var l;if(t)for(var s=0;s<t.length;++s){var f=t[s],d=f.marker,p=f.from==null||(d.inclusiveLeft?f.from<=r:f.from<r);if(p||f.from==r&&d.type=="bookmark"&&(!o||!f.marker.insertLeft)){var g=f.to==null||(d.inclusiveRight?f.to>=r:f.to>r);(l||(l=[])).push(new ys(d,f.from,g?null:f.to))}}return l}function J0(t,r,o){var l;if(t)for(var s=0;s<t.length;++s){var f=t[s],d=f.marker,p=f.to==null||(d.inclusiveRight?f.to>=r:f.to>r);if(p||f.from==r&&d.type=="bookmark"&&(!o||f.marker.insertLeft)){var g=f.from==null||(d.inclusiveLeft?f.from<=r:f.from<r);(l||(l=[])).push(new ys(d,g?null:f.from-r,f.to==null?null:f.to-r))}}return l}function Cu(t,r){if(r.full)return null;var o=w(t,r.from.line)&&de(t,r.from.line).markedSpans,l=w(t,r.to.line)&&de(t,r.to.line).markedSpans;if(!o&&!l)return null;var s=r.from.ch,f=r.to.ch,d=j(r.from,r.to)==0,p=Z0(o,s,d),g=J0(l,f,d),y=r.text.length==1,k=_e(r.text).length+(y?s:0);if(p)for(var N=0;N<p.length;++N){var I=p[N];if(I.to==null){var O=Kl(g,I.marker);O?y&&(I.to=O.to==null?null:O.to+k):I.to=s}}if(g)for(var $=0;$<g.length;++$){var q=g[$];if(q.to!=null&&(q.to+=k),q.from==null){var J=Kl(p,q.marker);J||(q.from=k,y&&(p||(p=[])).push(q))}else q.from+=k,y&&(p||(p=[])).push(q)}p&&(p=jd(p)),g&&g!=p&&(g=jd(g));var re=[p];if(!y){var se=r.text.length-2,ie;if(se>0&&p)for(var ae=0;ae<p.length;++ae)p[ae].to==null&&(ie||(ie=[])).push(new ys(p[ae].marker,null,null));for(var me=0;me<se;++me)re.push(ie);re.push(g)}return re}function jd(t){for(var r=0;r<t.length;++r){var o=t[r];o.from!=null&&o.from==o.to&&o.marker.clearWhenEmpty!==!1&&t.splice(r--,1)}return t.length?t:null}function ey(t,r,o){var l=null;if(t.iter(r.line,o.line+1,function(O){if(O.markedSpans)for(var $=0;$<O.markedSpans.length;++$){var q=O.markedSpans[$].marker;q.readOnly&&(!l||be(l,q)==-1)&&(l||(l=[])).push(q)}}),!l)return null;for(var s=[{from:r,to:o}],f=0;f<l.length;++f)for(var d=l[f],p=d.find(0),g=0;g<s.length;++g){var y=s[g];if(!(j(y.to,p.from)<0||j(y.from,p.to)>0)){var k=[g,1],N=j(y.from,p.from),I=j(y.to,p.to);(N<0||!d.inclusiveLeft&&!N)&&k.push({from:y.from,to:p.from}),(I>0||!d.inclusiveRight&&!I)&&k.push({from:p.to,to:y.to}),s.splice.apply(s,k),g+=k.length-3}}return s}function $d(t){var r=t.markedSpans;if(r){for(var o=0;o<r.length;++o)r[o].marker.detachLine(t);t.markedSpans=null}}function Kd(t,r){if(r){for(var o=0;o<r.length;++o)r[o].marker.attachLine(t);t.markedSpans=r}}function ws(t){return t.inclusiveLeft?-1:0}function xs(t){return t.inclusiveRight?1:0}function Tu(t,r){var o=t.lines.length-r.lines.length;if(o!=0)return o;var l=t.find(),s=r.find(),f=j(l.from,s.from)||ws(t)-ws(r);if(f)return-f;var d=j(l.to,s.to)||xs(t)-xs(r);return d||r.id-t.id}function Gd(t,r){var o=vr&&t.markedSpans,l;if(o)for(var s=void 0,f=0;f<o.length;++f)s=o[f],s.marker.collapsed&&(r?s.from:s.to)==null&&(!l||Tu(l,s.marker)<0)&&(l=s.marker);return l}function qd(t){return Gd(t,!0)}function Ss(t){return Gd(t,!1)}function ty(t,r){var o=vr&&t.markedSpans,l;if(o)for(var s=0;s<o.length;++s){var f=o[s];f.marker.collapsed&&(f.from==null||f.from<r)&&(f.to==null||f.to>r)&&(!l||Tu(l,f.marker)<0)&&(l=f.marker)}return l}function Vd(t,r,o,l,s){var f=de(t,r),d=vr&&f.markedSpans;if(d)for(var p=0;p<d.length;++p){var g=d[p];if(g.marker.collapsed){var y=g.marker.find(0),k=j(y.from,o)||ws(g.marker)-ws(s),N=j(y.to,l)||xs(g.marker)-xs(s);if(!(k>=0&&N<=0||k<=0&&N>=0)&&(k<=0&&(g.marker.inclusiveRight&&s.inclusiveLeft?j(y.to,o)>=0:j(y.to,o)>0)||k>=0&&(g.marker.inclusiveRight&&s.inclusiveLeft?j(y.from,l)<=0:j(y.from,l)<0)))return!0}}}function Rn(t){for(var r;r=qd(t);)t=r.find(-1,!0).line;return t}function ny(t){for(var r;r=Ss(t);)t=r.find(1,!0).line;return t}function ry(t){for(var r,o;r=Ss(t);)t=r.find(1,!0).line,(o||(o=[])).push(t);return o}function Lu(t,r){var o=de(t,r),l=Rn(o);return o==l?r:Re(l)}function Qd(t,r){if(r>t.lastLine())return r;var o=de(t,r),l;if(!Rr(t,o))return r;for(;l=Ss(o);)o=l.find(1,!0).line;return Re(o)+1}function Rr(t,r){var o=vr&&r.markedSpans;if(o){for(var l=void 0,s=0;s<o.length;++s)if(l=o[s],!!l.marker.collapsed){if(l.from==null)return!0;if(!l.marker.widgetNode&&l.from==0&&l.marker.inclusiveLeft&&Nu(t,r,l))return!0}}}function Nu(t,r,o){if(o.to==null){var l=o.marker.find(1,!0);return Nu(t,l.line,Kl(l.line.markedSpans,o.marker))}if(o.marker.inclusiveRight&&o.to==r.text.length)return!0;for(var s=void 0,f=0;f<r.markedSpans.length;++f)if(s=r.markedSpans[f],s.marker.collapsed&&!s.marker.widgetNode&&s.from==o.to&&(s.to==null||s.to!=o.from)&&(s.marker.inclusiveLeft||o.marker.inclusiveRight)&&Nu(t,r,s))return!0}function gr(t){t=Rn(t);for(var r=0,o=t.parent,l=0;l<o.lines.length;++l){var s=o.lines[l];if(s==t)break;r+=s.height}for(var f=o.parent;f;o=f,f=o.parent)for(var d=0;d<f.children.length;++d){var p=f.children[d];if(p==o)break;r+=p.height}return r}function ks(t){if(t.height==0)return 0;for(var r=t.text.length,o,l=t;o=qd(l);){var s=o.find(0,!0);l=s.from.line,r+=s.from.ch-s.to.ch}for(l=t;o=Ss(l);){var f=o.find(0,!0);r-=l.text.length-f.from.ch,l=f.to.line,r+=l.text.length-f.to.ch}return r}function Eu(t){var r=t.display,o=t.doc;r.maxLine=de(o,o.first),r.maxLineLength=ks(r.maxLine),r.maxLineChanged=!0,o.iter(function(l){var s=ks(l);s>r.maxLineLength&&(r.maxLineLength=s,r.maxLine=l)})}var Hi=function(t,r,o){this.text=t,Kd(this,r),this.height=o?o(this):1};Hi.prototype.lineNo=function(){return Re(this)},It(Hi);function iy(t,r,o,l){t.text=r,t.stateAfter&&(t.stateAfter=null),t.styles&&(t.styles=null),t.order!=null&&(t.order=null),$d(t),Kd(t,o);var s=l?l(t):1;s!=t.height&&En(t,s)}function ly(t){t.parent=null,$d(t)}var oy={},sy={};function Yd(t,r){if(!t||/^\s*$/.test(t))return null;var o=r.addModeClass?sy:oy;return o[t]||(o[t]=t.replace(/\S+/g,"cm-$&"))}function Xd(t,r){var o=X("span",null,null,L?"padding-right: .1px":null),l={pre:X("pre",[o],"CodeMirror-line"),content:o,col:0,pos:0,cm:t,trailingSpace:!1,splitSpaces:t.getOption("lineWrapping")};r.measure={};for(var s=0;s<=(r.rest?r.rest.length:0);s++){var f=s?r.rest[s-1]:r.line,d=void 0;l.pos=0,l.addToken=uy,hs(t.display.measure)&&(d=Ln(f,t.doc.direction))&&(l.addToken=cy(l.addToken,d)),l.map=[];var p=r!=t.display.externalMeasured&&Re(f);dy(f,l,Ad(t,f,p)),f.styleClasses&&(f.styleClasses.bgClass&&(l.bgClass=ne(f.styleClasses.bgClass,l.bgClass||"")),f.styleClasses.textClass&&(l.textClass=ne(f.styleClasses.textClass,l.textClass||""))),l.map.length==0&&l.map.push(0,0,l.content.appendChild(xu(t.display.measure))),s==0?(r.measure.map=l.map,r.measure.cache={}):((r.measure.maps||(r.measure.maps=[])).push(l.map),(r.measure.caches||(r.measure.caches=[])).push({}))}if(L){var g=l.content.lastChild;(/\bcm-tab\b/.test(g.className)||g.querySelector&&g.querySelector(".cm-tab"))&&(l.content.className="cm-tab-wrap-hack")}return $e(t,"renderLine",t,r.line,l.pre),l.pre.className&&(l.textClass=ne(l.pre.className,l.textClass||"")),l}function ay(t){var r=_("span","•","cm-invalidchar");return r.title="\\u"+t.charCodeAt(0).toString(16),r.setAttribute("aria-label",r.title),r}function uy(t,r,o,l,s,f,d){if(r){var p=t.splitSpaces?fy(r,t.trailingSpace):r,g=t.cm.state.specialChars,y=!1,k;if(!g.test(r))t.col+=r.length,k=document.createTextNode(p),t.map.push(t.pos,t.pos+r.length,k),m&&x<9&&(y=!0),t.pos+=r.length;else{k=document.createDocumentFragment();for(var N=0;;){g.lastIndex=N;var I=g.exec(r),O=I?I.index-N:r.length-N;if(O){var $=document.createTextNode(p.slice(N,N+O));m&&x<9?k.appendChild(_("span",[$])):k.appendChild($),t.map.push(t.pos,t.pos+O,$),t.col+=O,t.pos+=O}if(!I)break;N+=O+1;var q=void 0;if(I[0]=="	"){var J=t.cm.options.tabSize,re=J-t.col%J;q=k.appendChild(_("span",dn(re),"cm-tab")),q.setAttribute("role","presentation"),q.setAttribute("cm-text","	"),t.col+=re}else I[0]=="\r"||I[0]==`
`?(q=k.appendChild(_("span",I[0]=="\r"?"␍":"␤","cm-invalidchar")),q.setAttribute("cm-text",I[0]),t.col+=1):(q=t.cm.options.specialCharPlaceholder(I[0]),q.setAttribute("cm-text",I[0]),m&&x<9?k.appendChild(_("span",[q])):k.appendChild(q),t.col+=1);t.map.push(t.pos,t.pos+1,q),t.pos++}}if(t.trailingSpace=p.charCodeAt(r.length-1)==32,o||l||s||y||f||d){var se=o||"";l&&(se+=l),s&&(se+=s);var ie=_("span",[k],se,f);if(d)for(var ae in d)d.hasOwnProperty(ae)&&ae!="style"&&ae!="class"&&ie.setAttribute(ae,d[ae]);return t.content.appendChild(ie)}t.content.appendChild(k)}}function fy(t,r){if(t.length>1&&!/  /.test(t))return t;for(var o=r,l="",s=0;s<t.length;s++){var f=t.charAt(s);f==" "&&o&&(s==t.length-1||t.charCodeAt(s+1)==32)&&(f=" "),l+=f,o=f==" "}return l}function cy(t,r){return function(o,l,s,f,d,p,g){s=s?s+" cm-force-border":"cm-force-border";for(var y=o.pos,k=y+l.length;;){for(var N=void 0,I=0;I<r.length&&(N=r[I],!(N.to>y&&N.from<=y));I++);if(N.to>=k)return t(o,l,s,f,d,p,g);t(o,l.slice(0,N.to-y),s,f,null,p,g),f=null,l=l.slice(N.to-y),y=N.to}}}function Zd(t,r,o,l){var s=!l&&o.widgetNode;s&&t.map.push(t.pos,t.pos+r,s),!l&&t.cm.display.input.needsContentAttribute&&(s||(s=t.content.appendChild(document.createElement("span"))),s.setAttribute("cm-marker",o.id)),s&&(t.cm.display.input.setUneditable(s),t.content.appendChild(s)),t.pos+=r,t.trailingSpace=!1}function dy(t,r,o){var l=t.markedSpans,s=t.text,f=0;if(!l){for(var d=1;d<o.length;d+=2)r.addToken(r,s.slice(f,f=o[d]),Yd(o[d+1],r.cm.options));return}for(var p=s.length,g=0,y=1,k="",N,I,O=0,$,q,J,re,se;;){if(O==g){$=q=J=I="",se=null,re=null,O=1/0;for(var ie=[],ae=void 0,me=0;me<l.length;++me){var ve=l[me],Me=ve.marker;if(Me.type=="bookmark"&&ve.from==g&&Me.widgetNode)ie.push(Me);else if(ve.from<=g&&(ve.to==null||ve.to>g||Me.collapsed&&ve.to==g&&ve.from==g)){if(ve.to!=null&&ve.to!=g&&O>ve.to&&(O=ve.to,q=""),Me.className&&($+=" "+Me.className),Me.css&&(I=(I?I+";":"")+Me.css),Me.startStyle&&ve.from==g&&(J+=" "+Me.startStyle),Me.endStyle&&ve.to==O&&(ae||(ae=[])).push(Me.endStyle,ve.to),Me.title&&((se||(se={})).title=Me.title),Me.attributes)for(var Ue in Me.attributes)(se||(se={}))[Ue]=Me.attributes[Ue];Me.collapsed&&(!re||Tu(re.marker,Me)<0)&&(re=ve)}else ve.from>g&&O>ve.from&&(O=ve.from)}if(ae)for(var wt=0;wt<ae.length;wt+=2)ae[wt+1]==O&&(q+=" "+ae[wt]);if(!re||re.from==g)for(var Xe=0;Xe<ie.length;++Xe)Zd(r,0,ie[Xe]);if(re&&(re.from||0)==g){if(Zd(r,(re.to==null?p+1:re.to)-g,re.marker,re.from==null),re.to==null)return;re.to==g&&(re=!1)}}if(g>=p)break;for(var rn=Math.min(p,O);;){if(k){var qt=g+k.length;if(!re){var st=qt>rn?k.slice(0,rn-g):k;r.addToken(r,st,N?N+$:$,J,g+st.length==O?q:"",I,se)}if(qt>=rn){k=k.slice(rn-g),g=rn;break}g=qt,J=""}k=s.slice(f,f=o[y++]),N=Yd(o[y++],r.cm.options)}}}function Jd(t,r,o){this.line=r,this.rest=ry(r),this.size=this.rest?Re(_e(this.rest))-o+1:1,this.node=this.text=null,this.hidden=Rr(t,r)}function Cs(t,r,o){for(var l=[],s,f=r;f<o;f=s){var d=new Jd(t.doc,de(t.doc,f),f);s=f+d.size,l.push(d)}return l}var Ui=null;function hy(t){Ui?Ui.ops.push(t):t.ownsGroup=Ui={ops:[t],delayedCallbacks:[]}}function py(t){var r=t.delayedCallbacks,o=0;do{for(;o<r.length;o++)r[o].call(null);for(var l=0;l<t.ops.length;l++){var s=t.ops[l];if(s.cursorActivityHandlers)for(;s.cursorActivityCalled<s.cursorActivityHandlers.length;)s.cursorActivityHandlers[s.cursorActivityCalled++].call(null,s.cm)}}while(o<r.length)}function vy(t,r){var o=t.ownsGroup;if(o)try{py(o)}finally{Ui=null,r(o)}}var Gl=null;function gt(t,r){var o=Wl(t,r);if(o.length){var l=Array.prototype.slice.call(arguments,2),s;Ui?s=Ui.delayedCallbacks:Gl?s=Gl:(s=Gl=[],setTimeout(gy,0));for(var f=function(p){s.push(function(){return o[p].apply(null,l)})},d=0;d<o.length;++d)f(d)}}function gy(){var t=Gl;Gl=null;for(var r=0;r<t.length;++r)t[r]()}function eh(t,r,o,l){for(var s=0;s<r.changes.length;s++){var f=r.changes[s];f=="text"?yy(t,r):f=="gutter"?nh(t,r,o,l):f=="class"?bu(t,r):f=="widget"&&wy(t,r,l)}r.changes=null}function ql(t){return t.node==t.text&&(t.node=_("div",null,null,"position: relative"),t.text.parentNode&&t.text.parentNode.replaceChild(t.node,t.text),t.node.appendChild(t.text),m&&x<8&&(t.node.style.zIndex=2)),t.node}function my(t,r){var o=r.bgClass?r.bgClass+" "+(r.line.bgClass||""):r.line.bgClass;if(o&&(o+=" CodeMirror-linebackground"),r.background)o?r.background.className=o:(r.background.parentNode.removeChild(r.background),r.background=null);else if(o){var l=ql(r);r.background=l.insertBefore(_("div",null,o),l.firstChild),t.display.input.setUneditable(r.background)}}function th(t,r){var o=t.display.externalMeasured;return o&&o.line==r.line?(t.display.externalMeasured=null,r.measure=o.measure,o.built):Xd(t,r)}function yy(t,r){var o=r.text.className,l=th(t,r);r.text==r.node&&(r.node=l.pre),r.text.parentNode.replaceChild(l.pre,r.text),r.text=l.pre,l.bgClass!=r.bgClass||l.textClass!=r.textClass?(r.bgClass=l.bgClass,r.textClass=l.textClass,bu(t,r)):o&&(r.text.className=o)}function bu(t,r){my(t,r),r.line.wrapClass?ql(r).className=r.line.wrapClass:r.node!=r.text&&(r.node.className="");var o=r.textClass?r.textClass+" "+(r.line.textClass||""):r.line.textClass;r.text.className=o||""}function nh(t,r,o,l){if(r.gutter&&(r.node.removeChild(r.gutter),r.gutter=null),r.gutterBackground&&(r.node.removeChild(r.gutterBackground),r.gutterBackground=null),r.line.gutterClass){var s=ql(r);r.gutterBackground=_("div",null,"CodeMirror-gutter-background "+r.line.gutterClass,"left: "+(t.options.fixedGutter?l.fixedPos:-l.gutterTotalWidth)+"px; width: "+l.gutterTotalWidth+"px"),t.display.input.setUneditable(r.gutterBackground),s.insertBefore(r.gutterBackground,r.text)}var f=r.line.gutterMarkers;if(t.options.lineNumbers||f){var d=ql(r),p=r.gutter=_("div",null,"CodeMirror-gutter-wrapper","left: "+(t.options.fixedGutter?l.fixedPos:-l.gutterTotalWidth)+"px");if(p.setAttribute("aria-hidden","true"),t.display.input.setUneditable(p),d.insertBefore(p,r.text),r.line.gutterClass&&(p.className+=" "+r.line.gutterClass),t.options.lineNumbers&&(!f||!f["CodeMirror-linenumbers"])&&(r.lineNumber=p.appendChild(_("div",E(t.options,o),"CodeMirror-linenumber CodeMirror-gutter-elt","left: "+l.gutterLeft["CodeMirror-linenumbers"]+"px; width: "+t.display.lineNumInnerWidth+"px"))),f)for(var g=0;g<t.display.gutterSpecs.length;++g){var y=t.display.gutterSpecs[g].className,k=f.hasOwnProperty(y)&&f[y];k&&p.appendChild(_("div",[k],"CodeMirror-gutter-elt","left: "+l.gutterLeft[y]+"px; width: "+l.gutterWidth[y]+"px"))}}}function wy(t,r,o){r.alignable&&(r.alignable=null);for(var l=ze("CodeMirror-linewidget"),s=r.node.firstChild,f=void 0;s;s=f)f=s.nextSibling,l.test(s.className)&&r.node.removeChild(s);rh(t,r,o)}function xy(t,r,o,l){var s=th(t,r);return r.text=r.node=s.pre,s.bgClass&&(r.bgClass=s.bgClass),s.textClass&&(r.textClass=s.textClass),bu(t,r),nh(t,r,o,l),rh(t,r,l),r.node}function rh(t,r,o){if(ih(t,r.line,r,o,!0),r.rest)for(var l=0;l<r.rest.length;l++)ih(t,r.rest[l],r,o,!1)}function ih(t,r,o,l,s){if(r.widgets)for(var f=ql(o),d=0,p=r.widgets;d<p.length;++d){var g=p[d],y=_("div",[g.node],"CodeMirror-linewidget"+(g.className?" "+g.className:""));g.handleMouseEvents||y.setAttribute("cm-ignore-events","true"),Sy(g,y,o,l),t.display.input.setUneditable(y),s&&g.above?f.insertBefore(y,o.gutter||o.text):f.appendChild(y),gt(g,"redraw")}}function Sy(t,r,o,l){if(t.noHScroll){(o.alignable||(o.alignable=[])).push(r);var s=l.wrapperWidth;r.style.left=l.fixedPos+"px",t.coverGutter||(s-=l.gutterTotalWidth,r.style.paddingLeft=l.gutterTotalWidth+"px"),r.style.width=s+"px"}t.coverGutter&&(r.style.zIndex=5,r.style.position="relative",t.noHScroll||(r.style.marginLeft=-l.gutterTotalWidth+"px"))}function Vl(t){if(t.height!=null)return t.height;var r=t.doc.cm;if(!r)return 0;if(!C(document.body,t.node)){var o="position: relative;";t.coverGutter&&(o+="margin-left: -"+r.display.gutters.offsetWidth+"px;"),t.noHScroll&&(o+="width: "+r.display.wrapper.clientWidth+"px;"),le(r.display.measure,_("div",[t.node],null,o))}return t.height=t.node.parentNode.offsetHeight}function mr(t,r){for(var o=$t(r);o!=t.wrapper;o=o.parentNode)if(!o||o.nodeType==1&&o.getAttribute("cm-ignore-events")=="true"||o.parentNode==t.sizer&&o!=t.mover)return!0}function Ts(t){return t.lineSpace.offsetTop}function Mu(t){return t.mover.offsetHeight-t.lineSpace.offsetHeight}function lh(t){if(t.cachedPaddingH)return t.cachedPaddingH;var r=le(t.measure,_("pre","x","CodeMirror-line-like")),o=window.getComputedStyle?window.getComputedStyle(r):r.currentStyle,l={left:parseInt(o.paddingLeft),right:parseInt(o.paddingRight)};return!isNaN(l.left)&&!isNaN(l.right)&&(t.cachedPaddingH=l),l}function er(t){return Ri-t.display.nativeBarWidth}function hi(t){return t.display.scroller.clientWidth-er(t)-t.display.barWidth}function _u(t){return t.display.scroller.clientHeight-er(t)-t.display.barHeight}function ky(t,r,o){var l=t.options.lineWrapping,s=l&&hi(t);if(!r.measure.heights||l&&r.measure.width!=s){var f=r.measure.heights=[];if(l){r.measure.width=s;for(var d=r.text.firstChild.getClientRects(),p=0;p<d.length-1;p++){var g=d[p],y=d[p+1];Math.abs(g.bottom-y.bottom)>2&&f.push((g.bottom+y.top)/2-o.top)}}f.push(o.bottom-o.top)}}function oh(t,r,o){if(t.line==r)return{map:t.measure.map,cache:t.measure.cache};if(t.rest){for(var l=0;l<t.rest.length;l++)if(t.rest[l]==r)return{map:t.measure.maps[l],cache:t.measure.caches[l]};for(var s=0;s<t.rest.length;s++)if(Re(t.rest[s])>o)return{map:t.measure.maps[s],cache:t.measure.caches[s],before:!0}}}function Cy(t,r){r=Rn(r);var o=Re(r),l=t.display.externalMeasured=new Jd(t.doc,r,o);l.lineN=o;var s=l.built=Xd(t,l);return l.text=s.pre,le(t.display.lineMeasure,s.pre),l}function sh(t,r,o,l){return tr(t,ji(t,r),o,l)}function Du(t,r){if(r>=t.display.viewFrom&&r<t.display.viewTo)return t.display.view[gi(t,r)];var o=t.display.externalMeasured;if(o&&r>=o.lineN&&r<o.lineN+o.size)return o}function ji(t,r){var o=Re(r),l=Du(t,o);l&&!l.text?l=null:l&&l.changes&&(eh(t,l,o,Au(t)),t.curOp.forceUpdate=!0),l||(l=Cy(t,r));var s=oh(l,r,o);return{line:r,view:l,rect:null,map:s.map,cache:s.cache,before:s.before,hasHeights:!1}}function tr(t,r,o,l,s){r.before&&(o=-1);var f=o+(l||""),d;return r.cache.hasOwnProperty(f)?d=r.cache[f]:(r.rect||(r.rect=r.view.text.getBoundingClientRect()),r.hasHeights||(ky(t,r.view,r.rect),r.hasHeights=!0),d=Ly(t,r,o,l),d.bogus||(r.cache[f]=d)),{left:d.left,right:d.right,top:s?d.rtop:d.top,bottom:s?d.rbottom:d.bottom}}var ah={left:0,right:0,top:0,bottom:0};function uh(t,r,o){for(var l,s,f,d,p,g,y=0;y<t.length;y+=3)if(p=t[y],g=t[y+1],r<p?(s=0,f=1,d="left"):r<g?(s=r-p,f=s+1):(y==t.length-3||r==g&&t[y+3]>r)&&(f=g-p,s=f-1,r>=g&&(d="right")),s!=null){if(l=t[y+2],p==g&&o==(l.insertLeft?"left":"right")&&(d=o),o=="left"&&s==0)for(;y&&t[y-2]==t[y-3]&&t[y-1].insertLeft;)l=t[(y-=3)+2],d="left";if(o=="right"&&s==g-p)for(;y<t.length-3&&t[y+3]==t[y+4]&&!t[y+5].insertLeft;)l=t[(y+=3)+2],d="right";break}return{node:l,start:s,end:f,collapse:d,coverStart:p,coverEnd:g}}function Ty(t,r){var o=ah;if(r=="left")for(var l=0;l<t.length&&(o=t[l]).left==o.right;l++);else for(var s=t.length-1;s>=0&&(o=t[s]).left==o.right;s--);return o}function Ly(t,r,o,l){var s=uh(r.map,o,l),f=s.node,d=s.start,p=s.end,g=s.collapse,y;if(f.nodeType==3){for(var k=0;k<4;k++){for(;d&&he(r.line.text.charAt(s.coverStart+d));)--d;for(;s.coverStart+p<s.coverEnd&&he(r.line.text.charAt(s.coverStart+p));)++p;if(m&&x<9&&d==0&&p==s.coverEnd-s.coverStart?y=f.parentNode.getBoundingClientRect():y=Ty(ee(f,d,p).getClientRects(),l),y.left||y.right||d==0)break;p=d,d=d-1,g="right"}m&&x<11&&(y=Ny(t.display.measure,y))}else{d>0&&(g=l="right");var N;t.options.lineWrapping&&(N=f.getClientRects()).length>1?y=N[l=="right"?N.length-1:0]:y=f.getBoundingClientRect()}if(m&&x<9&&!d&&(!y||!y.left&&!y.right)){var I=f.parentNode.getClientRects()[0];I?y={left:I.left,right:I.left+Ki(t.display),top:I.top,bottom:I.bottom}:y=ah}for(var O=y.top-r.rect.top,$=y.bottom-r.rect.top,q=(O+$)/2,J=r.view.measure.heights,re=0;re<J.length-1&&!(q<J[re]);re++);var se=re?J[re-1]:0,ie=J[re],ae={left:(g=="right"?y.right:y.left)-r.rect.left,right:(g=="left"?y.left:y.right)-r.rect.left,top:se,bottom:ie};return!y.left&&!y.right&&(ae.bogus=!0),t.options.singleCursorHeightPerLine||(ae.rtop=O,ae.rbottom=$),ae}function Ny(t,r){if(!window.screen||screen.logicalXDPI==null||screen.logicalXDPI==screen.deviceXDPI||!ps(t))return r;var o=screen.logicalXDPI/screen.deviceXDPI,l=screen.logicalYDPI/screen.deviceYDPI;return{left:r.left*o,right:r.right*o,top:r.top*l,bottom:r.bottom*l}}function fh(t){if(t.measure&&(t.measure.cache={},t.measure.heights=null,t.rest))for(var r=0;r<t.rest.length;r++)t.measure.caches[r]={}}function ch(t){t.display.externalMeasure=null,F(t.display.lineMeasure);for(var r=0;r<t.display.view.length;r++)fh(t.display.view[r])}function Ql(t){ch(t),t.display.cachedCharWidth=t.display.cachedTextHeight=t.display.cachedPaddingH=null,t.options.lineWrapping||(t.display.maxLineChanged=!0),t.display.lineNumChars=null}function dh(t){return P&&M?-(t.body.getBoundingClientRect().left-parseInt(getComputedStyle(t.body).marginLeft)):t.defaultView.pageXOffset||(t.documentElement||t.body).scrollLeft}function hh(t){return P&&M?-(t.body.getBoundingClientRect().top-parseInt(getComputedStyle(t.body).marginTop)):t.defaultView.pageYOffset||(t.documentElement||t.body).scrollTop}function Pu(t){var r=Rn(t),o=r.widgets,l=0;if(o)for(var s=0;s<o.length;++s)o[s].above&&(l+=Vl(o[s]));return l}function Ls(t,r,o,l,s){if(!s){var f=Pu(r);o.top+=f,o.bottom+=f}if(l=="line")return o;l||(l="local");var d=gr(r);if(l=="local"?d+=Ts(t.display):d-=t.display.viewOffset,l=="page"||l=="window"){var p=t.display.lineSpace.getBoundingClientRect();d+=p.top+(l=="window"?0:hh(Se(t)));var g=p.left+(l=="window"?0:dh(Se(t)));o.left+=g,o.right+=g}return o.top+=d,o.bottom+=d,o}function ph(t,r,o){if(o=="div")return r;var l=r.left,s=r.top;if(o=="page")l-=dh(Se(t)),s-=hh(Se(t));else if(o=="local"||!o){var f=t.display.sizer.getBoundingClientRect();l+=f.left,s+=f.top}var d=t.display.lineSpace.getBoundingClientRect();return{left:l-d.left,top:s-d.top}}function Ns(t,r,o,l,s){return l||(l=de(t.doc,r.line)),Ls(t,l,sh(t,l,r.ch,s),o)}function Fn(t,r,o,l,s,f){l=l||de(t.doc,r.line),s||(s=ji(t,l));function d($,q){var J=tr(t,s,$,q?"right":"left",f);return q?J.left=J.right:J.right=J.left,Ls(t,l,J,o)}var p=Ln(l,t.doc.direction),g=r.ch,y=r.sticky;if(g>=l.text.length?(g=l.text.length,y="before"):g<=0&&(g=0,y="after"),!p)return d(y=="before"?g-1:g,y=="before");function k($,q,J){var re=p[q],se=re.level==1;return d(J?$-1:$,se!=J)}var N=Tn(p,g,y),I=Cn,O=k(g,N,y=="before");return I!=null&&(O.other=k(g,I,y!="before")),O}function vh(t,r){var o=0;r=Le(t.doc,r),t.options.lineWrapping||(o=Ki(t.display)*r.ch);var l=de(t.doc,r.line),s=gr(l)+Ts(t.display);return{left:o,right:o,top:s,bottom:s+l.height}}function Ou(t,r,o,l,s){var f=D(t,r,o);return f.xRel=s,l&&(f.outside=l),f}function zu(t,r,o){var l=t.doc;if(o+=t.display.viewOffset,o<0)return Ou(l.first,0,null,-1,-1);var s=Zn(l,o),f=l.first+l.size-1;if(s>f)return Ou(l.first+l.size-1,de(l,f).text.length,null,1,1);r<0&&(r=0);for(var d=de(l,s);;){var p=Ey(t,d,s,r,o),g=ty(d,p.ch+(p.xRel>0||p.outside>0?1:0));if(!g)return p;var y=g.find(1);if(y.line==s)return y;d=de(l,s=y.line)}}function gh(t,r,o,l){l-=Pu(r);var s=r.text.length,f=Ee(function(d){return tr(t,o,d-1).bottom<=l},s,0);return s=Ee(function(d){return tr(t,o,d).top>l},f,s),{begin:f,end:s}}function mh(t,r,o,l){o||(o=ji(t,r));var s=Ls(t,r,tr(t,o,l),"line").top;return gh(t,r,o,s)}function Iu(t,r,o,l){return t.bottom<=o?!1:t.top>o?!0:(l?t.left:t.right)>r}function Ey(t,r,o,l,s){s-=gr(r);var f=ji(t,r),d=Pu(r),p=0,g=r.text.length,y=!0,k=Ln(r,t.doc.direction);if(k){var N=(t.options.lineWrapping?My:by)(t,r,o,f,k,l,s);y=N.level!=1,p=y?N.from:N.to-1,g=y?N.to:N.from-1}var I=null,O=null,$=Ee(function(me){var ve=tr(t,f,me);return ve.top+=d,ve.bottom+=d,Iu(ve,l,s,!1)?(ve.top<=s&&ve.left<=l&&(I=me,O=ve),!0):!1},p,g),q,J,re=!1;if(O){var se=l-O.left<O.right-l,ie=se==y;$=I+(ie?0:1),J=ie?"after":"before",q=se?O.left:O.right}else{!y&&($==g||$==p)&&$++,J=$==0?"after":$==r.text.length?"before":tr(t,f,$-(y?1:0)).bottom+d<=s==y?"after":"before";var ae=Fn(t,D(o,$,J),"line",r,f);q=ae.left,re=s<ae.top?-1:s>=ae.bottom?1:0}return $=De(r.text,$,1),Ou(o,$,J,re,l-q)}function by(t,r,o,l,s,f,d){var p=Ee(function(N){var I=s[N],O=I.level!=1;return Iu(Fn(t,D(o,O?I.to:I.from,O?"before":"after"),"line",r,l),f,d,!0)},0,s.length-1),g=s[p];if(p>0){var y=g.level!=1,k=Fn(t,D(o,y?g.from:g.to,y?"after":"before"),"line",r,l);Iu(k,f,d,!0)&&k.top>d&&(g=s[p-1])}return g}function My(t,r,o,l,s,f,d){var p=gh(t,r,l,d),g=p.begin,y=p.end;/\s/.test(r.text.charAt(y-1))&&y--;for(var k=null,N=null,I=0;I<s.length;I++){var O=s[I];if(!(O.from>=y||O.to<=g)){var $=O.level!=1,q=tr(t,l,$?Math.min(y,O.to)-1:Math.max(g,O.from)).right,J=q<f?f-q+1e9:q-f;(!k||N>J)&&(k=O,N=J)}}return k||(k=s[s.length-1]),k.from<g&&(k={from:g,to:k.to,level:k.level}),k.to>y&&(k={from:k.from,to:y,level:k.level}),k}var pi;function $i(t){if(t.cachedTextHeight!=null)return t.cachedTextHeight;if(pi==null){pi=_("pre",null,"CodeMirror-line-like");for(var r=0;r<49;++r)pi.appendChild(document.createTextNode("x")),pi.appendChild(_("br"));pi.appendChild(document.createTextNode("x"))}le(t.measure,pi);var o=pi.offsetHeight/50;return o>3&&(t.cachedTextHeight=o),F(t.measure),o||1}function Ki(t){if(t.cachedCharWidth!=null)return t.cachedCharWidth;var r=_("span","xxxxxxxxxx"),o=_("pre",[r],"CodeMirror-line-like");le(t.measure,o);var l=r.getBoundingClientRect(),s=(l.right-l.left)/10;return s>2&&(t.cachedCharWidth=s),s||10}function Au(t){for(var r=t.display,o={},l={},s=r.gutters.clientLeft,f=r.gutters.firstChild,d=0;f;f=f.nextSibling,++d){var p=t.display.gutterSpecs[d].className;o[p]=f.offsetLeft+f.clientLeft+s,l[p]=f.clientWidth}return{fixedPos:Ru(r),gutterTotalWidth:r.gutters.offsetWidth,gutterLeft:o,gutterWidth:l,wrapperWidth:r.wrapper.clientWidth}}function Ru(t){return t.scroller.getBoundingClientRect().left-t.sizer.getBoundingClientRect().left}function yh(t){var r=$i(t.display),o=t.options.lineWrapping,l=o&&Math.max(5,t.display.scroller.clientWidth/Ki(t.display)-3);return function(s){if(Rr(t.doc,s))return 0;var f=0;if(s.widgets)for(var d=0;d<s.widgets.length;d++)s.widgets[d].height&&(f+=s.widgets[d].height);return o?f+(Math.ceil(s.text.length/l)||1)*r:f+r}}function Fu(t){var r=t.doc,o=yh(t);r.iter(function(l){var s=o(l);s!=l.height&&En(l,s)})}function vi(t,r,o,l){var s=t.display;if(!o&&$t(r).getAttribute("cm-not-content")=="true")return null;var f,d,p=s.lineSpace.getBoundingClientRect();try{f=r.clientX-p.left,d=r.clientY-p.top}catch{return null}var g=zu(t,f,d),y;if(l&&g.xRel>0&&(y=de(t.doc,g.line).text).length==g.ch){var k=ce(y,y.length,t.options.tabSize)-y.length;g=D(g.line,Math.max(0,Math.round((f-lh(t.display).left)/Ki(t.display))-k))}return g}function gi(t,r){if(r>=t.display.viewTo||(r-=t.display.viewFrom,r<0))return null;for(var o=t.display.view,l=0;l<o.length;l++)if(r-=o[l].size,r<0)return l}function Kt(t,r,o,l){r==null&&(r=t.doc.first),o==null&&(o=t.doc.first+t.doc.size),l||(l=0);var s=t.display;if(l&&o<s.viewTo&&(s.updateLineNumbers==null||s.updateLineNumbers>r)&&(s.updateLineNumbers=r),t.curOp.viewChanged=!0,r>=s.viewTo)vr&&Lu(t.doc,r)<s.viewTo&&Wr(t);else if(o<=s.viewFrom)vr&&Qd(t.doc,o+l)>s.viewFrom?Wr(t):(s.viewFrom+=l,s.viewTo+=l);else if(r<=s.viewFrom&&o>=s.viewTo)Wr(t);else if(r<=s.viewFrom){var f=Es(t,o,o+l,1);f?(s.view=s.view.slice(f.index),s.viewFrom=f.lineN,s.viewTo+=l):Wr(t)}else if(o>=s.viewTo){var d=Es(t,r,r,-1);d?(s.view=s.view.slice(0,d.index),s.viewTo=d.lineN):Wr(t)}else{var p=Es(t,r,r,-1),g=Es(t,o,o+l,1);p&&g?(s.view=s.view.slice(0,p.index).concat(Cs(t,p.lineN,g.lineN)).concat(s.view.slice(g.index)),s.viewTo+=l):Wr(t)}var y=s.externalMeasured;y&&(o<y.lineN?y.lineN+=l:r<y.lineN+y.size&&(s.externalMeasured=null))}function Fr(t,r,o){t.curOp.viewChanged=!0;var l=t.display,s=t.display.externalMeasured;if(s&&r>=s.lineN&&r<s.lineN+s.size&&(l.externalMeasured=null),!(r<l.viewFrom||r>=l.viewTo)){var f=l.view[gi(t,r)];if(f.node!=null){var d=f.changes||(f.changes=[]);be(d,o)==-1&&d.push(o)}}}function Wr(t){t.display.viewFrom=t.display.viewTo=t.doc.first,t.display.view=[],t.display.viewOffset=0}function Es(t,r,o,l){var s=gi(t,r),f,d=t.display.view;if(!vr||o==t.doc.first+t.doc.size)return{index:s,lineN:o};for(var p=t.display.viewFrom,g=0;g<s;g++)p+=d[g].size;if(p!=r){if(l>0){if(s==d.length-1)return null;f=p+d[s].size-r,s++}else f=p-r;r+=f,o+=f}for(;Lu(t.doc,o)!=o;){if(s==(l<0?0:d.length-1))return null;o+=l*d[s-(l<0?1:0)].size,s+=l}return{index:s,lineN:o}}function _y(t,r,o){var l=t.display,s=l.view;s.length==0||r>=l.viewTo||o<=l.viewFrom?(l.view=Cs(t,r,o),l.viewFrom=r):(l.viewFrom>r?l.view=Cs(t,r,l.viewFrom).concat(l.view):l.viewFrom<r&&(l.view=l.view.slice(gi(t,r))),l.viewFrom=r,l.viewTo<o?l.view=l.view.concat(Cs(t,l.viewTo,o)):l.viewTo>o&&(l.view=l.view.slice(0,gi(t,o)))),l.viewTo=o}function wh(t){for(var r=t.display.view,o=0,l=0;l<r.length;l++){var s=r[l];!s.hidden&&(!s.node||s.changes)&&++o}return o}function Yl(t){t.display.input.showSelection(t.display.input.prepareSelection())}function xh(t,r){r===void 0&&(r=!0);var o=t.doc,l={},s=l.cursors=document.createDocumentFragment(),f=l.selection=document.createDocumentFragment(),d=t.options.$customCursor;d&&(r=!0);for(var p=0;p<o.sel.ranges.length;p++)if(!(!r&&p==o.sel.primIndex)){var g=o.sel.ranges[p];if(!(g.from().line>=t.display.viewTo||g.to().line<t.display.viewFrom)){var y=g.empty();if(d){var k=d(t,g);k&&Wu(t,k,s)}else(y||t.options.showCursorWhenSelecting)&&Wu(t,g.head,s);y||Dy(t,g,f)}}return l}function Wu(t,r,o){var l=Fn(t,r,"div",null,null,!t.options.singleCursorHeightPerLine),s=o.appendChild(_("div"," ","CodeMirror-cursor"));if(s.style.left=l.left+"px",s.style.top=l.top+"px",s.style.height=Math.max(0,l.bottom-l.top)*t.options.cursorHeight+"px",/\bcm-fat-cursor\b/.test(t.getWrapperElement().className)){var f=Ns(t,r,"div",null,null),d=f.right-f.left;s.style.width=(d>0?d:t.defaultCharWidth())+"px"}if(l.other){var p=o.appendChild(_("div"," ","CodeMirror-cursor CodeMirror-secondarycursor"));p.style.display="",p.style.left=l.other.left+"px",p.style.top=l.other.top+"px",p.style.height=(l.other.bottom-l.other.top)*.85+"px"}}function bs(t,r){return t.top-r.top||t.left-r.left}function Dy(t,r,o){var l=t.display,s=t.doc,f=document.createDocumentFragment(),d=lh(t.display),p=d.left,g=Math.max(l.sizerWidth,hi(t)-l.sizer.offsetLeft)-d.right,y=s.direction=="ltr";function k(ie,ae,me,ve){ae<0&&(ae=0),ae=Math.round(ae),ve=Math.round(ve),f.appendChild(_("div",null,"CodeMirror-selected","position: absolute; left: "+ie+`px;
                             top: `+ae+"px; width: "+(me??g-ie)+`px;
                             height: `+(ve-ae)+"px"))}function N(ie,ae,me){var ve=de(s,ie),Me=ve.text.length,Ue,wt;function Xe(st,Vt){return Ns(t,D(ie,st),"div",ve,Vt)}function rn(st,Vt,Lt){var ht=mh(t,ve,null,st),at=Vt=="ltr"==(Lt=="after")?"left":"right",tt=Lt=="after"?ht.begin:ht.end-(/\s/.test(ve.text.charAt(ht.end-1))?2:1);return Xe(tt,at)[at]}var qt=Ln(ve,s.direction);return hn(qt,ae||0,me??Me,function(st,Vt,Lt,ht){var at=Lt=="ltr",tt=Xe(st,at?"left":"right"),Qt=Xe(Vt-1,at?"right":"left"),il=ae==null&&st==0,Kr=me==null&&Vt==Me,_t=ht==0,nr=!qt||ht==qt.length-1;if(Qt.top-tt.top<=3){var xt=(y?il:Kr)&&_t,df=(y?Kr:il)&&nr,xr=xt?p:(at?tt:Qt).left,Si=df?g:(at?Qt:tt).right;k(xr,tt.top,Si-xr,tt.bottom)}else{var ki,Rt,ll,hf;at?(ki=y&&il&&_t?p:tt.left,Rt=y?g:rn(st,Lt,"before"),ll=y?p:rn(Vt,Lt,"after"),hf=y&&Kr&&nr?g:Qt.right):(ki=y?rn(st,Lt,"before"):p,Rt=!y&&il&&_t?g:tt.right,ll=!y&&Kr&&nr?p:Qt.left,hf=y?rn(Vt,Lt,"after"):g),k(ki,tt.top,Rt-ki,tt.bottom),tt.bottom<Qt.top&&k(p,tt.bottom,null,Qt.top),k(ll,Qt.top,hf-ll,Qt.bottom)}(!Ue||bs(tt,Ue)<0)&&(Ue=tt),bs(Qt,Ue)<0&&(Ue=Qt),(!wt||bs(tt,wt)<0)&&(wt=tt),bs(Qt,wt)<0&&(wt=Qt)}),{start:Ue,end:wt}}var I=r.from(),O=r.to();if(I.line==O.line)N(I.line,I.ch,O.ch);else{var $=de(s,I.line),q=de(s,O.line),J=Rn($)==Rn(q),re=N(I.line,I.ch,J?$.text.length+1:null).end,se=N(O.line,J?0:null,O.ch).start;J&&(re.top<se.top-2?(k(re.right,re.top,null,re.bottom),k(p,se.top,se.left,se.bottom)):k(re.right,re.top,se.left-re.right,re.bottom)),re.bottom<se.top&&k(p,re.bottom,null,se.top)}o.appendChild(f)}function Bu(t){if(t.state.focused){var r=t.display;clearInterval(r.blinker);var o=!0;r.cursorDiv.style.visibility="",t.options.cursorBlinkRate>0?r.blinker=setInterval(function(){t.hasFocus()||Gi(t),r.cursorDiv.style.visibility=(o=!o)?"":"hidden"},t.options.cursorBlinkRate):t.options.cursorBlinkRate<0&&(r.cursorDiv.style.visibility="hidden")}}function Sh(t){t.hasFocus()||(t.display.input.focus(),t.state.focused||Uu(t))}function Hu(t){t.state.delayingBlurEvent=!0,setTimeout(function(){t.state.delayingBlurEvent&&(t.state.delayingBlurEvent=!1,t.state.focused&&Gi(t))},100)}function Uu(t,r){t.state.delayingBlurEvent&&!t.state.draggingText&&(t.state.delayingBlurEvent=!1),t.options.readOnly!="nocursor"&&(t.state.focused||($e(t,"focus",t,r),t.state.focused=!0,U(t.display.wrapper,"CodeMirror-focused"),!t.curOp&&t.display.selForContextMenu!=t.doc.sel&&(t.display.input.reset(),L&&setTimeout(function(){return t.display.input.reset(!0)},20)),t.display.input.receivedFocus()),Bu(t))}function Gi(t,r){t.state.delayingBlurEvent||(t.state.focused&&($e(t,"blur",t,r),t.state.focused=!1,V(t.display.wrapper,"CodeMirror-focused")),clearInterval(t.display.blinker),setTimeout(function(){t.state.focused||(t.display.shift=!1)},150))}function Ms(t){for(var r=t.display,o=r.lineDiv.offsetTop,l=Math.max(0,r.scroller.getBoundingClientRect().top),s=r.lineDiv.getBoundingClientRect().top,f=0,d=0;d<r.view.length;d++){var p=r.view[d],g=t.options.lineWrapping,y=void 0,k=0;if(!p.hidden){if(s+=p.line.height,m&&x<8){var N=p.node.offsetTop+p.node.offsetHeight;y=N-o,o=N}else{var I=p.node.getBoundingClientRect();y=I.bottom-I.top,!g&&p.text.firstChild&&(k=p.text.firstChild.getBoundingClientRect().right-I.left-1)}var O=p.line.height-y;if((O>.005||O<-.005)&&(s<l&&(f-=O),En(p.line,y),kh(p.line),p.rest))for(var $=0;$<p.rest.length;$++)kh(p.rest[$]);if(k>t.display.sizerWidth){var q=Math.ceil(k/Ki(t.display));q>t.display.maxLineLength&&(t.display.maxLineLength=q,t.display.maxLine=p.line,t.display.maxLineChanged=!0)}}}Math.abs(f)>2&&(r.scroller.scrollTop+=f)}function kh(t){if(t.widgets)for(var r=0;r<t.widgets.length;++r){var o=t.widgets[r],l=o.node.parentNode;l&&(o.height=l.offsetHeight)}}function _s(t,r,o){var l=o&&o.top!=null?Math.max(0,o.top):t.scroller.scrollTop;l=Math.floor(l-Ts(t));var s=o&&o.bottom!=null?o.bottom:l+t.wrapper.clientHeight,f=Zn(r,l),d=Zn(r,s);if(o&&o.ensure){var p=o.ensure.from.line,g=o.ensure.to.line;p<f?(f=p,d=Zn(r,gr(de(r,p))+t.wrapper.clientHeight)):Math.min(g,r.lastLine())>=d&&(f=Zn(r,gr(de(r,g))-t.wrapper.clientHeight),d=g)}return{from:f,to:Math.max(d,f+1)}}function Py(t,r){if(!Je(t,"scrollCursorIntoView")){var o=t.display,l=o.sizer.getBoundingClientRect(),s=null,f=o.wrapper.ownerDocument;if(r.top+l.top<0?s=!0:r.bottom+l.top>(f.defaultView.innerHeight||f.documentElement.clientHeight)&&(s=!1),s!=null&&!T){var d=_("div","​",null,`position: absolute;
                         top: `+(r.top-o.viewOffset-Ts(t.display))+`px;
                         height: `+(r.bottom-r.top+er(t)+o.barHeight)+`px;
                         left: `+r.left+"px; width: "+Math.max(2,r.right-r.left)+"px;");t.display.lineSpace.appendChild(d),d.scrollIntoView(s),t.display.lineSpace.removeChild(d)}}}function Oy(t,r,o,l){l==null&&(l=0);var s;!t.options.lineWrapping&&r==o&&(o=r.sticky=="before"?D(r.line,r.ch+1,"before"):r,r=r.ch?D(r.line,r.sticky=="before"?r.ch-1:r.ch,"after"):r);for(var f=0;f<5;f++){var d=!1,p=Fn(t,r),g=!o||o==r?p:Fn(t,o);s={left:Math.min(p.left,g.left),top:Math.min(p.top,g.top)-l,right:Math.max(p.left,g.left),bottom:Math.max(p.bottom,g.bottom)+l};var y=ju(t,s),k=t.doc.scrollTop,N=t.doc.scrollLeft;if(y.scrollTop!=null&&(Zl(t,y.scrollTop),Math.abs(t.doc.scrollTop-k)>1&&(d=!0)),y.scrollLeft!=null&&(mi(t,y.scrollLeft),Math.abs(t.doc.scrollLeft-N)>1&&(d=!0)),!d)break}return s}function zy(t,r){var o=ju(t,r);o.scrollTop!=null&&Zl(t,o.scrollTop),o.scrollLeft!=null&&mi(t,o.scrollLeft)}function ju(t,r){var o=t.display,l=$i(t.display);r.top<0&&(r.top=0);var s=t.curOp&&t.curOp.scrollTop!=null?t.curOp.scrollTop:o.scroller.scrollTop,f=_u(t),d={};r.bottom-r.top>f&&(r.bottom=r.top+f);var p=t.doc.height+Mu(o),g=r.top<l,y=r.bottom>p-l;if(r.top<s)d.scrollTop=g?0:r.top;else if(r.bottom>s+f){var k=Math.min(r.top,(y?p:r.bottom)-f);k!=s&&(d.scrollTop=k)}var N=t.options.fixedGutter?0:o.gutters.offsetWidth,I=t.curOp&&t.curOp.scrollLeft!=null?t.curOp.scrollLeft:o.scroller.scrollLeft-N,O=hi(t)-o.gutters.offsetWidth,$=r.right-r.left>O;return $&&(r.right=r.left+O),r.left<10?d.scrollLeft=0:r.left<I?d.scrollLeft=Math.max(0,r.left+N-($?0:10)):r.right>O+I-3&&(d.scrollLeft=r.right+($?0:10)-O),d}function $u(t,r){r!=null&&(Ds(t),t.curOp.scrollTop=(t.curOp.scrollTop==null?t.doc.scrollTop:t.curOp.scrollTop)+r)}function qi(t){Ds(t);var r=t.getCursor();t.curOp.scrollToPos={from:r,to:r,margin:t.options.cursorScrollMargin}}function Xl(t,r,o){(r!=null||o!=null)&&Ds(t),r!=null&&(t.curOp.scrollLeft=r),o!=null&&(t.curOp.scrollTop=o)}function Iy(t,r){Ds(t),t.curOp.scrollToPos=r}function Ds(t){var r=t.curOp.scrollToPos;if(r){t.curOp.scrollToPos=null;var o=vh(t,r.from),l=vh(t,r.to);Ch(t,o,l,r.margin)}}function Ch(t,r,o,l){var s=ju(t,{left:Math.min(r.left,o.left),top:Math.min(r.top,o.top)-l,right:Math.max(r.right,o.right),bottom:Math.max(r.bottom,o.bottom)+l});Xl(t,s.scrollLeft,s.scrollTop)}function Zl(t,r){Math.abs(t.doc.scrollTop-r)<2||(u||Gu(t,{top:r}),Th(t,r,!0),u&&Gu(t),to(t,100))}function Th(t,r,o){r=Math.max(0,Math.min(t.display.scroller.scrollHeight-t.display.scroller.clientHeight,r)),!(t.display.scroller.scrollTop==r&&!o)&&(t.doc.scrollTop=r,t.display.scrollbars.setScrollTop(r),t.display.scroller.scrollTop!=r&&(t.display.scroller.scrollTop=r))}function mi(t,r,o,l){r=Math.max(0,Math.min(r,t.display.scroller.scrollWidth-t.display.scroller.clientWidth)),!((o?r==t.doc.scrollLeft:Math.abs(t.doc.scrollLeft-r)<2)&&!l)&&(t.doc.scrollLeft=r,Mh(t),t.display.scroller.scrollLeft!=r&&(t.display.scroller.scrollLeft=r),t.display.scrollbars.setScrollLeft(r))}function Jl(t){var r=t.display,o=r.gutters.offsetWidth,l=Math.round(t.doc.height+Mu(t.display));return{clientHeight:r.scroller.clientHeight,viewHeight:r.wrapper.clientHeight,scrollWidth:r.scroller.scrollWidth,clientWidth:r.scroller.clientWidth,viewWidth:r.wrapper.clientWidth,barLeft:t.options.fixedGutter?o:0,docHeight:l,scrollHeight:l+er(t)+r.barHeight,nativeBarWidth:r.nativeBarWidth,gutterWidth:o}}var yi=function(t,r,o){this.cm=o;var l=this.vert=_("div",[_("div",null,null,"min-width: 1px")],"CodeMirror-vscrollbar"),s=this.horiz=_("div",[_("div",null,null,"height: 100%; min-height: 1px")],"CodeMirror-hscrollbar");l.tabIndex=s.tabIndex=-1,t(l),t(s),oe(l,"scroll",function(){l.clientHeight&&r(l.scrollTop,"vertical")}),oe(s,"scroll",function(){s.clientWidth&&r(s.scrollLeft,"horizontal")}),this.checkedZeroWidth=!1,m&&x<8&&(this.horiz.style.minHeight=this.vert.style.minWidth="18px")};yi.prototype.update=function(t){var r=t.scrollWidth>t.clientWidth+1,o=t.scrollHeight>t.clientHeight+1,l=t.nativeBarWidth;if(o){this.vert.style.display="block",this.vert.style.bottom=r?l+"px":"0";var s=t.viewHeight-(r?l:0);this.vert.firstChild.style.height=Math.max(0,t.scrollHeight-t.clientHeight+s)+"px"}else this.vert.scrollTop=0,this.vert.style.display="",this.vert.firstChild.style.height="0";if(r){this.horiz.style.display="block",this.horiz.style.right=o?l+"px":"0",this.horiz.style.left=t.barLeft+"px";var f=t.viewWidth-t.barLeft-(o?l:0);this.horiz.firstChild.style.width=Math.max(0,t.scrollWidth-t.clientWidth+f)+"px"}else this.horiz.style.display="",this.horiz.firstChild.style.width="0";return!this.checkedZeroWidth&&t.clientHeight>0&&(l==0&&this.zeroWidthHack(),this.checkedZeroWidth=!0),{right:o?l:0,bottom:r?l:0}},yi.prototype.setScrollLeft=function(t){this.horiz.scrollLeft!=t&&(this.horiz.scrollLeft=t),this.disableHoriz&&this.enableZeroWidthBar(this.horiz,this.disableHoriz,"horiz")},yi.prototype.setScrollTop=function(t){this.vert.scrollTop!=t&&(this.vert.scrollTop=t),this.disableVert&&this.enableZeroWidthBar(this.vert,this.disableVert,"vert")},yi.prototype.zeroWidthHack=function(){var t=G&&!K?"12px":"18px";this.horiz.style.height=this.vert.style.width=t,this.horiz.style.visibility=this.vert.style.visibility="hidden",this.disableHoriz=new Ne,this.disableVert=new Ne},yi.prototype.enableZeroWidthBar=function(t,r,o){t.style.visibility="";function l(){var s=t.getBoundingClientRect(),f=o=="vert"?document.elementFromPoint(s.right-1,(s.top+s.bottom)/2):document.elementFromPoint((s.right+s.left)/2,s.bottom-1);f!=t?t.style.visibility="hidden":r.set(1e3,l)}r.set(1e3,l)},yi.prototype.clear=function(){var t=this.horiz.parentNode;t.removeChild(this.horiz),t.removeChild(this.vert)};var eo=function(){};eo.prototype.update=function(){return{bottom:0,right:0}},eo.prototype.setScrollLeft=function(){},eo.prototype.setScrollTop=function(){},eo.prototype.clear=function(){};function Vi(t,r){r||(r=Jl(t));var o=t.display.barWidth,l=t.display.barHeight;Lh(t,r);for(var s=0;s<4&&o!=t.display.barWidth||l!=t.display.barHeight;s++)o!=t.display.barWidth&&t.options.lineWrapping&&Ms(t),Lh(t,Jl(t)),o=t.display.barWidth,l=t.display.barHeight}function Lh(t,r){var o=t.display,l=o.scrollbars.update(r);o.sizer.style.paddingRight=(o.barWidth=l.right)+"px",o.sizer.style.paddingBottom=(o.barHeight=l.bottom)+"px",o.heightForcer.style.borderBottom=l.bottom+"px solid transparent",l.right&&l.bottom?(o.scrollbarFiller.style.display="block",o.scrollbarFiller.style.height=l.bottom+"px",o.scrollbarFiller.style.width=l.right+"px"):o.scrollbarFiller.style.display="",l.bottom&&t.options.coverGutterNextToScrollbar&&t.options.fixedGutter?(o.gutterFiller.style.display="block",o.gutterFiller.style.height=l.bottom+"px",o.gutterFiller.style.width=r.gutterWidth+"px"):o.gutterFiller.style.display=""}var Nh={native:yi,null:eo};function Eh(t){t.display.scrollbars&&(t.display.scrollbars.clear(),t.display.scrollbars.addClass&&V(t.display.wrapper,t.display.scrollbars.addClass)),t.display.scrollbars=new Nh[t.options.scrollbarStyle](function(r){t.display.wrapper.insertBefore(r,t.display.scrollbarFiller),oe(r,"mousedown",function(){t.state.focused&&setTimeout(function(){return t.display.input.focus()},0)}),r.setAttribute("cm-not-content","true")},function(r,o){o=="horizontal"?mi(t,r):Zl(t,r)},t),t.display.scrollbars.addClass&&U(t.display.wrapper,t.display.scrollbars.addClass)}var Ay=0;function wi(t){t.curOp={cm:t,viewChanged:!1,startHeight:t.doc.height,forceUpdate:!1,updateInput:0,typing:!1,changeObjs:null,cursorActivityHandlers:null,cursorActivityCalled:0,selectionChanged:!1,updateMaxLine:!1,scrollLeft:null,scrollTop:null,scrollToPos:null,focus:!1,id:++Ay,markArrays:null},hy(t.curOp)}function xi(t){var r=t.curOp;r&&vy(r,function(o){for(var l=0;l<o.ops.length;l++)o.ops[l].cm.curOp=null;Ry(o)})}function Ry(t){for(var r=t.ops,o=0;o<r.length;o++)Fy(r[o]);for(var l=0;l<r.length;l++)Wy(r[l]);for(var s=0;s<r.length;s++)By(r[s]);for(var f=0;f<r.length;f++)Hy(r[f]);for(var d=0;d<r.length;d++)Uy(r[d])}function Fy(t){var r=t.cm,o=r.display;$y(r),t.updateMaxLine&&Eu(r),t.mustUpdate=t.viewChanged||t.forceUpdate||t.scrollTop!=null||t.scrollToPos&&(t.scrollToPos.from.line<o.viewFrom||t.scrollToPos.to.line>=o.viewTo)||o.maxLineChanged&&r.options.lineWrapping,t.update=t.mustUpdate&&new Ps(r,t.mustUpdate&&{top:t.scrollTop,ensure:t.scrollToPos},t.forceUpdate)}function Wy(t){t.updatedDisplay=t.mustUpdate&&Ku(t.cm,t.update)}function By(t){var r=t.cm,o=r.display;t.updatedDisplay&&Ms(r),t.barMeasure=Jl(r),o.maxLineChanged&&!r.options.lineWrapping&&(t.adjustWidthTo=sh(r,o.maxLine,o.maxLine.text.length).left+3,r.display.sizerWidth=t.adjustWidthTo,t.barMeasure.scrollWidth=Math.max(o.scroller.clientWidth,o.sizer.offsetLeft+t.adjustWidthTo+er(r)+r.display.barWidth),t.maxScrollLeft=Math.max(0,o.sizer.offsetLeft+t.adjustWidthTo-hi(r))),(t.updatedDisplay||t.selectionChanged)&&(t.preparedSelection=o.input.prepareSelection())}function Hy(t){var r=t.cm;t.adjustWidthTo!=null&&(r.display.sizer.style.minWidth=t.adjustWidthTo+"px",t.maxScrollLeft<r.doc.scrollLeft&&mi(r,Math.min(r.display.scroller.scrollLeft,t.maxScrollLeft),!0),r.display.maxLineChanged=!1);var o=t.focus&&t.focus==b(Se(r));t.preparedSelection&&r.display.input.showSelection(t.preparedSelection,o),(t.updatedDisplay||t.startHeight!=r.doc.height)&&Vi(r,t.barMeasure),t.updatedDisplay&&Vu(r,t.barMeasure),t.selectionChanged&&Bu(r),r.state.focused&&t.updateInput&&r.display.input.reset(t.typing),o&&Sh(t.cm)}function Uy(t){var r=t.cm,o=r.display,l=r.doc;if(t.updatedDisplay&&bh(r,t.update),o.wheelStartX!=null&&(t.scrollTop!=null||t.scrollLeft!=null||t.scrollToPos)&&(o.wheelStartX=o.wheelStartY=null),t.scrollTop!=null&&Th(r,t.scrollTop,t.forceScroll),t.scrollLeft!=null&&mi(r,t.scrollLeft,!0,!0),t.scrollToPos){var s=Oy(r,Le(l,t.scrollToPos.from),Le(l,t.scrollToPos.to),t.scrollToPos.margin);Py(r,s)}var f=t.maybeHiddenMarkers,d=t.maybeUnhiddenMarkers;if(f)for(var p=0;p<f.length;++p)f[p].lines.length||$e(f[p],"hide");if(d)for(var g=0;g<d.length;++g)d[g].lines.length&&$e(d[g],"unhide");o.wrapper.offsetHeight&&(l.scrollTop=r.display.scroller.scrollTop),t.changeObjs&&$e(r,"changes",r,t.changeObjs),t.update&&t.update.finish()}function nn(t,r){if(t.curOp)return r();wi(t);try{return r()}finally{xi(t)}}function mt(t,r){return function(){if(t.curOp)return r.apply(t,arguments);wi(t);try{return r.apply(t,arguments)}finally{xi(t)}}}function At(t){return function(){if(this.curOp)return t.apply(this,arguments);wi(this);try{return t.apply(this,arguments)}finally{xi(this)}}}function yt(t){return function(){var r=this.cm;if(!r||r.curOp)return t.apply(this,arguments);wi(r);try{return t.apply(this,arguments)}finally{xi(r)}}}function to(t,r){t.doc.highlightFrontier<t.display.viewTo&&t.state.highlight.set(r,Ye(jy,t))}function jy(t){var r=t.doc;if(!(r.highlightFrontier>=t.display.viewTo)){var o=+new Date+t.options.workTime,l=$l(t,r.highlightFrontier),s=[];r.iter(l.line,Math.min(r.first+r.size,t.display.viewTo+500),function(f){if(l.line>=t.display.viewFrom){var d=f.styles,p=f.text.length>t.options.maxHighlightLength?hr(r.mode,l.state):null,g=Id(t,f,l,!0);p&&(l.state=p),f.styles=g.styles;var y=f.styleClasses,k=g.classes;k?f.styleClasses=k:y&&(f.styleClasses=null);for(var N=!d||d.length!=f.styles.length||y!=k&&(!y||!k||y.bgClass!=k.bgClass||y.textClass!=k.textClass),I=0;!N&&I<d.length;++I)N=d[I]!=f.styles[I];N&&s.push(l.line),f.stateAfter=l.save(),l.nextLine()}else f.text.length<=t.options.maxHighlightLength&&Su(t,f.text,l),f.stateAfter=l.line%5==0?l.save():null,l.nextLine();if(+new Date>o)return to(t,t.options.workDelay),!0}),r.highlightFrontier=l.line,r.modeFrontier=Math.max(r.modeFrontier,l.line),s.length&&nn(t,function(){for(var f=0;f<s.length;f++)Fr(t,s[f],"text")})}}var Ps=function(t,r,o){var l=t.display;this.viewport=r,this.visible=_s(l,t.doc,r),this.editorIsHidden=!l.wrapper.offsetWidth,this.wrapperHeight=l.wrapper.clientHeight,this.wrapperWidth=l.wrapper.clientWidth,this.oldDisplayWidth=hi(t),this.force=o,this.dims=Au(t),this.events=[]};Ps.prototype.signal=function(t,r){zt(t,r)&&this.events.push(arguments)},Ps.prototype.finish=function(){for(var t=0;t<this.events.length;t++)$e.apply(null,this.events[t])};function $y(t){var r=t.display;!r.scrollbarsClipped&&r.scroller.offsetWidth&&(r.nativeBarWidth=r.scroller.offsetWidth-r.scroller.clientWidth,r.heightForcer.style.height=er(t)+"px",r.sizer.style.marginBottom=-r.nativeBarWidth+"px",r.sizer.style.borderRightWidth=er(t)+"px",r.scrollbarsClipped=!0)}function Ky(t){if(t.hasFocus())return null;var r=b(Se(t));if(!r||!C(t.display.lineDiv,r))return null;var o={activeElt:r};if(window.getSelection){var l=je(t).getSelection();l.anchorNode&&l.extend&&C(t.display.lineDiv,l.anchorNode)&&(o.anchorNode=l.anchorNode,o.anchorOffset=l.anchorOffset,o.focusNode=l.focusNode,o.focusOffset=l.focusOffset)}return o}function Gy(t){if(!(!t||!t.activeElt||t.activeElt==b(t.activeElt.ownerDocument))&&(t.activeElt.focus(),!/^(INPUT|TEXTAREA)$/.test(t.activeElt.nodeName)&&t.anchorNode&&C(document.body,t.anchorNode)&&C(document.body,t.focusNode))){var r=t.activeElt.ownerDocument,o=r.defaultView.getSelection(),l=r.createRange();l.setEnd(t.anchorNode,t.anchorOffset),l.collapse(!1),o.removeAllRanges(),o.addRange(l),o.extend(t.focusNode,t.focusOffset)}}function Ku(t,r){var o=t.display,l=t.doc;if(r.editorIsHidden)return Wr(t),!1;if(!r.force&&r.visible.from>=o.viewFrom&&r.visible.to<=o.viewTo&&(o.updateLineNumbers==null||o.updateLineNumbers>=o.viewTo)&&o.renderedView==o.view&&wh(t)==0)return!1;_h(t)&&(Wr(t),r.dims=Au(t));var s=l.first+l.size,f=Math.max(r.visible.from-t.options.viewportMargin,l.first),d=Math.min(s,r.visible.to+t.options.viewportMargin);o.viewFrom<f&&f-o.viewFrom<20&&(f=Math.max(l.first,o.viewFrom)),o.viewTo>d&&o.viewTo-d<20&&(d=Math.min(s,o.viewTo)),vr&&(f=Lu(t.doc,f),d=Qd(t.doc,d));var p=f!=o.viewFrom||d!=o.viewTo||o.lastWrapHeight!=r.wrapperHeight||o.lastWrapWidth!=r.wrapperWidth;_y(t,f,d),o.viewOffset=gr(de(t.doc,o.viewFrom)),t.display.mover.style.top=o.viewOffset+"px";var g=wh(t);if(!p&&g==0&&!r.force&&o.renderedView==o.view&&(o.updateLineNumbers==null||o.updateLineNumbers>=o.viewTo))return!1;var y=Ky(t);return g>4&&(o.lineDiv.style.display="none"),qy(t,o.updateLineNumbers,r.dims),g>4&&(o.lineDiv.style.display=""),o.renderedView=o.view,Gy(y),F(o.cursorDiv),F(o.selectionDiv),o.gutters.style.height=o.sizer.style.minHeight=0,p&&(o.lastWrapHeight=r.wrapperHeight,o.lastWrapWidth=r.wrapperWidth,to(t,400)),o.updateLineNumbers=null,!0}function bh(t,r){for(var o=r.viewport,l=!0;;l=!1){if(!l||!t.options.lineWrapping||r.oldDisplayWidth==hi(t)){if(o&&o.top!=null&&(o={top:Math.min(t.doc.height+Mu(t.display)-_u(t),o.top)}),r.visible=_s(t.display,t.doc,o),r.visible.from>=t.display.viewFrom&&r.visible.to<=t.display.viewTo)break}else l&&(r.visible=_s(t.display,t.doc,o));if(!Ku(t,r))break;Ms(t);var s=Jl(t);Yl(t),Vi(t,s),Vu(t,s),r.force=!1}r.signal(t,"update",t),(t.display.viewFrom!=t.display.reportedViewFrom||t.display.viewTo!=t.display.reportedViewTo)&&(r.signal(t,"viewportChange",t,t.display.viewFrom,t.display.viewTo),t.display.reportedViewFrom=t.display.viewFrom,t.display.reportedViewTo=t.display.viewTo)}function Gu(t,r){var o=new Ps(t,r);if(Ku(t,o)){Ms(t),bh(t,o);var l=Jl(t);Yl(t),Vi(t,l),Vu(t,l),o.finish()}}function qy(t,r,o){var l=t.display,s=t.options.lineNumbers,f=l.lineDiv,d=f.firstChild;function p($){var q=$.nextSibling;return L&&G&&t.display.currentWheelTarget==$?$.style.display="none":$.parentNode.removeChild($),q}for(var g=l.view,y=l.viewFrom,k=0;k<g.length;k++){var N=g[k];if(!N.hidden)if(!N.node||N.node.parentNode!=f){var I=xy(t,N,y,o);f.insertBefore(I,d)}else{for(;d!=N.node;)d=p(d);var O=s&&r!=null&&r<=y&&N.lineNumber;N.changes&&(be(N.changes,"gutter")>-1&&(O=!1),eh(t,N,y,o)),O&&(F(N.lineNumber),N.lineNumber.appendChild(document.createTextNode(E(t.options,y)))),d=N.node.nextSibling}y+=N.size}for(;d;)d=p(d)}function qu(t){var r=t.gutters.offsetWidth;t.sizer.style.marginLeft=r+"px",gt(t,"gutterChanged",t)}function Vu(t,r){t.display.sizer.style.minHeight=r.docHeight+"px",t.display.heightForcer.style.top=r.docHeight+"px",t.display.gutters.style.height=r.docHeight+t.display.barHeight+er(t)+"px"}function Mh(t){var r=t.display,o=r.view;if(!(!r.alignWidgets&&(!r.gutters.firstChild||!t.options.fixedGutter))){for(var l=Ru(r)-r.scroller.scrollLeft+t.doc.scrollLeft,s=r.gutters.offsetWidth,f=l+"px",d=0;d<o.length;d++)if(!o[d].hidden){t.options.fixedGutter&&(o[d].gutter&&(o[d].gutter.style.left=f),o[d].gutterBackground&&(o[d].gutterBackground.style.left=f));var p=o[d].alignable;if(p)for(var g=0;g<p.length;g++)p[g].style.left=f}t.options.fixedGutter&&(r.gutters.style.left=l+s+"px")}}function _h(t){if(!t.options.lineNumbers)return!1;var r=t.doc,o=E(t.options,r.first+r.size-1),l=t.display;if(o.length!=l.lineNumChars){var s=l.measure.appendChild(_("div",[_("div",o)],"CodeMirror-linenumber CodeMirror-gutter-elt")),f=s.firstChild.offsetWidth,d=s.offsetWidth-f;return l.lineGutter.style.width="",l.lineNumInnerWidth=Math.max(f,l.lineGutter.offsetWidth-d)+1,l.lineNumWidth=l.lineNumInnerWidth+d,l.lineNumChars=l.lineNumInnerWidth?o.length:-1,l.lineGutter.style.width=l.lineNumWidth+"px",qu(t.display),!0}return!1}function Qu(t,r){for(var o=[],l=!1,s=0;s<t.length;s++){var f=t[s],d=null;if(typeof f!="string"&&(d=f.style,f=f.className),f=="CodeMirror-linenumbers")if(r)l=!0;else continue;o.push({className:f,style:d})}return r&&!l&&o.push({className:"CodeMirror-linenumbers",style:null}),o}function Dh(t){var r=t.gutters,o=t.gutterSpecs;F(r),t.lineGutter=null;for(var l=0;l<o.length;++l){var s=o[l],f=s.className,d=s.style,p=r.appendChild(_("div",null,"CodeMirror-gutter "+f));d&&(p.style.cssText=d),f=="CodeMirror-linenumbers"&&(t.lineGutter=p,p.style.width=(t.lineNumWidth||1)+"px")}r.style.display=o.length?"":"none",qu(t)}function no(t){Dh(t.display),Kt(t),Mh(t)}function Vy(t,r,o,l){var s=this;this.input=o,s.scrollbarFiller=_("div",null,"CodeMirror-scrollbar-filler"),s.scrollbarFiller.setAttribute("cm-not-content","true"),s.gutterFiller=_("div",null,"CodeMirror-gutter-filler"),s.gutterFiller.setAttribute("cm-not-content","true"),s.lineDiv=X("div",null,"CodeMirror-code"),s.selectionDiv=_("div",null,null,"position: relative; z-index: 1"),s.cursorDiv=_("div",null,"CodeMirror-cursors"),s.measure=_("div",null,"CodeMirror-measure"),s.lineMeasure=_("div",null,"CodeMirror-measure"),s.lineSpace=X("div",[s.measure,s.lineMeasure,s.selectionDiv,s.cursorDiv,s.lineDiv],null,"position: relative; outline: none");var f=X("div",[s.lineSpace],"CodeMirror-lines");s.mover=_("div",[f],null,"position: relative"),s.sizer=_("div",[s.mover],"CodeMirror-sizer"),s.sizerWidth=null,s.heightForcer=_("div",null,null,"position: absolute; height: "+Ri+"px; width: 1px;"),s.gutters=_("div",null,"CodeMirror-gutters"),s.lineGutter=null,s.scroller=_("div",[s.sizer,s.heightForcer,s.gutters],"CodeMirror-scroll"),s.scroller.setAttribute("tabIndex","-1"),s.wrapper=_("div",[s.scrollbarFiller,s.gutterFiller,s.scroller],"CodeMirror"),P&&W>=105&&(s.wrapper.style.clipPath="inset(0px)"),s.wrapper.setAttribute("translate","no"),m&&x<8&&(s.gutters.style.zIndex=-1,s.scroller.style.paddingRight=0),!L&&!(u&&A)&&(s.scroller.draggable=!0),t&&(t.appendChild?t.appendChild(s.wrapper):t(s.wrapper)),s.viewFrom=s.viewTo=r.first,s.reportedViewFrom=s.reportedViewTo=r.first,s.view=[],s.renderedView=null,s.externalMeasured=null,s.viewOffset=0,s.lastWrapHeight=s.lastWrapWidth=0,s.updateLineNumbers=null,s.nativeBarWidth=s.barHeight=s.barWidth=0,s.scrollbarsClipped=!1,s.lineNumWidth=s.lineNumInnerWidth=s.lineNumChars=null,s.alignWidgets=!1,s.cachedCharWidth=s.cachedTextHeight=s.cachedPaddingH=null,s.maxLine=null,s.maxLineLength=0,s.maxLineChanged=!1,s.wheelDX=s.wheelDY=s.wheelStartX=s.wheelStartY=null,s.shift=!1,s.selForContextMenu=null,s.activeTouch=null,s.gutterSpecs=Qu(l.gutters,l.lineNumbers),Dh(s),o.init(s)}var Os=0,yr=null;m?yr=-.53:u?yr=15:P?yr=-.7:Y&&(yr=-1/3);function Ph(t){var r=t.wheelDeltaX,o=t.wheelDeltaY;return r==null&&t.detail&&t.axis==t.HORIZONTAL_AXIS&&(r=t.detail),o==null&&t.detail&&t.axis==t.VERTICAL_AXIS?o=t.detail:o==null&&(o=t.wheelDelta),{x:r,y:o}}function Qy(t){var r=Ph(t);return r.x*=yr,r.y*=yr,r}function Oh(t,r){P&&W==102&&(t.display.chromeScrollHack==null?t.display.sizer.style.pointerEvents="none":clearTimeout(t.display.chromeScrollHack),t.display.chromeScrollHack=setTimeout(function(){t.display.chromeScrollHack=null,t.display.sizer.style.pointerEvents=""},100));var o=Ph(r),l=o.x,s=o.y,f=yr;r.deltaMode===0&&(l=r.deltaX,s=r.deltaY,f=1);var d=t.display,p=d.scroller,g=p.scrollWidth>p.clientWidth,y=p.scrollHeight>p.clientHeight;if(l&&g||s&&y){if(s&&G&&L){e:for(var k=r.target,N=d.view;k!=p;k=k.parentNode)for(var I=0;I<N.length;I++)if(N[I].node==k){t.display.currentWheelTarget=k;break e}}if(l&&!u&&!B&&f!=null){s&&y&&Zl(t,Math.max(0,p.scrollTop+s*f)),mi(t,Math.max(0,p.scrollLeft+l*f)),(!s||s&&y)&&bt(r),d.wheelStartX=null;return}if(s&&f!=null){var O=s*f,$=t.doc.scrollTop,q=$+d.wrapper.clientHeight;O<0?$=Math.max(0,$+O-50):q=Math.min(t.doc.height,q+O+50),Gu(t,{top:$,bottom:q})}Os<20&&r.deltaMode!==0&&(d.wheelStartX==null?(d.wheelStartX=p.scrollLeft,d.wheelStartY=p.scrollTop,d.wheelDX=l,d.wheelDY=s,setTimeout(function(){if(d.wheelStartX!=null){var J=p.scrollLeft-d.wheelStartX,re=p.scrollTop-d.wheelStartY,se=re&&d.wheelDY&&re/d.wheelDY||J&&d.wheelDX&&J/d.wheelDX;d.wheelStartX=d.wheelStartY=null,se&&(yr=(yr*Os+se)/(Os+1),++Os)}},200)):(d.wheelDX+=l,d.wheelDY+=s))}}var vn=function(t,r){this.ranges=t,this.primIndex=r};vn.prototype.primary=function(){return this.ranges[this.primIndex]},vn.prototype.equals=function(t){if(t==this)return!0;if(t.primIndex!=this.primIndex||t.ranges.length!=this.ranges.length)return!1;for(var r=0;r<this.ranges.length;r++){var o=this.ranges[r],l=t.ranges[r];if(!Te(o.anchor,l.anchor)||!Te(o.head,l.head))return!1}return!0},vn.prototype.deepCopy=function(){for(var t=[],r=0;r<this.ranges.length;r++)t[r]=new Fe(Pe(this.ranges[r].anchor),Pe(this.ranges[r].head));return new vn(t,this.primIndex)},vn.prototype.somethingSelected=function(){for(var t=0;t<this.ranges.length;t++)if(!this.ranges[t].empty())return!0;return!1},vn.prototype.contains=function(t,r){r||(r=t);for(var o=0;o<this.ranges.length;o++){var l=this.ranges[o];if(j(r,l.from())>=0&&j(t,l.to())<=0)return o}return-1};var Fe=function(t,r){this.anchor=t,this.head=r};Fe.prototype.from=function(){return dt(this.anchor,this.head)},Fe.prototype.to=function(){return Ie(this.anchor,this.head)},Fe.prototype.empty=function(){return this.head.line==this.anchor.line&&this.head.ch==this.anchor.ch};function Wn(t,r,o){var l=t&&t.options.selectionsMayTouch,s=r[o];r.sort(function(I,O){return j(I.from(),O.from())}),o=be(r,s);for(var f=1;f<r.length;f++){var d=r[f],p=r[f-1],g=j(p.to(),d.from());if(l&&!d.empty()?g>0:g>=0){var y=dt(p.from(),d.from()),k=Ie(p.to(),d.to()),N=p.empty()?d.from()==d.head:p.from()==p.head;f<=o&&--o,r.splice(--f,2,new Fe(N?k:y,N?y:k))}}return new vn(r,o)}function Br(t,r){return new vn([new Fe(t,r||t)],0)}function Hr(t){return t.text?D(t.from.line+t.text.length-1,_e(t.text).length+(t.text.length==1?t.from.ch:0)):t.to}function zh(t,r){if(j(t,r.from)<0)return t;if(j(t,r.to)<=0)return Hr(r);var o=t.line+r.text.length-(r.to.line-r.from.line)-1,l=t.ch;return t.line==r.to.line&&(l+=Hr(r).ch-r.to.ch),D(o,l)}function Yu(t,r){for(var o=[],l=0;l<t.sel.ranges.length;l++){var s=t.sel.ranges[l];o.push(new Fe(zh(s.anchor,r),zh(s.head,r)))}return Wn(t.cm,o,t.sel.primIndex)}function Ih(t,r,o){return t.line==r.line?D(o.line,t.ch-r.ch+o.ch):D(o.line+(t.line-r.line),t.ch)}function Yy(t,r,o){for(var l=[],s=D(t.first,0),f=s,d=0;d<r.length;d++){var p=r[d],g=Ih(p.from,s,f),y=Ih(Hr(p),s,f);if(s=p.to,f=y,o=="around"){var k=t.sel.ranges[d],N=j(k.head,k.anchor)<0;l[d]=new Fe(N?y:g,N?g:y)}else l[d]=new Fe(g,g)}return new vn(l,t.sel.primIndex)}function Xu(t){t.doc.mode=dr(t.options,t.doc.modeOption),ro(t)}function ro(t){t.doc.iter(function(r){r.stateAfter&&(r.stateAfter=null),r.styles&&(r.styles=null)}),t.doc.modeFrontier=t.doc.highlightFrontier=t.doc.first,to(t,100),t.state.modeGen++,t.curOp&&Kt(t)}function Ah(t,r){return r.from.ch==0&&r.to.ch==0&&_e(r.text)==""&&(!t.cm||t.cm.options.wholeLineUpdateBefore)}function Zu(t,r,o,l){function s(se){return o?o[se]:null}function f(se,ie,ae){iy(se,ie,ae,l),gt(se,"change",se,r)}function d(se,ie){for(var ae=[],me=se;me<ie;++me)ae.push(new Hi(y[me],s(me),l));return ae}var p=r.from,g=r.to,y=r.text,k=de(t,p.line),N=de(t,g.line),I=_e(y),O=s(y.length-1),$=g.line-p.line;if(r.full)t.insert(0,d(0,y.length)),t.remove(y.length,t.size-y.length);else if(Ah(t,r)){var q=d(0,y.length-1);f(N,N.text,O),$&&t.remove(p.line,$),q.length&&t.insert(p.line,q)}else if(k==N)if(y.length==1)f(k,k.text.slice(0,p.ch)+I+k.text.slice(g.ch),O);else{var J=d(1,y.length-1);J.push(new Hi(I+k.text.slice(g.ch),O,l)),f(k,k.text.slice(0,p.ch)+y[0],s(0)),t.insert(p.line+1,J)}else if(y.length==1)f(k,k.text.slice(0,p.ch)+y[0]+N.text.slice(g.ch),s(0)),t.remove(p.line+1,$);else{f(k,k.text.slice(0,p.ch)+y[0],s(0)),f(N,I+N.text.slice(g.ch),O);var re=d(1,y.length-1);$>1&&t.remove(p.line+1,$-1),t.insert(p.line+1,re)}gt(t,"change",t,r)}function Ur(t,r,o){function l(s,f,d){if(s.linked)for(var p=0;p<s.linked.length;++p){var g=s.linked[p];if(g.doc!=f){var y=d&&g.sharedHist;o&&!y||(r(g.doc,y),l(g.doc,s,y))}}}l(t,null,!0)}function Rh(t,r){if(r.cm)throw new Error("This document is already in use.");t.doc=r,r.cm=t,Fu(t),Xu(t),Fh(t),t.options.direction=r.direction,t.options.lineWrapping||Eu(t),t.options.mode=r.modeOption,Kt(t)}function Fh(t){(t.doc.direction=="rtl"?U:V)(t.display.lineDiv,"CodeMirror-rtl")}function Xy(t){nn(t,function(){Fh(t),Kt(t)})}function zs(t){this.done=[],this.undone=[],this.undoDepth=t?t.undoDepth:1/0,this.lastModTime=this.lastSelTime=0,this.lastOp=this.lastSelOp=null,this.lastOrigin=this.lastSelOrigin=null,this.generation=this.maxGeneration=t?t.maxGeneration:1}function Ju(t,r){var o={from:Pe(r.from),to:Hr(r),text:pr(t,r.from,r.to)};return Hh(t,o,r.from.line,r.to.line+1),Ur(t,function(l){return Hh(l,o,r.from.line,r.to.line+1)},!0),o}function Wh(t){for(;t.length;){var r=_e(t);if(r.ranges)t.pop();else break}}function Zy(t,r){if(r)return Wh(t.done),_e(t.done);if(t.done.length&&!_e(t.done).ranges)return _e(t.done);if(t.done.length>1&&!t.done[t.done.length-2].ranges)return t.done.pop(),_e(t.done)}function Bh(t,r,o,l){var s=t.history;s.undone.length=0;var f=+new Date,d,p;if((s.lastOp==l||s.lastOrigin==r.origin&&r.origin&&(r.origin.charAt(0)=="+"&&s.lastModTime>f-(t.cm?t.cm.options.historyEventDelay:500)||r.origin.charAt(0)=="*"))&&(d=Zy(s,s.lastOp==l)))p=_e(d.changes),j(r.from,r.to)==0&&j(r.from,p.to)==0?p.to=Hr(r):d.changes.push(Ju(t,r));else{var g=_e(s.done);for((!g||!g.ranges)&&Is(t.sel,s.done),d={changes:[Ju(t,r)],generation:s.generation},s.done.push(d);s.done.length>s.undoDepth;)s.done.shift(),s.done[0].ranges||s.done.shift()}s.done.push(o),s.generation=++s.maxGeneration,s.lastModTime=s.lastSelTime=f,s.lastOp=s.lastSelOp=l,s.lastOrigin=s.lastSelOrigin=r.origin,p||$e(t,"historyAdded")}function Jy(t,r,o,l){var s=r.charAt(0);return s=="*"||s=="+"&&o.ranges.length==l.ranges.length&&o.somethingSelected()==l.somethingSelected()&&new Date-t.history.lastSelTime<=(t.cm?t.cm.options.historyEventDelay:500)}function ew(t,r,o,l){var s=t.history,f=l&&l.origin;o==s.lastSelOp||f&&s.lastSelOrigin==f&&(s.lastModTime==s.lastSelTime&&s.lastOrigin==f||Jy(t,f,_e(s.done),r))?s.done[s.done.length-1]=r:Is(r,s.done),s.lastSelTime=+new Date,s.lastSelOrigin=f,s.lastSelOp=o,l&&l.clearRedo!==!1&&Wh(s.undone)}function Is(t,r){var o=_e(r);o&&o.ranges&&o.equals(t)||r.push(t)}function Hh(t,r,o,l){var s=r["spans_"+t.id],f=0;t.iter(Math.max(t.first,o),Math.min(t.first+t.size,l),function(d){d.markedSpans&&((s||(s=r["spans_"+t.id]={}))[f]=d.markedSpans),++f})}function tw(t){if(!t)return null;for(var r,o=0;o<t.length;++o)t[o].marker.explicitlyCleared?r||(r=t.slice(0,o)):r&&r.push(t[o]);return r?r.length?r:null:t}function nw(t,r){var o=r["spans_"+t.id];if(!o)return null;for(var l=[],s=0;s<r.text.length;++s)l.push(tw(o[s]));return l}function Uh(t,r){var o=nw(t,r),l=Cu(t,r);if(!o)return l;if(!l)return o;for(var s=0;s<o.length;++s){var f=o[s],d=l[s];if(f&&d){e:for(var p=0;p<d.length;++p){for(var g=d[p],y=0;y<f.length;++y)if(f[y].marker==g.marker)continue e;f.push(g)}}else d&&(o[s]=d)}return o}function Qi(t,r,o){for(var l=[],s=0;s<t.length;++s){var f=t[s];if(f.ranges){l.push(o?vn.prototype.deepCopy.call(f):f);continue}var d=f.changes,p=[];l.push({changes:p});for(var g=0;g<d.length;++g){var y=d[g],k=void 0;if(p.push({from:y.from,to:y.to,text:y.text}),r)for(var N in y)(k=N.match(/^spans_(\d+)$/))&&be(r,Number(k[1]))>-1&&(_e(p)[N]=y[N],delete y[N])}}return l}function ef(t,r,o,l){if(l){var s=t.anchor;if(o){var f=j(r,s)<0;f!=j(o,s)<0?(s=r,r=o):f!=j(r,o)<0&&(r=o)}return new Fe(s,r)}else return new Fe(o||r,r)}function As(t,r,o,l,s){s==null&&(s=t.cm&&(t.cm.display.shift||t.extend)),Mt(t,new vn([ef(t.sel.primary(),r,o,s)],0),l)}function jh(t,r,o){for(var l=[],s=t.cm&&(t.cm.display.shift||t.extend),f=0;f<t.sel.ranges.length;f++)l[f]=ef(t.sel.ranges[f],r[f],null,s);var d=Wn(t.cm,l,t.sel.primIndex);Mt(t,d,o)}function tf(t,r,o,l){var s=t.sel.ranges.slice(0);s[r]=o,Mt(t,Wn(t.cm,s,t.sel.primIndex),l)}function $h(t,r,o,l){Mt(t,Br(r,o),l)}function rw(t,r,o){var l={ranges:r.ranges,update:function(s){this.ranges=[];for(var f=0;f<s.length;f++)this.ranges[f]=new Fe(Le(t,s[f].anchor),Le(t,s[f].head))},origin:o&&o.origin};return $e(t,"beforeSelectionChange",t,l),t.cm&&$e(t.cm,"beforeSelectionChange",t.cm,l),l.ranges!=r.ranges?Wn(t.cm,l.ranges,l.ranges.length-1):r}function Kh(t,r,o){var l=t.history.done,s=_e(l);s&&s.ranges?(l[l.length-1]=r,Rs(t,r,o)):Mt(t,r,o)}function Mt(t,r,o){Rs(t,r,o),ew(t,t.sel,t.cm?t.cm.curOp.id:NaN,o)}function Rs(t,r,o){(zt(t,"beforeSelectionChange")||t.cm&&zt(t.cm,"beforeSelectionChange"))&&(r=rw(t,r,o));var l=o&&o.bias||(j(r.primary().head,t.sel.primary().head)<0?-1:1);Gh(t,Vh(t,r,l,!0)),!(o&&o.scroll===!1)&&t.cm&&t.cm.getOption("readOnly")!="nocursor"&&qi(t.cm)}function Gh(t,r){r.equals(t.sel)||(t.sel=r,t.cm&&(t.cm.curOp.updateInput=1,t.cm.curOp.selectionChanged=!0,ds(t.cm)),gt(t,"cursorActivity",t))}function qh(t){Gh(t,Vh(t,t.sel,null,!1))}function Vh(t,r,o,l){for(var s,f=0;f<r.ranges.length;f++){var d=r.ranges[f],p=r.ranges.length==t.sel.ranges.length&&t.sel.ranges[f],g=Fs(t,d.anchor,p&&p.anchor,o,l),y=d.head==d.anchor?g:Fs(t,d.head,p&&p.head,o,l);(s||g!=d.anchor||y!=d.head)&&(s||(s=r.ranges.slice(0,f)),s[f]=new Fe(g,y))}return s?Wn(t.cm,s,r.primIndex):r}function Yi(t,r,o,l,s){var f=de(t,r.line);if(f.markedSpans)for(var d=0;d<f.markedSpans.length;++d){var p=f.markedSpans[d],g=p.marker,y="selectLeft"in g?!g.selectLeft:g.inclusiveLeft,k="selectRight"in g?!g.selectRight:g.inclusiveRight;if((p.from==null||(y?p.from<=r.ch:p.from<r.ch))&&(p.to==null||(k?p.to>=r.ch:p.to>r.ch))){if(s&&($e(g,"beforeCursorEnter"),g.explicitlyCleared))if(f.markedSpans){--d;continue}else break;if(!g.atomic)continue;if(o){var N=g.find(l<0?1:-1),I=void 0;if((l<0?k:y)&&(N=Qh(t,N,-l,N&&N.line==r.line?f:null)),N&&N.line==r.line&&(I=j(N,o))&&(l<0?I<0:I>0))return Yi(t,N,r,l,s)}var O=g.find(l<0?-1:1);return(l<0?y:k)&&(O=Qh(t,O,l,O.line==r.line?f:null)),O?Yi(t,O,r,l,s):null}}return r}function Fs(t,r,o,l,s){var f=l||1,d=Yi(t,r,o,f,s)||!s&&Yi(t,r,o,f,!0)||Yi(t,r,o,-f,s)||!s&&Yi(t,r,o,-f,!0);return d||(t.cantEdit=!0,D(t.first,0))}function Qh(t,r,o,l){return o<0&&r.ch==0?r.line>t.first?Le(t,D(r.line-1)):null:o>0&&r.ch==(l||de(t,r.line)).text.length?r.line<t.first+t.size-1?D(r.line+1,0):null:new D(r.line,r.ch+o)}function Yh(t){t.setSelection(D(t.firstLine(),0),D(t.lastLine()),Ke)}function Xh(t,r,o){var l={canceled:!1,from:r.from,to:r.to,text:r.text,origin:r.origin,cancel:function(){return l.canceled=!0}};return o&&(l.update=function(s,f,d,p){s&&(l.from=Le(t,s)),f&&(l.to=Le(t,f)),d&&(l.text=d),p!==void 0&&(l.origin=p)}),$e(t,"beforeChange",t,l),t.cm&&$e(t.cm,"beforeChange",t.cm,l),l.canceled?(t.cm&&(t.cm.curOp.updateInput=2),null):{from:l.from,to:l.to,text:l.text,origin:l.origin}}function Xi(t,r,o){if(t.cm){if(!t.cm.curOp)return mt(t.cm,Xi)(t,r,o);if(t.cm.state.suppressEdits)return}if(!((zt(t,"beforeChange")||t.cm&&zt(t.cm,"beforeChange"))&&(r=Xh(t,r,!0),!r))){var l=Ud&&!o&&ey(t,r.from,r.to);if(l)for(var s=l.length-1;s>=0;--s)Zh(t,{from:l[s].from,to:l[s].to,text:s?[""]:r.text,origin:r.origin});else Zh(t,r)}}function Zh(t,r){if(!(r.text.length==1&&r.text[0]==""&&j(r.from,r.to)==0)){var o=Yu(t,r);Bh(t,r,o,t.cm?t.cm.curOp.id:NaN),io(t,r,o,Cu(t,r));var l=[];Ur(t,function(s,f){!f&&be(l,s.history)==-1&&(np(s.history,r),l.push(s.history)),io(s,r,null,Cu(s,r))})}}function Ws(t,r,o){var l=t.cm&&t.cm.state.suppressEdits;if(!(l&&!o)){for(var s=t.history,f,d=t.sel,p=r=="undo"?s.done:s.undone,g=r=="undo"?s.undone:s.done,y=0;y<p.length&&(f=p[y],!(o?f.ranges&&!f.equals(t.sel):!f.ranges));y++);if(y!=p.length){for(s.lastOrigin=s.lastSelOrigin=null;;)if(f=p.pop(),f.ranges){if(Is(f,g),o&&!f.equals(t.sel)){Mt(t,f,{clearRedo:!1});return}d=f}else if(l){p.push(f);return}else break;var k=[];Is(d,g),g.push({changes:k,generation:s.generation}),s.generation=f.generation||++s.maxGeneration;for(var N=zt(t,"beforeChange")||t.cm&&zt(t.cm,"beforeChange"),I=function(q){var J=f.changes[q];if(J.origin=r,N&&!Xh(t,J,!1))return p.length=0,{};k.push(Ju(t,J));var re=q?Yu(t,J):_e(p);io(t,J,re,Uh(t,J)),!q&&t.cm&&t.cm.scrollIntoView({from:J.from,to:Hr(J)});var se=[];Ur(t,function(ie,ae){!ae&&be(se,ie.history)==-1&&(np(ie.history,J),se.push(ie.history)),io(ie,J,null,Uh(ie,J))})},O=f.changes.length-1;O>=0;--O){var $=I(O);if($)return $.v}}}}function Jh(t,r){if(r!=0&&(t.first+=r,t.sel=new vn(ar(t.sel.ranges,function(s){return new Fe(D(s.anchor.line+r,s.anchor.ch),D(s.head.line+r,s.head.ch))}),t.sel.primIndex),t.cm)){Kt(t.cm,t.first,t.first-r,r);for(var o=t.cm.display,l=o.viewFrom;l<o.viewTo;l++)Fr(t.cm,l,"gutter")}}function io(t,r,o,l){if(t.cm&&!t.cm.curOp)return mt(t.cm,io)(t,r,o,l);if(r.to.line<t.first){Jh(t,r.text.length-1-(r.to.line-r.from.line));return}if(!(r.from.line>t.lastLine())){if(r.from.line<t.first){var s=r.text.length-1-(t.first-r.from.line);Jh(t,s),r={from:D(t.first,0),to:D(r.to.line+s,r.to.ch),text:[_e(r.text)],origin:r.origin}}var f=t.lastLine();r.to.line>f&&(r={from:r.from,to:D(f,de(t,f).text.length),text:[r.text[0]],origin:r.origin}),r.removed=pr(t,r.from,r.to),o||(o=Yu(t,r)),t.cm?iw(t.cm,r,l):Zu(t,r,l),Rs(t,o,Ke),t.cantEdit&&Fs(t,D(t.firstLine(),0))&&(t.cantEdit=!1)}}function iw(t,r,o){var l=t.doc,s=t.display,f=r.from,d=r.to,p=!1,g=f.line;t.options.lineWrapping||(g=Re(Rn(de(l,f.line))),l.iter(g,d.line+1,function(O){if(O==s.maxLine)return p=!0,!0})),l.sel.contains(r.from,r.to)>-1&&ds(t),Zu(l,r,o,yh(t)),t.options.lineWrapping||(l.iter(g,f.line+r.text.length,function(O){var $=ks(O);$>s.maxLineLength&&(s.maxLine=O,s.maxLineLength=$,s.maxLineChanged=!0,p=!1)}),p&&(t.curOp.updateMaxLine=!0)),q0(l,f.line),to(t,400);var y=r.text.length-(d.line-f.line)-1;r.full?Kt(t):f.line==d.line&&r.text.length==1&&!Ah(t.doc,r)?Fr(t,f.line,"text"):Kt(t,f.line,d.line+1,y);var k=zt(t,"changes"),N=zt(t,"change");if(N||k){var I={from:f,to:d,text:r.text,removed:r.removed,origin:r.origin};N&&gt(t,"change",t,I),k&&(t.curOp.changeObjs||(t.curOp.changeObjs=[])).push(I)}t.display.selForContextMenu=null}function Zi(t,r,o,l,s){var f;l||(l=o),j(l,o)<0&&(f=[l,o],o=f[0],l=f[1]),typeof r=="string"&&(r=t.splitLines(r)),Xi(t,{from:o,to:l,text:r,origin:s})}function ep(t,r,o,l){o<t.line?t.line+=l:r<t.line&&(t.line=r,t.ch=0)}function tp(t,r,o,l){for(var s=0;s<t.length;++s){var f=t[s],d=!0;if(f.ranges){f.copied||(f=t[s]=f.deepCopy(),f.copied=!0);for(var p=0;p<f.ranges.length;p++)ep(f.ranges[p].anchor,r,o,l),ep(f.ranges[p].head,r,o,l);continue}for(var g=0;g<f.changes.length;++g){var y=f.changes[g];if(o<y.from.line)y.from=D(y.from.line+l,y.from.ch),y.to=D(y.to.line+l,y.to.ch);else if(r<=y.to.line){d=!1;break}}d||(t.splice(0,s+1),s=0)}}function np(t,r){var o=r.from.line,l=r.to.line,s=r.text.length-(l-o)-1;tp(t.done,o,l,s),tp(t.undone,o,l,s)}function lo(t,r,o,l){var s=r,f=r;return typeof r=="number"?f=de(t,pn(t,r)):s=Re(r),s==null?null:(l(f,s)&&t.cm&&Fr(t.cm,s,o),f)}function oo(t){this.lines=t,this.parent=null;for(var r=0,o=0;o<t.length;++o)t[o].parent=this,r+=t[o].height;this.height=r}oo.prototype={chunkSize:function(){return this.lines.length},removeInner:function(t,r){for(var o=t,l=t+r;o<l;++o){var s=this.lines[o];this.height-=s.height,ly(s),gt(s,"delete")}this.lines.splice(t,r)},collapse:function(t){t.push.apply(t,this.lines)},insertInner:function(t,r,o){this.height+=o,this.lines=this.lines.slice(0,t).concat(r).concat(this.lines.slice(t));for(var l=0;l<r.length;++l)r[l].parent=this},iterN:function(t,r,o){for(var l=t+r;t<l;++t)if(o(this.lines[t]))return!0}};function so(t){this.children=t;for(var r=0,o=0,l=0;l<t.length;++l){var s=t[l];r+=s.chunkSize(),o+=s.height,s.parent=this}this.size=r,this.height=o,this.parent=null}so.prototype={chunkSize:function(){return this.size},removeInner:function(t,r){this.size-=r;for(var o=0;o<this.children.length;++o){var l=this.children[o],s=l.chunkSize();if(t<s){var f=Math.min(r,s-t),d=l.height;if(l.removeInner(t,f),this.height-=d-l.height,s==f&&(this.children.splice(o--,1),l.parent=null),(r-=f)==0)break;t=0}else t-=s}if(this.size-r<25&&(this.children.length>1||!(this.children[0]instanceof oo))){var p=[];this.collapse(p),this.children=[new oo(p)],this.children[0].parent=this}},collapse:function(t){for(var r=0;r<this.children.length;++r)this.children[r].collapse(t)},insertInner:function(t,r,o){this.size+=r.length,this.height+=o;for(var l=0;l<this.children.length;++l){var s=this.children[l],f=s.chunkSize();if(t<=f){if(s.insertInner(t,r,o),s.lines&&s.lines.length>50){for(var d=s.lines.length%25+25,p=d;p<s.lines.length;){var g=new oo(s.lines.slice(p,p+=25));s.height-=g.height,this.children.splice(++l,0,g),g.parent=this}s.lines=s.lines.slice(0,d),this.maybeSpill()}break}t-=f}},maybeSpill:function(){if(!(this.children.length<=10)){var t=this;do{var r=t.children.splice(t.children.length-5,5),o=new so(r);if(t.parent){t.size-=o.size,t.height-=o.height;var s=be(t.parent.children,t);t.parent.children.splice(s+1,0,o)}else{var l=new so(t.children);l.parent=t,t.children=[l,o],t=l}o.parent=t.parent}while(t.children.length>10);t.parent.maybeSpill()}},iterN:function(t,r,o){for(var l=0;l<this.children.length;++l){var s=this.children[l],f=s.chunkSize();if(t<f){var d=Math.min(r,f-t);if(s.iterN(t,d,o))return!0;if((r-=d)==0)break;t=0}else t-=f}}};var ao=function(t,r,o){if(o)for(var l in o)o.hasOwnProperty(l)&&(this[l]=o[l]);this.doc=t,this.node=r};ao.prototype.clear=function(){var t=this.doc.cm,r=this.line.widgets,o=this.line,l=Re(o);if(!(l==null||!r)){for(var s=0;s<r.length;++s)r[s]==this&&r.splice(s--,1);r.length||(o.widgets=null);var f=Vl(this);En(o,Math.max(0,o.height-f)),t&&(nn(t,function(){rp(t,o,-f),Fr(t,l,"widget")}),gt(t,"lineWidgetCleared",t,this,l))}},ao.prototype.changed=function(){var t=this,r=this.height,o=this.doc.cm,l=this.line;this.height=null;var s=Vl(this)-r;s&&(Rr(this.doc,l)||En(l,l.height+s),o&&nn(o,function(){o.curOp.forceUpdate=!0,rp(o,l,s),gt(o,"lineWidgetChanged",o,t,Re(l))}))},It(ao);function rp(t,r,o){gr(r)<(t.curOp&&t.curOp.scrollTop||t.doc.scrollTop)&&$u(t,o)}function lw(t,r,o,l){var s=new ao(t,o,l),f=t.cm;return f&&s.noHScroll&&(f.display.alignWidgets=!0),lo(t,r,"widget",function(d){var p=d.widgets||(d.widgets=[]);if(s.insertAt==null?p.push(s):p.splice(Math.min(p.length,Math.max(0,s.insertAt)),0,s),s.line=d,f&&!Rr(t,d)){var g=gr(d)<t.scrollTop;En(d,d.height+Vl(s)),g&&$u(f,s.height),f.curOp.forceUpdate=!0}return!0}),f&&gt(f,"lineWidgetAdded",f,s,typeof r=="number"?r:Re(r)),s}var ip=0,jr=function(t,r){this.lines=[],this.type=r,this.doc=t,this.id=++ip};jr.prototype.clear=function(){if(!this.explicitlyCleared){var t=this.doc.cm,r=t&&!t.curOp;if(r&&wi(t),zt(this,"clear")){var o=this.find();o&&gt(this,"clear",o.from,o.to)}for(var l=null,s=null,f=0;f<this.lines.length;++f){var d=this.lines[f],p=Kl(d.markedSpans,this);t&&!this.collapsed?Fr(t,Re(d),"text"):t&&(p.to!=null&&(s=Re(d)),p.from!=null&&(l=Re(d))),d.markedSpans=Y0(d.markedSpans,p),p.from==null&&this.collapsed&&!Rr(this.doc,d)&&t&&En(d,$i(t.display))}if(t&&this.collapsed&&!t.options.lineWrapping)for(var g=0;g<this.lines.length;++g){var y=Rn(this.lines[g]),k=ks(y);k>t.display.maxLineLength&&(t.display.maxLine=y,t.display.maxLineLength=k,t.display.maxLineChanged=!0)}l!=null&&t&&this.collapsed&&Kt(t,l,s+1),this.lines.length=0,this.explicitlyCleared=!0,this.atomic&&this.doc.cantEdit&&(this.doc.cantEdit=!1,t&&qh(t.doc)),t&&gt(t,"markerCleared",t,this,l,s),r&&xi(t),this.parent&&this.parent.clear()}},jr.prototype.find=function(t,r){t==null&&this.type=="bookmark"&&(t=1);for(var o,l,s=0;s<this.lines.length;++s){var f=this.lines[s],d=Kl(f.markedSpans,this);if(d.from!=null&&(o=D(r?f:Re(f),d.from),t==-1))return o;if(d.to!=null&&(l=D(r?f:Re(f),d.to),t==1))return l}return o&&{from:o,to:l}},jr.prototype.changed=function(){var t=this,r=this.find(-1,!0),o=this,l=this.doc.cm;!r||!l||nn(l,function(){var s=r.line,f=Re(r.line),d=Du(l,f);if(d&&(fh(d),l.curOp.selectionChanged=l.curOp.forceUpdate=!0),l.curOp.updateMaxLine=!0,!Rr(o.doc,s)&&o.height!=null){var p=o.height;o.height=null;var g=Vl(o)-p;g&&En(s,s.height+g)}gt(l,"markerChanged",l,t)})},jr.prototype.attachLine=function(t){if(!this.lines.length&&this.doc.cm){var r=this.doc.cm.curOp;(!r.maybeHiddenMarkers||be(r.maybeHiddenMarkers,this)==-1)&&(r.maybeUnhiddenMarkers||(r.maybeUnhiddenMarkers=[])).push(this)}this.lines.push(t)},jr.prototype.detachLine=function(t){if(this.lines.splice(be(this.lines,t),1),!this.lines.length&&this.doc.cm){var r=this.doc.cm.curOp;(r.maybeHiddenMarkers||(r.maybeHiddenMarkers=[])).push(this)}},It(jr);function Ji(t,r,o,l,s){if(l&&l.shared)return ow(t,r,o,l,s);if(t.cm&&!t.cm.curOp)return mt(t.cm,Ji)(t,r,o,l,s);var f=new jr(t,s),d=j(r,o);if(l&&pe(l,f,!1),d>0||d==0&&f.clearWhenEmpty!==!1)return f;if(f.replacedWith&&(f.collapsed=!0,f.widgetNode=X("span",[f.replacedWith],"CodeMirror-widget"),l.handleMouseEvents||f.widgetNode.setAttribute("cm-ignore-events","true"),l.insertLeft&&(f.widgetNode.insertLeft=!0)),f.collapsed){if(Vd(t,r.line,r,o,f)||r.line!=o.line&&Vd(t,o.line,r,o,f))throw new Error("Inserting collapsed marker partially overlapping an existing one");Q0()}f.addToHistory&&Bh(t,{from:r,to:o,origin:"markText"},t.sel,NaN);var p=r.line,g=t.cm,y;if(t.iter(p,o.line+1,function(N){g&&f.collapsed&&!g.options.lineWrapping&&Rn(N)==g.display.maxLine&&(y=!0),f.collapsed&&p!=r.line&&En(N,0),X0(N,new ys(f,p==r.line?r.ch:null,p==o.line?o.ch:null),t.cm&&t.cm.curOp),++p}),f.collapsed&&t.iter(r.line,o.line+1,function(N){Rr(t,N)&&En(N,0)}),f.clearOnEnter&&oe(f,"beforeCursorEnter",function(){return f.clear()}),f.readOnly&&(V0(),(t.history.done.length||t.history.undone.length)&&t.clearHistory()),f.collapsed&&(f.id=++ip,f.atomic=!0),g){if(y&&(g.curOp.updateMaxLine=!0),f.collapsed)Kt(g,r.line,o.line+1);else if(f.className||f.startStyle||f.endStyle||f.css||f.attributes||f.title)for(var k=r.line;k<=o.line;k++)Fr(g,k,"text");f.atomic&&qh(g.doc),gt(g,"markerAdded",g,f)}return f}var uo=function(t,r){this.markers=t,this.primary=r;for(var o=0;o<t.length;++o)t[o].parent=this};uo.prototype.clear=function(){if(!this.explicitlyCleared){this.explicitlyCleared=!0;for(var t=0;t<this.markers.length;++t)this.markers[t].clear();gt(this,"clear")}},uo.prototype.find=function(t,r){return this.primary.find(t,r)},It(uo);function ow(t,r,o,l,s){l=pe(l),l.shared=!1;var f=[Ji(t,r,o,l,s)],d=f[0],p=l.widgetNode;return Ur(t,function(g){p&&(l.widgetNode=p.cloneNode(!0)),f.push(Ji(g,Le(g,r),Le(g,o),l,s));for(var y=0;y<g.linked.length;++y)if(g.linked[y].isParent)return;d=_e(f)}),new uo(f,d)}function lp(t){return t.findMarks(D(t.first,0),t.clipPos(D(t.lastLine())),function(r){return r.parent})}function sw(t,r){for(var o=0;o<r.length;o++){var l=r[o],s=l.find(),f=t.clipPos(s.from),d=t.clipPos(s.to);if(j(f,d)){var p=Ji(t,f,d,l.primary,l.primary.type);l.markers.push(p),p.parent=l}}}function aw(t){for(var r=function(l){var s=t[l],f=[s.primary.doc];Ur(s.primary.doc,function(g){return f.push(g)});for(var d=0;d<s.markers.length;d++){var p=s.markers[d];be(f,p.doc)==-1&&(p.parent=null,s.markers.splice(d--,1))}},o=0;o<t.length;o++)r(o)}var uw=0,Gt=function(t,r,o,l,s){if(!(this instanceof Gt))return new Gt(t,r,o,l,s);o==null&&(o=0),so.call(this,[new oo([new Hi("",null)])]),this.first=o,this.scrollTop=this.scrollLeft=0,this.cantEdit=!1,this.cleanGeneration=1,this.modeFrontier=this.highlightFrontier=o;var f=D(o,0);this.sel=Br(f),this.history=new zs(null),this.id=++uw,this.modeOption=r,this.lineSep=l,this.direction=s=="rtl"?"rtl":"ltr",this.extend=!1,typeof t=="string"&&(t=this.splitLines(t)),Zu(this,{from:f,to:f,text:t}),Mt(this,Br(f),Ke)};Gt.prototype=ci(so.prototype,{constructor:Gt,iter:function(t,r,o){o?this.iterN(t-this.first,r-t,o):this.iterN(this.first,this.first+this.size,t)},insert:function(t,r){for(var o=0,l=0;l<r.length;++l)o+=r[l].height;this.insertInner(t-this.first,r,o)},remove:function(t,r){this.removeInner(t-this.first,r)},getValue:function(t){var r=Bi(this,this.first,this.first+this.size);return t===!1?r:r.join(t||this.lineSeparator())},setValue:yt(function(t){var r=D(this.first,0),o=this.first+this.size-1;Xi(this,{from:r,to:D(o,de(this,o).text.length),text:this.splitLines(t),origin:"setValue",full:!0},!0),this.cm&&Xl(this.cm,0,0),Mt(this,Br(r),Ke)}),replaceRange:function(t,r,o,l){r=Le(this,r),o=o?Le(this,o):r,Zi(this,t,r,o,l)},getRange:function(t,r,o){var l=pr(this,Le(this,t),Le(this,r));return o===!1?l:o===""?l.join(""):l.join(o||this.lineSeparator())},getLine:function(t){var r=this.getLineHandle(t);return r&&r.text},getLineHandle:function(t){if(w(this,t))return de(this,t)},getLineNumber:function(t){return Re(t)},getLineHandleVisualStart:function(t){return typeof t=="number"&&(t=de(this,t)),Rn(t)},lineCount:function(){return this.size},firstLine:function(){return this.first},lastLine:function(){return this.first+this.size-1},clipPos:function(t){return Le(this,t)},getCursor:function(t){var r=this.sel.primary(),o;return t==null||t=="head"?o=r.head:t=="anchor"?o=r.anchor:t=="end"||t=="to"||t===!1?o=r.to():o=r.from(),o},listSelections:function(){return this.sel.ranges},somethingSelected:function(){return this.sel.somethingSelected()},setCursor:yt(function(t,r,o){$h(this,Le(this,typeof t=="number"?D(t,r||0):t),null,o)}),setSelection:yt(function(t,r,o){$h(this,Le(this,t),Le(this,r||t),o)}),extendSelection:yt(function(t,r,o){As(this,Le(this,t),r&&Le(this,r),o)}),extendSelections:yt(function(t,r){jh(this,zd(this,t),r)}),extendSelectionsBy:yt(function(t,r){var o=ar(this.sel.ranges,t);jh(this,zd(this,o),r)}),setSelections:yt(function(t,r,o){if(t.length){for(var l=[],s=0;s<t.length;s++)l[s]=new Fe(Le(this,t[s].anchor),Le(this,t[s].head||t[s].anchor));r==null&&(r=Math.min(t.length-1,this.sel.primIndex)),Mt(this,Wn(this.cm,l,r),o)}}),addSelection:yt(function(t,r,o){var l=this.sel.ranges.slice(0);l.push(new Fe(Le(this,t),Le(this,r||t))),Mt(this,Wn(this.cm,l,l.length-1),o)}),getSelection:function(t){for(var r=this.sel.ranges,o,l=0;l<r.length;l++){var s=pr(this,r[l].from(),r[l].to());o=o?o.concat(s):s}return t===!1?o:o.join(t||this.lineSeparator())},getSelections:function(t){for(var r=[],o=this.sel.ranges,l=0;l<o.length;l++){var s=pr(this,o[l].from(),o[l].to());t!==!1&&(s=s.join(t||this.lineSeparator())),r[l]=s}return r},replaceSelection:function(t,r,o){for(var l=[],s=0;s<this.sel.ranges.length;s++)l[s]=t;this.replaceSelections(l,r,o||"+input")},replaceSelections:yt(function(t,r,o){for(var l=[],s=this.sel,f=0;f<s.ranges.length;f++){var d=s.ranges[f];l[f]={from:d.from(),to:d.to(),text:this.splitLines(t[f]),origin:o}}for(var p=r&&r!="end"&&Yy(this,l,r),g=l.length-1;g>=0;g--)Xi(this,l[g]);p?Kh(this,p):this.cm&&qi(this.cm)}),undo:yt(function(){Ws(this,"undo")}),redo:yt(function(){Ws(this,"redo")}),undoSelection:yt(function(){Ws(this,"undo",!0)}),redoSelection:yt(function(){Ws(this,"redo",!0)}),setExtending:function(t){this.extend=t},getExtending:function(){return this.extend},historySize:function(){for(var t=this.history,r=0,o=0,l=0;l<t.done.length;l++)t.done[l].ranges||++r;for(var s=0;s<t.undone.length;s++)t.undone[s].ranges||++o;return{undo:r,redo:o}},clearHistory:function(){var t=this;this.history=new zs(this.history),Ur(this,function(r){return r.history=t.history},!0)},markClean:function(){this.cleanGeneration=this.changeGeneration(!0)},changeGeneration:function(t){return t&&(this.history.lastOp=this.history.lastSelOp=this.history.lastOrigin=null),this.history.generation},isClean:function(t){return this.history.generation==(t||this.cleanGeneration)},getHistory:function(){return{done:Qi(this.history.done),undone:Qi(this.history.undone)}},setHistory:function(t){var r=this.history=new zs(this.history);r.done=Qi(t.done.slice(0),null,!0),r.undone=Qi(t.undone.slice(0),null,!0)},setGutterMarker:yt(function(t,r,o){return lo(this,t,"gutter",function(l){var s=l.gutterMarkers||(l.gutterMarkers={});return s[r]=o,!o&&xe(s)&&(l.gutterMarkers=null),!0})}),clearGutter:yt(function(t){var r=this;this.iter(function(o){o.gutterMarkers&&o.gutterMarkers[t]&&lo(r,o,"gutter",function(){return o.gutterMarkers[t]=null,xe(o.gutterMarkers)&&(o.gutterMarkers=null),!0})})}),lineInfo:function(t){var r;if(typeof t=="number"){if(!w(this,t)||(r=t,t=de(this,t),!t))return null}else if(r=Re(t),r==null)return null;return{line:r,handle:t,text:t.text,gutterMarkers:t.gutterMarkers,textClass:t.textClass,bgClass:t.bgClass,wrapClass:t.wrapClass,widgets:t.widgets}},addLineClass:yt(function(t,r,o){return lo(this,t,r=="gutter"?"gutter":"class",function(l){var s=r=="text"?"textClass":r=="background"?"bgClass":r=="gutter"?"gutterClass":"wrapClass";if(!l[s])l[s]=o;else{if(ze(o).test(l[s]))return!1;l[s]+=" "+o}return!0})}),removeLineClass:yt(function(t,r,o){return lo(this,t,r=="gutter"?"gutter":"class",function(l){var s=r=="text"?"textClass":r=="background"?"bgClass":r=="gutter"?"gutterClass":"wrapClass",f=l[s];if(f)if(o==null)l[s]=null;else{var d=f.match(ze(o));if(!d)return!1;var p=d.index+d[0].length;l[s]=f.slice(0,d.index)+(!d.index||p==f.length?"":" ")+f.slice(p)||null}else return!1;return!0})}),addLineWidget:yt(function(t,r,o){return lw(this,t,r,o)}),removeLineWidget:function(t){t.clear()},markText:function(t,r,o){return Ji(this,Le(this,t),Le(this,r),o,o&&o.type||"range")},setBookmark:function(t,r){var o={replacedWith:r&&(r.nodeType==null?r.widget:r),insertLeft:r&&r.insertLeft,clearWhenEmpty:!1,shared:r&&r.shared,handleMouseEvents:r&&r.handleMouseEvents};return t=Le(this,t),Ji(this,t,t,o,"bookmark")},findMarksAt:function(t){t=Le(this,t);var r=[],o=de(this,t.line).markedSpans;if(o)for(var l=0;l<o.length;++l){var s=o[l];(s.from==null||s.from<=t.ch)&&(s.to==null||s.to>=t.ch)&&r.push(s.marker.parent||s.marker)}return r},findMarks:function(t,r,o){t=Le(this,t),r=Le(this,r);var l=[],s=t.line;return this.iter(t.line,r.line+1,function(f){var d=f.markedSpans;if(d)for(var p=0;p<d.length;p++){var g=d[p];!(g.to!=null&&s==t.line&&t.ch>=g.to||g.from==null&&s!=t.line||g.from!=null&&s==r.line&&g.from>=r.ch)&&(!o||o(g.marker))&&l.push(g.marker.parent||g.marker)}++s}),l},getAllMarks:function(){var t=[];return this.iter(function(r){var o=r.markedSpans;if(o)for(var l=0;l<o.length;++l)o[l].from!=null&&t.push(o[l].marker)}),t},posFromIndex:function(t){var r,o=this.first,l=this.lineSeparator().length;return this.iter(function(s){var f=s.text.length+l;if(f>t)return r=t,!0;t-=f,++o}),Le(this,D(o,r))},indexFromPos:function(t){t=Le(this,t);var r=t.ch;if(t.line<this.first||t.ch<0)return 0;var o=this.lineSeparator().length;return this.iter(this.first,t.line,function(l){r+=l.text.length+o}),r},copy:function(t){var r=new Gt(Bi(this,this.first,this.first+this.size),this.modeOption,this.first,this.lineSep,this.direction);return r.scrollTop=this.scrollTop,r.scrollLeft=this.scrollLeft,r.sel=this.sel,r.extend=!1,t&&(r.history.undoDepth=this.history.undoDepth,r.setHistory(this.getHistory())),r},linkedDoc:function(t){t||(t={});var r=this.first,o=this.first+this.size;t.from!=null&&t.from>r&&(r=t.from),t.to!=null&&t.to<o&&(o=t.to);var l=new Gt(Bi(this,r,o),t.mode||this.modeOption,r,this.lineSep,this.direction);return t.sharedHist&&(l.history=this.history),(this.linked||(this.linked=[])).push({doc:l,sharedHist:t.sharedHist}),l.linked=[{doc:this,isParent:!0,sharedHist:t.sharedHist}],sw(l,lp(this)),l},unlinkDoc:function(t){if(t instanceof qe&&(t=t.doc),this.linked)for(var r=0;r<this.linked.length;++r){var o=this.linked[r];if(o.doc==t){this.linked.splice(r,1),t.unlinkDoc(this),aw(lp(this));break}}if(t.history==this.history){var l=[t.id];Ur(t,function(s){return l.push(s.id)},!0),t.history=new zs(null),t.history.done=Qi(this.history.done,l),t.history.undone=Qi(this.history.undone,l)}},iterLinkedDocs:function(t){Ur(this,t)},getMode:function(){return this.mode},getEditor:function(){return this.cm},splitLines:function(t){return this.lineSep?t.split(this.lineSep):Ul(t)},lineSeparator:function(){return this.lineSep||`
`},setDirection:yt(function(t){t!="rtl"&&(t="ltr"),t!=this.direction&&(this.direction=t,this.iter(function(r){return r.order=null}),this.cm&&Xy(this.cm))})}),Gt.prototype.eachLine=Gt.prototype.iter;var op=0;function fw(t){var r=this;if(sp(r),!(Je(r,t)||mr(r.display,t))){bt(t),m&&(op=+new Date);var o=vi(r,t,!0),l=t.dataTransfer.files;if(!(!o||r.isReadOnly()))if(l&&l.length&&window.FileReader&&window.File)for(var s=l.length,f=Array(s),d=0,p=function(){++d==s&&mt(r,function(){o=Le(r.doc,o);var O={from:o,to:o,text:r.doc.splitLines(f.filter(function($){return $!=null}).join(r.doc.lineSeparator())),origin:"paste"};Xi(r.doc,O),Kh(r.doc,Br(Le(r.doc,o),Le(r.doc,Hr(O))))})()},g=function(O,$){if(r.options.allowDropFileTypes&&be(r.options.allowDropFileTypes,O.type)==-1){p();return}var q=new FileReader;q.onerror=function(){return p()},q.onload=function(){var J=q.result;if(/[\x00-\x08\x0e-\x1f]{2}/.test(J)){p();return}f[$]=J,p()},q.readAsText(O)},y=0;y<l.length;y++)g(l[y],y);else{if(r.state.draggingText&&r.doc.sel.contains(o)>-1){r.state.draggingText(t),setTimeout(function(){return r.display.input.focus()},20);return}try{var k=t.dataTransfer.getData("Text");if(k){var N;if(r.state.draggingText&&!r.state.draggingText.copy&&(N=r.listSelections()),Rs(r.doc,Br(o,o)),N)for(var I=0;I<N.length;++I)Zi(r.doc,"",N[I].anchor,N[I].head,"drag");r.replaceSelection(k,"around","paste"),r.display.input.focus()}}catch{}}}}function cw(t,r){if(m&&(!t.state.draggingText||+new Date-op<100)){cr(r);return}if(!(Je(t,r)||mr(t.display,r))&&(r.dataTransfer.setData("Text",t.getSelection()),r.dataTransfer.effectAllowed="copyMove",r.dataTransfer.setDragImage&&!Y)){var o=_("img",null,null,"position: fixed; left: 0; top: 0;");o.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",B&&(o.width=o.height=1,t.display.wrapper.appendChild(o),o._top=o.offsetTop),r.dataTransfer.setDragImage(o,0,0),B&&o.parentNode.removeChild(o)}}function dw(t,r){var o=vi(t,r);if(o){var l=document.createDocumentFragment();Wu(t,o,l),t.display.dragCursor||(t.display.dragCursor=_("div",null,"CodeMirror-cursors CodeMirror-dragcursors"),t.display.lineSpace.insertBefore(t.display.dragCursor,t.display.cursorDiv)),le(t.display.dragCursor,l)}}function sp(t){t.display.dragCursor&&(t.display.lineSpace.removeChild(t.display.dragCursor),t.display.dragCursor=null)}function ap(t){if(document.getElementsByClassName){for(var r=document.getElementsByClassName("CodeMirror"),o=[],l=0;l<r.length;l++){var s=r[l].CodeMirror;s&&o.push(s)}o.length&&o[0].operation(function(){for(var f=0;f<o.length;f++)t(o[f])})}}var up=!1;function hw(){up||(pw(),up=!0)}function pw(){var t;oe(window,"resize",function(){t==null&&(t=setTimeout(function(){t=null,ap(vw)},100))}),oe(window,"blur",function(){return ap(Gi)})}function vw(t){var r=t.display;r.cachedCharWidth=r.cachedTextHeight=r.cachedPaddingH=null,r.scrollbarsClipped=!1,t.setSize()}for(var $r={3:"Pause",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",61:"=",91:"Mod",92:"Mod",93:"Mod",106:"*",107:"=",109:"-",110:".",111:"/",145:"ScrollLock",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",224:"Mod",63232:"Up",63233:"Down",63234:"Left",63235:"Right",63272:"Delete",63273:"Home",63275:"End",63276:"PageUp",63277:"PageDown",63302:"Insert"},fo=0;fo<10;fo++)$r[fo+48]=$r[fo+96]=String(fo);for(var Bs=65;Bs<=90;Bs++)$r[Bs]=String.fromCharCode(Bs);for(var co=1;co<=12;co++)$r[co+111]=$r[co+63235]="F"+co;var wr={};wr.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharAfter",Backspace:"delCharBefore","Shift-Backspace":"delCharBefore",Tab:"defaultTab","Shift-Tab":"indentAuto",Enter:"newlineAndIndent",Insert:"toggleOverwrite",Esc:"singleSelection"},wr.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Up":"goLineUp","Ctrl-Down":"goLineDown","Ctrl-Left":"goGroupLeft","Ctrl-Right":"goGroupRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delGroupBefore","Ctrl-Delete":"delGroupAfter","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll","Ctrl-[":"indentLess","Ctrl-]":"indentMore","Ctrl-U":"undoSelection","Shift-Ctrl-U":"redoSelection","Alt-U":"redoSelection",fallthrough:"basic"},wr.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageDown","Shift-Ctrl-V":"goPageUp","Ctrl-D":"delCharAfter","Ctrl-H":"delCharBefore","Alt-Backspace":"delWordBefore","Ctrl-K":"killLine","Ctrl-T":"transposeChars","Ctrl-O":"openLine"},wr.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Home":"goDocStart","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goGroupLeft","Alt-Right":"goGroupRight","Cmd-Left":"goLineLeft","Cmd-Right":"goLineRight","Alt-Backspace":"delGroupBefore","Ctrl-Alt-Backspace":"delGroupAfter","Alt-Delete":"delGroupAfter","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll","Cmd-[":"indentLess","Cmd-]":"indentMore","Cmd-Backspace":"delWrappedLineLeft","Cmd-Delete":"delWrappedLineRight","Cmd-U":"undoSelection","Shift-Cmd-U":"redoSelection","Ctrl-Up":"goDocStart","Ctrl-Down":"goDocEnd",fallthrough:["basic","emacsy"]},wr.default=G?wr.macDefault:wr.pcDefault;function gw(t){var r=t.split(/-(?!$)/);t=r[r.length-1];for(var o,l,s,f,d=0;d<r.length-1;d++){var p=r[d];if(/^(cmd|meta|m)$/i.test(p))f=!0;else if(/^a(lt)?$/i.test(p))o=!0;else if(/^(c|ctrl|control)$/i.test(p))l=!0;else if(/^s(hift)?$/i.test(p))s=!0;else throw new Error("Unrecognized modifier name: "+p)}return o&&(t="Alt-"+t),l&&(t="Ctrl-"+t),f&&(t="Cmd-"+t),s&&(t="Shift-"+t),t}function mw(t){var r={};for(var o in t)if(t.hasOwnProperty(o)){var l=t[o];if(/^(name|fallthrough|(de|at)tach)$/.test(o))continue;if(l=="..."){delete t[o];continue}for(var s=ar(o.split(" "),gw),f=0;f<s.length;f++){var d=void 0,p=void 0;f==s.length-1?(p=s.join(" "),d=l):(p=s.slice(0,f+1).join(" "),d="...");var g=r[p];if(!g)r[p]=d;else if(g!=d)throw new Error("Inconsistent bindings for "+p)}delete t[o]}for(var y in r)t[y]=r[y];return t}function el(t,r,o,l){r=Hs(r);var s=r.call?r.call(t,l):r[t];if(s===!1)return"nothing";if(s==="...")return"multi";if(s!=null&&o(s))return"handled";if(r.fallthrough){if(Object.prototype.toString.call(r.fallthrough)!="[object Array]")return el(t,r.fallthrough,o,l);for(var f=0;f<r.fallthrough.length;f++){var d=el(t,r.fallthrough[f],o,l);if(d)return d}}}function fp(t){var r=typeof t=="string"?t:$r[t.keyCode];return r=="Ctrl"||r=="Alt"||r=="Shift"||r=="Mod"}function cp(t,r,o){var l=t;return r.altKey&&l!="Alt"&&(t="Alt-"+t),(Ce?r.metaKey:r.ctrlKey)&&l!="Ctrl"&&(t="Ctrl-"+t),(Ce?r.ctrlKey:r.metaKey)&&l!="Mod"&&(t="Cmd-"+t),!o&&r.shiftKey&&l!="Shift"&&(t="Shift-"+t),t}function dp(t,r){if(B&&t.keyCode==34&&t.char)return!1;var o=$r[t.keyCode];return o==null||t.altGraphKey?!1:(t.keyCode==3&&t.code&&(o=t.code),cp(o,t,r))}function Hs(t){return typeof t=="string"?wr[t]:t}function tl(t,r){for(var o=t.doc.sel.ranges,l=[],s=0;s<o.length;s++){for(var f=r(o[s]);l.length&&j(f.from,_e(l).to)<=0;){var d=l.pop();if(j(d.from,f.from)<0){f.from=d.from;break}}l.push(f)}nn(t,function(){for(var p=l.length-1;p>=0;p--)Zi(t.doc,"",l[p].from,l[p].to,"+delete");qi(t)})}function nf(t,r,o){var l=De(t.text,r+o,o);return l<0||l>t.text.length?null:l}function rf(t,r,o){var l=nf(t,r.ch,o);return l==null?null:new D(r.line,l,o<0?"after":"before")}function lf(t,r,o,l,s){if(t){r.doc.direction=="rtl"&&(s=-s);var f=Ln(o,r.doc.direction);if(f){var d=s<0?_e(f):f[0],p=s<0==(d.level==1),g=p?"after":"before",y;if(d.level>0||r.doc.direction=="rtl"){var k=ji(r,o);y=s<0?o.text.length-1:0;var N=tr(r,k,y).top;y=Ee(function(I){return tr(r,k,I).top==N},s<0==(d.level==1)?d.from:d.to-1,y),g=="before"&&(y=nf(o,y,1))}else y=s<0?d.to:d.from;return new D(l,y,g)}}return new D(l,s<0?o.text.length:0,s<0?"before":"after")}function yw(t,r,o,l){var s=Ln(r,t.doc.direction);if(!s)return rf(r,o,l);o.ch>=r.text.length?(o.ch=r.text.length,o.sticky="before"):o.ch<=0&&(o.ch=0,o.sticky="after");var f=Tn(s,o.ch,o.sticky),d=s[f];if(t.doc.direction=="ltr"&&d.level%2==0&&(l>0?d.to>o.ch:d.from<o.ch))return rf(r,o,l);var p=function(re,se){return nf(r,re instanceof D?re.ch:re,se)},g,y=function(re){return t.options.lineWrapping?(g=g||ji(t,r),mh(t,r,g,re)):{begin:0,end:r.text.length}},k=y(o.sticky=="before"?p(o,-1):o.ch);if(t.doc.direction=="rtl"||d.level==1){var N=d.level==1==l<0,I=p(o,N?1:-1);if(I!=null&&(N?I<=d.to&&I<=k.end:I>=d.from&&I>=k.begin)){var O=N?"before":"after";return new D(o.line,I,O)}}var $=function(re,se,ie){for(var ae=function(Ue,wt){return wt?new D(o.line,p(Ue,1),"before"):new D(o.line,Ue,"after")};re>=0&&re<s.length;re+=se){var me=s[re],ve=se>0==(me.level!=1),Me=ve?ie.begin:p(ie.end,-1);if(me.from<=Me&&Me<me.to||(Me=ve?me.from:p(me.to,-1),ie.begin<=Me&&Me<ie.end))return ae(Me,ve)}},q=$(f+l,l,k);if(q)return q;var J=l>0?k.end:p(k.begin,-1);return J!=null&&!(l>0&&J==r.text.length)&&(q=$(l>0?0:s.length-1,l,y(J)),q)?q:null}var ho={selectAll:Yh,singleSelection:function(t){return t.setSelection(t.getCursor("anchor"),t.getCursor("head"),Ke)},killLine:function(t){return tl(t,function(r){if(r.empty()){var o=de(t.doc,r.head.line).text.length;return r.head.ch==o&&r.head.line<t.lastLine()?{from:r.head,to:D(r.head.line+1,0)}:{from:r.head,to:D(r.head.line,o)}}else return{from:r.from(),to:r.to()}})},deleteLine:function(t){return tl(t,function(r){return{from:D(r.from().line,0),to:Le(t.doc,D(r.to().line+1,0))}})},delLineLeft:function(t){return tl(t,function(r){return{from:D(r.from().line,0),to:r.from()}})},delWrappedLineLeft:function(t){return tl(t,function(r){var o=t.charCoords(r.head,"div").top+5,l=t.coordsChar({left:0,top:o},"div");return{from:l,to:r.from()}})},delWrappedLineRight:function(t){return tl(t,function(r){var o=t.charCoords(r.head,"div").top+5,l=t.coordsChar({left:t.display.lineDiv.offsetWidth+100,top:o},"div");return{from:r.from(),to:l}})},undo:function(t){return t.undo()},redo:function(t){return t.redo()},undoSelection:function(t){return t.undoSelection()},redoSelection:function(t){return t.redoSelection()},goDocStart:function(t){return t.extendSelection(D(t.firstLine(),0))},goDocEnd:function(t){return t.extendSelection(D(t.lastLine()))},goLineStart:function(t){return t.extendSelectionsBy(function(r){return hp(t,r.head.line)},{origin:"+move",bias:1})},goLineStartSmart:function(t){return t.extendSelectionsBy(function(r){return pp(t,r.head)},{origin:"+move",bias:1})},goLineEnd:function(t){return t.extendSelectionsBy(function(r){return ww(t,r.head.line)},{origin:"+move",bias:-1})},goLineRight:function(t){return t.extendSelectionsBy(function(r){var o=t.cursorCoords(r.head,"div").top+5;return t.coordsChar({left:t.display.lineDiv.offsetWidth+100,top:o},"div")},Qn)},goLineLeft:function(t){return t.extendSelectionsBy(function(r){var o=t.cursorCoords(r.head,"div").top+5;return t.coordsChar({left:0,top:o},"div")},Qn)},goLineLeftSmart:function(t){return t.extendSelectionsBy(function(r){var o=t.cursorCoords(r.head,"div").top+5,l=t.coordsChar({left:0,top:o},"div");return l.ch<t.getLine(l.line).search(/\S/)?pp(t,r.head):l},Qn)},goLineUp:function(t){return t.moveV(-1,"line")},goLineDown:function(t){return t.moveV(1,"line")},goPageUp:function(t){return t.moveV(-1,"page")},goPageDown:function(t){return t.moveV(1,"page")},goCharLeft:function(t){return t.moveH(-1,"char")},goCharRight:function(t){return t.moveH(1,"char")},goColumnLeft:function(t){return t.moveH(-1,"column")},goColumnRight:function(t){return t.moveH(1,"column")},goWordLeft:function(t){return t.moveH(-1,"word")},goGroupRight:function(t){return t.moveH(1,"group")},goGroupLeft:function(t){return t.moveH(-1,"group")},goWordRight:function(t){return t.moveH(1,"word")},delCharBefore:function(t){return t.deleteH(-1,"codepoint")},delCharAfter:function(t){return t.deleteH(1,"char")},delWordBefore:function(t){return t.deleteH(-1,"word")},delWordAfter:function(t){return t.deleteH(1,"word")},delGroupBefore:function(t){return t.deleteH(-1,"group")},delGroupAfter:function(t){return t.deleteH(1,"group")},indentAuto:function(t){return t.indentSelection("smart")},indentMore:function(t){return t.indentSelection("add")},indentLess:function(t){return t.indentSelection("subtract")},insertTab:function(t){return t.replaceSelection("	")},insertSoftTab:function(t){for(var r=[],o=t.listSelections(),l=t.options.tabSize,s=0;s<o.length;s++){var f=o[s].from(),d=ce(t.getLine(f.line),f.ch,l);r.push(dn(l-d%l))}t.replaceSelections(r)},defaultTab:function(t){t.somethingSelected()?t.indentSelection("add"):t.execCommand("insertTab")},transposeChars:function(t){return nn(t,function(){for(var r=t.listSelections(),o=[],l=0;l<r.length;l++)if(r[l].empty()){var s=r[l].head,f=de(t.doc,s.line).text;if(f){if(s.ch==f.length&&(s=new D(s.line,s.ch-1)),s.ch>0)s=new D(s.line,s.ch+1),t.replaceRange(f.charAt(s.ch-1)+f.charAt(s.ch-2),D(s.line,s.ch-2),s,"+transpose");else if(s.line>t.doc.first){var d=de(t.doc,s.line-1).text;d&&(s=new D(s.line,1),t.replaceRange(f.charAt(0)+t.doc.lineSeparator()+d.charAt(d.length-1),D(s.line-1,d.length-1),s,"+transpose"))}}o.push(new Fe(s,s))}t.setSelections(o)})},newlineAndIndent:function(t){return nn(t,function(){for(var r=t.listSelections(),o=r.length-1;o>=0;o--)t.replaceRange(t.doc.lineSeparator(),r[o].anchor,r[o].head,"+input");r=t.listSelections();for(var l=0;l<r.length;l++)t.indentLine(r[l].from().line,null,!0);qi(t)})},openLine:function(t){return t.replaceSelection(`
`,"start")},toggleOverwrite:function(t){return t.toggleOverwrite()}};function hp(t,r){var o=de(t.doc,r),l=Rn(o);return l!=o&&(r=Re(l)),lf(!0,t,l,r,1)}function ww(t,r){var o=de(t.doc,r),l=ny(o);return l!=o&&(r=Re(l)),lf(!0,t,o,r,-1)}function pp(t,r){var o=hp(t,r.line),l=de(t.doc,o.line),s=Ln(l,t.doc.direction);if(!s||s[0].level==0){var f=Math.max(o.ch,l.text.search(/\S/)),d=r.line==o.line&&r.ch<=f&&r.ch;return D(o.line,d?0:f,o.sticky)}return o}function Us(t,r,o){if(typeof r=="string"&&(r=ho[r],!r))return!1;t.display.input.ensurePolled();var l=t.display.shift,s=!1;try{t.isReadOnly()&&(t.state.suppressEdits=!0),o&&(t.display.shift=!1),s=r(t)!=We}finally{t.display.shift=l,t.state.suppressEdits=!1}return s}function xw(t,r,o){for(var l=0;l<t.state.keyMaps.length;l++){var s=el(r,t.state.keyMaps[l],o,t);if(s)return s}return t.options.extraKeys&&el(r,t.options.extraKeys,o,t)||el(r,t.options.keyMap,o,t)}var Sw=new Ne;function po(t,r,o,l){var s=t.state.keySeq;if(s){if(fp(r))return"handled";if(/\'$/.test(r)?t.state.keySeq=null:Sw.set(50,function(){t.state.keySeq==s&&(t.state.keySeq=null,t.display.input.reset())}),vp(t,s+" "+r,o,l))return!0}return vp(t,r,o,l)}function vp(t,r,o,l){var s=xw(t,r,l);return s=="multi"&&(t.state.keySeq=r),s=="handled"&&gt(t,"keyHandled",t,r,o),(s=="handled"||s=="multi")&&(bt(o),Bu(t)),!!s}function gp(t,r){var o=dp(r,!0);return o?r.shiftKey&&!t.state.keySeq?po(t,"Shift-"+o,r,function(l){return Us(t,l,!0)})||po(t,o,r,function(l){if(typeof l=="string"?/^go[A-Z]/.test(l):l.motion)return Us(t,l)}):po(t,o,r,function(l){return Us(t,l)}):!1}function kw(t,r,o){return po(t,"'"+o+"'",r,function(l){return Us(t,l,!0)})}var of=null;function mp(t){var r=this;if(!(t.target&&t.target!=r.display.input.getField())&&(r.curOp.focus=b(Se(r)),!Je(r,t))){m&&x<11&&t.keyCode==27&&(t.returnValue=!1);var o=t.keyCode;r.display.shift=o==16||t.shiftKey;var l=gp(r,t);B&&(of=l?o:null,!l&&o==88&&!Yn&&(G?t.metaKey:t.ctrlKey)&&r.replaceSelection("",null,"cut")),u&&!G&&!l&&o==46&&t.shiftKey&&!t.ctrlKey&&document.execCommand&&document.execCommand("cut"),o==18&&!/\bCodeMirror-crosshair\b/.test(r.display.lineDiv.className)&&Cw(r)}}function Cw(t){var r=t.display.lineDiv;U(r,"CodeMirror-crosshair");function o(l){(l.keyCode==18||!l.altKey)&&(V(r,"CodeMirror-crosshair"),Et(document,"keyup",o),Et(document,"mouseover",o))}oe(document,"keyup",o),oe(document,"mouseover",o)}function yp(t){t.keyCode==16&&(this.doc.sel.shift=!1),Je(this,t)}function wp(t){var r=this;if(!(t.target&&t.target!=r.display.input.getField())&&!(mr(r.display,t)||Je(r,t)||t.ctrlKey&&!t.altKey||G&&t.metaKey)){var o=t.keyCode,l=t.charCode;if(B&&o==of){of=null,bt(t);return}if(!(B&&(!t.which||t.which<10)&&gp(r,t))){var s=String.fromCharCode(l??o);s!="\b"&&(kw(r,t,s)||r.display.input.onKeyPress(t))}}}var Tw=400,sf=function(t,r,o){this.time=t,this.pos=r,this.button=o};sf.prototype.compare=function(t,r,o){return this.time+Tw>t&&j(r,this.pos)==0&&o==this.button};var vo,go;function Lw(t,r){var o=+new Date;return go&&go.compare(o,t,r)?(vo=go=null,"triple"):vo&&vo.compare(o,t,r)?(go=new sf(o,t,r),vo=null,"double"):(vo=new sf(o,t,r),go=null,"single")}function xp(t){var r=this,o=r.display;if(!(Je(r,t)||o.activeTouch&&o.input.supportsTouch())){if(o.input.ensurePolled(),o.shift=t.shiftKey,mr(o,t)){L||(o.scroller.draggable=!1,setTimeout(function(){return o.scroller.draggable=!0},100));return}if(!af(r,t)){var l=vi(r,t),s=Hl(t),f=l?Lw(l,s):"single";je(r).focus(),s==1&&r.state.selectingText&&r.state.selectingText(t),!(l&&Nw(r,s,l,f,t))&&(s==1?l?bw(r,l,f,t):$t(t)==o.scroller&&bt(t):s==2?(l&&As(r.doc,l),setTimeout(function(){return o.input.focus()},20)):s==3&&(we?r.display.input.onContextMenu(t):Hu(r)))}}}function Nw(t,r,o,l,s){var f="Click";return l=="double"?f="Double"+f:l=="triple"&&(f="Triple"+f),f=(r==1?"Left":r==2?"Middle":"Right")+f,po(t,cp(f,s),s,function(d){if(typeof d=="string"&&(d=ho[d]),!d)return!1;var p=!1;try{t.isReadOnly()&&(t.state.suppressEdits=!0),p=d(t,o)!=We}finally{t.state.suppressEdits=!1}return p})}function Ew(t,r,o){var l=t.getOption("configureMouse"),s=l?l(t,r,o):{};if(s.unit==null){var f=Q?o.shiftKey&&o.metaKey:o.altKey;s.unit=f?"rectangle":r=="single"?"char":r=="double"?"word":"line"}return(s.extend==null||t.doc.extend)&&(s.extend=t.doc.extend||o.shiftKey),s.addNew==null&&(s.addNew=G?o.metaKey:o.ctrlKey),s.moveOnDrag==null&&(s.moveOnDrag=!(G?o.altKey:o.ctrlKey)),s}function bw(t,r,o,l){m?setTimeout(Ye(Sh,t),0):t.curOp.focus=b(Se(t));var s=Ew(t,o,l),f=t.doc.sel,d;t.options.dragDrop&&wu&&!t.isReadOnly()&&o=="single"&&(d=f.contains(r))>-1&&(j((d=f.ranges[d]).from(),r)<0||r.xRel>0)&&(j(d.to(),r)>0||r.xRel<0)?Mw(t,l,r,s):_w(t,l,r,s)}function Mw(t,r,o,l){var s=t.display,f=!1,d=mt(t,function(y){L&&(s.scroller.draggable=!1),t.state.draggingText=!1,t.state.delayingBlurEvent&&(t.hasFocus()?t.state.delayingBlurEvent=!1:Hu(t)),Et(s.wrapper.ownerDocument,"mouseup",d),Et(s.wrapper.ownerDocument,"mousemove",p),Et(s.scroller,"dragstart",g),Et(s.scroller,"drop",d),f||(bt(y),l.addNew||As(t.doc,o,null,null,l.extend),L&&!Y||m&&x==9?setTimeout(function(){s.wrapper.ownerDocument.body.focus({preventScroll:!0}),s.input.focus()},20):s.input.focus())}),p=function(y){f=f||Math.abs(r.clientX-y.clientX)+Math.abs(r.clientY-y.clientY)>=10},g=function(){return f=!0};L&&(s.scroller.draggable=!0),t.state.draggingText=d,d.copy=!l.moveOnDrag,oe(s.wrapper.ownerDocument,"mouseup",d),oe(s.wrapper.ownerDocument,"mousemove",p),oe(s.scroller,"dragstart",g),oe(s.scroller,"drop",d),t.state.delayingBlurEvent=!0,setTimeout(function(){return s.input.focus()},20),s.scroller.dragDrop&&s.scroller.dragDrop()}function Sp(t,r,o){if(o=="char")return new Fe(r,r);if(o=="word")return t.findWordAt(r);if(o=="line")return new Fe(D(r.line,0),Le(t.doc,D(r.line+1,0)));var l=o(t,r);return new Fe(l.from,l.to)}function _w(t,r,o,l){m&&Hu(t);var s=t.display,f=t.doc;bt(r);var d,p,g=f.sel,y=g.ranges;if(l.addNew&&!l.extend?(p=f.sel.contains(o),p>-1?d=y[p]:d=new Fe(o,o)):(d=f.sel.primary(),p=f.sel.primIndex),l.unit=="rectangle")l.addNew||(d=new Fe(o,o)),o=vi(t,r,!0,!0),p=-1;else{var k=Sp(t,o,l.unit);l.extend?d=ef(d,k.anchor,k.head,l.extend):d=k}l.addNew?p==-1?(p=y.length,Mt(f,Wn(t,y.concat([d]),p),{scroll:!1,origin:"*mouse"})):y.length>1&&y[p].empty()&&l.unit=="char"&&!l.extend?(Mt(f,Wn(t,y.slice(0,p).concat(y.slice(p+1)),0),{scroll:!1,origin:"*mouse"}),g=f.sel):tf(f,p,d,Vn):(p=0,Mt(f,new vn([d],0),Vn),g=f.sel);var N=o;function I(ie){if(j(N,ie)!=0)if(N=ie,l.unit=="rectangle"){for(var ae=[],me=t.options.tabSize,ve=ce(de(f,o.line).text,o.ch,me),Me=ce(de(f,ie.line).text,ie.ch,me),Ue=Math.min(ve,Me),wt=Math.max(ve,Me),Xe=Math.min(o.line,ie.line),rn=Math.min(t.lastLine(),Math.max(o.line,ie.line));Xe<=rn;Xe++){var qt=de(f,Xe).text,st=kn(qt,Ue,me);Ue==wt?ae.push(new Fe(D(Xe,st),D(Xe,st))):qt.length>st&&ae.push(new Fe(D(Xe,st),D(Xe,kn(qt,wt,me))))}ae.length||ae.push(new Fe(o,o)),Mt(f,Wn(t,g.ranges.slice(0,p).concat(ae),p),{origin:"*mouse",scroll:!1}),t.scrollIntoView(ie)}else{var Vt=d,Lt=Sp(t,ie,l.unit),ht=Vt.anchor,at;j(Lt.anchor,ht)>0?(at=Lt.head,ht=dt(Vt.from(),Lt.anchor)):(at=Lt.anchor,ht=Ie(Vt.to(),Lt.head));var tt=g.ranges.slice(0);tt[p]=Dw(t,new Fe(Le(f,ht),at)),Mt(f,Wn(t,tt,p),Vn)}}var O=s.wrapper.getBoundingClientRect(),$=0;function q(ie){var ae=++$,me=vi(t,ie,!0,l.unit=="rectangle");if(me)if(j(me,N)!=0){t.curOp.focus=b(Se(t)),I(me);var ve=_s(s,f);(me.line>=ve.to||me.line<ve.from)&&setTimeout(mt(t,function(){$==ae&&q(ie)}),150)}else{var Me=ie.clientY<O.top?-20:ie.clientY>O.bottom?20:0;Me&&setTimeout(mt(t,function(){$==ae&&(s.scroller.scrollTop+=Me,q(ie))}),50)}}function J(ie){t.state.selectingText=!1,$=1/0,ie&&(bt(ie),s.input.focus()),Et(s.wrapper.ownerDocument,"mousemove",re),Et(s.wrapper.ownerDocument,"mouseup",se),f.history.lastSelOrigin=null}var re=mt(t,function(ie){ie.buttons===0||!Hl(ie)?J(ie):q(ie)}),se=mt(t,J);t.state.selectingText=se,oe(s.wrapper.ownerDocument,"mousemove",re),oe(s.wrapper.ownerDocument,"mouseup",se)}function Dw(t,r){var o=r.anchor,l=r.head,s=de(t.doc,o.line);if(j(o,l)==0&&o.sticky==l.sticky)return r;var f=Ln(s);if(!f)return r;var d=Tn(f,o.ch,o.sticky),p=f[d];if(p.from!=o.ch&&p.to!=o.ch)return r;var g=d+(p.from==o.ch==(p.level!=1)?0:1);if(g==0||g==f.length)return r;var y;if(l.line!=o.line)y=(l.line-o.line)*(t.doc.direction=="ltr"?1:-1)>0;else{var k=Tn(f,l.ch,l.sticky),N=k-d||(l.ch-o.ch)*(p.level==1?-1:1);k==g-1||k==g?y=N<0:y=N>0}var I=f[g+(y?-1:0)],O=y==(I.level==1),$=O?I.from:I.to,q=O?"after":"before";return o.ch==$&&o.sticky==q?r:new Fe(new D(o.line,$,q),l)}function kp(t,r,o,l){var s,f;if(r.touches)s=r.touches[0].clientX,f=r.touches[0].clientY;else try{s=r.clientX,f=r.clientY}catch{return!1}if(s>=Math.floor(t.display.gutters.getBoundingClientRect().right))return!1;l&&bt(r);var d=t.display,p=d.lineDiv.getBoundingClientRect();if(f>p.bottom||!zt(t,o))return Bl(r);f-=p.top-d.viewOffset;for(var g=0;g<t.display.gutterSpecs.length;++g){var y=d.gutters.childNodes[g];if(y&&y.getBoundingClientRect().right>=s){var k=Zn(t.doc,f),N=t.display.gutterSpecs[g];return $e(t,o,t,k,N.className,r),Bl(r)}}}function af(t,r){return kp(t,r,"gutterClick",!0)}function Cp(t,r){mr(t.display,r)||Pw(t,r)||Je(t,r,"contextmenu")||we||t.display.input.onContextMenu(r)}function Pw(t,r){return zt(t,"gutterContextMenu")?kp(t,r,"gutterContextMenu",!1):!1}function Tp(t){t.display.wrapper.className=t.display.wrapper.className.replace(/\s*cm-s-\S+/g,"")+t.options.theme.replace(/(^|\s)\s*/g," cm-s-"),Ql(t)}var nl={toString:function(){return"CodeMirror.Init"}},Lp={},js={};function Ow(t){var r=t.optionHandlers;function o(l,s,f,d){t.defaults[l]=s,f&&(r[l]=d?function(p,g,y){y!=nl&&f(p,g,y)}:f)}t.defineOption=o,t.Init=nl,o("value","",function(l,s){return l.setValue(s)},!0),o("mode",null,function(l,s){l.doc.modeOption=s,Xu(l)},!0),o("indentUnit",2,Xu,!0),o("indentWithTabs",!1),o("smartIndent",!0),o("tabSize",4,function(l){ro(l),Ql(l),Kt(l)},!0),o("lineSeparator",null,function(l,s){if(l.doc.lineSep=s,!!s){var f=[],d=l.doc.first;l.doc.iter(function(g){for(var y=0;;){var k=g.text.indexOf(s,y);if(k==-1)break;y=k+s.length,f.push(D(d,k))}d++});for(var p=f.length-1;p>=0;p--)Zi(l.doc,s,f[p],D(f[p].line,f[p].ch+s.length))}}),o("specialChars",/[\u0000-\u001f\u007f-\u009f\u00ad\u061c\u200b\u200e\u200f\u2028\u2029\u202d\u202e\u2066\u2067\u2069\ufeff\ufff9-\ufffc]/g,function(l,s,f){l.state.specialChars=new RegExp(s.source+(s.test("	")?"":"|	"),"g"),f!=nl&&l.refresh()}),o("specialCharPlaceholder",ay,function(l){return l.refresh()},!0),o("electricChars",!0),o("inputStyle",A?"contenteditable":"textarea",function(){throw new Error("inputStyle can not (yet) be changed in a running editor")},!0),o("spellcheck",!1,function(l,s){return l.getInputField().spellcheck=s},!0),o("autocorrect",!1,function(l,s){return l.getInputField().autocorrect=s},!0),o("autocapitalize",!1,function(l,s){return l.getInputField().autocapitalize=s},!0),o("rtlMoveVisually",!z),o("wholeLineUpdateBefore",!0),o("theme","default",function(l){Tp(l),no(l)},!0),o("keyMap","default",function(l,s,f){var d=Hs(s),p=f!=nl&&Hs(f);p&&p.detach&&p.detach(l,d),d.attach&&d.attach(l,p||null)}),o("extraKeys",null),o("configureMouse",null),o("lineWrapping",!1,Iw,!0),o("gutters",[],function(l,s){l.display.gutterSpecs=Qu(s,l.options.lineNumbers),no(l)},!0),o("fixedGutter",!0,function(l,s){l.display.gutters.style.left=s?Ru(l.display)+"px":"0",l.refresh()},!0),o("coverGutterNextToScrollbar",!1,function(l){return Vi(l)},!0),o("scrollbarStyle","native",function(l){Eh(l),Vi(l),l.display.scrollbars.setScrollTop(l.doc.scrollTop),l.display.scrollbars.setScrollLeft(l.doc.scrollLeft)},!0),o("lineNumbers",!1,function(l,s){l.display.gutterSpecs=Qu(l.options.gutters,s),no(l)},!0),o("firstLineNumber",1,no,!0),o("lineNumberFormatter",function(l){return l},no,!0),o("showCursorWhenSelecting",!1,Yl,!0),o("resetSelectionOnContextMenu",!0),o("lineWiseCopyCut",!0),o("pasteLinesPerSelection",!0),o("selectionsMayTouch",!1),o("readOnly",!1,function(l,s){s=="nocursor"&&(Gi(l),l.display.input.blur()),l.display.input.readOnlyChanged(s)}),o("screenReaderLabel",null,function(l,s){s=s===""?null:s,l.display.input.screenReaderLabelChanged(s)}),o("disableInput",!1,function(l,s){s||l.display.input.reset()},!0),o("dragDrop",!0,zw),o("allowDropFileTypes",null),o("cursorBlinkRate",530),o("cursorScrollMargin",0),o("cursorHeight",1,Yl,!0),o("singleCursorHeightPerLine",!0,Yl,!0),o("workTime",100),o("workDelay",100),o("flattenSpans",!0,ro,!0),o("addModeClass",!1,ro,!0),o("pollInterval",100),o("undoDepth",200,function(l,s){return l.doc.history.undoDepth=s}),o("historyEventDelay",1250),o("viewportMargin",10,function(l){return l.refresh()},!0),o("maxHighlightLength",1e4,ro,!0),o("moveInputWithCursor",!0,function(l,s){s||l.display.input.resetPosition()}),o("tabindex",null,function(l,s){return l.display.input.getField().tabIndex=s||""}),o("autofocus",null),o("direction","ltr",function(l,s){return l.doc.setDirection(s)},!0),o("phrases",null)}function zw(t,r,o){var l=o&&o!=nl;if(!r!=!l){var s=t.display.dragFunctions,f=r?oe:Et;f(t.display.scroller,"dragstart",s.start),f(t.display.scroller,"dragenter",s.enter),f(t.display.scroller,"dragover",s.over),f(t.display.scroller,"dragleave",s.leave),f(t.display.scroller,"drop",s.drop)}}function Iw(t){t.options.lineWrapping?(U(t.display.wrapper,"CodeMirror-wrap"),t.display.sizer.style.minWidth="",t.display.sizerWidth=null):(V(t.display.wrapper,"CodeMirror-wrap"),Eu(t)),Fu(t),Kt(t),Ql(t),setTimeout(function(){return Vi(t)},100)}function qe(t,r){var o=this;if(!(this instanceof qe))return new qe(t,r);this.options=r=r?pe(r):{},pe(Lp,r,!1);var l=r.value;typeof l=="string"?l=new Gt(l,r.mode,null,r.lineSeparator,r.direction):r.mode&&(l.modeOption=r.mode),this.doc=l;var s=new qe.inputStyles[r.inputStyle](this),f=this.display=new Vy(t,l,s,r);f.wrapper.CodeMirror=this,Tp(this),r.lineWrapping&&(this.display.wrapper.className+=" CodeMirror-wrap"),Eh(this),this.state={keyMaps:[],overlays:[],modeGen:0,overwrite:!1,delayingBlurEvent:!1,focused:!1,suppressEdits:!1,pasteIncoming:-1,cutIncoming:-1,selectingText:!1,draggingText:!1,highlight:new Ne,keySeq:null,specialChars:null},r.autofocus&&!A&&f.input.focus(),m&&x<11&&setTimeout(function(){return o.display.input.reset(!0)},20),Aw(this),hw(),wi(this),this.curOp.forceUpdate=!0,Rh(this,l),r.autofocus&&!A||this.hasFocus()?setTimeout(function(){o.hasFocus()&&!o.state.focused&&Uu(o)},20):Gi(this);for(var d in js)js.hasOwnProperty(d)&&js[d](this,r[d],nl);_h(this),r.finishInit&&r.finishInit(this);for(var p=0;p<uf.length;++p)uf[p](this);xi(this),L&&r.lineWrapping&&getComputedStyle(f.lineDiv).textRendering=="optimizelegibility"&&(f.lineDiv.style.textRendering="auto")}qe.defaults=Lp,qe.optionHandlers=js;function Aw(t){var r=t.display;oe(r.scroller,"mousedown",mt(t,xp)),m&&x<11?oe(r.scroller,"dblclick",mt(t,function(g){if(!Je(t,g)){var y=vi(t,g);if(!(!y||af(t,g)||mr(t.display,g))){bt(g);var k=t.findWordAt(y);As(t.doc,k.anchor,k.head)}}})):oe(r.scroller,"dblclick",function(g){return Je(t,g)||bt(g)}),oe(r.scroller,"contextmenu",function(g){return Cp(t,g)}),oe(r.input.getField(),"contextmenu",function(g){r.scroller.contains(g.target)||Cp(t,g)});var o,l={end:0};function s(){r.activeTouch&&(o=setTimeout(function(){return r.activeTouch=null},1e3),l=r.activeTouch,l.end=+new Date)}function f(g){if(g.touches.length!=1)return!1;var y=g.touches[0];return y.radiusX<=1&&y.radiusY<=1}function d(g,y){if(y.left==null)return!0;var k=y.left-g.left,N=y.top-g.top;return k*k+N*N>20*20}oe(r.scroller,"touchstart",function(g){if(!Je(t,g)&&!f(g)&&!af(t,g)){r.input.ensurePolled(),clearTimeout(o);var y=+new Date;r.activeTouch={start:y,moved:!1,prev:y-l.end<=300?l:null},g.touches.length==1&&(r.activeTouch.left=g.touches[0].pageX,r.activeTouch.top=g.touches[0].pageY)}}),oe(r.scroller,"touchmove",function(){r.activeTouch&&(r.activeTouch.moved=!0)}),oe(r.scroller,"touchend",function(g){var y=r.activeTouch;if(y&&!mr(r,g)&&y.left!=null&&!y.moved&&new Date-y.start<300){var k=t.coordsChar(r.activeTouch,"page"),N;!y.prev||d(y,y.prev)?N=new Fe(k,k):!y.prev.prev||d(y,y.prev.prev)?N=t.findWordAt(k):N=new Fe(D(k.line,0),Le(t.doc,D(k.line+1,0))),t.setSelection(N.anchor,N.head),t.focus(),bt(g)}s()}),oe(r.scroller,"touchcancel",s),oe(r.scroller,"scroll",function(){r.scroller.clientHeight&&(Zl(t,r.scroller.scrollTop),mi(t,r.scroller.scrollLeft,!0),$e(t,"scroll",t))}),oe(r.scroller,"mousewheel",function(g){return Oh(t,g)}),oe(r.scroller,"DOMMouseScroll",function(g){return Oh(t,g)}),oe(r.wrapper,"scroll",function(){return r.wrapper.scrollTop=r.wrapper.scrollLeft=0}),r.dragFunctions={enter:function(g){Je(t,g)||cr(g)},over:function(g){Je(t,g)||(dw(t,g),cr(g))},start:function(g){return cw(t,g)},drop:mt(t,fw),leave:function(g){Je(t,g)||sp(t)}};var p=r.input.getField();oe(p,"keyup",function(g){return yp.call(t,g)}),oe(p,"keydown",mt(t,mp)),oe(p,"keypress",mt(t,wp)),oe(p,"focus",function(g){return Uu(t,g)}),oe(p,"blur",function(g){return Gi(t,g)})}var uf=[];qe.defineInitHook=function(t){return uf.push(t)};function mo(t,r,o,l){var s=t.doc,f;o==null&&(o="add"),o=="smart"&&(s.mode.indent?f=$l(t,r).state:o="prev");var d=t.options.tabSize,p=de(s,r),g=ce(p.text,null,d);p.stateAfter&&(p.stateAfter=null);var y=p.text.match(/^\s*/)[0],k;if(!l&&!/\S/.test(p.text))k=0,o="not";else if(o=="smart"&&(k=s.mode.indent(f,p.text.slice(y.length),p.text),k==We||k>150)){if(!l)return;o="prev"}o=="prev"?r>s.first?k=ce(de(s,r-1).text,null,d):k=0:o=="add"?k=g+t.options.indentUnit:o=="subtract"?k=g-t.options.indentUnit:typeof o=="number"&&(k=g+o),k=Math.max(0,k);var N="",I=0;if(t.options.indentWithTabs)for(var O=Math.floor(k/d);O;--O)I+=d,N+="	";if(I<k&&(N+=dn(k-I)),N!=y)return Zi(s,N,D(r,0),D(r,y.length),"+input"),p.stateAfter=null,!0;for(var $=0;$<s.sel.ranges.length;$++){var q=s.sel.ranges[$];if(q.head.line==r&&q.head.ch<y.length){var J=D(r,y.length);tf(s,$,new Fe(J,J));break}}}var Bn=null;function $s(t){Bn=t}function ff(t,r,o,l,s){var f=t.doc;t.display.shift=!1,l||(l=f.sel);var d=+new Date-200,p=s=="paste"||t.state.pasteIncoming>d,g=Ul(r),y=null;if(p&&l.ranges.length>1)if(Bn&&Bn.text.join(`
`)==r){if(l.ranges.length%Bn.text.length==0){y=[];for(var k=0;k<Bn.text.length;k++)y.push(f.splitLines(Bn.text[k]))}}else g.length==l.ranges.length&&t.options.pasteLinesPerSelection&&(y=ar(g,function(re){return[re]}));for(var N=t.curOp.updateInput,I=l.ranges.length-1;I>=0;I--){var O=l.ranges[I],$=O.from(),q=O.to();O.empty()&&(o&&o>0?$=D($.line,$.ch-o):t.state.overwrite&&!p?q=D(q.line,Math.min(de(f,q.line).text.length,q.ch+_e(g).length)):p&&Bn&&Bn.lineWise&&Bn.text.join(`
`)==g.join(`
`)&&($=q=D($.line,0)));var J={from:$,to:q,text:y?y[I%y.length]:g,origin:s||(p?"paste":t.state.cutIncoming>d?"cut":"+input")};Xi(t.doc,J),gt(t,"inputRead",t,J)}r&&!p&&Ep(t,r),qi(t),t.curOp.updateInput<2&&(t.curOp.updateInput=N),t.curOp.typing=!0,t.state.pasteIncoming=t.state.cutIncoming=-1}function Np(t,r){var o=t.clipboardData&&t.clipboardData.getData("Text");if(o)return t.preventDefault(),!r.isReadOnly()&&!r.options.disableInput&&r.hasFocus()&&nn(r,function(){return ff(r,o,0,null,"paste")}),!0}function Ep(t,r){if(!(!t.options.electricChars||!t.options.smartIndent))for(var o=t.doc.sel,l=o.ranges.length-1;l>=0;l--){var s=o.ranges[l];if(!(s.head.ch>100||l&&o.ranges[l-1].head.line==s.head.line)){var f=t.getModeAt(s.head),d=!1;if(f.electricChars){for(var p=0;p<f.electricChars.length;p++)if(r.indexOf(f.electricChars.charAt(p))>-1){d=mo(t,s.head.line,"smart");break}}else f.electricInput&&f.electricInput.test(de(t.doc,s.head.line).text.slice(0,s.head.ch))&&(d=mo(t,s.head.line,"smart"));d&&gt(t,"electricInput",t,s.head.line)}}}function bp(t){for(var r=[],o=[],l=0;l<t.doc.sel.ranges.length;l++){var s=t.doc.sel.ranges[l].head.line,f={anchor:D(s,0),head:D(s+1,0)};o.push(f),r.push(t.getRange(f.anchor,f.head))}return{text:r,ranges:o}}function Mp(t,r,o,l){t.setAttribute("autocorrect",o?"":"off"),t.setAttribute("autocapitalize",l?"":"off"),t.setAttribute("spellcheck",!!r)}function _p(){var t=_("textarea",null,null,"position: absolute; bottom: -1em; padding: 0; width: 1px; height: 1em; min-height: 1em; outline: none"),r=_("div",[t],null,"overflow: hidden; position: relative; width: 3px; height: 0px;");return L?t.style.width="1000px":t.setAttribute("wrap","off"),S&&(t.style.border="1px solid black"),Mp(t),r}function Rw(t){var r=t.optionHandlers,o=t.helpers={};t.prototype={constructor:t,focus:function(){je(this).focus(),this.display.input.focus()},setOption:function(l,s){var f=this.options,d=f[l];f[l]==s&&l!="mode"||(f[l]=s,r.hasOwnProperty(l)&&mt(this,r[l])(this,s,d),$e(this,"optionChange",this,l))},getOption:function(l){return this.options[l]},getDoc:function(){return this.doc},addKeyMap:function(l,s){this.state.keyMaps[s?"push":"unshift"](Hs(l))},removeKeyMap:function(l){for(var s=this.state.keyMaps,f=0;f<s.length;++f)if(s[f]==l||s[f].name==l)return s.splice(f,1),!0},addOverlay:At(function(l,s){var f=l.token?l:t.getMode(this.options,l);if(f.startState)throw new Error("Overlays may not be stateful.");ct(this.state.overlays,{mode:f,modeSpec:l,opaque:s&&s.opaque,priority:s&&s.priority||0},function(d){return d.priority}),this.state.modeGen++,Kt(this)}),removeOverlay:At(function(l){for(var s=this.state.overlays,f=0;f<s.length;++f){var d=s[f].modeSpec;if(d==l||typeof l=="string"&&d.name==l){s.splice(f,1),this.state.modeGen++,Kt(this);return}}}),indentLine:At(function(l,s,f){typeof s!="string"&&typeof s!="number"&&(s==null?s=this.options.smartIndent?"smart":"prev":s=s?"add":"subtract"),w(this.doc,l)&&mo(this,l,s,f)}),indentSelection:At(function(l){for(var s=this.doc.sel.ranges,f=-1,d=0;d<s.length;d++){var p=s[d];if(p.empty())p.head.line>f&&(mo(this,p.head.line,l,!0),f=p.head.line,d==this.doc.sel.primIndex&&qi(this));else{var g=p.from(),y=p.to(),k=Math.max(f,g.line);f=Math.min(this.lastLine(),y.line-(y.ch?0:1))+1;for(var N=k;N<f;++N)mo(this,N,l);var I=this.doc.sel.ranges;g.ch==0&&s.length==I.length&&I[d].from().ch>0&&tf(this.doc,d,new Fe(g,I[d].to()),Ke)}}}),getTokenAt:function(l,s){return Wd(this,l,s)},getLineTokens:function(l,s){return Wd(this,D(l),s,!0)},getTokenTypeAt:function(l){l=Le(this.doc,l);var s=Ad(this,de(this.doc,l.line)),f=0,d=(s.length-1)/2,p=l.ch,g;if(p==0)g=s[2];else for(;;){var y=f+d>>1;if((y?s[y*2-1]:0)>=p)d=y;else if(s[y*2+1]<p)f=y+1;else{g=s[y*2+2];break}}var k=g?g.indexOf("overlay "):-1;return k<0?g:k==0?null:g.slice(0,k-1)},getModeAt:function(l){var s=this.doc.mode;return s.innerMode?t.innerMode(s,this.getTokenAt(l).state).mode:s},getHelper:function(l,s){return this.getHelpers(l,s)[0]},getHelpers:function(l,s){var f=[];if(!o.hasOwnProperty(s))return f;var d=o[s],p=this.getModeAt(l);if(typeof p[s]=="string")d[p[s]]&&f.push(d[p[s]]);else if(p[s])for(var g=0;g<p[s].length;g++){var y=d[p[s][g]];y&&f.push(y)}else p.helperType&&d[p.helperType]?f.push(d[p.helperType]):d[p.name]&&f.push(d[p.name]);for(var k=0;k<d._global.length;k++){var N=d._global[k];N.pred(p,this)&&be(f,N.val)==-1&&f.push(N.val)}return f},getStateAfter:function(l,s){var f=this.doc;return l=pn(f,l??f.first+f.size-1),$l(this,l+1,s).state},cursorCoords:function(l,s){var f,d=this.doc.sel.primary();return l==null?f=d.head:typeof l=="object"?f=Le(this.doc,l):f=l?d.from():d.to(),Fn(this,f,s||"page")},charCoords:function(l,s){return Ns(this,Le(this.doc,l),s||"page")},coordsChar:function(l,s){return l=ph(this,l,s||"page"),zu(this,l.left,l.top)},lineAtHeight:function(l,s){return l=ph(this,{top:l,left:0},s||"page").top,Zn(this.doc,l+this.display.viewOffset)},heightAtLine:function(l,s,f){var d=!1,p;if(typeof l=="number"){var g=this.doc.first+this.doc.size-1;l<this.doc.first?l=this.doc.first:l>g&&(l=g,d=!0),p=de(this.doc,l)}else p=l;return Ls(this,p,{top:0,left:0},s||"page",f||d).top+(d?this.doc.height-gr(p):0)},defaultTextHeight:function(){return $i(this.display)},defaultCharWidth:function(){return Ki(this.display)},getViewport:function(){return{from:this.display.viewFrom,to:this.display.viewTo}},addWidget:function(l,s,f,d,p){var g=this.display;l=Fn(this,Le(this.doc,l));var y=l.bottom,k=l.left;if(s.style.position="absolute",s.setAttribute("cm-ignore-events","true"),this.display.input.setUneditable(s),g.sizer.appendChild(s),d=="over")y=l.top;else if(d=="above"||d=="near"){var N=Math.max(g.wrapper.clientHeight,this.doc.height),I=Math.max(g.sizer.clientWidth,g.lineSpace.clientWidth);(d=="above"||l.bottom+s.offsetHeight>N)&&l.top>s.offsetHeight?y=l.top-s.offsetHeight:l.bottom+s.offsetHeight<=N&&(y=l.bottom),k+s.offsetWidth>I&&(k=I-s.offsetWidth)}s.style.top=y+"px",s.style.left=s.style.right="",p=="right"?(k=g.sizer.clientWidth-s.offsetWidth,s.style.right="0px"):(p=="left"?k=0:p=="middle"&&(k=(g.sizer.clientWidth-s.offsetWidth)/2),s.style.left=k+"px"),f&&zy(this,{left:k,top:y,right:k+s.offsetWidth,bottom:y+s.offsetHeight})},triggerOnKeyDown:At(mp),triggerOnKeyPress:At(wp),triggerOnKeyUp:yp,triggerOnMouseDown:At(xp),execCommand:function(l){if(ho.hasOwnProperty(l))return ho[l].call(null,this)},triggerElectric:At(function(l){Ep(this,l)}),findPosH:function(l,s,f,d){var p=1;s<0&&(p=-1,s=-s);for(var g=Le(this.doc,l),y=0;y<s&&(g=cf(this.doc,g,p,f,d),!g.hitSide);++y);return g},moveH:At(function(l,s){var f=this;this.extendSelectionsBy(function(d){return f.display.shift||f.doc.extend||d.empty()?cf(f.doc,d.head,l,s,f.options.rtlMoveVisually):l<0?d.from():d.to()},Qn)}),deleteH:At(function(l,s){var f=this.doc.sel,d=this.doc;f.somethingSelected()?d.replaceSelection("",null,"+delete"):tl(this,function(p){var g=cf(d,p.head,l,s,!1);return l<0?{from:g,to:p.head}:{from:p.head,to:g}})}),findPosV:function(l,s,f,d){var p=1,g=d;s<0&&(p=-1,s=-s);for(var y=Le(this.doc,l),k=0;k<s;++k){var N=Fn(this,y,"div");if(g==null?g=N.left:N.left=g,y=Dp(this,N,p,f),y.hitSide)break}return y},moveV:At(function(l,s){var f=this,d=this.doc,p=[],g=!this.display.shift&&!d.extend&&d.sel.somethingSelected();if(d.extendSelectionsBy(function(k){if(g)return l<0?k.from():k.to();var N=Fn(f,k.head,"div");k.goalColumn!=null&&(N.left=k.goalColumn),p.push(N.left);var I=Dp(f,N,l,s);return s=="page"&&k==d.sel.primary()&&$u(f,Ns(f,I,"div").top-N.top),I},Qn),p.length)for(var y=0;y<d.sel.ranges.length;y++)d.sel.ranges[y].goalColumn=p[y]}),findWordAt:function(l){var s=this.doc,f=de(s,l.line).text,d=l.ch,p=l.ch;if(f){var g=this.getHelper(l,"wordChars");(l.sticky=="before"||p==f.length)&&d?--d:++p;for(var y=f.charAt(d),k=fr(y,g)?function(N){return fr(N,g)}:/\s/.test(y)?function(N){return/\s/.test(N)}:function(N){return!/\s/.test(N)&&!fr(N)};d>0&&k(f.charAt(d-1));)--d;for(;p<f.length&&k(f.charAt(p));)++p}return new Fe(D(l.line,d),D(l.line,p))},toggleOverwrite:function(l){l!=null&&l==this.state.overwrite||((this.state.overwrite=!this.state.overwrite)?U(this.display.cursorDiv,"CodeMirror-overwrite"):V(this.display.cursorDiv,"CodeMirror-overwrite"),$e(this,"overwriteToggle",this,this.state.overwrite))},hasFocus:function(){return this.display.input.getField()==b(Se(this))},isReadOnly:function(){return!!(this.options.readOnly||this.doc.cantEdit)},scrollTo:At(function(l,s){Xl(this,l,s)}),getScrollInfo:function(){var l=this.display.scroller;return{left:l.scrollLeft,top:l.scrollTop,height:l.scrollHeight-er(this)-this.display.barHeight,width:l.scrollWidth-er(this)-this.display.barWidth,clientHeight:_u(this),clientWidth:hi(this)}},scrollIntoView:At(function(l,s){l==null?(l={from:this.doc.sel.primary().head,to:null},s==null&&(s=this.options.cursorScrollMargin)):typeof l=="number"?l={from:D(l,0),to:null}:l.from==null&&(l={from:l,to:null}),l.to||(l.to=l.from),l.margin=s||0,l.from.line!=null?Iy(this,l):Ch(this,l.from,l.to,l.margin)}),setSize:At(function(l,s){var f=this,d=function(g){return typeof g=="number"||/^\d+$/.test(String(g))?g+"px":g};l!=null&&(this.display.wrapper.style.width=d(l)),s!=null&&(this.display.wrapper.style.height=d(s)),this.options.lineWrapping&&ch(this);var p=this.display.viewFrom;this.doc.iter(p,this.display.viewTo,function(g){if(g.widgets){for(var y=0;y<g.widgets.length;y++)if(g.widgets[y].noHScroll){Fr(f,p,"widget");break}}++p}),this.curOp.forceUpdate=!0,$e(this,"refresh",this)}),operation:function(l){return nn(this,l)},startOperation:function(){return wi(this)},endOperation:function(){return xi(this)},refresh:At(function(){var l=this.display.cachedTextHeight;Kt(this),this.curOp.forceUpdate=!0,Ql(this),Xl(this,this.doc.scrollLeft,this.doc.scrollTop),qu(this.display),(l==null||Math.abs(l-$i(this.display))>.5||this.options.lineWrapping)&&Fu(this),$e(this,"refresh",this)}),swapDoc:At(function(l){var s=this.doc;return s.cm=null,this.state.selectingText&&this.state.selectingText(),Rh(this,l),Ql(this),this.display.input.reset(),Xl(this,l.scrollLeft,l.scrollTop),this.curOp.forceScroll=!0,gt(this,"swapDoc",this,s),s}),phrase:function(l){var s=this.options.phrases;return s&&Object.prototype.hasOwnProperty.call(s,l)?s[l]:l},getInputField:function(){return this.display.input.getField()},getWrapperElement:function(){return this.display.wrapper},getScrollerElement:function(){return this.display.scroller},getGutterElement:function(){return this.display.gutters}},It(t),t.registerHelper=function(l,s,f){o.hasOwnProperty(l)||(o[l]=t[l]={_global:[]}),o[l][s]=f},t.registerGlobalHelper=function(l,s,f,d){t.registerHelper(l,s,d),o[l]._global.push({pred:f,val:d})}}function cf(t,r,o,l,s){var f=r,d=o,p=de(t,r.line),g=s&&t.direction=="rtl"?-o:o;function y(){var se=r.line+g;return se<t.first||se>=t.first+t.size?!1:(r=new D(se,r.ch,r.sticky),p=de(t,se))}function k(se){var ie;if(l=="codepoint"){var ae=p.text.charCodeAt(r.ch+(o>0?0:-1));if(isNaN(ae))ie=null;else{var me=o>0?ae>=55296&&ae<56320:ae>=56320&&ae<57343;ie=new D(r.line,Math.max(0,Math.min(p.text.length,r.ch+o*(me?2:1))),-o)}}else s?ie=yw(t.cm,p,r,o):ie=rf(p,r,o);if(ie==null)if(!se&&y())r=lf(s,t.cm,p,r.line,g);else return!1;else r=ie;return!0}if(l=="char"||l=="codepoint")k();else if(l=="column")k(!0);else if(l=="word"||l=="group")for(var N=null,I=l=="group",O=t.cm&&t.cm.getHelper(r,"wordChars"),$=!0;!(o<0&&!k(!$));$=!1){var q=p.text.charAt(r.ch)||`
`,J=fr(q,O)?"w":I&&q==`
`?"n":!I||/\s/.test(q)?null:"p";if(I&&!$&&!J&&(J="s"),N&&N!=J){o<0&&(o=1,k(),r.sticky="after");break}if(J&&(N=J),o>0&&!k(!$))break}var re=Fs(t,r,f,d,!0);return Te(f,re)&&(re.hitSide=!0),re}function Dp(t,r,o,l){var s=t.doc,f=r.left,d;if(l=="page"){var p=Math.min(t.display.wrapper.clientHeight,je(t).innerHeight||s(t).documentElement.clientHeight),g=Math.max(p-.5*$i(t.display),3);d=(o>0?r.bottom:r.top)+o*g}else l=="line"&&(d=o>0?r.bottom+3:r.top-3);for(var y;y=zu(t,f,d),!!y.outside;){if(o<0?d<=0:d>=s.height){y.hitSide=!0;break}d+=o*5}return y}var Be=function(t){this.cm=t,this.lastAnchorNode=this.lastAnchorOffset=this.lastFocusNode=this.lastFocusOffset=null,this.polling=new Ne,this.composing=null,this.gracePeriod=!1,this.readDOMTimeout=null};Be.prototype.init=function(t){var r=this,o=this,l=o.cm,s=o.div=t.lineDiv;s.contentEditable=!0,Mp(s,l.options.spellcheck,l.options.autocorrect,l.options.autocapitalize);function f(p){for(var g=p.target;g;g=g.parentNode){if(g==s)return!0;if(/\bCodeMirror-(?:line)?widget\b/.test(g.className))break}return!1}oe(s,"paste",function(p){!f(p)||Je(l,p)||Np(p,l)||x<=11&&setTimeout(mt(l,function(){return r.updateFromDOM()}),20)}),oe(s,"compositionstart",function(p){r.composing={data:p.data,done:!1}}),oe(s,"compositionupdate",function(p){r.composing||(r.composing={data:p.data,done:!1})}),oe(s,"compositionend",function(p){r.composing&&(p.data!=r.composing.data&&r.readFromDOMSoon(),r.composing.done=!0)}),oe(s,"touchstart",function(){return o.forceCompositionEnd()}),oe(s,"input",function(){r.composing||r.readFromDOMSoon()});function d(p){if(!(!f(p)||Je(l,p))){if(l.somethingSelected())$s({lineWise:!1,text:l.getSelections()}),p.type=="cut"&&l.replaceSelection("",null,"cut");else if(l.options.lineWiseCopyCut){var g=bp(l);$s({lineWise:!0,text:g.text}),p.type=="cut"&&l.operation(function(){l.setSelections(g.ranges,0,Ke),l.replaceSelection("",null,"cut")})}else return;if(p.clipboardData){p.clipboardData.clearData();var y=Bn.text.join(`
`);if(p.clipboardData.setData("Text",y),p.clipboardData.getData("Text")==y){p.preventDefault();return}}var k=_p(),N=k.firstChild;l.display.lineSpace.insertBefore(k,l.display.lineSpace.firstChild),N.value=Bn.text.join(`
`);var I=b(s.ownerDocument);ue(N),setTimeout(function(){l.display.lineSpace.removeChild(k),I.focus(),I==s&&o.showPrimarySelection()},50)}}oe(s,"copy",d),oe(s,"cut",d)},Be.prototype.screenReaderLabelChanged=function(t){t?this.div.setAttribute("aria-label",t):this.div.removeAttribute("aria-label")},Be.prototype.prepareSelection=function(){var t=xh(this.cm,!1);return t.focus=b(this.div.ownerDocument)==this.div,t},Be.prototype.showSelection=function(t,r){!t||!this.cm.display.view.length||((t.focus||r)&&this.showPrimarySelection(),this.showMultipleSelections(t))},Be.prototype.getSelection=function(){return this.cm.display.wrapper.ownerDocument.getSelection()},Be.prototype.showPrimarySelection=function(){var t=this.getSelection(),r=this.cm,o=r.doc.sel.primary(),l=o.from(),s=o.to();if(r.display.viewTo==r.display.viewFrom||l.line>=r.display.viewTo||s.line<r.display.viewFrom){t.removeAllRanges();return}var f=Ks(r,t.anchorNode,t.anchorOffset),d=Ks(r,t.focusNode,t.focusOffset);if(!(f&&!f.bad&&d&&!d.bad&&j(dt(f,d),l)==0&&j(Ie(f,d),s)==0)){var p=r.display.view,g=l.line>=r.display.viewFrom&&Pp(r,l)||{node:p[0].measure.map[2],offset:0},y=s.line<r.display.viewTo&&Pp(r,s);if(!y){var k=p[p.length-1].measure,N=k.maps?k.maps[k.maps.length-1]:k.map;y={node:N[N.length-1],offset:N[N.length-2]-N[N.length-3]}}if(!g||!y){t.removeAllRanges();return}var I=t.rangeCount&&t.getRangeAt(0),O;try{O=ee(g.node,g.offset,y.offset,y.node)}catch{}O&&(!u&&r.state.focused?(t.collapse(g.node,g.offset),O.collapsed||(t.removeAllRanges(),t.addRange(O))):(t.removeAllRanges(),t.addRange(O)),I&&t.anchorNode==null?t.addRange(I):u&&this.startGracePeriod()),this.rememberSelection()}},Be.prototype.startGracePeriod=function(){var t=this;clearTimeout(this.gracePeriod),this.gracePeriod=setTimeout(function(){t.gracePeriod=!1,t.selectionChanged()&&t.cm.operation(function(){return t.cm.curOp.selectionChanged=!0})},20)},Be.prototype.showMultipleSelections=function(t){le(this.cm.display.cursorDiv,t.cursors),le(this.cm.display.selectionDiv,t.selection)},Be.prototype.rememberSelection=function(){var t=this.getSelection();this.lastAnchorNode=t.anchorNode,this.lastAnchorOffset=t.anchorOffset,this.lastFocusNode=t.focusNode,this.lastFocusOffset=t.focusOffset},Be.prototype.selectionInEditor=function(){var t=this.getSelection();if(!t.rangeCount)return!1;var r=t.getRangeAt(0).commonAncestorContainer;return C(this.div,r)},Be.prototype.focus=function(){this.cm.options.readOnly!="nocursor"&&((!this.selectionInEditor()||b(this.div.ownerDocument)!=this.div)&&this.showSelection(this.prepareSelection(),!0),this.div.focus())},Be.prototype.blur=function(){this.div.blur()},Be.prototype.getField=function(){return this.div},Be.prototype.supportsTouch=function(){return!0},Be.prototype.receivedFocus=function(){var t=this,r=this;this.selectionInEditor()?setTimeout(function(){return t.pollSelection()},20):nn(this.cm,function(){return r.cm.curOp.selectionChanged=!0});function o(){r.cm.state.focused&&(r.pollSelection(),r.polling.set(r.cm.options.pollInterval,o))}this.polling.set(this.cm.options.pollInterval,o)},Be.prototype.selectionChanged=function(){var t=this.getSelection();return t.anchorNode!=this.lastAnchorNode||t.anchorOffset!=this.lastAnchorOffset||t.focusNode!=this.lastFocusNode||t.focusOffset!=this.lastFocusOffset},Be.prototype.pollSelection=function(){if(!(this.readDOMTimeout!=null||this.gracePeriod||!this.selectionChanged())){var t=this.getSelection(),r=this.cm;if(M&&P&&this.cm.display.gutterSpecs.length&&Fw(t.anchorNode)){this.cm.triggerOnKeyDown({type:"keydown",keyCode:8,preventDefault:Math.abs}),this.blur(),this.focus();return}if(!this.composing){this.rememberSelection();var o=Ks(r,t.anchorNode,t.anchorOffset),l=Ks(r,t.focusNode,t.focusOffset);o&&l&&nn(r,function(){Mt(r.doc,Br(o,l),Ke),(o.bad||l.bad)&&(r.curOp.selectionChanged=!0)})}}},Be.prototype.pollContent=function(){this.readDOMTimeout!=null&&(clearTimeout(this.readDOMTimeout),this.readDOMTimeout=null);var t=this.cm,r=t.display,o=t.doc.sel.primary(),l=o.from(),s=o.to();if(l.ch==0&&l.line>t.firstLine()&&(l=D(l.line-1,de(t.doc,l.line-1).length)),s.ch==de(t.doc,s.line).text.length&&s.line<t.lastLine()&&(s=D(s.line+1,0)),l.line<r.viewFrom||s.line>r.viewTo-1)return!1;var f,d,p;l.line==r.viewFrom||(f=gi(t,l.line))==0?(d=Re(r.view[0].line),p=r.view[0].node):(d=Re(r.view[f].line),p=r.view[f-1].node.nextSibling);var g=gi(t,s.line),y,k;if(g==r.view.length-1?(y=r.viewTo-1,k=r.lineDiv.lastChild):(y=Re(r.view[g+1].line)-1,k=r.view[g+1].node.previousSibling),!p)return!1;for(var N=t.doc.splitLines(Ww(t,p,k,d,y)),I=pr(t.doc,D(d,0),D(y,de(t.doc,y).text.length));N.length>1&&I.length>1;)if(_e(N)==_e(I))N.pop(),I.pop(),y--;else if(N[0]==I[0])N.shift(),I.shift(),d++;else break;for(var O=0,$=0,q=N[0],J=I[0],re=Math.min(q.length,J.length);O<re&&q.charCodeAt(O)==J.charCodeAt(O);)++O;for(var se=_e(N),ie=_e(I),ae=Math.min(se.length-(N.length==1?O:0),ie.length-(I.length==1?O:0));$<ae&&se.charCodeAt(se.length-$-1)==ie.charCodeAt(ie.length-$-1);)++$;if(N.length==1&&I.length==1&&d==l.line)for(;O&&O>l.ch&&se.charCodeAt(se.length-$-1)==ie.charCodeAt(ie.length-$-1);)O--,$++;N[N.length-1]=se.slice(0,se.length-$).replace(/^\u200b+/,""),N[0]=N[0].slice(O).replace(/\u200b+$/,"");var me=D(d,O),ve=D(y,I.length?_e(I).length-$:0);if(N.length>1||N[0]||j(me,ve))return Zi(t.doc,N,me,ve,"+input"),!0},Be.prototype.ensurePolled=function(){this.forceCompositionEnd()},Be.prototype.reset=function(){this.forceCompositionEnd()},Be.prototype.forceCompositionEnd=function(){this.composing&&(clearTimeout(this.readDOMTimeout),this.composing=null,this.updateFromDOM(),this.div.blur(),this.div.focus())},Be.prototype.readFromDOMSoon=function(){var t=this;this.readDOMTimeout==null&&(this.readDOMTimeout=setTimeout(function(){if(t.readDOMTimeout=null,t.composing)if(t.composing.done)t.composing=null;else return;t.updateFromDOM()},80))},Be.prototype.updateFromDOM=function(){var t=this;(this.cm.isReadOnly()||!this.pollContent())&&nn(this.cm,function(){return Kt(t.cm)})},Be.prototype.setUneditable=function(t){t.contentEditable="false"},Be.prototype.onKeyPress=function(t){t.charCode==0||this.composing||(t.preventDefault(),this.cm.isReadOnly()||mt(this.cm,ff)(this.cm,String.fromCharCode(t.charCode==null?t.keyCode:t.charCode),0))},Be.prototype.readOnlyChanged=function(t){this.div.contentEditable=String(t!="nocursor")},Be.prototype.onContextMenu=function(){},Be.prototype.resetPosition=function(){},Be.prototype.needsContentAttribute=!0;function Pp(t,r){var o=Du(t,r.line);if(!o||o.hidden)return null;var l=de(t.doc,r.line),s=oh(o,l,r.line),f=Ln(l,t.doc.direction),d="left";if(f){var p=Tn(f,r.ch);d=p%2?"right":"left"}var g=uh(s.map,r.ch,d);return g.offset=g.collapse=="right"?g.end:g.start,g}function Fw(t){for(var r=t;r;r=r.parentNode)if(/CodeMirror-gutter-wrapper/.test(r.className))return!0;return!1}function rl(t,r){return r&&(t.bad=!0),t}function Ww(t,r,o,l,s){var f="",d=!1,p=t.doc.lineSeparator(),g=!1;function y(O){return function($){return $.id==O}}function k(){d&&(f+=p,g&&(f+=p),d=g=!1)}function N(O){O&&(k(),f+=O)}function I(O){if(O.nodeType==1){var $=O.getAttribute("cm-text");if($){N($);return}var q=O.getAttribute("cm-marker"),J;if(q){var re=t.findMarks(D(l,0),D(s+1,0),y(+q));re.length&&(J=re[0].find(0))&&N(pr(t.doc,J.from,J.to).join(p));return}if(O.getAttribute("contenteditable")=="false")return;var se=/^(pre|div|p|li|table|br)$/i.test(O.nodeName);if(!/^br$/i.test(O.nodeName)&&O.textContent.length==0)return;se&&k();for(var ie=0;ie<O.childNodes.length;ie++)I(O.childNodes[ie]);/^(pre|p)$/i.test(O.nodeName)&&(g=!0),se&&(d=!0)}else O.nodeType==3&&N(O.nodeValue.replace(/\u200b/g,"").replace(/\u00a0/g," "))}for(;I(r),r!=o;)r=r.nextSibling,g=!1;return f}function Ks(t,r,o){var l;if(r==t.display.lineDiv){if(l=t.display.lineDiv.childNodes[o],!l)return rl(t.clipPos(D(t.display.viewTo-1)),!0);r=null,o=0}else for(l=r;;l=l.parentNode){if(!l||l==t.display.lineDiv)return null;if(l.parentNode&&l.parentNode==t.display.lineDiv)break}for(var s=0;s<t.display.view.length;s++){var f=t.display.view[s];if(f.node==l)return Bw(f,r,o)}}function Bw(t,r,o){var l=t.text.firstChild,s=!1;if(!r||!C(l,r))return rl(D(Re(t.line),0),!0);if(r==l&&(s=!0,r=l.childNodes[o],o=0,!r)){var f=t.rest?_e(t.rest):t.line;return rl(D(Re(f),f.text.length),s)}var d=r.nodeType==3?r:null,p=r;for(!d&&r.childNodes.length==1&&r.firstChild.nodeType==3&&(d=r.firstChild,o&&(o=d.nodeValue.length));p.parentNode!=l;)p=p.parentNode;var g=t.measure,y=g.maps;function k(J,re,se){for(var ie=-1;ie<(y?y.length:0);ie++)for(var ae=ie<0?g.map:y[ie],me=0;me<ae.length;me+=3){var ve=ae[me+2];if(ve==J||ve==re){var Me=Re(ie<0?t.line:t.rest[ie]),Ue=ae[me]+se;return(se<0||ve!=J)&&(Ue=ae[me+(se?1:0)]),D(Me,Ue)}}}var N=k(d,p,o);if(N)return rl(N,s);for(var I=p.nextSibling,O=d?d.nodeValue.length-o:0;I;I=I.nextSibling){if(N=k(I,I.firstChild,0),N)return rl(D(N.line,N.ch-O),s);O+=I.textContent.length}for(var $=p.previousSibling,q=o;$;$=$.previousSibling){if(N=k($,$.firstChild,-1),N)return rl(D(N.line,N.ch+q),s);q+=$.textContent.length}}var lt=function(t){this.cm=t,this.prevInput="",this.pollingFast=!1,this.polling=new Ne,this.hasSelection=!1,this.composing=null,this.resetting=!1};lt.prototype.init=function(t){var r=this,o=this,l=this.cm;this.createField(t);var s=this.textarea;t.wrapper.insertBefore(this.wrapper,t.wrapper.firstChild),S&&(s.style.width="0px"),oe(s,"input",function(){m&&x>=9&&r.hasSelection&&(r.hasSelection=null),o.poll()}),oe(s,"paste",function(d){Je(l,d)||Np(d,l)||(l.state.pasteIncoming=+new Date,o.fastPoll())});function f(d){if(!Je(l,d)){if(l.somethingSelected())$s({lineWise:!1,text:l.getSelections()});else if(l.options.lineWiseCopyCut){var p=bp(l);$s({lineWise:!0,text:p.text}),d.type=="cut"?l.setSelections(p.ranges,null,Ke):(o.prevInput="",s.value=p.text.join(`
`),ue(s))}else return;d.type=="cut"&&(l.state.cutIncoming=+new Date)}}oe(s,"cut",f),oe(s,"copy",f),oe(t.scroller,"paste",function(d){if(!(mr(t,d)||Je(l,d))){if(!s.dispatchEvent){l.state.pasteIncoming=+new Date,o.focus();return}var p=new Event("paste");p.clipboardData=d.clipboardData,s.dispatchEvent(p)}}),oe(t.lineSpace,"selectstart",function(d){mr(t,d)||bt(d)}),oe(s,"compositionstart",function(){var d=l.getCursor("from");o.composing&&o.composing.range.clear(),o.composing={start:d,range:l.markText(d,l.getCursor("to"),{className:"CodeMirror-composing"})}}),oe(s,"compositionend",function(){o.composing&&(o.poll(),o.composing.range.clear(),o.composing=null)})},lt.prototype.createField=function(t){this.wrapper=_p(),this.textarea=this.wrapper.firstChild},lt.prototype.screenReaderLabelChanged=function(t){t?this.textarea.setAttribute("aria-label",t):this.textarea.removeAttribute("aria-label")},lt.prototype.prepareSelection=function(){var t=this.cm,r=t.display,o=t.doc,l=xh(t);if(t.options.moveInputWithCursor){var s=Fn(t,o.sel.primary().head,"div"),f=r.wrapper.getBoundingClientRect(),d=r.lineDiv.getBoundingClientRect();l.teTop=Math.max(0,Math.min(r.wrapper.clientHeight-10,s.top+d.top-f.top)),l.teLeft=Math.max(0,Math.min(r.wrapper.clientWidth-10,s.left+d.left-f.left))}return l},lt.prototype.showSelection=function(t){var r=this.cm,o=r.display;le(o.cursorDiv,t.cursors),le(o.selectionDiv,t.selection),t.teTop!=null&&(this.wrapper.style.top=t.teTop+"px",this.wrapper.style.left=t.teLeft+"px")},lt.prototype.reset=function(t){if(!(this.contextMenuPending||this.composing&&t)){var r=this.cm;if(this.resetting=!0,r.somethingSelected()){this.prevInput="";var o=r.getSelection();this.textarea.value=o,r.state.focused&&ue(this.textarea),m&&x>=9&&(this.hasSelection=o)}else t||(this.prevInput=this.textarea.value="",m&&x>=9&&(this.hasSelection=null));this.resetting=!1}},lt.prototype.getField=function(){return this.textarea},lt.prototype.supportsTouch=function(){return!1},lt.prototype.focus=function(){if(this.cm.options.readOnly!="nocursor"&&(!A||b(this.textarea.ownerDocument)!=this.textarea))try{this.textarea.focus()}catch{}},lt.prototype.blur=function(){this.textarea.blur()},lt.prototype.resetPosition=function(){this.wrapper.style.top=this.wrapper.style.left=0},lt.prototype.receivedFocus=function(){this.slowPoll()},lt.prototype.slowPoll=function(){var t=this;this.pollingFast||this.polling.set(this.cm.options.pollInterval,function(){t.poll(),t.cm.state.focused&&t.slowPoll()})},lt.prototype.fastPoll=function(){var t=!1,r=this;r.pollingFast=!0;function o(){var l=r.poll();!l&&!t?(t=!0,r.polling.set(60,o)):(r.pollingFast=!1,r.slowPoll())}r.polling.set(20,o)},lt.prototype.poll=function(){var t=this,r=this.cm,o=this.textarea,l=this.prevInput;if(this.contextMenuPending||this.resetting||!r.state.focused||Or(o)&&!l&&!this.composing||r.isReadOnly()||r.options.disableInput||r.state.keySeq)return!1;var s=o.value;if(s==l&&!r.somethingSelected())return!1;if(m&&x>=9&&this.hasSelection===s||G&&/[\uf700-\uf7ff]/.test(s))return r.display.input.reset(),!1;if(r.doc.sel==r.display.selForContextMenu){var f=s.charCodeAt(0);if(f==8203&&!l&&(l="​"),f==8666)return this.reset(),this.cm.execCommand("undo")}for(var d=0,p=Math.min(l.length,s.length);d<p&&l.charCodeAt(d)==s.charCodeAt(d);)++d;return nn(r,function(){ff(r,s.slice(d),l.length-d,null,t.composing?"*compose":null),s.length>1e3||s.indexOf(`
`)>-1?o.value=t.prevInput="":t.prevInput=s,t.composing&&(t.composing.range.clear(),t.composing.range=r.markText(t.composing.start,r.getCursor("to"),{className:"CodeMirror-composing"}))}),!0},lt.prototype.ensurePolled=function(){this.pollingFast&&this.poll()&&(this.pollingFast=!1)},lt.prototype.onKeyPress=function(){m&&x>=9&&(this.hasSelection=null),this.fastPoll()},lt.prototype.onContextMenu=function(t){var r=this,o=r.cm,l=o.display,s=r.textarea;r.contextMenuPending&&r.contextMenuPending();var f=vi(o,t),d=l.scroller.scrollTop;if(!f||B)return;var p=o.options.resetSelectionOnContextMenu;p&&o.doc.sel.contains(f)==-1&&mt(o,Mt)(o.doc,Br(f),Ke);var g=s.style.cssText,y=r.wrapper.style.cssText,k=r.wrapper.offsetParent.getBoundingClientRect();r.wrapper.style.cssText="position: static",s.style.cssText=`position: absolute; width: 30px; height: 30px;
      top: `+(t.clientY-k.top-5)+"px; left: "+(t.clientX-k.left-5)+`px;
      z-index: 1000; background: `+(m?"rgba(255, 255, 255, .05)":"transparent")+`;
      outline: none; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);`;var N;L&&(N=s.ownerDocument.defaultView.scrollY),l.input.focus(),L&&s.ownerDocument.defaultView.scrollTo(null,N),l.input.reset(),o.somethingSelected()||(s.value=r.prevInput=" "),r.contextMenuPending=O,l.selForContextMenu=o.doc.sel,clearTimeout(l.detectingSelectAll);function I(){if(s.selectionStart!=null){var q=o.somethingSelected(),J="​"+(q?s.value:"");s.value="⇚",s.value=J,r.prevInput=q?"":"​",s.selectionStart=1,s.selectionEnd=J.length,l.selForContextMenu=o.doc.sel}}function O(){if(r.contextMenuPending==O&&(r.contextMenuPending=!1,r.wrapper.style.cssText=y,s.style.cssText=g,m&&x<9&&l.scrollbars.setScrollTop(l.scroller.scrollTop=d),s.selectionStart!=null)){(!m||m&&x<9)&&I();var q=0,J=function(){l.selForContextMenu==o.doc.sel&&s.selectionStart==0&&s.selectionEnd>0&&r.prevInput=="​"?mt(o,Yh)(o):q++<10?l.detectingSelectAll=setTimeout(J,500):(l.selForContextMenu=null,l.input.reset())};l.detectingSelectAll=setTimeout(J,200)}}if(m&&x>=9&&I(),we){cr(t);var $=function(){Et(window,"mouseup",$),setTimeout(O,20)};oe(window,"mouseup",$)}else setTimeout(O,50)},lt.prototype.readOnlyChanged=function(t){t||this.reset(),this.textarea.disabled=t=="nocursor",this.textarea.readOnly=!!t},lt.prototype.setUneditable=function(){},lt.prototype.needsContentAttribute=!1;function Hw(t,r){if(r=r?pe(r):{},r.value=t.value,!r.tabindex&&t.tabIndex&&(r.tabindex=t.tabIndex),!r.placeholder&&t.placeholder&&(r.placeholder=t.placeholder),r.autofocus==null){var o=b(t.ownerDocument);r.autofocus=o==t||t.getAttribute("autofocus")!=null&&o==document.body}function l(){t.value=p.getValue()}var s;if(t.form&&(oe(t.form,"submit",l),!r.leaveSubmitMethodAlone)){var f=t.form;s=f.submit;try{var d=f.submit=function(){l(),f.submit=s,f.submit(),f.submit=d}}catch{}}r.finishInit=function(g){g.save=l,g.getTextArea=function(){return t},g.toTextArea=function(){g.toTextArea=isNaN,l(),t.parentNode.removeChild(g.getWrapperElement()),t.style.display="",t.form&&(Et(t.form,"submit",l),!r.leaveSubmitMethodAlone&&typeof t.form.submit=="function"&&(t.form.submit=s))}},t.style.display="none";var p=qe(function(g){return t.parentNode.insertBefore(g,t.nextSibling)},r);return p}function Uw(t){t.off=Et,t.on=oe,t.wheelEventPixels=Qy,t.Doc=Gt,t.splitLines=Ul,t.countColumn=ce,t.findColumn=kn,t.isWordChar=Pr,t.Pass=We,t.signal=$e,t.Line=Hi,t.changeEnd=Hr,t.scrollbarModel=Nh,t.Pos=D,t.cmpPos=j,t.modes=An,t.mimeModes=zr,t.resolveMode=tn,t.getMode=dr,t.modeExtensions=Ir,t.extendMode=gs,t.copyState=hr,t.startState=jl,t.innerMode=Ar,t.commands=ho,t.keyMap=wr,t.keyName=dp,t.isModifierKey=fp,t.lookupKey=el,t.normalizeKeyMap=mw,t.StringStream=et,t.SharedTextMarker=uo,t.TextMarker=jr,t.LineWidget=ao,t.e_preventDefault=bt,t.e_stopPropagation=di,t.e_stop=cr,t.addClass=U,t.contains=C,t.rmClass=V,t.keyNames=$r}Ow(qe),Rw(qe);var jw="iter insert remove copy getEditor constructor".split(" ");for(var Gs in Gt.prototype)Gt.prototype.hasOwnProperty(Gs)&&be(jw,Gs)<0&&(qe.prototype[Gs]=function(t){return function(){return t.apply(this.doc,arguments)}}(Gt.prototype[Gs]));return It(Gt),qe.inputStyles={textarea:lt,contenteditable:Be},qe.defineMode=function(t){!qe.defaults.mode&&t!="null"&&(qe.defaults.mode=t),vs.apply(this,arguments)},qe.defineMIME=Wi,qe.defineMode("null",function(){return{token:function(t){return t.skipToEnd()}}}),qe.defineMIME("text/plain","null"),qe.defineExtension=function(t,r){qe.prototype[t]=r},qe.defineDocExtension=function(t,r){Gt.prototype[t]=r},qe.fromTextArea=Hw,Uw(qe),qe.version="5.65.9",qe})})(uk);const fk=Pl;(function(e,n){(function(i){i(Pl)})(function(i){i.defineMode("javascript",function(a,u){var c=a.indentUnit,h=u.statementIndent,v=u.jsonld,m=u.json||v,x=u.trackScope!==!1,L=u.typescript,R=u.wordCharacters||/[\w$\xa1-\uffff]/,P=function(){function w(dt){return{type:dt,style:"keyword"}}var E=w("keyword a"),D=w("keyword b"),j=w("keyword c"),Te=w("keyword d"),Pe=w("operator"),Ie={type:"atom",style:"atom"};return{if:w("if"),while:E,with:E,else:D,do:D,try:D,finally:D,return:Te,break:Te,continue:Te,new:w("new"),delete:j,void:j,throw:j,debugger:w("debugger"),var:w("var"),const:w("var"),let:w("var"),function:w("function"),catch:w("catch"),for:w("for"),switch:w("switch"),case:w("case"),default:w("default"),in:Pe,typeof:Pe,instanceof:Pe,true:Ie,false:Ie,null:Ie,undefined:Ie,NaN:Ie,Infinity:Ie,this:w("this"),class:w("class"),super:w("atom"),yield:j,export:w("export"),import:w("import"),extends:j,await:j}}(),W=/[+\-*&%=<>!?|~^@]/,B=/^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)"/;function Y(w){for(var E=!1,D,j=!1;(D=w.next())!=null;){if(!E){if(D=="/"&&!j)return;D=="["?j=!0:j&&D=="]"&&(j=!1)}E=!E&&D=="\\"}}var K,T;function S(w,E,D){return K=w,T=D,E}function M(w,E){var D=w.next();if(D=='"'||D=="'")return E.tokenize=A(D),E.tokenize(w,E);if(D=="."&&w.match(/^\d[\d_]*(?:[eE][+\-]?[\d_]+)?/))return S("number","number");if(D=="."&&w.match(".."))return S("spread","meta");if(/[\[\]{}\(\),;\:\.]/.test(D))return S(D);if(D=="="&&w.eat(">"))return S("=>","operator");if(D=="0"&&w.match(/^(?:x[\dA-Fa-f_]+|o[0-7_]+|b[01_]+)n?/))return S("number","number");if(/\d/.test(D))return w.match(/^[\d_]*(?:n|(?:\.[\d_]*)?(?:[eE][+\-]?[\d_]+)?)?/),S("number","number");if(D=="/")return w.eat("*")?(E.tokenize=G,G(w,E)):w.eat("/")?(w.skipToEnd(),S("comment","comment")):Zn(w,E,1)?(Y(w),w.match(/^\b(([gimyus])(?![gimyus]*\2))+\b/),S("regexp","string-2")):(w.eat("="),S("operator","operator",w.current()));if(D=="`")return E.tokenize=Q,Q(w,E);if(D=="#"&&w.peek()=="!")return w.skipToEnd(),S("meta","meta");if(D=="#"&&w.eatWhile(R))return S("variable","property");if(D=="<"&&w.match("!--")||D=="-"&&w.match("->")&&!/\S/.test(w.string.slice(0,w.start)))return w.skipToEnd(),S("comment","comment");if(W.test(D))return(D!=">"||!E.lexical||E.lexical.type!=">")&&(w.eat("=")?(D=="!"||D=="=")&&w.eat("="):/[<>*+\-|&?]/.test(D)&&(w.eat(D),D==">"&&w.eat(D))),D=="?"&&w.eat(".")?S("."):S("operator","operator",w.current());if(R.test(D)){w.eatWhile(R);var j=w.current();if(E.lastType!="."){if(P.propertyIsEnumerable(j)){var Te=P[j];return S(Te.type,Te.style,j)}if(j=="async"&&w.match(/^(\s|\/\*([^*]|\*(?!\/))*?\*\/)*[\[\(\w]/,!1))return S("async","keyword",j)}return S("variable","variable",j)}}function A(w){return function(E,D){var j=!1,Te;if(v&&E.peek()=="@"&&E.match(B))return D.tokenize=M,S("jsonld-keyword","meta");for(;(Te=E.next())!=null&&!(Te==w&&!j);)j=!j&&Te=="\\";return j||(D.tokenize=M),S("string","string")}}function G(w,E){for(var D=!1,j;j=w.next();){if(j=="/"&&D){E.tokenize=M;break}D=j=="*"}return S("comment","comment")}function Q(w,E){for(var D=!1,j;(j=w.next())!=null;){if(!D&&(j=="`"||j=="$"&&w.eat("{"))){E.tokenize=M;break}D=!D&&j=="\\"}return S("quasi","string-2",w.current())}var z="([{}])";function Z(w,E){E.fatArrowAt&&(E.fatArrowAt=null);var D=w.string.indexOf("=>",w.start);if(!(D<0)){if(L){var j=/:\s*(?:\w+(?:<[^>]*>|\[\])?|\{[^}]*\})\s*$/.exec(w.string.slice(w.start,D));j&&(D=j.index)}for(var Te=0,Pe=!1,Ie=D-1;Ie>=0;--Ie){var dt=w.string.charAt(Ie),pn=z.indexOf(dt);if(pn>=0&&pn<3){if(!Te){++Ie;break}if(--Te==0){dt=="("&&(Pe=!0);break}}else if(pn>=3&&pn<6)++Te;else if(R.test(dt))Pe=!0;else if(/["'\/`]/.test(dt))for(;;--Ie){if(Ie==0)return;var Le=w.string.charAt(Ie-1);if(Le==dt&&w.string.charAt(Ie-2)!="\\"){Ie--;break}}else if(Pe&&!Te){++Ie;break}}Pe&&!Te&&(E.fatArrowAt=Ie)}}var Ce={atom:!0,number:!0,variable:!0,string:!0,regexp:!0,this:!0,import:!0,"jsonld-keyword":!0};function we(w,E,D,j,Te,Pe){this.indented=w,this.column=E,this.type=D,this.prev=Te,this.info=Pe,j!=null&&(this.align=j)}function ze(w,E){if(!x)return!1;for(var D=w.localVars;D;D=D.next)if(D.name==E)return!0;for(var j=w.context;j;j=j.prev)for(var D=j.vars;D;D=D.next)if(D.name==E)return!0}function V(w,E,D,j,Te){var Pe=w.cc;for(F.state=w,F.stream=Te,F.marked=null,F.cc=Pe,F.style=E,w.lexical.hasOwnProperty("align")||(w.lexical.align=!0);;){var Ie=Pe.length?Pe.pop():m?We:be;if(Ie(D,j)){for(;Pe.length&&Pe[Pe.length-1].lex;)Pe.pop()();return F.marked?F.marked:D=="variable"&&ze(w,j)?"variable-2":E}}}var F={state:null,column:null,marked:null,cc:null};function le(){for(var w=arguments.length-1;w>=0;w--)F.cc.push(arguments[w])}function _(){return le.apply(null,arguments),!0}function X(w,E){for(var D=E;D;D=D.next)if(D.name==w)return!0;return!1}function ee(w){var E=F.state;if(F.marked="def",!!x){if(E.context){if(E.lexical.info=="var"&&E.context&&E.context.block){var D=C(w,E.context);if(D!=null){E.context=D;return}}else if(!X(w,E.localVars)){E.localVars=new ne(w,E.localVars);return}}u.globalVars&&!X(w,E.globalVars)&&(E.globalVars=new ne(w,E.globalVars))}}function C(w,E){if(E)if(E.block){var D=C(w,E.prev);return D?D==E.prev?E:new U(D,E.vars,!0):null}else return X(w,E.vars)?E:new U(E.prev,new ne(w,E.vars),!1);else return null}function b(w){return w=="public"||w=="private"||w=="protected"||w=="abstract"||w=="readonly"}function U(w,E,D){this.prev=w,this.vars=E,this.block=D}function ne(w,E){this.name=w,this.next=E}var ue=new ne("this",new ne("arguments",null));function Se(){F.state.context=new U(F.state.context,F.state.localVars,!1),F.state.localVars=ue}function je(){F.state.context=new U(F.state.context,F.state.localVars,!0),F.state.localVars=null}Se.lex=je.lex=!0;function Ye(){F.state.localVars=F.state.context.vars,F.state.context=F.state.context.prev}Ye.lex=!0;function pe(w,E){var D=function(){var j=F.state,Te=j.indented;if(j.lexical.type=="stat")Te=j.lexical.indented;else for(var Pe=j.lexical;Pe&&Pe.type==")"&&Pe.align;Pe=Pe.prev)Te=Pe.indented;j.lexical=new we(Te,F.stream.column(),w,null,j.lexical,E)};return D.lex=!0,D}function ce(){var w=F.state;w.lexical.prev&&(w.lexical.type==")"&&(w.indented=w.lexical.indented),w.lexical=w.lexical.prev)}ce.lex=!0;function Ne(w){function E(D){return D==w?_():w==";"||D=="}"||D==")"||D=="]"?le():_(E)}return E}function be(w,E){return w=="var"?_(pe("vardef",E),cr,Ne(";"),ce):w=="keyword a"?_(pe("form"),Vn,be,ce):w=="keyword b"?_(pe("form"),be,ce):w=="keyword d"?F.stream.match(/^\s*$/,!1)?_():_(pe("stat"),kn,Ne(";"),ce):w=="debugger"?_(Ne(";")):w=="{"?_(pe("}"),je,Cn,ce,Ye):w==";"?_():w=="if"?(F.state.lexical.info=="else"&&F.state.cc[F.state.cc.length-1]==ce&&F.state.cc.pop()(),_(pe("form"),Vn,be,ce,Fi)):w=="function"?_(Yn):w=="for"?_(pe("form"),je,hs,be,Ye,ce):w=="class"||L&&E=="interface"?(F.marked="keyword",_(pe("form",w=="class"?w:E),vs,ce)):w=="variable"?L&&E=="declare"?(F.marked="keyword",_(be)):L&&(E=="module"||E=="enum"||E=="type")&&F.stream.match(/^\s*\w/,!1)?(F.marked="keyword",E=="enum"?_(Bi):E=="type"?_(ps,Ne("operator"),oe,Ne(";")):_(pe("form"),$t,Ne("{"),pe("}"),Cn,ce,ce)):L&&E=="namespace"?(F.marked="keyword",_(pe("form"),We,be,ce)):L&&E=="abstract"?(F.marked="keyword",_(be)):_(pe("stat"),fr):w=="switch"?_(pe("form"),Vn,Ne("{"),pe("}","switch"),je,Cn,ce,ce,Ye):w=="case"?_(We,Ne(":")):w=="default"?_(Ne(":")):w=="catch"?_(pe("form"),Se,Ri,be,ce,Ye):w=="export"?_(pe("stat"),Ir,ce):w=="import"?_(pe("stat"),hr,ce):w=="async"?_(be):E=="@"?_(We,be):le(pe("stat"),We,Ne(";"),ce)}function Ri(w){if(w=="(")return _(An,Ne(")"))}function We(w,E){return Qn(w,E,!1)}function Ke(w,E){return Qn(w,E,!0)}function Vn(w){return w!="("?le():_(pe(")"),kn,Ne(")"),ce)}function Qn(w,E,D){if(F.state.fatArrowAt==F.stream.start){var j=D?ur:ct;if(w=="(")return _(Se,pe(")"),Ee(An,")"),ce,Ne("=>"),j,Ye);if(w=="variable")return le(Se,$t,Ne("=>"),j,Ye)}var Te=D?dn:en;return Ce.hasOwnProperty(w)?_(Te):w=="function"?_(Yn,Te):w=="class"||L&&E=="interface"?(F.marked="keyword",_(pe("form"),zr,ce)):w=="keyword c"||w=="async"?_(D?Ke:We):w=="("?_(pe(")"),kn,Ne(")"),ce,Te):w=="operator"||w=="spread"?_(D?Ke:We):w=="["?_(pe("]"),pr,ce,Te):w=="{"?hn(ke,"}",null,Te):w=="quasi"?le(_e,Te):w=="new"?_(ci(D)):_()}function kn(w){return w.match(/[;\}\)\],]/)?le():le(We)}function en(w,E){return w==","?_(kn):dn(w,E,!1)}function dn(w,E,D){var j=D==!1?en:dn,Te=D==!1?We:Ke;if(w=="=>")return _(Se,D?ur:ct,Ye);if(w=="operator")return/\+\+|--/.test(E)||L&&E=="!"?_(j):L&&E=="<"&&F.stream.match(/^([^<>]|<[^<>]*>)*>\s*\(/,!1)?_(pe(">"),Ee(oe,">"),ce,j):E=="?"?_(We,Ne(":"),Te):_(Te);if(w=="quasi")return le(_e,j);if(w!=";"){if(w=="(")return hn(Ke,")","call",j);if(w==".")return _(xe,j);if(w=="[")return _(pe("]"),kn,Ne("]"),ce,j);if(L&&E=="as")return F.marked="keyword",_(oe,j);if(w=="regexp")return F.state.lastType=F.marked="operator",F.stream.backUp(F.stream.pos-F.stream.start-1),_(Te)}}function _e(w,E){return w!="quasi"?le():E.slice(E.length-2)!="${"?_(_e):_(kn,ar)}function ar(w){if(w=="}")return F.marked="string-2",F.state.tokenize=Q,_(_e)}function ct(w){return Z(F.stream,F.state),le(w=="{"?be:We)}function ur(w){return Z(F.stream,F.state),le(w=="{"?be:Ke)}function ci(w){return function(E){return E=="."?_(w?Pr:Fl):E=="variable"&&L?_(bt,w?dn:en):le(w?Ke:We)}}function Fl(w,E){if(E=="target")return F.marked="keyword",_(en)}function Pr(w,E){if(E=="target")return F.marked="keyword",_(dn)}function fr(w){return w==":"?_(ce,be):le(en,Ne(";"),ce)}function xe(w){if(w=="variable")return F.marked="property",_()}function ke(w,E){if(w=="async")return F.marked="property",_(ke);if(w=="variable"||F.style=="keyword"){if(F.marked="property",E=="get"||E=="set")return _(he);var D;return L&&F.state.fatArrowAt==F.stream.start&&(D=F.stream.match(/^\s*:\s*/,!1))&&(F.state.fatArrowAt=F.stream.pos+D[0].length),_(De)}else{if(w=="number"||w=="string")return F.marked=v?"property":F.style+" property",_(De);if(w=="jsonld-keyword")return _(De);if(L&&b(E))return F.marked="keyword",_(ke);if(w=="[")return _(We,Tn,Ne("]"),De);if(w=="spread")return _(Ke,De);if(E=="*")return F.marked="keyword",_(ke);if(w==":")return le(De)}}function he(w){return w!="variable"?le(De):(F.marked="property",_(Yn))}function De(w){if(w==":")return _(Ke);if(w=="(")return le(Yn)}function Ee(w,E,D){function j(Te,Pe){if(D?D.indexOf(Te)>-1:Te==","){var Ie=F.state.lexical;return Ie.info=="call"&&(Ie.pos=(Ie.pos||0)+1),_(function(dt,pn){return dt==E||pn==E?le():le(w)},j)}return Te==E||Pe==E?_():D&&D.indexOf(";")>-1?le(w):_(Ne(E))}return function(Te,Pe){return Te==E||Pe==E?_():le(w,j)}}function hn(w,E,D){for(var j=3;j<arguments.length;j++)F.cc.push(arguments[j]);return _(pe(E,D),Ee(w,E),ce)}function Cn(w){return w=="}"?_():le(be,Cn)}function Tn(w,E){if(L){if(w==":")return _(oe);if(E=="?")return _(Tn)}}function yu(w,E){if(L&&(w==":"||E=="in"))return _(oe)}function Ln(w){if(L&&w==":")return F.stream.match(/^\s*\w+\s+is\b/,!1)?_(We,cs,oe):_(oe)}function cs(w,E){if(E=="is")return F.marked="keyword",_()}function oe(w,E){if(E=="keyof"||E=="typeof"||E=="infer"||E=="readonly")return F.marked="keyword",_(E=="typeof"?Ke:oe);if(w=="variable"||E=="void")return F.marked="type",_(It);if(E=="|"||E=="&")return _(oe);if(w=="string"||w=="number"||w=="atom")return _(It);if(w=="[")return _(pe("]"),Ee(oe,"]",","),ce,It);if(w=="{")return _(pe("}"),Et,ce,It);if(w=="(")return _(Ee(zt,")"),Wl,It);if(w=="<")return _(Ee(oe,">"),oe);if(w=="quasi")return le(Je,It)}function Wl(w){if(w=="=>")return _(oe)}function Et(w){return w.match(/[\}\)\]]/)?_():w==","||w==";"?_(Et):le($e,Et)}function $e(w,E){if(w=="variable"||F.style=="keyword")return F.marked="property",_($e);if(E=="?"||w=="number"||w=="string")return _($e);if(w==":")return _(oe);if(w=="[")return _(Ne("variable"),yu,Ne("]"),$e);if(w=="(")return le(Xn,$e);if(!w.match(/[;\}\)\],]/))return _()}function Je(w,E){return w!="quasi"?le():E.slice(E.length-2)!="${"?_(Je):_(oe,ds)}function ds(w){if(w=="}")return F.marked="string-2",F.state.tokenize=Q,_(Je)}function zt(w,E){return w=="variable"&&F.stream.match(/^\s*[?:]/,!1)||E=="?"?_(zt):w==":"?_(oe):w=="spread"?_(zt):le(oe)}function It(w,E){if(E=="<")return _(pe(">"),Ee(oe,">"),ce,It);if(E=="|"||w=="."||E=="&")return _(oe);if(w=="[")return _(oe,Ne("]"),It);if(E=="extends"||E=="implements")return F.marked="keyword",_(oe);if(E=="?")return _(oe,Ne(":"),oe)}function bt(w,E){if(E=="<")return _(pe(">"),Ee(oe,">"),ce,It)}function di(){return le(oe,Bl)}function Bl(w,E){if(E=="=")return _(oe)}function cr(w,E){return E=="enum"?(F.marked="keyword",_(Bi)):le($t,Tn,Nn,xu)}function $t(w,E){if(L&&b(E))return F.marked="keyword",_($t);if(w=="variable")return ee(E),_();if(w=="spread")return _($t);if(w=="[")return hn(wu,"]");if(w=="{")return hn(Hl,"}")}function Hl(w,E){return w=="variable"&&!F.stream.match(/^\s*:/,!1)?(ee(E),_(Nn)):(w=="variable"&&(F.marked="property"),w=="spread"?_($t):w=="}"?le():w=="["?_(We,Ne("]"),Ne(":"),Hl):_(Ne(":"),$t,Nn))}function wu(){return le($t,Nn)}function Nn(w,E){if(E=="=")return _(Ke)}function xu(w){if(w==",")return _(cr)}function Fi(w,E){if(w=="keyword b"&&E=="else")return _(pe("form","else"),be,ce)}function hs(w,E){if(E=="await")return _(hs);if(w=="(")return _(pe(")"),Ul,ce)}function Ul(w){return w=="var"?_(cr,Or):w=="variable"?_(Or):le(Or)}function Or(w,E){return w==")"?_():w==";"?_(Or):E=="in"||E=="of"?(F.marked="keyword",_(We,Or)):le(We,Or)}function Yn(w,E){if(E=="*")return F.marked="keyword",_(Yn);if(w=="variable")return ee(E),_(Yn);if(w=="(")return _(Se,pe(")"),Ee(An,")"),ce,Ln,be,Ye);if(L&&E=="<")return _(pe(">"),Ee(di,">"),ce,Yn)}function Xn(w,E){if(E=="*")return F.marked="keyword",_(Xn);if(w=="variable")return ee(E),_(Xn);if(w=="(")return _(Se,pe(")"),Ee(An,")"),ce,Ln,Ye);if(L&&E=="<")return _(pe(">"),Ee(di,">"),ce,Xn)}function ps(w,E){if(w=="keyword"||w=="variable")return F.marked="type",_(ps);if(E=="<")return _(pe(">"),Ee(di,">"),ce)}function An(w,E){return E=="@"&&_(We,An),w=="spread"?_(An):L&&b(E)?(F.marked="keyword",_(An)):L&&w=="this"?_(Tn,Nn):le($t,Tn,Nn)}function zr(w,E){return w=="variable"?vs(w,E):Wi(w,E)}function vs(w,E){if(w=="variable")return ee(E),_(Wi)}function Wi(w,E){if(E=="<")return _(pe(">"),Ee(di,">"),ce,Wi);if(E=="extends"||E=="implements"||L&&w==",")return E=="implements"&&(F.marked="keyword"),_(L?oe:We,Wi);if(w=="{")return _(pe("}"),tn,ce)}function tn(w,E){if(w=="async"||w=="variable"&&(E=="static"||E=="get"||E=="set"||L&&b(E))&&F.stream.match(/^\s+[\w$\xa1-\uffff]/,!1))return F.marked="keyword",_(tn);if(w=="variable"||F.style=="keyword")return F.marked="property",_(dr,tn);if(w=="number"||w=="string")return _(dr,tn);if(w=="[")return _(We,Tn,Ne("]"),dr,tn);if(E=="*")return F.marked="keyword",_(tn);if(L&&w=="(")return le(Xn,tn);if(w==";"||w==",")return _(tn);if(w=="}")return _();if(E=="@")return _(We,tn)}function dr(w,E){if(E=="!"||E=="?")return _(dr);if(w==":")return _(oe,Nn);if(E=="=")return _(Ke);var D=F.state.lexical.prev,j=D&&D.info=="interface";return le(j?Xn:Yn)}function Ir(w,E){return E=="*"?(F.marked="keyword",_(de,Ne(";"))):E=="default"?(F.marked="keyword",_(We,Ne(";"))):w=="{"?_(Ee(gs,"}"),de,Ne(";")):le(be)}function gs(w,E){if(E=="as")return F.marked="keyword",_(Ne("variable"));if(w=="variable")return le(Ke,gs)}function hr(w){return w=="string"?_():w=="("?le(We):w=="."?le(en):le(Ar,jl,de)}function Ar(w,E){return w=="{"?hn(Ar,"}"):(w=="variable"&&ee(E),E=="*"&&(F.marked="keyword"),_(et))}function jl(w){if(w==",")return _(Ar,jl)}function et(w,E){if(E=="as")return F.marked="keyword",_(Ar)}function de(w,E){if(E=="from")return F.marked="keyword",_(We)}function pr(w){return w=="]"?_():le(Ee(Ke,"]"))}function Bi(){return le(pe("form"),$t,Ne("{"),pe("}"),Ee(En,"}"),ce,ce)}function En(){return le($t,Nn)}function Re(w,E){return w.lastType=="operator"||w.lastType==","||W.test(E.charAt(0))||/[,.]/.test(E.charAt(0))}function Zn(w,E,D){return E.tokenize==M&&/^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\[{}\(,;:]|=>)$/.test(E.lastType)||E.lastType=="quasi"&&/\{\s*$/.test(w.string.slice(0,w.pos-(D||0)))}return{startState:function(w){var E={tokenize:M,lastType:"sof",cc:[],lexical:new we((w||0)-c,0,"block",!1),localVars:u.localVars,context:u.localVars&&new U(null,null,!1),indented:w||0};return u.globalVars&&typeof u.globalVars=="object"&&(E.globalVars=u.globalVars),E},token:function(w,E){if(w.sol()&&(E.lexical.hasOwnProperty("align")||(E.lexical.align=!1),E.indented=w.indentation(),Z(w,E)),E.tokenize!=G&&w.eatSpace())return null;var D=E.tokenize(w,E);return K=="comment"?D:(E.lastType=K=="operator"&&(T=="++"||T=="--")?"incdec":K,V(E,D,K,T,w))},indent:function(w,E){if(w.tokenize==G||w.tokenize==Q)return i.Pass;if(w.tokenize!=M)return 0;var D=E&&E.charAt(0),j=w.lexical,Te;if(!/^\s*else\b/.test(E))for(var Pe=w.cc.length-1;Pe>=0;--Pe){var Ie=w.cc[Pe];if(Ie==ce)j=j.prev;else if(Ie!=Fi&&Ie!=Ye)break}for(;(j.type=="stat"||j.type=="form")&&(D=="}"||(Te=w.cc[w.cc.length-1])&&(Te==en||Te==dn)&&!/^[,\.=+\-*:?[\(]/.test(E));)j=j.prev;h&&j.type==")"&&j.prev.type=="stat"&&(j=j.prev);var dt=j.type,pn=D==dt;return dt=="vardef"?j.indented+(w.lastType=="operator"||w.lastType==","?j.info.length+1:0):dt=="form"&&D=="{"?j.indented:dt=="form"?j.indented+c:dt=="stat"?j.indented+(Re(w,E)?h||c:0):j.info=="switch"&&!pn&&u.doubleIndentSwitch!=!1?j.indented+(/^(?:case|default)\b/.test(E)?c:2*c):j.align?j.column+(pn?0:1):j.indented+(pn?0:c)},electricInput:/^\s*(?:case .*?:|default:|\{|\})$/,blockCommentStart:m?null:"/*",blockCommentEnd:m?null:"*/",blockCommentContinue:m?null:" * ",lineComment:m?null:"//",fold:"brace",closeBrackets:"()[]{}''\"\"``",helperType:m?"json":"javascript",jsonldMode:v,jsonMode:m,expressionAllowed:Zn,skipExpression:function(w){V(w,"atom","atom","true",new i.StringStream("",2,null))}}}),i.registerHelper("wordChars","javascript",/[\w$]/),i.defineMIME("text/javascript","javascript"),i.defineMIME("text/ecmascript","javascript"),i.defineMIME("application/javascript","javascript"),i.defineMIME("application/x-javascript","javascript"),i.defineMIME("application/ecmascript","javascript"),i.defineMIME("application/json",{name:"javascript",json:!0}),i.defineMIME("application/x-json",{name:"javascript",json:!0}),i.defineMIME("application/manifest+json",{name:"javascript",json:!0}),i.defineMIME("application/ld+json",{name:"javascript",jsonld:!0}),i.defineMIME("text/typescript",{name:"javascript",typescript:!0}),i.defineMIME("application/typescript",{name:"javascript",typescript:!0})})})();(function(e,n){(function(i){i(Pl)})(function(i){function a(x){return new RegExp("^(("+x.join(")|(")+"))\\b")}var u=a(["and","or","not","is"]),c=["as","assert","break","class","continue","def","del","elif","else","except","finally","for","from","global","if","import","lambda","pass","raise","return","try","while","with","yield","in"],h=["abs","all","any","bin","bool","bytearray","callable","chr","classmethod","compile","complex","delattr","dict","dir","divmod","enumerate","eval","filter","float","format","frozenset","getattr","globals","hasattr","hash","help","hex","id","input","int","isinstance","issubclass","iter","len","list","locals","map","max","memoryview","min","next","object","oct","open","ord","pow","property","range","repr","reversed","round","set","setattr","slice","sorted","staticmethod","str","sum","super","tuple","type","vars","zip","__import__","NotImplemented","Ellipsis","__debug__"];i.registerHelper("hintWords","python",c.concat(h).concat(["exec","print"]));function v(x){return x.scopes[x.scopes.length-1]}i.defineMode("python",function(x,L){for(var R="error",P=L.delimiters||L.singleDelimiters||/^[\(\)\[\]\{\}@,:`=;\.\\]/,W=[L.singleOperators,L.doubleOperators,L.doubleDelimiters,L.tripleDelimiters,L.operators||/^([-+*/%\/&|^]=?|[<>=]+|\/\/=?|\*\*=?|!=|[~!@]|\.\.\.)/],B=0;B<W.length;B++)W[B]||W.splice(B--,1);var Y=L.hangingIndent||x.indentUnit,K=c,T=h;L.extra_keywords!=null&&(K=K.concat(L.extra_keywords)),L.extra_builtins!=null&&(T=T.concat(L.extra_builtins));var S=!(L.version&&Number(L.version)<3);if(S){var M=L.identifiers||/^[_A-Za-z\u00A1-\uFFFF][_A-Za-z0-9\u00A1-\uFFFF]*/;K=K.concat(["nonlocal","False","True","None","async","await"]),T=T.concat(["ascii","bytes","exec","print"]);var A=new RegExp(`^(([rbuf]|(br)|(rb)|(fr)|(rf))?('{3}|"{3}|['"]))`,"i")}else{var M=L.identifiers||/^[_A-Za-z][_A-Za-z0-9]*/;K=K.concat(["exec","print"]),T=T.concat(["apply","basestring","buffer","cmp","coerce","execfile","file","intern","long","raw_input","reduce","reload","unichr","unicode","xrange","False","True","None"]);var A=new RegExp(`^(([rubf]|(ur)|(br))?('{3}|"{3}|['"]))`,"i")}var G=a(K),Q=a(T);function z(X,ee){var C=X.sol()&&ee.lastToken!="\\";if(C&&(ee.indent=X.indentation()),C&&v(ee).type=="py"){var b=v(ee).offset;if(X.eatSpace()){var U=X.indentation();return U>b?ze(ee):U<b&&F(X,ee)&&X.peek()!="#"&&(ee.errorToken=!0),null}else{var ne=Z(X,ee);return b>0&&F(X,ee)&&(ne+=" "+R),ne}}return Z(X,ee)}function Z(X,ee,C){if(X.eatSpace())return null;if(!C&&X.match(/^#.*/))return"comment";if(X.match(/^[0-9\.]/,!1)){var b=!1;if(X.match(/^[\d_]*\.\d+(e[\+\-]?\d+)?/i)&&(b=!0),X.match(/^[\d_]+\.\d*/)&&(b=!0),X.match(/^\.\d+/)&&(b=!0),b)return X.eat(/J/i),"number";var U=!1;if(X.match(/^0x[0-9a-f_]+/i)&&(U=!0),X.match(/^0b[01_]+/i)&&(U=!0),X.match(/^0o[0-7_]+/i)&&(U=!0),X.match(/^[1-9][\d_]*(e[\+\-]?[\d_]+)?/)&&(X.eat(/J/i),U=!0),X.match(/^0(?![\dx])/i)&&(U=!0),U)return X.eat(/L/i),"number"}if(X.match(A)){var ne=X.current().toLowerCase().indexOf("f")!==-1;return ne?(ee.tokenize=Ce(X.current(),ee.tokenize),ee.tokenize(X,ee)):(ee.tokenize=we(X.current(),ee.tokenize),ee.tokenize(X,ee))}for(var ue=0;ue<W.length;ue++)if(X.match(W[ue]))return"operator";return X.match(P)?"punctuation":ee.lastToken=="."&&X.match(M)?"property":X.match(G)||X.match(u)?"keyword":X.match(Q)?"builtin":X.match(/^(self|cls)\b/)?"variable-2":X.match(M)?ee.lastToken=="def"||ee.lastToken=="class"?"def":"variable":(X.next(),C?null:R)}function Ce(X,ee){for(;"rubf".indexOf(X.charAt(0).toLowerCase())>=0;)X=X.substr(1);var C=X.length==1,b="string";function U(ue){return function(Se,je){var Ye=Z(Se,je,!0);return Ye=="punctuation"&&(Se.current()=="{"?je.tokenize=U(ue+1):Se.current()=="}"&&(ue>1?je.tokenize=U(ue-1):je.tokenize=ne)),Ye}}function ne(ue,Se){for(;!ue.eol();)if(ue.eatWhile(/[^'"\{\}\\]/),ue.eat("\\")){if(ue.next(),C&&ue.eol())return b}else{if(ue.match(X))return Se.tokenize=ee,b;if(ue.match("{{"))return b;if(ue.match("{",!1))return Se.tokenize=U(0),ue.current()?b:Se.tokenize(ue,Se);if(ue.match("}}"))return b;if(ue.match("}"))return R;ue.eat(/['"]/)}if(C){if(L.singleLineStringErrors)return R;Se.tokenize=ee}return b}return ne.isString=!0,ne}function we(X,ee){for(;"rubf".indexOf(X.charAt(0).toLowerCase())>=0;)X=X.substr(1);var C=X.length==1,b="string";function U(ne,ue){for(;!ne.eol();)if(ne.eatWhile(/[^'"\\]/),ne.eat("\\")){if(ne.next(),C&&ne.eol())return b}else{if(ne.match(X))return ue.tokenize=ee,b;ne.eat(/['"]/)}if(C){if(L.singleLineStringErrors)return R;ue.tokenize=ee}return b}return U.isString=!0,U}function ze(X){for(;v(X).type!="py";)X.scopes.pop();X.scopes.push({offset:v(X).offset+x.indentUnit,type:"py",align:null})}function V(X,ee,C){var b=X.match(/^[\s\[\{\(]*(?:#|$)/,!1)?null:X.column()+1;ee.scopes.push({offset:ee.indent+Y,type:C,align:b})}function F(X,ee){for(var C=X.indentation();ee.scopes.length>1&&v(ee).offset>C;){if(v(ee).type!="py")return!0;ee.scopes.pop()}return v(ee).offset!=C}function le(X,ee){X.sol()&&(ee.beginningOfLine=!0,ee.dedent=!1);var C=ee.tokenize(X,ee),b=X.current();if(ee.beginningOfLine&&b=="@")return X.match(M,!1)?"meta":S?"operator":R;if(/\S/.test(b)&&(ee.beginningOfLine=!1),(C=="variable"||C=="builtin")&&ee.lastToken=="meta"&&(C="meta"),(b=="pass"||b=="return")&&(ee.dedent=!0),b=="lambda"&&(ee.lambda=!0),b==":"&&!ee.lambda&&v(ee).type=="py"&&X.match(/^\s*(?:#|$)/,!1)&&ze(ee),b.length==1&&!/string|comment/.test(C)){var U="[({".indexOf(b);if(U!=-1&&V(X,ee,"])}".slice(U,U+1)),U="])}".indexOf(b),U!=-1)if(v(ee).type==b)ee.indent=ee.scopes.pop().offset-Y;else return R}return ee.dedent&&X.eol()&&v(ee).type=="py"&&ee.scopes.length>1&&ee.scopes.pop(),C}var _={startState:function(X){return{tokenize:z,scopes:[{offset:X||0,type:"py",align:null}],indent:X||0,lastToken:null,lambda:!1,dedent:0}},token:function(X,ee){var C=ee.errorToken;C&&(ee.errorToken=!1);var b=le(X,ee);return b&&b!="comment"&&(ee.lastToken=b=="keyword"||b=="punctuation"?X.current():b),b=="punctuation"&&(b=null),X.eol()&&ee.lambda&&(ee.lambda=!1),C?b+" "+R:b},indent:function(X,ee){if(X.tokenize!=z)return X.tokenize.isString?i.Pass:0;var C=v(X),b=C.type==ee.charAt(0)||C.type=="py"&&!X.dedent&&/^(else:|elif |except |finally:)/.test(ee);return C.align!=null?C.align-(b?1:0):C.offset-(b?Y:0)},electricInput:/^\s*([\}\]\)]|else:|elif |except |finally:)$/,closeBrackets:{triples:`'"`},lineComment:"#",fold:"indent"};return _}),i.defineMIME("text/x-python","python");var m=function(x){return x.split(" ")};i.defineMIME("text/x-cython",{name:"python",extra_keywords:m("by cdef cimport cpdef ctypedef enum except extern gil include nogil property public readonly struct union DEF IF ELIF ELSE")})})})();(function(e,n){(function(i){i(Pl)})(function(i){function a(C,b,U,ne,ue,Se){this.indented=C,this.column=b,this.type=U,this.info=ne,this.align=ue,this.prev=Se}function u(C,b,U,ne){var ue=C.indented;return C.context&&C.context.type=="statement"&&U!="statement"&&(ue=C.context.indented),C.context=new a(ue,b,U,ne,null,C.context)}function c(C){var b=C.context.type;return(b==")"||b=="]"||b=="}")&&(C.indented=C.context.indented),C.context=C.context.prev}function h(C,b,U){if(b.prevToken=="variable"||b.prevToken=="type"||/\S(?:[^- ]>|[*\]])\s*$|\*$/.test(C.string.slice(0,U))||b.typeAtEndOfLine&&C.column()==C.indentation())return!0}function v(C){for(;;){if(!C||C.type=="top")return!0;if(C.type=="}"&&C.prev.info!="namespace")return!1;C=C.prev}}i.defineMode("clike",function(C,b){var U=C.indentUnit,ne=b.statementIndentUnit||U,ue=b.dontAlignCalls,Se=b.keywords||{},je=b.types||{},Ye=b.builtin||{},pe=b.blockKeywords||{},ce=b.defKeywords||{},Ne=b.atoms||{},be=b.hooks||{},Ri=b.multiLineStrings,We=b.indentStatements!==!1,Ke=b.indentSwitch!==!1,Vn=b.namespaceSeparator,Qn=b.isPunctuationChar||/[\[\]{}\(\),;\:\.]/,kn=b.numberStart||/[\d\.]/,en=b.number||/^(?:0x[a-f\d]+|0b[01]+|(?:\d+\.?\d*|\.\d+)(?:e[-+]?\d+)?)(u|ll?|l|f)?/i,dn=b.isOperatorChar||/[+\-*&%=<>!?|\/]/,_e=b.isIdentifierChar||/[\w\$_\xa1-\uffff]/,ar=b.isReservedIdentifier||!1,ct,ur;function ci(xe,ke){var he=xe.next();if(be[he]){var De=be[he](xe,ke);if(De!==!1)return De}if(he=='"'||he=="'")return ke.tokenize=Fl(he),ke.tokenize(xe,ke);if(kn.test(he)){if(xe.backUp(1),xe.match(en))return"number";xe.next()}if(Qn.test(he))return ct=he,null;if(he=="/"){if(xe.eat("*"))return ke.tokenize=Pr,Pr(xe,ke);if(xe.eat("/"))return xe.skipToEnd(),"comment"}if(dn.test(he)){for(;!xe.match(/^\/[\/*]/,!1)&&xe.eat(dn););return"operator"}if(xe.eatWhile(_e),Vn)for(;xe.match(Vn);)xe.eatWhile(_e);var Ee=xe.current();return x(Se,Ee)?(x(pe,Ee)&&(ct="newstatement"),x(ce,Ee)&&(ur=!0),"keyword"):x(je,Ee)?"type":x(Ye,Ee)||ar&&ar(Ee)?(x(pe,Ee)&&(ct="newstatement"),"builtin"):x(Ne,Ee)?"atom":"variable"}function Fl(xe){return function(ke,he){for(var De=!1,Ee,hn=!1;(Ee=ke.next())!=null;){if(Ee==xe&&!De){hn=!0;break}De=!De&&Ee=="\\"}return(hn||!(De||Ri))&&(he.tokenize=null),"string"}}function Pr(xe,ke){for(var he=!1,De;De=xe.next();){if(De=="/"&&he){ke.tokenize=null;break}he=De=="*"}return"comment"}function fr(xe,ke){b.typeFirstDefinitions&&xe.eol()&&v(ke.context)&&(ke.typeAtEndOfLine=h(xe,ke,xe.pos))}return{startState:function(xe){return{tokenize:null,context:new a((xe||0)-U,0,"top",null,!1),indented:0,startOfLine:!0,prevToken:null}},token:function(xe,ke){var he=ke.context;if(xe.sol()&&(he.align==null&&(he.align=!1),ke.indented=xe.indentation(),ke.startOfLine=!0),xe.eatSpace())return fr(xe,ke),null;ct=ur=null;var De=(ke.tokenize||ci)(xe,ke);if(De=="comment"||De=="meta")return De;if(he.align==null&&(he.align=!0),ct==";"||ct==":"||ct==","&&xe.match(/^\s*(?:\/\/.*)?$/,!1))for(;ke.context.type=="statement";)c(ke);else if(ct=="{")u(ke,xe.column(),"}");else if(ct=="[")u(ke,xe.column(),"]");else if(ct=="(")u(ke,xe.column(),")");else if(ct=="}"){for(;he.type=="statement";)he=c(ke);for(he.type=="}"&&(he=c(ke));he.type=="statement";)he=c(ke)}else ct==he.type?c(ke):We&&((he.type=="}"||he.type=="top")&&ct!=";"||he.type=="statement"&&ct=="newstatement")&&u(ke,xe.column(),"statement",xe.current());if(De=="variable"&&(ke.prevToken=="def"||b.typeFirstDefinitions&&h(xe,ke,xe.start)&&v(ke.context)&&xe.match(/^\s*\(/,!1))&&(De="def"),be.token){var Ee=be.token(xe,ke,De);Ee!==void 0&&(De=Ee)}return De=="def"&&b.styleDefs===!1&&(De="variable"),ke.startOfLine=!1,ke.prevToken=ur?"def":De||ct,fr(xe,ke),De},indent:function(xe,ke){if(xe.tokenize!=ci&&xe.tokenize!=null||xe.typeAtEndOfLine)return i.Pass;var he=xe.context,De=ke&&ke.charAt(0),Ee=De==he.type;if(he.type=="statement"&&De=="}"&&(he=he.prev),b.dontIndentStatements)for(;he.type=="statement"&&b.dontIndentStatements.test(he.info);)he=he.prev;if(be.indent){var hn=be.indent(xe,he,ke,U);if(typeof hn=="number")return hn}var Cn=he.prev&&he.prev.info=="switch";if(b.allmanIndentation&&/[{(]/.test(De)){for(;he.type!="top"&&he.type!="}";)he=he.prev;return he.indented}return he.type=="statement"?he.indented+(De=="{"?0:ne):he.align&&(!ue||he.type!=")")?he.column+(Ee?0:1):he.type==")"&&!Ee?he.indented+ne:he.indented+(Ee?0:U)+(!Ee&&Cn&&!/^(?:case|default)\b/.test(ke)?U:0)},electricInput:Ke?/^\s*(?:case .*?:|default:|\{\}?|\})$/:/^\s*[{}]$/,blockCommentStart:"/*",blockCommentEnd:"*/",blockCommentContinue:" * ",lineComment:"//",fold:"brace"}});function m(C){for(var b={},U=C.split(" "),ne=0;ne<U.length;++ne)b[U[ne]]=!0;return b}function x(C,b){return typeof C=="function"?C(b):C.propertyIsEnumerable(b)}var L="auto if break case register continue return default do sizeof static else struct switch extern typedef union for goto while enum const volatile inline restrict asm fortran",R="alignas alignof and and_eq audit axiom bitand bitor catch class compl concept constexpr const_cast decltype delete dynamic_cast explicit export final friend import module mutable namespace new noexcept not not_eq operator or or_eq override private protected public reinterpret_cast requires static_assert static_cast template this thread_local throw try typeid typename using virtual xor xor_eq",P="bycopy byref in inout oneway out self super atomic nonatomic retain copy readwrite readonly strong weak assign typeof nullable nonnull null_resettable _cmd @interface @implementation @end @protocol @encode @property @synthesize @dynamic @class @public @package @private @protected @required @optional @try @catch @finally @import @selector @encode @defs @synchronized @autoreleasepool @compatibility_alias @available",W="FOUNDATION_EXPORT FOUNDATION_EXTERN NS_INLINE NS_FORMAT_FUNCTION  NS_RETURNS_RETAINEDNS_ERROR_ENUM NS_RETURNS_NOT_RETAINED NS_RETURNS_INNER_POINTER NS_DESIGNATED_INITIALIZER NS_ENUM NS_OPTIONS NS_REQUIRES_NIL_TERMINATION NS_ASSUME_NONNULL_BEGIN NS_ASSUME_NONNULL_END NS_SWIFT_NAME NS_REFINED_FOR_SWIFT",B=m("int long char short double float unsigned signed void bool"),Y=m("SEL instancetype id Class Protocol BOOL");function K(C){return x(B,C)||/.+_t$/.test(C)}function T(C){return K(C)||x(Y,C)}var S="case do else for if switch while struct enum union",M="struct enum union";function A(C,b){if(!b.startOfLine)return!1;for(var U,ne=null;U=C.peek();){if(U=="\\"&&C.match(/^.$/)){ne=A;break}else if(U=="/"&&C.match(/^\/[\/\*]/,!1))break;C.next()}return b.tokenize=ne,"meta"}function G(C,b){return b.prevToken=="type"?"type":!1}function Q(C){return!C||C.length<2||C[0]!="_"?!1:C[1]=="_"||C[1]!==C[1].toLowerCase()}function z(C){return C.eatWhile(/[\w\.']/),"number"}function Z(C,b){if(C.backUp(1),C.match(/^(?:R|u8R|uR|UR|LR)/)){var U=C.match(/^"([^\s\\()]{0,16})\(/);return U?(b.cpp11RawStringDelim=U[1],b.tokenize=ze,ze(C,b)):!1}return C.match(/^(?:u8|u|U|L)/)?C.match(/^["']/,!1)?"string":!1:(C.next(),!1)}function Ce(C){var b=/(\w+)::~?(\w+)$/.exec(C);return b&&b[1]==b[2]}function we(C,b){for(var U;(U=C.next())!=null;)if(U=='"'&&!C.eat('"')){b.tokenize=null;break}return"string"}function ze(C,b){var U=b.cpp11RawStringDelim.replace(/[^\w\s]/g,"\\$&"),ne=C.match(new RegExp(".*?\\)"+U+'"'));return ne?b.tokenize=null:C.skipToEnd(),"string"}function V(C,b){typeof C=="string"&&(C=[C]);var U=[];function ne(Se){if(Se)for(var je in Se)Se.hasOwnProperty(je)&&U.push(je)}ne(b.keywords),ne(b.types),ne(b.builtin),ne(b.atoms),U.length&&(b.helperType=C[0],i.registerHelper("hintWords",C[0],U));for(var ue=0;ue<C.length;++ue)i.defineMIME(C[ue],b)}V(["text/x-csrc","text/x-c","text/x-chdr"],{name:"clike",keywords:m(L),types:K,blockKeywords:m(S),defKeywords:m(M),typeFirstDefinitions:!0,atoms:m("NULL true false"),isReservedIdentifier:Q,hooks:{"#":A,"*":G},modeProps:{fold:["brace","include"]}}),V(["text/x-c++src","text/x-c++hdr"],{name:"clike",keywords:m(L+" "+R),types:K,blockKeywords:m(S+" class try catch"),defKeywords:m(M+" class namespace"),typeFirstDefinitions:!0,atoms:m("true false NULL nullptr"),dontIndentStatements:/^template$/,isIdentifierChar:/[\w\$_~\xa1-\uffff]/,isReservedIdentifier:Q,hooks:{"#":A,"*":G,u:Z,U:Z,L:Z,R:Z,0:z,1:z,2:z,3:z,4:z,5:z,6:z,7:z,8:z,9:z,token:function(C,b,U){if(U=="variable"&&C.peek()=="("&&(b.prevToken==";"||b.prevToken==null||b.prevToken=="}")&&Ce(C.current()))return"def"}},namespaceSeparator:"::",modeProps:{fold:["brace","include"]}}),V("text/x-java",{name:"clike",keywords:m("abstract assert break case catch class const continue default do else enum extends final finally for goto if implements import instanceof interface native new package private protected public return static strictfp super switch synchronized this throw throws transient try volatile while @interface"),types:m("var byte short int long float double boolean char void Boolean Byte Character Double Float Integer Long Number Object Short String StringBuffer StringBuilder Void"),blockKeywords:m("catch class do else finally for if switch try while"),defKeywords:m("class interface enum @interface"),typeFirstDefinitions:!0,atoms:m("true false null"),number:/^(?:0x[a-f\d_]+|0b[01_]+|(?:[\d_]+\.?\d*|\.\d+)(?:e[-+]?[\d_]+)?)(u|ll?|l|f)?/i,hooks:{"@":function(C){return C.match("interface",!1)?!1:(C.eatWhile(/[\w\$_]/),"meta")},'"':function(C,b){return C.match(/""$/)?(b.tokenize=F,b.tokenize(C,b)):!1}},modeProps:{fold:["brace","import"]}}),V("text/x-csharp",{name:"clike",keywords:m("abstract as async await base break case catch checked class const continue default delegate do else enum event explicit extern finally fixed for foreach goto if implicit in interface internal is lock namespace new operator out override params private protected public readonly ref return sealed sizeof stackalloc static struct switch this throw try typeof unchecked unsafe using virtual void volatile while add alias ascending descending dynamic from get global group into join let orderby partial remove select set value var yield"),types:m("Action Boolean Byte Char DateTime DateTimeOffset Decimal Double Func Guid Int16 Int32 Int64 Object SByte Single String Task TimeSpan UInt16 UInt32 UInt64 bool byte char decimal double short int long object sbyte float string ushort uint ulong"),blockKeywords:m("catch class do else finally for foreach if struct switch try while"),defKeywords:m("class interface namespace struct var"),typeFirstDefinitions:!0,atoms:m("true false null"),hooks:{"@":function(C,b){return C.eat('"')?(b.tokenize=we,we(C,b)):(C.eatWhile(/[\w\$_]/),"meta")}}});function F(C,b){for(var U=!1;!C.eol();){if(!U&&C.match('"""')){b.tokenize=null;break}U=C.next()=="\\"&&!U}return"string"}function le(C){return function(b,U){for(var ne;ne=b.next();)if(ne=="*"&&b.eat("/"))if(C==1){U.tokenize=null;break}else return U.tokenize=le(C-1),U.tokenize(b,U);else if(ne=="/"&&b.eat("*"))return U.tokenize=le(C+1),U.tokenize(b,U);return"comment"}}V("text/x-scala",{name:"clike",keywords:m("abstract case catch class def do else extends final finally for forSome if implicit import lazy match new null object override package private protected return sealed super this throw trait try type val var while with yield _ assert assume require print println printf readLine readBoolean readByte readShort readChar readInt readLong readFloat readDouble"),types:m("AnyVal App Application Array BufferedIterator BigDecimal BigInt Char Console Either Enumeration Equiv Error Exception Fractional Function IndexedSeq Int Integral Iterable Iterator List Map Numeric Nil NotNull Option Ordered Ordering PartialFunction PartialOrdering Product Proxy Range Responder Seq Serializable Set Specializable Stream StringBuilder StringContext Symbol Throwable Traversable TraversableOnce Tuple Unit Vector Boolean Byte Character CharSequence Class ClassLoader Cloneable Comparable Compiler Double Exception Float Integer Long Math Number Object Package Pair Process Runtime Runnable SecurityManager Short StackTraceElement StrictMath String StringBuffer System Thread ThreadGroup ThreadLocal Throwable Triple Void"),multiLineStrings:!0,blockKeywords:m("catch class enum do else finally for forSome if match switch try while"),defKeywords:m("class enum def object package trait type val var"),atoms:m("true false null"),indentStatements:!1,indentSwitch:!1,isOperatorChar:/[+\-*&%=<>!?|\/#:@]/,hooks:{"@":function(C){return C.eatWhile(/[\w\$_]/),"meta"},'"':function(C,b){return C.match('""')?(b.tokenize=F,b.tokenize(C,b)):!1},"'":function(C){return C.eatWhile(/[\w\$_\xa1-\uffff]/),"atom"},"=":function(C,b){var U=b.context;return U.type=="}"&&U.align&&C.eat(">")?(b.context=new a(U.indented,U.column,U.type,U.info,null,U.prev),"operator"):!1},"/":function(C,b){return C.eat("*")?(b.tokenize=le(1),b.tokenize(C,b)):!1}},modeProps:{closeBrackets:{pairs:'()[]{}""',triples:'"'}}});function _(C){return function(b,U){for(var ne=!1,ue,Se=!1;!b.eol();){if(!C&&!ne&&b.match('"')){Se=!0;break}if(C&&b.match('"""')){Se=!0;break}ue=b.next(),!ne&&ue=="$"&&b.match("{")&&b.skipTo("}"),ne=!ne&&ue=="\\"&&!C}return(Se||!C)&&(U.tokenize=null),"string"}}V("text/x-kotlin",{name:"clike",keywords:m("package as typealias class interface this super val operator var fun for is in This throw return annotation break continue object if else while do try when !in !is as? file import where by get set abstract enum open inner override private public internal protected catch finally out final vararg reified dynamic companion constructor init sealed field property receiver param sparam lateinit data inline noinline tailrec external annotation crossinline const operator infix suspend actual expect setparam value"),types:m("Boolean Byte Character CharSequence Class ClassLoader Cloneable Comparable Compiler Double Exception Float Integer Long Math Number Object Package Pair Process Runtime Runnable SecurityManager Short StackTraceElement StrictMath String StringBuffer System Thread ThreadGroup ThreadLocal Throwable Triple Void Annotation Any BooleanArray ByteArray Char CharArray DeprecationLevel DoubleArray Enum FloatArray Function Int IntArray Lazy LazyThreadSafetyMode LongArray Nothing ShortArray Unit"),intendSwitch:!1,indentStatements:!1,multiLineStrings:!0,number:/^(?:0x[a-f\d_]+|0b[01_]+|(?:[\d_]+(\.\d+)?|\.\d+)(?:e[-+]?[\d_]+)?)(u|ll?|l|f)?/i,blockKeywords:m("catch class do else finally for if where try while enum"),defKeywords:m("class val var object interface fun"),atoms:m("true false null this"),hooks:{"@":function(C){return C.eatWhile(/[\w\$_]/),"meta"},"*":function(C,b){return b.prevToken=="."?"variable":"operator"},'"':function(C,b){return b.tokenize=_(C.match('""')),b.tokenize(C,b)},"/":function(C,b){return C.eat("*")?(b.tokenize=le(1),b.tokenize(C,b)):!1},indent:function(C,b,U,ne){var ue=U&&U.charAt(0);if((C.prevToken=="}"||C.prevToken==")")&&U=="")return C.indented;if(C.prevToken=="operator"&&U!="}"&&C.context.type!="}"||C.prevToken=="variable"&&ue=="."||(C.prevToken=="}"||C.prevToken==")")&&ue==".")return ne*2+b.indented;if(b.align&&b.type=="}")return b.indented+(C.context.type==(U||"").charAt(0)?0:ne)}},modeProps:{closeBrackets:{triples:'"'}}}),V(["x-shader/x-vertex","x-shader/x-fragment"],{name:"clike",keywords:m("sampler1D sampler2D sampler3D samplerCube sampler1DShadow sampler2DShadow const attribute uniform varying break continue discard return for while do if else struct in out inout"),types:m("float int bool void vec2 vec3 vec4 ivec2 ivec3 ivec4 bvec2 bvec3 bvec4 mat2 mat3 mat4"),blockKeywords:m("for while do if else struct"),builtin:m("radians degrees sin cos tan asin acos atan pow exp log exp2 sqrt inversesqrt abs sign floor ceil fract mod min max clamp mix step smoothstep length distance dot cross normalize ftransform faceforward reflect refract matrixCompMult lessThan lessThanEqual greaterThan greaterThanEqual equal notEqual any all not texture1D texture1DProj texture1DLod texture1DProjLod texture2D texture2DProj texture2DLod texture2DProjLod texture3D texture3DProj texture3DLod texture3DProjLod textureCube textureCubeLod shadow1D shadow2D shadow1DProj shadow2DProj shadow1DLod shadow2DLod shadow1DProjLod shadow2DProjLod dFdx dFdy fwidth noise1 noise2 noise3 noise4"),atoms:m("true false gl_FragColor gl_SecondaryColor gl_Normal gl_Vertex gl_MultiTexCoord0 gl_MultiTexCoord1 gl_MultiTexCoord2 gl_MultiTexCoord3 gl_MultiTexCoord4 gl_MultiTexCoord5 gl_MultiTexCoord6 gl_MultiTexCoord7 gl_FogCoord gl_PointCoord gl_Position gl_PointSize gl_ClipVertex gl_FrontColor gl_BackColor gl_FrontSecondaryColor gl_BackSecondaryColor gl_TexCoord gl_FogFragCoord gl_FragCoord gl_FrontFacing gl_FragData gl_FragDepth gl_ModelViewMatrix gl_ProjectionMatrix gl_ModelViewProjectionMatrix gl_TextureMatrix gl_NormalMatrix gl_ModelViewMatrixInverse gl_ProjectionMatrixInverse gl_ModelViewProjectionMatrixInverse gl_TextureMatrixTranspose gl_ModelViewMatrixInverseTranspose gl_ProjectionMatrixInverseTranspose gl_ModelViewProjectionMatrixInverseTranspose gl_TextureMatrixInverseTranspose gl_NormalScale gl_DepthRange gl_ClipPlane gl_Point gl_FrontMaterial gl_BackMaterial gl_LightSource gl_LightModel gl_FrontLightModelProduct gl_BackLightModelProduct gl_TextureColor gl_EyePlaneS gl_EyePlaneT gl_EyePlaneR gl_EyePlaneQ gl_FogParameters gl_MaxLights gl_MaxClipPlanes gl_MaxTextureUnits gl_MaxTextureCoords gl_MaxVertexAttribs gl_MaxVertexUniformComponents gl_MaxVaryingFloats gl_MaxVertexTextureImageUnits gl_MaxTextureImageUnits gl_MaxFragmentUniformComponents gl_MaxCombineTextureImageUnits gl_MaxDrawBuffers"),indentSwitch:!1,hooks:{"#":A},modeProps:{fold:["brace","include"]}}),V("text/x-nesc",{name:"clike",keywords:m(L+" as atomic async call command component components configuration event generic implementation includes interface module new norace nx_struct nx_union post provides signal task uses abstract extends"),types:K,blockKeywords:m(S),atoms:m("null true false"),hooks:{"#":A},modeProps:{fold:["brace","include"]}}),V("text/x-objectivec",{name:"clike",keywords:m(L+" "+P),types:T,builtin:m(W),blockKeywords:m(S+" @synthesize @try @catch @finally @autoreleasepool @synchronized"),defKeywords:m(M+" @interface @implementation @protocol @class"),dontIndentStatements:/^@.*$/,typeFirstDefinitions:!0,atoms:m("YES NO NULL Nil nil true false nullptr"),isReservedIdentifier:Q,hooks:{"#":A,"*":G},modeProps:{fold:["brace","include"]}}),V("text/x-objectivec++",{name:"clike",keywords:m(L+" "+P+" "+R),types:T,builtin:m(W),blockKeywords:m(S+" @synthesize @try @catch @finally @autoreleasepool @synchronized class try catch"),defKeywords:m(M+" @interface @implementation @protocol @class class namespace"),dontIndentStatements:/^@.*$|^template$/,typeFirstDefinitions:!0,atoms:m("YES NO NULL Nil nil true false nullptr"),isReservedIdentifier:Q,hooks:{"#":A,"*":G,u:Z,U:Z,L:Z,R:Z,0:z,1:z,2:z,3:z,4:z,5:z,6:z,7:z,8:z,9:z,token:function(C,b,U){if(U=="variable"&&C.peek()=="("&&(b.prevToken==";"||b.prevToken==null||b.prevToken=="}")&&Ce(C.current()))return"def"}},namespaceSeparator:"::",modeProps:{fold:["brace","include"]}}),V("text/x-squirrel",{name:"clike",keywords:m("base break clone continue const default delete enum extends function in class foreach local resume return this throw typeof yield constructor instanceof static"),types:K,blockKeywords:m("case catch class else for foreach if switch try while"),defKeywords:m("function local class"),typeFirstDefinitions:!0,atoms:m("true false null"),hooks:{"#":A},modeProps:{fold:["brace","include"]}});var X=null;function ee(C){return function(b,U){for(var ne=!1,ue,Se=!1;!b.eol();){if(!ne&&b.match('"')&&(C=="single"||b.match('""'))){Se=!0;break}if(!ne&&b.match("``")){X=ee(C),Se=!0;break}ue=b.next(),ne=C=="single"&&!ne&&ue=="\\"}return Se&&(U.tokenize=null),"string"}}V("text/x-ceylon",{name:"clike",keywords:m("abstracts alias assembly assert assign break case catch class continue dynamic else exists extends finally for function given if import in interface is let module new nonempty object of out outer package return satisfies super switch then this throw try value void while"),types:function(C){var b=C.charAt(0);return b===b.toUpperCase()&&b!==b.toLowerCase()},blockKeywords:m("case catch class dynamic else finally for function if interface module new object switch try while"),defKeywords:m("class dynamic function interface module object package value"),builtin:m("abstract actual aliased annotation by default deprecated doc final formal late license native optional sealed see serializable shared suppressWarnings tagged throws variable"),isPunctuationChar:/[\[\]{}\(\),;\:\.`]/,isOperatorChar:/[+\-*&%=<>!?|^~:\/]/,numberStart:/[\d#$]/,number:/^(?:#[\da-fA-F_]+|\$[01_]+|[\d_]+[kMGTPmunpf]?|[\d_]+\.[\d_]+(?:[eE][-+]?\d+|[kMGTPmunpf]|)|)/i,multiLineStrings:!0,typeFirstDefinitions:!0,atoms:m("true false null larger smaller equal empty finished"),indentSwitch:!1,styleDefs:!1,hooks:{"@":function(C){return C.eatWhile(/[\w\$_]/),"meta"},'"':function(C,b){return b.tokenize=ee(C.match('""')?"triple":"single"),b.tokenize(C,b)},"`":function(C,b){return!X||!C.match("`")?!1:(b.tokenize=X,X=null,b.tokenize(C,b))},"'":function(C){return C.eatWhile(/[\w\$_\xa1-\uffff]/),"atom"},token:function(C,b,U){if((U=="variable"||U=="type")&&b.prevToken==".")return"variable-2"}},modeProps:{fold:["brace","import"],closeBrackets:{triples:'"'}}})})})();const ck=({text:e,language:n,readOnly:i,highlight:a=[],revealLine:u,lineNumbers:c,focusOnChange:h,wrapLines:v,onChange:m})=>{const x=ye.createRef(),[L,R]=ye.useState();return ye.useEffect(()=>{let P;if(n==="javascript"&&(P="javascript"),n==="python"&&(P="python"),n==="java"&&(P="text/x-java"),n==="csharp"&&(P="text/x-csharp"),L&&L.getOption("mode")===P||!x.current)return;L&&L.getWrapperElement().remove();const W=fk(x.current,{value:"",mode:P,readOnly:i,lineNumbers:c,lineWrapping:v});m&&W.on("change",()=>m(W.getValue())),R(W),Zv(W,e,a,u,h)},[L,x,e,n,a,u,h,c,v,i,m]),L&&Zv(L,e,a,u,h),H("div",{className:"cm-wrapper",ref:x})};function Zv(e,n,i,a,u){e.getValue()!==n&&(e.setValue(n),u&&(e.execCommand("selectAll"),e.focus()));for(let c=0;c<e.lineCount();++c)e.removeLineClass(c,"wrap");for(const c of i)e.addLineClass(c.line-1,"wrap",`source-line-${c.type}`);a&&e.scrollIntoView({line:a-1,ch:0},50)}const dk=({text:e,language:n,highlight:i=[],revealLine:a})=>H(ck,{text:e,language:n,readOnly:!0,highlight:i,revealLine:a,lineNumbers:!0});const hk=({action:e,setSelectedFrame:n,selectedFrame:i})=>{const a=(e==null?void 0:e.metadata.stack)||[];return H("div",{className:"stack-trace",children:a.map((u,c)=>{const h=u.file[1]===":"?"\\":"/";return ge("div",{className:"stack-trace-frame"+(i===c?" selected":""),onClick:()=>{n(c)},children:[H("span",{className:"stack-trace-frame-function",children:u.function||"(anonymous)"}),H("span",{className:"stack-trace-frame-location",children:u.file.split(h).pop()}),H("span",{className:"stack-trace-frame-line",children:":"+u.line})]},c)})})},pk=({action:e})=>{var R;const[n,i]=ye.useState(),[a,u]=ye.useState(0),[c,h]=ye.useState(!1);n!==e&&(i(e),u(0),h(!0));const v=ye.useMemo(()=>{if(!e)return"";const{metadata:P}=e;return P.stack?{frames:P.stack,fileContent:new Map}:""},[e]),m=ok(async()=>{let P;if(typeof v=="string")P=v;else{const W=v.frames[a].file;if(!v.fileContent.has(W)){const B=await vk(W);v.fileContent.set(W,await fetch(`sha1/src@${B}.txt`).then(Y=>Y.text()).catch(Y=>`<Unable to read "${W}">`))}P=v.fileContent.get(W)}return P},[v,a],""),x=typeof v=="string"?0:((R=v.frames[a])==null?void 0:R.line)||0,L=ye.createRef();return ye.useLayoutEffect(()=>{c&&L.current&&(L.current.scrollIntoView({block:"center",inline:"nearest"}),h(!1))},[c,L]),ge(Ac,{sidebarSize:100,orientation:"vertical",children:[H(dk,{text:m,language:"javascript",highlight:[{line:x,type:"running"}],revealLine:x}),H(hk,{action:e,selectedFrame:a,setSelectedFrame:u})]})};async function vk(e){const n=new TextEncoder().encode(e),i=await crypto.subtle.digest("SHA-1",n),a=[],u=new DataView(i);for(let c=0;c<u.byteLength;c+=1){const h=u.getUint8(c).toString(16).padStart(2,"0");a.push(h)}return a.join("")}const Jv=({tabs:e,selectedTab:n,setSelectedTab:i})=>H("div",{className:"tabbed-pane",children:ge("div",{className:"vbox",children:[H("div",{className:"hbox",style:{flex:"none"},children:H("div",{className:"tab-strip",children:e.map(a=>ge("div",{className:"tab-element "+(n===a.id?"selected":""),onClick:()=>i(a.id),children:[H("div",{className:"tab-label",children:a.title}),H("div",{className:"tab-count",children:a.count||""})]},a.id))})}),e.map(a=>{if(n===a.id)return H("div",{className:"tab-content",children:a.render()},a.id)})]})});const U0={width:200,height:45},gk=({context:e,boundaries:n,previewPoint:i})=>{var x;const[a,u]=Od();let c=0;if(u.current&&i){const L=u.current.getBoundingClientRect();c=(i.clientY-L.top)/U0.height|0}const h=(x=e.pages[c])==null?void 0:x.screencastFrames;let v,m;if(i!==void 0&&h){const L=n.minimum+(n.maximum-n.minimum)*i.x/a.width;v=h[c0(h,L,j0)-1],m=v?$0({width:v.width,height:v.height},{width:window.innerWidth*3/4|0,height:window.innerHeight*3/4|0}):void 0}return ge("div",{className:"film-strip",ref:u,children:[e.pages.filter(L=>L.screencastFrames.length).map((L,R)=>H(mk,{boundaries:n,page:L,width:a.width},R)),v&&m&&(i==null?void 0:i.x)!==void 0&&H("div",{className:"film-strip-hover",style:{width:m.width,height:m.height,top:a.bottom+5,left:Math.min(i.x,a.width-m.width-10)},children:H("img",{src:`sha1/${v.sha1}`,width:m.width,height:m.height})})]})},mk=({boundaries:e,page:n,width:i})=>{const a={width:0,height:0},u=n.screencastFrames;for(const K of u)a.width=Math.max(a.width,K.width),a.height=Math.max(a.height,K.height);const c=$0(a,U0),h=2.5,v=u[0].timestamp,m=u[u.length-1].timestamp,x=e.maximum-e.minimum,L=(v-e.minimum)/x*i,R=(e.maximum-m)/x*i,W=(m-v)/x*i/(c.width+2*h)|0,B=(m-v)/W,Y=[];for(let K=0;v&&B&&K<W;++K){const T=v+B*K,S=c0(u,T,j0)-1;Y.push(H("div",{className:"film-strip-frame",style:{width:c.width,height:c.height,backgroundImage:`url(sha1/${u[S].sha1})`,backgroundSize:`${c.width}px ${c.height}px`,margin:h,marginRight:h}},K))}return Y.push(H("div",{className:"film-strip-frame",style:{width:c.width,height:c.height,backgroundImage:`url(sha1/${u[u.length-1].sha1})`,backgroundSize:`${c.width}px ${c.height}px`,margin:h,marginRight:h}},Y.length)),H("div",{className:"film-strip-lane",style:{marginLeft:L+"px",marginRight:R+"px"},children:Y})};function j0(e,n){return e-n.timestamp}function $0(e,n){const i=Math.max(e.width/n.width,e.height/n.height);return{width:e.width/i|0,height:e.height/i|0}}const yk=({context:e,boundaries:n,selectedAction:i,onSelected:a})=>{const[u,c]=Od(),h=ye.useRef(null),[v,m]=ye.useState(),[x,L]=ye.useState(),R=ye.useMemo(()=>wk(u.width,n),[u.width,n]),P=ye.useMemo(()=>{const M=[];for(const A of e.actions){let G=eg(A.metadata.params.selector||"",50);A.metadata.method==="goto"&&(G=eg(A.metadata.params.url||"",50)),M.push({action:A,leftTime:A.metadata.startTime,rightTime:A.metadata.endTime,leftPosition:Do(u.width,n,A.metadata.startTime),rightPosition:Do(u.width,n,A.metadata.endTime),label:A.metadata.apiName+" "+G,title:A.metadata.endTime?Mi(A.metadata.endTime-A.metadata.startTime):"Timed Out",type:A.metadata.type+"."+A.metadata.method,className:`${A.metadata.type}_${A.metadata.method}`.toLowerCase()})}for(const A of e.events){const G=A.metadata.startTime;M.push({event:A,leftTime:G,rightTime:G,leftPosition:Do(u.width,n,G),rightPosition:Do(u.width,n,G),label:A.metadata.method,title:A.metadata.endTime?Mi(A.metadata.endTime-A.metadata.startTime):"Timed Out",type:A.metadata.type+"."+A.metadata.method,className:`${A.metadata.type}_${A.metadata.method}`.toLowerCase()})}return M},[e,n,u.width]),W=x!==void 0?P[x]:void 0;let B=P.find(M=>M.action===i);B=W||B;const Y=(M,A)=>{const G=Uf(u.width,n,M),Q=Uf(u.width,n,M-5),z=Uf(u.width,n,M+5);let Z,Ce,we;for(let ze=0;ze<P.length;ze++){const V=P[ze],F=xk/2+tg(V),le=Math.max(V.leftTime,Q),_=Math.min(V.rightTime,z),X=(V.leftTime+V.rightTime)/2,ee=Math.abs(G-X),C=Math.abs(A-F);le>_||(Z===void 0||C<Ce||Math.abs(C-Ce)<.01&&ee<we)&&(Z=ze,we=ee,Ce=C)}return Z},K=M=>{if(!c.current||!h.current)return;const A=M.clientX-c.current.getBoundingClientRect().left,G=M.clientY-h.current.getBoundingClientRect().top,Q=Y(A,G);m({x:A,clientY:M.clientY}),L(Q)};return ge("div",{ref:c,className:"timeline-view",onMouseMove:K,onMouseOver:K,onMouseLeave:()=>{m(void 0),L(void 0)},onClick:M=>{if(m(void 0),!c.current||!h.current)return;const A=M.clientX-c.current.getBoundingClientRect().left,G=M.clientY-h.current.getBoundingClientRect().top,Q=Y(A,G);if(Q===void 0)return;const z=P[Q].action;z&&a(z)},children:[H("div",{className:"timeline-grid",children:R.map((M,A)=>H("div",{className:"timeline-divider",style:{left:M.position+"px"},children:H("div",{className:"timeline-time",children:Mi(M.time-n.minimum)})},A))}),H("div",{className:"timeline-lane timeline-labels",children:P.map((M,A)=>H("div",{className:"timeline-label "+M.className+(B===M?" selected":""),style:{left:M.leftPosition,maxWidth:100},children:M.label},A))}),H("div",{className:"timeline-lane timeline-bars",ref:h,children:P.map((M,A)=>H("div",{className:"timeline-bar "+(M.action?"action ":"")+(M.event?"event ":"")+M.className+(B===M?" selected":""),style:{left:M.leftPosition+"px",width:Math.max(1,M.rightPosition-M.leftPosition)+"px",top:tg(M)+"px"},title:M.title},A))}),H(gk,{context:e,boundaries:n,previewPoint:v}),H("div",{className:"timeline-marker timeline-marker-hover",style:{display:v!==void 0?"block":"none",left:((v==null?void 0:v.x)||0)+"px"}})]})};function wk(e,n){let a=e/64;const u=n.maximum-n.minimum,c=e/u;let h=u/a;const v=Math.ceil(Math.log(h)/Math.LN10);h=Math.pow(10,v),h*c>=5*64&&(h=h/5),h*c>=2*64&&(h=h/2);const m=n.minimum;let x=n.maximum;x+=64/c,a=Math.ceil((x-m)/h),h||(a=0);const L=[];for(let R=0;R<a;++R){const P=m+h*R;L.push({position:Do(e,n,P),time:P})}return L}function Do(e,n,i){return(i-n.minimum)/(n.maximum-n.minimum)*e}function Uf(e,n,i){return i/e*(n.maximum-n.minimum)+n.minimum}function eg(e,n){return e.length<=n?e:e.substring(0,n-1)+"…"}const xk=11;function tg(e){var n;return e.event?22:((n=e.action)==null?void 0:n.metadata.method)==="waitForEventInfo"?0:11}const Sk=()=>{const[e,n]=ye.useState([]),[i,a]=ye.useState([]),[u,c]=ye.useState(ng),[h,v]=ye.useState({done:0,total:0}),[m,x]=ye.useState(!1),[L,R]=ye.useState(null),[P,W]=ye.useState(null),B=T=>{const S=[],M=[],A=new URL(window.location.href);for(let Q=0;Q<T.length;Q++){const z=T.item(Q);if(!z)continue;const Z=URL.createObjectURL(z);S.push(Z),M.push(z.name),A.searchParams.append("trace",Z),A.searchParams.append("traceFileName",z.name)}const G=A.toString();window.history.pushState({},"",G),n(S),a(M),x(!1),R(null)},Y=T=>{T.preventDefault(),B(T.dataTransfer.files)},K=T=>{T.preventDefault(),T.target.files&&B(T.target.files)};return ye.useEffect(()=>{const T=new URL(window.location.href).searchParams.getAll("trace");for(const S of T)if(S.startsWith("file:")){W(S||null);return}T.some(S=>S.startsWith("blob:"))||n(T)},[n]),ye.useEffect(()=>{(async()=>{if(e.length){const T=A=>{A.data.method==="progress"&&v(A.data.params)};navigator.serviceWorker.addEventListener("message",T),v({done:0,total:1});const S=[];for(let A=0;A<e.length;A++){const G=e[A],Q=new URLSearchParams;Q.set("trace",G),i.length&&Q.set("traceFileName",i[A]);const z=await fetch(`context?${Q.toString()}`);if(!z.ok){n([]),R((await z.json()).error);return}const Z=await z.json();S.push(Z)}navigator.serviceWorker.removeEventListener("message",T);const M=new p0(S);v({done:0,total:0}),c(M)}else c(ng)})()},[e,i]),ge("div",{className:"vbox workbench",onDragOver:T=>{T.preventDefault(),x(!0)},children:[ge("div",{className:"hbox header",children:[H("div",{className:"logo",children:"🎭"}),H("div",{className:"product",children:"Playwright"}),u.title&&H("div",{className:"title",children:u.title}),H("div",{className:"spacer"}),H(SS,{icon:"color-mode",title:"Toggle color mode",toggled:!1,onClick:()=>xS()})]}),H(kk,{model:u,view:"standalone"}),!!h.total&&H("div",{className:"progress",children:H("div",{className:"inner-progress",style:{width:100*h.done/h.total+"%"}})}),P&&ge("div",{className:"drop-target",children:[H("div",{children:"Trace Viewer uses Service Workers to show traces. To view trace:"}),ge("div",{style:{paddingTop:20},children:[ge("div",{children:["1. Click ",H("a",{href:P,children:"here"})," to put your trace into the download shelf"]}),ge("div",{children:["2. Go to ",H("a",{href:"https://trace.playwright.dev",children:"trace.playwright.dev"})]}),H("div",{children:"3. Drop the trace from the download shelf into the page"})]})]}),!m&&!P&&(!e.length||L)&&ge("div",{className:"drop-target",children:[H("div",{className:"processing-error",children:L}),H("div",{className:"title",children:"Drop Playwright Trace to load"}),H("div",{children:"or"}),H("button",{onClick:()=>{const T=document.createElement("input");T.type="file",T.click(),T.addEventListener("change",S=>K(S))},children:"Select file"}),H("div",{style:{maxWidth:400},children:"Playwright Trace Viewer is a Progressive Web App, it does not send your trace anywhere, it opens it locally."})]}),m&&H("div",{className:"drop-target",onDragLeave:()=>{x(!1)},onDrop:T=>Y(T),children:H("div",{className:"title",children:"Release to analyse the Playwright Trace"})})]})},kk=({model:e,view:n})=>{const[i,a]=ye.useState(),[u,c]=ye.useState(),[h,v]=ye.useState("actions"),[m,x]=ye.useState("logs"),L=u||i,R={minimum:e.startTime,maximum:e.endTime};R.maximum+=(R.maximum-R.minimum)/20;const{errors:P,warnings:W}=L?g0(L):{errors:0,warnings:0},B=P+W,Y=L?y0(L).length:0,K=[{id:"logs",title:"Call",count:0,render:()=>H(ek,{action:L,sdkLanguage:e.sdkLanguage})},{id:"console",title:"Console",count:B,render:()=>H(tk,{action:L})},{id:"network",title:"Network",count:Y,render:()=>H(lk,{action:L})}];return e.hasSource&&K.push({id:"source",title:"Source",count:0,render:()=>H(pk,{action:i})}),ge("div",{className:"vbox",children:[H("div",{style:{paddingLeft:"20px",flex:"none",borderBottom:"1px solid var(--vscode-panel-border)"},children:H(yk,{context:e,boundaries:R,selectedAction:L,onSelected:T=>a(T)})}),ge(Ac,{sidebarSize:300,orientation:"horizontal",sidebarIsFirst:!0,children:[ge(Ac,{sidebarSize:300,orientation:n==="embedded"?"vertical":"horizontal",children:[H(sk,{action:L}),H(Jv,{tabs:K,selectedTab:m,setSelectedTab:x})]}),H(Jv,{tabs:[{id:"actions",title:"Actions",count:0,render:()=>H(XS,{sdkLanguage:e.sdkLanguage,actions:e.actions,selectedAction:i,onSelected:T=>{a(T)},onHighlighted:T=>{c(T)},setSelectedTab:x})},{id:"metadata",title:"Metadata",count:0,render:()=>{var T,S;return ge("div",{className:"vbox",children:[H("div",{className:"call-section",style:{paddingTop:2},children:"Time"}),e.wallTime&&ge("div",{className:"call-line",children:["start time:",H("span",{className:"call-value datetime",title:new Date(e.wallTime).toLocaleString(),children:new Date(e.wallTime).toLocaleString()})]}),ge("div",{className:"call-line",children:["duration:",H("span",{className:"call-value number",title:Mi(e.endTime-e.startTime),children:Mi(e.endTime-e.startTime)})]}),H("div",{className:"call-section",children:"Browser"}),ge("div",{className:"call-line",children:["engine:",H("span",{className:"call-value string",title:e.browserName,children:e.browserName})]}),e.platform&&ge("div",{className:"call-line",children:["platform:",H("span",{className:"call-value string",title:e.platform,children:e.platform})]}),e.options.userAgent&&ge("div",{className:"call-line",children:["user agent:",H("span",{className:"call-value datetime",title:e.options.userAgent,children:e.options.userAgent})]}),H("div",{className:"call-section",children:"Viewport"}),e.options.viewport&&ge("div",{className:"call-line",children:["width:",H("span",{className:"call-value number",title:String(!!((T=e.options.viewport)!=null&&T.width)),children:e.options.viewport.width})]}),e.options.viewport&&ge("div",{className:"call-line",children:["height:",H("span",{className:"call-value number",title:String(!!((S=e.options.viewport)!=null&&S.height)),children:e.options.viewport.height})]}),ge("div",{className:"call-line",children:["is mobile:",H("span",{className:"call-value boolean",title:String(!!e.options.isMobile),children:String(!!e.options.isMobile)})]}),e.options.deviceScaleFactor&&ge("div",{className:"call-line",children:["device scale:",H("span",{className:"call-value number",title:String(e.options.deviceScaleFactor),children:String(e.options.deviceScaleFactor)})]}),H("div",{className:"call-section",children:"Counts"}),ge("div",{className:"call-line",children:["pages:",H("span",{className:"call-value number",children:e.pages.length})]}),ge("div",{className:"call-line",children:["actions:",H("span",{className:"call-value number",children:e.actions.length})]}),ge("div",{className:"call-line",children:["events:",H("span",{className:"call-value number",children:e.events.length})]})]})}}],selectedTab:h,setSelectedTab:v})]})]})},ng=new p0([]);(async()=>(wS(),window.location.protocol!=="file:"&&(window.location.href.includes("isUnderTest=true")&&await new Promise(e=>setTimeout(e,1e3)),navigator.serviceWorker.register("sw.bundle.js"),navigator.serviceWorker.controller||await new Promise(e=>{navigator.serviceWorker.oncontrollerchange=()=>e()}),setInterval(function(){fetch("ping")},1e4)),jf.render(H(Sk,{}),document.querySelector("#root"))))();
