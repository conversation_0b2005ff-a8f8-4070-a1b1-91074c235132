# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Architecture Overview

This is a multi-store supermarket price scraping system for New Zealand supermarkets (Woolworths, New World, PakNSave). The system consists of:

### Scrapers
- **Woolworths scraper** (Node.js/TypeScript) - `Woolworths/src/index.ts`
- **New World scraper** (C#/.NET) - `new-world/src/Program.cs`  
- **PakNSave scraper** (C#/.NET) - `paknsave/src/Program.cs`

### Database Architecture
- **Current**: MongoDB (migrated from Supabase PostgreSQL)
- **Collections**: `stores`, `brands`, `consolidatedProducts`, `priceHistory`, `categoryHierarchy`
- **Storage**: GridFS for product images
- **Migration complete**: All scrapers now use MongoDB exclusively

### Shared Components
- Each scraper uses Playwright for web automation
- MongoDB client libraries for database operations
- Product overrides system for data quality (`ProductOverrides.txt` files)
- Rate limiting between page loads to avoid being blocked
- Consolidated products system for cross-store price comparison

## Common Development Commands

### Node.js (Woolworths)
```bash
cd Woolworths
npm install
npx playwright install  # Required on first setup
npm run dev          # Dry run mode (console output only)
npm run db           # Scrape with database storage
npm run db images    # Scrape with database + image upload
npm test             # Run Jest tests
```

### C# (.NET 6+)
```bash
cd paknsave         # or cd new-world/src
dotnet restore
dotnet build        # Build the project
pwsh bin/Debug/net6.0/playwright.ps1 install chromium  # Required on first setup
dotnet run          # Dry run mode
dotnet run db       # Scrape with database storage
dotnet run db images # Scrape with database + image upload
dotnet test         # Run tests (paknsave only)
```

**⚠️ Windows NuGet Issues**: If `dotnet restore` fails with NuGet source errors, run:
```cmd
fix-dotnet-issues.bat     # Command Prompt
# or
.\fix-dotnet-issues.ps1   # PowerShell
```

### Migration Scripts (Historical)
```bash
cd scripts
npm install
npm run export      # Historical data export utility
npm run import      # Historical data import utility
```

## Configuration

### Node.js Environment (.env)
```
MONGODB_CONNECTION_STRING=mongodb://localhost:27017/nz-supermarket-scraper
MONGODB_DATABASE_NAME=nz-supermarket-scraper
STORE_NAME=optional-store-location
```

### C# Configuration (appsettings.json)
```json
{
  "MONGODB_CONNECTION_STRING": "mongodb://localhost:27017/nz-supermarket-scraper",
  "MONGODB_DATABASE_NAME": "nz-supermarket-scraper"
}
```

## Key Implementation Details

### Data Models
All scrapers use similar Product records with:
- Unique product ID per store
- Price history tracking with dates
- Unit pricing calculations
- Category classification
- Store-specific metadata

### Rate Limiting
- Woolworths: 7 second delay between pages
- New World: 11 second delay between pages
- PakNSave: 11 second delay between pages

### Product URL Management
- URLs stored in `urls.txt` files in each scraper directory
- Supports category-based URL organization
- Single URL testing via command line arguments

### Error Handling
- Robust retry mechanisms for network failures
- Product override system for data quality issues
- Detailed logging with colored console output

## Testing

### Woolworths (Jest)
- Tests located in `Woolworths/tests/`
- Configuration in `Woolworths/jest.config.js`
- Run with `npm test`

### PakNSave (MSTest)
- Tests in `paknsave/tests/`
- Includes MongoDB and scraper unit tests  
- Run with `dotnet test`
- Uses MSTest framework, not XUnit

## Database Migration Notes

The system uses MongoDB for data storage. Key features:

- All scrapers now use MongoDB exclusively
- Document-based storage with flexible schema
- GridFS for binary image storage
- Indexed collections for performance
- Consolidated products system for cross-store price comparison
- Historical data management scripts remain available in `scripts/` for reference

## MongoDB Setup

MongoDB must be running locally on port 27017. Use the provided helper scripts:

```bash
# Start MongoDB using Docker (recommended)
docker run -d -p 27017:27017 --name mongodb mongo:latest

# Or use the provided scripts
start-mongodb.bat        # Windows
./start-mongodb.sh       # Linux/macOS
```

## Running All Scrapers

### Recommended Approach (Windows)
Use the simplified batch files for reliable execution:

```cmd
# Run all scrapers in database + images mode
run-all-db-images.bat

# Or run individual scrapers
start-woolworths-db-images.bat
start-newworld-db-images.bat  
start-paknsave-db-images.bat
```

### Alternative: Unified Script
A unified Node.js script is available but has platform compatibility issues:

```bash
# Check MongoDB connection first
node start-scrapers.js check-schema

# Run all scrapers in dev mode (dry run)
node start-scrapers.js dev

# Run all scrapers with database storage
node start-scrapers.js db

# Run all scrapers with database + image upload
node start-scrapers.js db-images

# Run specific scrapers only
node start-scrapers.js dev woolworths
node start-scrapers.js db newworld paknsave
```

### Manual Execution
For direct control, run scrapers individually:

```bash
# Woolworths (use Windows cmd for best compatibility)
cd Woolworths && npm run "db images"

# New World
cd new-world/src && dotnet run db images

# PakNSave  
cd paknsave/src && dotnet run db images
```

## Known Issues & Fixes

### Woolworths TypeScript Execution
- **Issue**: `esrun` has E2BIG errors in WSL
- **Fix**: Run via Windows Command Prompt instead of WSL
- **Alternative**: Use `tsx` runner with `npm run dev-tsx`

### .NET Framework Warnings
- **Issue**: Microsoft.Bcl.AsyncInterfaces warnings about net6.0 compatibility
- **Fix**: Consider upgrading to net8.0 or suppress warnings with `<SuppressTfmSupportBuildWarnings>true</SuppressTfmSupportBuildWarnings>`

### NuGet Package Vulnerabilities
- **Issue**: Npgsql 7.0.6 has known security vulnerabilities
- **Fix**: Update to latest stable version in .csproj files

## Development Tips

- Use dry run mode (`npm run dev` or `dotnet run`) for testing without database writes
- Check product overrides files when data appears incorrect
- Monitor console output for detailed scraping progress and errors
- Use single URL testing for debugging specific products or pages
- Respect rate limits to avoid being blocked by target websites