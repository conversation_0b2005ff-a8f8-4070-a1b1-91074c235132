import { MongoClient, Db, Collection, GridFSBucket, ObjectId } from "mongodb";
import * as dotenv from "dotenv";
dotenv.config();
dotenv.config({ path: `.env.local`, override: true });

import { Product, UpsertResponse } from "./typings.js";
import { colour, log, logError } from "./utilities.js";

let client: MongoClient;
let db: Db;
let gridFS: GridFSBucket;
let storeId: ObjectId | undefined; // Woolworths store ObjectId

// Collections
let storesCollection: Collection;
let brandsCollection: Collection;
let consolidatedProductsCollection: Collection;
let priceHistoryCollection: Collection;
let categoryHierarchyCollection: Collection;

export async function establishMongoDB() {
  const connectionString = process.env.MONGODB_CONNECTION_STRING;
  const databaseName = process.env.MONGODB_DATABASE_NAME || "nz-supermarket-scraper";
  
  if (!connectionString) {
    throw Error("MONGODB_CONNECTION_STRING not set in env");
  }

  try {
    client = new MongoClient(connectionString);
    await client.connect();
    db = client.db(databaseName);
    
    // Initialize collections
    storesCollection = db.collection("stores");
    brandsCollection = db.collection("brands");
    consolidatedProductsCollection = db.collection("consolidatedProducts");
    priceHistoryCollection = db.collection("priceHistory");
    categoryHierarchyCollection = db.collection("categoryHierarchy");
    
    // Initialize GridFS for image storage
    gridFS = new GridFSBucket(db, { bucketName: "productImages" });
    
    log(colour.green, "✅ MongoDB connection established");
    
    // Create indexes for performance
    await createIndexes();
    
  } catch (error: any) {
    logError(`Failed to connect to MongoDB: ${error.message}`);
    throw error;
  }
}

async function createIndexes() {
  try {
    // Consolidated products indexes
    await consolidatedProductsCollection.createIndex({ 
      "displayName": "text", 
      "normalizedName": "text", 
      "variants.storeName": "text" 
    });
    await consolidatedProductsCollection.createIndex({ "categoryId": 1 });
    await consolidatedProductsCollection.createIndex({ "brandId": 1 });
    await consolidatedProductsCollection.createIndex({ 
      "variants.storeProductId": 1, 
      "variants.storeId": 1 
    });
    
    // Price history indexes
    await priceHistoryCollection.createIndex({ 
      "consolidatedProductId": 1, 
      "recordedAt": -1 
    });
    await priceHistoryCollection.createIndex({ "year": 1, "month": 1 });
    
    // Category hierarchy indexes
    await categoryHierarchyCollection.createIndex({ "parentId": 1, "sortOrder": 1 });
    await categoryHierarchyCollection.createIndex({ "level": 1, "sortOrder": 1 });
    
    log(colour.blue, "✅ MongoDB indexes created");
  } catch (error: any) {
    logError(`Failed to create indexes: ${error.message}`);
  }
}

async function ensureStoreRow(): Promise<ObjectId> {
  if (storeId !== undefined) return storeId;
  
  try {
    const existingStore = await storesCollection.findOne({ storeId: "woolworths" });
    
    if (existingStore) {
      storeId = existingStore._id;
      return storeId;
    }

    // Insert store if not exists
    const insertResult = await storesCollection.insertOne({
      storeId: "woolworths",
      name: "Woolworths",
      logoUrl: null,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    storeId = insertResult.insertedId;
    return storeId;
    
  } catch (error: any) {
    logError(`Failed to ensure store row: ${error.message}`);
    throw error;
  }
}

export async function upsertProductToMongoDB(scraped: Product): Promise<UpsertResponse> {
  if (!db) throw Error("MongoDB client not initialised");
  
  try {
    const sId = await ensureStoreRow();
    const now = new Date();
    
    // Find or create consolidated product
    let consolidatedProduct = await consolidatedProductsCollection.findOne({
      "variants.storeProductId": scraped.id,
      "variants.storeId": sId
    });
    
    if (!consolidatedProduct) {
      // Try to find matching consolidated product using enhanced algorithm
      const normalizedName = normalizeProductName(scraped.name, scraped.size);
      log(colour.cyan, `🔍 Processing: ${scraped.name} -> normalized: ${normalizedName}`);

      const matchResult = await findBestMatch(normalizedName, scraped.size, scraped.name);

      if (matchResult && matchResult.confidence >= 0.85 && matchResult.product) {
        // High confidence match - add this as a new variant and add alias
        consolidatedProduct = matchResult.product;

        // Add current product name as alias if different and not exact match
        if (matchResult.matchType !== 'exact' && consolidatedProduct) {
          await addProductAlias(consolidatedProduct._id, scraped.name);
        }

        // Add new variant to existing consolidated product
        if (consolidatedProduct) {
          const newVariant = {
            storeProductId: scraped.id,
            storeId: sId,
            storeName: scraped.name,
            storeSize: scraped.size || null,
            storeUnitPrice: scraped.unitPrice || null,
            storeUnitName: scraped.unitName || null,
            lastSeen: now,
            isActive: true,
            imageUrl: null
          };

          await consolidatedProductsCollection.updateOne(
            { _id: consolidatedProduct._id },
            {
              $push: { variants: newVariant } as any,
              $set: { updatedAt: now }
            }
          );

          log(colour.green, `✅ Added variant to existing product: ${consolidatedProduct.displayName} (confidence: ${matchResult.confidence.toFixed(3)}, type: ${matchResult.matchType})`);
        }
      } else {
        // Create new consolidated product
        const newProduct = {
          normalizedName,
          displayName: scraped.name,
          primarySize: scraped.size,
          categoryId: null, // TODO: Implement category mapping
          brandId: null, // TODO: Implement brand extraction
          matchConfidence: matchResult ? Math.round(matchResult.confidence * 100) : 100,
          manualMatch: false,
          aliases: [], // Initialize empty aliases array
          variants: [{
            storeProductId: scraped.id,
            storeId: sId,
            storeName: scraped.name,
            storeSize: scraped.size || null,
            storeUnitPrice: scraped.unitPrice || null,
            storeUnitName: scraped.unitName || null,
            lastSeen: now,
            isActive: true,
            imageUrl: null
          }],
          sizeVariants: scraped.size ? [{
            sizeName: scraped.size,
            sizeWeightGrams: null,
            sizeVolumeMl: null,
            isPrimarySize: true
          }] : [],
          currentPrices: [{
            storeId: sId,
            price: scraped.currentPrice,
            isSpecial: false,
            wasAvailable: true,
            recordedAt: now
          }],
          createdAt: now,
          updatedAt: now
        };

        const insertResult = await consolidatedProductsCollection.insertOne(newProduct);
        consolidatedProduct = { _id: insertResult.insertedId, ...newProduct };

        if (matchResult && matchResult.confidence >= 0.7) {
          log(colour.blue, `🆕 Created new product (matches below threshold): ${scraped.name} (Best match: ${matchResult.confidence.toFixed(3)})`);
        } else {
          log(colour.blue, `🆕 Created new consolidated product: ${scraped.name}`);
        }
      }
    } else {
      // Update existing consolidated product
      await consolidatedProductsCollection.updateOne(
        { _id: consolidatedProduct._id },
        {
          $set: {
            "variants.$[variant].lastSeen": now,
            "variants.$[variant].storeUnitPrice": scraped.unitPrice,
            "variants.$[variant].storeUnitName": scraped.unitName,
            "currentPrices.$[price].price": scraped.currentPrice,
            "currentPrices.$[price].recordedAt": now,
            updatedAt: now
          }
        },
        {
          arrayFilters: [
            { "variant.storeProductId": scraped.id },
            { "price.storeId": sId }
          ]
        }
      );
    }
    
    // Insert price history record
    if (consolidatedProduct) {
      await priceHistoryCollection.insertOne({
        consolidatedProductId: consolidatedProduct._id,
        storeId: sId,
        price: scraped.currentPrice,
        isSpecial: false,
        wasAvailable: true,
        recordedAt: now,
        year: now.getFullYear(),
        month: now.getMonth() + 1
      });
    }
    
    log(colour.green, `  Upserted: ${scraped.name.slice(0, 45).padEnd(45)} | $${scraped.currentPrice}`);
    return UpsertResponse.PriceChanged;
    
  } catch (error: any) {
    logError(`MongoDB upsert failed: ${error.message}`);
    return UpsertResponse.Failed;
  }
}

// Upload product image to MongoDB GridFS
export async function uploadImageToMongoDB(imageUrl: string, product: Product): Promise<boolean> {
  if (!gridFS) throw Error("MongoDB GridFS not initialised");

  try {
    const sId = await ensureStoreRow();
    
    // Download image from Woolworths
    const imageResponse = await fetch(imageUrl);
    if (!imageResponse.ok) {
      logError(`Failed to download image from ${imageUrl}: ${imageResponse.statusText}`);
      return false;
    }

    const imageBuffer = Buffer.from(await imageResponse.arrayBuffer());
    
    // Check if image already exists and delete it
    const existingFiles = await gridFS.find({ 
      "metadata.productId": product.id 
    }).toArray();
    
    for (const file of existingFiles) {
      await gridFS.delete(file._id);
    }
    
    // Create upload stream
    const filename = `${product.id}.jpg`;
    const uploadStream = gridFS.openUploadStream(filename, {
      contentType: 'image/jpeg',
      metadata: {
        productId: product.id,
        storeId: sId,
        originalUrl: imageUrl,
        uploadedAt: new Date()
      }
    });
    
    // Upload the image
    await new Promise((resolve, reject) => {
      uploadStream.end(imageBuffer, (error) => {
        if (error) reject(error);
        else resolve(uploadStream.id);
      });
    });
    
    // Update product with image URL reference
    const imageUrl_gridfs = `gridfs://productImages/${uploadStream.id}`;
    
    await consolidatedProductsCollection.updateOne(
      { "variants.storeProductId": product.id },
      {
        $set: {
          "variants.$[variant].imageUrl": imageUrl_gridfs
        }
      },
      {
        arrayFilters: [{ "variant.storeProductId": product.id }]
      }
    );
    
    log(colour.blue, `  Image uploaded: ${product.id} -> GridFS ${uploadStream.id}`);
    return true;

  } catch (err: any) {
    logError(`Image upload error: ${err.message}`);
    return false;
  }
}

// Store descriptor removal patterns
const storeDescriptors = [
  'woolworths', 'countdown', 'new world', 'paknsave', 'pak n save',
  'select', 'premium', 'value', 'budget', 'signature', 'essentials',
  'pams', 'homebrand', 'signature range', 'fresh choice', 'macro',
  'organic', 'free range', 'grass fed', 'natural', 'artisan',
  'gourmet', 'deluxe', 'finest', 'choice', 'quality', 'fresh',
  'pure', 'real', 'authentic', 'traditional', 'classic'
];

// Size normalization patterns
const sizeNormalizations: Record<string, string> = {
  'grams': 'g',
  'gram': 'g',
  'kilograms': 'kg',
  'kilogram': 'kg',
  'kg': 'kg',
  'litres': 'l',
  'litre': 'l',
  'ltr': 'l',
  'millilitres': 'ml',
  'millilitre': 'ml',
  'pieces': 'pc',
  'piece': 'pc',
  'each': 'ea',
  'pack': 'pk',
  'packet': 'pk'
};

// Helper function to remove store descriptors
function removeStoreDescriptors(productName: string): string {
  let cleaned = productName.toLowerCase();

  for (const descriptor of storeDescriptors) {
    const regex = new RegExp(`\\b${descriptor}\\b`, 'gi');
    cleaned = cleaned.replace(regex, '').trim();
  }

  return cleaned.replace(/\s+/g, ' ').trim();
}

// Helper function to normalize size
function normalizeSize(size?: string): string {
  if (!size) return '';

  let normalized = size.toLowerCase().replace(/[^\w\d]/g, '');

  for (const [original, replacement] of Object.entries(sizeNormalizations)) {
    const regex = new RegExp(original, 'gi');
    normalized = normalized.replace(regex, replacement);
  }

  return normalized;
}

// Calculate size similarity for better matching
function calculateSizeSimilarity(size1?: string, size2?: string): number {
  if (!size1 || !size2) return 0;

  const normalized1 = normalizeSize(size1);
  const normalized2 = normalizeSize(size2);

  if (normalized1 === normalized2) return 1.0;

  // Try to extract numeric values for comparison
  const num1 = parseFloat(normalized1.replace(/[^\d.]/g, ''));
  const num2 = parseFloat(normalized2.replace(/[^\d.]/g, ''));

  if (!isNaN(num1) && !isNaN(num2)) {
    const ratio = Math.min(num1, num2) / Math.max(num1, num2);
    return ratio > 0.8 ? ratio : 0;
  }

  return calculateSimilarity(normalized1, normalized2);
}

// Enhanced product name normalization with comprehensive brand mappings
function normalizeProductName(name: string, size?: string): string {
  if (!name) return '';

  // Basic normalization
  let normalized = name.toLowerCase()
    .replace(/'/g, '')
    .replace(/"/g, '')
    .replace(/-/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();

  // Remove store descriptors
  normalized = removeStoreDescriptors(normalized);

  // Apply comprehensive NZ brand mappings
  const nzBrandMappings = getNZBrandMappings();

  // Apply brand mappings
  for (const [canonical, variations] of Object.entries(nzBrandMappings)) {
    if (normalized.includes(canonical)) {
      normalized = normalized.replace(canonical, canonical);
      break; // Use first match to avoid multiple replacements
    }

    // Check variations
    for (const variation of variations) {
      if (normalized.includes(variation)) {
        normalized = normalized.replace(variation, canonical);
        break;
      }
    }
  }

  // Include normalized size if provided
  if (size) {
    const normalizedSize = normalizeSize(size);
    if (normalizedSize) {
      normalized += ` ${normalizedSize}`;
    }
  }

  return normalized.trim();
}

// Get comprehensive NZ brand mappings (152 brands with 500+ variations)
function getNZBrandMappings(): Record<string, string[]> {
  return {
    // === SUPERMARKET PRIVATE LABELS ===
    'pams': ['pam', 'pams brand', 'pams select'],
    'essentials': ['essentials brand', 'countdown essentials'],
    'macro': ['macro brand', 'macro organic'],
    'woolworths': ['woolworths brand', 'woolworths select'],
    'countdown': ['countdown brand', 'countdown select'],
    'homebrand': ['home brand', 'homebrand select'],
    'signature': ['signature range', 'signature brand'],
    'value': ['value brand', 'budget'],
    'fresh choice': ['freshchoice', 'fresh choice brand'],

    // === DAIRY BRANDS ===
    'anchor': ['anchor brand', 'anchor dairy', 'anchor milk', 'anchor butter', 'anchor cheese'],
    'mainland': ['mainland cheese', 'mainland dairy', 'mainland brand'],
    'meadowfresh': ['meadow fresh', 'meadowfresh milk', 'meadow fresh milk'],
    'lewis road': ['lewis road creamery', 'lewis road milk', 'lewis road butter'],
    'kapiti': ['kapiti cheese', 'kapiti ice cream', 'kapiti brand'],
    'fernleaf': ['fernleaf milk', 'fernleaf powder'],
    'tararua': ['tararua cheese', 'tararua dairy'],
    'rolling meadow': ['rolling meadow cheese', 'rolling meadow dairy'],
    'whitestone': ['whitestone cheese', 'whitestone dairy'],
    'mercer': ['mercer cheese', 'mercer dairy'],
    'epicure': ['epicure cheese', 'epicure dairy'],
    'kikorangi': ['kikorangi cheese', 'kikorangi blue'],

    // === MEAT BRANDS ===
    'tegel': ['tegel chicken', 'tegel poultry', 'tegel brand'],
    'inghams': ['ingham', 'inghams chicken', 'inghams poultry'],
    'turks': ['turks poultry', 'turks chicken'],
    'brinks': ['brinks chicken', 'brinks poultry'],
    'hellers': ['heller', 'hellers bacon', 'hellers sausages', 'hellers smallgoods'],
    'beehive': ['beehive bacon', 'beehive ham', 'beehive smallgoods'],
    'farmland': ['farmland bacon', 'farmland ham'],
    'primo': ['primo smallgoods', 'primo bacon'],
    'hans': ['hans smallgoods', 'hans continental'],
    'continental deli': ['continental smallgoods', 'continental deli'],

    // === BREAD & BAKERY BRANDS ===
    'tip top': ['tiptop', 'tip top bread', 'tiptop bread'],
    'molenberg': ['molenberg bread', 'molenburg', 'molenberg wholemeal'],
    'vogels': ['vogel', 'vogels bread', 'vogel bread', 'vogels original'],
    'freyas': ['freya', 'freyas bread', 'freya bread'],
    'natures fresh': ['nature fresh', 'natures fresh bread'],
    'burgen': ['burgen bread', 'burgen soy lin'],
    'ploughmans': ['ploughman', 'ploughmans bread'],
    'golden': ['golden bread', 'golden bakery'],
    'bakers delight': ['bakersdelight', 'bakers delight bread'],

    // === BEVERAGE BRANDS ===
    'coke': ['coca cola', 'coca-cola', 'coke classic', 'coca cola classic'],
    'coke zero': ['coca cola zero', 'coke zero sugar', 'coca cola zero sugar'],
    'diet coke': ['coca cola diet', 'diet coca cola'],
    'pepsi': ['pepsi cola', 'pepsi classic', 'pepsi original'],
    'pepsi max': ['pepsi maximum taste', 'pepsi max no sugar'],
    'fanta': ['fanta orange', 'fanta grape'],
    'sprite': ['sprite lemon', 'sprite lime'],
    'l&p': ['lemon paeroa', 'lemon and paeroa', 'l and p'],
    'just juice': ['justjuice', 'just juice brand'],
    'fresh up': ['freshup', 'fresh up juice'],
    'keri': ['keri juice', 'keri fresh'],
    'charlies': ['charlie', 'charlies juice', 'charlies honest'],
    'phoenix': ['phoenix organic', 'phoenix juice'],
    'pump': ['pump water', 'pump brand'],
    'powerade': ['powerade sports drink'],
    'gatorade': ['gatorade sports drink'],

    // === CEREAL & BREAKFAST BRANDS ===
    'sanitarium': ['sanitarium weetbix', 'sanitarium so good'],
    'weetbix': ['weet bix', 'wheat biscuits', 'sanitarium weetbix'],
    'uncle tobys': ['uncle toby', 'uncle tobys oats', 'uncle tobys muesli'],
    'kelloggs': ['kellogg', 'kelloggs cornflakes', 'kelloggs special k'],
    'cornflakes': ['corn flakes', 'kelloggs cornflakes'],
    'nutrigrain': ['nutri grain', 'kelloggs nutrigrain'],
    'special k': ['specialk', 'kelloggs special k'],
    'hubbards': ['hubbards cereal', 'hubbards muesli'],

    // === SNACK & CONFECTIONERY BRANDS ===
    'cadbury': ['cadburys', 'cadbury chocolate'],
    'whittakers': ['whittaker', 'whittakers chocolate'],
    'nestle': ['nestlé', 'nestle chocolate'],
    'mars': ['mars bar', 'mars chocolate'],
    'snickers': ['snickers bar', 'snickers chocolate'],
    'kit kat': ['kitkat', 'kit-kat'],
    'twix': ['twix bar', 'twix chocolate'],
    'moro': ['moro bar', 'moro chocolate'],
    'picnic': ['picnic bar', 'picnic chocolate'],
    'crunchie': ['crunchie bar', 'crunchie chocolate'],
    'bluebird': ['bluebird chips', 'bluebird snacks'],
    'eta': ['eta chips', 'eta snacks', 'eta peanut butter'],
    'proper': ['proper chips', 'proper crisps'],
    'heartland': ['heartland chips', 'heartland snacks'],
    'griffins': ['griffin', 'griffins biscuits', 'griffin biscuits'],
    'arnott': ['arnotts', 'arnott biscuits', 'arnotts biscuits'],
    'toffee pops': ['toffeepops', 'griffins toffee pops'],
    'mallowpuffs': ['mallow puffs', 'griffins mallowpuffs'],
    'pics': ['pic', 'pic peanut butter', 'pics peanut butter'],
    'cottees': ['cottee', 'cottees jam', 'cottee jam'],

    // === PANTRY & COOKING BRANDS ===
    'watties': ['wattie', 'watties tomatoes', 'watties sauce'],
    'heinz': ['heinz beans', 'heinz tomato sauce', 'heinz soup'],
    'maggi': ['maggi noodles', 'maggi soup', 'maggi instant'],
    'continental soup': ['continental soup', 'continental pasta'],
    'mccains': ['mccain', 'mccains chips', 'mccains frozen'],
    'edgell': ['edgell vegetables', 'edgell canned'],
    'greggs': ['greggs coffee', 'greggs instant'],
    'nescafe': ['nescafé', 'nescafe coffee'],
    'moccona': ['moccona coffee', 'moccona instant'],
    'robert harris': ['robertharris', 'robert harris coffee'],
    'bell tea': ['bell tea bags', 'bell black tea'],
    'dilmah': ['dilmah tea', 'dilmah ceylon'],
    'olivani': ['olivani oil', 'olivani olive oil'],
    'bertolli': ['bertolli oil', 'bertolli olive oil'],
    'praise': ['praise mayonnaise', 'praise dressing'],
    'best foods': ['bestfoods', 'best foods mayo'],
    'masterfoods': ['master foods', 'masterfoods sauce'],
    'fountain': ['fountain sauce', 'fountain tomato sauce'],

    // === FROZEN FOOD BRANDS ===
    'birds eye': ['birdseye', 'birds eye vegetables', 'birds eye fish'],
    'talley': ['talleys', 'talley vegetables', 'talley frozen'],

    // === CLEANING & HOUSEHOLD BRANDS ===
    'janola': ['janola bleach', 'janola cleaning'],
    'earthwise': ['earthwise cleaning', 'earthwise eco'],
    'finish': ['finish dishwasher', 'finish tablets'],
    'ajax': ['ajax spray', 'ajax cleaning'],
    'jif': ['jif cream cleanser', 'jif bathroom'],
    'domestos': ['domestos bleach', 'domestos toilet'],
    'toilet duck': ['toiletduck', 'toilet duck cleaner'],
    'mr muscle': ['mrmuscle', 'mr muscle bathroom'],
    'windex': ['windex glass', 'windex cleaner'],
    'persil': ['persil washing powder', 'persil liquid'],
    'surf': ['surf washing powder', 'surf liquid'],
    'omo': ['omo washing powder', 'omo liquid'],
    'cold power': ['coldpower', 'cold power liquid'],
    'dynamo': ['dynamo washing liquid'],
    'sorbent': ['sorbent toilet paper', 'sorbent tissues'],
    'kleenex': ['kleenex tissues', 'kleenex toilet paper'],
    'quilton': ['quilton toilet paper', 'quilton tissues'],
    'treasures': ['treasures toilet paper', 'treasures tissues'],

    // === HEALTH & BEAUTY BRANDS ===
    'colgate': ['colgate toothpaste', 'colgate toothbrush'],
    'oral b': ['oral-b', 'oralb', 'oral b toothbrush'],
    'sensodyne': ['sensodyne toothpaste'],
    'macleans': ['macleans toothpaste'],
    'head shoulders': ['head and shoulders', 'head & shoulders'],
    'pantene': ['pantene shampoo', 'pantene conditioner'],
    'herbal essences': ['herbal essence', 'herbal essences shampoo'],
    'dove': ['dove soap', 'dove body wash'],
    'nivea': ['nivea cream', 'nivea body'],
    'vaseline': ['vaseline petroleum jelly'],

    // === BABY & PERSONAL CARE ===
    'huggies': ['huggies nappies', 'huggies diapers'],
    'pampers': ['pampers nappies', 'pampers diapers'],
    'johnson': ['johnsons', 'johnson baby', 'johnsons baby'],
    'bepanthen': ['bepanthen cream', 'bepanthen nappy'],

    // === PET FOOD BRANDS ===
    'pedigree': ['pedigree dog food', 'pedigree dry'],
    'whiskas': ['whiskas cat food', 'whiskas wet'],
    'fancy feast': ['fancyfeast', 'fancy feast cat'],
    'royal canin': ['royalcanin', 'royal canin dog'],
    'hills': ['hills pet food', 'hills science diet'],
    'eukanuba': ['eukanuba dog food'],
    'iams': ['iams pet food', 'iams dog'],
    'optimum': ['optimum dog food', 'optimum pet'],
    'tux': ['tux cat food', 'tux pet'],
    'champ': ['champ dog food', 'champ pet'],

    // === ADDITIONAL NZ SPECIFIC BRANDS ===
    'tip top ice cream': ['tiptop ice cream', 'tip top icecream'],
    'new zealand natural': ['nz natural', 'new zealand natural ice cream'],
    'deep south': ['deep south ice cream', 'deep south icecream'],
    'barkers': ['barkers jam', 'barkers preserves'],
    'san remo': ['sanremo', 'san remo pasta'],
    'latina': ['latina pasta', 'latina fresh'],
    'uncle bens': ['uncle ben', 'uncle bens rice'],
    'sunrice': ['sun rice', 'sunrice brand'],
    'campbells': ['campbell', 'campbells soup', 'campbell soup'],
    'delmaine': ['delmaine vegetables', 'delmaine canned'],
    'john west': ['johnwest', 'john west tuna', 'johnwest tuna'],
    'sirena': ['sirena tuna', 'sirena seafood'],
    'edmonds': ['edmonds flour', 'edmonds baking'],
    'champion': ['champion flour', 'champion baking'],
    'chelsea': ['chelsea sugar', 'chelsea baking']
  };
}

// Calculate Levenshtein distance for string similarity
function levenshteinDistance(s1: string, s2: string): number {
  if (!s1) return s2 ? s2.length : 0;
  if (!s2) return s1.length;

  const matrix: number[][] = [];

  for (let i = 0; i <= s2.length; i++) {
    matrix[i] = [i];
  }

  for (let j = 0; j <= s1.length; j++) {
    matrix[0][j] = j;
  }

  for (let i = 1; i <= s2.length; i++) {
    for (let j = 1; j <= s1.length; j++) {
      const cost = s1[j - 1] === s2[i - 1] ? 0 : 1;
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,
        matrix[i][j - 1] + 1,
        matrix[i - 1][j - 1] + cost
      );
    }
  }

  return matrix[s2.length][s1.length];
}

// Calculate Jaccard similarity between two strings based on word sets
function jaccardSimilarity(str1: string, str2: string): number {
  const set1 = new Set(str1.toLowerCase().split(/\s+/).filter(w => w.length > 1));
  const set2 = new Set(str2.toLowerCase().split(/\s+/).filter(w => w.length > 1));

  const intersection = new Set([...set1].filter(x => set2.has(x)));
  const union = new Set([...set1, ...set2]);

  return union.size === 0 ? 0 : intersection.size / union.size;
}

// Enhanced similarity calculation combining multiple algorithms (matches .NET implementation)
function calculateSimilarity(name1: string, name2: string, brand1?: string, brand2?: string): number {
  if (!name1 || !name2) return 0;
  if (name1 === name2) return 1.0;

  const normalized1 = normalizeProductName(name1, undefined);
  const normalized2 = normalizeProductName(name2, undefined);

  if (normalized1 === normalized2) return 1.0;

  // Check for manual mapping match first
  const nzBrandMappings = getNZBrandMappings();
  let mapping1 = null;
  let mapping2 = null;

  for (const [canonical, variations] of Object.entries(nzBrandMappings)) {
    if (normalized1.includes(canonical) || variations.some(v => normalized1.includes(v))) {
      mapping1 = canonical;
    }
    if (normalized2.includes(canonical) || variations.some(v => normalized2.includes(v))) {
      mapping2 = canonical;
    }
  }

  if (mapping1 && mapping2 && mapping1 === mapping2) return 0.95;

  // Calculate Levenshtein similarity (60% weight)
  const distance = levenshteinDistance(normalized1, normalized2);
  const maxLength = Math.max(normalized1.length, normalized2.length);
  const levenshteinSim = maxLength === 0 ? 1 : Math.max(0, 1 - distance / maxLength);

  // Calculate Jaccard similarity (40% weight)
  const jaccardSim = jaccardSimilarity(normalized1, normalized2);

  // Weighted combination: 60% Levenshtein, 40% Jaccard
  let combinedSimilarity = (levenshteinSim * 0.6) + (jaccardSim * 0.4);

  // Boost score for exact word matches
  const words1 = normalized1.split(' ').filter(w => w.length > 2);
  const words2 = normalized2.split(' ').filter(w => w.length > 2);
  const commonWords = words1.filter(w => words2.includes(w));

  if (commonWords.length > 0) {
    const wordBoost = (commonWords.length / Math.max(words1.length, words2.length)) * 0.2;
    combinedSimilarity = Math.min(1, combinedSimilarity + wordBoost);
  }

  // Penalty for significant length differences
  const lengthRatio = Math.min(normalized1.length, normalized2.length) /
                     Math.max(normalized1.length, normalized2.length);
  if (lengthRatio < 0.5) {
    combinedSimilarity *= 0.8;
  }

  return combinedSimilarity;
}

// Find best matching consolidated product using enhanced algorithm (matches .NET implementation)
async function findBestMatch(normalizedName: string, size: string | undefined, originalName: string): Promise<any> {
  try {
    log(colour.cyan, `🔍 Finding match for: "${originalName}" -> normalized: "${normalizedName}"`);

    // First try exact match with normalized name
    const exactMatch = await consolidatedProductsCollection.findOne({
      normalizedName: normalizedName
    });

    if (exactMatch) {
      log(colour.green, `✅ Exact match found: ${exactMatch.displayName}`);
      return { product: exactMatch, confidence: 1.0, matchType: 'exact' };
    }

    // Check for manual mapping match
    const nzBrandMappings = getNZBrandMappings();
    for (const [canonical, variations] of Object.entries(nzBrandMappings)) {
      if (normalizedName.includes(canonical) || variations.some(v => normalizedName.includes(v))) {
        const manualMatch = await consolidatedProductsCollection.findOne({
          normalizedName: canonical
        });

        if (manualMatch) {
          log(colour.green, `✅ Manual mapping match found: ${manualMatch.displayName}`);
          return { product: manualMatch, confidence: 0.95, matchType: 'manual' };
        }
      }
    }

    // Try fuzzy matching against all products
    const allProducts = await consolidatedProductsCollection.find({}).limit(1000).toArray();

    let bestMatch: any = null;
    let bestScore = 0;
    let matchType = 'fuzzy';
    const threshold = 0.8;

    for (const product of allProducts) {
      // Check against normalized name
      let score = calculateSimilarity(normalizedName, product.normalizedName);

      // Check against aliases if they exist
      if (product.aliases && Array.isArray(product.aliases)) {
        for (const alias of product.aliases) {
          const aliasScore = calculateSimilarity(normalizedName, alias);
          if (aliasScore > score) {
            score = aliasScore;
            matchType = 'alias';
          }
        }
      }

      // Size similarity bonus
      if (size && product.primarySize) {
        const sizeScore = calculateSizeSimilarity(size, product.primarySize);
        if (sizeScore > 0.8) {
          score += 0.05; // Small bonus for size match
        }
      }

      if (score > bestScore && score >= threshold) {
        bestScore = score;
        bestMatch = product;
      }
    }

    if (bestMatch) {
      log(colour.yellow, `✅ Fuzzy match found: ${bestMatch.displayName} (confidence: ${bestScore.toFixed(3)}, type: ${matchType})`);
      return {
        product: bestMatch,
        confidence: Math.min(1.0, bestScore),
        matchType: matchType,
        existingConfidence: bestMatch.matchConfidence
      };
    }

    log(colour.red, `❌ No match found for: ${originalName}`);
    return null;
  } catch (error: any) {
    logError(`❌ Error finding match: ${error.message}`);
    return null;
  }
}

// Add alias to consolidated product (matches .NET implementation)
async function addProductAlias(consolidatedProductId: any, newAlias: string): Promise<void> {
  try {
    const normalizedAlias = normalizeProductName(newAlias);
    if (!normalizedAlias || normalizedAlias.trim() === '') return;

    log(colour.cyan, `📝 Attempting to add alias: "${newAlias}" -> normalized: "${normalizedAlias}"`);

    const product = await consolidatedProductsCollection.findOne({ _id: consolidatedProductId });
    if (!product) {
      log(colour.red, `❌ Product not found for alias addition: ${consolidatedProductId}`);
      return;
    }

    // Get current aliases or initialize empty array
    const currentAliases = product.aliases || [];

    // Don't add if alias already exists or is the same as normalized name
    if (normalizedAlias === product.normalizedName) {
      log(colour.yellow, `⚠️ Alias matches normalized name, skipping: ${normalizedAlias}`);
      return;
    }

    if (currentAliases.includes(normalizedAlias)) {
      log(colour.yellow, `⚠️ Alias already exists, skipping: ${normalizedAlias}`);
      return;
    }

    // Add new alias
    currentAliases.push(normalizedAlias);

    await consolidatedProductsCollection.updateOne(
      { _id: consolidatedProductId },
      {
        $set: {
          aliases: currentAliases,
          updatedAt: new Date()
        }
      }
    );

    log(colour.cyan, `✅ Added alias '${normalizedAlias}' to product: ${product.displayName}`);
  } catch (error: any) {
    logError(`❌ Error adding alias: ${error.message}`);
  }
}

// Get image stream from GridFS
export async function getImageFromGridFS(fileId: string): Promise<NodeJS.ReadableStream | null> {
  if (!gridFS) return null;

  try {
    const objectId = new ObjectId(fileId);
    return gridFS.openDownloadStream(objectId);
  } catch (error: any) {
    logError(`Failed to get image from GridFS: ${error.message}`);
    return null;
  }
}

// Close MongoDB connection
export async function closeMongoDB() {
  if (client) {
    await client.close();
    log(colour.blue, "MongoDB connection closed");
  }
}

// Health check
export async function mongoHealthCheck(): Promise<boolean> {
  try {
    await db.admin().ping();
    return true;
  } catch (error) {
    return false;
  }
}