/**
 * Example Product Card Component
 * Shows how to display consolidated product data with multiple store prices
 */

import React, { useState } from 'react';

const ProductCard = ({ product }) => {
    const [selectedSize, setSelectedSize] = useState(product.primary_size);
    
    // Parse store prices from the product data
    const storePrices = product.store_prices || [];
    
    // Find lowest price
    const lowestPrice = storePrices.reduce((min, store) => 
        store.price < min ? store.price : min, 
        storePrices[0]?.price || 0
    );
    
    // Group prices by availability
    const availablePrices = storePrices.filter(store => store.is_available);
    const unavailableStores = storePrices.filter(store => !store.is_available);
    
    return (
        <div className="product-card">
            {/* Product Header */}
            <div className="product-header">
                <div className="product-image">
                    {/* Product image would go here */}
                    <div className="image-placeholder">📦</div>
                </div>
                
                <div className="product-info">
                    <h3 className="product-name">{product.display_name}</h3>
                    <div className="product-meta">
                        <span className="category">{product.main_category_name}</span>
                        {product.brand_name && (
                            <span className="brand">{product.brand_name}</span>
                        )}
                    </div>
                </div>
            </div>
            
            {/* Size Selection */}
            {product.available_sizes && product.available_sizes.length > 1 && (
                <div className="size-selector">
                    <label>Size:</label>
                    <select 
                        value={selectedSize} 
                        onChange={(e) => setSelectedSize(e.target.value)}
                        className="size-dropdown"
                    >
                        {product.available_sizes.map(size => (
                            <option key={size} value={size}>{size}</option>
                        ))}
                    </select>
                </div>
            )}
            
            {/* Price Comparison Section */}
            <div className="price-section">
                <div className="price-header">
                    <span className="lowest-price">From ${lowestPrice.toFixed(2)}</span>
                    <span className="store-count">{availablePrices.length} stores</span>
                </div>
                
                {/* Store Prices Grid */}
                <div className="store-prices">
                    {availablePrices.map((store, index) => (
                        <div 
                            key={store.store_name} 
                            className={`store-price ${store.price === lowestPrice ? 'best-price' : ''}`}
                        >
                            <div className="store-info">
                                <span className="store-name">{store.store_name}</span>
                                <span className="store-logo">
                                    {getStoreLogo(store.store_name)}
                                </span>
                            </div>
                            <div className="price-info">
                                <span className="price">${store.price.toFixed(2)}</span>
                                {store.price === lowestPrice && (
                                    <span className="best-badge">Best Price!</span>
                                )}
                                <span className="last-updated">
                                    Updated {formatLastUpdated(store.recorded_at)}
                                </span>
                            </div>
                        </div>
                    ))}
                </div>
                
                {/* Unavailable Stores */}
                {unavailableStores.length > 0 && (
                    <div className="unavailable-stores">
                        <details>
                            <summary>Not available in {unavailableStores.length} stores</summary>
                            <div className="unavailable-list">
                                {unavailableStores.map(store => (
                                    <span key={store.store_name} className="unavailable-store">
                                        {store.store_name}
                                    </span>
                                ))}
                            </div>
                        </details>
                    </div>
                )}
            </div>
            
            {/* Action Buttons */}
            <div className="product-actions">
                <button className="btn-primary" onClick={() => handleAddToList(product)}>
                    Add to Shopping List
                </button>
                <button className="btn-secondary" onClick={() => handleViewHistory(product)}>
                    Price History
                </button>
            </div>
        </div>
    );
};

// Helper functions
const getStoreLogo = (storeName) => {
    const logos = {
        'woolworths': '🟢',
        'newworld': '🔴', 
        'paknsave': '🟡'
    };
    return logos[storeName.toLowerCase()] || '🏪';
};

const formatLastUpdated = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffHours = Math.floor((now - date) / (1000 * 60 * 60));
    
    if (diffHours < 1) return 'Just now';
    if (diffHours < 24) return `${diffHours}h ago`;
    return `${Math.floor(diffHours / 24)}d ago`;
};

const handleAddToList = (product) => {
    // Implementation for adding to shopping list
    console.log('Adding to list:', product.display_name);
};

const handleViewHistory = (product) => {
    // Implementation for viewing price history
    console.log('Viewing history for:', product.display_name);
};

// CSS styles (would typically be in a separate file)
const styles = `
.product-card {
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 16px;
    margin: 8px;
    background: white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.2s, box-shadow 0.2s;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.product-header {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.image-placeholder {
    width: 80px;
    height: 80px;
    background: #f5f5f5;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.product-name {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.product-meta {
    display: flex;
    gap: 12px;
    font-size: 14px;
    color: #666;
}

.category {
    background: #e3f2fd;
    padding: 4px 8px;
    border-radius: 4px;
}

.brand {
    background: #f3e5f5;
    padding: 4px 8px;
    border-radius: 4px;
}

.size-selector {
    margin-bottom: 16px;
}

.size-dropdown {
    margin-left: 8px;
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.price-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.lowest-price {
    font-size: 24px;
    font-weight: bold;
    color: #2e7d32;
}

.store-count {
    color: #666;
    font-size: 14px;
}

.store-prices {
    display: grid;
    gap: 8px;
    margin-bottom: 12px;
}

.store-price {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fafafa;
}

.store-price.best-price {
    border-color: #4caf50;
    background: #e8f5e8;
}

.store-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.store-name {
    font-weight: 500;
    text-transform: capitalize;
}

.price-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 2px;
}

.price {
    font-size: 18px;
    font-weight: bold;
}

.best-badge {
    font-size: 12px;
    color: #4caf50;
    font-weight: 600;
}

.last-updated {
    font-size: 11px;
    color: #999;
}

.unavailable-stores {
    margin-top: 8px;
    font-size: 14px;
    color: #666;
}

.unavailable-list {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-top: 4px;
}

.unavailable-store {
    background: #ffebee;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
}

.product-actions {
    display: flex;
    gap: 8px;
    margin-top: 16px;
}

.btn-primary, .btn-secondary {
    flex: 1;
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn-primary {
    background: #1976d2;
    color: white;
}

.btn-primary:hover {
    background: #1565c0;
}

.btn-secondary {
    background: #e0e0e0;
    color: #333;
}

.btn-secondary:hover {
    background: #d5d5d5;
}
`;

export default ProductCard;