using Npgsql;
using Dapper;
using Microsoft.Extensions.Configuration;
using static Scraper.Utilities;

namespace Scraper
{
    public static class SupabaseDB
    {
        private static string? _connString;
        private static int? _storeId;

        public static void Initialise(IConfiguration config)
        {
            _connString = config.GetSection("POSTGRES_CONNECTION").Get<string>();
            if (string.IsNullOrWhiteSpace(_connString))
                throw new Exception("POSTGRES_CONNECTION not configured");
        }

        private static async Task<NpgsqlConnection> GetConn()
        {
            if (_connString == null) throw new Exception("SupabaseDB not initialised");
            var c = new NpgsqlConnection(_connString);
            await c.OpenAsync();
            return c;
        }

        public enum UpsertResponse { NewProduct, PriceUpdated, InfoUpdated, AlreadyUpToDate, Failed }

        public static async Task<int> EnsureStoreRow(string name)
        {
            if (_storeId != null) return _storeId.Value;
            using var conn = await GetConn();
            var id = await conn.QueryFirstOrDefaultAsync<int?>("select id from stores where name=@n", new { n = name });
            if (id == null)
                id = await conn.ExecuteScalarAsync<int>("insert into stores(name) values(@n) returning id", new { n = name });
            _storeId = id;
            return id.Value;
        }

        public static async Task<UpsertResponse> UpsertProduct(Program.Product p, int storeId)
        {
            try
            {
                using var conn = await GetConn();
                var inserted = await conn.ExecuteScalarAsync<bool>(@"insert into products(id,name,size,unit_price,unit_name,original_unit_qty,source_site,last_updated,last_checked)
                    values(@id,@name,@size,@uprice,@uname,@oqty,@site,@lu,@lc)
                    on conflict(id) do update set name=excluded.name, size=excluded.size, unit_price=excluded.unit_price, unit_name=excluded.unit_name, original_unit_qty=excluded.original_unit_qty, source_site=excluded.source_site, last_checked=excluded.last_checked
                    returning xmax=0 as inserted", new { id=p.id, name=p.name, size=p.size, uprice=p.unitPrice, uname=p.unitName, oqty=p.originalUnitQuantity, site=p.sourceSite, lu=p.lastUpdated, lc=p.lastChecked });

                await conn.ExecuteAsync("insert into prices(product_id,store_id,price,recorded_at) values(@pid,@sid,@price,@ts)", new { pid=p.id, sid=storeId, price=p.currentPrice, ts=p.lastUpdated });
                return inserted ? UpsertResponse.NewProduct : UpsertResponse.PriceUpdated;
            }
            catch (Exception e)
            {
                LogError("Upsert failed: "+e.Message);
                return UpsertResponse.Failed;
            }
        }
    }
} 