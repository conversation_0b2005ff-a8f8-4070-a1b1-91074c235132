/**
 * API Endpoints for Consolidated Products
 * This provides REST API endpoints for your frontend to consume consolidated product data
 */

import { createClient } from '@supabase/supabase-js';
import express from 'express';
import cors from 'cors';

const app = express();
app.use(cors());
app.use(express.json());

// Initialize Supabase client
const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_ANON_KEY
);

/**
 * GET /api/products
 * Search and filter consolidated products
 * Query params: search, category, limit, offset
 */
app.get('/api/products', async (req, res) => {
    try {
        const { search, category, limit = 50, offset = 0 } = req.query;
        
        let query = supabase
            .from('consolidated_product_details')
            .select('*')
            .range(offset, offset + limit - 1)
            .order('display_name');
            
        if (search) {
            query = query.textSearch('display_name', search);
        }
        
        if (category) {
            query = query.eq('main_category_name', category);
        }
        
        const { data, error, count } = await query;
        
        if (error) {
            return res.status(500).json({ error: error.message });
        }
        
        res.json({
            products: data || [],
            total: count,
            limit: parseInt(limit),
            offset: parseInt(offset)
        });
        
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

/**
 * GET /api/products/:id
 * Get detailed information about a specific consolidated product
 */
app.get('/api/products/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        // Get main product details
        const { data: product, error } = await supabase
            .from('consolidated_product_details')
            .select('*')
            .eq('id', id)
            .single();
            
        if (error || !product) {
            return res.status(404).json({ error: 'Product not found' });
        }
        
        // Get price history for the last 30 days
        const { data: priceHistory } = await supabase
            .from('consolidated_prices')
            .select(`
                price,
                recorded_at,
                is_special,
                stores(name)
            `)
            .eq('consolidated_product_id', id)
            .gte('recorded_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
            .order('recorded_at', { ascending: false });
            
        // Get all size variants
        const { data: sizeVariants } = await supabase
            .from('product_size_variants')
            .select('*')
            .eq('consolidated_product_id', id)
            .order('size_weight_grams', { ascending: true, nullsLast: true });
            
        res.json({
            product,
            priceHistory: priceHistory || [],
            sizeVariants: sizeVariants || []
        });
        
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

/**
 * GET /api/categories
 * Get all available categories
 */
app.get('/api/categories', async (req, res) => {
    try {
        const { data, error } = await supabase
            .from('category_hierarchy')
            .select('*')
            .order('level, sort_order');
            
        if (error) {
            return res.status(500).json({ error: error.message });
        }
        
        // Organize into hierarchical structure
        const categories = [];
        const categoriesMap = new Map();
        
        data.forEach(cat => {
            categoriesMap.set(cat.id, { ...cat, children: [] });
        });
        
        data.forEach(cat => {
            if (cat.parent_id) {
                const parent = categoriesMap.get(cat.parent_id);
                if (parent) {
                    parent.children.push(categoriesMap.get(cat.id));
                }
            } else {
                categories.push(categoriesMap.get(cat.id));
            }
        });
        
        res.json({ categories });
        
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

/**
 * GET /api/stores
 * Get all available stores
 */
app.get('/api/stores', async (req, res) => {
    try {
        const { data, error } = await supabase
            .from('stores')
            .select('*')
            .order('name');
            
        if (error) {
            return res.status(500).json({ error: error.message });
        }
        
        res.json({ stores: data || [] });
        
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

/**
 * GET /api/products/:id/compare
 * Compare prices across stores for a specific product
 */
app.get('/api/products/:id/compare', async (req, res) => {
    try {
        const { id } = req.params;
        
        // Get latest prices from all stores
        const { data, error } = await supabase
            .rpc('get_latest_prices_by_product', { product_id: id });
            
        if (error) {
            return res.status(500).json({ error: error.message });
        }
        
        res.json({ comparison: data || [] });
        
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

/**
 * GET /api/deals
 * Get products with special deals/discounts
 */
app.get('/api/deals', async (req, res) => {
    try {
        const { limit = 50, store } = req.query;
        
        let query = supabase
            .from('consolidated_prices')
            .select(`
                consolidated_product_id,
                price,
                recorded_at,
                stores(name),
                consolidated_products(display_name, primary_size)
            `)
            .eq('is_special', true)
            .order('recorded_at', { ascending: false })
            .limit(limit);
            
        if (store) {
            const { data: storeData } = await supabase
                .from('stores')
                .select('id')
                .eq('name', store)
                .single();
                
            if (storeData) {
                query = query.eq('store_id', storeData.id);
            }
        }
        
        const { data, error } = await query;
        
        if (error) {
            return res.status(500).json({ error: error.message });
        }
        
        res.json({ deals: data || [] });
        
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Create PostgreSQL function for getting latest prices (to be run as migration)
const createLatestPricesFunction = `
CREATE OR REPLACE FUNCTION get_latest_prices_by_product(product_id uuid)
RETURNS TABLE (
    store_name text,
    price numeric,
    recorded_at timestamptz,
    is_special boolean
) AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT ON (s.name)
        s.name,
        cp.price,
        cp.recorded_at,
        cp.is_special
    FROM consolidated_prices cp
    JOIN stores s ON cp.store_id = s.id
    WHERE cp.consolidated_product_id = product_id
    ORDER BY s.name, cp.recorded_at DESC;
END;
$$ LANGUAGE plpgsql;
`;

const port = process.env.PORT || 3001;
app.listen(port, () => {
    console.log(`Consolidated Products API running on port ${port}`);
    console.log('Available endpoints:');
    console.log('  GET /api/products - Search products');
    console.log('  GET /api/products/:id - Get product details');
    console.log('  GET /api/products/:id/compare - Compare prices');
    console.log('  GET /api/categories - Get categories');
    console.log('  GET /api/stores - Get stores');
    console.log('  GET /api/deals - Get special deals');
    console.log('\nMake sure to run this SQL function in your database:');
    console.log(createLatestPricesFunction);
});

export default app;