"use strict";
/**
 * Consolidated Products Integration for Woolworths Scraper
 * This module integrates the product matching algorithm with the Woolworths scraper
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeConsolidatedProducts = initializeConsolidatedProducts;
exports.ensureWoolworthsStoreId = ensureWoolworthsStoreId;
exports.processConsolidatedProduct = processConsolidatedProduct;
exports.getConsolidatedProductWithPrices = getConsolidatedProductWithPrices;
exports.searchConsolidatedProducts = searchConsolidatedProducts;
const supabase_js_1 = require("@supabase/supabase-js");
const ProductMatcher = __importStar(require("../../shared/product-matcher.js"));
let supabase;
let woolworthsStoreId;
function initializeConsolidatedProducts(supabaseUrl, supabaseKey) {
    supabase = (0, supabase_js_1.createClient)(supabaseUrl, supabaseKey, { auth: { persistSession: false } });
    ProductMatcher.initializeSupabase(supabaseUrl, supabaseKey);
}
function ensureWoolworthsStoreId() {
    return __awaiter(this, void 0, void 0, function* () {
        if (woolworthsStoreId)
            return woolworthsStoreId;
        const { data, error } = yield supabase
            .from("stores")
            .select("id")
            .eq("name", "woolworths")
            .single();
        if (error && error.code !== "PGRST116") {
            throw error;
        }
        if (data === null || data === void 0 ? void 0 : data.id) {
            woolworthsStoreId = data.id;
            return woolworthsStoreId;
        }
        // Insert store row if not exists
        const { data: insertData, error: insertErr } = yield supabase
            .from("stores")
            .insert({ name: "woolworths" })
            .select("id")
            .single();
        if (insertErr)
            throw insertErr;
        woolworthsStoreId = insertData.id;
        return woolworthsStoreId;
    });
}
/**
 * Process a scraped Woolworths product and integrate with consolidated products
 */
function processConsolidatedProduct(scrapedProduct) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            // Ensure we have the Woolworths store ID
            const storeId = yield ensureWoolworthsStoreId();
            // Convert scraped product to format expected by matcher
            const productForMatching = {
                id: scrapedProduct.id,
                name: scrapedProduct.name,
                size: scrapedProduct.size,
                category: scrapedProduct.category,
                brand: extractBrandFromName(scrapedProduct.name), // Extract brand if possible
                unitPrice: scrapedProduct.unitPrice,
                unitName: scrapedProduct.unitName
            };
            // Find or create consolidated product
            const consolidatedProductId = yield ProductMatcher.consolidateProduct(productForMatching, storeId);
            if (consolidatedProductId) {
                // Store the current price
                yield ProductMatcher.storeConsolidatedPrice(consolidatedProductId, storeId, scrapedProduct.currentPrice, false // isSpecial - could be determined from product data
                );
                // Handle multiple sizes if applicable
                yield handleProductSizeVariants(consolidatedProductId, scrapedProduct);
            }
            return consolidatedProductId;
        }
        catch (error) {
            console.error(`Error processing consolidated product for ${scrapedProduct.name}:`, error);
            return null;
        }
    });
}
/**
 * Extract brand name from product name using common patterns
 */
function extractBrandFromName(productName) {
    // Common brand patterns in Woolworths products
    const brandPatterns = [
        /^([A-Z][a-z]+(?:\s+[A-Z][a-z]*)*)\s+/, // Capitalize Case at start
        /\b(Select|Countdown|Essentials|Free Range|Signature)\b/i // Store brands
    ];
    for (const pattern of brandPatterns) {
        const match = productName.match(pattern);
        if (match) {
            return match[1].trim();
        }
    }
    return undefined;
}
/**
 * Handle different size variants of the same product
 */
function handleProductSizeVariants(consolidatedProductId, product) {
    return __awaiter(this, void 0, void 0, function* () {
        if (!product.size)
            return;
        try {
            // Check if this size variant already exists
            const { data: existingVariant } = yield supabase
                .from('product_size_variants')
                .select('id')
                .eq('consolidated_product_id', consolidatedProductId)
                .eq('size_name', product.size)
                .single();
            if (existingVariant)
                return; // Size variant already exists
            // Calculate normalized weight/volume for comparison
            const weightGrams = calculateWeightInGrams(product.size);
            const volumeML = calculateVolumeInML(product.size);
            // Insert new size variant
            yield supabase
                .from('product_size_variants')
                .insert({
                consolidated_product_id: consolidatedProductId,
                size_name: product.size,
                size_weight_grams: weightGrams,
                size_volume_ml: volumeML,
                is_primary_size: false // Could be determined by most common size
            });
        }
        catch (error) {
            console.error(`Error handling size variants for ${product.name}:`, error);
        }
    });
}
/**
 * Convert size string to weight in grams for comparison
 */
function calculateWeightInGrams(size) {
    if (!size)
        return null;
    const sizeNormalized = size.toLowerCase();
    // Match patterns like "500g", "1.5kg", "2kg"
    const gramMatch = sizeNormalized.match(/(\d+(?:\.\d+)?)\s*g\b/);
    if (gramMatch) {
        return parseInt(gramMatch[1]);
    }
    const kgMatch = sizeNormalized.match(/(\d+(?:\.\d+)?)\s*kg\b/);
    if (kgMatch) {
        return Math.round(parseFloat(kgMatch[1]) * 1000);
    }
    return null;
}
/**
 * Convert size string to volume in ML for comparison
 */
function calculateVolumeInML(size) {
    if (!size)
        return null;
    const sizeNormalized = size.toLowerCase();
    // Match patterns like "500ml", "1.5l", "2l"
    const mlMatch = sizeNormalized.match(/(\d+(?:\.\d+)?)\s*ml\b/);
    if (mlMatch) {
        return parseInt(mlMatch[1]);
    }
    const lMatch = sizeNormalized.match(/(\d+(?:\.\d+)?)\s*l\b/);
    if (lMatch) {
        return Math.round(parseFloat(lMatch[1]) * 1000);
    }
    return null;
}
/**
 * Get consolidated product details with all store prices
 */
function getConsolidatedProductWithPrices(consolidatedProductId) {
    return __awaiter(this, void 0, void 0, function* () {
        const { data, error } = yield supabase
            .from('consolidated_product_details')
            .select('*')
            .eq('id', consolidatedProductId)
            .single();
        if (error) {
            console.error('Error fetching consolidated product details:', error);
            return null;
        }
        return data;
    });
}
/**
 * Search consolidated products for the frontend
 */
function searchConsolidatedProducts(searchTerm_1, categoryId_1) {
    return __awaiter(this, arguments, void 0, function* (searchTerm, categoryId, limit = 50) {
        let query = supabase
            .from('consolidated_product_details')
            .select('*')
            .limit(limit);
        if (searchTerm) {
            query = query.textSearch('display_name', searchTerm);
        }
        if (categoryId) {
            query = query.eq('category_id', categoryId);
        }
        const { data, error } = yield query;
        if (error) {
            console.error('Error searching consolidated products:', error);
            return [];
        }
        return data || [];
    });
}
