<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <RootNamespace>pak_scraper</RootNamespace>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <ApplicationIcon>icon.ico</ApplicationIcon>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="icon.ico" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Dapper" Version="2.1.45" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Microsoft.Playwright" Version="1.47.0" />
    <PackageReference Include="MongoDB.Driver" Version="2.23.1" />
    <PackageReference Include="MongoDB.Driver.GridFS" Version="2.23.1" />
    <PackageReference Include="Npgsql" Version="7.0.5" />
    <PackageReference Include="PlaywrightExtraSharp2" Version="*******" />
  </ItemGroup>

  <ItemGroup>
    <None Update="ProductOverrides.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Urls.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
