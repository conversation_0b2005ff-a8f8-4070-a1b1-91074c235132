"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
require("jest");
// jest.useFakeTimers();
const cheerio = __importStar(require("cheerio"));
const utilities_1 = require("../src/utilities");
// Sample input
const html = `
  <h3 id="prod123">Product Name fresh fruit</h3>
  <div class="product-meta">
    <p><span class="size">Large</p>
  `;
// Sample product
const juiceProduct = {
    id: '12345',
    name: 'Orange Juice',
    size: '250ml',
    currentPrice: 4,
    lastUpdated: new Date('01-20-2023'),
    lastChecked: new Date('01-20-2023'),
    priceHistory: [],
    sourceSite: 'countdown.co.nz',
    category: ['juice'],
};
const $ = cheerio.load(html);
const productEntries = $('cdx-card a.product-entry');
describe('scraping', () => {
    // it('extract normal product titles', async () => {
    //   const result = playwrightElementToProduct(productEntries[0], ['test']);
    //   expect(result!.name).toBe('yes');
    // });
    it('per unit price is derived from quantity and size', () => __awaiter(void 0, void 0, void 0, function* () {
        const result = (0, utilities_1.addUnitPriceToProduct)(juiceProduct);
        expect(result.unitName).toBe('L');
        expect(result.unitPrice).toBe(16);
        expect(result.originalUnitQuantity).toBe(250);
    }));
});
