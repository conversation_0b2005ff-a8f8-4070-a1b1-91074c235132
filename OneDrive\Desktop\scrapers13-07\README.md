# New Zealand Supermarket Scrapers

A comprehensive web scraping system for collecting and consolidating product data from New Zealand's three major supermarket chains: **Woolworths**, **New World**, and **PakNSave**.

## 🎯 **Project Overview**

This system provides:
- **Cross-store price comparison** across all major NZ supermarkets
- **Advanced product matching** with 90%+ accuracy using fuzzy algorithms
- **Comprehensive NZ brand recognition** (152 brands, 500+ variations)
- **Real-time price tracking** with historical data
- **Product image storage** via MongoDB GridFS
- **Consolidated product database** for unified shopping data

## 🏗️ **System Architecture**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Woolworths    │    │   New World     │    │   PakNSave      │
│  (TypeScript)   │    │    (.NET)       │    │    (.NET)       │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │      MongoDB Database     │
                    │  ┌─────────────────────┐  │
                    │  │ consolidated_products│  │
                    │  │   price_history     │  │
                    │  │   productImages     │  │
                    │  └─────────────────────┘  │
                    └───────────────────────────┘
```

## 🚀 **Quick Start**

### **Prerequisites**
1. **Node.js** (v16+) - [Download](https://nodejs.org/)
2. **.NET 6 SDK** - [Download](https://dotnet.microsoft.com/download)
3. **Docker** - [Download](https://www.docker.com/products/docker-desktop)
4. **Git** (optional) - [Download](https://git-scm.com/)

### **Installation**

1. **Clone or download** this repository
2. **Start MongoDB** using Docker:
   ```bash
   docker run -d -p 27017:27017 --name mongodb mongo:latest
   ```

3. **Install dependencies** for each scraper:
   ```bash
   # Woolworths (TypeScript)
   cd Woolworths
   npm install
   cd ..

   # New World (.NET)
   cd new-world/src
   dotnet restore
   cd ../..

   # PakNSave (.NET)
   cd paknsave/src
   dotnet restore
   cd ../..
   ```

### **Running All Scrapers**

#### **Option 1: Batch File (Recommended)**
```cmd
# Dry run (testing only)
run-all-scrapers.bat dev

# Save to database
run-all-scrapers.bat db

# Save to database with images
run-all-scrapers.bat db-images
```

#### **Option 2: PowerShell Script**
```powershell
# Dry run (testing only)
.\run-all-scrapers.ps1 -Mode dev

# Save to database
.\run-all-scrapers.ps1 -Mode db

# Save to database with images
.\run-all-scrapers.ps1 -Mode db-images
```

#### **Option 3: Individual Scrapers**
See individual README files in each scraper directory for detailed instructions.

## 📊 **Expected Results**

### **Scraping Statistics**
- **Woolworths**: ~260 pages, ~12,000 products, ~30 minutes
- **New World**: ~86 pages, ~4,000 products, ~15 minutes  
- **PakNSave**: ~30 pages, ~1,500 products, ~6 minutes
- **Total**: ~17,500 products consolidated into ~8,000 unique products

### **Database Collections**
- `consolidated_products` - Master product catalog with cross-store variants
- `price_history` - Historical pricing data for trend analysis
- `productImages.files` - GridFS image metadata
- `productImages.chunks` - GridFS image binary data

## 🎯 **Key Features**

### **Advanced Product Matching**
- **Levenshtein Distance** (60% weight) - Character-level similarity
- **Jaccard Similarity** (40% weight) - Word-set semantic matching
- **Brand Normalization** - 152 NZ brands with 500+ variations
- **Size Standardization** - Consistent unit formatting (kg, L, ml, etc.)

### **Cross-Store Consolidation Examples**
| Store | Original Name | Normalized | Consolidated |
|-------|---------------|------------|--------------|
| Woolworths | "Anchor Blue Milk 2L" | "anchor milk 2l" | ✅ |
| New World | "Anchor Milk Standard 2 Litre" | "anchor milk 2l" | ✅ |
| PakNSave | "Anchor Brand Milk 2L" | "anchor milk 2l" | ✅ |

**Result**: Single product with 3 store variants and price comparison

### **NZ Brand Recognition**
- **Supermarket Brands**: Pams, Essentials, Homebrand, Signature, Macro
- **Dairy Brands**: Anchor, Mainland, Meadowfresh, Lewis Road, Kapiti
- **Meat Brands**: Tegel, Inghams, Hellers, Beehive, Farmland
- **Beverage Brands**: Coke variations, L&P, Just Juice, Fresh Up
- **And 140+ more NZ-specific brands**

## 📁 **Project Structure**

```
scrapers13-07/
├── README.md                    # This file
├── run-all-scrapers.bat        # Windows batch file
├── run-all-scrapers.ps1        # PowerShell script
├── docs/                       # Documentation
│   └── consolidated-product-database-documentation.md
├── logs/                       # Scraper output logs
│   ├── woolworths.log
│   ├── newworld.log
│   └── paknsave.log
├── Woolworths/                 # TypeScript scraper
│   ├── README.md
│   ├── package.json
│   ├── src/
│   └── .env
├── new-world/                  # .NET scraper
│   ├── README.md
│   ├── src/
│   └── .env
└── paknsave/                   # .NET scraper
    ├── README.md
    ├── src/
    └── .env
```

## 🔧 **Configuration**

Each scraper can be configured via `.env` files:

### **Common Settings**
- `MONGODB_CONNECTION_STRING` - Database connection
- `STORE_NAME` - Default store location
- `SCRAPE_DELAY` - Delay between requests (seconds)
- `DRY_RUN` - Enable/disable database writes

### **Scraper-Specific Settings**
- **Woolworths**: `SKIP_STORE_SELECTION`, `HEADLESS_MODE`
- **New World**: `MAX_PAGES`, `CATEGORY_FILTER`
- **PakNSave**: `MAX_PAGES`, `LOCATION_OVERRIDE`

## 📈 **Monitoring & Logs**

### **Real-time Monitoring**
Monitor scraper progress via log files:
```bash
# Watch Woolworths progress
tail -f logs/woolworths.log

# Watch New World progress  
tail -f logs/newworld.log

# Watch PakNSave progress
tail -f logs/paknsave.log
```

### **Database Queries**
```javascript
// Check total products
db.consolidated_products.countDocuments()

// Find cross-store products
db.consolidated_products.find({"variants.1": {$exists: true}})

// Price comparison for specific product
db.consolidated_products.findOne({normalizedName: "anchor milk 2l"})
```

## 🛠️ **Troubleshooting**

### **Common Issues**

#### **MongoDB Connection Errors**
```bash
# Check if MongoDB is running
docker ps | grep mongodb

# Restart MongoDB
docker restart mongodb

# Check MongoDB logs
docker logs mongodb
```

#### **Scraper Failures**
1. Check log files in `logs/` directory
2. Verify internet connection
3. Check if target websites are accessible
4. Ensure all dependencies are installed

#### **Build Errors**
```bash
# .NET projects
dotnet clean && dotnet restore && dotnet build

# Node.js project
cd Woolworths && npm install && npm run build
```

## 📚 **Documentation**

- **[Woolworths Scraper README](Woolworths/README.md)** - TypeScript scraper details
- **[New World Scraper README](new-world/README.md)** - .NET scraper details  
- **[PakNSave Scraper README](paknsave/README.md)** - .NET scraper details
- **[Database Documentation](docs/consolidated-product-database-documentation.md)** - Complete schema reference
- **[Batch File Usage](README-BATCH-FILES.md)** - Automation scripts guide

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 **License**

This project is for educational and research purposes. Please respect the terms of service of the target websites and implement appropriate rate limiting.

## 🆘 **Support**

For issues, questions, or contributions:
1. Check the troubleshooting section above
2. Review individual scraper README files
3. Check log files for error details
4. Ensure all prerequisites are properly installed

---

**Last Updated**: January 2024  
**Version**: 2.0  
**Compatibility**: Windows 10+, .NET 6+, Node.js 16+
