process.argv = [process.argv[0], ...process.argv.slice(3)];

		import __esrun_url from 'url';

		import { createRequire as __esrun_createRequire } from "module";

		const __esrun_fileUrl = __esrun_url.pathToFileURL("C:\Users\<USER>\OneDrive\Desktop\scrapers13-07\Woolworths\esrun-1753241714316.tmp.mjs");

		const require = __esrun_createRequire(__esrun_fileUrl);
// src/index.ts
import * as dotenv2 from "./node_modules/dotenv/lib/main.js";
import playwright from "./node_modules/playwright/index.mjs";
import * as cheerio from "./node_modules/cheerio/dist/esm/index.js";
import _ from "./node_modules/lodash/lodash.js";
import { setTimeout } from "timers/promises";

// src/supabase.ts
import { createClient } from "./node_modules/@supabase/supabase-js/dist/main/index.js";
import * as dotenv from "./node_modules/dotenv/lib/main.js";

// src/utilities.ts
import { readFileSync } from "fs";
var tableIDWidth = 6;
var tableNameWidth = 60;
var tableSizeWidth = 17;
var colour = {
  red: "\x1B[31m",
  green: "\x1B[32m",
  yellow: "\x1B[33m",
  blue: "\x1B[38;5;117m",
  magenta: "\x1B[35m",
  cyan: "\x1B[36m",
  white: "\x1B[37m",
  crimson: "\x1B[38m",
  grey: "\x1B[90m",
  orange: "\x1B[38;5;214m",
  sky: "\x1B[38;5;153m"
};
function log(colour2, text) {
  const clear = "\x1B[0m";
  console.log(`${colour2}%s${clear}`, text);
}
function logError(text) {
  log(colour.red, text);
}
function logProductRow(product) {
  const unitPriceString = product.unitPrice ? `$${product.unitPrice.toFixed(2)} /${product.unitName}` : ``;
  log(
    getAlternatingRowColour(colour.sky, colour.white),
    `${product.id.padStart(tableIDWidth)} | ${product.name.slice(0, tableNameWidth).padEnd(tableNameWidth)} | ${product.size?.slice(0, tableSizeWidth).padEnd(tableSizeWidth)} | $ ${product.currentPrice.toFixed(2).padStart(4).padEnd(5)} | ` + unitPriceString
  );
}
function logTableHeader() {
  log(
    colour.yellow,
    `${"ID".padStart(tableIDWidth)} | ${"Name".padEnd(tableNameWidth)} | ${"Size".padEnd(tableSizeWidth)} | ${"Price".padEnd(7)} | Unit Price`
  );
  let headerLine = "";
  for (let i = 0; i < 111; i++) {
    headerLine += "-";
  }
  log(colour.yellow, headerLine);
}
var alternatingRowColour = false;
function getAlternatingRowColour(colourA, colourB) {
  alternatingRowColour = alternatingRowColour ? false : true;
  return alternatingRowColour ? colourA : colourB;
}
function readLinesFromTextFile(filename) {
  try {
    const file = readFileSync(filename, "utf-8");
    const result = file.split(/\r?\n/).filter((line) => {
      if (line.trim().length > 0) return true;
      else return false;
    });
    return result;
  } catch (error) {
    throw "Error reading " + filename;
  }
}
function getTimeElapsedSince(startTime2) {
  let elapsedTimeSeconds = (Date.now() - startTime2) / 1e3;
  let elapsedTimeString = Math.floor(elapsedTimeSeconds).toString();
  if (elapsedTimeSeconds >= 60) {
    return Math.floor(elapsedTimeSeconds / 60) + ":" + Math.floor(elapsedTimeSeconds % 60).toString().padStart(2, "0");
  } else return elapsedTimeString + "s";
}
function toTitleCase(str) {
  return str.replace(/\w\S*/g, function(txt) {
    return txt.charAt(0).toUpperCase() + txt.substring(1).toLowerCase();
  });
}

// src/supabase.ts
dotenv.config();
dotenv.config({ path: `.env.local`, override: true });
var supabase;
var storeId;
function establishSupabase() {
  const url = process.env.SUPABASE_URL;
  const key = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;
  if (!url || !key) {
    throw Error("SUPABASE_URL or SUPABASE_ANON_KEY not set in env");
  }
  supabase = createClient(url, key, { auth: { persistSession: false } });
}
async function ensureStoreRow() {
  if (storeId !== void 0) return storeId;
  const { data, error } = await supabase.from("stores").select("id").eq("name", "woolworths").single();
  if (error && error.code !== "PGRST116") {
    throw error;
  }
  if (data?.id) {
    storeId = data.id;
    return storeId;
  }
  const { data: insertData, error: insertErr } = await supabase.from("stores").insert({ name: "woolworths" }).select("id").single();
  if (insertErr) throw insertErr;
  storeId = insertData.id;
  return storeId;
}
async function upsertProductToSupabase(scraped) {
  if (!supabase) throw Error("Supabase client not initialised");
  const sId = await ensureStoreRow();
  const productPayload = {
    id: scraped.id,
    name: scraped.name,
    size: scraped.size,
    unit_price: scraped.unitPrice,
    unit_name: scraped.unitName,
    original_unit_qty: scraped.originalUnitQuantity,
    source_site: scraped.sourceSite,
    last_checked: scraped.lastChecked.toISOString(),
    last_updated: scraped.lastUpdated.toISOString()
  };
  const { error: prodErr } = await supabase.from("products").upsert(productPayload, { onConflict: "id" });
  if (prodErr) {
    logError("Supabase upsert product failed: " + prodErr.message);
    return 4 /* Failed */;
  }
  const pricePayload = {
    product_id: scraped.id,
    store_id: sId,
    price: scraped.currentPrice,
    recorded_at: scraped.lastUpdated.toISOString()
  };
  const { error: priceErr } = await supabase.from("prices").insert(pricePayload);
  if (priceErr) {
    logError("Supabase insert price failed: " + priceErr.message);
    return 4 /* Failed */;
  }
  log(colour.green, `  Upserted: ${scraped.name.slice(0, 45).padEnd(45)} | $${scraped.currentPrice}`);
  return 1 /* PriceChanged */;
}
async function uploadImageToSupabase(imageUrl, product) {
  if (!supabase) throw Error("Supabase client not initialised");
  try {
    const imageResponse = await fetch(imageUrl);
    if (!imageResponse.ok) {
      logError(`Failed to download image from ${imageUrl}: ${imageResponse.statusText}`);
      return false;
    }
    const imageBuffer = await imageResponse.arrayBuffer();
    const imageBlob = new Blob([imageBuffer], { type: "image/jpeg" });
    const filePath = `products/${product.id}.jpg`;
    const { data, error } = await supabase.storage.from("product-images").upload(filePath, imageBlob, {
      contentType: "image/jpeg",
      upsert: true
      // Overwrite if exists
    });
    if (error) {
      logError(`Supabase storage upload failed: ${error.message}`);
      return false;
    }
    const { data: publicUrlData } = supabase.storage.from("product-images").getPublicUrl(filePath);
    log(colour.blue, `  Image uploaded: ${product.id} -> ${publicUrlData.publicUrl}`);
    return true;
  } catch (err) {
    logError(`Image upload error: ${err.message}`);
    return false;
  }
}

// src/consolidated-products.ts
import { createClient as createClient3 } from "./node_modules/@supabase/supabase-js/dist/main/index.js";

// ../shared/product-matcher.js
import { createClient as createClient2 } from "../node_modules/@supabase/supabase-js/dist/main/index.js";
var supabase2 = null;
function initializeSupabase(url, key) {
  supabase2 = createClient2(url, key);
}
function normalizeProductName(name, brandName = "") {
  let normalized = name.toLowerCase().trim();
  if (brandName) {
    const brandRegex = new RegExp(brandName.toLowerCase(), "gi");
    normalized = normalized.replace(brandRegex, "").trim();
  }
  const descriptorsToRemove = [
    "countdown",
    "woolworths",
    "new world",
    "paknsave",
    "pak n save",
    "select",
    "premium",
    "value",
    "budget",
    "signature",
    "essentials",
    "organic",
    "free range",
    "grass fed",
    "natural",
    "home brand"
  ];
  for (const descriptor of descriptorsToRemove) {
    const regex = new RegExp(`\\b${descriptor}\\b`, "gi");
    normalized = normalized.replace(regex, "").trim();
  }
  normalized = normalized.replace(/[^\w\s]/g, " ").replace(/\s+/g, " ").replace(/\b(the|and|or|with|in|of|for)\b/g, "").trim();
  return normalized;
}
function normalizeSize(size) {
  if (!size) return "";
  let normalized = size.toLowerCase().trim();
  normalized = normalized.replace(/\bmls?\b/g, "ml").replace(/\blitres?\b/g, "l").replace(/\bliters?\b/g, "l").replace(/\bgrams?\b/g, "g").replace(/\bkilos?\b/g, "kg").replace(/\bkilograms?\b/g, "kg").replace(/\bpacks?\b/g, "pk").replace(/\beach\b/g, "ea").replace(/\bper\s+kg\b/g, "per kg").replace(/\s+/g, "");
  return normalized;
}
function sizeToComparableValue(size) {
  if (!size) return 0;
  const normalized = normalizeSize(size);
  const match = normalized.match(/^(\d+(?:\.\d+)?)(ml|l|g|kg|pk|ea)?/);
  if (!match) return 0;
  const value = parseFloat(match[1]);
  const unit = match[2] || "";
  switch (unit) {
    case "l":
      return value * 1e3;
    // Convert to ml
    case "kg":
      return value * 1e3;
    // Convert to g
    case "pk":
    case "ea":
      return value;
    case "ml":
    case "g":
    default:
      return value;
  }
}
function calculateNameSimilarity(name1, name2) {
  const normalized1 = normalizeProductName(name1);
  const normalized2 = normalizeProductName(name2);
  if (normalized1 === normalized2) return 100;
  const distance = levenshteinDistance(normalized1, normalized2);
  const maxLength = Math.max(normalized1.length, normalized2.length);
  if (maxLength === 0) return 100;
  const similarity = Math.max(0, (1 - distance / maxLength) * 100);
  const words1 = normalized1.split(" ").filter((w) => w.length > 2);
  const words2 = normalized2.split(" ").filter((w) => w.length > 2);
  const commonWords = words1.filter((w) => words2.includes(w));
  if (commonWords.length > 0) {
    const wordBoost = commonWords.length / Math.max(words1.length, words2.length) * 20;
    return Math.min(100, similarity + wordBoost);
  }
  return similarity;
}
function levenshteinDistance(str1, str2) {
  const matrix = [];
  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i];
  }
  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j;
  }
  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        );
      }
    }
  }
  return matrix[str2.length][str1.length];
}
function calculateProductMatchScore(product1, product2) {
  const nameScore = calculateNameSimilarity(product1.name, product2.name);
  let sizeScore = 0;
  if (product1.size && product2.size) {
    const normalizedSize1 = normalizeSize(product1.size);
    const normalizedSize2 = normalizeSize(product2.size);
    if (normalizedSize1 === normalizedSize2) {
      sizeScore = 30;
    } else {
      const value1 = sizeToComparableValue(product1.size);
      const value2 = sizeToComparableValue(product2.size);
      const sizeDiff = Math.abs(value1 - value2) / Math.max(value1, value2);
      if (sizeDiff < 0.1) sizeScore = 20;
      else if (sizeDiff < 0.3) sizeScore = 10;
    }
  }
  let brandScore = 0;
  if (product1.brand && product2.brand) {
    if (product1.brand.toLowerCase() === product2.brand.toLowerCase()) {
      brandScore = 20;
    }
  }
  const totalScore = nameScore * 0.5 + sizeScore * 0.3 + brandScore * 0.2;
  return Math.round(totalScore);
}
async function findPotentialMatches(product, minScore = 70) {
  if (!supabase2) {
    throw new Error("Supabase client not initialized");
  }
  const normalizedName = normalizeProductName(product.name);
  const nameWords = normalizedName.split(" ").filter((w) => w.length > 2);
  const { data: candidates, error } = await supabase2.from("consolidated_products").select(`
            id, 
            display_name, 
            normalized_name,
            primary_size,
            brand_id,
            brands(name)
        `).textSearch("normalized_name", nameWords.join(" | "));
  if (error) {
    console.error("Error searching for product matches:", error);
    return [];
  }
  const matches = [];
  for (const candidate of candidates || []) {
    const candidateProduct = {
      name: candidate.display_name,
      size: candidate.primary_size,
      brand: candidate.brands?.name
    };
    const score = calculateProductMatchScore(product, candidateProduct);
    if (score >= minScore) {
      matches.push({
        consolidatedProductId: candidate.id,
        displayName: candidate.display_name,
        score,
        confidence: score >= 90 ? "high" : score >= 80 ? "medium" : "low"
      });
    }
  }
  return matches.sort((a, b) => b.score - a.score);
}
async function consolidateProduct(storeProduct, storeId2) {
  if (!supabase2) {
    throw new Error("Supabase client not initialized");
  }
  const matches = await findPotentialMatches(storeProduct, 70);
  let consolidatedProductId;
  if (matches.length > 0 && matches[0].score >= 85) {
    consolidatedProductId = matches[0].consolidatedProductId;
    console.log(`Linked ${storeProduct.name} to existing consolidated product (score: ${matches[0].score})`);
  } else {
    const normalizedName = normalizeProductName(storeProduct.name);
    const categoryId = await mapStoreCategory(storeProduct.category);
    const { data: newProduct, error } = await supabase2.from("consolidated_products").insert({
      normalized_name: normalizedName,
      display_name: storeProduct.name,
      primary_size: storeProduct.size,
      category_id: categoryId,
      match_confidence: matches.length > 0 ? matches[0].score : 100
    }).select().single();
    if (error) {
      console.error("Error creating consolidated product:", error);
      return null;
    }
    consolidatedProductId = newProduct.id;
    console.log(`Created new consolidated product for ${storeProduct.name}`);
  }
  await linkStoreProduct(storeProduct, storeId2, consolidatedProductId);
  return consolidatedProductId;
}
async function linkStoreProduct(storeProduct, storeId2, consolidatedProductId) {
  const { error } = await supabase2.from("product_variants").upsert({
    consolidated_product_id: consolidatedProductId,
    store_product_id: storeProduct.id,
    store_id: storeId2,
    store_name: storeProduct.name,
    store_size: storeProduct.size,
    store_unit_price: storeProduct.unitPrice,
    store_unit_name: storeProduct.unitName,
    last_seen: (/* @__PURE__ */ new Date()).toISOString(),
    is_active: true
  });
  if (error) {
    console.error("Error linking store product:", error);
  }
}
async function mapStoreCategory(storeCategories) {
  if (!storeCategories || storeCategories.length === 0) return null;
  const categoryMappings = {
    "fruit-veg": "Fruit & Vegetables",
    "fresh-foods-and-bakery": "Fresh Foods",
    "meat-poultry": "Meat & Poultry",
    "fish-seafood": "Fish & Seafood",
    "bakery": "Bakery",
    "fridge-deli": "Dairy & Deli",
    "chilled-frozen-and-desserts": "Chilled & Frozen",
    "frozen": "Frozen Foods",
    "pantry": "Pantry & Dry Goods",
    "drinks": "Cold Drinks",
    "hot-and-cold-drinks": "Beverages",
    "beer-wine": "Beer, Wine & Cider",
    "beer-wine-and-cider": "Beer, Wine & Cider",
    "health-body": "Health & Body",
    "health-and-body": "Health & Body",
    "household": "Household & Cleaning",
    "household-and-cleaning": "Household & Cleaning",
    "baby-child": "Baby & Toddler",
    "baby-and-toddler": "Baby & Toddler",
    "pet": "Pet Supplies",
    "pets": "Pet Supplies"
  };
  const firstCategory = storeCategories[0].toLowerCase().replace(/\s+/g, "-");
  const mappedCategory = categoryMappings[firstCategory];
  if (mappedCategory) {
    const { data } = await supabase2.from("category_hierarchy").select("id").eq("name", mappedCategory).single();
    return data?.id;
  }
  return null;
}
async function storeConsolidatedPrice(consolidatedProductId, storeId2, price, isSpecial = false) {
  if (!supabase2) {
    throw new Error("Supabase client not initialized");
  }
  const { error } = await supabase2.from("consolidated_prices").insert({
    consolidated_product_id: consolidatedProductId,
    store_id: storeId2,
    price,
    is_special: isSpecial,
    was_available: true,
    recorded_at: (/* @__PURE__ */ new Date()).toISOString()
  });
  if (error) {
    console.error("Error storing consolidated price:", error);
  }
}

// src/consolidated-products.ts
var supabase3;
var woolworthsStoreId;
function initializeConsolidatedProducts(supabaseUrl, supabaseKey) {
  supabase3 = createClient3(supabaseUrl, supabaseKey, { auth: { persistSession: false } });
  initializeSupabase(supabaseUrl, supabaseKey);
}
async function ensureWoolworthsStoreId() {
  if (woolworthsStoreId) return woolworthsStoreId;
  const { data, error } = await supabase3.from("stores").select("id").eq("name", "woolworths").single();
  if (error && error.code !== "PGRST116") {
    throw error;
  }
  if (data?.id) {
    woolworthsStoreId = data.id;
    return woolworthsStoreId;
  }
  const { data: insertData, error: insertErr } = await supabase3.from("stores").insert({ name: "woolworths" }).select("id").single();
  if (insertErr) throw insertErr;
  woolworthsStoreId = insertData.id;
  return woolworthsStoreId;
}
async function processConsolidatedProduct(scrapedProduct) {
  try {
    const storeId2 = await ensureWoolworthsStoreId();
    const productForMatching = {
      id: scrapedProduct.id,
      name: scrapedProduct.name,
      size: scrapedProduct.size,
      category: scrapedProduct.category,
      brand: extractBrandFromName(scrapedProduct.name),
      // Extract brand if possible
      unitPrice: scrapedProduct.unitPrice,
      unitName: scrapedProduct.unitName
    };
    const consolidatedProductId = await consolidateProduct(productForMatching, storeId2);
    if (consolidatedProductId) {
      await storeConsolidatedPrice(
        consolidatedProductId,
        storeId2,
        scrapedProduct.currentPrice,
        false
        // isSpecial - could be determined from product data
      );
      await handleProductSizeVariants(consolidatedProductId, scrapedProduct);
    }
    return consolidatedProductId;
  } catch (error) {
    console.error(`Error processing consolidated product for ${scrapedProduct.name}:`, error);
    return null;
  }
}
function extractBrandFromName(productName) {
  const brandPatterns = [
    /^([A-Z][a-z]+(?:\s+[A-Z][a-z]*)*)\s+/,
    // Capitalize Case at start
    /\b(Select|Countdown|Essentials|Free Range|Signature)\b/i
    // Store brands
  ];
  for (const pattern of brandPatterns) {
    const match = productName.match(pattern);
    if (match) {
      return match[1].trim();
    }
  }
  return void 0;
}
async function handleProductSizeVariants(consolidatedProductId, product) {
  if (!product.size) return;
  try {
    const { data: existingVariant } = await supabase3.from("product_size_variants").select("id").eq("consolidated_product_id", consolidatedProductId).eq("size_name", product.size).single();
    if (existingVariant) return;
    const weightGrams = calculateWeightInGrams(product.size);
    const volumeML = calculateVolumeInML(product.size);
    await supabase3.from("product_size_variants").insert({
      consolidated_product_id: consolidatedProductId,
      size_name: product.size,
      size_weight_grams: weightGrams,
      size_volume_ml: volumeML,
      is_primary_size: false
      // Could be determined by most common size
    });
  } catch (error) {
    console.error(`Error handling size variants for ${product.name}:`, error);
  }
}
function calculateWeightInGrams(size) {
  if (!size) return null;
  const sizeNormalized = size.toLowerCase();
  const gramMatch = sizeNormalized.match(/(\d+(?:\.\d+)?)\s*g\b/);
  if (gramMatch) {
    return parseInt(gramMatch[1]);
  }
  const kgMatch = sizeNormalized.match(/(\d+(?:\.\d+)?)\s*kg\b/);
  if (kgMatch) {
    return Math.round(parseFloat(kgMatch[1]) * 1e3);
  }
  return null;
}
function calculateVolumeInML(size) {
  if (!size) return null;
  const sizeNormalized = size.toLowerCase();
  const mlMatch = sizeNormalized.match(/(\d+(?:\.\d+)?)\s*ml\b/);
  if (mlMatch) {
    return parseInt(mlMatch[1]);
  }
  const lMatch = sizeNormalized.match(/(\d+(?:\.\d+)?)\s*l\b/);
  if (lMatch) {
    return Math.round(parseFloat(lMatch[1]) * 1e3);
  }
  return null;
}

// src/product-overrides.ts
var productOverrides = [
  { id: "206889", size: "180g" },
  { id: "196996", size: "300g" },
  { id: "137967", size: "420g" },
  { id: "125856", size: "450g" },
  { id: "189268", size: "1.13kg" },
  { id: "189150", size: "1.2kg" },
  { id: "190454", size: "2.1kg" },
  { id: "189078", size: "1.3kg" },
  { id: "189136", size: "1.2kg" },
  { id: "755237", size: "931g" },
  { id: "755304", size: "1.1kg" },
  { id: "755246", size: "1020g" },
  { id: "755245", size: "1.2kg" },
  { id: "112273", size: "865ml" },
  { id: "269514", size: "584ml" },
  { id: "269515", size: "584ml" },
  { id: "116518", size: "440ml" },
  { id: "151191", size: "570ml" },
  { id: "279904", size: "575ml" },
  { id: "146149", size: "1000ml" },
  { id: "791925", size: "525g" },
  { id: "774216", size: "525g" },
  { id: "784406", size: "525g" },
  { id: "791916", size: "525g" },
  { id: "306624", size: "185g" },
  { id: "156824", size: "180g" },
  { id: "9023", size: "375g" },
  { id: "266962", category: "sweets-lollies" },
  { id: "171524", size: "230ml", category: "baking" },
  { id: "170021", category: "ice-blocks" },
  { id: "71164", category: "sausages" },
  { id: "71174", category: "sausages" },
  { id: "71168", category: "sausages" },
  { id: "71165", category: "sausages" },
  { id: "331560", category: "specialty-bread" },
  { id: "679412", category: "herbal-tea" },
  { id: "790129", category: "herbal-tea" },
  { id: "267492", category: "herbal-tea" },
  { id: "267485", category: "herbal-tea" },
  { id: "413302", category: "herbal-tea" },
  { id: "267488", category: "herbal-tea" },
  { id: "760872", category: "herbal-tea" },
  { id: "681177", category: "herbal-tea" },
  { id: "95091", category: "herbal-tea" },
  { id: "761093", category: "black-tea" },
  { id: "721661", category: "green-tea" },
  { id: "790129", category: "herbal-tea" },
  { id: "267492", category: "herbal-tea" },
  { id: "267485", category: "herbal-tea" },
  { id: "721034", category: "herbal-tea" },
  { id: "413302", category: "herbal-tea" },
  { id: "267488", category: "herbal-tea" },
  { id: "760872", category: "herbal-tea" },
  { id: "681177", category: "herbal-tea" },
  { id: "95091.", category: "herbal-tea" },
  { id: "184090", category: "herbal-tea" },
  { id: "761093", category: "black-tea" },
  { id: "721661", category: "green-tea" },
  { id: "690093", category: "green-tea" },
  { id: "780922", category: "sauces" },
  { id: "780921", category: "sauces" },
  { id: "72618", category: "black-tea" },
  { id: "6053", category: "black-tea" },
  { id: "72617", category: "black-tea" },
  { id: "168068", category: "black-tea" },
  { id: "6052", category: "black-tea" },
  { id: "761436", category: "black-tea" }
];

// src/index.ts
dotenv2.config();
dotenv2.config({ path: `.env.local`, override: true });
var pageLoadDelaySeconds = 7;
var productLogDelayMilliSeconds = 20;
var startTime = Date.now();
var categorisedUrls = loadUrlsFile();
var databaseMode = false;
var uploadImagesMode = false;
var headlessMode = true;
categorisedUrls = await handleArguments(categorisedUrls);
if (databaseMode) {
  establishSupabase();
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;
  initializeConsolidatedProducts(supabaseUrl, supabaseKey);
}
var browser;
var page;
browser = await establishPlaywrightPage(headlessMode);
await selectStoreByLocationName();
await scrapeAllPageURLs();
browser.close();
log(
  colour.sky,
  `
All Pages Completed = Total Time Elapsed ${getTimeElapsedSince(startTime)} 
`
);
function loadUrlsFile(filePath = "src/urls.txt") {
  const rawLinesFromFile = readLinesFromTextFile(filePath);
  let categorisedUrls2 = [];
  rawLinesFromFile.map((line) => {
    let parsedUrls = parseAndCategoriseURL(line);
    if (parsedUrls !== void 0) {
      categorisedUrls2 = [...categorisedUrls2, ...parsedUrls];
    }
  });
  return categorisedUrls2;
}
async function scrapeAllPageURLs() {
  log(
    colour.yellow,
    `${categorisedUrls.length} pages to be scraped`.padEnd(35) + `${pageLoadDelaySeconds}s delay between scrapes`.padEnd(35) + (databaseMode ? "(Database Mode)" : "(Dry Run Mode)")
  );
  for (let i = 0; i < categorisedUrls.length; i++) {
    const categorisedUrl = categorisedUrls[i];
    let url = categorisedUrls[i].url;
    const shortUrl = url.replace("https://", "");
    log(
      colour.yellow,
      `
[${i + 1}/${categorisedUrls.length}] ${shortUrl}`
    );
    try {
      await page.goto(url);
      for (let pageDown = 0; pageDown < 5; pageDown++) {
        const timeBetweenPgDowns = Math.random() * 1e3 + 500;
        await page.waitForTimeout(timeBetweenPgDowns);
        await page.keyboard.press("PageDown");
      }
      await page.setDefaultTimeout(3e4);
      await page.waitForSelector("product-price h3");
      const html = await page.innerHTML("product-grid");
      const $ = cheerio.load(html);
      const allProductEntries = $("cdx-card product-stamp-grid div.product-entry");
      const advertisementEntries = $("div.carousel-track div cdx-card product-stamp-grid div.product-entry");
      const adHrefs = advertisementEntries.map((index, element) => {
        return $(element).find("a").first().attr("href");
      }).toArray();
      const productEntries = allProductEntries.filter((index, element) => {
        const productHref = $(element).find("a").first().attr("href");
        return !adHrefs.includes(productHref);
      });
      log(
        colour.yellow,
        `${productEntries.length} product entries found`.padEnd(35) + `Time Elapsed: ${getTimeElapsedSince(startTime)}`.padEnd(35) + `Category: ${_.startCase(categorisedUrl.categories.join(" - "))}`
      );
      if (!databaseMode) logTableHeader();
      let perPageLogStats = {
        newProducts: 0,
        priceChanged: 0,
        infoUpdated: 0,
        alreadyUpToDate: 0
      };
      perPageLogStats = await processFoundProductEntries(categorisedUrl, productEntries, perPageLogStats);
      if (databaseMode) {
        log(
          colour.blue,
          `Supabase: ${perPageLogStats.newProducts} new products, ${perPageLogStats.priceChanged} updated prices, ${perPageLogStats.infoUpdated} updated info, ${perPageLogStats.alreadyUpToDate} already up-to-date`
        );
      }
      await setTimeout(pageLoadDelaySeconds * 1e3);
    } catch (error) {
      if (typeof error === "string") {
        if (error.includes("NS_ERROR_CONNECTION_REFUSED")) {
          logError("Connection Failed - Check Firewall\n" + error);
          return;
        }
      }
      logError(
        "Page Timeout after 30 seconds - Skipping this page\n" + error
      );
    }
  }
}
async function processFoundProductEntries(categorisedUrl, productEntries, perPageLogStats) {
  for (let i = 0; i < productEntries.length; i++) {
    const productEntryElement = productEntries[i];
    const product = playwrightElementToProduct(
      productEntryElement,
      categorisedUrl.categories
    );
    if (databaseMode && product !== void 0) {
      const response = await upsertProductToSupabase(product);
      const consolidatedProductId = await processConsolidatedProduct(product);
      switch (response) {
        case 3 /* AlreadyUpToDate */:
          perPageLogStats.alreadyUpToDate++;
          break;
        case 2 /* InfoChanged */:
          perPageLogStats.infoUpdated++;
          break;
        case 0 /* NewProduct */:
          perPageLogStats.newProducts++;
          break;
        case 1 /* PriceChanged */:
          perPageLogStats.priceChanged++;
          break;
        default:
          break;
      }
      if (uploadImagesMode) {
        const imageUrlBase = "https://assets.woolworths.com.au/images/2010/";
        const imageUrlExtensionAndQueryParams = ".jpg?impolicy=wowcdxwbjbx&w=900&h=900";
        const imageUrl = imageUrlBase + product.id + imageUrlExtensionAndQueryParams;
        await uploadImageToSupabase(imageUrl, product);
      }
    } else if (!databaseMode && product !== void 0) {
      logProductRow(product);
    }
    await setTimeout(productLogDelayMilliSeconds);
  }
  return perPageLogStats;
}
function handleArguments(categorisedUrls2) {
  if (process.argv.length > 2) {
    const userArgs = process.argv.slice(2, process.argv.length);
    let potentialUrl = "";
    userArgs.forEach(async (arg) => {
      if (arg === "db") databaseMode = true;
      else if (arg === "images") uploadImagesMode = true;
      else if (arg === "headless") headlessMode = true;
      else if (arg === "headed") headlessMode = false;
      else if (arg.includes(".co.nz")) potentialUrl += arg;
      else if (arg === "reverse") categorisedUrls2 = categorisedUrls2.reverse();
    });
    const parsedUrl = parseAndCategoriseURL(potentialUrl);
    if (parsedUrl !== void 0) categorisedUrls2 = [parsedUrl];
  }
  return categorisedUrls2;
}
async function establishPlaywrightPage(headless = true) {
  log(
    colour.yellow,
    "Launching Browser.. " + (process.argv.length > 2 ? "(" + (process.argv.length - 2) + " arguments found)" : "")
  );
  browser = await playwright.firefox.launch({
    headless
  });
  page = await browser.newPage();
  await routePlaywrightExclusions();
  return browser;
}
async function selectStoreByLocationName(locationName = "") {
  if (locationName === "") {
    if (process.env.STORE_NAME) locationName = process.env.STORE_NAME;
    else return;
  }
  log(colour.yellow, "Selecting Store Location..");
  try {
    await page.setDefaultTimeout(12e3);
    await page.goto("https://www.woolworths.co.nz/bookatimeslot", {
      waitUntil: "domcontentloaded"
    });
    await page.waitForSelector("fieldset div div p button");
  } catch (error) {
    logError("Location selection page timed out - Using default location instead");
    return;
  }
  const oldLocation = await page.locator("fieldset div div p strong").innerText();
  await page.locator("fieldset div div p button").click();
  await page.waitForSelector("form-suburb-autocomplete form-input input");
  try {
    await page.locator("form-suburb-autocomplete form-input input").type(locationName);
    await page.waitForTimeout(1500);
    await page.keyboard.press("ArrowDown");
    await page.waitForTimeout(300);
    await page.keyboard.press("Enter");
    await page.waitForTimeout(1e3);
    await page.getByText("Save and Continue Shopping").click();
    log(
      colour.yellow,
      "Changed Location from " + oldLocation + " to " + locationName + "\n"
    );
    await page.waitForTimeout(2e3);
  } catch {
    logError(
      `Store Location:${locationName} not found. Using default instead.`
    );
  }
}
function playwrightElementToProduct(element, categories) {
  const $ = cheerio.load(element);
  let idNameSizeH3 = $(element).find("h3").filter((i, element2) => {
    if ($(element2).attr("id")?.includes("-title")) {
      return true;
    } else return false;
  });
  let product = {
    // ID
    // -------
    // Extract product ID from h3 id attribute, and remove non-numbers
    id: idNameSizeH3.attr("id")?.replace(/\D/g, ""),
    // Source Site - set where the source of information came from
    sourceSite: "countdown.co.nz",
    // use countdown for consistency with old data
    // Categories
    category: categories,
    // already obtained from url/text file
    // Store today's date
    lastChecked: /* @__PURE__ */ new Date(),
    lastUpdated: /* @__PURE__ */ new Date(),
    // These values will later be overwritten
    name: "",
    priceHistory: [],
    currentPrice: 0
  };
  let rawNameAndSize = idNameSizeH3.text().trim();
  rawNameAndSize = rawNameAndSize.toLowerCase().replace("  ", " ").replace("fresh fruit", "").replace("fresh vegetable", "").trim();
  let tryMatchSize = rawNameAndSize.match(/(tray\s\d+)|(\d+(\.\d+)?(\-\d+\.\d+)?\s?(g|kg|l|ml|pack))\b/g);
  if (!tryMatchSize) {
    product.name = toTitleCase(rawNameAndSize);
    product.size = "";
  } else {
    let indexOfSizeSection = rawNameAndSize.indexOf(tryMatchSize[0]);
    product.name = toTitleCase(rawNameAndSize.slice(0, indexOfSizeSection)).trim();
    let cleanedSize = rawNameAndSize.slice(indexOfSizeSection).trim();
    if (cleanedSize.match(/\d+l\b/)) {
      cleanedSize = cleanedSize.replace("l", "L");
    }
    cleanedSize.replace("tray", "Tray");
    product.size = cleanedSize;
  }
  const dollarString = $(element).find("product-price div h3 em").text().trim();
  let centString = $(element).find("product-price div h3 span").text().trim();
  if (centString.includes("kg")) product.size = "per kg";
  centString = centString.replace(/\D/g, "");
  product.currentPrice = Number(dollarString + "." + centString);
  const today = /* @__PURE__ */ new Date();
  today.setMinutes(0);
  today.setSeconds(0);
  const todaysDatedPrice = {
    date: today,
    price: product.currentPrice
  };
  product.priceHistory = [todaysDatedPrice];
  const rawUnitPrice = $(element).find("span.cupPrice").text().trim();
  if (rawUnitPrice) {
    const unitPriceString = rawUnitPrice.split("/")[0].replace("$", "").trim();
    let unitPrice = Number.parseFloat(unitPriceString);
    const amountAndUnit = rawUnitPrice.split("/")[1].trim();
    let amount = Number.parseInt(amountAndUnit.match(/\d+/g)?.[0] || "");
    let unit = amountAndUnit.match(/\w+/g)?.[0] || "";
    if (amountAndUnit == "100g") {
      amount = amount * 10;
      unitPrice = unitPrice * 10;
      unit = "kg";
    } else if (amountAndUnit == "100mL") {
      amount = amount * 10;
      unitPrice = unitPrice * 10;
      unit = "L";
    }
    unit = unit.replace("1kg", "kg");
    unit = unit.replace("1L", "L");
    product.unitPrice = unitPrice;
    product.unitName = unit;
  }
  productOverrides.forEach((override) => {
    if (override.id === product.id) {
      if (override.size !== void 0) {
        product.size = override.size;
      }
      if (override.category !== void 0) {
        product.category = [override.category];
      }
    }
  });
  if (validateProduct(product)) return product;
  else {
    try {
      logError(
        `  Unable to Scrape: ${product.id.padStart(6)} | ${product.name} | $${product.currentPrice}`
      );
    } catch {
      logError("  Unable to Scrape ID from product");
    }
    return void 0;
  }
}
function validateProduct(product) {
  try {
    if (product.name.match(/\$\s\d+/)) return false;
    if (product.name.length < 4 || product.name.length > 100) return false;
    if (product.id.length < 2 || product.id.length > 20) return false;
    if (product.currentPrice <= 0 || product.currentPrice === null || product.currentPrice === void 0 || Number.isNaN(product.currentPrice) || product.currentPrice > 999) {
      return false;
    }
    return true;
  } catch (error) {
    return false;
  }
}
function parseAndCategoriseURL(line) {
  let baseCategorisedURL = { url: "", categories: [] };
  let parsedUrls = [];
  let numPagesPerURL = 1;
  if (!line.includes("woolworths.co.nz")) {
    return void 0;
  } else if (line.includes("?search=")) {
    parsedUrls.push({ url: line, categories: [] });
  } else {
    line.split(" ").forEach((section) => {
      if (section.includes("woolworths.co.nz")) {
        baseCategorisedURL.url = section;
        if (!baseCategorisedURL.url.startsWith("http"))
          baseCategorisedURL.url = "https://" + section;
        if (section.includes("?")) {
          baseCategorisedURL.url = line.substring(0, line.indexOf("?"));
        }
        baseCategorisedURL.url += "?page=1";
      } else if (section.startsWith("categories=")) {
        let splitCategories = [section.replace("categories=", "")];
        if (section.includes(","))
          splitCategories = section.replace("categories=", "").split(",");
        baseCategorisedURL.categories = splitCategories;
      } else if (section.startsWith("pages=")) {
        numPagesPerURL = Number.parseInt(section.split("=")[1]);
      }
      if (baseCategorisedURL.categories.length === 0) {
        const baseUrl = baseCategorisedURL.url.split("?")[0];
        let slashSections = baseUrl.split("/");
        baseCategorisedURL.categories = [slashSections[slashSections.length - 1]];
      }
    });
    for (let i = 1; i <= numPagesPerURL; i++) {
      let pagedUrl = {
        url: baseCategorisedURL.url.replace("page=1", "page=" + i),
        categories: baseCategorisedURL.categories
      };
      parsedUrls.push(pagedUrl);
    }
  }
  return parsedUrls;
}
async function routePlaywrightExclusions() {
  let typeExclusions = ["image", "media", "font"];
  let urlExclusions = [
    "googleoptimize.com",
    "gtm.js",
    "visitoridentification.js",
    "js-agent.newrelic.com",
    "cquotient.com",
    "googletagmanager.com",
    "cloudflareinsights.com",
    "dwanalytics",
    "facebook.net",
    "chatWidget",
    "edge.adobedc.net",
    "\u200B/Content/Banners/",
    "go-mpulse.net"
  ];
  await page.route("**/*", async (route) => {
    const req = route.request();
    let excludeThisRequest = false;
    urlExclusions.forEach((excludedURL) => {
      if (req.url().includes(excludedURL)) excludeThisRequest = true;
    });
    typeExclusions.forEach((excludedType) => {
      if (req.resourceType() === excludedType) excludeThisRequest = true;
    });
    if (excludeThisRequest) {
      await route.abort();
    } else {
      await route.continue();
    }
  });
  return;
}
export {
  databaseMode,
  parseAndCategoriseURL,
  playwrightElementToProduct,
  uploadImagesMode
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsic3JjL2luZGV4LnRzIiwgInNyYy9zdXBhYmFzZS50cyIsICJzcmMvdXRpbGl0aWVzLnRzIiwgInNyYy9jb25zb2xpZGF0ZWQtcHJvZHVjdHMudHMiLCAiLi4vc2hhcmVkL3Byb2R1Y3QtbWF0Y2hlci5qcyIsICJzcmMvcHJvZHVjdC1vdmVycmlkZXMudHMiXSwKICAic291cmNlUm9vdCI6ICJDOlxcVXNlcnNcXHNrZWFtXFxPbmVEcml2ZVxcRGVza3RvcFxcc2NyYXBlcnMxMy0wN1xcV29vbHdvcnRocyIsCiAgInNvdXJjZXNDb250ZW50IjogWyJpbXBvcnQgKiBhcyBkb3RlbnYgZnJvbSBcImRvdGVudlwiO1xuZG90ZW52LmNvbmZpZygpO1xuZG90ZW52LmNvbmZpZyh7IHBhdGg6IGAuZW52LmxvY2FsYCwgb3ZlcnJpZGU6IHRydWUgfSk7XG5cbmltcG9ydCBwbGF5d3JpZ2h0IGZyb20gXCJwbGF5d3JpZ2h0XCI7XG5pbXBvcnQgKiBhcyBjaGVlcmlvIGZyb20gXCJjaGVlcmlvXCI7XG5pbXBvcnQgXyBmcm9tIFwibG9kYXNoXCI7XG5pbXBvcnQgeyBzZXRUaW1lb3V0IH0gZnJvbSBcInRpbWVycy9wcm9taXNlc1wiO1xuXG5pbXBvcnQgeyBlc3RhYmxpc2hTdXBhYmFzZSwgdXBzZXJ0UHJvZHVjdFRvU3VwYWJhc2UsIHVwbG9hZEltYWdlVG9TdXBhYmFzZSB9IGZyb20gXCIuL3N1cGFiYXNlLmpzXCI7XG5pbXBvcnQgeyBpbml0aWFsaXplQ29uc29saWRhdGVkUHJvZHVjdHMsIHByb2Nlc3NDb25zb2xpZGF0ZWRQcm9kdWN0IH0gZnJvbSBcIi4vY29uc29saWRhdGVkLXByb2R1Y3RzLmpzXCI7XG5pbXBvcnQgeyBwcm9kdWN0T3ZlcnJpZGVzIH0gZnJvbSBcIi4vcHJvZHVjdC1vdmVycmlkZXMuanNcIjtcbmltcG9ydCB7IENhdGVnb3Jpc2VkVXJsLCBEYXRlZFByaWNlLCBQcm9kdWN0LCBVcHNlcnRSZXNwb25zZSB9IGZyb20gXCIuL3R5cGluZ3NcIjtcbmltcG9ydCB7XG4gIGxvZywgY29sb3VyLCBsb2dQcm9kdWN0Um93LCBsb2dFcnJvciwgcmVhZExpbmVzRnJvbVRleHRGaWxlLCBnZXRUaW1lRWxhcHNlZFNpbmNlLFxuICBsb2dUYWJsZUhlYWRlciwgdG9UaXRsZUNhc2UsXG59IGZyb20gXCIuL3V0aWxpdGllcy5qc1wiO1xuXG4vLyBXb29sd29ydGhzIC8gQ291bnRkb3duIFNjcmFwZXJcbi8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuLy8gU2NyYXBlcyBwcmljaW5nIGFuZCBvdGhlciBpbmZvIGZyb20gV29vbHdvcnRocyBOWidzIHdlYnNpdGUuXG5cbi8vIFNldCBhIHJlYXNvbmFibGUgZGVsYXkgYmV0d2VlbiBlYWNoIHBhZ2UgbG9hZCB0byByZWR1Y2UgbG9hZCBvbiB0aGUgc2VydmVyLlxuY29uc3QgcGFnZUxvYWREZWxheVNlY29uZHMgPSA3O1xuXG4vLyBTZXQgYSBkZWxheSB3aGVuIGxvZ2dpbmcgZWFjaCBwcm9kdWN0IHBlciBwYWdlIHRvIHRoZSBjb25zb2xlLlxuY29uc3QgcHJvZHVjdExvZ0RlbGF5TWlsbGlTZWNvbmRzID0gMjA7XG5cbi8vIFJlY29yZCBzdGFydCB0aW1lIGZvciBsb2dnaW5nIHB1cnBvc2VzXG5jb25zdCBzdGFydFRpbWUgPSBEYXRlLm5vdygpO1xuXG4vLyBMb2FkIFVSTHMgZnJvbSB0ZXh0IGZpbGUgJ3VybHMudHh0J1xubGV0IGNhdGVnb3Jpc2VkVXJsczogQ2F0ZWdvcmlzZWRVcmxbXSA9IGxvYWRVcmxzRmlsZSgpO1xuXG4vLyBIYW5kbGUgY29tbWFuZC1saW5lIGFyZ3VtZW50cywgZXg6ICdkYicsICdpbWFnZXMnLCBvciBzaW5nbGUgdXJsc1xuZXhwb3J0IGxldCBkYXRhYmFzZU1vZGUgPSBmYWxzZTtcbmV4cG9ydCBsZXQgdXBsb2FkSW1hZ2VzTW9kZSA9IGZhbHNlO1xubGV0IGhlYWRsZXNzTW9kZSA9IHRydWU7XG5jYXRlZ29yaXNlZFVybHMgPSBhd2FpdCBoYW5kbGVBcmd1bWVudHMoY2F0ZWdvcmlzZWRVcmxzKTtcblxuLy8gRXN0YWJsaXNoIFN1cGFiYXNlIGlmIGJlaW5nIHVzZWRcbmlmIChkYXRhYmFzZU1vZGUpIHtcbiAgZXN0YWJsaXNoU3VwYWJhc2UoKTtcbiAgXG4gIC8vIEluaXRpYWxpemUgY29uc29saWRhdGVkIHByb2R1Y3RzIHN5c3RlbVxuICBjb25zdCBzdXBhYmFzZVVybCA9IHByb2Nlc3MuZW52LlNVUEFCQVNFX1VSTCE7XG4gIGNvbnN0IHN1cGFiYXNlS2V5ID0gcHJvY2Vzcy5lbnYuU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWSB8fCBwcm9jZXNzLmVudi5TVVBBQkFTRV9BTk9OX0tFWSE7XG4gIGluaXRpYWxpemVDb25zb2xpZGF0ZWRQcm9kdWN0cyhzdXBhYmFzZVVybCwgc3VwYWJhc2VLZXkpO1xufVxuXG4vLyBFc3RhYmxpc2ggcGxheXdyaWdodCBicm93c2VyXG5sZXQgYnJvd3NlcjogcGxheXdyaWdodC5Ccm93c2VyO1xubGV0IHBhZ2U6IHBsYXl3cmlnaHQuUGFnZTtcbmJyb3dzZXIgPSBhd2FpdCBlc3RhYmxpc2hQbGF5d3JpZ2h0UGFnZShoZWFkbGVzc01vZGUpO1xuXG4vLyBTZWxlY3Qgc3RvcmUgbG9jYXRpb25cbmF3YWl0IHNlbGVjdFN0b3JlQnlMb2NhdGlvbk5hbWUoKTtcblxuLy8gTWFpbiBMb29wIC0gU2NyYXBlIHRocm91Z2ggZWFjaCBwYWdlXG5hd2FpdCBzY3JhcGVBbGxQYWdlVVJMcygpO1xuXG4vLyBQcm9ncmFtIEVuZCBhbmQgQ2xlYW51cFxuYnJvd3Nlci5jbG9zZSgpO1xubG9nKFxuICBjb2xvdXIuc2t5LFxuICBgXFxuQWxsIFBhZ2VzIENvbXBsZXRlZCA9IFRvdGFsIFRpbWUgRWxhcHNlZCAke2dldFRpbWVFbGFwc2VkU2luY2Uoc3RhcnRUaW1lKX0gXFxuYFxuKTtcbi8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG5cblxuLy8gbG9hZFVybHNGaWxlXG4vLyAtLS0tLS0tLS0tLS1cbi8vIExvYWRzIGFuZCB2YWxpZGF0ZXMgVVJMcyBmcm9tIGEgdHh0IGZpbGUgdG8gYmUgc2NyYXBlZC5cblxuZnVuY3Rpb24gbG9hZFVybHNGaWxlKGZpbGVQYXRoOiBzdHJpbmcgPSBcInNyYy91cmxzLnR4dFwiKTogQ2F0ZWdvcmlzZWRVcmxbXSB7XG4gIC8vIFRyeSB0byByZWFkIGZpbGUgdXJscy50eHQgb3Igb3RoZXIgZmlsZSBmb3IgYSBsaXN0IG9mIFVSTHNcbiAgY29uc3QgcmF3TGluZXNGcm9tRmlsZTogc3RyaW5nW10gPSByZWFkTGluZXNGcm9tVGV4dEZpbGUoZmlsZVBhdGgpO1xuXG4gIC8vIFBhcnNlIGFuZCBvcHRpbWlzZSBVUkxzXG4gIGxldCBjYXRlZ29yaXNlZFVybHM6IENhdGVnb3Jpc2VkVXJsW10gPSBbXTtcbiAgcmF3TGluZXNGcm9tRmlsZS5tYXAoKGxpbmUpID0+IHtcbiAgICBsZXQgcGFyc2VkVXJscyA9IHBhcnNlQW5kQ2F0ZWdvcmlzZVVSTChsaW5lKTtcbiAgICBpZiAocGFyc2VkVXJscyAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICBjYXRlZ29yaXNlZFVybHMgPSBbLi4uY2F0ZWdvcmlzZWRVcmxzLCAuLi5wYXJzZWRVcmxzXTtcbiAgICB9XG4gIH0pO1xuXG4gIC8vIFJldHVybiBhcyBhbiBhcnJheSBvZiBDYXRlZ29yaXNlZFVybCBvYmplY3RzXG4gIHJldHVybiBjYXRlZ29yaXNlZFVybHM7XG59XG5cbi8vIHNjcmFwZUFsbFBhZ2VVUkxzXG4vLyAtLS0tLS0tLS0tLS0tLS1cbi8vIExvb3BzIHRocm91Z2ggZWFjaCBwYWdlIFVSTCBhbmQgc2NyYXBlcyBwcmljaW5nIGFuZCBvdGhlciBpbmZvLlxuLy8gVGhpcyBpcyB0aGUgbWFpbiBmdW5jdGlvbiB0aGF0IGNhbGxzIHRoZSBvdGhlciBmdW5jdGlvbnMuXG5cbmFzeW5jIGZ1bmN0aW9uIHNjcmFwZUFsbFBhZ2VVUkxzKCkge1xuXG4gIC8vIExvZyBsb29wIHN0YXJ0XG4gIGxvZyhcbiAgICBjb2xvdXIueWVsbG93LFxuICAgIGAke2NhdGVnb3Jpc2VkVXJscy5sZW5ndGh9IHBhZ2VzIHRvIGJlIHNjcmFwZWRgLnBhZEVuZCgzNSkgK1xuICAgIGAke3BhZ2VMb2FkRGVsYXlTZWNvbmRzfXMgZGVsYXkgYmV0d2VlbiBzY3JhcGVzYC5wYWRFbmQoMzUpICtcbiAgICAoZGF0YWJhc2VNb2RlID8gXCIoRGF0YWJhc2UgTW9kZSlcIiA6IFwiKERyeSBSdW4gTW9kZSlcIilcbiAgKTtcblxuICAvLyBMb29wIHRocm91Z2ggZWFjaCBwYWdlIFVSTCB0byBzY3JhcGVcbiAgZm9yIChsZXQgaSA9IDA7IGkgPCBjYXRlZ29yaXNlZFVybHMubGVuZ3RoOyBpKyspIHtcbiAgICBjb25zdCBjYXRlZ29yaXNlZFVybDogQ2F0ZWdvcmlzZWRVcmwgPSBjYXRlZ29yaXNlZFVybHNbaV07XG4gICAgbGV0IHVybDogc3RyaW5nID0gY2F0ZWdvcmlzZWRVcmxzW2ldLnVybDtcblxuICAgIC8vIExvZyBjdXJyZW50IHNjcmFwZSBzZXF1ZW5jZSBhbmQgdGhlIHRvdGFsIG51bWJlciBvZiBwYWdlcyB0byBzY3JhcGVcbiAgICBjb25zdCBzaG9ydFVybCA9IHVybC5yZXBsYWNlKFwiaHR0cHM6Ly9cIiwgXCJcIik7XG4gICAgbG9nKFxuICAgICAgY29sb3VyLnllbGxvdyxcbiAgICAgIGBcXG5bJHtpICsgMX0vJHtjYXRlZ29yaXNlZFVybHMubGVuZ3RofV0gJHtzaG9ydFVybH1gXG4gICAgKTtcblxuICAgIHRyeSB7XG4gICAgICAvLyBPcGVuIHBhZ2Ugd2l0aCB1cmwgb3B0aW9ucyBub3cgc2V0XG4gICAgICBhd2FpdCBwYWdlLmdvdG8odXJsKTtcblxuICAgICAgLy8gV2FpdCBhbmQgcGFnZSBkb3duIG11bHRpcGxlIHRpbWVzIHRvIGZ1cnRoZXIgdHJpZ2dlciBhbnkgbGF6eSBsb2Fkc1xuICAgICAgZm9yIChsZXQgcGFnZURvd24gPSAwOyBwYWdlRG93biA8IDU7IHBhZ2VEb3duKyspIHtcbiAgICAgICAgLy8gY3JlYXRlIGEgcmFuZG9tIG51bWJlciBiZXR3ZWVuIDUwMCBhbmQgMTUwMFxuICAgICAgICBjb25zdCB0aW1lQmV0d2VlblBnRG93bnMgPSBNYXRoLnJhbmRvbSgpICogMTAwMCArIDUwMDtcbiAgICAgICAgYXdhaXQgcGFnZS53YWl0Rm9yVGltZW91dCh0aW1lQmV0d2VlblBnRG93bnMpO1xuICAgICAgICBhd2FpdCBwYWdlLmtleWJvYXJkLnByZXNzKFwiUGFnZURvd25cIik7XG4gICAgICB9XG5cbiAgICAgIC8vIFNldCBwYWdlIHRpbWVvdXQgdG8gMzAgc2Vjb25kc1xuICAgICAgYXdhaXQgcGFnZS5zZXREZWZhdWx0VGltZW91dCgzMDAwMCk7XG5cbiAgICAgIC8vIFdhaXQgZm9yIHByb2R1Y3QtcHJpY2UgaDMgaHRtbCBlbGVtZW50IHRvIGR5bmFtaWNhbGx5IGxvYWQgaW4sXG4gICAgICAvLyAgdGhpcyBpcyByZXF1aXJlZCB0byBzZWUgcHJvZHVjdCBkYXRhXG4gICAgICBhd2FpdCBwYWdlLndhaXRGb3JTZWxlY3RvcihcInByb2R1Y3QtcHJpY2UgaDNcIik7XG5cbiAgICAgIC8vIExvYWQgaHRtbCBpbnRvIENoZWVyaW8gZm9yIERPTSBzZWxlY3Rpb25cbiAgICAgIGNvbnN0IGh0bWwgPSBhd2FpdCBwYWdlLmlubmVySFRNTChcInByb2R1Y3QtZ3JpZFwiKTtcbiAgICAgIGNvbnN0ICQgPSBjaGVlcmlvLmxvYWQoaHRtbCk7XG5cbiAgICAgIC8vIEZpbmQgYWxsIHByb2R1Y3QgZW50cmllc1xuICAgICAgY29uc3QgYWxsUHJvZHVjdEVudHJpZXMgPSAkKFwiY2R4LWNhcmQgcHJvZHVjdC1zdGFtcC1ncmlkIGRpdi5wcm9kdWN0LWVudHJ5XCIpO1xuXG4gICAgICAvLyBGaW5kIGFkdmVydGlzZW1lbnQgcHJvZHVjdCBlbnRyaWVzIG5vdCBub3JtYWxseSBwYXJ0IG9mIHRoaXMgcHJvZHVjdCBjYXRlZ29yeVxuICAgICAgY29uc3QgYWR2ZXJ0aXNlbWVudEVudHJpZXMgPSAkKFwiZGl2LmNhcm91c2VsLXRyYWNrIGRpdiBjZHgtY2FyZCBwcm9kdWN0LXN0YW1wLWdyaWQgZGl2LnByb2R1Y3QtZW50cnlcIilcbiAgICAgIGNvbnN0IGFkSHJlZnM6IHN0cmluZ1tdID0gYWR2ZXJ0aXNlbWVudEVudHJpZXMubWFwKChpbmRleCwgZWxlbWVudCkgPT4ge1xuICAgICAgICByZXR1cm4gJChlbGVtZW50KS5maW5kKFwiYVwiKS5maXJzdCgpLmF0dHIoXCJocmVmXCIpO1xuICAgICAgfSkudG9BcnJheSgpO1xuXG4gICAgICAvLyBGaWx0ZXIgb3V0IHByb2R1Y3QgZW50cmllcyB0aGF0IG1hdGNoIHRoZSBmb3VuZCBhZHZlcnRpc2VtZW50c1xuICAgICAgY29uc3QgcHJvZHVjdEVudHJpZXMgPSBhbGxQcm9kdWN0RW50cmllcy5maWx0ZXIoKGluZGV4LCBlbGVtZW50KSA9PiB7XG4gICAgICAgIGNvbnN0IHByb2R1Y3RIcmVmID0gJChlbGVtZW50KS5maW5kKFwiYVwiKS5maXJzdCgpLmF0dHIoXCJocmVmXCIpO1xuICAgICAgICByZXR1cm4gIWFkSHJlZnMuaW5jbHVkZXMocHJvZHVjdEhyZWYhKTtcbiAgICAgIH0pXG5cbiAgICAgIC8vIExvZyB0aGUgbnVtYmVyIG9mIHByb2R1Y3RzIGZvdW5kLCB0aW1lIGVsYXBzZWQsIGNhdGVnb3J5XG4gICAgICBsb2coXG4gICAgICAgIGNvbG91ci55ZWxsb3csXG4gICAgICAgIGAke3Byb2R1Y3RFbnRyaWVzLmxlbmd0aH0gcHJvZHVjdCBlbnRyaWVzIGZvdW5kYC5wYWRFbmQoMzUpICtcbiAgICAgICAgYFRpbWUgRWxhcHNlZDogJHtnZXRUaW1lRWxhcHNlZFNpbmNlKHN0YXJ0VGltZSl9YC5wYWRFbmQoMzUpICtcbiAgICAgICAgYENhdGVnb3J5OiAke18uc3RhcnRDYXNlKGNhdGVnb3Jpc2VkVXJsLmNhdGVnb3JpZXMuam9pbihcIiAtIFwiKSl9YFxuICAgICAgKTtcblxuICAgICAgLy8gTG9nIHRhYmxlIGhlYWRlclxuICAgICAgaWYgKCFkYXRhYmFzZU1vZGUpIGxvZ1RhYmxlSGVhZGVyKCk7XG5cbiAgICAgIC8vIFN0b3JlIG51bWJlciBvZiBpdGVtcyBwcm9jZXNzZWQgZm9yIGxvZ2dpbmcgcHVycG9zZXNcbiAgICAgIGxldCBwZXJQYWdlTG9nU3RhdHMgPSB7XG4gICAgICAgIG5ld1Byb2R1Y3RzOiAwLFxuICAgICAgICBwcmljZUNoYW5nZWQ6IDAsXG4gICAgICAgIGluZm9VcGRhdGVkOiAwLFxuICAgICAgICBhbHJlYWR5VXBUb0RhdGU6IDAsXG4gICAgICB9XG5cbiAgICAgIC8vIFN0YXJ0IG5lc3RlZCBsb29wIHdoaWNoIGxvb3BzIHRocm91Z2ggZWFjaCBwcm9kdWN0IGVudHJ5XG4gICAgICBwZXJQYWdlTG9nU3RhdHMgPVxuICAgICAgICBhd2FpdCBwcm9jZXNzRm91bmRQcm9kdWN0RW50cmllcyhjYXRlZ29yaXNlZFVybCwgcHJvZHVjdEVudHJpZXMsIHBlclBhZ2VMb2dTdGF0cyk7XG5cbiAgICAgIC8vIEFmdGVyIHNjcmFwaW5nIGV2ZXJ5IGl0ZW0gaXMgY29tcGxldGUsIGxvZyBob3cgbWFueSBwcm9kdWN0cyB3ZXJlIHNjcmFwZWRcbiAgICAgIGlmIChkYXRhYmFzZU1vZGUpIHtcbiAgICAgICAgbG9nKFxuICAgICAgICAgIGNvbG91ci5ibHVlLFxuICAgICAgICAgIGBTdXBhYmFzZTogJHtwZXJQYWdlTG9nU3RhdHMubmV3UHJvZHVjdHN9IG5ldyBwcm9kdWN0cywgYCArXG4gICAgICAgICAgYCR7cGVyUGFnZUxvZ1N0YXRzLnByaWNlQ2hhbmdlZH0gdXBkYXRlZCBwcmljZXMsIGAgK1xuICAgICAgICAgIGAke3BlclBhZ2VMb2dTdGF0cy5pbmZvVXBkYXRlZH0gdXBkYXRlZCBpbmZvLCBgICtcbiAgICAgICAgICBgJHtwZXJQYWdlTG9nU3RhdHMuYWxyZWFkeVVwVG9EYXRlfSBhbHJlYWR5IHVwLXRvLWRhdGVgXG4gICAgICAgICk7XG4gICAgICB9XG5cbiAgICAgIC8vIERlbGF5IGJldHdlZW4gZWFjaCBwYWdlIGxvYWRcbiAgICAgIGF3YWl0IHNldFRpbWVvdXQocGFnZUxvYWREZWxheVNlY29uZHMgKiAxMDAwKTtcblxuICAgIH0gY2F0Y2ggKGVycm9yOiB1bmtub3duKSB7XG4gICAgICBpZiAodHlwZW9mIGVycm9yID09PSAnc3RyaW5nJykge1xuICAgICAgICBpZiAoZXJyb3IuaW5jbHVkZXMoXCJOU19FUlJPUl9DT05ORUNUSU9OX1JFRlVTRURcIikpIHtcbiAgICAgICAgICBsb2dFcnJvcihcIkNvbm5lY3Rpb24gRmFpbGVkIC0gQ2hlY2sgRmlyZXdhbGxcXG5cIiArIGVycm9yKTtcbiAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIGxvZ0Vycm9yKFxuICAgICAgICBcIlBhZ2UgVGltZW91dCBhZnRlciAzMCBzZWNvbmRzIC0gU2tpcHBpbmcgdGhpcyBwYWdlXFxuXCIgKyBlcnJvclxuICAgICAgKTtcbiAgICB9XG4gIH1cbn1cblxuLy8gcHJvY2Vzc0ZvdW5kUHJvZHVjdEVudHJpZXNcbi8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4vLyBMb29wcyB0aHJvdWdoIGVhY2ggcHJvZHVjdCBlbnRyeSBhbmQgc2NyYXBlcyBwcmljaW5nIGFuZCBvdGhlciBpbmZvLlxuLy8gVGhpcyBmdW5jdGlvbiBpcyBjYWxsZWQgYnkgc2NyYXBlQWxsUGFnZVVSTHMuXG5cbmFzeW5jIGZ1bmN0aW9uIHByb2Nlc3NGb3VuZFByb2R1Y3RFbnRyaWVzXG4gIChcbiAgICBjYXRlZ29yaXNlZFVybDogQ2F0ZWdvcmlzZWRVcmwsXG4gICAgcHJvZHVjdEVudHJpZXM6IGNoZWVyaW8uQ2hlZXJpbzxjaGVlcmlvLkVsZW1lbnQ+LFxuICAgIHBlclBhZ2VMb2dTdGF0czoge1xuICAgICAgbmV3UHJvZHVjdHM6IG51bWJlcjtcbiAgICAgIHByaWNlQ2hhbmdlZDogbnVtYmVyO1xuICAgICAgaW5mb1VwZGF0ZWQ6IG51bWJlcjtcbiAgICAgIGFscmVhZHlVcFRvRGF0ZTogbnVtYmVyO1xuICAgIH0pIHtcblxuICAvLyBMb29wIHRocm91Z2ggZWFjaCBwcm9kdWN0IGVudHJ5XG4gIGZvciAobGV0IGkgPSAwOyBpIDwgcHJvZHVjdEVudHJpZXMubGVuZ3RoOyBpKyspIHtcbiAgICBjb25zdCBwcm9kdWN0RW50cnlFbGVtZW50ID0gcHJvZHVjdEVudHJpZXNbaV07XG5cbiAgICBjb25zdCBwcm9kdWN0ID0gcGxheXdyaWdodEVsZW1lbnRUb1Byb2R1Y3QoXG4gICAgICBwcm9kdWN0RW50cnlFbGVtZW50LFxuICAgICAgY2F0ZWdvcmlzZWRVcmwuY2F0ZWdvcmllc1xuICAgICk7XG5cbiAgICBpZiAoZGF0YWJhc2VNb2RlICYmIHByb2R1Y3QgIT09IHVuZGVmaW5lZCkge1xuICAgICAgLy8gSW5zZXJ0IG9yIHVwZGF0ZSBpdGVtIGludG8gU3VwYWJhc2UgKGxlZ2FjeSlcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdXBzZXJ0UHJvZHVjdFRvU3VwYWJhc2UocHJvZHVjdCk7XG5cbiAgICAgIC8vIFByb2Nlc3MgY29uc29saWRhdGVkIHByb2R1Y3QgKG5ldyBzeXN0ZW0pXG4gICAgICBjb25zdCBjb25zb2xpZGF0ZWRQcm9kdWN0SWQgPSBhd2FpdCBwcm9jZXNzQ29uc29saWRhdGVkUHJvZHVjdChwcm9kdWN0KTtcblxuICAgICAgLy8gVXNlIHJlc3BvbnNlIHRvIHVwZGF0ZSBsb2dnaW5nIGNvdW50ZXJzXG4gICAgICBzd2l0Y2ggKHJlc3BvbnNlKSB7XG4gICAgICAgIGNhc2UgVXBzZXJ0UmVzcG9uc2UuQWxyZWFkeVVwVG9EYXRlOlxuICAgICAgICAgIHBlclBhZ2VMb2dTdGF0cy5hbHJlYWR5VXBUb0RhdGUrKztcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSBVcHNlcnRSZXNwb25zZS5JbmZvQ2hhbmdlZDpcbiAgICAgICAgICBwZXJQYWdlTG9nU3RhdHMuaW5mb1VwZGF0ZWQrKztcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSBVcHNlcnRSZXNwb25zZS5OZXdQcm9kdWN0OlxuICAgICAgICAgIHBlclBhZ2VMb2dTdGF0cy5uZXdQcm9kdWN0cysrO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIFVwc2VydFJlc3BvbnNlLlByaWNlQ2hhbmdlZDpcbiAgICAgICAgICBwZXJQYWdlTG9nU3RhdHMucHJpY2VDaGFuZ2VkKys7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgYnJlYWs7XG4gICAgICB9XG5cbiAgICAgIC8vIFVwbG9hZCBpbWFnZSB0byBTdXBhYmFzZSBTdG9yYWdlXG4gICAgICBpZiAodXBsb2FkSW1hZ2VzTW9kZSkge1xuICAgICAgICAvLyBHZXQgaW1hZ2UgdXJsIHVzaW5nIHByb3ZpZGVkIGJhc2UgdXJsLCBwcm9kdWN0IElELCBhbmQgaGktcmVzIHF1ZXJ5IHBhcmFtZXRlcnNcbiAgICAgICAgY29uc3QgaW1hZ2VVcmxCYXNlID1cbiAgICAgICAgICBcImh0dHBzOi8vYXNzZXRzLndvb2x3b3J0aHMuY29tLmF1L2ltYWdlcy8yMDEwL1wiO1xuICAgICAgICBjb25zdCBpbWFnZVVybEV4dGVuc2lvbkFuZFF1ZXJ5UGFyYW1zID1cbiAgICAgICAgICBcIi5qcGc/aW1wb2xpY3k9d293Y2R4d2JqYngmdz05MDAmaD05MDBcIjtcbiAgICAgICAgY29uc3QgaW1hZ2VVcmwgPVxuICAgICAgICAgIGltYWdlVXJsQmFzZSArIHByb2R1Y3QuaWQgKyBpbWFnZVVybEV4dGVuc2lvbkFuZFF1ZXJ5UGFyYW1zO1xuXG4gICAgICAgIGF3YWl0IHVwbG9hZEltYWdlVG9TdXBhYmFzZShpbWFnZVVybCEsIHByb2R1Y3QpO1xuICAgICAgfVxuICAgIH0gZWxzZSBpZiAoIWRhdGFiYXNlTW9kZSAmJiBwcm9kdWN0ICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIC8vIFdoZW4gZG9pbmcgYSBkcnkgcnVuLCBsb2cgcHJvZHVjdCBuYW1lIC0gc2l6ZSAtIHByaWNlIGluIHRhYmxlIGZvcm1hdFxuICAgICAgbG9nUHJvZHVjdFJvdyhwcm9kdWN0ISk7XG4gICAgfVxuXG4gICAgLy8gQWRkIGEgdGlueSBkZWxheSBiZXR3ZWVuIGVhY2ggcHJvZHVjdCBsb29wLlxuICAgIC8vIFRoaXMgbWFrZXMgcHJpbnRpbmcgdGhlIGxvZyBtb3JlIHJlYWRhYmxlXG4gICAgYXdhaXQgc2V0VGltZW91dChwcm9kdWN0TG9nRGVsYXlNaWxsaVNlY29uZHMpO1xuICB9XG5cbiAgLy8gUmV0dXJuIGxvZyBzdGF0cyBmb3IgY29tcGxldGVkIHBhZ2VcbiAgcmV0dXJuIHBlclBhZ2VMb2dTdGF0cztcbn1cblxuXG4vLyBoYW5kbGVBcmd1bWVudHMoKVxuLy8gLS0tLS0tLS0tLS0tLS0tLS1cbi8vIEhhbmRsZSBjb21tYW5kIGxpbmUgYXJndW1lbnRzLiBDYW4gYmUgcmV2ZXJzZSBtb2RlLCBkcnktcnVuLW1vZGUsIGN1c3RvbSB1cmwsIG9yIGNhdGVnb3JpZXNcblxuZnVuY3Rpb24gaGFuZGxlQXJndW1lbnRzKGNhdGVnb3Jpc2VkVXJscyk6IENhdGVnb3Jpc2VkVXJsW10ge1xuICBpZiAocHJvY2Vzcy5hcmd2Lmxlbmd0aCA+IDIpIHtcbiAgICAvLyBTbGljZSBvdXQgdGhlIGZpcnN0IDIgYXJndW1lbnRzLCBhcyB0aGV5IGFyZSBub3QgdXNlci1wcm92aWRlZFxuICAgIGNvbnN0IHVzZXJBcmdzID0gcHJvY2Vzcy5hcmd2LnNsaWNlKDIsIHByb2Nlc3MuYXJndi5sZW5ndGgpO1xuXG4gICAgLy8gTG9vcCB0aHJvdWdoIGFsbCBhcmdzIGFuZCBmaW5kIGFueSBtYXRjaGluZyBrZXl3b3Jkc1xuICAgIGxldCBwb3RlbnRpYWxVcmwgPSBcIlwiO1xuICAgIHVzZXJBcmdzLmZvckVhY2goYXN5bmMgKGFyZykgPT4ge1xuICAgICAgaWYgKGFyZyA9PT0gXCJkYlwiKSBkYXRhYmFzZU1vZGUgPSB0cnVlO1xuICAgICAgZWxzZSBpZiAoYXJnID09PSBcImltYWdlc1wiKSB1cGxvYWRJbWFnZXNNb2RlID0gdHJ1ZTtcbiAgICAgIGVsc2UgaWYgKGFyZyA9PT0gXCJoZWFkbGVzc1wiKSBoZWFkbGVzc01vZGUgPSB0cnVlIC8vIGlzIGFscmVhZHkgZGVmYXVsdFxuICAgICAgZWxzZSBpZiAoYXJnID09PSBcImhlYWRlZFwiKSBoZWFkbGVzc01vZGUgPSBmYWxzZVxuXG4gICAgICAvLyBBbnkgYXJnIGNvbnRhaW5pbmcgLmNvLm56IHdpbGwgcmVwbGFjZWQgdGhlIFVSTHMgdGV4dCBmaWxlIHRvIGJlIHNjcmFwZWQuXG4gICAgICBlbHNlIGlmIChhcmcuaW5jbHVkZXMoXCIuY28ubnpcIikpIHBvdGVudGlhbFVybCArPSBhcmc7XG5cbiAgICAgIC8vIFJldmVyc2UgdGhlIG9yZGVyIG9mIHRoZSBVUkxzIHRvIGJlIHNjcmFwZWQsIHN0YXJ0aW5nIGZyb20gdGhlIGJvdHRvbVxuICAgICAgZWxzZSBpZiAoYXJnID09PSBcInJldmVyc2VcIikgY2F0ZWdvcmlzZWRVcmxzID0gY2F0ZWdvcmlzZWRVcmxzLnJldmVyc2UoKTtcbiAgICAgIC8vIGVsc2UgaWYgKGFyZyA9PT0gXCJjdXN0b21cIikge1xuICAgICAgLy8gICBjYXRlZ29yaXNlZFVybHMgPSBbXTtcbiAgICAgIC8vICAgYXdhaXQgY3VzdG9tUXVlcnkoKTtcbiAgICAgIC8vICAgcHJvY2Vzcy5leGl0KCk7XG4gICAgICAvLyB9XG4gICAgfSk7XG5cbiAgICAvLyBUcnkgdG8gcGFyc2UgdGhlIHBvdGVudGlhbCBuZXcgdXJsXG4gICAgY29uc3QgcGFyc2VkVXJsID0gcGFyc2VBbmRDYXRlZ29yaXNlVVJMKHBvdGVudGlhbFVybCk7XG4gICAgaWYgKHBhcnNlZFVybCAhPT0gdW5kZWZpbmVkKSBjYXRlZ29yaXNlZFVybHMgPSBbcGFyc2VkVXJsXTtcbiAgfVxuICByZXR1cm4gY2F0ZWdvcmlzZWRVcmxzO1xufVxuXG4vLyBlc3RhYmxpc2hQbGF5d3JpZ2h0UGFnZSgpXG4vLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4vLyBDcmVhdGUgYSBwbGF5d3JpZ2h0IGJyb3dzZXJcblxuYXN5bmMgZnVuY3Rpb24gZXN0YWJsaXNoUGxheXdyaWdodFBhZ2UoaGVhZGxlc3MgPSB0cnVlKSB7XG4gIGxvZyhcbiAgICBjb2xvdXIueWVsbG93LFxuICAgIFwiTGF1bmNoaW5nIEJyb3dzZXIuLiBcIiArXG4gICAgKHByb2Nlc3MuYXJndi5sZW5ndGggPiAyXG4gICAgICA/IFwiKFwiICsgKHByb2Nlc3MuYXJndi5sZW5ndGggLSAyKSArIFwiIGFyZ3VtZW50cyBmb3VuZClcIlxuICAgICAgOiBcIlwiKVxuICApO1xuICBicm93c2VyID0gYXdhaXQgcGxheXdyaWdodC5maXJlZm94LmxhdW5jaCh7XG4gICAgaGVhZGxlc3M6IGhlYWRsZXNzLFxuICB9KTtcbiAgcGFnZSA9IGF3YWl0IGJyb3dzZXIubmV3UGFnZSgpO1xuXG4gIC8vIFJlamVjdCB1bm5lY2Vzc2FyeSBhZC90cmFja2luZyB1cmxzXG4gIGF3YWl0IHJvdXRlUGxheXdyaWdodEV4Y2x1c2lvbnMoKTtcblxuICByZXR1cm4gYnJvd3Nlcjtcbn1cblxuLy8gc2VsZWN0U3RvcmVCeUxvY2F0aW9uTmFtZSgpXG4vLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbi8vIFNlbGVjdHMgYSBzdG9yZSBsb2NhdGlvbiBieSB0eXBpbmcgaW4gdGhlIHNwZWNpZmllZCBsb2NhdGlvbiBhZGRyZXNzXG5cbmFzeW5jIGZ1bmN0aW9uIHNlbGVjdFN0b3JlQnlMb2NhdGlvbk5hbWUobG9jYXRpb25OYW1lOiBzdHJpbmcgPSBcIlwiKSB7XG4gIC8vIElmIG5vIGxvY2F0aW9uIHdhcyBwYXNzZWQgaW4sIGFsc28gY2hlY2sgLmVudiBmb3IgU1RPUkVfTkFNRVxuICBpZiAobG9jYXRpb25OYW1lID09PSBcIlwiKSB7XG4gICAgaWYgKHByb2Nlc3MuZW52LlNUT1JFX05BTUUpIGxvY2F0aW9uTmFtZSA9IHByb2Nlc3MuZW52LlNUT1JFX05BTUU7XG4gICAgLy8gSWYgU1RPUkVfTkFNRSBpcyBhbHNvIG5vdCBwcmVzZW50LCBza2lwIHN0b3JlIGxvY2F0aW9uIHNlbGVjdGlvblxuICAgIGVsc2UgcmV0dXJuO1xuICB9XG5cbiAgbG9nKGNvbG91ci55ZWxsb3csIFwiU2VsZWN0aW5nIFN0b3JlIExvY2F0aW9uLi5cIik7XG5cbiAgLy8gT3BlbiBzdG9yZSBzZWxlY3Rpb24gcGFnZVxuICB0cnkge1xuICAgIGF3YWl0IHBhZ2Uuc2V0RGVmYXVsdFRpbWVvdXQoMTIwMDApO1xuICAgIGF3YWl0IHBhZ2UuZ290byhcImh0dHBzOi8vd3d3Lndvb2x3b3J0aHMuY28ubnovYm9va2F0aW1lc2xvdFwiLCB7XG4gICAgICB3YWl0VW50aWw6IFwiZG9tY29udGVudGxvYWRlZFwiLFxuICAgIH0pO1xuICAgIGF3YWl0IHBhZ2Uud2FpdEZvclNlbGVjdG9yKFwiZmllbGRzZXQgZGl2IGRpdiBwIGJ1dHRvblwiKTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBsb2dFcnJvcihcIkxvY2F0aW9uIHNlbGVjdGlvbiBwYWdlIHRpbWVkIG91dCAtIFVzaW5nIGRlZmF1bHQgbG9jYXRpb24gaW5zdGVhZFwiKTtcbiAgICByZXR1cm47XG4gIH1cblxuICBjb25zdCBvbGRMb2NhdGlvbiA9IGF3YWl0IHBhZ2VcbiAgICAubG9jYXRvcihcImZpZWxkc2V0IGRpdiBkaXYgcCBzdHJvbmdcIilcbiAgICAuaW5uZXJUZXh0KCk7XG5cbiAgLy8gQ2xpY2sgY2hhbmdlIGFkZHJlc3MgbW9kYWxcbiAgYXdhaXQgcGFnZS5sb2NhdG9yKFwiZmllbGRzZXQgZGl2IGRpdiBwIGJ1dHRvblwiKS5jbGljaygpO1xuICBhd2FpdCBwYWdlLndhaXRGb3JTZWxlY3RvcihcImZvcm0tc3VidXJiLWF1dG9jb21wbGV0ZSBmb3JtLWlucHV0IGlucHV0XCIpO1xuICB0cnkge1xuICAgIC8vIFR5cGUgaW4gYWRkcmVzcywgd2FpdCAxLjVzIGZvciBhdXRvLWNvbXBsZXRlIHRvIHBvcHVsYXRlIGVudHJpZXNcbiAgICBhd2FpdCBwYWdlXG4gICAgICAubG9jYXRvcihcImZvcm0tc3VidXJiLWF1dG9jb21wbGV0ZSBmb3JtLWlucHV0IGlucHV0XCIpXG4gICAgICAudHlwZShsb2NhdGlvbk5hbWUpO1xuICAgIGF3YWl0IHBhZ2Uud2FpdEZvclRpbWVvdXQoMTUwMCk7XG5cbiAgICAvLyBTZWxlY3QgZmlyc3QgbWF0Y2hlZCBlbnRyeSwgd2FpdCBmb3IgdmFsaWRhdGlvblxuICAgIGF3YWl0IHBhZ2Uua2V5Ym9hcmQucHJlc3MoXCJBcnJvd0Rvd25cIik7XG4gICAgYXdhaXQgcGFnZS53YWl0Rm9yVGltZW91dCgzMDApO1xuICAgIGF3YWl0IHBhZ2Uua2V5Ym9hcmQucHJlc3MoXCJFbnRlclwiKTtcbiAgICBhd2FpdCBwYWdlLndhaXRGb3JUaW1lb3V0KDEwMDApO1xuXG4gICAgLy8gQ2xpY2sgc2F2ZSBsb2NhdGlvbiBidXR0b25cbiAgICBhd2FpdCBwYWdlLmdldEJ5VGV4dChcIlNhdmUgYW5kIENvbnRpbnVlIFNob3BwaW5nXCIpLmNsaWNrKCk7XG4gICAgbG9nKFxuICAgICAgY29sb3VyLnllbGxvdyxcbiAgICAgIFwiQ2hhbmdlZCBMb2NhdGlvbiBmcm9tIFwiICsgb2xkTG9jYXRpb24gKyBcIiB0byBcIiArIGxvY2F0aW9uTmFtZSArIFwiXFxuXCJcbiAgICApO1xuXG4gICAgLy8gRW5zdXJlIGxvY2F0aW9uIGlzIHNhdmVkIGJlZm9yZSBtb3Zpbmcgb25cbiAgICBhd2FpdCBwYWdlLndhaXRGb3JUaW1lb3V0KDIwMDApO1xuICB9IGNhdGNoIHtcbiAgICAvLyBDYXRjaCB0aW1lb3V0IGlmIG5vIGxvY2F0aW9ucyBhcmUgZm91bmQgdXNpbmcgdGhlIHByb3ZpZGVkIGVudiB2YWx1ZS5cbiAgICBsb2dFcnJvcihcbiAgICAgIGBTdG9yZSBMb2NhdGlvbjoke2xvY2F0aW9uTmFtZX0gbm90IGZvdW5kLiBVc2luZyBkZWZhdWx0IGluc3RlYWQuYFxuICAgICk7XG4gIH1cbn1cblxuLy8gcGxheXdyaWdodEVsZW1lbnRUb1Byb2R1Y3QoKVxuLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuLy8gVGFrZXMgYSBwbGF5d3JpZ2h0IGh0bWwgZWxlbWVudCBmb3IgJ2EucHJvZHVjdC1lbnRyeScsIGJ1aWxkcyBhbmQgcmV0dXJucyBhIFByb2R1Y3RcblxuZXhwb3J0IGZ1bmN0aW9uIHBsYXl3cmlnaHRFbGVtZW50VG9Qcm9kdWN0KFxuICBlbGVtZW50OiBjaGVlcmlvLkVsZW1lbnQsXG4gIGNhdGVnb3JpZXM6IHN0cmluZ1tdXG4pOiBQcm9kdWN0IHwgdW5kZWZpbmVkIHtcbiAgY29uc3QgJCA9IGNoZWVyaW8ubG9hZChlbGVtZW50KTtcblxuICAvLyBGaW5kIHRoZSA8aDM+IHRhZyB3aXRoIGFuIGlkIGNvbnRhaW5pbmcgXCItdGl0bGVcIlxuICAvLyBUaGlzIGhvbGRzIHRoZSBwcm9kdWN0IElELCBuYW1lIGFuZCBzaXplXG4gIGxldCBpZE5hbWVTaXplSDMgPSAkKGVsZW1lbnQpLmZpbmQoXCJoM1wiKS5maWx0ZXIoKGksIGVsZW1lbnQpID0+IHtcbiAgICBpZiAoJChlbGVtZW50KS5hdHRyKFwiaWRcIik/LmluY2x1ZGVzKFwiLXRpdGxlXCIpKSB7XG4gICAgICByZXR1cm4gdHJ1ZVxuICAgIH0gZWxzZSByZXR1cm4gZmFsc2U7XG4gIH0pO1xuXG4gIGxldCBwcm9kdWN0OiBQcm9kdWN0ID0ge1xuXG4gICAgLy8gSURcbiAgICAvLyAtLS0tLS0tXG4gICAgLy8gRXh0cmFjdCBwcm9kdWN0IElEIGZyb20gaDMgaWQgYXR0cmlidXRlLCBhbmQgcmVtb3ZlIG5vbi1udW1iZXJzXG4gICAgaWQ6IGlkTmFtZVNpemVIMy5hdHRyKFwiaWRcIik/LnJlcGxhY2UoL1xcRC9nLCBcIlwiKSBhcyBzdHJpbmcsXG5cbiAgICAvLyBTb3VyY2UgU2l0ZSAtIHNldCB3aGVyZSB0aGUgc291cmNlIG9mIGluZm9ybWF0aW9uIGNhbWUgZnJvbVxuICAgIHNvdXJjZVNpdGU6IFwiY291bnRkb3duLmNvLm56XCIsIC8vIHVzZSBjb3VudGRvd24gZm9yIGNvbnNpc3RlbmN5IHdpdGggb2xkIGRhdGFcblxuICAgIC8vIENhdGVnb3JpZXNcbiAgICBjYXRlZ29yeTogY2F0ZWdvcmllcywgIC8vIGFscmVhZHkgb2J0YWluZWQgZnJvbSB1cmwvdGV4dCBmaWxlXG5cbiAgICAvLyBTdG9yZSB0b2RheSdzIGRhdGVcbiAgICBsYXN0Q2hlY2tlZDogbmV3IERhdGUoKSxcbiAgICBsYXN0VXBkYXRlZDogbmV3IERhdGUoKSxcblxuICAgIC8vIFRoZXNlIHZhbHVlcyB3aWxsIGxhdGVyIGJlIG92ZXJ3cml0dGVuXG4gICAgbmFtZTogXCJcIixcbiAgICBwcmljZUhpc3Rvcnk6IFtdLFxuICAgIGN1cnJlbnRQcmljZTogMCxcbiAgfTtcblxuICAvLyBOYW1lICYgU2l6ZVxuICAvLyAtLS0tLS0tLS0tLS1cbiAgLy8gVHJ5IHRvIGV4dHJhY3QgY29tYmluZWQgbmFtZSBhbmQgc2l6ZSBmcm9tIGgzIHRhZyBpbm5lciB0ZXh0XG4gIGxldCByYXdOYW1lQW5kU2l6ZSA9IGlkTmFtZVNpemVIMy50ZXh0KCkudHJpbSgpO1xuXG4gIC8vIENsZWFuIHVubmVjZXNzYXJ5IHdvcmRzIGZyb20gdGl0bGVzXG4gIHJhd05hbWVBbmRTaXplID0gcmF3TmFtZUFuZFNpemVcbiAgICAudG9Mb3dlckNhc2UoKVxuICAgIC5yZXBsYWNlKFwiICBcIiwgXCIgXCIpXG4gICAgLnJlcGxhY2UoXCJmcmVzaCBmcnVpdFwiLCBcIlwiKVxuICAgIC5yZXBsYWNlKFwiZnJlc2ggdmVnZXRhYmxlXCIsIFwiXCIpXG4gICAgLnRyaW0oKVxuICAgIDtcblxuICAvLyBUcnkgdG8gcmVnZXggbWF0Y2ggYSBzaXplIHNlY3Rpb24gc3VjaCBhczpcbiAgLy8gMTAwZywgMTUwbWwsIDE2cGFjaywgMC41LTEuNWtnLCB0cmF5IDFrZywgZXRjXG4gIGxldCB0cnlNYXRjaFNpemUgPVxuICAgIHJhd05hbWVBbmRTaXplLm1hdGNoKC8odHJheVxcc1xcZCspfChcXGQrKFxcLlxcZCspPyhcXC1cXGQrXFwuXFxkKyk/XFxzPyhnfGtnfGx8bWx8cGFjaykpXFxiL2cpO1xuXG4gIGlmICghdHJ5TWF0Y2hTaXplKSB7XG4gICAgLy8gQ2FwaXRhbGlzZSBhbmQgc2V0IG5hbWVcbiAgICBwcm9kdWN0Lm5hbWUgPSB0b1RpdGxlQ2FzZShyYXdOYW1lQW5kU2l6ZSk7XG5cbiAgICAvLyBObyBzaXplIHdhcyBmb3VuZCBpbiBuYW1lLCBzaXplIGNhbiBiZSBkZXJpdmVkIGZyb20gdW5pdCBwcmljZSBsYXRlclxuICAgIHByb2R1Y3Quc2l6ZSA9IFwiXCI7XG4gIH0gZWxzZSB7XG4gICAgLy8gSWYgYSBzaXplIHdhcyBmb3VuZCwgZ2V0IHRoZSBpbmRleCB0byBzcGxpdCB0aGUgc3RyaW5nIGludG8gbmFtZSBhbmQgc2l6ZVxuICAgIGxldCBpbmRleE9mU2l6ZVNlY3Rpb24gPSByYXdOYW1lQW5kU2l6ZS5pbmRleE9mKHRyeU1hdGNoU2l6ZVswXSk7XG5cbiAgICAvLyBDYXBpdGFsaXNlIGFuZCBzZXQgbmFtZVxuICAgIHByb2R1Y3QubmFtZSA9IHRvVGl0bGVDYXNlKHJhd05hbWVBbmRTaXplLnNsaWNlKDAsIGluZGV4T2ZTaXplU2VjdGlvbikpLnRyaW0oKTtcblxuICAgIC8vIENsZWFuIHVwIGFuZCBzZXQgc2l6ZVxuICAgIGxldCBjbGVhbmVkU2l6ZSA9IHJhd05hbWVBbmRTaXplLnNsaWNlKGluZGV4T2ZTaXplU2VjdGlvbikudHJpbSgpO1xuICAgIGlmIChjbGVhbmVkU2l6ZS5tYXRjaCgvXFxkK2xcXGIvKSkge1xuICAgICAgLy8gQ2FwaXRhbGlzZSBMIGZvciBsaXRyZXNcbiAgICAgIGNsZWFuZWRTaXplID0gY2xlYW5lZFNpemUucmVwbGFjZShcImxcIiwgXCJMXCIpO1xuICAgIH1cbiAgICBjbGVhbmVkU2l6ZS5yZXBsYWNlKFwidHJheVwiLCBcIlRyYXlcIik7XG4gICAgcHJvZHVjdC5zaXplID0gY2xlYW5lZFNpemU7XG4gIH1cblxuICAvLyBQcmljZVxuICAvLyAtLS0tLS1cbiAgLy8gSXMgb3JpZ2luYWxseSBkaXNwbGF5ZWQgd2l0aCBkb2xsYXJzIGluIGFuIDxlbT4sIGNlbnRzIGluIGEgPHNwYW4+LFxuICAvLyBhbmQgcG90ZW50aWFsbHkgYSBrZyB1bml0IG5hbWUgaW5zaWRlIHRoZSA8c3Bhbj4gZm9yIHNvbWUgbWVhdCBwcm9kdWN0cy5cbiAgLy8gVGhlIDIgbnVtYmVycyBhcmUgam9pbmVkLCBwYXJzZWQsIGFuZCBub24tbnVtYmVyIGNoYXJzIGFyZSByZW1vdmVkLlxuICBjb25zdCBkb2xsYXJTdHJpbmc6IHN0cmluZyA9ICQoZWxlbWVudClcbiAgICAuZmluZChcInByb2R1Y3QtcHJpY2UgZGl2IGgzIGVtXCIpXG4gICAgLnRleHQoKVxuICAgIC50cmltKCk7XG4gIGxldCBjZW50U3RyaW5nOiBzdHJpbmcgPSAkKGVsZW1lbnQpXG4gICAgLmZpbmQoXCJwcm9kdWN0LXByaWNlIGRpdiBoMyBzcGFuXCIpXG4gICAgLnRleHQoKVxuICAgIC50cmltKCk7XG4gIGlmIChjZW50U3RyaW5nLmluY2x1ZGVzKFwia2dcIikpIHByb2R1Y3Quc2l6ZSA9IFwicGVyIGtnXCI7XG4gIGNlbnRTdHJpbmcgPSBjZW50U3RyaW5nLnJlcGxhY2UoL1xcRC9nLCBcIlwiKTtcbiAgcHJvZHVjdC5jdXJyZW50UHJpY2UgPSBOdW1iZXIoZG9sbGFyU3RyaW5nICsgXCIuXCIgKyBjZW50U3RyaW5nKTtcblxuICAvLyBDcmVhdGUgYSBkYXRlIG9iamVjdCBmb3Igbm93LCBidXQgd2l0aCBtaW51dGVzIGFuZCBzZWNvbmRzIHNldCB0byAwXG4gIGNvbnN0IHRvZGF5ID0gbmV3IERhdGUoKTtcbiAgdG9kYXkuc2V0TWludXRlcygwKTtcbiAgdG9kYXkuc2V0U2Vjb25kcygwKTtcblxuICAvLyBDcmVhdGUgYSBEYXRlZFByaWNlIG9iamVjdCwgd2hpY2ggbWF5IGJlIGFkZGVkIGludG8gdGhlIHByb2R1Y3QgaWYgbmVlZGVkXG4gIGNvbnN0IHRvZGF5c0RhdGVkUHJpY2U6IERhdGVkUHJpY2UgPSB7XG4gICAgZGF0ZTogdG9kYXksXG4gICAgcHJpY2U6IHByb2R1Y3QuY3VycmVudFByaWNlLFxuICB9O1xuICBwcm9kdWN0LnByaWNlSGlzdG9yeSA9IFt0b2RheXNEYXRlZFByaWNlXTtcblxuICAvLyBVbml0IFByaWNlXG4gIC8vIC0tLS0tLS0tLS0tXG4gIC8vIFRyeSB0byBleHRyYWN0IGZyb20gc3Bhbi5jdXBQcmljZSwgZXg6ICQyLjUyIC8gMTAwbUxcbiAgY29uc3QgcmF3VW5pdFByaWNlID0gJChlbGVtZW50KS5maW5kKFwic3Bhbi5jdXBQcmljZVwiKS50ZXh0KCkudHJpbSgpO1xuXG4gIGlmIChyYXdVbml0UHJpY2UpIHtcbiAgICAvLyBFeHRyYWN0IGFuZCBwYXJzZSB1bml0IHByaWNlLCBleDogMi41MlxuICAgIGNvbnN0IHVuaXRQcmljZVN0cmluZyA9IHJhd1VuaXRQcmljZS5zcGxpdChcIi9cIilbMF0ucmVwbGFjZShcIiRcIiwgXCJcIikudHJpbSgpO1xuICAgIGxldCB1bml0UHJpY2UgPSBOdW1iZXIucGFyc2VGbG9hdCh1bml0UHJpY2VTdHJpbmcpO1xuXG4gICAgLy8gRXh0cmFjdCBhbW91bnQgYW5kIHVuaXQsIGV4OiAxMDBtTFxuICAgIGNvbnN0IGFtb3VudEFuZFVuaXQgPSByYXdVbml0UHJpY2Uuc3BsaXQoXCIvXCIpWzFdLnRyaW0oKTtcblxuICAgIC8vIFBhcnNlIGFtb3VudCwgZXg6IDEwMFxuICAgIGxldCBhbW91bnQgPSBOdW1iZXIucGFyc2VJbnQoYW1vdW50QW5kVW5pdC5tYXRjaCgvXFxkKy9nKT8uWzBdIHx8IFwiXCIpO1xuXG4gICAgLy8gRXh0cmFjdCB1bml0LCBleDogbUxcbiAgICBsZXQgdW5pdCA9IGFtb3VudEFuZFVuaXQubWF0Y2goL1xcdysvZyk/LlswXSB8fCBcIlwiXG5cbiAgICAvLyBOb3JtYWxpemUgdW5pdHMgdG8ga2cgb3IgTFxuICAgIGlmIChhbW91bnRBbmRVbml0ID09IFwiMTAwZ1wiKSB7XG4gICAgICBhbW91bnQgPSBhbW91bnQgKiAxMDtcbiAgICAgIHVuaXRQcmljZSA9IHVuaXRQcmljZSAqIDEwO1xuICAgICAgdW5pdCA9IFwia2dcIjtcbiAgICB9XG4gICAgZWxzZSBpZiAoYW1vdW50QW5kVW5pdCA9PSBcIjEwMG1MXCIpIHtcbiAgICAgIGFtb3VudCA9IGFtb3VudCAqIDEwO1xuICAgICAgdW5pdFByaWNlID0gdW5pdFByaWNlICogMTA7XG4gICAgICB1bml0ID0gXCJMXCI7XG4gICAgfVxuXG4gICAgLy8gQ2xlYW51cCAxa2cgdG8ganVzdCBrZ1xuICAgIHVuaXQgPSB1bml0LnJlcGxhY2UoXCIxa2dcIiwgXCJrZ1wiKTtcbiAgICB1bml0ID0gdW5pdC5yZXBsYWNlKFwiMUxcIiwgXCJMXCIpO1xuXG4gICAgLy8gU2V0IGZpbmFsaXNlZCB1bml0IHByaWNlIGFuZCBuYW1lXG4gICAgcHJvZHVjdC51bml0UHJpY2UgPSB1bml0UHJpY2U7XG4gICAgcHJvZHVjdC51bml0TmFtZSA9IHVuaXQ7XG4gIH1cblxuICAvLyBPdmVycmlkZXNcbiAgLy8gLS0tLS0tLS0tLVxuICAvLyBDaGVjayAudHMgZmlsZSBmb3IgbWFudWFsbHkgb3ZlcnJpZGRlbiBwcm9kdWN0IGRhdGFcbiAgcHJvZHVjdE92ZXJyaWRlcy5mb3JFYWNoKChvdmVycmlkZSkgPT4ge1xuICAgIC8vIEZpcnN0IGNoZWNrIGlmIHByb2R1Y3QgSUQgaGFzIGFueSBvdmVycmlkZXNcbiAgICBpZiAob3ZlcnJpZGUuaWQgPT09IHByb2R1Y3QuaWQpIHtcbiAgICAgIC8vIENoZWNrIGZvciBzaXplIG92ZXJyaWRlXG4gICAgICBpZiAob3ZlcnJpZGUuc2l6ZSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIHByb2R1Y3Quc2l6ZSA9IG92ZXJyaWRlLnNpemU7XG4gICAgICB9XG5cbiAgICAgIC8vIENoZWNrIGZvciBjYXRlZ29yeSBvdmVycmlkZVxuICAgICAgaWYgKG92ZXJyaWRlLmNhdGVnb3J5ICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgcHJvZHVjdC5jYXRlZ29yeSA9IFtvdmVycmlkZS5jYXRlZ29yeV07XG4gICAgICB9XG4gICAgfVxuICB9KTtcblxuICAvLyBWYWxpZGF0aW9uXG4gIC8vIC0tLS0tLS0tLS1cbiAgLy8gSWYgcHJvZHVjdCB2YWx1ZXMgcGFzcyB2YWxpZGF0aW9uLCByZXR1cm4gcHJvZHVjdFxuICBpZiAodmFsaWRhdGVQcm9kdWN0KHByb2R1Y3QpKSByZXR1cm4gcHJvZHVjdDtcbiAgZWxzZSB7XG4gICAgdHJ5IHtcbiAgICAgIGxvZ0Vycm9yKFxuICAgICAgICBgICBVbmFibGUgdG8gU2NyYXBlOiAke3Byb2R1Y3QuaWQucGFkU3RhcnQoNil9IHwgJHtwcm9kdWN0Lm5hbWV9IHwgYCArXG4gICAgICAgIGAkJHtwcm9kdWN0LmN1cnJlbnRQcmljZX1gXG4gICAgICApO1xuICAgIH0gY2F0Y2gge1xuICAgICAgbG9nRXJyb3IoXCIgIFVuYWJsZSB0byBTY3JhcGUgSUQgZnJvbSBwcm9kdWN0XCIpO1xuICAgIH1cbiAgICByZXR1cm4gdW5kZWZpbmVkO1xuICB9XG59XG5cbi8vIHZhbGlkYXRlUHJvZHVjdCgpXG4vLyAtLS0tLS0tLS0tLS0tLS0tLVxuLy8gQ2hlY2tzIHNjcmFwZWQgcHJvZHVjdCB2YWx1ZXMgYXJlIHdpdGhpbiByZWFzb25hYmxlIHJhbmdlc1xuXG5mdW5jdGlvbiB2YWxpZGF0ZVByb2R1Y3QocHJvZHVjdDogUHJvZHVjdCk6IGJvb2xlYW4ge1xuICB0cnkge1xuICAgIGlmIChwcm9kdWN0Lm5hbWUubWF0Y2goL1xcJFxcc1xcZCsvKSkgcmV0dXJuIGZhbHNlO1xuICAgIGlmIChwcm9kdWN0Lm5hbWUubGVuZ3RoIDwgNCB8fCBwcm9kdWN0Lm5hbWUubGVuZ3RoID4gMTAwKSByZXR1cm4gZmFsc2U7XG4gICAgaWYgKHByb2R1Y3QuaWQubGVuZ3RoIDwgMiB8fCBwcm9kdWN0LmlkLmxlbmd0aCA+IDIwKSByZXR1cm4gZmFsc2U7XG4gICAgaWYgKFxuICAgICAgcHJvZHVjdC5jdXJyZW50UHJpY2UgPD0gMCB8fFxuICAgICAgcHJvZHVjdC5jdXJyZW50UHJpY2UgPT09IG51bGwgfHxcbiAgICAgIHByb2R1Y3QuY3VycmVudFByaWNlID09PSB1bmRlZmluZWQgfHxcbiAgICAgIE51bWJlci5pc05hTihwcm9kdWN0LmN1cnJlbnRQcmljZSkgfHxcbiAgICAgIHByb2R1Y3QuY3VycmVudFByaWNlID4gOTk5XG4gICAgKSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIHJldHVybiB0cnVlO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxufVxuXG4vLyBwYXJzZUFuZENhdGVnb3Jpc2VVUkwoKVxuLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbi8vIFBhcnNlcyBhIFVSTCBzdHJpbmcsIGFuIG9wdGlvbmFsIGNhdGVnb3J5LCBvcHRpb25hbCBudW1iZXIgb2YgcGFnZXMgdG8gc2NyYXBlXG4vLyAgZnJvbSBhIHNpbmdsZSBsaW5lIG9mIHRleHQuXG4vLyBSZXR1cm5zIHVuZGVmaW5lZCBpZiBub3QgYSB2YWxpZCBVUkxcbi8vIEV4YW1wbGUgSW5wdXQ6XG4vLyAgICB3b29sd29ydGhzLmNvLm56L3Nob3AvYnJvd3NlL2Zyb3plbi9pY2UtY3JlYW0tc29yYmV0L3R1YnMgY2F0ZWdvcnk9aWNlLWNyZWFtIHBhZ2VzPTJcbi8vIEV4YW1wbGUgUmV0dXJuOlxuLy8gIFtcbi8vICAgIHtcbi8vICAgICAgICB1cmw6IFwiaHR0cHM6Ly93b29sd29ydGhzLmNvLm56L3Nob3AvYnJvd3NlL2Zyb3plbi9pY2UtY3JlYW0tc29yYmV0L3R1YnM/cGFnZT0xJmluU3RvY2tQcm9kdWN0c09ubHk9dHJ1ZVwiXG4vLyAgICAgICAgY2F0ZWdvcnk6IFwiaWNlLWNyZWFtXCJcbi8vICAgIH0sXG4vLyAgICB7XG4vLyAgICAgICAgdXJsOiBcImh0dHBzOi8vd29vbHdvcnRocy5jby5uei9zaG9wL2Jyb3dzZS9mcm96ZW4vaWNlLWNyZWFtLXNvcmJldC90dWJzP3BhZ2U9MiZpblN0b2NrUHJvZHVjdHNPbmx5PXRydWVcIlxuLy8gICAgICAgIGNhdGVnb3J5OiBcImljZS1jcmVhbVwiXG4vLyAgICB9XG4vLyAgXVxuXG5leHBvcnQgZnVuY3Rpb24gcGFyc2VBbmRDYXRlZ29yaXNlVVJMKFxuICBsaW5lOiBzdHJpbmdcbik6IENhdGVnb3Jpc2VkVXJsW10gfCB1bmRlZmluZWQge1xuICBsZXQgYmFzZUNhdGVnb3Jpc2VkVVJMOiBDYXRlZ29yaXNlZFVybCA9IHsgdXJsOiBcIlwiLCBjYXRlZ29yaWVzOiBbXSB9O1xuICBsZXQgcGFyc2VkVXJsczogQ2F0ZWdvcmlzZWRVcmxbXSA9IFtdO1xuICBsZXQgbnVtUGFnZXNQZXJVUkwgPSAxO1xuXG4gIC8vIElmIGxpbmUgZG9lc24ndCBjb250YWluIGRlc2lyZWQgdXJsIHNlY3Rpb24sIHJldHVybiB1bmRlZmluZWRcbiAgaWYgKCFsaW5lLmluY2x1ZGVzKFwid29vbHdvcnRocy5jby5uelwiKSkge1xuICAgIHJldHVybiB1bmRlZmluZWQ7XG5cbiAgICAvLyBJZiBsaW5lIGlzIGEgc2VhcmNoIHVybCwgcmV0dXJuIGFzLWlzXG4gIH0gZWxzZSBpZiAobGluZS5pbmNsdWRlcyhcIj9zZWFyY2g9XCIpKSB7XG4gICAgcGFyc2VkVXJscy5wdXNoKHsgdXJsOiBsaW5lLCBjYXRlZ29yaWVzOiBbXSB9KVxuXG4gICAgLy8gRWxzZSBvcHRpbWl6ZSBhbmQgY2xlYW51cCBVUkxcbiAgfSBlbHNlIHtcbiAgICAvLyBTcGxpdCBsaW5lIGJ5IGVtcHR5IHNwYWNlLCBsb29rIGZvciB1cmwsIG9wdGlvbmFsIHBhZ2UgYW1vdW50ICYgY2F0ZWdvcnlcbiAgICBsaW5lLnNwbGl0KFwiIFwiKS5mb3JFYWNoKChzZWN0aW9uKSA9PiB7XG5cbiAgICAgIC8vIFBhcnNlIFVSTFxuICAgICAgaWYgKHNlY3Rpb24uaW5jbHVkZXMoXCJ3b29sd29ydGhzLmNvLm56XCIpKSB7XG4gICAgICAgIGJhc2VDYXRlZ29yaXNlZFVSTC51cmwgPSBzZWN0aW9uO1xuXG4gICAgICAgIC8vIEVuc3VyZSBVUkwgaGFzIGh0dHA6Ly8gb3IgaHR0cHM6Ly9cbiAgICAgICAgaWYgKCFiYXNlQ2F0ZWdvcmlzZWRVUkwudXJsLnN0YXJ0c1dpdGgoXCJodHRwXCIpKVxuICAgICAgICAgIGJhc2VDYXRlZ29yaXNlZFVSTC51cmwgPSBcImh0dHBzOi8vXCIgKyBzZWN0aW9uO1xuXG4gICAgICAgIC8vIElmIHVybCBjb250YWlucyA/IGl0IGhhcyBxdWVyeSBvcHRpb25zIGFscmVhZHkgc2V0XG4gICAgICAgIGlmIChzZWN0aW9uLmluY2x1ZGVzKFwiP1wiKSkge1xuICAgICAgICAgIC8vIFN0cmlwIGFueSBleGlzdGluZyBxdWVyeSBvcHRpb25zIG9mZiBvZiBVUkxcbiAgICAgICAgICBiYXNlQ2F0ZWdvcmlzZWRVUkwudXJsID0gbGluZS5zdWJzdHJpbmcoMCwgbGluZS5pbmRleE9mKFwiP1wiKSk7XG4gICAgICAgIH1cbiAgICAgICAgLy8gUmVwbGFjZSBxdWVyeSBwYXJhbWV0ZXJzIHdpdGggb3B0aW1pc2VkIG9uZXNcbiAgICAgICAgYmFzZUNhdGVnb3Jpc2VkVVJMLnVybCArPSAnP3BhZ2U9MSc7XG5cbiAgICAgICAgLy8gUGFyc2UgQ2F0ZWdvcnlcbiAgICAgIH0gZWxzZSBpZiAoc2VjdGlvbi5zdGFydHNXaXRoKFwiY2F0ZWdvcmllcz1cIikpIHtcbiAgICAgICAgbGV0IHNwbGl0Q2F0ZWdvcmllcyA9IFtzZWN0aW9uLnJlcGxhY2UoXCJjYXRlZ29yaWVzPVwiLCBcIlwiKV07XG4gICAgICAgIGlmIChzZWN0aW9uLmluY2x1ZGVzKFwiLFwiKSlcbiAgICAgICAgICBzcGxpdENhdGVnb3JpZXMgPSBzZWN0aW9uLnJlcGxhY2UoXCJjYXRlZ29yaWVzPVwiLCBcIlwiKS5zcGxpdChcIixcIik7XG4gICAgICAgIGJhc2VDYXRlZ29yaXNlZFVSTC5jYXRlZ29yaWVzID0gc3BsaXRDYXRlZ29yaWVzO1xuXG4gICAgICAgIC8vIFBhcnNlIG51bWJlciBvZiBwYWdlc1xuICAgICAgfSBlbHNlIGlmIChzZWN0aW9uLnN0YXJ0c1dpdGgoXCJwYWdlcz1cIikpIHtcbiAgICAgICAgbnVtUGFnZXNQZXJVUkwgPSBOdW1iZXIucGFyc2VJbnQoc2VjdGlvbi5zcGxpdChcIj1cIilbMV0pO1xuICAgICAgfVxuXG4gICAgICAvLyBJZiBubyBjYXRlZ29yeSB3YXMgc3BlY2lmaWVkLCBkZXJpdmUgb25lIGZyb20gdGhlIGxhc3QgdXJsIC9zZWN0aW9uXG4gICAgICBpZiAoYmFzZUNhdGVnb3Jpc2VkVVJMLmNhdGVnb3JpZXMubGVuZ3RoID09PSAwKSB7XG4gICAgICAgIC8vIEV4dHJhY3QgL3NsYXNoU2VjdGlvbnMvIGZyb20gdXJsLCB3aGlsZSBleGNsdWRpbmcgY29udGVudCBhZnRlciAnPydcbiAgICAgICAgY29uc3QgYmFzZVVybCA9IGJhc2VDYXRlZ29yaXNlZFVSTC51cmwuc3BsaXQoXCI/XCIpWzBdO1xuICAgICAgICBsZXQgc2xhc2hTZWN0aW9ucyA9IGJhc2VVcmwuc3BsaXQoXCIvXCIpO1xuXG4gICAgICAgIC8vIFNldCBjYXRlZ29yeSB0byBsYXN0IHVybCAvc2VjdGlvbi9cbiAgICAgICAgYmFzZUNhdGVnb3Jpc2VkVVJMLmNhdGVnb3JpZXMgPSBbc2xhc2hTZWN0aW9uc1tzbGFzaFNlY3Rpb25zLmxlbmd0aCAtIDFdXTtcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIC8vIEZvciBtdWx0aXBsZSBwYWdlcywgZHVwbGljYXRlIHRoZSB1cmwgYW5kIGVkaXQgdGhlID9wYWdlPTEgcXVlcnkgcGFyYW1ldGVyXG4gICAgZm9yIChsZXQgaSA9IDE7IGkgPD0gbnVtUGFnZXNQZXJVUkw7IGkrKykge1xuICAgICAgbGV0IHBhZ2VkVXJsID0ge1xuICAgICAgICB1cmw6IGJhc2VDYXRlZ29yaXNlZFVSTC51cmwucmVwbGFjZShcInBhZ2U9MVwiLCBcInBhZ2U9XCIgKyBpKSxcbiAgICAgICAgY2F0ZWdvcmllczogYmFzZUNhdGVnb3Jpc2VkVVJMLmNhdGVnb3JpZXMsXG4gICAgICB9XG4gICAgICBwYXJzZWRVcmxzLnB1c2gocGFnZWRVcmwpO1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiBwYXJzZWRVcmxzO1xufVxuXG4vLyByb3V0ZVBsYXl3cmlnaHRFeGNsdXNpb25zKClcbi8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuLy8gRXhjbHVkZXMgYWRzLCB0cmFja2luZywgYW5kIGJhbmR3aWR0aCBpbnRlbnNpdmUgcmVzb3VyY2VzIGZyb20gYmVpbmcgZG93bmxvYWRlZCBieSBQbGF5d3JpZ2h0XG5cbmFzeW5jIGZ1bmN0aW9uIHJvdXRlUGxheXdyaWdodEV4Y2x1c2lvbnMoKSB7XG4gIGxldCB0eXBlRXhjbHVzaW9ucyA9IFtcImltYWdlXCIsIFwibWVkaWFcIiwgXCJmb250XCJdO1xuICBsZXQgdXJsRXhjbHVzaW9ucyA9IFtcbiAgICBcImdvb2dsZW9wdGltaXplLmNvbVwiLFxuICAgIFwiZ3RtLmpzXCIsXG4gICAgXCJ2aXNpdG9yaWRlbnRpZmljYXRpb24uanNcIixcbiAgICBcImpzLWFnZW50Lm5ld3JlbGljLmNvbVwiLFxuICAgIFwiY3F1b3RpZW50LmNvbVwiLFxuICAgIFwiZ29vZ2xldGFnbWFuYWdlci5jb21cIixcbiAgICBcImNsb3VkZmxhcmVpbnNpZ2h0cy5jb21cIixcbiAgICBcImR3YW5hbHl0aWNzXCIsXG4gICAgXCJmYWNlYm9vay5uZXRcIixcbiAgICBcImNoYXRXaWRnZXRcIixcbiAgICBcImVkZ2UuYWRvYmVkYy5uZXRcIixcbiAgICBcIlx1MjAwQi9Db250ZW50L0Jhbm5lcnMvXCIsXG4gICAgXCJnby1tcHVsc2UubmV0XCJcbiAgXTtcblxuICAvLyBSb3V0ZSB3aXRoIGV4Y2x1c2lvbnMgcHJvY2Vzc2VkXG4gIGF3YWl0IHBhZ2Uucm91dGUoXCIqKi8qXCIsIGFzeW5jIChyb3V0ZSkgPT4ge1xuICAgIGNvbnN0IHJlcSA9IHJvdXRlLnJlcXVlc3QoKTtcbiAgICBsZXQgZXhjbHVkZVRoaXNSZXF1ZXN0ID0gZmFsc2U7XG4gICAgLy9sZXQgdHJpbW1lZFVybCA9IHJlcS51cmwoKS5sZW5ndGggPiAxMjAgPyByZXEudXJsKCkuc3Vic3RyaW5nKDAsIDEyMCkgKyAnLi4uJyA6IHJlcS51cmwoKTtcblxuICAgIHVybEV4Y2x1c2lvbnMuZm9yRWFjaCgoZXhjbHVkZWRVUkwpID0+IHtcbiAgICAgIGlmIChyZXEudXJsKCkuaW5jbHVkZXMoZXhjbHVkZWRVUkwpKSBleGNsdWRlVGhpc1JlcXVlc3QgPSB0cnVlO1xuICAgIH0pO1xuXG4gICAgdHlwZUV4Y2x1c2lvbnMuZm9yRWFjaCgoZXhjbHVkZWRUeXBlKSA9PiB7XG4gICAgICBpZiAocmVxLnJlc291cmNlVHlwZSgpID09PSBleGNsdWRlZFR5cGUpIGV4Y2x1ZGVUaGlzUmVxdWVzdCA9IHRydWU7XG4gICAgfSk7XG5cbiAgICBpZiAoZXhjbHVkZVRoaXNSZXF1ZXN0KSB7XG4gICAgICAvL2xvZ0Vycm9yKGAke3JlcS5tZXRob2QoKX0gJHtyZXEucmVzb3VyY2VUeXBlKCl9IC0gJHt0cmltbWVkVXJsfWApO1xuICAgICAgYXdhaXQgcm91dGUuYWJvcnQoKTtcbiAgICB9IGVsc2Uge1xuICAgICAgLy9sb2coY29sb3VyLndoaXRlLCBgJHtyZXEubWV0aG9kKCl9ICR7cmVxLnJlc291cmNlVHlwZSgpfSAtICR7dHJpbW1lZFVybH1gKTtcbiAgICAgIGF3YWl0IHJvdXRlLmNvbnRpbnVlKCk7XG4gICAgfVxuICB9KTtcblxuICByZXR1cm47XG59XG5cbiIsICJpbXBvcnQgeyBjcmVhdGVDbGllbnQsIFN1cGFiYXNlQ2xpZW50IH0gZnJvbSBcIkBzdXBhYmFzZS9zdXBhYmFzZS1qc1wiO1xyXG5pbXBvcnQgKiBhcyBkb3RlbnYgZnJvbSBcImRvdGVudlwiO1xyXG5kb3RlbnYuY29uZmlnKCk7XHJcbmRvdGVudi5jb25maWcoeyBwYXRoOiBgLmVudi5sb2NhbGAsIG92ZXJyaWRlOiB0cnVlIH0pO1xyXG5cclxuaW1wb3J0IHsgUHJvZHVjdCwgVXBzZXJ0UmVzcG9uc2UsIFByb2R1Y3RSZXNwb25zZSwgRGF0ZWRQcmljZSB9IGZyb20gXCIuL3R5cGluZ3MuanNcIjtcclxuaW1wb3J0IHsgY29sb3VyLCBsb2csIGxvZ0Vycm9yIH0gZnJvbSBcIi4vdXRpbGl0aWVzLmpzXCI7XHJcblxyXG5sZXQgc3VwYWJhc2U6IFN1cGFiYXNlQ2xpZW50O1xyXG5sZXQgc3RvcmVJZDogbnVtYmVyIHwgdW5kZWZpbmVkOyAvLyBXb29sd29ydGhzIHN0b3JlIHJvdyBpZFxyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIGVzdGFibGlzaFN1cGFiYXNlKCkge1xyXG4gIGNvbnN0IHVybCA9IHByb2Nlc3MuZW52LlNVUEFCQVNFX1VSTDtcclxuICBjb25zdCBrZXkgPSBwcm9jZXNzLmVudi5TVVBBQkFTRV9TRVJWSUNFX1JPTEVfS0VZIHx8IHByb2Nlc3MuZW52LlNVUEFCQVNFX0FOT05fS0VZO1xyXG4gIGlmICghdXJsIHx8ICFrZXkpIHtcclxuICAgIHRocm93IEVycm9yKFwiU1VQQUJBU0VfVVJMIG9yIFNVUEFCQVNFX0FOT05fS0VZIG5vdCBzZXQgaW4gZW52XCIpO1xyXG4gIH1cclxuICBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudCh1cmwsIGtleSwgeyBhdXRoOiB7IHBlcnNpc3RTZXNzaW9uOiBmYWxzZSB9IH0pO1xyXG59XHJcblxyXG5hc3luYyBmdW5jdGlvbiBlbnN1cmVTdG9yZVJvdygpOiBQcm9taXNlPG51bWJlcj4ge1xyXG4gIGlmIChzdG9yZUlkICE9PSB1bmRlZmluZWQpIHJldHVybiBzdG9yZUlkO1xyXG4gIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXHJcbiAgICAuZnJvbShcInN0b3Jlc1wiKVxyXG4gICAgLnNlbGVjdChcImlkXCIpXHJcbiAgICAuZXEoXCJuYW1lXCIsIFwid29vbHdvcnRoc1wiKVxyXG4gICAgLnNpbmdsZSgpO1xyXG5cclxuICBpZiAoZXJyb3IgJiYgZXJyb3IuY29kZSAhPT0gXCJQR1JTVDExNlwiKSB7XHJcbiAgICB0aHJvdyBlcnJvcjtcclxuICB9XHJcblxyXG4gIGlmIChkYXRhPy5pZCkge1xyXG4gICAgc3RvcmVJZCA9IGRhdGEuaWQ7XHJcbiAgICByZXR1cm4gc3RvcmVJZCE7XHJcbiAgfVxyXG5cclxuICAvLyBJbnNlcnQgc3RvcmUgcm93IGlmIG5vdCBleGlzdHNcclxuICBjb25zdCB7IGRhdGE6IGluc2VydERhdGEsIGVycm9yOiBpbnNlcnRFcnIgfSA9IGF3YWl0IHN1cGFiYXNlXHJcbiAgICAuZnJvbShcInN0b3Jlc1wiKVxyXG4gICAgLmluc2VydCh7IG5hbWU6IFwid29vbHdvcnRoc1wiIH0pXHJcbiAgICAuc2VsZWN0KFwiaWRcIilcclxuICAgIC5zaW5nbGUoKTtcclxuICBpZiAoaW5zZXJ0RXJyKSB0aHJvdyBpbnNlcnRFcnI7XHJcbiAgc3RvcmVJZCA9IGluc2VydERhdGEhLmlkO1xyXG4gIHJldHVybiBzdG9yZUlkITtcclxufVxyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHVwc2VydFByb2R1Y3RUb1N1cGFiYXNlKHNjcmFwZWQ6IFByb2R1Y3QpOiBQcm9taXNlPFVwc2VydFJlc3BvbnNlPiB7XHJcbiAgaWYgKCFzdXBhYmFzZSkgdGhyb3cgRXJyb3IoXCJTdXBhYmFzZSBjbGllbnQgbm90IGluaXRpYWxpc2VkXCIpO1xyXG4gIGNvbnN0IHNJZCA9IGF3YWl0IGVuc3VyZVN0b3JlUm93KCk7XHJcblxyXG4gIC8vIFVwc2VydCBpbnRvIHByb2R1Y3RzXHJcbiAgY29uc3QgcHJvZHVjdFBheWxvYWQ6IGFueSA9IHtcclxuICAgIGlkOiBzY3JhcGVkLmlkLFxyXG4gICAgbmFtZTogc2NyYXBlZC5uYW1lLFxyXG4gICAgc2l6ZTogc2NyYXBlZC5zaXplLFxyXG4gICAgdW5pdF9wcmljZTogc2NyYXBlZC51bml0UHJpY2UsXHJcbiAgICB1bml0X25hbWU6IHNjcmFwZWQudW5pdE5hbWUsXHJcbiAgICBvcmlnaW5hbF91bml0X3F0eTogc2NyYXBlZC5vcmlnaW5hbFVuaXRRdWFudGl0eSxcclxuICAgIHNvdXJjZV9zaXRlOiBzY3JhcGVkLnNvdXJjZVNpdGUsXHJcbiAgICBsYXN0X2NoZWNrZWQ6IHNjcmFwZWQubGFzdENoZWNrZWQudG9JU09TdHJpbmcoKSxcclxuICAgIGxhc3RfdXBkYXRlZDogc2NyYXBlZC5sYXN0VXBkYXRlZC50b0lTT1N0cmluZygpXHJcbiAgfTtcclxuXHJcbiAgY29uc3QgeyBlcnJvcjogcHJvZEVyciB9ID0gYXdhaXQgc3VwYWJhc2UuZnJvbShcInByb2R1Y3RzXCIpLnVwc2VydChwcm9kdWN0UGF5bG9hZCwgeyBvbkNvbmZsaWN0OiBcImlkXCIgfSk7XHJcbiAgaWYgKHByb2RFcnIpIHtcclxuICAgIGxvZ0Vycm9yKFwiU3VwYWJhc2UgdXBzZXJ0IHByb2R1Y3QgZmFpbGVkOiBcIiArIHByb2RFcnIubWVzc2FnZSk7XHJcbiAgICByZXR1cm4gVXBzZXJ0UmVzcG9uc2UuRmFpbGVkO1xyXG4gIH1cclxuXHJcbiAgLy8gSW5zZXJ0IHByaWNlIHJvdyAoYWx3YXlzKSBcdTIwMTMgbG9naWMgdG8gb21pdCBkdXBsaWNhdGVzIGNhbiBiZSBhZGRlZCBsYXRlclxyXG4gIGNvbnN0IHByaWNlUGF5bG9hZCA9IHtcclxuICAgIHByb2R1Y3RfaWQ6IHNjcmFwZWQuaWQsXHJcbiAgICBzdG9yZV9pZDogc0lkLFxyXG4gICAgcHJpY2U6IHNjcmFwZWQuY3VycmVudFByaWNlLFxyXG4gICAgcmVjb3JkZWRfYXQ6IHNjcmFwZWQubGFzdFVwZGF0ZWQudG9JU09TdHJpbmcoKVxyXG4gIH07XHJcbiAgY29uc3QgeyBlcnJvcjogcHJpY2VFcnIgfSA9IGF3YWl0IHN1cGFiYXNlLmZyb20oXCJwcmljZXNcIikuaW5zZXJ0KHByaWNlUGF5bG9hZCk7XHJcbiAgaWYgKHByaWNlRXJyKSB7XHJcbiAgICBsb2dFcnJvcihcIlN1cGFiYXNlIGluc2VydCBwcmljZSBmYWlsZWQ6IFwiICsgcHJpY2VFcnIubWVzc2FnZSk7XHJcbiAgICByZXR1cm4gVXBzZXJ0UmVzcG9uc2UuRmFpbGVkO1xyXG4gIH1cclxuXHJcbiAgbG9nKGNvbG91ci5ncmVlbiwgYCAgVXBzZXJ0ZWQ6ICR7c2NyYXBlZC5uYW1lLnNsaWNlKDAsIDQ1KS5wYWRFbmQoNDUpfSB8ICQke3NjcmFwZWQuY3VycmVudFByaWNlfWApO1xyXG4gIHJldHVybiBVcHNlcnRSZXNwb25zZS5QcmljZUNoYW5nZWQ7IC8vIGNvYXJzZTsgY2FuIHJlZmluZSBsYXRlclxyXG59XHJcblxyXG4vLyBVcGxvYWQgcHJvZHVjdCBpbWFnZSB0byBTdXBhYmFzZSBTdG9yYWdlXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB1cGxvYWRJbWFnZVRvU3VwYWJhc2UoaW1hZ2VVcmw6IHN0cmluZywgcHJvZHVjdDogUHJvZHVjdCk6IFByb21pc2U8Ym9vbGVhbj4ge1xyXG4gIGlmICghc3VwYWJhc2UpIHRocm93IEVycm9yKFwiU3VwYWJhc2UgY2xpZW50IG5vdCBpbml0aWFsaXNlZFwiKTtcclxuXHJcbiAgdHJ5IHtcclxuICAgIC8vIERvd25sb2FkIGltYWdlIGZyb20gV29vbHdvcnRoc1xyXG4gICAgY29uc3QgaW1hZ2VSZXNwb25zZSA9IGF3YWl0IGZldGNoKGltYWdlVXJsKTtcclxuICAgIGlmICghaW1hZ2VSZXNwb25zZS5vaykge1xyXG4gICAgICBsb2dFcnJvcihgRmFpbGVkIHRvIGRvd25sb2FkIGltYWdlIGZyb20gJHtpbWFnZVVybH06ICR7aW1hZ2VSZXNwb25zZS5zdGF0dXNUZXh0fWApO1xyXG4gICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgaW1hZ2VCdWZmZXIgPSBhd2FpdCBpbWFnZVJlc3BvbnNlLmFycmF5QnVmZmVyKCk7XHJcbiAgICBjb25zdCBpbWFnZUJsb2IgPSBuZXcgQmxvYihbaW1hZ2VCdWZmZXJdLCB7IHR5cGU6ICdpbWFnZS9qcGVnJyB9KTtcclxuICAgIFxyXG4gICAgLy8gR2VuZXJhdGUgZmlsZSBwYXRoOiBwcm9kdWN0cy97cHJvZHVjdElkfS5qcGdcclxuICAgIGNvbnN0IGZpbGVQYXRoID0gYHByb2R1Y3RzLyR7cHJvZHVjdC5pZH0uanBnYDtcclxuICAgIFxyXG4gICAgLy8gVXBsb2FkIHRvIFN1cGFiYXNlIFN0b3JhZ2VcclxuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlLnN0b3JhZ2VcclxuICAgICAgLmZyb20oJ3Byb2R1Y3QtaW1hZ2VzJylcclxuICAgICAgLnVwbG9hZChmaWxlUGF0aCwgaW1hZ2VCbG9iLCB7XHJcbiAgICAgICAgY29udGVudFR5cGU6ICdpbWFnZS9qcGVnJyxcclxuICAgICAgICB1cHNlcnQ6IHRydWUgLy8gT3ZlcndyaXRlIGlmIGV4aXN0c1xyXG4gICAgICB9KTtcclxuXHJcbiAgICBpZiAoZXJyb3IpIHtcclxuICAgICAgbG9nRXJyb3IoYFN1cGFiYXNlIHN0b3JhZ2UgdXBsb2FkIGZhaWxlZDogJHtlcnJvci5tZXNzYWdlfWApO1xyXG4gICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gR2V0IHB1YmxpYyBVUkwgZm9yIHRoZSB1cGxvYWRlZCBpbWFnZVxyXG4gICAgY29uc3QgeyBkYXRhOiBwdWJsaWNVcmxEYXRhIH0gPSBzdXBhYmFzZS5zdG9yYWdlXHJcbiAgICAgIC5mcm9tKCdwcm9kdWN0LWltYWdlcycpXHJcbiAgICAgIC5nZXRQdWJsaWNVcmwoZmlsZVBhdGgpO1xyXG5cclxuICAgIGxvZyhjb2xvdXIuYmx1ZSwgYCAgSW1hZ2UgdXBsb2FkZWQ6ICR7cHJvZHVjdC5pZH0gLT4gJHtwdWJsaWNVcmxEYXRhLnB1YmxpY1VybH1gKTtcclxuICAgIHJldHVybiB0cnVlO1xyXG5cclxuICB9IGNhdGNoIChlcnI6IGFueSkge1xyXG4gICAgbG9nRXJyb3IoYEltYWdlIHVwbG9hZCBlcnJvcjogJHtlcnIubWVzc2FnZX1gKTtcclxuICAgIHJldHVybiBmYWxzZTtcclxuICB9XHJcbn0gIiwgImltcG9ydCB7IFByb2R1Y3QgfSBmcm9tICcuL3R5cGluZ3MnO1xuaW1wb3J0IHsgcmVhZEZpbGVTeW5jIH0gZnJvbSAnZnMnO1xuXG4vLyBTZXQgd2lkdGhzIGZvciB0YWJsZSBsb2cgb3V0cHV0XG5jb25zdCB0YWJsZUlEV2lkdGggPSA2XG5jb25zdCB0YWJsZU5hbWVXaWR0aCA9IDYwO1xuY29uc3QgdGFibGVTaXplV2lkdGggPSAxNztcblxuZXhwb3J0IGNvbnN0IGNvbG91ciA9IHtcbiAgcmVkOiAnXFx4MWJbMzFtJyxcbiAgZ3JlZW46ICdcXHgxYlszMm0nLFxuICB5ZWxsb3c6ICdcXHgxYlszM20nLFxuICBibHVlOiAnXFx4MWJbMzg7NTsxMTdtJyxcbiAgbWFnZW50YTogJ1xceDFiWzM1bScsXG4gIGN5YW46ICdcXHgxYlszNm0nLFxuICB3aGl0ZTogJ1xceDFiWzM3bScsXG4gIGNyaW1zb246ICdcXHgxYlszOG0nLFxuICBncmV5OiAnXFx4MWJbOTBtJyxcbiAgb3JhbmdlOiAnXFx4MWJbMzg7NTsyMTRtJyxcbiAgc2t5OiAnXFx4MWJbMzg7NTsxNTNtJyxcbn07XG5cbi8vIGxvZygpXG4vLyAtLS0tLVxuLy8gQ29uc29sZSBsb2cgd2l0aCBzcGVjaWZpZWQgY29sb3VyXG5cbmV4cG9ydCBmdW5jdGlvbiBsb2coY29sb3VyOiBzdHJpbmcsIHRleHQ6IHN0cmluZykge1xuICBjb25zdCBjbGVhciA9ICdcXHgxYlswbSc7XG4gIGNvbnNvbGUubG9nKGAke2NvbG91cn0lcyR7Y2xlYXJ9YCwgdGV4dCk7XG59XG5cbi8vIGxvZ0Vycm9yKClcbi8vIC0tLS0tLS0tLS1cbi8vIFNob3J0aGFuZCBmdW5jdGlvbiBmb3IgbG9nZ2luZyB3aXRoIHJlZCBjb2xvdXJcblxuZXhwb3J0IGZ1bmN0aW9uIGxvZ0Vycm9yKHRleHQ6IHN0cmluZykge1xuICBsb2coY29sb3VyLnJlZCwgdGV4dCk7XG59XG5cbi8vIGxvZ1Byb2R1Y3RSb3coKVxuLy8gLS0tLS0tLS0tLS0tLS0tXG4vLyBMb2cgYSBzaW5nbGUgcHJvZHVjdCBpbiBvbmUgcm93LCB1c2luZyBhbHRlcm5hdGluZyBjb2xvdXJzIGZvciByZWFkYWJpbGl0eS5cblxuZXhwb3J0IGZ1bmN0aW9uIGxvZ1Byb2R1Y3RSb3cocHJvZHVjdDogUHJvZHVjdCkge1xuICBjb25zdCB1bml0UHJpY2VTdHJpbmcgPSBwcm9kdWN0LnVuaXRQcmljZSA/IGAkJHtwcm9kdWN0LnVuaXRQcmljZS50b0ZpeGVkKDIpfSAvJHtwcm9kdWN0LnVuaXROYW1lfWAgOiBgYDtcbiAgbG9nKFxuICAgIGdldEFsdGVybmF0aW5nUm93Q29sb3VyKGNvbG91ci5za3ksIGNvbG91ci53aGl0ZSksXG4gICAgYCR7cHJvZHVjdC5pZC5wYWRTdGFydCh0YWJsZUlEV2lkdGgpfSB8IGAgK1xuICAgIGAke3Byb2R1Y3QubmFtZS5zbGljZSgwLCB0YWJsZU5hbWVXaWR0aCkucGFkRW5kKHRhYmxlTmFtZVdpZHRoKX0gfCBgICtcbiAgICBgJHtwcm9kdWN0LnNpemU/LnNsaWNlKDAsIHRhYmxlU2l6ZVdpZHRoKS5wYWRFbmQodGFibGVTaXplV2lkdGgpfSB8IGAgK1xuICAgIGAkICR7cHJvZHVjdC5jdXJyZW50UHJpY2UudG9GaXhlZCgyKS5wYWRTdGFydCg0KS5wYWRFbmQoNSl9IHwgYCArXG4gICAgdW5pdFByaWNlU3RyaW5nXG4gICk7XG59XG5cbi8vIGxvZ1RhYmxlSGVhZGVyKClcbi8vIC0tLS0tLS0tLS0tLS0tLS1cblxuZXhwb3J0IGZ1bmN0aW9uIGxvZ1RhYmxlSGVhZGVyKCkge1xuICBsb2coXG4gICAgY29sb3VyLnllbGxvdyxcbiAgICBgJHsnSUQnLnBhZFN0YXJ0KHRhYmxlSURXaWR0aCl9IHwgJHsnTmFtZScucGFkRW5kKHRhYmxlTmFtZVdpZHRoKX0gfCBgICtcbiAgICBgJHsnU2l6ZScucGFkRW5kKHRhYmxlU2l6ZVdpZHRoKX0gfCBgICtcbiAgICBgJHsnUHJpY2UnLnBhZEVuZCg3KX0gfCBVbml0IFByaWNlYFxuICApO1xuXG4gIGxldCBoZWFkZXJMaW5lID0gXCJcIlxuICBmb3IgKGxldCBpID0gMDsgaSA8IDExMTsgaSsrKSB7XG4gICAgaGVhZGVyTGluZSArPSBcIi1cIlxuICB9XG4gIGxvZyhjb2xvdXIueWVsbG93LCBoZWFkZXJMaW5lKTtcblxufVxuXG4vLyBnZXRBbHRlcm5hdGluZ1Jvd0NvbG91cigpXG4vLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4vLyBUYWtlcyAyIGNvbG91cnMgYW5kIGZsaXAtZmxvcHMgYmV0d2VlbiB0aGVtIG9uIGVhY2ggZnVuY3Rpb24gY2FsbC5cbi8vIElzIHVzZWQgZm9yIHByaW50aW5nIHRhYmxlcyB3aXRoIGJldHRlciByZWFkYWJpbGl0eS5cblxubGV0IGFsdGVybmF0aW5nUm93Q29sb3VyID0gZmFsc2U7XG5mdW5jdGlvbiBnZXRBbHRlcm5hdGluZ1Jvd0NvbG91cihjb2xvdXJBOiBzdHJpbmcsIGNvbG91ckI6IHN0cmluZykge1xuICBhbHRlcm5hdGluZ1Jvd0NvbG91ciA9IGFsdGVybmF0aW5nUm93Q29sb3VyID8gZmFsc2UgOiB0cnVlO1xuICByZXR1cm4gYWx0ZXJuYXRpbmdSb3dDb2xvdXIgPyBjb2xvdXJBIDogY29sb3VyQjtcbn1cblxuLy8gcmVhZExpbmVzRnJvbVRleHRGaWxlKClcbi8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4vLyBSZWFkIGZyb20gbG9jYWwgdGV4dCBmaWxlIGNvbnRhaW5pbmcgb25lIHVybCBwZXIgbGluZSwgcmV0dXJuIGFzIHN0cmluZyBhcnJheS5cblxuZXhwb3J0IGZ1bmN0aW9uIHJlYWRMaW5lc0Zyb21UZXh0RmlsZShmaWxlbmFtZTogc3RyaW5nKTogc3RyaW5nW10ge1xuICB0cnkge1xuICAgIGNvbnN0IGZpbGUgPSByZWFkRmlsZVN5bmMoZmlsZW5hbWUsICd1dGYtOCcpO1xuICAgIGNvbnN0IHJlc3VsdCA9IGZpbGUuc3BsaXQoL1xccj9cXG4vKS5maWx0ZXIoKGxpbmUpID0+IHtcbiAgICAgIGlmIChsaW5lLnRyaW0oKS5sZW5ndGggPiAwKSByZXR1cm4gdHJ1ZTtcbiAgICAgIGVsc2UgcmV0dXJuIGZhbHNlO1xuICAgIH0pO1xuICAgIHJldHVybiByZXN1bHQ7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgdGhyb3cgJ0Vycm9yIHJlYWRpbmcgJyArIGZpbGVuYW1lO1xuICB9XG59XG5cbi8vIGdldFRpbWVFbGFwc2VkU2luY2UoKVxuLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4vLyBHZXQgdGltZSBkaWZmZXJlbmNlIGluIGJldHdlZW4gc3RhcnRUaW1lIGFuZCBub3cuIFJldHVybnMgaW4gNThzIG9yIDEyOjMyIGZvcm1hdC5cblxuZXhwb3J0IGZ1bmN0aW9uIGdldFRpbWVFbGFwc2VkU2luY2Uoc3RhcnRUaW1lOiBudW1iZXIpOiBzdHJpbmcge1xuICBsZXQgZWxhcHNlZFRpbWVTZWNvbmRzOiBudW1iZXIgPSAoRGF0ZS5ub3coKSAtIHN0YXJ0VGltZSkgLyAxMDAwO1xuICBsZXQgZWxhcHNlZFRpbWVTdHJpbmc6IHN0cmluZyA9IE1hdGguZmxvb3IoZWxhcHNlZFRpbWVTZWNvbmRzKS50b1N0cmluZygpO1xuXG4gIC8vIElmIG92ZXIgNjAgc2VjcywgcHJpbnQgYXMgMToyMyBmb3JtYXRcbiAgaWYgKGVsYXBzZWRUaW1lU2Vjb25kcyA+PSA2MCkge1xuICAgIHJldHVybiAoXG4gICAgICBNYXRoLmZsb29yKGVsYXBzZWRUaW1lU2Vjb25kcyAvIDYwKSArXG4gICAgICAnOicgK1xuICAgICAgTWF0aC5mbG9vcihlbGFwc2VkVGltZVNlY29uZHMgJSA2MClcbiAgICAgICAgLnRvU3RyaW5nKClcbiAgICAgICAgLnBhZFN0YXJ0KDIsICcwJylcbiAgICApXG4gICAgLy8gRWxzZSBwcmludCBpbiA0MHMgZm9ybWF0XG4gIH0gZWxzZSByZXR1cm4gZWxhcHNlZFRpbWVTdHJpbmcgKyBcInNcIjtcbn1cblxuLy8gTGlzdCBvZiB2YWxpZCBjYXRlZ29yeSBuYW1lcyB0aGF0IHNjcmFwZWQgcHJvZHVjdHMgc2hvdWxkIGJlIHB1dCBpblxuZXhwb3J0IGNvbnN0IHZhbGlkQ2F0ZWdvcmllczogc3RyaW5nW10gPSBbXG4gIC8vIGZyZXNoQ2F0ZWdvcnlcbiAgJ2VnZ3MnLFxuICAnZnJ1aXQnLFxuICAnZnJlc2gtdmVnZXRhYmxlcycsXG4gICdzYWxhZHMtY29sZXNsYXcnLFxuICAnYnJlYWQnLFxuICAnYnJlYWQtcm9sbHMnLFxuICAnc3BlY2lhbHR5LWJyZWFkJyxcbiAgJ2Jha2VyeS1jYWtlcycsXG4gICdiYWtlcnktZGVzc2VydHMnLFxuICAvLyBjaGlsbGVkQ2F0ZWdvcnlcbiAgJ21pbGsnLFxuICAnbG9uZy1saWZlLW1pbGsnLFxuICAnc291ci1jcmVhbScsXG4gICdjcmVhbScsXG4gICd5b2dodXJ0JyxcbiAgJ2J1dHRlcicsXG4gICdjaGVlc2UnLFxuICAnY2hlZXNlLXNsaWNlcycsXG4gICdzYWxhbWknLFxuICAnb3RoZXItZGVsaS1mb29kcycsXG4gIC8vIG1lYXRDYXRlZ29yeVxuICAnYmVlZi1sYW1iJyxcbiAgJ2NoaWNrZW4nLFxuICAnaGFtJyxcbiAgJ2JhY29uJyxcbiAgJ3BvcmsnLFxuICAncGF0dGllcy1tZWF0YmFsbHMnLFxuICAnc2F1c2FnZXMnLFxuICAnZGVsaS1tZWF0cycsXG4gICdtZWF0LWFsdGVybmF0aXZlcycsXG4gICdzZWFmb29kJyxcbiAgJ3NhbG1vbicsXG4gIC8vIGZyb3plbkNhdGVnb3J5XG4gICdpY2UtY3JlYW0nLFxuICAnaWNlLWJsb2NrcycsXG4gICdwYXN0cmllcy1jaGVlc2VjYWtlJyxcbiAgJ2Zyb3plbi1jaGlwcycsXG4gICdmcm96ZW4tdmVnZXRhYmxlcycsXG4gICdmcm96ZW4tZnJ1aXQnLFxuICAnZnJvemVuLXNlYWZvb2QnLFxuICAncGllcy1zYXVzYWdlLXJvbGxzJyxcbiAgJ3BpenphJyxcbiAgJ290aGVyLXNhdm91cmllcycsXG4gIC8vIHBhbnRyeUNhdGVnb3J5XG4gICdyaWNlJyxcbiAgJ25vb2RsZXMnLFxuICAncGFzdGEnLFxuICAnYmVhbnMtc3BhZ2hldHRpJyxcbiAgJ2Nhbm5lZC1maXNoJyxcbiAgJ2Nhbm5lZC1tZWF0JyxcbiAgJ3NvdXAnLFxuICAnY2VyZWFsJyxcbiAgJ3NwcmVhZHMnLFxuICAnYmFraW5nJyxcbiAgJ3NhdWNlcycsXG4gICdvaWxzLXZpbmVnYXJzJyxcbiAgJ3dvcmxkLWZvb2RzJyxcbiAgLy8gc25hY2tzQ2F0ZWdvcnlcbiAgJ2Nob2NvbGF0ZScsXG4gICdib3hlZC1jaG9jb2xhdGUnLFxuICAnY2hpcHMnLFxuICAnY3JhY2tlcnMnLFxuICAnYmlzY3VpdHMnLFxuICAnbXVlc2xpLWJhcnMnLFxuICAnbnV0cy1idWxrLW1peCcsXG4gICdzd2VldHMtbG9sbGllcycsXG4gICdvdGhlci1zbmFja3MnLFxuICAvLyBkcmlua3NDYXRlZ29yeVxuICAnYmxhY2stdGVhJyxcbiAgJ2dyZWVuLXRlYScsXG4gICdoZXJiYWwtdGVhJyxcbiAgJ2RyaW5raW5nLWNob2NvbGF0ZScsXG4gICdjb2ZmZWUnLFxuICAnc29mdC1kcmlua3MnLFxuICAnZW5lcmd5LWRyaW5rcycsXG4gICdqdWljZScsXG4gIC8vIHBldHNDYXRlZ29yeVxuICAnY2F0LWZvb2QnLFxuICAnY2F0LXRyZWF0cycsXG4gICdkb2ctZm9vZCcsXG4gICdkb2ctdHJlYXRzJyxcbl07XG5cbi8vIHRvVGl0bGVDYXNlKClcbi8vIC0tLS0tLS0tLS0tLS1cbi8vIENvbnZlcnQgYSBzdHJpbmcgdG8gdGl0bGUgY2FzZVxuXG5leHBvcnQgZnVuY3Rpb24gdG9UaXRsZUNhc2Uoc3RyOiBzdHJpbmcpIHtcbiAgcmV0dXJuIHN0ci5yZXBsYWNlKC9cXHdcXFMqL2csIGZ1bmN0aW9uICh0eHQpIHtcbiAgICByZXR1cm4gdHh0LmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpICsgdHh0LnN1YnN0cmluZygxKS50b0xvd2VyQ2FzZSgpO1xuICB9KTtcbn0iLCAiLyoqXHJcbiAqIENvbnNvbGlkYXRlZCBQcm9kdWN0cyBJbnRlZ3JhdGlvbiBmb3IgV29vbHdvcnRocyBTY3JhcGVyXHJcbiAqIFRoaXMgbW9kdWxlIGludGVncmF0ZXMgdGhlIHByb2R1Y3QgbWF0Y2hpbmcgYWxnb3JpdGhtIHdpdGggdGhlIFdvb2x3b3J0aHMgc2NyYXBlclxyXG4gKi9cclxuXHJcbmltcG9ydCB7IGNyZWF0ZUNsaWVudCwgU3VwYWJhc2VDbGllbnQgfSBmcm9tIFwiQHN1cGFiYXNlL3N1cGFiYXNlLWpzXCI7XHJcbmltcG9ydCB7IFByb2R1Y3QgfSBmcm9tIFwiLi90eXBpbmdzLmpzXCI7XHJcbmltcG9ydCAqIGFzIFByb2R1Y3RNYXRjaGVyIGZyb20gXCIuLi8uLi9zaGFyZWQvcHJvZHVjdC1tYXRjaGVyLmpzXCI7XHJcblxyXG5sZXQgc3VwYWJhc2U6IFN1cGFiYXNlQ2xpZW50O1xyXG5sZXQgd29vbHdvcnRoc1N0b3JlSWQ6IG51bWJlcjtcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBpbml0aWFsaXplQ29uc29saWRhdGVkUHJvZHVjdHMoc3VwYWJhc2VVcmw6IHN0cmluZywgc3VwYWJhc2VLZXk6IHN0cmluZykge1xyXG4gICAgc3VwYWJhc2UgPSBjcmVhdGVDbGllbnQoc3VwYWJhc2VVcmwsIHN1cGFiYXNlS2V5LCB7IGF1dGg6IHsgcGVyc2lzdFNlc3Npb246IGZhbHNlIH0gfSk7XHJcbiAgICBQcm9kdWN0TWF0Y2hlci5pbml0aWFsaXplU3VwYWJhc2Uoc3VwYWJhc2VVcmwsIHN1cGFiYXNlS2V5KTtcclxufVxyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGVuc3VyZVdvb2x3b3J0aHNTdG9yZUlkKCk6IFByb21pc2U8bnVtYmVyPiB7XHJcbiAgICBpZiAod29vbHdvcnRoc1N0b3JlSWQpIHJldHVybiB3b29sd29ydGhzU3RvcmVJZDtcclxuICAgIFxyXG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcclxuICAgICAgICAuZnJvbShcInN0b3Jlc1wiKVxyXG4gICAgICAgIC5zZWxlY3QoXCJpZFwiKVxyXG4gICAgICAgIC5lcShcIm5hbWVcIiwgXCJ3b29sd29ydGhzXCIpXHJcbiAgICAgICAgLnNpbmdsZSgpO1xyXG5cclxuICAgIGlmIChlcnJvciAmJiBlcnJvci5jb2RlICE9PSBcIlBHUlNUMTE2XCIpIHtcclxuICAgICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuXHJcbiAgICBpZiAoZGF0YT8uaWQpIHtcclxuICAgICAgICB3b29sd29ydGhzU3RvcmVJZCA9IGRhdGEuaWQ7XHJcbiAgICAgICAgcmV0dXJuIHdvb2x3b3J0aHNTdG9yZUlkO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIEluc2VydCBzdG9yZSByb3cgaWYgbm90IGV4aXN0c1xyXG4gICAgY29uc3QgeyBkYXRhOiBpbnNlcnREYXRhLCBlcnJvcjogaW5zZXJ0RXJyIH0gPSBhd2FpdCBzdXBhYmFzZVxyXG4gICAgICAgIC5mcm9tKFwic3RvcmVzXCIpXHJcbiAgICAgICAgLmluc2VydCh7IG5hbWU6IFwid29vbHdvcnRoc1wiIH0pXHJcbiAgICAgICAgLnNlbGVjdChcImlkXCIpXHJcbiAgICAgICAgLnNpbmdsZSgpO1xyXG4gICAgaWYgKGluc2VydEVycikgdGhyb3cgaW5zZXJ0RXJyO1xyXG4gICAgXHJcbiAgICB3b29sd29ydGhzU3RvcmVJZCA9IGluc2VydERhdGEhLmlkO1xyXG4gICAgcmV0dXJuIHdvb2x3b3J0aHNTdG9yZUlkO1xyXG59XHJcblxyXG4vKipcclxuICogUHJvY2VzcyBhIHNjcmFwZWQgV29vbHdvcnRocyBwcm9kdWN0IGFuZCBpbnRlZ3JhdGUgd2l0aCBjb25zb2xpZGF0ZWQgcHJvZHVjdHNcclxuICovXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBwcm9jZXNzQ29uc29saWRhdGVkUHJvZHVjdChzY3JhcGVkUHJvZHVjdDogUHJvZHVjdCk6IFByb21pc2U8c3RyaW5nIHwgbnVsbD4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgICAvLyBFbnN1cmUgd2UgaGF2ZSB0aGUgV29vbHdvcnRocyBzdG9yZSBJRFxyXG4gICAgICAgIGNvbnN0IHN0b3JlSWQgPSBhd2FpdCBlbnN1cmVXb29sd29ydGhzU3RvcmVJZCgpO1xyXG4gICAgICAgIFxyXG4gICAgICAgIC8vIENvbnZlcnQgc2NyYXBlZCBwcm9kdWN0IHRvIGZvcm1hdCBleHBlY3RlZCBieSBtYXRjaGVyXHJcbiAgICAgICAgY29uc3QgcHJvZHVjdEZvck1hdGNoaW5nID0ge1xyXG4gICAgICAgICAgICBpZDogc2NyYXBlZFByb2R1Y3QuaWQsXHJcbiAgICAgICAgICAgIG5hbWU6IHNjcmFwZWRQcm9kdWN0Lm5hbWUsXHJcbiAgICAgICAgICAgIHNpemU6IHNjcmFwZWRQcm9kdWN0LnNpemUsXHJcbiAgICAgICAgICAgIGNhdGVnb3J5OiBzY3JhcGVkUHJvZHVjdC5jYXRlZ29yeSxcclxuICAgICAgICAgICAgYnJhbmQ6IGV4dHJhY3RCcmFuZEZyb21OYW1lKHNjcmFwZWRQcm9kdWN0Lm5hbWUpLCAvLyBFeHRyYWN0IGJyYW5kIGlmIHBvc3NpYmxlXHJcbiAgICAgICAgICAgIHVuaXRQcmljZTogc2NyYXBlZFByb2R1Y3QudW5pdFByaWNlLFxyXG4gICAgICAgICAgICB1bml0TmFtZTogc2NyYXBlZFByb2R1Y3QudW5pdE5hbWVcclxuICAgICAgICB9O1xyXG4gICAgICAgIFxyXG4gICAgICAgIC8vIEZpbmQgb3IgY3JlYXRlIGNvbnNvbGlkYXRlZCBwcm9kdWN0XHJcbiAgICAgICAgY29uc3QgY29uc29saWRhdGVkUHJvZHVjdElkID0gYXdhaXQgUHJvZHVjdE1hdGNoZXIuY29uc29saWRhdGVQcm9kdWN0KHByb2R1Y3RGb3JNYXRjaGluZywgc3RvcmVJZCk7XHJcbiAgICAgICAgXHJcbiAgICAgICAgaWYgKGNvbnNvbGlkYXRlZFByb2R1Y3RJZCkge1xyXG4gICAgICAgICAgICAvLyBTdG9yZSB0aGUgY3VycmVudCBwcmljZVxyXG4gICAgICAgICAgICBhd2FpdCBQcm9kdWN0TWF0Y2hlci5zdG9yZUNvbnNvbGlkYXRlZFByaWNlKFxyXG4gICAgICAgICAgICAgICAgY29uc29saWRhdGVkUHJvZHVjdElkLFxyXG4gICAgICAgICAgICAgICAgc3RvcmVJZCxcclxuICAgICAgICAgICAgICAgIHNjcmFwZWRQcm9kdWN0LmN1cnJlbnRQcmljZSxcclxuICAgICAgICAgICAgICAgIGZhbHNlIC8vIGlzU3BlY2lhbCAtIGNvdWxkIGJlIGRldGVybWluZWQgZnJvbSBwcm9kdWN0IGRhdGFcclxuICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgXHJcbiAgICAgICAgICAgIC8vIEhhbmRsZSBtdWx0aXBsZSBzaXplcyBpZiBhcHBsaWNhYmxlXHJcbiAgICAgICAgICAgIGF3YWl0IGhhbmRsZVByb2R1Y3RTaXplVmFyaWFudHMoY29uc29saWRhdGVkUHJvZHVjdElkLCBzY3JhcGVkUHJvZHVjdCk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIFxyXG4gICAgICAgIHJldHVybiBjb25zb2xpZGF0ZWRQcm9kdWN0SWQ7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoYEVycm9yIHByb2Nlc3NpbmcgY29uc29saWRhdGVkIHByb2R1Y3QgZm9yICR7c2NyYXBlZFByb2R1Y3QubmFtZX06YCwgZXJyb3IpO1xyXG4gICAgICAgIHJldHVybiBudWxsO1xyXG4gICAgfVxyXG59XHJcblxyXG4vKipcclxuICogRXh0cmFjdCBicmFuZCBuYW1lIGZyb20gcHJvZHVjdCBuYW1lIHVzaW5nIGNvbW1vbiBwYXR0ZXJuc1xyXG4gKi9cclxuZnVuY3Rpb24gZXh0cmFjdEJyYW5kRnJvbU5hbWUocHJvZHVjdE5hbWU6IHN0cmluZyk6IHN0cmluZyB8IHVuZGVmaW5lZCB7XHJcbiAgICAvLyBDb21tb24gYnJhbmQgcGF0dGVybnMgaW4gV29vbHdvcnRocyBwcm9kdWN0c1xyXG4gICAgY29uc3QgYnJhbmRQYXR0ZXJucyA9IFtcclxuICAgICAgICAvXihbQS1aXVthLXpdKyg/OlxccytbQS1aXVthLXpdKikqKVxccysvLCAgLy8gQ2FwaXRhbGl6ZSBDYXNlIGF0IHN0YXJ0XHJcbiAgICAgICAgL1xcYihTZWxlY3R8Q291bnRkb3dufEVzc2VudGlhbHN8RnJlZSBSYW5nZXxTaWduYXR1cmUpXFxiL2kgLy8gU3RvcmUgYnJhbmRzXHJcbiAgICBdO1xyXG4gICAgXHJcbiAgICBmb3IgKGNvbnN0IHBhdHRlcm4gb2YgYnJhbmRQYXR0ZXJucykge1xyXG4gICAgICAgIGNvbnN0IG1hdGNoID0gcHJvZHVjdE5hbWUubWF0Y2gocGF0dGVybik7XHJcbiAgICAgICAgaWYgKG1hdGNoKSB7XHJcbiAgICAgICAgICAgIHJldHVybiBtYXRjaFsxXS50cmltKCk7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgXHJcbiAgICByZXR1cm4gdW5kZWZpbmVkO1xyXG59XHJcblxyXG4vKipcclxuICogSGFuZGxlIGRpZmZlcmVudCBzaXplIHZhcmlhbnRzIG9mIHRoZSBzYW1lIHByb2R1Y3RcclxuICovXHJcbmFzeW5jIGZ1bmN0aW9uIGhhbmRsZVByb2R1Y3RTaXplVmFyaWFudHMoY29uc29saWRhdGVkUHJvZHVjdElkOiBzdHJpbmcsIHByb2R1Y3Q6IFByb2R1Y3QpIHtcclxuICAgIGlmICghcHJvZHVjdC5zaXplKSByZXR1cm47XHJcbiAgICBcclxuICAgIHRyeSB7XHJcbiAgICAgICAgLy8gQ2hlY2sgaWYgdGhpcyBzaXplIHZhcmlhbnQgYWxyZWFkeSBleGlzdHNcclxuICAgICAgICBjb25zdCB7IGRhdGE6IGV4aXN0aW5nVmFyaWFudCB9ID0gYXdhaXQgc3VwYWJhc2VcclxuICAgICAgICAgICAgLmZyb20oJ3Byb2R1Y3Rfc2l6ZV92YXJpYW50cycpXHJcbiAgICAgICAgICAgIC5zZWxlY3QoJ2lkJylcclxuICAgICAgICAgICAgLmVxKCdjb25zb2xpZGF0ZWRfcHJvZHVjdF9pZCcsIGNvbnNvbGlkYXRlZFByb2R1Y3RJZClcclxuICAgICAgICAgICAgLmVxKCdzaXplX25hbWUnLCBwcm9kdWN0LnNpemUpXHJcbiAgICAgICAgICAgIC5zaW5nbGUoKTtcclxuICAgICAgICAgICAgXHJcbiAgICAgICAgaWYgKGV4aXN0aW5nVmFyaWFudCkgcmV0dXJuOyAvLyBTaXplIHZhcmlhbnQgYWxyZWFkeSBleGlzdHNcclxuICAgICAgICBcclxuICAgICAgICAvLyBDYWxjdWxhdGUgbm9ybWFsaXplZCB3ZWlnaHQvdm9sdW1lIGZvciBjb21wYXJpc29uXHJcbiAgICAgICAgY29uc3Qgd2VpZ2h0R3JhbXMgPSBjYWxjdWxhdGVXZWlnaHRJbkdyYW1zKHByb2R1Y3Quc2l6ZSk7XHJcbiAgICAgICAgY29uc3Qgdm9sdW1lTUwgPSBjYWxjdWxhdGVWb2x1bWVJbk1MKHByb2R1Y3Quc2l6ZSk7XHJcbiAgICAgICAgXHJcbiAgICAgICAgLy8gSW5zZXJ0IG5ldyBzaXplIHZhcmlhbnRcclxuICAgICAgICBhd2FpdCBzdXBhYmFzZVxyXG4gICAgICAgICAgICAuZnJvbSgncHJvZHVjdF9zaXplX3ZhcmlhbnRzJylcclxuICAgICAgICAgICAgLmluc2VydCh7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xpZGF0ZWRfcHJvZHVjdF9pZDogY29uc29saWRhdGVkUHJvZHVjdElkLFxyXG4gICAgICAgICAgICAgICAgc2l6ZV9uYW1lOiBwcm9kdWN0LnNpemUsXHJcbiAgICAgICAgICAgICAgICBzaXplX3dlaWdodF9ncmFtczogd2VpZ2h0R3JhbXMsXHJcbiAgICAgICAgICAgICAgICBzaXplX3ZvbHVtZV9tbDogdm9sdW1lTUwsXHJcbiAgICAgICAgICAgICAgICBpc19wcmltYXJ5X3NpemU6IGZhbHNlIC8vIENvdWxkIGJlIGRldGVybWluZWQgYnkgbW9zdCBjb21tb24gc2l6ZVxyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgXHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoYEVycm9yIGhhbmRsaW5nIHNpemUgdmFyaWFudHMgZm9yICR7cHJvZHVjdC5uYW1lfTpgLCBlcnJvcik7XHJcbiAgICB9XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBDb252ZXJ0IHNpemUgc3RyaW5nIHRvIHdlaWdodCBpbiBncmFtcyBmb3IgY29tcGFyaXNvblxyXG4gKi9cclxuZnVuY3Rpb24gY2FsY3VsYXRlV2VpZ2h0SW5HcmFtcyhzaXplOiBzdHJpbmcpOiBudW1iZXIgfCBudWxsIHtcclxuICAgIGlmICghc2l6ZSkgcmV0dXJuIG51bGw7XHJcbiAgICBcclxuICAgIGNvbnN0IHNpemVOb3JtYWxpemVkID0gc2l6ZS50b0xvd2VyQ2FzZSgpO1xyXG4gICAgXHJcbiAgICAvLyBNYXRjaCBwYXR0ZXJucyBsaWtlIFwiNTAwZ1wiLCBcIjEuNWtnXCIsIFwiMmtnXCJcclxuICAgIGNvbnN0IGdyYW1NYXRjaCA9IHNpemVOb3JtYWxpemVkLm1hdGNoKC8oXFxkKyg/OlxcLlxcZCspPylcXHMqZ1xcYi8pO1xyXG4gICAgaWYgKGdyYW1NYXRjaCkge1xyXG4gICAgICAgIHJldHVybiBwYXJzZUludChncmFtTWF0Y2hbMV0pO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICBjb25zdCBrZ01hdGNoID0gc2l6ZU5vcm1hbGl6ZWQubWF0Y2goLyhcXGQrKD86XFwuXFxkKyk/KVxccyprZ1xcYi8pO1xyXG4gICAgaWYgKGtnTWF0Y2gpIHtcclxuICAgICAgICByZXR1cm4gTWF0aC5yb3VuZChwYXJzZUZsb2F0KGtnTWF0Y2hbMV0pICogMTAwMCk7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIHJldHVybiBudWxsO1xyXG59XHJcblxyXG4vKipcclxuICogQ29udmVydCBzaXplIHN0cmluZyB0byB2b2x1bWUgaW4gTUwgZm9yIGNvbXBhcmlzb25cclxuICovXHJcbmZ1bmN0aW9uIGNhbGN1bGF0ZVZvbHVtZUluTUwoc2l6ZTogc3RyaW5nKTogbnVtYmVyIHwgbnVsbCB7XHJcbiAgICBpZiAoIXNpemUpIHJldHVybiBudWxsO1xyXG4gICAgXHJcbiAgICBjb25zdCBzaXplTm9ybWFsaXplZCA9IHNpemUudG9Mb3dlckNhc2UoKTtcclxuICAgIFxyXG4gICAgLy8gTWF0Y2ggcGF0dGVybnMgbGlrZSBcIjUwMG1sXCIsIFwiMS41bFwiLCBcIjJsXCJcclxuICAgIGNvbnN0IG1sTWF0Y2ggPSBzaXplTm9ybWFsaXplZC5tYXRjaCgvKFxcZCsoPzpcXC5cXGQrKT8pXFxzKm1sXFxiLyk7XHJcbiAgICBpZiAobWxNYXRjaCkge1xyXG4gICAgICAgIHJldHVybiBwYXJzZUludChtbE1hdGNoWzFdKTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgY29uc3QgbE1hdGNoID0gc2l6ZU5vcm1hbGl6ZWQubWF0Y2goLyhcXGQrKD86XFwuXFxkKyk/KVxccypsXFxiLyk7XHJcbiAgICBpZiAobE1hdGNoKSB7XHJcbiAgICAgICAgcmV0dXJuIE1hdGgucm91bmQocGFyc2VGbG9hdChsTWF0Y2hbMV0pICogMTAwMCk7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIHJldHVybiBudWxsO1xyXG59XHJcblxyXG4vKipcclxuICogR2V0IGNvbnNvbGlkYXRlZCBwcm9kdWN0IGRldGFpbHMgd2l0aCBhbGwgc3RvcmUgcHJpY2VzXHJcbiAqL1xyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0Q29uc29saWRhdGVkUHJvZHVjdFdpdGhQcmljZXMoY29uc29saWRhdGVkUHJvZHVjdElkOiBzdHJpbmcpIHtcclxuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXHJcbiAgICAgICAgLmZyb20oJ2NvbnNvbGlkYXRlZF9wcm9kdWN0X2RldGFpbHMnKVxyXG4gICAgICAgIC5zZWxlY3QoJyonKVxyXG4gICAgICAgIC5lcSgnaWQnLCBjb25zb2xpZGF0ZWRQcm9kdWN0SWQpXHJcbiAgICAgICAgLnNpbmdsZSgpO1xyXG4gICAgICAgIFxyXG4gICAgaWYgKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgY29uc29saWRhdGVkIHByb2R1Y3QgZGV0YWlsczonLCBlcnJvcik7XHJcbiAgICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIHJldHVybiBkYXRhO1xyXG59XHJcblxyXG4vKipcclxuICogU2VhcmNoIGNvbnNvbGlkYXRlZCBwcm9kdWN0cyBmb3IgdGhlIGZyb250ZW5kXHJcbiAqL1xyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gc2VhcmNoQ29uc29saWRhdGVkUHJvZHVjdHMoc2VhcmNoVGVybTogc3RyaW5nLCBjYXRlZ29yeUlkPzogbnVtYmVyLCBsaW1pdCA9IDUwKSB7XHJcbiAgICBsZXQgcXVlcnkgPSBzdXBhYmFzZVxyXG4gICAgICAgIC5mcm9tKCdjb25zb2xpZGF0ZWRfcHJvZHVjdF9kZXRhaWxzJylcclxuICAgICAgICAuc2VsZWN0KCcqJylcclxuICAgICAgICAubGltaXQobGltaXQpO1xyXG4gICAgICAgIFxyXG4gICAgaWYgKHNlYXJjaFRlcm0pIHtcclxuICAgICAgICBxdWVyeSA9IHF1ZXJ5LnRleHRTZWFyY2goJ2Rpc3BsYXlfbmFtZScsIHNlYXJjaFRlcm0pO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICBpZiAoY2F0ZWdvcnlJZCkge1xyXG4gICAgICAgIHF1ZXJ5ID0gcXVlcnkuZXEoJ2NhdGVnb3J5X2lkJywgY2F0ZWdvcnlJZCk7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHF1ZXJ5O1xyXG4gICAgXHJcbiAgICBpZiAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzZWFyY2hpbmcgY29uc29saWRhdGVkIHByb2R1Y3RzOicsIGVycm9yKTtcclxuICAgICAgICByZXR1cm4gW107XHJcbiAgICB9XHJcbiAgICBcclxuICAgIHJldHVybiBkYXRhIHx8IFtdO1xyXG59IiwgIi8qKlxyXG4gKiBQcm9kdWN0IE1hdGNoaW5nIEFsZ29yaXRobSBmb3IgQ3Jvc3MtU3RvcmUgUHJvZHVjdCBJZGVudGlmaWNhdGlvblxyXG4gKiBcclxuICogVGhpcyBtb2R1bGUgcHJvdmlkZXMgZnVuY3Rpb25zIHRvIG1hdGNoIHByb2R1Y3RzIGFjcm9zcyBkaWZmZXJlbnQgc3VwZXJtYXJrZXQgc3RvcmVzXHJcbiAqIGJ5IGNvbXBhcmluZyBwcm9kdWN0IG5hbWVzLCBzaXplcywgYnJhbmRzLCBhbmQgb3RoZXIgYXR0cmlidXRlcy5cclxuICovXHJcblxyXG5pbXBvcnQgeyBjcmVhdGVDbGllbnQgfSBmcm9tICdAc3VwYWJhc2Uvc3VwYWJhc2UtanMnO1xyXG5cclxuLy8gU3VwYWJhc2UgY2xpZW50IChzaG91bGQgYmUgaW5pdGlhbGl6ZWQgd2l0aCB5b3VyIGNyZWRlbnRpYWxzKVxyXG5sZXQgc3VwYWJhc2UgPSBudWxsO1xyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIGluaXRpYWxpemVTdXBhYmFzZSh1cmwsIGtleSkge1xyXG4gICAgc3VwYWJhc2UgPSBjcmVhdGVDbGllbnQodXJsLCBrZXkpO1xyXG59XHJcblxyXG4vKipcclxuICogTm9ybWFsaXplIHByb2R1Y3QgbmFtZSBmb3IgbWF0Y2hpbmdcclxuICogUmVtb3ZlcyBicmFuZCBuYW1lcywgY29tbW9uIGRlc2NyaXB0b3JzLCBhbmQgc3RhbmRhcmRpemVzIGZvcm1hdFxyXG4gKi9cclxuZXhwb3J0IGZ1bmN0aW9uIG5vcm1hbGl6ZVByb2R1Y3ROYW1lKG5hbWUsIGJyYW5kTmFtZSA9ICcnKSB7XHJcbiAgICBsZXQgbm9ybWFsaXplZCA9IG5hbWUudG9Mb3dlckNhc2UoKS50cmltKCk7XHJcbiAgICBcclxuICAgIC8vIFJlbW92ZSBicmFuZCBuYW1lIGlmIHByb3ZpZGVkXHJcbiAgICBpZiAoYnJhbmROYW1lKSB7XHJcbiAgICAgICAgY29uc3QgYnJhbmRSZWdleCA9IG5ldyBSZWdFeHAoYnJhbmROYW1lLnRvTG93ZXJDYXNlKCksICdnaScpO1xyXG4gICAgICAgIG5vcm1hbGl6ZWQgPSBub3JtYWxpemVkLnJlcGxhY2UoYnJhbmRSZWdleCwgJycpLnRyaW0oKTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgLy8gUmVtb3ZlIGNvbW1vbiBzdG9yZS1zcGVjaWZpYyBkZXNjcmlwdG9yc1xyXG4gICAgY29uc3QgZGVzY3JpcHRvcnNUb1JlbW92ZSA9IFtcclxuICAgICAgICAnY291bnRkb3duJywgJ3dvb2x3b3J0aHMnLCAnbmV3IHdvcmxkJywgJ3Bha25zYXZlJywgJ3BhayBuIHNhdmUnLFxyXG4gICAgICAgICdzZWxlY3QnLCAncHJlbWl1bScsICd2YWx1ZScsICdidWRnZXQnLCAnc2lnbmF0dXJlJywgJ2Vzc2VudGlhbHMnLFxyXG4gICAgICAgICdvcmdhbmljJywgJ2ZyZWUgcmFuZ2UnLCAnZ3Jhc3MgZmVkJywgJ25hdHVyYWwnLCAnaG9tZSBicmFuZCdcclxuICAgIF07XHJcbiAgICBcclxuICAgIGZvciAoY29uc3QgZGVzY3JpcHRvciBvZiBkZXNjcmlwdG9yc1RvUmVtb3ZlKSB7XHJcbiAgICAgICAgY29uc3QgcmVnZXggPSBuZXcgUmVnRXhwKGBcXFxcYiR7ZGVzY3JpcHRvcn1cXFxcYmAsICdnaScpO1xyXG4gICAgICAgIG5vcm1hbGl6ZWQgPSBub3JtYWxpemVkLnJlcGxhY2UocmVnZXgsICcnKS50cmltKCk7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIC8vIFJlbW92ZSBleHRyYSB3aGl0ZXNwYWNlIGFuZCBzcGVjaWFsIGNoYXJhY3RlcnNcclxuICAgIG5vcm1hbGl6ZWQgPSBub3JtYWxpemVkXHJcbiAgICAgICAgLnJlcGxhY2UoL1teXFx3XFxzXS9nLCAnICcpICAgICAgICAgIC8vIFJlcGxhY2Ugc3BlY2lhbCBjaGFycyB3aXRoIHNwYWNlc1xyXG4gICAgICAgIC5yZXBsYWNlKC9cXHMrL2csICcgJykgICAgICAgICAgICAgLy8gQ29sbGFwc2UgbXVsdGlwbGUgc3BhY2VzXHJcbiAgICAgICAgLnJlcGxhY2UoL1xcYih0aGV8YW5kfG9yfHdpdGh8aW58b2Z8Zm9yKVxcYi9nLCAnJykgLy8gUmVtb3ZlIGNvbW1vbiB3b3Jkc1xyXG4gICAgICAgIC50cmltKCk7XHJcbiAgICBcclxuICAgIHJldHVybiBub3JtYWxpemVkO1xyXG59XHJcblxyXG4vKipcclxuICogTm9ybWFsaXplIHNpemUgZm9ybWF0IGZvciBjb21wYXJpc29uXHJcbiAqL1xyXG5leHBvcnQgZnVuY3Rpb24gbm9ybWFsaXplU2l6ZShzaXplKSB7XHJcbiAgICBpZiAoIXNpemUpIHJldHVybiAnJztcclxuICAgIFxyXG4gICAgbGV0IG5vcm1hbGl6ZWQgPSBzaXplLnRvTG93ZXJDYXNlKCkudHJpbSgpO1xyXG4gICAgXHJcbiAgICAvLyBTdGFuZGFyZGl6ZSB1bml0c1xyXG4gICAgbm9ybWFsaXplZCA9IG5vcm1hbGl6ZWRcclxuICAgICAgICAucmVwbGFjZSgvXFxibWxzP1xcYi9nLCAnbWwnKVxyXG4gICAgICAgIC5yZXBsYWNlKC9cXGJsaXRyZXM/XFxiL2csICdsJylcclxuICAgICAgICAucmVwbGFjZSgvXFxibGl0ZXJzP1xcYi9nLCAnbCcpXHJcbiAgICAgICAgLnJlcGxhY2UoL1xcYmdyYW1zP1xcYi9nLCAnZycpXHJcbiAgICAgICAgLnJlcGxhY2UoL1xcYmtpbG9zP1xcYi9nLCAna2cnKVxyXG4gICAgICAgIC5yZXBsYWNlKC9cXGJraWxvZ3JhbXM/XFxiL2csICdrZycpXHJcbiAgICAgICAgLnJlcGxhY2UoL1xcYnBhY2tzP1xcYi9nLCAncGsnKVxyXG4gICAgICAgIC5yZXBsYWNlKC9cXGJlYWNoXFxiL2csICdlYScpXHJcbiAgICAgICAgLnJlcGxhY2UoL1xcYnBlclxccytrZ1xcYi9nLCAncGVyIGtnJylcclxuICAgICAgICAucmVwbGFjZSgvXFxzKy9nLCAnJyk7XHJcbiAgICAgICAgXHJcbiAgICByZXR1cm4gbm9ybWFsaXplZDtcclxufVxyXG5cclxuLyoqXHJcbiAqIENvbnZlcnQgc2l6ZSB0byBjb21wYXJhYmxlIG51bWVyaWMgdmFsdWUgZm9yIHNvcnRpbmcvZ3JvdXBpbmdcclxuICovXHJcbmV4cG9ydCBmdW5jdGlvbiBzaXplVG9Db21wYXJhYmxlVmFsdWUoc2l6ZSkge1xyXG4gICAgaWYgKCFzaXplKSByZXR1cm4gMDtcclxuICAgIFxyXG4gICAgY29uc3Qgbm9ybWFsaXplZCA9IG5vcm1hbGl6ZVNpemUoc2l6ZSk7XHJcbiAgICBjb25zdCBtYXRjaCA9IG5vcm1hbGl6ZWQubWF0Y2goL14oXFxkKyg/OlxcLlxcZCspPykobWx8bHxnfGtnfHBrfGVhKT8vKTtcclxuICAgIFxyXG4gICAgaWYgKCFtYXRjaCkgcmV0dXJuIDA7XHJcbiAgICBcclxuICAgIGNvbnN0IHZhbHVlID0gcGFyc2VGbG9hdChtYXRjaFsxXSk7XHJcbiAgICBjb25zdCB1bml0ID0gbWF0Y2hbMl0gfHwgJyc7XHJcbiAgICBcclxuICAgIC8vIENvbnZlcnQgdG8gYmFzZSB1bml0cyBmb3IgY29tcGFyaXNvblxyXG4gICAgc3dpdGNoICh1bml0KSB7XHJcbiAgICAgICAgY2FzZSAnbCc6IHJldHVybiB2YWx1ZSAqIDEwMDA7IC8vIENvbnZlcnQgdG8gbWxcclxuICAgICAgICBjYXNlICdrZyc6IHJldHVybiB2YWx1ZSAqIDEwMDA7IC8vIENvbnZlcnQgdG8gZ1xyXG4gICAgICAgIGNhc2UgJ3BrJzpcclxuICAgICAgICBjYXNlICdlYSc6IHJldHVybiB2YWx1ZTtcclxuICAgICAgICBjYXNlICdtbCc6XHJcbiAgICAgICAgY2FzZSAnZyc6XHJcbiAgICAgICAgZGVmYXVsdDogcmV0dXJuIHZhbHVlO1xyXG4gICAgfVxyXG59XHJcblxyXG4vKipcclxuICogQ2FsY3VsYXRlIHNpbWlsYXJpdHkgc2NvcmUgYmV0d2VlbiB0d28gcHJvZHVjdCBuYW1lcyAoMC0xMDApXHJcbiAqL1xyXG5leHBvcnQgZnVuY3Rpb24gY2FsY3VsYXRlTmFtZVNpbWlsYXJpdHkobmFtZTEsIG5hbWUyKSB7XHJcbiAgICBjb25zdCBub3JtYWxpemVkMSA9IG5vcm1hbGl6ZVByb2R1Y3ROYW1lKG5hbWUxKTtcclxuICAgIGNvbnN0IG5vcm1hbGl6ZWQyID0gbm9ybWFsaXplUHJvZHVjdE5hbWUobmFtZTIpO1xyXG4gICAgXHJcbiAgICBpZiAobm9ybWFsaXplZDEgPT09IG5vcm1hbGl6ZWQyKSByZXR1cm4gMTAwO1xyXG4gICAgXHJcbiAgICAvLyBVc2UgTGV2ZW5zaHRlaW4gZGlzdGFuY2VcclxuICAgIGNvbnN0IGRpc3RhbmNlID0gbGV2ZW5zaHRlaW5EaXN0YW5jZShub3JtYWxpemVkMSwgbm9ybWFsaXplZDIpO1xyXG4gICAgY29uc3QgbWF4TGVuZ3RoID0gTWF0aC5tYXgobm9ybWFsaXplZDEubGVuZ3RoLCBub3JtYWxpemVkMi5sZW5ndGgpO1xyXG4gICAgXHJcbiAgICBpZiAobWF4TGVuZ3RoID09PSAwKSByZXR1cm4gMTAwO1xyXG4gICAgXHJcbiAgICBjb25zdCBzaW1pbGFyaXR5ID0gTWF0aC5tYXgoMCwgKDEgLSBkaXN0YW5jZSAvIG1heExlbmd0aCkgKiAxMDApO1xyXG4gICAgXHJcbiAgICAvLyBCb29zdCBzY29yZSBmb3IgZXhhY3Qgd29yZCBtYXRjaGVzXHJcbiAgICBjb25zdCB3b3JkczEgPSBub3JtYWxpemVkMS5zcGxpdCgnICcpLmZpbHRlcih3ID0+IHcubGVuZ3RoID4gMik7XHJcbiAgICBjb25zdCB3b3JkczIgPSBub3JtYWxpemVkMi5zcGxpdCgnICcpLmZpbHRlcih3ID0+IHcubGVuZ3RoID4gMik7XHJcbiAgICBjb25zdCBjb21tb25Xb3JkcyA9IHdvcmRzMS5maWx0ZXIodyA9PiB3b3JkczIuaW5jbHVkZXModykpO1xyXG4gICAgXHJcbiAgICBpZiAoY29tbW9uV29yZHMubGVuZ3RoID4gMCkge1xyXG4gICAgICAgIGNvbnN0IHdvcmRCb29zdCA9IChjb21tb25Xb3Jkcy5sZW5ndGggLyBNYXRoLm1heCh3b3JkczEubGVuZ3RoLCB3b3JkczIubGVuZ3RoKSkgKiAyMDtcclxuICAgICAgICByZXR1cm4gTWF0aC5taW4oMTAwLCBzaW1pbGFyaXR5ICsgd29yZEJvb3N0KTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgcmV0dXJuIHNpbWlsYXJpdHk7XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBDYWxjdWxhdGUgTGV2ZW5zaHRlaW4gZGlzdGFuY2UgYmV0d2VlbiB0d28gc3RyaW5nc1xyXG4gKi9cclxuZnVuY3Rpb24gbGV2ZW5zaHRlaW5EaXN0YW5jZShzdHIxLCBzdHIyKSB7XHJcbiAgICBjb25zdCBtYXRyaXggPSBbXTtcclxuICAgIFxyXG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPD0gc3RyMi5sZW5ndGg7IGkrKykge1xyXG4gICAgICAgIG1hdHJpeFtpXSA9IFtpXTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgZm9yIChsZXQgaiA9IDA7IGogPD0gc3RyMS5sZW5ndGg7IGorKykge1xyXG4gICAgICAgIG1hdHJpeFswXVtqXSA9IGo7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIGZvciAobGV0IGkgPSAxOyBpIDw9IHN0cjIubGVuZ3RoOyBpKyspIHtcclxuICAgICAgICBmb3IgKGxldCBqID0gMTsgaiA8PSBzdHIxLmxlbmd0aDsgaisrKSB7XHJcbiAgICAgICAgICAgIGlmIChzdHIyLmNoYXJBdChpIC0gMSkgPT09IHN0cjEuY2hhckF0KGogLSAxKSkge1xyXG4gICAgICAgICAgICAgICAgbWF0cml4W2ldW2pdID0gbWF0cml4W2kgLSAxXVtqIC0gMV07XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICBtYXRyaXhbaV1bal0gPSBNYXRoLm1pbihcclxuICAgICAgICAgICAgICAgICAgICBtYXRyaXhbaSAtIDFdW2ogLSAxXSArIDEsXHJcbiAgICAgICAgICAgICAgICAgICAgbWF0cml4W2ldW2ogLSAxXSArIDEsXHJcbiAgICAgICAgICAgICAgICAgICAgbWF0cml4W2kgLSAxXVtqXSArIDFcclxuICAgICAgICAgICAgICAgICk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICBcclxuICAgIHJldHVybiBtYXRyaXhbc3RyMi5sZW5ndGhdW3N0cjEubGVuZ3RoXTtcclxufVxyXG5cclxuLyoqXHJcbiAqIENhbGN1bGF0ZSBvdmVyYWxsIHByb2R1Y3QgbWF0Y2ggc2NvcmVcclxuICovXHJcbmV4cG9ydCBmdW5jdGlvbiBjYWxjdWxhdGVQcm9kdWN0TWF0Y2hTY29yZShwcm9kdWN0MSwgcHJvZHVjdDIpIHtcclxuICAgIGNvbnN0IG5hbWVTY29yZSA9IGNhbGN1bGF0ZU5hbWVTaW1pbGFyaXR5KHByb2R1Y3QxLm5hbWUsIHByb2R1Y3QyLm5hbWUpO1xyXG4gICAgXHJcbiAgICAvLyBTaXplIHNpbWlsYXJpdHkgKGV4YWN0IG1hdGNoID0gMzAgcG9pbnRzLCBzaW1pbGFyID0gMTUgcG9pbnRzKVxyXG4gICAgbGV0IHNpemVTY29yZSA9IDA7XHJcbiAgICBpZiAocHJvZHVjdDEuc2l6ZSAmJiBwcm9kdWN0Mi5zaXplKSB7XHJcbiAgICAgICAgY29uc3Qgbm9ybWFsaXplZFNpemUxID0gbm9ybWFsaXplU2l6ZShwcm9kdWN0MS5zaXplKTtcclxuICAgICAgICBjb25zdCBub3JtYWxpemVkU2l6ZTIgPSBub3JtYWxpemVTaXplKHByb2R1Y3QyLnNpemUpO1xyXG4gICAgICAgIFxyXG4gICAgICAgIGlmIChub3JtYWxpemVkU2l6ZTEgPT09IG5vcm1hbGl6ZWRTaXplMikge1xyXG4gICAgICAgICAgICBzaXplU2NvcmUgPSAzMDtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBjb25zdCB2YWx1ZTEgPSBzaXplVG9Db21wYXJhYmxlVmFsdWUocHJvZHVjdDEuc2l6ZSk7XHJcbiAgICAgICAgICAgIGNvbnN0IHZhbHVlMiA9IHNpemVUb0NvbXBhcmFibGVWYWx1ZShwcm9kdWN0Mi5zaXplKTtcclxuICAgICAgICAgICAgY29uc3Qgc2l6ZURpZmYgPSBNYXRoLmFicyh2YWx1ZTEgLSB2YWx1ZTIpIC8gTWF0aC5tYXgodmFsdWUxLCB2YWx1ZTIpO1xyXG4gICAgICAgICAgICBpZiAoc2l6ZURpZmYgPCAwLjEpIHNpemVTY29yZSA9IDIwOyAvLyBWZXJ5IHNpbWlsYXIgc2l6ZXNcclxuICAgICAgICAgICAgZWxzZSBpZiAoc2l6ZURpZmYgPCAwLjMpIHNpemVTY29yZSA9IDEwOyAvLyBTb21ld2hhdCBzaW1pbGFyIHNpemVzXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgXHJcbiAgICAvLyBCcmFuZCBzaW1pbGFyaXR5IChpZiBhdmFpbGFibGUpXHJcbiAgICBsZXQgYnJhbmRTY29yZSA9IDA7XHJcbiAgICBpZiAocHJvZHVjdDEuYnJhbmQgJiYgcHJvZHVjdDIuYnJhbmQpIHtcclxuICAgICAgICBpZiAocHJvZHVjdDEuYnJhbmQudG9Mb3dlckNhc2UoKSA9PT0gcHJvZHVjdDIuYnJhbmQudG9Mb3dlckNhc2UoKSkge1xyXG4gICAgICAgICAgICBicmFuZFNjb3JlID0gMjA7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgXHJcbiAgICAvLyBDYWxjdWxhdGUgd2VpZ2h0ZWQgdG90YWwgKG5hbWUgaXMgbW9zdCBpbXBvcnRhbnQpXHJcbiAgICBjb25zdCB0b3RhbFNjb3JlID0gKG5hbWVTY29yZSAqIDAuNSkgKyAoc2l6ZVNjb3JlICogMC4zKSArIChicmFuZFNjb3JlICogMC4yKTtcclxuICAgIFxyXG4gICAgcmV0dXJuIE1hdGgucm91bmQodG90YWxTY29yZSk7XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBGaW5kIHBvdGVudGlhbCBtYXRjaGVzIGZvciBhIHByb2R1Y3QgaW4gdGhlIGRhdGFiYXNlXHJcbiAqL1xyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZmluZFBvdGVudGlhbE1hdGNoZXMocHJvZHVjdCwgbWluU2NvcmUgPSA3MCkge1xyXG4gICAgaWYgKCFzdXBhYmFzZSkge1xyXG4gICAgICAgIHRocm93IG5ldyBFcnJvcignU3VwYWJhc2UgY2xpZW50IG5vdCBpbml0aWFsaXplZCcpO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICAvLyBHZXQgbm9ybWFsaXplZCBuYW1lIGZvciBpbml0aWFsIGZpbHRlcmluZ1xyXG4gICAgY29uc3Qgbm9ybWFsaXplZE5hbWUgPSBub3JtYWxpemVQcm9kdWN0TmFtZShwcm9kdWN0Lm5hbWUpO1xyXG4gICAgY29uc3QgbmFtZVdvcmRzID0gbm9ybWFsaXplZE5hbWUuc3BsaXQoJyAnKS5maWx0ZXIodyA9PiB3Lmxlbmd0aCA+IDIpO1xyXG4gICAgXHJcbiAgICAvLyBTZWFyY2ggZm9yIHByb2R1Y3RzIHdpdGggc2ltaWxhciBuYW1lcyB1c2luZyBmdWxsLXRleHQgc2VhcmNoXHJcbiAgICBjb25zdCB7IGRhdGE6IGNhbmRpZGF0ZXMsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxyXG4gICAgICAgIC5mcm9tKCdjb25zb2xpZGF0ZWRfcHJvZHVjdHMnKVxyXG4gICAgICAgIC5zZWxlY3QoYFxyXG4gICAgICAgICAgICBpZCwgXHJcbiAgICAgICAgICAgIGRpc3BsYXlfbmFtZSwgXHJcbiAgICAgICAgICAgIG5vcm1hbGl6ZWRfbmFtZSxcclxuICAgICAgICAgICAgcHJpbWFyeV9zaXplLFxyXG4gICAgICAgICAgICBicmFuZF9pZCxcclxuICAgICAgICAgICAgYnJhbmRzKG5hbWUpXHJcbiAgICAgICAgYClcclxuICAgICAgICAudGV4dFNlYXJjaCgnbm9ybWFsaXplZF9uYW1lJywgbmFtZVdvcmRzLmpvaW4oJyB8ICcpKTtcclxuICAgIFxyXG4gICAgaWYgKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc2VhcmNoaW5nIGZvciBwcm9kdWN0IG1hdGNoZXM6JywgZXJyb3IpO1xyXG4gICAgICAgIHJldHVybiBbXTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgLy8gU2NvcmUgZWFjaCBjYW5kaWRhdGVcclxuICAgIGNvbnN0IG1hdGNoZXMgPSBbXTtcclxuICAgIGZvciAoY29uc3QgY2FuZGlkYXRlIG9mIGNhbmRpZGF0ZXMgfHwgW10pIHtcclxuICAgICAgICBjb25zdCBjYW5kaWRhdGVQcm9kdWN0ID0ge1xyXG4gICAgICAgICAgICBuYW1lOiBjYW5kaWRhdGUuZGlzcGxheV9uYW1lLFxyXG4gICAgICAgICAgICBzaXplOiBjYW5kaWRhdGUucHJpbWFyeV9zaXplLFxyXG4gICAgICAgICAgICBicmFuZDogY2FuZGlkYXRlLmJyYW5kcz8ubmFtZVxyXG4gICAgICAgIH07XHJcbiAgICAgICAgXHJcbiAgICAgICAgY29uc3Qgc2NvcmUgPSBjYWxjdWxhdGVQcm9kdWN0TWF0Y2hTY29yZShwcm9kdWN0LCBjYW5kaWRhdGVQcm9kdWN0KTtcclxuICAgICAgICBcclxuICAgICAgICBpZiAoc2NvcmUgPj0gbWluU2NvcmUpIHtcclxuICAgICAgICAgICAgbWF0Y2hlcy5wdXNoKHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGlkYXRlZFByb2R1Y3RJZDogY2FuZGlkYXRlLmlkLFxyXG4gICAgICAgICAgICAgICAgZGlzcGxheU5hbWU6IGNhbmRpZGF0ZS5kaXNwbGF5X25hbWUsXHJcbiAgICAgICAgICAgICAgICBzY29yZTogc2NvcmUsXHJcbiAgICAgICAgICAgICAgICBjb25maWRlbmNlOiBzY29yZSA+PSA5MCA/ICdoaWdoJyA6IHNjb3JlID49IDgwID8gJ21lZGl1bScgOiAnbG93J1xyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICBcclxuICAgIC8vIFNvcnQgYnkgc2NvcmUgZGVzY2VuZGluZ1xyXG4gICAgcmV0dXJuIG1hdGNoZXMuc29ydCgoYSwgYikgPT4gYi5zY29yZSAtIGEuc2NvcmUpO1xyXG59XHJcblxyXG4vKipcclxuICogQ3JlYXRlIG9yIGxpbmsgYSBwcm9kdWN0IHRvIGNvbnNvbGlkYXRlZCBwcm9kdWN0c1xyXG4gKi9cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNvbnNvbGlkYXRlUHJvZHVjdChzdG9yZVByb2R1Y3QsIHN0b3JlSWQpIHtcclxuICAgIGlmICghc3VwYWJhc2UpIHtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1N1cGFiYXNlIGNsaWVudCBub3QgaW5pdGlhbGl6ZWQnKTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgLy8gRmluZCBwb3RlbnRpYWwgbWF0Y2hlc1xyXG4gICAgY29uc3QgbWF0Y2hlcyA9IGF3YWl0IGZpbmRQb3RlbnRpYWxNYXRjaGVzKHN0b3JlUHJvZHVjdCwgNzApO1xyXG4gICAgXHJcbiAgICBsZXQgY29uc29saWRhdGVkUHJvZHVjdElkO1xyXG4gICAgXHJcbiAgICBpZiAobWF0Y2hlcy5sZW5ndGggPiAwICYmIG1hdGNoZXNbMF0uc2NvcmUgPj0gODUpIHtcclxuICAgICAgICAvLyBIaWdoIGNvbmZpZGVuY2UgbWF0Y2ggLSB1c2UgZXhpc3RpbmcgY29uc29saWRhdGVkIHByb2R1Y3RcclxuICAgICAgICBjb25zb2xpZGF0ZWRQcm9kdWN0SWQgPSBtYXRjaGVzWzBdLmNvbnNvbGlkYXRlZFByb2R1Y3RJZDtcclxuICAgICAgICBjb25zb2xlLmxvZyhgTGlua2VkICR7c3RvcmVQcm9kdWN0Lm5hbWV9IHRvIGV4aXN0aW5nIGNvbnNvbGlkYXRlZCBwcm9kdWN0IChzY29yZTogJHttYXRjaGVzWzBdLnNjb3JlfSlgKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgICAgLy8gQ3JlYXRlIG5ldyBjb25zb2xpZGF0ZWQgcHJvZHVjdFxyXG4gICAgICAgIGNvbnN0IG5vcm1hbGl6ZWROYW1lID0gbm9ybWFsaXplUHJvZHVjdE5hbWUoc3RvcmVQcm9kdWN0Lm5hbWUpO1xyXG4gICAgICAgIFxyXG4gICAgICAgIC8vIERldGVybWluZSBjYXRlZ29yeSBmcm9tIHN0b3JlIHByb2R1Y3QgY2F0ZWdvcnlcclxuICAgICAgICBjb25zdCBjYXRlZ29yeUlkID0gYXdhaXQgbWFwU3RvcmVDYXRlZ29yeShzdG9yZVByb2R1Y3QuY2F0ZWdvcnkpO1xyXG4gICAgICAgIFxyXG4gICAgICAgIGNvbnN0IHsgZGF0YTogbmV3UHJvZHVjdCwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXHJcbiAgICAgICAgICAgIC5mcm9tKCdjb25zb2xpZGF0ZWRfcHJvZHVjdHMnKVxyXG4gICAgICAgICAgICAuaW5zZXJ0KHtcclxuICAgICAgICAgICAgICAgIG5vcm1hbGl6ZWRfbmFtZTogbm9ybWFsaXplZE5hbWUsXHJcbiAgICAgICAgICAgICAgICBkaXNwbGF5X25hbWU6IHN0b3JlUHJvZHVjdC5uYW1lLFxyXG4gICAgICAgICAgICAgICAgcHJpbWFyeV9zaXplOiBzdG9yZVByb2R1Y3Quc2l6ZSxcclxuICAgICAgICAgICAgICAgIGNhdGVnb3J5X2lkOiBjYXRlZ29yeUlkLFxyXG4gICAgICAgICAgICAgICAgbWF0Y2hfY29uZmlkZW5jZTogbWF0Y2hlcy5sZW5ndGggPiAwID8gbWF0Y2hlc1swXS5zY29yZSA6IDEwMFxyXG4gICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAuc2VsZWN0KClcclxuICAgICAgICAgICAgLnNpbmdsZSgpO1xyXG4gICAgICAgICAgICBcclxuICAgICAgICBpZiAoZXJyb3IpIHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY3JlYXRpbmcgY29uc29saWRhdGVkIHByb2R1Y3Q6JywgZXJyb3IpO1xyXG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcclxuICAgICAgICB9XHJcbiAgICAgICAgXHJcbiAgICAgICAgY29uc29saWRhdGVkUHJvZHVjdElkID0gbmV3UHJvZHVjdC5pZDtcclxuICAgICAgICBjb25zb2xlLmxvZyhgQ3JlYXRlZCBuZXcgY29uc29saWRhdGVkIHByb2R1Y3QgZm9yICR7c3RvcmVQcm9kdWN0Lm5hbWV9YCk7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIC8vIExpbmsgc3RvcmUgcHJvZHVjdCB0byBjb25zb2xpZGF0ZWQgcHJvZHVjdFxyXG4gICAgYXdhaXQgbGlua1N0b3JlUHJvZHVjdChzdG9yZVByb2R1Y3QsIHN0b3JlSWQsIGNvbnNvbGlkYXRlZFByb2R1Y3RJZCk7XHJcbiAgICBcclxuICAgIHJldHVybiBjb25zb2xpZGF0ZWRQcm9kdWN0SWQ7XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBMaW5rIHN0b3JlLXNwZWNpZmljIHByb2R1Y3QgdG8gY29uc29saWRhdGVkIHByb2R1Y3RcclxuICovXHJcbmFzeW5jIGZ1bmN0aW9uIGxpbmtTdG9yZVByb2R1Y3Qoc3RvcmVQcm9kdWN0LCBzdG9yZUlkLCBjb25zb2xpZGF0ZWRQcm9kdWN0SWQpIHtcclxuICAgIGNvbnN0IHsgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXHJcbiAgICAgICAgLmZyb20oJ3Byb2R1Y3RfdmFyaWFudHMnKVxyXG4gICAgICAgIC51cHNlcnQoe1xyXG4gICAgICAgICAgICBjb25zb2xpZGF0ZWRfcHJvZHVjdF9pZDogY29uc29saWRhdGVkUHJvZHVjdElkLFxyXG4gICAgICAgICAgICBzdG9yZV9wcm9kdWN0X2lkOiBzdG9yZVByb2R1Y3QuaWQsXHJcbiAgICAgICAgICAgIHN0b3JlX2lkOiBzdG9yZUlkLFxyXG4gICAgICAgICAgICBzdG9yZV9uYW1lOiBzdG9yZVByb2R1Y3QubmFtZSxcclxuICAgICAgICAgICAgc3RvcmVfc2l6ZTogc3RvcmVQcm9kdWN0LnNpemUsXHJcbiAgICAgICAgICAgIHN0b3JlX3VuaXRfcHJpY2U6IHN0b3JlUHJvZHVjdC51bml0UHJpY2UsXHJcbiAgICAgICAgICAgIHN0b3JlX3VuaXRfbmFtZTogc3RvcmVQcm9kdWN0LnVuaXROYW1lLFxyXG4gICAgICAgICAgICBsYXN0X3NlZW46IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcclxuICAgICAgICAgICAgaXNfYWN0aXZlOiB0cnVlXHJcbiAgICAgICAgfSk7XHJcbiAgICAgICAgXHJcbiAgICBpZiAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsaW5raW5nIHN0b3JlIHByb2R1Y3Q6JywgZXJyb3IpO1xyXG4gICAgfVxyXG59XHJcblxyXG4vKipcclxuICogTWFwIHN0b3JlIGNhdGVnb3JpZXMgdG8gdW5pZmllZCBjYXRlZ29yeSBoaWVyYXJjaHlcclxuICovXHJcbmFzeW5jIGZ1bmN0aW9uIG1hcFN0b3JlQ2F0ZWdvcnkoc3RvcmVDYXRlZ29yaWVzKSB7XHJcbiAgICBpZiAoIXN0b3JlQ2F0ZWdvcmllcyB8fCBzdG9yZUNhdGVnb3JpZXMubGVuZ3RoID09PSAwKSByZXR1cm4gbnVsbDtcclxuICAgIFxyXG4gICAgY29uc3QgY2F0ZWdvcnlNYXBwaW5ncyA9IHtcclxuICAgICAgICAnZnJ1aXQtdmVnJzogJ0ZydWl0ICYgVmVnZXRhYmxlcycsXHJcbiAgICAgICAgJ2ZyZXNoLWZvb2RzLWFuZC1iYWtlcnknOiAnRnJlc2ggRm9vZHMnLFxyXG4gICAgICAgICdtZWF0LXBvdWx0cnknOiAnTWVhdCAmIFBvdWx0cnknLCBcclxuICAgICAgICAnZmlzaC1zZWFmb29kJzogJ0Zpc2ggJiBTZWFmb29kJyxcclxuICAgICAgICAnYmFrZXJ5JzogJ0Jha2VyeScsXHJcbiAgICAgICAgJ2ZyaWRnZS1kZWxpJzogJ0RhaXJ5ICYgRGVsaScsXHJcbiAgICAgICAgJ2NoaWxsZWQtZnJvemVuLWFuZC1kZXNzZXJ0cyc6ICdDaGlsbGVkICYgRnJvemVuJyxcclxuICAgICAgICAnZnJvemVuJzogJ0Zyb3plbiBGb29kcycsXHJcbiAgICAgICAgJ3BhbnRyeSc6ICdQYW50cnkgJiBEcnkgR29vZHMnLFxyXG4gICAgICAgICdkcmlua3MnOiAnQ29sZCBEcmlua3MnLFxyXG4gICAgICAgICdob3QtYW5kLWNvbGQtZHJpbmtzJzogJ0JldmVyYWdlcycsXHJcbiAgICAgICAgJ2JlZXItd2luZSc6ICdCZWVyLCBXaW5lICYgQ2lkZXInLFxyXG4gICAgICAgICdiZWVyLXdpbmUtYW5kLWNpZGVyJzogJ0JlZXIsIFdpbmUgJiBDaWRlcicsXHJcbiAgICAgICAgJ2hlYWx0aC1ib2R5JzogJ0hlYWx0aCAmIEJvZHknLFxyXG4gICAgICAgICdoZWFsdGgtYW5kLWJvZHknOiAnSGVhbHRoICYgQm9keScsXHJcbiAgICAgICAgJ2hvdXNlaG9sZCc6ICdIb3VzZWhvbGQgJiBDbGVhbmluZycsXHJcbiAgICAgICAgJ2hvdXNlaG9sZC1hbmQtY2xlYW5pbmcnOiAnSG91c2Vob2xkICYgQ2xlYW5pbmcnLFxyXG4gICAgICAgICdiYWJ5LWNoaWxkJzogJ0JhYnkgJiBUb2RkbGVyJyxcclxuICAgICAgICAnYmFieS1hbmQtdG9kZGxlcic6ICdCYWJ5ICYgVG9kZGxlcicsXHJcbiAgICAgICAgJ3BldCc6ICdQZXQgU3VwcGxpZXMnLFxyXG4gICAgICAgICdwZXRzJzogJ1BldCBTdXBwbGllcydcclxuICAgIH07XHJcbiAgICBcclxuICAgIC8vIFRyeSB0byBtYXAgZmlyc3QgY2F0ZWdvcnlcclxuICAgIGNvbnN0IGZpcnN0Q2F0ZWdvcnkgPSBzdG9yZUNhdGVnb3JpZXNbMF0udG9Mb3dlckNhc2UoKS5yZXBsYWNlKC9cXHMrL2csICctJyk7XHJcbiAgICBjb25zdCBtYXBwZWRDYXRlZ29yeSA9IGNhdGVnb3J5TWFwcGluZ3NbZmlyc3RDYXRlZ29yeV07XHJcbiAgICBcclxuICAgIGlmIChtYXBwZWRDYXRlZ29yeSkge1xyXG4gICAgICAgIGNvbnN0IHsgZGF0YSB9ID0gYXdhaXQgc3VwYWJhc2VcclxuICAgICAgICAgICAgLmZyb20oJ2NhdGVnb3J5X2hpZXJhcmNoeScpXHJcbiAgICAgICAgICAgIC5zZWxlY3QoJ2lkJylcclxuICAgICAgICAgICAgLmVxKCduYW1lJywgbWFwcGVkQ2F0ZWdvcnkpXHJcbiAgICAgICAgICAgIC5zaW5nbGUoKTtcclxuICAgICAgICAgICAgXHJcbiAgICAgICAgcmV0dXJuIGRhdGE/LmlkO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICByZXR1cm4gbnVsbDtcclxufVxyXG5cclxuLyoqXHJcbiAqIFN0b3JlIGNvbnNvbGlkYXRlZCBwcmljZSBkYXRhXHJcbiAqL1xyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gc3RvcmVDb25zb2xpZGF0ZWRQcmljZShjb25zb2xpZGF0ZWRQcm9kdWN0SWQsIHN0b3JlSWQsIHByaWNlLCBpc1NwZWNpYWwgPSBmYWxzZSkge1xyXG4gICAgaWYgKCFzdXBhYmFzZSkge1xyXG4gICAgICAgIHRocm93IG5ldyBFcnJvcignU3VwYWJhc2UgY2xpZW50IG5vdCBpbml0aWFsaXplZCcpO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxyXG4gICAgICAgIC5mcm9tKCdjb25zb2xpZGF0ZWRfcHJpY2VzJylcclxuICAgICAgICAuaW5zZXJ0KHtcclxuICAgICAgICAgICAgY29uc29saWRhdGVkX3Byb2R1Y3RfaWQ6IGNvbnNvbGlkYXRlZFByb2R1Y3RJZCxcclxuICAgICAgICAgICAgc3RvcmVfaWQ6IHN0b3JlSWQsXHJcbiAgICAgICAgICAgIHByaWNlOiBwcmljZSxcclxuICAgICAgICAgICAgaXNfc3BlY2lhbDogaXNTcGVjaWFsLFxyXG4gICAgICAgICAgICB3YXNfYXZhaWxhYmxlOiB0cnVlLFxyXG4gICAgICAgICAgICByZWNvcmRlZF9hdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXHJcbiAgICAgICAgfSk7XHJcbiAgICAgICAgXHJcbiAgICBpZiAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzdG9yaW5nIGNvbnNvbGlkYXRlZCBwcmljZTonLCBlcnJvcik7XHJcbiAgICB9XHJcbn0iLCAiLy8gVGhpcyBmaWxlIGlzIGZvciBtYW51YWxseSBvdmVycmlkaW5nIHByb2R1Y3Qgc2l6ZSBhbmQgY2F0ZWdvcnkgZGF0YS5cbi8vIElzIHVzZWQgZm9yIHByb2R1Y3RzIHRoYXQgZG8gbm90IGhhdmUgbGlzdGVkIHNpemVzIHRvIGJlIHNjcmFwZWQsIG9yIGhhdmUgaW5jb3JyZWN0IGNhdGVnb3JpZXMuXG5cbmV4cG9ydCBjb25zdCBwcm9kdWN0T3ZlcnJpZGVzID0gW1xuICB7IGlkOiAnMjA2ODg5Jywgc2l6ZTogJzE4MGcnIH0sXG4gIHsgaWQ6ICcxOTY5OTYnLCBzaXplOiAnMzAwZycgfSxcbiAgeyBpZDogJzEzNzk2NycsIHNpemU6ICc0MjBnJyB9LFxuICB7IGlkOiAnMTI1ODU2Jywgc2l6ZTogJzQ1MGcnIH0sXG4gIHsgaWQ6ICcxODkyNjgnLCBzaXplOiAnMS4xM2tnJyB9LFxuICB7IGlkOiAnMTg5MTUwJywgc2l6ZTogJzEuMmtnJyB9LFxuICB7IGlkOiAnMTkwNDU0Jywgc2l6ZTogJzIuMWtnJyB9LFxuICB7IGlkOiAnMTg5MDc4Jywgc2l6ZTogJzEuM2tnJyB9LFxuICB7IGlkOiAnMTg5MTM2Jywgc2l6ZTogJzEuMmtnJyB9LFxuICB7IGlkOiAnNzU1MjM3Jywgc2l6ZTogJzkzMWcnIH0sXG4gIHsgaWQ6ICc3NTUzMDQnLCBzaXplOiAnMS4xa2cnIH0sXG4gIHsgaWQ6ICc3NTUyNDYnLCBzaXplOiAnMTAyMGcnIH0sXG4gIHsgaWQ6ICc3NTUyNDUnLCBzaXplOiAnMS4ya2cnIH0sXG4gIHsgaWQ6ICcxMTIyNzMnLCBzaXplOiAnODY1bWwnIH0sXG4gIHsgaWQ6ICcyNjk1MTQnLCBzaXplOiAnNTg0bWwnIH0sXG4gIHsgaWQ6ICcyNjk1MTUnLCBzaXplOiAnNTg0bWwnIH0sXG4gIHsgaWQ6ICcxMTY1MTgnLCBzaXplOiAnNDQwbWwnIH0sXG4gIHsgaWQ6ICcxNTExOTEnLCBzaXplOiAnNTcwbWwnIH0sXG4gIHsgaWQ6ICcyNzk5MDQnLCBzaXplOiAnNTc1bWwnIH0sXG4gIHsgaWQ6ICcxNDYxNDknLCBzaXplOiAnMTAwMG1sJyB9LFxuICB7IGlkOiAnNzkxOTI1Jywgc2l6ZTogJzUyNWcnIH0sXG4gIHsgaWQ6ICc3NzQyMTYnLCBzaXplOiAnNTI1ZycgfSxcbiAgeyBpZDogJzc4NDQwNicsIHNpemU6ICc1MjVnJyB9LFxuICB7IGlkOiAnNzkxOTE2Jywgc2l6ZTogJzUyNWcnIH0sXG4gIHsgaWQ6ICczMDY2MjQnLCBzaXplOiAnMTg1ZycgfSxcbiAgeyBpZDogJzE1NjgyNCcsIHNpemU6ICcxODBnJyB9LFxuICB7IGlkOiAnOTAyMycsIHNpemU6ICczNzVnJyB9LFxuICB7IGlkOiAnMjY2OTYyJywgY2F0ZWdvcnk6ICdzd2VldHMtbG9sbGllcycgfSxcbiAgeyBpZDogJzE3MTUyNCcsIHNpemU6ICcyMzBtbCcsIGNhdGVnb3J5OiAnYmFraW5nJyB9LFxuICB7IGlkOiAnMTcwMDIxJywgY2F0ZWdvcnk6ICdpY2UtYmxvY2tzJyB9LFxuICB7IGlkOiAnNzExNjQnLCBjYXRlZ29yeTogJ3NhdXNhZ2VzJyB9LFxuICB7IGlkOiAnNzExNzQnLCBjYXRlZ29yeTogJ3NhdXNhZ2VzJyB9LFxuICB7IGlkOiAnNzExNjgnLCBjYXRlZ29yeTogJ3NhdXNhZ2VzJyB9LFxuICB7IGlkOiAnNzExNjUnLCBjYXRlZ29yeTogJ3NhdXNhZ2VzJyB9LFxuICB7IGlkOiAnMzMxNTYwJywgY2F0ZWdvcnk6ICdzcGVjaWFsdHktYnJlYWQnIH0sXG4gIHsgaWQ6ICc2Nzk0MTInLCBjYXRlZ29yeTogJ2hlcmJhbC10ZWEnIH0sXG4gIHsgaWQ6ICc3OTAxMjknLCBjYXRlZ29yeTogJ2hlcmJhbC10ZWEnIH0sXG4gIHsgaWQ6ICcyNjc0OTInLCBjYXRlZ29yeTogJ2hlcmJhbC10ZWEnIH0sXG4gIHsgaWQ6ICcyNjc0ODUnLCBjYXRlZ29yeTogJ2hlcmJhbC10ZWEnIH0sXG4gIHsgaWQ6ICc0MTMzMDInLCBjYXRlZ29yeTogJ2hlcmJhbC10ZWEnIH0sXG4gIHsgaWQ6ICcyNjc0ODgnLCBjYXRlZ29yeTogJ2hlcmJhbC10ZWEnIH0sXG4gIHsgaWQ6ICc3NjA4NzInLCBjYXRlZ29yeTogJ2hlcmJhbC10ZWEnIH0sXG4gIHsgaWQ6ICc2ODExNzcnLCBjYXRlZ29yeTogJ2hlcmJhbC10ZWEnIH0sXG4gIHsgaWQ6ICc5NTA5MScsIGNhdGVnb3J5OiAnaGVyYmFsLXRlYScgfSxcbiAgeyBpZDogJzc2MTA5MycsIGNhdGVnb3J5OiAnYmxhY2stdGVhJyB9LFxuICB7IGlkOiAnNzIxNjYxJywgY2F0ZWdvcnk6ICdncmVlbi10ZWEnIH0sXG4gIHsgaWQ6ICc3OTAxMjknLCBjYXRlZ29yeTogJ2hlcmJhbC10ZWEnIH0sXG4gIHsgaWQ6ICcyNjc0OTInLCBjYXRlZ29yeTogJ2hlcmJhbC10ZWEnIH0sXG4gIHsgaWQ6ICcyNjc0ODUnLCBjYXRlZ29yeTogJ2hlcmJhbC10ZWEnIH0sXG4gIHsgaWQ6ICc3MjEwMzQnLCBjYXRlZ29yeTogJ2hlcmJhbC10ZWEnIH0sXG4gIHsgaWQ6ICc0MTMzMDInLCBjYXRlZ29yeTogJ2hlcmJhbC10ZWEnIH0sXG4gIHsgaWQ6ICcyNjc0ODgnLCBjYXRlZ29yeTogJ2hlcmJhbC10ZWEnIH0sXG4gIHsgaWQ6ICc3NjA4NzInLCBjYXRlZ29yeTogJ2hlcmJhbC10ZWEnIH0sXG4gIHsgaWQ6ICc2ODExNzcnLCBjYXRlZ29yeTogJ2hlcmJhbC10ZWEnIH0sXG4gIHsgaWQ6ICc5NTA5MS4nLCBjYXRlZ29yeTogJ2hlcmJhbC10ZWEnIH0sXG4gIHsgaWQ6ICcxODQwOTAnLCBjYXRlZ29yeTogJ2hlcmJhbC10ZWEnIH0sXG4gIHsgaWQ6ICc3NjEwOTMnLCBjYXRlZ29yeTogJ2JsYWNrLXRlYScgfSxcbiAgeyBpZDogJzcyMTY2MScsIGNhdGVnb3J5OiAnZ3JlZW4tdGVhJyB9LFxuICB7IGlkOiAnNjkwMDkzJywgY2F0ZWdvcnk6ICdncmVlbi10ZWEnIH0sXG4gIHsgaWQ6ICc3ODA5MjInLCBjYXRlZ29yeTogJ3NhdWNlcycgfSxcbiAgeyBpZDogJzc4MDkyMScsIGNhdGVnb3J5OiAnc2F1Y2VzJyB9LFxuICB7IGlkOiAnNzI2MTgnLCBjYXRlZ29yeTogJ2JsYWNrLXRlYScgfSxcbiAgeyBpZDogJzYwNTMnLCBjYXRlZ29yeTogJ2JsYWNrLXRlYScgfSxcbiAgeyBpZDogJzcyNjE3JywgY2F0ZWdvcnk6ICdibGFjay10ZWEnIH0sXG4gIHsgaWQ6ICcxNjgwNjgnLCBjYXRlZ29yeTogJ2JsYWNrLXRlYScgfSxcbiAgeyBpZDogJzYwNTInLCBjYXRlZ29yeTogJ2JsYWNrLXRlYScgfSxcbiAgeyBpZDogJzc2MTQzNicsIGNhdGVnb3J5OiAnYmxhY2stdGVhJyB9LFxuXTtcbiJdLAogICJtYXBwaW5ncyI6ICI7QUFBQSxZQUFZQSxhQUFZO0FBSXhCLE9BQU8sZ0JBQWdCO0FBQ3ZCLFlBQVksYUFBYTtBQUN6QixPQUFPLE9BQU87QUFDZCxTQUFTLGtCQUFrQjs7O0FDUDNCLFNBQVMsb0JBQW9DO0FBQzdDLFlBQVksWUFBWTs7O0FDQXhCLFNBQVMsb0JBQW9CO0FBRzdCLElBQU0sZUFBZTtBQUNyQixJQUFNLGlCQUFpQjtBQUN2QixJQUFNLGlCQUFpQjtBQUVoQixJQUFNLFNBQVM7QUFBQSxFQUNwQixLQUFLO0FBQUEsRUFDTCxPQUFPO0FBQUEsRUFDUCxRQUFRO0FBQUEsRUFDUixNQUFNO0FBQUEsRUFDTixTQUFTO0FBQUEsRUFDVCxNQUFNO0FBQUEsRUFDTixPQUFPO0FBQUEsRUFDUCxTQUFTO0FBQUEsRUFDVCxNQUFNO0FBQUEsRUFDTixRQUFRO0FBQUEsRUFDUixLQUFLO0FBQ1A7QUFNTyxTQUFTLElBQUlDLFNBQWdCLE1BQWM7QUFDaEQsUUFBTSxRQUFRO0FBQ2QsVUFBUSxJQUFJLEdBQUdBLE9BQU0sS0FBSyxLQUFLLElBQUksSUFBSTtBQUN6QztBQU1PLFNBQVMsU0FBUyxNQUFjO0FBQ3JDLE1BQUksT0FBTyxLQUFLLElBQUk7QUFDdEI7QUFNTyxTQUFTLGNBQWMsU0FBa0I7QUFDOUMsUUFBTSxrQkFBa0IsUUFBUSxZQUFZLElBQUksUUFBUSxVQUFVLFFBQVEsQ0FBQyxDQUFDLEtBQUssUUFBUSxRQUFRLEtBQUs7QUFDdEc7QUFBQSxJQUNFLHdCQUF3QixPQUFPLEtBQUssT0FBTyxLQUFLO0FBQUEsSUFDaEQsR0FBRyxRQUFRLEdBQUcsU0FBUyxZQUFZLENBQUMsTUFDakMsUUFBUSxLQUFLLE1BQU0sR0FBRyxjQUFjLEVBQUUsT0FBTyxjQUFjLENBQUMsTUFDNUQsUUFBUSxNQUFNLE1BQU0sR0FBRyxjQUFjLEVBQUUsT0FBTyxjQUFjLENBQUMsUUFDM0QsUUFBUSxhQUFhLFFBQVEsQ0FBQyxFQUFFLFNBQVMsQ0FBQyxFQUFFLE9BQU8sQ0FBQyxDQUFDLFFBQzFEO0FBQUEsRUFDRjtBQUNGO0FBS08sU0FBUyxpQkFBaUI7QUFDL0I7QUFBQSxJQUNFLE9BQU87QUFBQSxJQUNQLEdBQUcsS0FBSyxTQUFTLFlBQVksQ0FBQyxNQUFNLE9BQU8sT0FBTyxjQUFjLENBQUMsTUFDOUQsT0FBTyxPQUFPLGNBQWMsQ0FBQyxNQUM3QixRQUFRLE9BQU8sQ0FBQyxDQUFDO0FBQUEsRUFDdEI7QUFFQSxNQUFJLGFBQWE7QUFDakIsV0FBUyxJQUFJLEdBQUcsSUFBSSxLQUFLLEtBQUs7QUFDNUIsa0JBQWM7QUFBQSxFQUNoQjtBQUNBLE1BQUksT0FBTyxRQUFRLFVBQVU7QUFFL0I7QUFPQSxJQUFJLHVCQUF1QjtBQUMzQixTQUFTLHdCQUF3QixTQUFpQixTQUFpQjtBQUNqRSx5QkFBdUIsdUJBQXVCLFFBQVE7QUFDdEQsU0FBTyx1QkFBdUIsVUFBVTtBQUMxQztBQU1PLFNBQVMsc0JBQXNCLFVBQTRCO0FBQ2hFLE1BQUk7QUFDRixVQUFNLE9BQU8sYUFBYSxVQUFVLE9BQU87QUFDM0MsVUFBTSxTQUFTLEtBQUssTUFBTSxPQUFPLEVBQUUsT0FBTyxDQUFDLFNBQVM7QUFDbEQsVUFBSSxLQUFLLEtBQUssRUFBRSxTQUFTLEVBQUcsUUFBTztBQUFBLFVBQzlCLFFBQU87QUFBQSxJQUNkLENBQUM7QUFDRCxXQUFPO0FBQUEsRUFDVCxTQUFTLE9BQU87QUFDZCxVQUFNLG1CQUFtQjtBQUFBLEVBQzNCO0FBQ0Y7QUFNTyxTQUFTLG9CQUFvQkMsWUFBMkI7QUFDN0QsTUFBSSxzQkFBOEIsS0FBSyxJQUFJLElBQUlBLGNBQWE7QUFDNUQsTUFBSSxvQkFBNEIsS0FBSyxNQUFNLGtCQUFrQixFQUFFLFNBQVM7QUFHeEUsTUFBSSxzQkFBc0IsSUFBSTtBQUM1QixXQUNFLEtBQUssTUFBTSxxQkFBcUIsRUFBRSxJQUNsQyxNQUNBLEtBQUssTUFBTSxxQkFBcUIsRUFBRSxFQUMvQixTQUFTLEVBQ1QsU0FBUyxHQUFHLEdBQUc7QUFBQSxFQUd0QixNQUFPLFFBQU8sb0JBQW9CO0FBQ3BDO0FBNEZPLFNBQVMsWUFBWSxLQUFhO0FBQ3ZDLFNBQU8sSUFBSSxRQUFRLFVBQVUsU0FBVSxLQUFLO0FBQzFDLFdBQU8sSUFBSSxPQUFPLENBQUMsRUFBRSxZQUFZLElBQUksSUFBSSxVQUFVLENBQUMsRUFBRSxZQUFZO0FBQUEsRUFDcEUsQ0FBQztBQUNIOzs7QUR2Tk8sY0FBTztBQUNQLGNBQU8sRUFBRSxNQUFNLGNBQWMsVUFBVSxLQUFLLENBQUM7QUFLcEQsSUFBSTtBQUNKLElBQUk7QUFFRyxTQUFTLG9CQUFvQjtBQUNsQyxRQUFNLE1BQU0sUUFBUSxJQUFJO0FBQ3hCLFFBQU0sTUFBTSxRQUFRLElBQUksNkJBQTZCLFFBQVEsSUFBSTtBQUNqRSxNQUFJLENBQUMsT0FBTyxDQUFDLEtBQUs7QUFDaEIsVUFBTSxNQUFNLGtEQUFrRDtBQUFBLEVBQ2hFO0FBQ0EsYUFBVyxhQUFhLEtBQUssS0FBSyxFQUFFLE1BQU0sRUFBRSxnQkFBZ0IsTUFBTSxFQUFFLENBQUM7QUFDdkU7QUFFQSxlQUFlLGlCQUFrQztBQUMvQyxNQUFJLFlBQVksT0FBVyxRQUFPO0FBQ2xDLFFBQU0sRUFBRSxNQUFNLE1BQU0sSUFBSSxNQUFNLFNBQzNCLEtBQUssUUFBUSxFQUNiLE9BQU8sSUFBSSxFQUNYLEdBQUcsUUFBUSxZQUFZLEVBQ3ZCLE9BQU87QUFFVixNQUFJLFNBQVMsTUFBTSxTQUFTLFlBQVk7QUFDdEMsVUFBTTtBQUFBLEVBQ1I7QUFFQSxNQUFJLE1BQU0sSUFBSTtBQUNaLGNBQVUsS0FBSztBQUNmLFdBQU87QUFBQSxFQUNUO0FBR0EsUUFBTSxFQUFFLE1BQU0sWUFBWSxPQUFPLFVBQVUsSUFBSSxNQUFNLFNBQ2xELEtBQUssUUFBUSxFQUNiLE9BQU8sRUFBRSxNQUFNLGFBQWEsQ0FBQyxFQUM3QixPQUFPLElBQUksRUFDWCxPQUFPO0FBQ1YsTUFBSSxVQUFXLE9BQU07QUFDckIsWUFBVSxXQUFZO0FBQ3RCLFNBQU87QUFDVDtBQUVBLGVBQXNCLHdCQUF3QixTQUEyQztBQUN2RixNQUFJLENBQUMsU0FBVSxPQUFNLE1BQU0saUNBQWlDO0FBQzVELFFBQU0sTUFBTSxNQUFNLGVBQWU7QUFHakMsUUFBTSxpQkFBc0I7QUFBQSxJQUMxQixJQUFJLFFBQVE7QUFBQSxJQUNaLE1BQU0sUUFBUTtBQUFBLElBQ2QsTUFBTSxRQUFRO0FBQUEsSUFDZCxZQUFZLFFBQVE7QUFBQSxJQUNwQixXQUFXLFFBQVE7QUFBQSxJQUNuQixtQkFBbUIsUUFBUTtBQUFBLElBQzNCLGFBQWEsUUFBUTtBQUFBLElBQ3JCLGNBQWMsUUFBUSxZQUFZLFlBQVk7QUFBQSxJQUM5QyxjQUFjLFFBQVEsWUFBWSxZQUFZO0FBQUEsRUFDaEQ7QUFFQSxRQUFNLEVBQUUsT0FBTyxRQUFRLElBQUksTUFBTSxTQUFTLEtBQUssVUFBVSxFQUFFLE9BQU8sZ0JBQWdCLEVBQUUsWUFBWSxLQUFLLENBQUM7QUFDdEcsTUFBSSxTQUFTO0FBQ1gsYUFBUyxxQ0FBcUMsUUFBUSxPQUFPO0FBQzdEO0FBQUEsRUFDRjtBQUdBLFFBQU0sZUFBZTtBQUFBLElBQ25CLFlBQVksUUFBUTtBQUFBLElBQ3BCLFVBQVU7QUFBQSxJQUNWLE9BQU8sUUFBUTtBQUFBLElBQ2YsYUFBYSxRQUFRLFlBQVksWUFBWTtBQUFBLEVBQy9DO0FBQ0EsUUFBTSxFQUFFLE9BQU8sU0FBUyxJQUFJLE1BQU0sU0FBUyxLQUFLLFFBQVEsRUFBRSxPQUFPLFlBQVk7QUFDN0UsTUFBSSxVQUFVO0FBQ1osYUFBUyxtQ0FBbUMsU0FBUyxPQUFPO0FBQzVEO0FBQUEsRUFDRjtBQUVBLE1BQUksT0FBTyxPQUFPLGVBQWUsUUFBUSxLQUFLLE1BQU0sR0FBRyxFQUFFLEVBQUUsT0FBTyxFQUFFLENBQUMsT0FBTyxRQUFRLFlBQVksRUFBRTtBQUNsRztBQUNGO0FBR0EsZUFBc0Isc0JBQXNCLFVBQWtCLFNBQW9DO0FBQ2hHLE1BQUksQ0FBQyxTQUFVLE9BQU0sTUFBTSxpQ0FBaUM7QUFFNUQsTUFBSTtBQUVGLFVBQU0sZ0JBQWdCLE1BQU0sTUFBTSxRQUFRO0FBQzFDLFFBQUksQ0FBQyxjQUFjLElBQUk7QUFDckIsZUFBUyxpQ0FBaUMsUUFBUSxLQUFLLGNBQWMsVUFBVSxFQUFFO0FBQ2pGLGFBQU87QUFBQSxJQUNUO0FBRUEsVUFBTSxjQUFjLE1BQU0sY0FBYyxZQUFZO0FBQ3BELFVBQU0sWUFBWSxJQUFJLEtBQUssQ0FBQyxXQUFXLEdBQUcsRUFBRSxNQUFNLGFBQWEsQ0FBQztBQUdoRSxVQUFNLFdBQVcsWUFBWSxRQUFRLEVBQUU7QUFHdkMsVUFBTSxFQUFFLE1BQU0sTUFBTSxJQUFJLE1BQU0sU0FBUyxRQUNwQyxLQUFLLGdCQUFnQixFQUNyQixPQUFPLFVBQVUsV0FBVztBQUFBLE1BQzNCLGFBQWE7QUFBQSxNQUNiLFFBQVE7QUFBQTtBQUFBLElBQ1YsQ0FBQztBQUVILFFBQUksT0FBTztBQUNULGVBQVMsbUNBQW1DLE1BQU0sT0FBTyxFQUFFO0FBQzNELGFBQU87QUFBQSxJQUNUO0FBR0EsVUFBTSxFQUFFLE1BQU0sY0FBYyxJQUFJLFNBQVMsUUFDdEMsS0FBSyxnQkFBZ0IsRUFDckIsYUFBYSxRQUFRO0FBRXhCLFFBQUksT0FBTyxNQUFNLHFCQUFxQixRQUFRLEVBQUUsT0FBTyxjQUFjLFNBQVMsRUFBRTtBQUNoRixXQUFPO0FBQUEsRUFFVCxTQUFTLEtBQVU7QUFDakIsYUFBUyx1QkFBdUIsSUFBSSxPQUFPLEVBQUU7QUFDN0MsV0FBTztBQUFBLEVBQ1Q7QUFDRjs7O0FFOUhBLFNBQVMsZ0JBQUFDLHFCQUFvQzs7O0FDRTdDLFNBQVMsZ0JBQUFDLHFCQUFvQjtBQUc3QixJQUFJQyxZQUFXO0FBRVIsU0FBUyxtQkFBbUIsS0FBSyxLQUFLO0FBQ3pDLEVBQUFBLFlBQVdELGNBQWEsS0FBSyxHQUFHO0FBQ3BDO0FBTU8sU0FBUyxxQkFBcUIsTUFBTSxZQUFZLElBQUk7QUFDdkQsTUFBSSxhQUFhLEtBQUssWUFBWSxFQUFFLEtBQUs7QUFHekMsTUFBSSxXQUFXO0FBQ1gsVUFBTSxhQUFhLElBQUksT0FBTyxVQUFVLFlBQVksR0FBRyxJQUFJO0FBQzNELGlCQUFhLFdBQVcsUUFBUSxZQUFZLEVBQUUsRUFBRSxLQUFLO0FBQUEsRUFDekQ7QUFHQSxRQUFNLHNCQUFzQjtBQUFBLElBQ3hCO0FBQUEsSUFBYTtBQUFBLElBQWM7QUFBQSxJQUFhO0FBQUEsSUFBWTtBQUFBLElBQ3BEO0FBQUEsSUFBVTtBQUFBLElBQVc7QUFBQSxJQUFTO0FBQUEsSUFBVTtBQUFBLElBQWE7QUFBQSxJQUNyRDtBQUFBLElBQVc7QUFBQSxJQUFjO0FBQUEsSUFBYTtBQUFBLElBQVc7QUFBQSxFQUNyRDtBQUVBLGFBQVcsY0FBYyxxQkFBcUI7QUFDMUMsVUFBTSxRQUFRLElBQUksT0FBTyxNQUFNLFVBQVUsT0FBTyxJQUFJO0FBQ3BELGlCQUFhLFdBQVcsUUFBUSxPQUFPLEVBQUUsRUFBRSxLQUFLO0FBQUEsRUFDcEQ7QUFHQSxlQUFhLFdBQ1IsUUFBUSxZQUFZLEdBQUcsRUFDdkIsUUFBUSxRQUFRLEdBQUcsRUFDbkIsUUFBUSxvQ0FBb0MsRUFBRSxFQUM5QyxLQUFLO0FBRVYsU0FBTztBQUNYO0FBS08sU0FBUyxjQUFjLE1BQU07QUFDaEMsTUFBSSxDQUFDLEtBQU0sUUFBTztBQUVsQixNQUFJLGFBQWEsS0FBSyxZQUFZLEVBQUUsS0FBSztBQUd6QyxlQUFhLFdBQ1IsUUFBUSxhQUFhLElBQUksRUFDekIsUUFBUSxnQkFBZ0IsR0FBRyxFQUMzQixRQUFRLGdCQUFnQixHQUFHLEVBQzNCLFFBQVEsZUFBZSxHQUFHLEVBQzFCLFFBQVEsZUFBZSxJQUFJLEVBQzNCLFFBQVEsbUJBQW1CLElBQUksRUFDL0IsUUFBUSxlQUFlLElBQUksRUFDM0IsUUFBUSxhQUFhLElBQUksRUFDekIsUUFBUSxpQkFBaUIsUUFBUSxFQUNqQyxRQUFRLFFBQVEsRUFBRTtBQUV2QixTQUFPO0FBQ1g7QUFLTyxTQUFTLHNCQUFzQixNQUFNO0FBQ3hDLE1BQUksQ0FBQyxLQUFNLFFBQU87QUFFbEIsUUFBTSxhQUFhLGNBQWMsSUFBSTtBQUNyQyxRQUFNLFFBQVEsV0FBVyxNQUFNLG9DQUFvQztBQUVuRSxNQUFJLENBQUMsTUFBTyxRQUFPO0FBRW5CLFFBQU0sUUFBUSxXQUFXLE1BQU0sQ0FBQyxDQUFDO0FBQ2pDLFFBQU0sT0FBTyxNQUFNLENBQUMsS0FBSztBQUd6QixVQUFRLE1BQU07QUFBQSxJQUNWLEtBQUs7QUFBSyxhQUFPLFFBQVE7QUFBQTtBQUFBLElBQ3pCLEtBQUs7QUFBTSxhQUFPLFFBQVE7QUFBQTtBQUFBLElBQzFCLEtBQUs7QUFBQSxJQUNMLEtBQUs7QUFBTSxhQUFPO0FBQUEsSUFDbEIsS0FBSztBQUFBLElBQ0wsS0FBSztBQUFBLElBQ0w7QUFBUyxhQUFPO0FBQUEsRUFDcEI7QUFDSjtBQUtPLFNBQVMsd0JBQXdCLE9BQU8sT0FBTztBQUNsRCxRQUFNLGNBQWMscUJBQXFCLEtBQUs7QUFDOUMsUUFBTSxjQUFjLHFCQUFxQixLQUFLO0FBRTlDLE1BQUksZ0JBQWdCLFlBQWEsUUFBTztBQUd4QyxRQUFNLFdBQVcsb0JBQW9CLGFBQWEsV0FBVztBQUM3RCxRQUFNLFlBQVksS0FBSyxJQUFJLFlBQVksUUFBUSxZQUFZLE1BQU07QUFFakUsTUFBSSxjQUFjLEVBQUcsUUFBTztBQUU1QixRQUFNLGFBQWEsS0FBSyxJQUFJLElBQUksSUFBSSxXQUFXLGFBQWEsR0FBRztBQUcvRCxRQUFNLFNBQVMsWUFBWSxNQUFNLEdBQUcsRUFBRSxPQUFPLE9BQUssRUFBRSxTQUFTLENBQUM7QUFDOUQsUUFBTSxTQUFTLFlBQVksTUFBTSxHQUFHLEVBQUUsT0FBTyxPQUFLLEVBQUUsU0FBUyxDQUFDO0FBQzlELFFBQU0sY0FBYyxPQUFPLE9BQU8sT0FBSyxPQUFPLFNBQVMsQ0FBQyxDQUFDO0FBRXpELE1BQUksWUFBWSxTQUFTLEdBQUc7QUFDeEIsVUFBTSxZQUFhLFlBQVksU0FBUyxLQUFLLElBQUksT0FBTyxRQUFRLE9BQU8sTUFBTSxJQUFLO0FBQ2xGLFdBQU8sS0FBSyxJQUFJLEtBQUssYUFBYSxTQUFTO0FBQUEsRUFDL0M7QUFFQSxTQUFPO0FBQ1g7QUFLQSxTQUFTLG9CQUFvQixNQUFNLE1BQU07QUFDckMsUUFBTSxTQUFTLENBQUM7QUFFaEIsV0FBUyxJQUFJLEdBQUcsS0FBSyxLQUFLLFFBQVEsS0FBSztBQUNuQyxXQUFPLENBQUMsSUFBSSxDQUFDLENBQUM7QUFBQSxFQUNsQjtBQUVBLFdBQVMsSUFBSSxHQUFHLEtBQUssS0FBSyxRQUFRLEtBQUs7QUFDbkMsV0FBTyxDQUFDLEVBQUUsQ0FBQyxJQUFJO0FBQUEsRUFDbkI7QUFFQSxXQUFTLElBQUksR0FBRyxLQUFLLEtBQUssUUFBUSxLQUFLO0FBQ25DLGFBQVMsSUFBSSxHQUFHLEtBQUssS0FBSyxRQUFRLEtBQUs7QUFDbkMsVUFBSSxLQUFLLE9BQU8sSUFBSSxDQUFDLE1BQU0sS0FBSyxPQUFPLElBQUksQ0FBQyxHQUFHO0FBQzNDLGVBQU8sQ0FBQyxFQUFFLENBQUMsSUFBSSxPQUFPLElBQUksQ0FBQyxFQUFFLElBQUksQ0FBQztBQUFBLE1BQ3RDLE9BQU87QUFDSCxlQUFPLENBQUMsRUFBRSxDQUFDLElBQUksS0FBSztBQUFBLFVBQ2hCLE9BQU8sSUFBSSxDQUFDLEVBQUUsSUFBSSxDQUFDLElBQUk7QUFBQSxVQUN2QixPQUFPLENBQUMsRUFBRSxJQUFJLENBQUMsSUFBSTtBQUFBLFVBQ25CLE9BQU8sSUFBSSxDQUFDLEVBQUUsQ0FBQyxJQUFJO0FBQUEsUUFDdkI7QUFBQSxNQUNKO0FBQUEsSUFDSjtBQUFBLEVBQ0o7QUFFQSxTQUFPLE9BQU8sS0FBSyxNQUFNLEVBQUUsS0FBSyxNQUFNO0FBQzFDO0FBS08sU0FBUywyQkFBMkIsVUFBVSxVQUFVO0FBQzNELFFBQU0sWUFBWSx3QkFBd0IsU0FBUyxNQUFNLFNBQVMsSUFBSTtBQUd0RSxNQUFJLFlBQVk7QUFDaEIsTUFBSSxTQUFTLFFBQVEsU0FBUyxNQUFNO0FBQ2hDLFVBQU0sa0JBQWtCLGNBQWMsU0FBUyxJQUFJO0FBQ25ELFVBQU0sa0JBQWtCLGNBQWMsU0FBUyxJQUFJO0FBRW5ELFFBQUksb0JBQW9CLGlCQUFpQjtBQUNyQyxrQkFBWTtBQUFBLElBQ2hCLE9BQU87QUFDSCxZQUFNLFNBQVMsc0JBQXNCLFNBQVMsSUFBSTtBQUNsRCxZQUFNLFNBQVMsc0JBQXNCLFNBQVMsSUFBSTtBQUNsRCxZQUFNLFdBQVcsS0FBSyxJQUFJLFNBQVMsTUFBTSxJQUFJLEtBQUssSUFBSSxRQUFRLE1BQU07QUFDcEUsVUFBSSxXQUFXLElBQUssYUFBWTtBQUFBLGVBQ3ZCLFdBQVcsSUFBSyxhQUFZO0FBQUEsSUFDekM7QUFBQSxFQUNKO0FBR0EsTUFBSSxhQUFhO0FBQ2pCLE1BQUksU0FBUyxTQUFTLFNBQVMsT0FBTztBQUNsQyxRQUFJLFNBQVMsTUFBTSxZQUFZLE1BQU0sU0FBUyxNQUFNLFlBQVksR0FBRztBQUMvRCxtQkFBYTtBQUFBLElBQ2pCO0FBQUEsRUFDSjtBQUdBLFFBQU0sYUFBYyxZQUFZLE1BQVEsWUFBWSxNQUFRLGFBQWE7QUFFekUsU0FBTyxLQUFLLE1BQU0sVUFBVTtBQUNoQztBQUtBLGVBQXNCLHFCQUFxQixTQUFTLFdBQVcsSUFBSTtBQUMvRCxNQUFJLENBQUNDLFdBQVU7QUFDWCxVQUFNLElBQUksTUFBTSxpQ0FBaUM7QUFBQSxFQUNyRDtBQUdBLFFBQU0saUJBQWlCLHFCQUFxQixRQUFRLElBQUk7QUFDeEQsUUFBTSxZQUFZLGVBQWUsTUFBTSxHQUFHLEVBQUUsT0FBTyxPQUFLLEVBQUUsU0FBUyxDQUFDO0FBR3BFLFFBQU0sRUFBRSxNQUFNLFlBQVksTUFBTSxJQUFJLE1BQU1BLFVBQ3JDLEtBQUssdUJBQXVCLEVBQzVCLE9BQU87QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQSxTQU9QLEVBQ0EsV0FBVyxtQkFBbUIsVUFBVSxLQUFLLEtBQUssQ0FBQztBQUV4RCxNQUFJLE9BQU87QUFDUCxZQUFRLE1BQU0sd0NBQXdDLEtBQUs7QUFDM0QsV0FBTyxDQUFDO0FBQUEsRUFDWjtBQUdBLFFBQU0sVUFBVSxDQUFDO0FBQ2pCLGFBQVcsYUFBYSxjQUFjLENBQUMsR0FBRztBQUN0QyxVQUFNLG1CQUFtQjtBQUFBLE1BQ3JCLE1BQU0sVUFBVTtBQUFBLE1BQ2hCLE1BQU0sVUFBVTtBQUFBLE1BQ2hCLE9BQU8sVUFBVSxRQUFRO0FBQUEsSUFDN0I7QUFFQSxVQUFNLFFBQVEsMkJBQTJCLFNBQVMsZ0JBQWdCO0FBRWxFLFFBQUksU0FBUyxVQUFVO0FBQ25CLGNBQVEsS0FBSztBQUFBLFFBQ1QsdUJBQXVCLFVBQVU7QUFBQSxRQUNqQyxhQUFhLFVBQVU7QUFBQSxRQUN2QjtBQUFBLFFBQ0EsWUFBWSxTQUFTLEtBQUssU0FBUyxTQUFTLEtBQUssV0FBVztBQUFBLE1BQ2hFLENBQUM7QUFBQSxJQUNMO0FBQUEsRUFDSjtBQUdBLFNBQU8sUUFBUSxLQUFLLENBQUMsR0FBRyxNQUFNLEVBQUUsUUFBUSxFQUFFLEtBQUs7QUFDbkQ7QUFLQSxlQUFzQixtQkFBbUIsY0FBY0MsVUFBUztBQUM1RCxNQUFJLENBQUNELFdBQVU7QUFDWCxVQUFNLElBQUksTUFBTSxpQ0FBaUM7QUFBQSxFQUNyRDtBQUdBLFFBQU0sVUFBVSxNQUFNLHFCQUFxQixjQUFjLEVBQUU7QUFFM0QsTUFBSTtBQUVKLE1BQUksUUFBUSxTQUFTLEtBQUssUUFBUSxDQUFDLEVBQUUsU0FBUyxJQUFJO0FBRTlDLDRCQUF3QixRQUFRLENBQUMsRUFBRTtBQUNuQyxZQUFRLElBQUksVUFBVSxhQUFhLElBQUksNkNBQTZDLFFBQVEsQ0FBQyxFQUFFLEtBQUssR0FBRztBQUFBLEVBQzNHLE9BQU87QUFFSCxVQUFNLGlCQUFpQixxQkFBcUIsYUFBYSxJQUFJO0FBRzdELFVBQU0sYUFBYSxNQUFNLGlCQUFpQixhQUFhLFFBQVE7QUFFL0QsVUFBTSxFQUFFLE1BQU0sWUFBWSxNQUFNLElBQUksTUFBTUEsVUFDckMsS0FBSyx1QkFBdUIsRUFDNUIsT0FBTztBQUFBLE1BQ0osaUJBQWlCO0FBQUEsTUFDakIsY0FBYyxhQUFhO0FBQUEsTUFDM0IsY0FBYyxhQUFhO0FBQUEsTUFDM0IsYUFBYTtBQUFBLE1BQ2Isa0JBQWtCLFFBQVEsU0FBUyxJQUFJLFFBQVEsQ0FBQyxFQUFFLFFBQVE7QUFBQSxJQUM5RCxDQUFDLEVBQ0EsT0FBTyxFQUNQLE9BQU87QUFFWixRQUFJLE9BQU87QUFDUCxjQUFRLE1BQU0sd0NBQXdDLEtBQUs7QUFDM0QsYUFBTztBQUFBLElBQ1g7QUFFQSw0QkFBd0IsV0FBVztBQUNuQyxZQUFRLElBQUksd0NBQXdDLGFBQWEsSUFBSSxFQUFFO0FBQUEsRUFDM0U7QUFHQSxRQUFNLGlCQUFpQixjQUFjQyxVQUFTLHFCQUFxQjtBQUVuRSxTQUFPO0FBQ1g7QUFLQSxlQUFlLGlCQUFpQixjQUFjQSxVQUFTLHVCQUF1QjtBQUMxRSxRQUFNLEVBQUUsTUFBTSxJQUFJLE1BQU1ELFVBQ25CLEtBQUssa0JBQWtCLEVBQ3ZCLE9BQU87QUFBQSxJQUNKLHlCQUF5QjtBQUFBLElBQ3pCLGtCQUFrQixhQUFhO0FBQUEsSUFDL0IsVUFBVUM7QUFBQSxJQUNWLFlBQVksYUFBYTtBQUFBLElBQ3pCLFlBQVksYUFBYTtBQUFBLElBQ3pCLGtCQUFrQixhQUFhO0FBQUEsSUFDL0IsaUJBQWlCLGFBQWE7QUFBQSxJQUM5QixZQUFXLG9CQUFJLEtBQUssR0FBRSxZQUFZO0FBQUEsSUFDbEMsV0FBVztBQUFBLEVBQ2YsQ0FBQztBQUVMLE1BQUksT0FBTztBQUNQLFlBQVEsTUFBTSxnQ0FBZ0MsS0FBSztBQUFBLEVBQ3ZEO0FBQ0o7QUFLQSxlQUFlLGlCQUFpQixpQkFBaUI7QUFDN0MsTUFBSSxDQUFDLG1CQUFtQixnQkFBZ0IsV0FBVyxFQUFHLFFBQU87QUFFN0QsUUFBTSxtQkFBbUI7QUFBQSxJQUNyQixhQUFhO0FBQUEsSUFDYiwwQkFBMEI7QUFBQSxJQUMxQixnQkFBZ0I7QUFBQSxJQUNoQixnQkFBZ0I7QUFBQSxJQUNoQixVQUFVO0FBQUEsSUFDVixlQUFlO0FBQUEsSUFDZiwrQkFBK0I7QUFBQSxJQUMvQixVQUFVO0FBQUEsSUFDVixVQUFVO0FBQUEsSUFDVixVQUFVO0FBQUEsSUFDVix1QkFBdUI7QUFBQSxJQUN2QixhQUFhO0FBQUEsSUFDYix1QkFBdUI7QUFBQSxJQUN2QixlQUFlO0FBQUEsSUFDZixtQkFBbUI7QUFBQSxJQUNuQixhQUFhO0FBQUEsSUFDYiwwQkFBMEI7QUFBQSxJQUMxQixjQUFjO0FBQUEsSUFDZCxvQkFBb0I7QUFBQSxJQUNwQixPQUFPO0FBQUEsSUFDUCxRQUFRO0FBQUEsRUFDWjtBQUdBLFFBQU0sZ0JBQWdCLGdCQUFnQixDQUFDLEVBQUUsWUFBWSxFQUFFLFFBQVEsUUFBUSxHQUFHO0FBQzFFLFFBQU0saUJBQWlCLGlCQUFpQixhQUFhO0FBRXJELE1BQUksZ0JBQWdCO0FBQ2hCLFVBQU0sRUFBRSxLQUFLLElBQUksTUFBTUQsVUFDbEIsS0FBSyxvQkFBb0IsRUFDekIsT0FBTyxJQUFJLEVBQ1gsR0FBRyxRQUFRLGNBQWMsRUFDekIsT0FBTztBQUVaLFdBQU8sTUFBTTtBQUFBLEVBQ2pCO0FBRUEsU0FBTztBQUNYO0FBS0EsZUFBc0IsdUJBQXVCLHVCQUF1QkMsVUFBUyxPQUFPLFlBQVksT0FBTztBQUNuRyxNQUFJLENBQUNELFdBQVU7QUFDWCxVQUFNLElBQUksTUFBTSxpQ0FBaUM7QUFBQSxFQUNyRDtBQUVBLFFBQU0sRUFBRSxNQUFNLElBQUksTUFBTUEsVUFDbkIsS0FBSyxxQkFBcUIsRUFDMUIsT0FBTztBQUFBLElBQ0oseUJBQXlCO0FBQUEsSUFDekIsVUFBVUM7QUFBQSxJQUNWO0FBQUEsSUFDQSxZQUFZO0FBQUEsSUFDWixlQUFlO0FBQUEsSUFDZixjQUFhLG9CQUFJLEtBQUssR0FBRSxZQUFZO0FBQUEsRUFDeEMsQ0FBQztBQUVMLE1BQUksT0FBTztBQUNQLFlBQVEsTUFBTSxxQ0FBcUMsS0FBSztBQUFBLEVBQzVEO0FBQ0o7OztBRHBZQSxJQUFJQztBQUNKLElBQUk7QUFFRyxTQUFTLCtCQUErQixhQUFxQixhQUFxQjtBQUNyRixFQUFBQSxZQUFXQyxjQUFhLGFBQWEsYUFBYSxFQUFFLE1BQU0sRUFBRSxnQkFBZ0IsTUFBTSxFQUFFLENBQUM7QUFDckYsRUFBZSxtQkFBbUIsYUFBYSxXQUFXO0FBQzlEO0FBRUEsZUFBc0IsMEJBQTJDO0FBQzdELE1BQUksa0JBQW1CLFFBQU87QUFFOUIsUUFBTSxFQUFFLE1BQU0sTUFBTSxJQUFJLE1BQU1ELFVBQ3pCLEtBQUssUUFBUSxFQUNiLE9BQU8sSUFBSSxFQUNYLEdBQUcsUUFBUSxZQUFZLEVBQ3ZCLE9BQU87QUFFWixNQUFJLFNBQVMsTUFBTSxTQUFTLFlBQVk7QUFDcEMsVUFBTTtBQUFBLEVBQ1Y7QUFFQSxNQUFJLE1BQU0sSUFBSTtBQUNWLHdCQUFvQixLQUFLO0FBQ3pCLFdBQU87QUFBQSxFQUNYO0FBR0EsUUFBTSxFQUFFLE1BQU0sWUFBWSxPQUFPLFVBQVUsSUFBSSxNQUFNQSxVQUNoRCxLQUFLLFFBQVEsRUFDYixPQUFPLEVBQUUsTUFBTSxhQUFhLENBQUMsRUFDN0IsT0FBTyxJQUFJLEVBQ1gsT0FBTztBQUNaLE1BQUksVUFBVyxPQUFNO0FBRXJCLHNCQUFvQixXQUFZO0FBQ2hDLFNBQU87QUFDWDtBQUtBLGVBQXNCLDJCQUEyQixnQkFBaUQ7QUFDOUYsTUFBSTtBQUVBLFVBQU1FLFdBQVUsTUFBTSx3QkFBd0I7QUFHOUMsVUFBTSxxQkFBcUI7QUFBQSxNQUN2QixJQUFJLGVBQWU7QUFBQSxNQUNuQixNQUFNLGVBQWU7QUFBQSxNQUNyQixNQUFNLGVBQWU7QUFBQSxNQUNyQixVQUFVLGVBQWU7QUFBQSxNQUN6QixPQUFPLHFCQUFxQixlQUFlLElBQUk7QUFBQTtBQUFBLE1BQy9DLFdBQVcsZUFBZTtBQUFBLE1BQzFCLFVBQVUsZUFBZTtBQUFBLElBQzdCO0FBR0EsVUFBTSx3QkFBd0IsTUFBcUIsbUJBQW1CLG9CQUFvQkEsUUFBTztBQUVqRyxRQUFJLHVCQUF1QjtBQUV2QixZQUFxQjtBQUFBLFFBQ2pCO0FBQUEsUUFDQUE7QUFBQSxRQUNBLGVBQWU7QUFBQSxRQUNmO0FBQUE7QUFBQSxNQUNKO0FBR0EsWUFBTSwwQkFBMEIsdUJBQXVCLGNBQWM7QUFBQSxJQUN6RTtBQUVBLFdBQU87QUFBQSxFQUNYLFNBQVMsT0FBTztBQUNaLFlBQVEsTUFBTSw2Q0FBNkMsZUFBZSxJQUFJLEtBQUssS0FBSztBQUN4RixXQUFPO0FBQUEsRUFDWDtBQUNKO0FBS0EsU0FBUyxxQkFBcUIsYUFBeUM7QUFFbkUsUUFBTSxnQkFBZ0I7QUFBQSxJQUNsQjtBQUFBO0FBQUEsSUFDQTtBQUFBO0FBQUEsRUFDSjtBQUVBLGFBQVcsV0FBVyxlQUFlO0FBQ2pDLFVBQU0sUUFBUSxZQUFZLE1BQU0sT0FBTztBQUN2QyxRQUFJLE9BQU87QUFDUCxhQUFPLE1BQU0sQ0FBQyxFQUFFLEtBQUs7QUFBQSxJQUN6QjtBQUFBLEVBQ0o7QUFFQSxTQUFPO0FBQ1g7QUFLQSxlQUFlLDBCQUEwQix1QkFBK0IsU0FBa0I7QUFDdEYsTUFBSSxDQUFDLFFBQVEsS0FBTTtBQUVuQixNQUFJO0FBRUEsVUFBTSxFQUFFLE1BQU0sZ0JBQWdCLElBQUksTUFBTUYsVUFDbkMsS0FBSyx1QkFBdUIsRUFDNUIsT0FBTyxJQUFJLEVBQ1gsR0FBRywyQkFBMkIscUJBQXFCLEVBQ25ELEdBQUcsYUFBYSxRQUFRLElBQUksRUFDNUIsT0FBTztBQUVaLFFBQUksZ0JBQWlCO0FBR3JCLFVBQU0sY0FBYyx1QkFBdUIsUUFBUSxJQUFJO0FBQ3ZELFVBQU0sV0FBVyxvQkFBb0IsUUFBUSxJQUFJO0FBR2pELFVBQU1BLFVBQ0QsS0FBSyx1QkFBdUIsRUFDNUIsT0FBTztBQUFBLE1BQ0oseUJBQXlCO0FBQUEsTUFDekIsV0FBVyxRQUFRO0FBQUEsTUFDbkIsbUJBQW1CO0FBQUEsTUFDbkIsZ0JBQWdCO0FBQUEsTUFDaEIsaUJBQWlCO0FBQUE7QUFBQSxJQUNyQixDQUFDO0FBQUEsRUFFVCxTQUFTLE9BQU87QUFDWixZQUFRLE1BQU0sb0NBQW9DLFFBQVEsSUFBSSxLQUFLLEtBQUs7QUFBQSxFQUM1RTtBQUNKO0FBS0EsU0FBUyx1QkFBdUIsTUFBNkI7QUFDekQsTUFBSSxDQUFDLEtBQU0sUUFBTztBQUVsQixRQUFNLGlCQUFpQixLQUFLLFlBQVk7QUFHeEMsUUFBTSxZQUFZLGVBQWUsTUFBTSx1QkFBdUI7QUFDOUQsTUFBSSxXQUFXO0FBQ1gsV0FBTyxTQUFTLFVBQVUsQ0FBQyxDQUFDO0FBQUEsRUFDaEM7QUFFQSxRQUFNLFVBQVUsZUFBZSxNQUFNLHdCQUF3QjtBQUM3RCxNQUFJLFNBQVM7QUFDVCxXQUFPLEtBQUssTUFBTSxXQUFXLFFBQVEsQ0FBQyxDQUFDLElBQUksR0FBSTtBQUFBLEVBQ25EO0FBRUEsU0FBTztBQUNYO0FBS0EsU0FBUyxvQkFBb0IsTUFBNkI7QUFDdEQsTUFBSSxDQUFDLEtBQU0sUUFBTztBQUVsQixRQUFNLGlCQUFpQixLQUFLLFlBQVk7QUFHeEMsUUFBTSxVQUFVLGVBQWUsTUFBTSx3QkFBd0I7QUFDN0QsTUFBSSxTQUFTO0FBQ1QsV0FBTyxTQUFTLFFBQVEsQ0FBQyxDQUFDO0FBQUEsRUFDOUI7QUFFQSxRQUFNLFNBQVMsZUFBZSxNQUFNLHVCQUF1QjtBQUMzRCxNQUFJLFFBQVE7QUFDUixXQUFPLEtBQUssTUFBTSxXQUFXLE9BQU8sQ0FBQyxDQUFDLElBQUksR0FBSTtBQUFBLEVBQ2xEO0FBRUEsU0FBTztBQUNYOzs7QUV6TE8sSUFBTSxtQkFBbUI7QUFBQSxFQUM5QixFQUFFLElBQUksVUFBVSxNQUFNLE9BQU87QUFBQSxFQUM3QixFQUFFLElBQUksVUFBVSxNQUFNLE9BQU87QUFBQSxFQUM3QixFQUFFLElBQUksVUFBVSxNQUFNLE9BQU87QUFBQSxFQUM3QixFQUFFLElBQUksVUFBVSxNQUFNLE9BQU87QUFBQSxFQUM3QixFQUFFLElBQUksVUFBVSxNQUFNLFNBQVM7QUFBQSxFQUMvQixFQUFFLElBQUksVUFBVSxNQUFNLFFBQVE7QUFBQSxFQUM5QixFQUFFLElBQUksVUFBVSxNQUFNLFFBQVE7QUFBQSxFQUM5QixFQUFFLElBQUksVUFBVSxNQUFNLFFBQVE7QUFBQSxFQUM5QixFQUFFLElBQUksVUFBVSxNQUFNLFFBQVE7QUFBQSxFQUM5QixFQUFFLElBQUksVUFBVSxNQUFNLE9BQU87QUFBQSxFQUM3QixFQUFFLElBQUksVUFBVSxNQUFNLFFBQVE7QUFBQSxFQUM5QixFQUFFLElBQUksVUFBVSxNQUFNLFFBQVE7QUFBQSxFQUM5QixFQUFFLElBQUksVUFBVSxNQUFNLFFBQVE7QUFBQSxFQUM5QixFQUFFLElBQUksVUFBVSxNQUFNLFFBQVE7QUFBQSxFQUM5QixFQUFFLElBQUksVUFBVSxNQUFNLFFBQVE7QUFBQSxFQUM5QixFQUFFLElBQUksVUFBVSxNQUFNLFFBQVE7QUFBQSxFQUM5QixFQUFFLElBQUksVUFBVSxNQUFNLFFBQVE7QUFBQSxFQUM5QixFQUFFLElBQUksVUFBVSxNQUFNLFFBQVE7QUFBQSxFQUM5QixFQUFFLElBQUksVUFBVSxNQUFNLFFBQVE7QUFBQSxFQUM5QixFQUFFLElBQUksVUFBVSxNQUFNLFNBQVM7QUFBQSxFQUMvQixFQUFFLElBQUksVUFBVSxNQUFNLE9BQU87QUFBQSxFQUM3QixFQUFFLElBQUksVUFBVSxNQUFNLE9BQU87QUFBQSxFQUM3QixFQUFFLElBQUksVUFBVSxNQUFNLE9BQU87QUFBQSxFQUM3QixFQUFFLElBQUksVUFBVSxNQUFNLE9BQU87QUFBQSxFQUM3QixFQUFFLElBQUksVUFBVSxNQUFNLE9BQU87QUFBQSxFQUM3QixFQUFFLElBQUksVUFBVSxNQUFNLE9BQU87QUFBQSxFQUM3QixFQUFFLElBQUksUUFBUSxNQUFNLE9BQU87QUFBQSxFQUMzQixFQUFFLElBQUksVUFBVSxVQUFVLGlCQUFpQjtBQUFBLEVBQzNDLEVBQUUsSUFBSSxVQUFVLE1BQU0sU0FBUyxVQUFVLFNBQVM7QUFBQSxFQUNsRCxFQUFFLElBQUksVUFBVSxVQUFVLGFBQWE7QUFBQSxFQUN2QyxFQUFFLElBQUksU0FBUyxVQUFVLFdBQVc7QUFBQSxFQUNwQyxFQUFFLElBQUksU0FBUyxVQUFVLFdBQVc7QUFBQSxFQUNwQyxFQUFFLElBQUksU0FBUyxVQUFVLFdBQVc7QUFBQSxFQUNwQyxFQUFFLElBQUksU0FBUyxVQUFVLFdBQVc7QUFBQSxFQUNwQyxFQUFFLElBQUksVUFBVSxVQUFVLGtCQUFrQjtBQUFBLEVBQzVDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLEVBQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLEVBQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLEVBQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLEVBQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLEVBQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLEVBQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLEVBQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLEVBQ3ZDLEVBQUUsSUFBSSxTQUFTLFVBQVUsYUFBYTtBQUFBLEVBQ3RDLEVBQUUsSUFBSSxVQUFVLFVBQVUsWUFBWTtBQUFBLEVBQ3RDLEVBQUUsSUFBSSxVQUFVLFVBQVUsWUFBWTtBQUFBLEVBQ3RDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLEVBQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLEVBQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLEVBQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLEVBQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLEVBQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLEVBQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLEVBQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLEVBQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLEVBQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsYUFBYTtBQUFBLEVBQ3ZDLEVBQUUsSUFBSSxVQUFVLFVBQVUsWUFBWTtBQUFBLEVBQ3RDLEVBQUUsSUFBSSxVQUFVLFVBQVUsWUFBWTtBQUFBLEVBQ3RDLEVBQUUsSUFBSSxVQUFVLFVBQVUsWUFBWTtBQUFBLEVBQ3RDLEVBQUUsSUFBSSxVQUFVLFVBQVUsU0FBUztBQUFBLEVBQ25DLEVBQUUsSUFBSSxVQUFVLFVBQVUsU0FBUztBQUFBLEVBQ25DLEVBQUUsSUFBSSxTQUFTLFVBQVUsWUFBWTtBQUFBLEVBQ3JDLEVBQUUsSUFBSSxRQUFRLFVBQVUsWUFBWTtBQUFBLEVBQ3BDLEVBQUUsSUFBSSxTQUFTLFVBQVUsWUFBWTtBQUFBLEVBQ3JDLEVBQUUsSUFBSSxVQUFVLFVBQVUsWUFBWTtBQUFBLEVBQ3RDLEVBQUUsSUFBSSxRQUFRLFVBQVUsWUFBWTtBQUFBLEVBQ3BDLEVBQUUsSUFBSSxVQUFVLFVBQVUsWUFBWTtBQUN4Qzs7O0FMdEVPLGVBQU87QUFDUCxlQUFPLEVBQUUsTUFBTSxjQUFjLFVBQVUsS0FBSyxDQUFDO0FBcUJwRCxJQUFNLHVCQUF1QjtBQUc3QixJQUFNLDhCQUE4QjtBQUdwQyxJQUFNLFlBQVksS0FBSyxJQUFJO0FBRzNCLElBQUksa0JBQW9DLGFBQWE7QUFHOUMsSUFBSSxlQUFlO0FBQ25CLElBQUksbUJBQW1CO0FBQzlCLElBQUksZUFBZTtBQUNuQixrQkFBa0IsTUFBTSxnQkFBZ0IsZUFBZTtBQUd2RCxJQUFJLGNBQWM7QUFDaEIsb0JBQWtCO0FBR2xCLFFBQU0sY0FBYyxRQUFRLElBQUk7QUFDaEMsUUFBTSxjQUFjLFFBQVEsSUFBSSw2QkFBNkIsUUFBUSxJQUFJO0FBQ3pFLGlDQUErQixhQUFhLFdBQVc7QUFDekQ7QUFHQSxJQUFJO0FBQ0osSUFBSTtBQUNKLFVBQVUsTUFBTSx3QkFBd0IsWUFBWTtBQUdwRCxNQUFNLDBCQUEwQjtBQUdoQyxNQUFNLGtCQUFrQjtBQUd4QixRQUFRLE1BQU07QUFDZDtBQUFBLEVBQ0UsT0FBTztBQUFBLEVBQ1A7QUFBQSwyQ0FBOEMsb0JBQW9CLFNBQVMsQ0FBQztBQUFBO0FBQzlFO0FBUUEsU0FBUyxhQUFhLFdBQW1CLGdCQUFrQztBQUV6RSxRQUFNLG1CQUE2QixzQkFBc0IsUUFBUTtBQUdqRSxNQUFJRyxtQkFBb0MsQ0FBQztBQUN6QyxtQkFBaUIsSUFBSSxDQUFDLFNBQVM7QUFDN0IsUUFBSSxhQUFhLHNCQUFzQixJQUFJO0FBQzNDLFFBQUksZUFBZSxRQUFXO0FBQzVCLE1BQUFBLG1CQUFrQixDQUFDLEdBQUdBLGtCQUFpQixHQUFHLFVBQVU7QUFBQSxJQUN0RDtBQUFBLEVBQ0YsQ0FBQztBQUdELFNBQU9BO0FBQ1Q7QUFPQSxlQUFlLG9CQUFvQjtBQUdqQztBQUFBLElBQ0UsT0FBTztBQUFBLElBQ1AsR0FBRyxnQkFBZ0IsTUFBTSx1QkFBdUIsT0FBTyxFQUFFLElBQ3pELEdBQUcsb0JBQW9CLDBCQUEwQixPQUFPLEVBQUUsS0FDekQsZUFBZSxvQkFBb0I7QUFBQSxFQUN0QztBQUdBLFdBQVMsSUFBSSxHQUFHLElBQUksZ0JBQWdCLFFBQVEsS0FBSztBQUMvQyxVQUFNLGlCQUFpQyxnQkFBZ0IsQ0FBQztBQUN4RCxRQUFJLE1BQWMsZ0JBQWdCLENBQUMsRUFBRTtBQUdyQyxVQUFNLFdBQVcsSUFBSSxRQUFRLFlBQVksRUFBRTtBQUMzQztBQUFBLE1BQ0UsT0FBTztBQUFBLE1BQ1A7QUFBQSxHQUFNLElBQUksQ0FBQyxJQUFJLGdCQUFnQixNQUFNLEtBQUssUUFBUTtBQUFBLElBQ3BEO0FBRUEsUUFBSTtBQUVGLFlBQU0sS0FBSyxLQUFLLEdBQUc7QUFHbkIsZUFBUyxXQUFXLEdBQUcsV0FBVyxHQUFHLFlBQVk7QUFFL0MsY0FBTSxxQkFBcUIsS0FBSyxPQUFPLElBQUksTUFBTztBQUNsRCxjQUFNLEtBQUssZUFBZSxrQkFBa0I7QUFDNUMsY0FBTSxLQUFLLFNBQVMsTUFBTSxVQUFVO0FBQUEsTUFDdEM7QUFHQSxZQUFNLEtBQUssa0JBQWtCLEdBQUs7QUFJbEMsWUFBTSxLQUFLLGdCQUFnQixrQkFBa0I7QUFHN0MsWUFBTSxPQUFPLE1BQU0sS0FBSyxVQUFVLGNBQWM7QUFDaEQsWUFBTSxJQUFZLGFBQUssSUFBSTtBQUczQixZQUFNLG9CQUFvQixFQUFFLCtDQUErQztBQUczRSxZQUFNLHVCQUF1QixFQUFFLHNFQUFzRTtBQUNyRyxZQUFNLFVBQW9CLHFCQUFxQixJQUFJLENBQUMsT0FBTyxZQUFZO0FBQ3JFLGVBQU8sRUFBRSxPQUFPLEVBQUUsS0FBSyxHQUFHLEVBQUUsTUFBTSxFQUFFLEtBQUssTUFBTTtBQUFBLE1BQ2pELENBQUMsRUFBRSxRQUFRO0FBR1gsWUFBTSxpQkFBaUIsa0JBQWtCLE9BQU8sQ0FBQyxPQUFPLFlBQVk7QUFDbEUsY0FBTSxjQUFjLEVBQUUsT0FBTyxFQUFFLEtBQUssR0FBRyxFQUFFLE1BQU0sRUFBRSxLQUFLLE1BQU07QUFDNUQsZUFBTyxDQUFDLFFBQVEsU0FBUyxXQUFZO0FBQUEsTUFDdkMsQ0FBQztBQUdEO0FBQUEsUUFDRSxPQUFPO0FBQUEsUUFDUCxHQUFHLGVBQWUsTUFBTSx5QkFBeUIsT0FBTyxFQUFFLElBQzFELGlCQUFpQixvQkFBb0IsU0FBUyxDQUFDLEdBQUcsT0FBTyxFQUFFLElBQzNELGFBQWEsRUFBRSxVQUFVLGVBQWUsV0FBVyxLQUFLLEtBQUssQ0FBQyxDQUFDO0FBQUEsTUFDakU7QUFHQSxVQUFJLENBQUMsYUFBYyxnQkFBZTtBQUdsQyxVQUFJLGtCQUFrQjtBQUFBLFFBQ3BCLGFBQWE7QUFBQSxRQUNiLGNBQWM7QUFBQSxRQUNkLGFBQWE7QUFBQSxRQUNiLGlCQUFpQjtBQUFBLE1BQ25CO0FBR0Esd0JBQ0UsTUFBTSwyQkFBMkIsZ0JBQWdCLGdCQUFnQixlQUFlO0FBR2xGLFVBQUksY0FBYztBQUNoQjtBQUFBLFVBQ0UsT0FBTztBQUFBLFVBQ1AsYUFBYSxnQkFBZ0IsV0FBVyxrQkFDckMsZ0JBQWdCLFlBQVksb0JBQzVCLGdCQUFnQixXQUFXLGtCQUMzQixnQkFBZ0IsZUFBZTtBQUFBLFFBQ3BDO0FBQUEsTUFDRjtBQUdBLFlBQU0sV0FBVyx1QkFBdUIsR0FBSTtBQUFBLElBRTlDLFNBQVMsT0FBZ0I7QUFDdkIsVUFBSSxPQUFPLFVBQVUsVUFBVTtBQUM3QixZQUFJLE1BQU0sU0FBUyw2QkFBNkIsR0FBRztBQUNqRCxtQkFBUyx5Q0FBeUMsS0FBSztBQUN2RDtBQUFBLFFBQ0Y7QUFBQSxNQUNGO0FBQ0E7QUFBQSxRQUNFLHlEQUF5RDtBQUFBLE1BQzNEO0FBQUEsSUFDRjtBQUFBLEVBQ0Y7QUFDRjtBQU9BLGVBQWUsMkJBRVgsZ0JBQ0EsZ0JBQ0EsaUJBS0c7QUFHTCxXQUFTLElBQUksR0FBRyxJQUFJLGVBQWUsUUFBUSxLQUFLO0FBQzlDLFVBQU0sc0JBQXNCLGVBQWUsQ0FBQztBQUU1QyxVQUFNLFVBQVU7QUFBQSxNQUNkO0FBQUEsTUFDQSxlQUFlO0FBQUEsSUFDakI7QUFFQSxRQUFJLGdCQUFnQixZQUFZLFFBQVc7QUFFekMsWUFBTSxXQUFXLE1BQU0sd0JBQXdCLE9BQU87QUFHdEQsWUFBTSx3QkFBd0IsTUFBTSwyQkFBMkIsT0FBTztBQUd0RSxjQUFRLFVBQVU7QUFBQSxRQUNoQjtBQUNFLDBCQUFnQjtBQUNoQjtBQUFBLFFBQ0Y7QUFDRSwwQkFBZ0I7QUFDaEI7QUFBQSxRQUNGO0FBQ0UsMEJBQWdCO0FBQ2hCO0FBQUEsUUFDRjtBQUNFLDBCQUFnQjtBQUNoQjtBQUFBLFFBQ0Y7QUFDRTtBQUFBLE1BQ0o7QUFHQSxVQUFJLGtCQUFrQjtBQUVwQixjQUFNLGVBQ0o7QUFDRixjQUFNLGtDQUNKO0FBQ0YsY0FBTSxXQUNKLGVBQWUsUUFBUSxLQUFLO0FBRTlCLGNBQU0sc0JBQXNCLFVBQVcsT0FBTztBQUFBLE1BQ2hEO0FBQUEsSUFDRixXQUFXLENBQUMsZ0JBQWdCLFlBQVksUUFBVztBQUVqRCxvQkFBYyxPQUFRO0FBQUEsSUFDeEI7QUFJQSxVQUFNLFdBQVcsMkJBQTJCO0FBQUEsRUFDOUM7QUFHQSxTQUFPO0FBQ1Q7QUFPQSxTQUFTLGdCQUFnQkEsa0JBQW1DO0FBQzFELE1BQUksUUFBUSxLQUFLLFNBQVMsR0FBRztBQUUzQixVQUFNLFdBQVcsUUFBUSxLQUFLLE1BQU0sR0FBRyxRQUFRLEtBQUssTUFBTTtBQUcxRCxRQUFJLGVBQWU7QUFDbkIsYUFBUyxRQUFRLE9BQU8sUUFBUTtBQUM5QixVQUFJLFFBQVEsS0FBTSxnQkFBZTtBQUFBLGVBQ3hCLFFBQVEsU0FBVSxvQkFBbUI7QUFBQSxlQUNyQyxRQUFRLFdBQVksZ0JBQWU7QUFBQSxlQUNuQyxRQUFRLFNBQVUsZ0JBQWU7QUFBQSxlQUdqQyxJQUFJLFNBQVMsUUFBUSxFQUFHLGlCQUFnQjtBQUFBLGVBR3hDLFFBQVEsVUFBVyxDQUFBQSxtQkFBa0JBLGlCQUFnQixRQUFRO0FBQUEsSUFNeEUsQ0FBQztBQUdELFVBQU0sWUFBWSxzQkFBc0IsWUFBWTtBQUNwRCxRQUFJLGNBQWMsT0FBVyxDQUFBQSxtQkFBa0IsQ0FBQyxTQUFTO0FBQUEsRUFDM0Q7QUFDQSxTQUFPQTtBQUNUO0FBTUEsZUFBZSx3QkFBd0IsV0FBVyxNQUFNO0FBQ3REO0FBQUEsSUFDRSxPQUFPO0FBQUEsSUFDUCwwQkFDQyxRQUFRLEtBQUssU0FBUyxJQUNuQixPQUFPLFFBQVEsS0FBSyxTQUFTLEtBQUssc0JBQ2xDO0FBQUEsRUFDTjtBQUNBLFlBQVUsTUFBTSxXQUFXLFFBQVEsT0FBTztBQUFBLElBQ3hDO0FBQUEsRUFDRixDQUFDO0FBQ0QsU0FBTyxNQUFNLFFBQVEsUUFBUTtBQUc3QixRQUFNLDBCQUEwQjtBQUVoQyxTQUFPO0FBQ1Q7QUFNQSxlQUFlLDBCQUEwQixlQUF1QixJQUFJO0FBRWxFLE1BQUksaUJBQWlCLElBQUk7QUFDdkIsUUFBSSxRQUFRLElBQUksV0FBWSxnQkFBZSxRQUFRLElBQUk7QUFBQSxRQUVsRDtBQUFBLEVBQ1A7QUFFQSxNQUFJLE9BQU8sUUFBUSw0QkFBNEI7QUFHL0MsTUFBSTtBQUNGLFVBQU0sS0FBSyxrQkFBa0IsSUFBSztBQUNsQyxVQUFNLEtBQUssS0FBSyw4Q0FBOEM7QUFBQSxNQUM1RCxXQUFXO0FBQUEsSUFDYixDQUFDO0FBQ0QsVUFBTSxLQUFLLGdCQUFnQiwyQkFBMkI7QUFBQSxFQUN4RCxTQUFTLE9BQU87QUFDZCxhQUFTLG9FQUFvRTtBQUM3RTtBQUFBLEVBQ0Y7QUFFQSxRQUFNLGNBQWMsTUFBTSxLQUN2QixRQUFRLDJCQUEyQixFQUNuQyxVQUFVO0FBR2IsUUFBTSxLQUFLLFFBQVEsMkJBQTJCLEVBQUUsTUFBTTtBQUN0RCxRQUFNLEtBQUssZ0JBQWdCLDJDQUEyQztBQUN0RSxNQUFJO0FBRUYsVUFBTSxLQUNILFFBQVEsMkNBQTJDLEVBQ25ELEtBQUssWUFBWTtBQUNwQixVQUFNLEtBQUssZUFBZSxJQUFJO0FBRzlCLFVBQU0sS0FBSyxTQUFTLE1BQU0sV0FBVztBQUNyQyxVQUFNLEtBQUssZUFBZSxHQUFHO0FBQzdCLFVBQU0sS0FBSyxTQUFTLE1BQU0sT0FBTztBQUNqQyxVQUFNLEtBQUssZUFBZSxHQUFJO0FBRzlCLFVBQU0sS0FBSyxVQUFVLDRCQUE0QixFQUFFLE1BQU07QUFDekQ7QUFBQSxNQUNFLE9BQU87QUFBQSxNQUNQLDJCQUEyQixjQUFjLFNBQVMsZUFBZTtBQUFBLElBQ25FO0FBR0EsVUFBTSxLQUFLLGVBQWUsR0FBSTtBQUFBLEVBQ2hDLFFBQVE7QUFFTjtBQUFBLE1BQ0Usa0JBQWtCLFlBQVk7QUFBQSxJQUNoQztBQUFBLEVBQ0Y7QUFDRjtBQU1PLFNBQVMsMkJBQ2QsU0FDQSxZQUNxQjtBQUNyQixRQUFNLElBQVksYUFBSyxPQUFPO0FBSTlCLE1BQUksZUFBZSxFQUFFLE9BQU8sRUFBRSxLQUFLLElBQUksRUFBRSxPQUFPLENBQUMsR0FBR0MsYUFBWTtBQUM5RCxRQUFJLEVBQUVBLFFBQU8sRUFBRSxLQUFLLElBQUksR0FBRyxTQUFTLFFBQVEsR0FBRztBQUM3QyxhQUFPO0FBQUEsSUFDVCxNQUFPLFFBQU87QUFBQSxFQUNoQixDQUFDO0FBRUQsTUFBSSxVQUFtQjtBQUFBO0FBQUE7QUFBQTtBQUFBLElBS3JCLElBQUksYUFBYSxLQUFLLElBQUksR0FBRyxRQUFRLE9BQU8sRUFBRTtBQUFBO0FBQUEsSUFHOUMsWUFBWTtBQUFBO0FBQUE7QUFBQSxJQUdaLFVBQVU7QUFBQTtBQUFBO0FBQUEsSUFHVixhQUFhLG9CQUFJLEtBQUs7QUFBQSxJQUN0QixhQUFhLG9CQUFJLEtBQUs7QUFBQTtBQUFBLElBR3RCLE1BQU07QUFBQSxJQUNOLGNBQWMsQ0FBQztBQUFBLElBQ2YsY0FBYztBQUFBLEVBQ2hCO0FBS0EsTUFBSSxpQkFBaUIsYUFBYSxLQUFLLEVBQUUsS0FBSztBQUc5QyxtQkFBaUIsZUFDZCxZQUFZLEVBQ1osUUFBUSxNQUFNLEdBQUcsRUFDakIsUUFBUSxlQUFlLEVBQUUsRUFDekIsUUFBUSxtQkFBbUIsRUFBRSxFQUM3QixLQUFLO0FBS1IsTUFBSSxlQUNGLGVBQWUsTUFBTSw4REFBOEQ7QUFFckYsTUFBSSxDQUFDLGNBQWM7QUFFakIsWUFBUSxPQUFPLFlBQVksY0FBYztBQUd6QyxZQUFRLE9BQU87QUFBQSxFQUNqQixPQUFPO0FBRUwsUUFBSSxxQkFBcUIsZUFBZSxRQUFRLGFBQWEsQ0FBQyxDQUFDO0FBRy9ELFlBQVEsT0FBTyxZQUFZLGVBQWUsTUFBTSxHQUFHLGtCQUFrQixDQUFDLEVBQUUsS0FBSztBQUc3RSxRQUFJLGNBQWMsZUFBZSxNQUFNLGtCQUFrQixFQUFFLEtBQUs7QUFDaEUsUUFBSSxZQUFZLE1BQU0sUUFBUSxHQUFHO0FBRS9CLG9CQUFjLFlBQVksUUFBUSxLQUFLLEdBQUc7QUFBQSxJQUM1QztBQUNBLGdCQUFZLFFBQVEsUUFBUSxNQUFNO0FBQ2xDLFlBQVEsT0FBTztBQUFBLEVBQ2pCO0FBT0EsUUFBTSxlQUF1QixFQUFFLE9BQU8sRUFDbkMsS0FBSyx5QkFBeUIsRUFDOUIsS0FBSyxFQUNMLEtBQUs7QUFDUixNQUFJLGFBQXFCLEVBQUUsT0FBTyxFQUMvQixLQUFLLDJCQUEyQixFQUNoQyxLQUFLLEVBQ0wsS0FBSztBQUNSLE1BQUksV0FBVyxTQUFTLElBQUksRUFBRyxTQUFRLE9BQU87QUFDOUMsZUFBYSxXQUFXLFFBQVEsT0FBTyxFQUFFO0FBQ3pDLFVBQVEsZUFBZSxPQUFPLGVBQWUsTUFBTSxVQUFVO0FBRzdELFFBQU0sUUFBUSxvQkFBSSxLQUFLO0FBQ3ZCLFFBQU0sV0FBVyxDQUFDO0FBQ2xCLFFBQU0sV0FBVyxDQUFDO0FBR2xCLFFBQU0sbUJBQStCO0FBQUEsSUFDbkMsTUFBTTtBQUFBLElBQ04sT0FBTyxRQUFRO0FBQUEsRUFDakI7QUFDQSxVQUFRLGVBQWUsQ0FBQyxnQkFBZ0I7QUFLeEMsUUFBTSxlQUFlLEVBQUUsT0FBTyxFQUFFLEtBQUssZUFBZSxFQUFFLEtBQUssRUFBRSxLQUFLO0FBRWxFLE1BQUksY0FBYztBQUVoQixVQUFNLGtCQUFrQixhQUFhLE1BQU0sR0FBRyxFQUFFLENBQUMsRUFBRSxRQUFRLEtBQUssRUFBRSxFQUFFLEtBQUs7QUFDekUsUUFBSSxZQUFZLE9BQU8sV0FBVyxlQUFlO0FBR2pELFVBQU0sZ0JBQWdCLGFBQWEsTUFBTSxHQUFHLEVBQUUsQ0FBQyxFQUFFLEtBQUs7QUFHdEQsUUFBSSxTQUFTLE9BQU8sU0FBUyxjQUFjLE1BQU0sTUFBTSxJQUFJLENBQUMsS0FBSyxFQUFFO0FBR25FLFFBQUksT0FBTyxjQUFjLE1BQU0sTUFBTSxJQUFJLENBQUMsS0FBSztBQUcvQyxRQUFJLGlCQUFpQixRQUFRO0FBQzNCLGVBQVMsU0FBUztBQUNsQixrQkFBWSxZQUFZO0FBQ3hCLGFBQU87QUFBQSxJQUNULFdBQ1MsaUJBQWlCLFNBQVM7QUFDakMsZUFBUyxTQUFTO0FBQ2xCLGtCQUFZLFlBQVk7QUFDeEIsYUFBTztBQUFBLElBQ1Q7QUFHQSxXQUFPLEtBQUssUUFBUSxPQUFPLElBQUk7QUFDL0IsV0FBTyxLQUFLLFFBQVEsTUFBTSxHQUFHO0FBRzdCLFlBQVEsWUFBWTtBQUNwQixZQUFRLFdBQVc7QUFBQSxFQUNyQjtBQUtBLG1CQUFpQixRQUFRLENBQUMsYUFBYTtBQUVyQyxRQUFJLFNBQVMsT0FBTyxRQUFRLElBQUk7QUFFOUIsVUFBSSxTQUFTLFNBQVMsUUFBVztBQUMvQixnQkFBUSxPQUFPLFNBQVM7QUFBQSxNQUMxQjtBQUdBLFVBQUksU0FBUyxhQUFhLFFBQVc7QUFDbkMsZ0JBQVEsV0FBVyxDQUFDLFNBQVMsUUFBUTtBQUFBLE1BQ3ZDO0FBQUEsSUFDRjtBQUFBLEVBQ0YsQ0FBQztBQUtELE1BQUksZ0JBQWdCLE9BQU8sRUFBRyxRQUFPO0FBQUEsT0FDaEM7QUFDSCxRQUFJO0FBQ0Y7QUFBQSxRQUNFLHVCQUF1QixRQUFRLEdBQUcsU0FBUyxDQUFDLENBQUMsTUFBTSxRQUFRLElBQUksT0FDM0QsUUFBUSxZQUFZO0FBQUEsTUFDMUI7QUFBQSxJQUNGLFFBQVE7QUFDTixlQUFTLG9DQUFvQztBQUFBLElBQy9DO0FBQ0EsV0FBTztBQUFBLEVBQ1Q7QUFDRjtBQU1BLFNBQVMsZ0JBQWdCLFNBQTJCO0FBQ2xELE1BQUk7QUFDRixRQUFJLFFBQVEsS0FBSyxNQUFNLFNBQVMsRUFBRyxRQUFPO0FBQzFDLFFBQUksUUFBUSxLQUFLLFNBQVMsS0FBSyxRQUFRLEtBQUssU0FBUyxJQUFLLFFBQU87QUFDakUsUUFBSSxRQUFRLEdBQUcsU0FBUyxLQUFLLFFBQVEsR0FBRyxTQUFTLEdBQUksUUFBTztBQUM1RCxRQUNFLFFBQVEsZ0JBQWdCLEtBQ3hCLFFBQVEsaUJBQWlCLFFBQ3pCLFFBQVEsaUJBQWlCLFVBQ3pCLE9BQU8sTUFBTSxRQUFRLFlBQVksS0FDakMsUUFBUSxlQUFlLEtBQ3ZCO0FBQ0EsYUFBTztBQUFBLElBQ1Q7QUFDQSxXQUFPO0FBQUEsRUFDVCxTQUFTLE9BQU87QUFDZCxXQUFPO0FBQUEsRUFDVDtBQUNGO0FBcUJPLFNBQVMsc0JBQ2QsTUFDOEI7QUFDOUIsTUFBSSxxQkFBcUMsRUFBRSxLQUFLLElBQUksWUFBWSxDQUFDLEVBQUU7QUFDbkUsTUFBSSxhQUErQixDQUFDO0FBQ3BDLE1BQUksaUJBQWlCO0FBR3JCLE1BQUksQ0FBQyxLQUFLLFNBQVMsa0JBQWtCLEdBQUc7QUFDdEMsV0FBTztBQUFBLEVBR1QsV0FBVyxLQUFLLFNBQVMsVUFBVSxHQUFHO0FBQ3BDLGVBQVcsS0FBSyxFQUFFLEtBQUssTUFBTSxZQUFZLENBQUMsRUFBRSxDQUFDO0FBQUEsRUFHL0MsT0FBTztBQUVMLFNBQUssTUFBTSxHQUFHLEVBQUUsUUFBUSxDQUFDLFlBQVk7QUFHbkMsVUFBSSxRQUFRLFNBQVMsa0JBQWtCLEdBQUc7QUFDeEMsMkJBQW1CLE1BQU07QUFHekIsWUFBSSxDQUFDLG1CQUFtQixJQUFJLFdBQVcsTUFBTTtBQUMzQyw2QkFBbUIsTUFBTSxhQUFhO0FBR3hDLFlBQUksUUFBUSxTQUFTLEdBQUcsR0FBRztBQUV6Qiw2QkFBbUIsTUFBTSxLQUFLLFVBQVUsR0FBRyxLQUFLLFFBQVEsR0FBRyxDQUFDO0FBQUEsUUFDOUQ7QUFFQSwyQkFBbUIsT0FBTztBQUFBLE1BRzVCLFdBQVcsUUFBUSxXQUFXLGFBQWEsR0FBRztBQUM1QyxZQUFJLGtCQUFrQixDQUFDLFFBQVEsUUFBUSxlQUFlLEVBQUUsQ0FBQztBQUN6RCxZQUFJLFFBQVEsU0FBUyxHQUFHO0FBQ3RCLDRCQUFrQixRQUFRLFFBQVEsZUFBZSxFQUFFLEVBQUUsTUFBTSxHQUFHO0FBQ2hFLDJCQUFtQixhQUFhO0FBQUEsTUFHbEMsV0FBVyxRQUFRLFdBQVcsUUFBUSxHQUFHO0FBQ3ZDLHlCQUFpQixPQUFPLFNBQVMsUUFBUSxNQUFNLEdBQUcsRUFBRSxDQUFDLENBQUM7QUFBQSxNQUN4RDtBQUdBLFVBQUksbUJBQW1CLFdBQVcsV0FBVyxHQUFHO0FBRTlDLGNBQU0sVUFBVSxtQkFBbUIsSUFBSSxNQUFNLEdBQUcsRUFBRSxDQUFDO0FBQ25ELFlBQUksZ0JBQWdCLFFBQVEsTUFBTSxHQUFHO0FBR3JDLDJCQUFtQixhQUFhLENBQUMsY0FBYyxjQUFjLFNBQVMsQ0FBQyxDQUFDO0FBQUEsTUFDMUU7QUFBQSxJQUNGLENBQUM7QUFHRCxhQUFTLElBQUksR0FBRyxLQUFLLGdCQUFnQixLQUFLO0FBQ3hDLFVBQUksV0FBVztBQUFBLFFBQ2IsS0FBSyxtQkFBbUIsSUFBSSxRQUFRLFVBQVUsVUFBVSxDQUFDO0FBQUEsUUFDekQsWUFBWSxtQkFBbUI7QUFBQSxNQUNqQztBQUNBLGlCQUFXLEtBQUssUUFBUTtBQUFBLElBQzFCO0FBQUEsRUFDRjtBQUVBLFNBQU87QUFDVDtBQU1BLGVBQWUsNEJBQTRCO0FBQ3pDLE1BQUksaUJBQWlCLENBQUMsU0FBUyxTQUFTLE1BQU07QUFDOUMsTUFBSSxnQkFBZ0I7QUFBQSxJQUNsQjtBQUFBLElBQ0E7QUFBQSxJQUNBO0FBQUEsSUFDQTtBQUFBLElBQ0E7QUFBQSxJQUNBO0FBQUEsSUFDQTtBQUFBLElBQ0E7QUFBQSxJQUNBO0FBQUEsSUFDQTtBQUFBLElBQ0E7QUFBQSxJQUNBO0FBQUEsSUFDQTtBQUFBLEVBQ0Y7QUFHQSxRQUFNLEtBQUssTUFBTSxRQUFRLE9BQU8sVUFBVTtBQUN4QyxVQUFNLE1BQU0sTUFBTSxRQUFRO0FBQzFCLFFBQUkscUJBQXFCO0FBR3pCLGtCQUFjLFFBQVEsQ0FBQyxnQkFBZ0I7QUFDckMsVUFBSSxJQUFJLElBQUksRUFBRSxTQUFTLFdBQVcsRUFBRyxzQkFBcUI7QUFBQSxJQUM1RCxDQUFDO0FBRUQsbUJBQWUsUUFBUSxDQUFDLGlCQUFpQjtBQUN2QyxVQUFJLElBQUksYUFBYSxNQUFNLGFBQWMsc0JBQXFCO0FBQUEsSUFDaEUsQ0FBQztBQUVELFFBQUksb0JBQW9CO0FBRXRCLFlBQU0sTUFBTSxNQUFNO0FBQUEsSUFDcEIsT0FBTztBQUVMLFlBQU0sTUFBTSxTQUFTO0FBQUEsSUFDdkI7QUFBQSxFQUNGLENBQUM7QUFFRDtBQUNGOyIsCiAgIm5hbWVzIjogWyJkb3RlbnYiLCAiY29sb3VyIiwgInN0YXJ0VGltZSIsICJjcmVhdGVDbGllbnQiLCAiY3JlYXRlQ2xpZW50IiwgInN1cGFiYXNlIiwgInN0b3JlSWQiLCAic3VwYWJhc2UiLCAiY3JlYXRlQ2xpZW50IiwgInN0b3JlSWQiLCAiY2F0ZWdvcmlzZWRVcmxzIiwgImVsZW1lbnQiXQp9Cg==

	