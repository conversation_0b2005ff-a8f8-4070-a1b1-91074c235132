# New Zealand Supermarket Scrapers - Status Checker
# This script monitors the status of all three scrapers and their MongoDB connections

Write-Host "🇳🇿 New Zealand Supermarket Scrapers - Status Check" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Gray
Write-Host ""

# Check MongoDB status
Write-Host "📊 MongoDB Status:" -ForegroundColor Cyan
$mongoStatus = docker ps --format "table {{.Names}}\t{{.Status}}" | Select-String "mongodb"
if ($mongoStatus) {
    Write-Host "  ✅ MongoDB Container: $mongoStatus" -ForegroundColor Green
} else {
    Write-Host "  ❌ MongoDB Container: Not running" -ForegroundColor Red
    Write-Host "  💡 Start with: docker start mongodb" -ForegroundColor Yellow
}
Write-Host ""

# Check log files and scraper status
$scrapers = @(
    @{Name="Woolworths"; LogFile="logs\woolworths.log"; Color="Green"},
    @{Name="New World"; LogFile="logs\newworld.log"; Color="Blue"},
    @{Name="PakNSave"; LogFile="logs\paknsave.log"; Color="Magenta"}
)

foreach ($scraper in $scrapers) {
    Write-Host "🛒 $($scraper.Name) Scraper:" -ForegroundColor $scraper.Color
    
    if (Test-Path $scraper.LogFile) {
        $logContent = Get-Content $scraper.LogFile -Tail 10 -ErrorAction SilentlyContinue
        
        # Check for MongoDB connection
        $mongoConnected = $logContent | Select-String "MongoDB connection established"
        if ($mongoConnected) {
            Write-Host "  ✅ MongoDB: Connected" -ForegroundColor Green
        } else {
            $mongoError = $logContent | Select-String "MongoDB connection failed|ECONNREFUSED"
            if ($mongoError) {
                Write-Host "  ❌ MongoDB: Connection failed" -ForegroundColor Red
            } else {
                Write-Host "  ⏳ MongoDB: Status unknown" -ForegroundColor Yellow
            }
        }
        
        # Check for scraping activity
        $scrapingActivity = $logContent | Select-String "pages to be scraped|Page \[|Scraping page"
        if ($scrapingActivity) {
            Write-Host "  ✅ Scraping: Active" -ForegroundColor Green
            $latestActivity = $scrapingActivity | Select-Object -Last 1
            Write-Host "  📄 Latest: $($latestActivity.Line.Trim())" -ForegroundColor White
        } else {
            Write-Host "  ⏳ Scraping: No recent activity" -ForegroundColor Yellow
        }
        
        # Check for errors
        $errors = $logContent | Select-String "❌|Error|Exception|Failed" | Select-Object -Last 1
        if ($errors) {
            Write-Host "  ⚠️  Last Error: $($errors.Line.Trim())" -ForegroundColor Red
        }
        
        # Show log file size and last modified
        $logInfo = Get-Item $scraper.LogFile
        Write-Host "  📁 Log Size: $([math]::Round($logInfo.Length/1KB, 2)) KB, Modified: $($logInfo.LastWriteTime.ToString('HH:mm:ss'))" -ForegroundColor Gray
        
    } else {
        Write-Host "  ❌ Log file not found: $($scraper.LogFile)" -ForegroundColor Red
    }
    Write-Host ""
}

# Check for consolidated products in MongoDB
Write-Host "🗄️  Database Status:" -ForegroundColor Cyan
try {
    # Try to connect to MongoDB and get collection stats
    $mongoRunning = docker ps --format "{{.Names}}" | Select-String "mongodb"
    if ($mongoRunning) {
        Write-Host "  ✅ MongoDB accessible for queries" -ForegroundColor Green
        Write-Host "  💡 Use MongoDB Compass or mongo shell to view data" -ForegroundColor Yellow
        Write-Host "  🔗 Connection: mongodb://localhost:27017/nz-supermarket-scraper" -ForegroundColor Gray
    } else {
        Write-Host "  ❌ MongoDB not accessible" -ForegroundColor Red
    }
} catch {
    Write-Host "  ⚠️  Could not check database status" -ForegroundColor Yellow
}
Write-Host ""

# Show available commands
Write-Host "🛠️  Available Commands:" -ForegroundColor Cyan
Write-Host "  Start all scrapers:     .\run-all-scrapers.ps1 -Mode db-images" -ForegroundColor White
Write-Host "  Start MongoDB:          docker start mongodb" -ForegroundColor White
Write-Host "  View live logs:         Get-Content logs\woolworths.log -Wait" -ForegroundColor White
Write-Host "  Stop all processes:     Get-Process | Where-Object {`$_.ProcessName -match 'node|dotnet'} | Stop-Process" -ForegroundColor White
Write-Host ""

Write-Host "Status check completed at $(Get-Date -Format 'HH:mm:ss')" -ForegroundColor Gray
