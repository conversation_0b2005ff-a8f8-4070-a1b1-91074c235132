// Simple test script to verify Supabase Storage setup
// Run with: node test-image-upload.js

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

async function testSupabaseStorage() {
  console.log('🧪 Testing Supabase Storage setup...');
  
  // Check environment variables
  const url = process.env.SUPABASE_URL;
  const key = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;
  
  if (!url || !key) {
    console.error('❌ Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY in .env file');
    process.exit(1);
  }
  
  console.log('✅ Environment variables found');
  
  // Create Supabase client
  const supabase = createClient(url, key, { auth: { persistSession: false } });
  
  try {
    // Test bucket access
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    
    if (bucketsError) {
      console.error('❌ Failed to list storage buckets:', bucketsError.message);
      process.exit(1);
    }
    
    console.log('📦 Available buckets:', buckets.map(b => b.name));
    
    // Check if product-images bucket exists
    const productImagesBucket = buckets.find(b => b.name === 'product-images');
    if (!productImagesBucket) {
      console.error('❌ "product-images" bucket not found!');
      console.log('   Please create it in your Supabase Dashboard → Storage');
      process.exit(1);
    }
    
    console.log('✅ "product-images" bucket exists');
    
    // Test upload with a small test file
    const testData = Buffer.from('test-image-data', 'utf-8');
    const testPath = 'test/test-upload.txt';
    
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('product-images')
      .upload(testPath, testData, {
        contentType: 'text/plain',
        upsert: true
      });
    
    if (uploadError) {
      console.error('❌ Test upload failed:', uploadError.message);
      process.exit(1);
    }
    
    console.log('✅ Test upload successful');
    
    // Test getting public URL
    const { data: publicUrlData } = supabase.storage
      .from('product-images')
      .getPublicUrl(testPath);
    
    console.log('🔗 Public URL generated:', publicUrlData.publicUrl);
    
    // Clean up test file
    const { error: deleteError } = await supabase.storage
      .from('product-images')
      .remove([testPath]);
    
    if (deleteError) {
      console.warn('⚠️ Failed to cleanup test file:', deleteError.message);
    } else {
      console.log('🧹 Test file cleaned up');
    }
    
    console.log('');
    console.log('🎉 Supabase Storage is ready for image uploads!');
    console.log('   You can now run: npm run "db images"');
    
  } catch (err) {
    console.error('❌ Unexpected error:', err.message);
    process.exit(1);
  }
}

testSupabaseStorage();