process.argv = [process.argv[0], ...process.argv.slice(3)];

		import __esrun_url from 'url';

		import { createRequire as __esrun_createRequire } from "module";

		const __esrun_fileUrl = __esrun_url.pathToFileURL("C:\Users\<USER>\OneDrive\Desktop\scrapers13-07\Woolworths\esrun-1753257088967.tmp.mjs");

		const require = __esrun_createRequire(__esrun_fileUrl);
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __require = /* @__PURE__ */ ((x) => typeof require !== "undefined" ? require : typeof Proxy !== "undefined" ? new Proxy(x, {
  get: (a, b) => (typeof require !== "undefined" ? require : a)[b]
}) : x)(function(x) {
  if (typeof require !== "undefined") return require.apply(this, arguments);
  throw Error('Dynamic require of "' + x + '" is not supported');
});
var __commonJS = (cb, mod) => function __require2() {
  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));

// src/typings.js
var require_typings = __commonJS({
  "src/typings.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// src/utilities.js
var require_utilities = __commonJS({
  "src/utilities.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.validCategories = exports.colour = void 0;
    exports.log = log4;
    exports.logError = logError4;
    exports.logProductRow = logProductRow2;
    exports.logTableHeader = logTableHeader2;
    exports.readLinesFromTextFile = readLinesFromTextFile2;
    exports.getTimeElapsedSince = getTimeElapsedSince2;
    exports.toTitleCase = toTitleCase2;
    var fs_1 = __require("fs");
    var tableIDWidth = 6;
    var tableNameWidth = 60;
    var tableSizeWidth = 17;
    exports.colour = {
      red: "\x1B[31m",
      green: "\x1B[32m",
      yellow: "\x1B[33m",
      blue: "\x1B[38;5;117m",
      magenta: "\x1B[35m",
      cyan: "\x1B[36m",
      white: "\x1B[37m",
      crimson: "\x1B[38m",
      grey: "\x1B[90m",
      orange: "\x1B[38;5;214m",
      sky: "\x1B[38;5;153m"
    };
    function log4(colour4, text) {
      const clear = "\x1B[0m";
      console.log(`${colour4}%s${clear}`, text);
    }
    function logError4(text) {
      log4(exports.colour.red, text);
    }
    function logProductRow2(product) {
      var _a;
      const unitPriceString = product.unitPrice ? `$${product.unitPrice.toFixed(2)} /${product.unitName}` : ``;
      log4(getAlternatingRowColour(exports.colour.sky, exports.colour.white), `${product.id.padStart(tableIDWidth)} | ${product.name.slice(0, tableNameWidth).padEnd(tableNameWidth)} | ${(_a = product.size) === null || _a === void 0 ? void 0 : _a.slice(0, tableSizeWidth).padEnd(tableSizeWidth)} | $ ${product.currentPrice.toFixed(2).padStart(4).padEnd(5)} | ` + unitPriceString);
    }
    function logTableHeader2() {
      log4(exports.colour.yellow, `${"ID".padStart(tableIDWidth)} | ${"Name".padEnd(tableNameWidth)} | ${"Size".padEnd(tableSizeWidth)} | ${"Price".padEnd(7)} | Unit Price`);
      let headerLine = "";
      for (let i = 0; i < 111; i++) {
        headerLine += "-";
      }
      log4(exports.colour.yellow, headerLine);
    }
    var alternatingRowColour = false;
    function getAlternatingRowColour(colourA, colourB) {
      alternatingRowColour = alternatingRowColour ? false : true;
      return alternatingRowColour ? colourA : colourB;
    }
    function readLinesFromTextFile2(filename) {
      try {
        const file = (0, fs_1.readFileSync)(filename, "utf-8");
        const result = file.split(/\r?\n/).filter((line) => {
          if (line.trim().length > 0)
            return true;
          else
            return false;
        });
        return result;
      } catch (error) {
        throw "Error reading " + filename;
      }
    }
    function getTimeElapsedSince2(startTime2) {
      let elapsedTimeSeconds = (Date.now() - startTime2) / 1e3;
      let elapsedTimeString = Math.floor(elapsedTimeSeconds).toString();
      if (elapsedTimeSeconds >= 60) {
        return Math.floor(elapsedTimeSeconds / 60) + ":" + Math.floor(elapsedTimeSeconds % 60).toString().padStart(2, "0");
      } else
        return elapsedTimeString + "s";
    }
    exports.validCategories = [
      // freshCategory
      "eggs",
      "fruit",
      "fresh-vegetables",
      "salads-coleslaw",
      "bread",
      "bread-rolls",
      "specialty-bread",
      "bakery-cakes",
      "bakery-desserts",
      // chilledCategory
      "milk",
      "long-life-milk",
      "sour-cream",
      "cream",
      "yoghurt",
      "butter",
      "cheese",
      "cheese-slices",
      "salami",
      "other-deli-foods",
      // meatCategory
      "beef-lamb",
      "chicken",
      "ham",
      "bacon",
      "pork",
      "patties-meatballs",
      "sausages",
      "deli-meats",
      "meat-alternatives",
      "seafood",
      "salmon",
      // frozenCategory
      "ice-cream",
      "ice-blocks",
      "pastries-cheesecake",
      "frozen-chips",
      "frozen-vegetables",
      "frozen-fruit",
      "frozen-seafood",
      "pies-sausage-rolls",
      "pizza",
      "other-savouries",
      // pantryCategory
      "rice",
      "noodles",
      "pasta",
      "beans-spaghetti",
      "canned-fish",
      "canned-meat",
      "soup",
      "cereal",
      "spreads",
      "baking",
      "sauces",
      "oils-vinegars",
      "world-foods",
      // snacksCategory
      "chocolate",
      "boxed-chocolate",
      "chips",
      "crackers",
      "biscuits",
      "muesli-bars",
      "nuts-bulk-mix",
      "sweets-lollies",
      "other-snacks",
      // drinksCategory
      "black-tea",
      "green-tea",
      "herbal-tea",
      "drinking-chocolate",
      "coffee",
      "soft-drinks",
      "energy-drinks",
      "juice",
      // petsCategory
      "cat-food",
      "cat-treats",
      "dog-food",
      "dog-treats"
    ];
    function toTitleCase2(str) {
      return str.replace(/\w\S*/g, function(txt) {
        return txt.charAt(0).toUpperCase() + txt.substring(1).toLowerCase();
      });
    }
  }
});

// src/product-overrides.js
var require_product_overrides = __commonJS({
  "src/product-overrides.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.productOverrides = void 0;
    exports.productOverrides = [
      { id: "206889", size: "180g" },
      { id: "196996", size: "300g" },
      { id: "137967", size: "420g" },
      { id: "125856", size: "450g" },
      { id: "189268", size: "1.13kg" },
      { id: "189150", size: "1.2kg" },
      { id: "190454", size: "2.1kg" },
      { id: "189078", size: "1.3kg" },
      { id: "189136", size: "1.2kg" },
      { id: "755237", size: "931g" },
      { id: "755304", size: "1.1kg" },
      { id: "755246", size: "1020g" },
      { id: "755245", size: "1.2kg" },
      { id: "112273", size: "865ml" },
      { id: "269514", size: "584ml" },
      { id: "269515", size: "584ml" },
      { id: "116518", size: "440ml" },
      { id: "151191", size: "570ml" },
      { id: "279904", size: "575ml" },
      { id: "146149", size: "1000ml" },
      { id: "791925", size: "525g" },
      { id: "774216", size: "525g" },
      { id: "784406", size: "525g" },
      { id: "791916", size: "525g" },
      { id: "306624", size: "185g" },
      { id: "156824", size: "180g" },
      { id: "9023", size: "375g" },
      { id: "266962", category: "sweets-lollies" },
      { id: "171524", size: "230ml", category: "baking" },
      { id: "170021", category: "ice-blocks" },
      { id: "71164", category: "sausages" },
      { id: "71174", category: "sausages" },
      { id: "71168", category: "sausages" },
      { id: "71165", category: "sausages" },
      { id: "331560", category: "specialty-bread" },
      { id: "679412", category: "herbal-tea" },
      { id: "790129", category: "herbal-tea" },
      { id: "267492", category: "herbal-tea" },
      { id: "267485", category: "herbal-tea" },
      { id: "413302", category: "herbal-tea" },
      { id: "267488", category: "herbal-tea" },
      { id: "760872", category: "herbal-tea" },
      { id: "681177", category: "herbal-tea" },
      { id: "95091", category: "herbal-tea" },
      { id: "761093", category: "black-tea" },
      { id: "721661", category: "green-tea" },
      { id: "790129", category: "herbal-tea" },
      { id: "267492", category: "herbal-tea" },
      { id: "267485", category: "herbal-tea" },
      { id: "721034", category: "herbal-tea" },
      { id: "413302", category: "herbal-tea" },
      { id: "267488", category: "herbal-tea" },
      { id: "760872", category: "herbal-tea" },
      { id: "681177", category: "herbal-tea" },
      { id: "95091.", category: "herbal-tea" },
      { id: "184090", category: "herbal-tea" },
      { id: "761093", category: "black-tea" },
      { id: "721661", category: "green-tea" },
      { id: "690093", category: "green-tea" },
      { id: "780922", category: "sauces" },
      { id: "780921", category: "sauces" },
      { id: "72618", category: "black-tea" },
      { id: "6053", category: "black-tea" },
      { id: "72617", category: "black-tea" },
      { id: "168068", category: "black-tea" },
      { id: "6052", category: "black-tea" },
      { id: "761436", category: "black-tea" }
    ];
  }
});

// src/index.ts
import * as dotenv3 from "./node_modules/dotenv/lib/main.js";
import playwright from "./node_modules/playwright/index.mjs";
import * as cheerio from "./node_modules/cheerio/dist/esm/index.js";
import _ from "./node_modules/lodash/lodash.js";
import { setTimeout } from "timers/promises";

// src/mongodb.ts
var import_typings = __toESM(require_typings());
var import_utilities = __toESM(require_utilities());
import { MongoClient, GridFSBucket, ObjectId } from "./node_modules/mongodb/lib/index.js";
import * as dotenv from "./node_modules/dotenv/lib/main.js";
dotenv.config();
dotenv.config({ path: `.env.local`, override: true });
var client;
var db;
var gridFS;
var storeId;
var storesCollection;
var brandsCollection;
var consolidatedProductsCollection;
var priceHistoryCollection;
var categoryHierarchyCollection;
async function establishMongoDB() {
  const connectionString = process.env.MONGODB_CONNECTION_STRING;
  const databaseName = process.env.MONGODB_DATABASE_NAME || "nz-supermarket-scraper";
  if (!connectionString) {
    throw Error("MONGODB_CONNECTION_STRING not set in env");
  }
  try {
    client = new MongoClient(connectionString);
    await client.connect();
    db = client.db(databaseName);
    storesCollection = db.collection("stores");
    brandsCollection = db.collection("brands");
    consolidatedProductsCollection = db.collection("consolidatedProducts");
    priceHistoryCollection = db.collection("priceHistory");
    categoryHierarchyCollection = db.collection("categoryHierarchy");
    gridFS = new GridFSBucket(db, { bucketName: "productImages" });
    (0, import_utilities.log)(import_utilities.colour.green, "\u2705 MongoDB connection established");
    await createIndexes();
  } catch (error) {
    (0, import_utilities.logError)(`Failed to connect to MongoDB: ${error.message}`);
    throw error;
  }
}
async function createIndexes() {
  try {
    await consolidatedProductsCollection.createIndex({
      "displayName": "text",
      "normalizedName": "text",
      "variants.storeName": "text"
    });
    await consolidatedProductsCollection.createIndex({ "categoryId": 1 });
    await consolidatedProductsCollection.createIndex({ "brandId": 1 });
    await consolidatedProductsCollection.createIndex({
      "variants.storeProductId": 1,
      "variants.storeId": 1
    });
    await priceHistoryCollection.createIndex({
      "consolidatedProductId": 1,
      "recordedAt": -1
    });
    await priceHistoryCollection.createIndex({ "year": 1, "month": 1 });
    await categoryHierarchyCollection.createIndex({ "parentId": 1, "sortOrder": 1 });
    await categoryHierarchyCollection.createIndex({ "level": 1, "sortOrder": 1 });
    (0, import_utilities.log)(import_utilities.colour.blue, "\u2705 MongoDB indexes created");
  } catch (error) {
    (0, import_utilities.logError)(`Failed to create indexes: ${error.message}`);
  }
}
async function ensureStoreRow() {
  if (storeId !== void 0) return storeId;
  try {
    const existingStore = await storesCollection.findOne({ storeId: "woolworths" });
    if (existingStore) {
      storeId = existingStore._id;
      return storeId;
    }
    const insertResult = await storesCollection.insertOne({
      storeId: "woolworths",
      name: "Woolworths",
      logoUrl: null,
      createdAt: /* @__PURE__ */ new Date(),
      updatedAt: /* @__PURE__ */ new Date()
    });
    storeId = insertResult.insertedId;
    return storeId;
  } catch (error) {
    (0, import_utilities.logError)(`Failed to ensure store row: ${error.message}`);
    throw error;
  }
}
async function upsertProductToMongoDB(scraped) {
  if (!db) throw Error("MongoDB client not initialised");
  try {
    const sId = await ensureStoreRow();
    const now = /* @__PURE__ */ new Date();
    let consolidatedProduct = await consolidatedProductsCollection.findOne({
      "variants.storeProductId": scraped.id,
      "variants.storeId": sId
    });
    if (!consolidatedProduct) {
      const normalizedName = normalizeProductName(scraped.name, scraped.size);
      const newProduct = {
        normalizedName,
        displayName: scraped.name,
        primarySize: scraped.size,
        categoryId: null,
        // TODO: Implement category mapping
        brandId: null,
        // TODO: Implement brand extraction
        matchConfidence: 100,
        manualMatch: false,
        variants: [{
          storeProductId: scraped.id,
          storeId: sId,
          storeName: scraped.name,
          storeSize: scraped.size,
          storeUnitPrice: scraped.unitPrice,
          storeUnitName: scraped.unitName,
          lastSeen: now,
          isActive: true,
          imageUrl: null
        }],
        sizeVariants: scraped.size ? [{
          sizeName: scraped.size,
          sizeWeightGrams: null,
          sizeVolumeMl: null,
          isPrimarySize: true
        }] : [],
        currentPrices: [{
          storeId: sId,
          price: scraped.currentPrice,
          isSpecial: false,
          wasAvailable: true,
          recordedAt: now
        }],
        createdAt: now,
        updatedAt: now
      };
      const insertResult = await consolidatedProductsCollection.insertOne(newProduct);
      consolidatedProduct = { _id: insertResult.insertedId, ...newProduct };
    } else {
      await consolidatedProductsCollection.updateOne(
        { _id: consolidatedProduct._id },
        {
          $set: {
            "variants.$[variant].lastSeen": now,
            "variants.$[variant].storeUnitPrice": scraped.unitPrice,
            "variants.$[variant].storeUnitName": scraped.unitName,
            "currentPrices.$[price].price": scraped.currentPrice,
            "currentPrices.$[price].recordedAt": now,
            updatedAt: now
          }
        },
        {
          arrayFilters: [
            { "variant.storeProductId": scraped.id },
            { "price.storeId": sId }
          ]
        }
      );
    }
    await priceHistoryCollection.insertOne({
      consolidatedProductId: consolidatedProduct._id,
      storeId: sId,
      price: scraped.currentPrice,
      isSpecial: false,
      wasAvailable: true,
      recordedAt: now,
      year: now.getFullYear(),
      month: now.getMonth() + 1
    });
    (0, import_utilities.log)(import_utilities.colour.green, `  Upserted: ${scraped.name.slice(0, 45).padEnd(45)} | $${scraped.currentPrice}`);
    return import_typings.UpsertResponse.PriceChanged;
  } catch (error) {
    (0, import_utilities.logError)(`MongoDB upsert failed: ${error.message}`);
    return import_typings.UpsertResponse.Failed;
  }
}
async function uploadImageToMongoDB(imageUrl, product) {
  if (!gridFS) throw Error("MongoDB GridFS not initialised");
  try {
    const sId = await ensureStoreRow();
    const imageResponse = await fetch(imageUrl);
    if (!imageResponse.ok) {
      (0, import_utilities.logError)(`Failed to download image from ${imageUrl}: ${imageResponse.statusText}`);
      return false;
    }
    const imageBuffer = Buffer.from(await imageResponse.arrayBuffer());
    const existingFiles = await gridFS.find({
      "metadata.productId": product.id
    }).toArray();
    for (const file of existingFiles) {
      await gridFS.delete(file._id);
    }
    const filename = `${product.id}.jpg`;
    const uploadStream = gridFS.openUploadStream(filename, {
      contentType: "image/jpeg",
      metadata: {
        productId: product.id,
        storeId: sId,
        originalUrl: imageUrl,
        uploadedAt: /* @__PURE__ */ new Date()
      }
    });
    await new Promise((resolve, reject) => {
      uploadStream.end(imageBuffer, (error) => {
        if (error) reject(error);
        else resolve(uploadStream.id);
      });
    });
    const imageUrl_gridfs = `gridfs://productImages/${uploadStream.id}`;
    await consolidatedProductsCollection.updateOne(
      { "variants.storeProductId": product.id },
      {
        $set: {
          "variants.$[variant].imageUrl": imageUrl_gridfs
        }
      },
      {
        arrayFilters: [{ "variant.storeProductId": product.id }]
      }
    );
    (0, import_utilities.log)(import_utilities.colour.blue, `  Image uploaded: ${product.id} -> GridFS ${uploadStream.id}`);
    return true;
  } catch (err) {
    (0, import_utilities.logError)(`Image upload error: ${err.message}`);
    return false;
  }
}
function normalizeProductName(name, size) {
  let normalized = name.toLowerCase().replace(/[^a-z0-9\s]/g, " ").replace(/\s+/g, "_").trim();
  if (size) {
    const normalizedSize = size.toLowerCase().replace(/[^a-z0-9]/g, "").trim();
    normalized += "_" + normalizedSize;
  }
  return normalized;
}
async function closeMongoDB() {
  if (client) {
    await client.close();
    (0, import_utilities.log)(import_utilities.colour.blue, "MongoDB connection closed");
  }
}

// src/consolidated-products-mongodb.ts
var import_utilities2 = __toESM(require_utilities());
import { MongoClient as MongoClient2, ObjectId as ObjectId2 } from "./node_modules/mongodb/lib/index.js";
import * as dotenv2 from "./node_modules/dotenv/lib/main.js";
dotenv2.config();
var client2;
var db2;
var consolidatedProductsCollection2;
var brandsCollection2;
var categoryHierarchyCollection2;
async function initializeConsolidatedProductsMongoDB() {
  const connectionString = process.env.MONGODB_CONNECTION_STRING;
  const databaseName = process.env.MONGODB_DATABASE_NAME || "nz-supermarket-scraper";
  if (!connectionString) {
    throw Error("MONGODB_CONNECTION_STRING not set in env");
  }
  try {
    client2 = new MongoClient2(connectionString);
    await client2.connect();
    db2 = client2.db(databaseName);
    consolidatedProductsCollection2 = db2.collection("consolidatedProducts");
    brandsCollection2 = db2.collection("brands");
    categoryHierarchyCollection2 = db2.collection("categoryHierarchy");
    (0, import_utilities2.log)(import_utilities2.colour.green, "\u2705 Consolidated products MongoDB initialized");
    await ensureBasicCategories();
  } catch (error) {
    (0, import_utilities2.logError)(`Failed to initialize consolidated products MongoDB: ${error.message}`);
    throw error;
  }
}
async function ensureBasicCategories() {
  try {
    const existingCategories = await categoryHierarchyCollection2.countDocuments();
    if (existingCategories === 0) {
      const mainCategories = [
        { name: "Fresh Foods", parentId: null, level: 0, sortOrder: 1 },
        { name: "Chilled & Frozen", parentId: null, level: 0, sortOrder: 2 },
        { name: "Pantry & Dry Goods", parentId: null, level: 0, sortOrder: 3 },
        { name: "Beverages", parentId: null, level: 0, sortOrder: 4 },
        { name: "Health & Household", parentId: null, level: 0, sortOrder: 5 }
      ];
      const insertResult = await categoryHierarchyCollection2.insertMany(mainCategories);
      (0, import_utilities2.log)(import_utilities2.colour.blue, `\u2705 Created ${insertResult.insertedCount} main categories`);
      const freshFoodsId = Object.values(insertResult.insertedIds).find(async (id) => {
        const cat = await categoryHierarchyCollection2.findOne({ _id: id });
        return cat?.name === "Fresh Foods";
      });
      (0, import_utilities2.log)(import_utilities2.colour.blue, "\u2705 Basic category structure created");
    }
  } catch (error) {
    (0, import_utilities2.logError)(`Failed to ensure basic categories: ${error.message}`);
  }
}
async function processConsolidatedProductMongoDB(scrapedProduct) {
  if (!consolidatedProductsCollection2) {
    (0, import_utilities2.logError)("Consolidated products collection not initialized");
    return null;
  }
  try {
    const normalizedName = normalizeProductName2(scrapedProduct.name, scrapedProduct.size);
    const now = /* @__PURE__ */ new Date();
    const existingProduct = await consolidatedProductsCollection2.findOne({
      "variants.storeProductId": scrapedProduct.id
    });
    if (existingProduct) {
      await consolidatedProductsCollection2.updateOne(
        { _id: existingProduct._id },
        {
          $set: {
            "variants.$[variant].lastSeen": now,
            "variants.$[variant].storeUnitPrice": scrapedProduct.unitPrice,
            "variants.$[variant].storeUnitName": scrapedProduct.unitName,
            updatedAt: now
          }
        },
        {
          arrayFilters: [{ "variant.storeProductId": scrapedProduct.id }]
        }
      );
      (0, import_utilities2.log)(import_utilities2.colour.cyan, `  Updated consolidated product: ${scrapedProduct.name.slice(0, 40)}`);
      return existingProduct._id.toString();
    } else {
      const newProduct = {
        normalizedName,
        displayName: scrapedProduct.name,
        primarySize: scrapedProduct.size,
        categoryId: null,
        // TODO: Implement category mapping
        brandId: null,
        // TODO: Implement brand extraction
        matchConfidence: 100,
        manualMatch: false,
        variants: [{
          storeProductId: scrapedProduct.id,
          storeId: "woolworths",
          // Store identifier
          storeName: scrapedProduct.name,
          storeSize: scrapedProduct.size,
          storeUnitPrice: scrapedProduct.unitPrice,
          storeUnitName: scrapedProduct.unitName,
          lastSeen: now,
          isActive: true,
          imageUrl: null
        }],
        sizeVariants: scrapedProduct.size ? [{
          sizeName: scrapedProduct.size,
          sizeWeightGrams: null,
          sizeVolumeMl: null,
          isPrimarySize: true
        }] : [],
        currentPrices: [{
          storeId: "woolworths",
          price: scrapedProduct.currentPrice,
          isSpecial: false,
          wasAvailable: true,
          recordedAt: now
        }],
        createdAt: now,
        updatedAt: now
      };
      const insertResult = await consolidatedProductsCollection2.insertOne(newProduct);
      (0, import_utilities2.log)(import_utilities2.colour.cyan, `  Created consolidated product: ${scrapedProduct.name.slice(0, 40)}`);
      return insertResult.insertedId.toString();
    }
  } catch (error) {
    (0, import_utilities2.logError)(`Failed to process consolidated product: ${error.message}`);
    return null;
  }
}
function normalizeProductName2(name, size) {
  let normalized = name.toLowerCase().replace(/[^a-z0-9\s]/g, " ").replace(/\s+/g, "_").trim();
  if (size) {
    const normalizedSize = size.toLowerCase().replace(/[^a-z0-9]/g, "").trim();
    normalized += "_" + normalizedSize;
  }
  return normalized;
}
async function closeConsolidatedProductsMongoDB() {
  try {
    if (client2) {
      await client2.close();
    }
    (0, import_utilities2.log)(import_utilities2.colour.blue, "\u2705 Consolidated products MongoDB connections closed");
  } catch (error) {
    (0, import_utilities2.logError)(`Failed to close MongoDB connections: ${error.message}`);
  }
}

// src/index.ts
var import_product_overrides = __toESM(require_product_overrides());
var import_utilities3 = __toESM(require_utilities());
dotenv3.config();
dotenv3.config({ path: `.env.local`, override: true });
var pageLoadDelaySeconds = 7;
var productLogDelayMilliSeconds = 20;
var startTime = Date.now();
var categorisedUrls = loadUrlsFile();
var databaseMode = false;
var uploadImagesMode = false;
var headlessMode = true;
categorisedUrls = await handleArguments(categorisedUrls);
if (databaseMode) {
  await establishMongoDB();
  await initializeConsolidatedProductsMongoDB();
}
var browser;
var page;
browser = await establishPlaywrightPage(headlessMode);
await selectStoreByLocationName();
await scrapeAllPageURLs();
await browser.close();
if (databaseMode) {
  await closeConsolidatedProductsMongoDB();
  await closeMongoDB();
}
(0, import_utilities3.log)(
  import_utilities3.colour.sky,
  `
All Pages Completed = Total Time Elapsed ${(0, import_utilities3.getTimeElapsedSince)(startTime)} 
`
);
function loadUrlsFile(filePath = "src/urls.txt") {
  const rawLinesFromFile = (0, import_utilities3.readLinesFromTextFile)(filePath);
  let categorisedUrls2 = [];
  rawLinesFromFile.map((line) => {
    let parsedUrls = parseAndCategoriseURL(line);
    if (parsedUrls !== void 0) {
      categorisedUrls2 = [...categorisedUrls2, ...parsedUrls];
    }
  });
  return categorisedUrls2;
}
async function scrapeAllPageURLs() {
  (0, import_utilities3.log)(
    import_utilities3.colour.yellow,
    `${categorisedUrls.length} pages to be scraped`.padEnd(35) + `${pageLoadDelaySeconds}s delay between scrapes`.padEnd(35) + (databaseMode ? "(Database Mode)" : "(Dry Run Mode)")
  );
  for (let i = 0; i < categorisedUrls.length; i++) {
    const categorisedUrl = categorisedUrls[i];
    let url = categorisedUrls[i].url;
    const shortUrl = url.replace("https://", "");
    (0, import_utilities3.log)(
      import_utilities3.colour.yellow,
      `
[${i + 1}/${categorisedUrls.length}] ${shortUrl}`
    );
    try {
      await page.goto(url);
      for (let pageDown = 0; pageDown < 5; pageDown++) {
        const timeBetweenPgDowns = Math.random() * 1e3 + 500;
        await page.waitForTimeout(timeBetweenPgDowns);
        await page.keyboard.press("PageDown");
      }
      await page.setDefaultTimeout(3e4);
      await page.waitForSelector("product-price h3");
      const html = await page.innerHTML("product-grid");
      const $ = cheerio.load(html);
      const allProductEntries = $("cdx-card product-stamp-grid div.product-entry");
      const advertisementEntries = $("div.carousel-track div cdx-card product-stamp-grid div.product-entry");
      const adHrefs = advertisementEntries.map((index, element) => {
        return $(element).find("a").first().attr("href");
      }).toArray();
      const productEntries = allProductEntries.filter((index, element) => {
        const productHref = $(element).find("a").first().attr("href");
        return !adHrefs.includes(productHref);
      });
      (0, import_utilities3.log)(
        import_utilities3.colour.yellow,
        `${productEntries.length} product entries found`.padEnd(35) + `Time Elapsed: ${(0, import_utilities3.getTimeElapsedSince)(startTime)}`.padEnd(35) + `Category: ${_.startCase(categorisedUrl.categories.join(" - "))}`
      );
      if (!databaseMode) (0, import_utilities3.logTableHeader)();
      let perPageLogStats = {
        newProducts: 0,
        priceChanged: 0,
        infoUpdated: 0,
        alreadyUpToDate: 0
      };
      perPageLogStats = await processFoundProductEntries(categorisedUrl, productEntries, perPageLogStats);
      if (databaseMode) {
        (0, import_utilities3.log)(
          import_utilities3.colour.blue,
          `Supabase: ${perPageLogStats.newProducts} new products, ${perPageLogStats.priceChanged} updated prices, ${perPageLogStats.infoUpdated} updated info, ${perPageLogStats.alreadyUpToDate} already up-to-date`
        );
      }
      await setTimeout(pageLoadDelaySeconds * 1e3);
    } catch (error) {
      if (typeof error === "string") {
        if (error.includes("NS_ERROR_CONNECTION_REFUSED")) {
          (0, import_utilities3.logError)("Connection Failed - Check Firewall\n" + error);
          return;
        }
      }
      (0, import_utilities3.logError)(
        "Page Timeout after 30 seconds - Skipping this page\n" + error
      );
    }
  }
}
async function processFoundProductEntries(categorisedUrl, productEntries, perPageLogStats) {
  for (let i = 0; i < productEntries.length; i++) {
    const productEntryElement = productEntries[i];
    const product = playwrightElementToProduct(
      productEntryElement,
      categorisedUrl.categories
    );
    if (databaseMode && product !== void 0) {
      const response = await upsertProductToMongoDB(product);
      const consolidatedProductId = await processConsolidatedProductMongoDB(product);
      switch (response) {
        case 3 /* AlreadyUpToDate */:
          perPageLogStats.alreadyUpToDate++;
          break;
        case 2 /* InfoChanged */:
          perPageLogStats.infoUpdated++;
          break;
        case 0 /* NewProduct */:
          perPageLogStats.newProducts++;
          break;
        case 1 /* PriceChanged */:
          perPageLogStats.priceChanged++;
          break;
        default:
          break;
      }
      if (uploadImagesMode) {
        const imageUrlBase = "https://assets.woolworths.com.au/images/2010/";
        const imageUrlExtensionAndQueryParams = ".jpg?impolicy=wowcdxwbjbx&w=900&h=900";
        const imageUrl = imageUrlBase + product.id + imageUrlExtensionAndQueryParams;
        await uploadImageToMongoDB(imageUrl, product);
      }
    } else if (!databaseMode && product !== void 0) {
      (0, import_utilities3.logProductRow)(product);
    }
    await setTimeout(productLogDelayMilliSeconds);
  }
  return perPageLogStats;
}
function handleArguments(categorisedUrls2) {
  if (process.argv.length > 2) {
    const userArgs = process.argv.slice(2, process.argv.length);
    let potentialUrl = "";
    userArgs.forEach(async (arg) => {
      if (arg === "db") databaseMode = true;
      else if (arg === "images") uploadImagesMode = true;
      else if (arg === "headless") headlessMode = true;
      else if (arg === "headed") headlessMode = false;
      else if (arg.includes(".co.nz")) potentialUrl += arg;
      else if (arg === "reverse") categorisedUrls2 = categorisedUrls2.reverse();
    });
    const parsedUrl = parseAndCategoriseURL(potentialUrl);
    if (parsedUrl !== void 0) categorisedUrls2 = [parsedUrl];
  }
  return categorisedUrls2;
}
async function establishPlaywrightPage(headless = true) {
  (0, import_utilities3.log)(
    import_utilities3.colour.yellow,
    "Launching Browser.. " + (process.argv.length > 2 ? "(" + (process.argv.length - 2) + " arguments found)" : "")
  );
  browser = await playwright.firefox.launch({
    headless
  });
  page = await browser.newPage();
  await routePlaywrightExclusions();
  return browser;
}
async function selectStoreByLocationName(locationName = "") {
  if (locationName === "") {
    if (process.env.STORE_NAME) locationName = process.env.STORE_NAME;
    else return;
  }
  (0, import_utilities3.log)(import_utilities3.colour.yellow, "Selecting Store Location..");
  try {
    await page.setDefaultTimeout(12e3);
    await page.goto("https://www.woolworths.co.nz/bookatimeslot", {
      waitUntil: "domcontentloaded"
    });
    await page.waitForSelector("fieldset div div p button");
  } catch (error) {
    (0, import_utilities3.logError)("Location selection page timed out - Using default location instead");
    return;
  }
  const oldLocation = await page.locator("fieldset div div p strong").innerText();
  await page.locator("fieldset div div p button").click();
  await page.waitForSelector("form-suburb-autocomplete form-input input");
  try {
    await page.locator("form-suburb-autocomplete form-input input").type(locationName);
    await page.waitForTimeout(1500);
    await page.keyboard.press("ArrowDown");
    await page.waitForTimeout(300);
    await page.keyboard.press("Enter");
    await page.waitForTimeout(1e3);
    await page.getByText("Save and Continue Shopping").click();
    (0, import_utilities3.log)(
      import_utilities3.colour.yellow,
      "Changed Location from " + oldLocation + " to " + locationName + "\n"
    );
    await page.waitForTimeout(2e3);
  } catch {
    (0, import_utilities3.logError)(
      `Store Location:${locationName} not found. Using default instead.`
    );
  }
}
function playwrightElementToProduct(element, categories) {
  const $ = cheerio.load(element);
  let idNameSizeH3 = $(element).find("h3").filter((i, element2) => {
    if ($(element2).attr("id")?.includes("-title")) {
      return true;
    } else return false;
  });
  let product = {
    // ID
    // -------
    // Extract product ID from h3 id attribute, and remove non-numbers
    id: idNameSizeH3.attr("id")?.replace(/\D/g, ""),
    // Source Site - set where the source of information came from
    sourceSite: "countdown.co.nz",
    // use countdown for consistency with old data
    // Categories
    category: categories,
    // already obtained from url/text file
    // Store today's date
    lastChecked: /* @__PURE__ */ new Date(),
    lastUpdated: /* @__PURE__ */ new Date(),
    // These values will later be overwritten
    name: "",
    priceHistory: [],
    currentPrice: 0
  };
  let rawNameAndSize = idNameSizeH3.text().trim();
  rawNameAndSize = rawNameAndSize.toLowerCase().replace("  ", " ").replace("fresh fruit", "").replace("fresh vegetable", "").trim();
  let tryMatchSize = rawNameAndSize.match(/(tray\s\d+)|(\d+(\.\d+)?(\-\d+\.\d+)?\s?(g|kg|l|ml|pack))\b/g);
  if (!tryMatchSize) {
    product.name = (0, import_utilities3.toTitleCase)(rawNameAndSize);
    product.size = "";
  } else {
    let indexOfSizeSection = rawNameAndSize.indexOf(tryMatchSize[0]);
    product.name = (0, import_utilities3.toTitleCase)(rawNameAndSize.slice(0, indexOfSizeSection)).trim();
    let cleanedSize = rawNameAndSize.slice(indexOfSizeSection).trim();
    if (cleanedSize.match(/\d+l\b/)) {
      cleanedSize = cleanedSize.replace("l", "L");
    }
    cleanedSize.replace("tray", "Tray");
    product.size = cleanedSize;
  }
  const dollarString = $(element).find("product-price div h3 em").text().trim();
  let centString = $(element).find("product-price div h3 span").text().trim();
  if (centString.includes("kg")) product.size = "per kg";
  centString = centString.replace(/\D/g, "");
  product.currentPrice = Number(dollarString + "." + centString);
  const today = /* @__PURE__ */ new Date();
  today.setMinutes(0);
  today.setSeconds(0);
  const todaysDatedPrice = {
    date: today,
    price: product.currentPrice
  };
  product.priceHistory = [todaysDatedPrice];
  const rawUnitPrice = $(element).find("span.cupPrice").text().trim();
  if (rawUnitPrice) {
    const unitPriceString = rawUnitPrice.split("/")[0].replace("$", "").trim();
    let unitPrice = Number.parseFloat(unitPriceString);
    const amountAndUnit = rawUnitPrice.split("/")[1].trim();
    let amount = Number.parseInt(amountAndUnit.match(/\d+/g)?.[0] || "");
    let unit = amountAndUnit.match(/\w+/g)?.[0] || "";
    if (amountAndUnit == "100g") {
      amount = amount * 10;
      unitPrice = unitPrice * 10;
      unit = "kg";
    } else if (amountAndUnit == "100mL") {
      amount = amount * 10;
      unitPrice = unitPrice * 10;
      unit = "L";
    }
    unit = unit.replace("1kg", "kg");
    unit = unit.replace("1L", "L");
    product.unitPrice = unitPrice;
    product.unitName = unit;
  }
  import_product_overrides.productOverrides.forEach((override) => {
    if (override.id === product.id) {
      if (override.size !== void 0) {
        product.size = override.size;
      }
      if (override.category !== void 0) {
        product.category = [override.category];
      }
    }
  });
  if (validateProduct(product)) return product;
  else {
    try {
      (0, import_utilities3.logError)(
        `  Unable to Scrape: ${product.id.padStart(6)} | ${product.name} | $${product.currentPrice}`
      );
    } catch {
      (0, import_utilities3.logError)("  Unable to Scrape ID from product");
    }
    return void 0;
  }
}
function validateProduct(product) {
  try {
    if (product.name.match(/\$\s\d+/)) return false;
    if (product.name.length < 4 || product.name.length > 100) return false;
    if (product.id.length < 2 || product.id.length > 20) return false;
    if (product.currentPrice <= 0 || product.currentPrice === null || product.currentPrice === void 0 || Number.isNaN(product.currentPrice) || product.currentPrice > 999) {
      return false;
    }
    return true;
  } catch (error) {
    return false;
  }
}
function parseAndCategoriseURL(line) {
  let baseCategorisedURL = { url: "", categories: [] };
  let parsedUrls = [];
  let numPagesPerURL = 1;
  if (!line.includes("woolworths.co.nz")) {
    return void 0;
  } else if (line.includes("?search=")) {
    parsedUrls.push({ url: line, categories: [] });
  } else {
    line.split(" ").forEach((section) => {
      if (section.includes("woolworths.co.nz")) {
        baseCategorisedURL.url = section;
        if (!baseCategorisedURL.url.startsWith("http"))
          baseCategorisedURL.url = "https://" + section;
        if (section.includes("?")) {
          baseCategorisedURL.url = line.substring(0, line.indexOf("?"));
        }
        baseCategorisedURL.url += "?page=1";
      } else if (section.startsWith("categories=")) {
        let splitCategories = [section.replace("categories=", "")];
        if (section.includes(","))
          splitCategories = section.replace("categories=", "").split(",");
        baseCategorisedURL.categories = splitCategories;
      } else if (section.startsWith("pages=")) {
        numPagesPerURL = Number.parseInt(section.split("=")[1]);
      }
      if (baseCategorisedURL.categories.length === 0) {
        const baseUrl = baseCategorisedURL.url.split("?")[0];
        let slashSections = baseUrl.split("/");
        baseCategorisedURL.categories = [slashSections[slashSections.length - 1]];
      }
    });
    for (let i = 1; i <= numPagesPerURL; i++) {
      let pagedUrl = {
        url: baseCategorisedURL.url.replace("page=1", "page=" + i),
        categories: baseCategorisedURL.categories
      };
      parsedUrls.push(pagedUrl);
    }
  }
  return parsedUrls;
}
async function routePlaywrightExclusions() {
  let typeExclusions = ["image", "media", "font"];
  let urlExclusions = [
    "googleoptimize.com",
    "gtm.js",
    "visitoridentification.js",
    "js-agent.newrelic.com",
    "cquotient.com",
    "googletagmanager.com",
    "cloudflareinsights.com",
    "dwanalytics",
    "facebook.net",
    "chatWidget",
    "edge.adobedc.net",
    "\u200B/Content/Banners/",
    "go-mpulse.net"
  ];
  await page.route("**/*", async (route) => {
    const req = route.request();
    let excludeThisRequest = false;
    urlExclusions.forEach((excludedURL) => {
      if (req.url().includes(excludedURL)) excludeThisRequest = true;
    });
    typeExclusions.forEach((excludedType) => {
      if (req.resourceType() === excludedType) excludeThisRequest = true;
    });
    if (excludeThisRequest) {
      await route.abort();
    } else {
      await route.continue();
    }
  });
  return;
}
export {
  databaseMode,
  parseAndCategoriseURL,
  playwrightElementToProduct,
  uploadImagesMode
};
//# sourceMappingURL=data:application/json;base64,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

	