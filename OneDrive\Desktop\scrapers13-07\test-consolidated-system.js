#!/usr/bin/env node

/**
 * Test Consolidated Products System
 * This creates sample data to test the consolidated products functionality
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();
dotenv.config({ path: '.env.local', override: true });

const COLORS = {
    reset: '\x1b[0m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    red: '\x1b[31m',
    cyan: '\x1b[36m',
    blue: '\x1b[34m'
};

function log(color, message) {
    console.log(`${color}${message}${COLORS.reset}`);
}

async function createSampleData() {
    log(COLORS.cyan, '\n=== Creating Sample Consolidated Products ===');
    
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;
    
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    try {
        // Create sample consolidated product
        const sampleProduct = {
            normalized_name: 'anchor milk 2l',
            display_name: 'Anchor Milk 2L',
            primary_size: '2L',
            match_confidence: 100,
            manual_match: false
        };
        
        const { data: product, error: productError } = await supabase
            .from('consolidated_products')
            .insert(sampleProduct)
            .select()
            .single();
            
        if (productError) {
            log(COLORS.red, `❌ Failed to create product: ${productError.message}`);
            return;
        }
        
        log(COLORS.green, `✅ Created consolidated product: ${product.display_name}`);
        
        // Get store IDs
        const { data: stores } = await supabase
            .from('stores')
            .select('id, name')
            .in('name', ['woolworths', 'newworld', 'paknsave']);
            
        if (!stores || stores.length === 0) {
            log(COLORS.red, '❌ No stores found');
            return;
        }
        
        // Create product variants for each store
        for (const store of stores) {
            const variant = {
                consolidated_product_id: product.id,
                store_product_id: `${store.name.charAt(0).toUpperCase()}123456`,
                store_id: store.id,
                store_name: `Anchor Milk 2L - ${store.name}`,
                store_size: '2L',
                store_unit_price: store.name === 'woolworths' ? 4.50 : store.name === 'newworld' ? 4.80 : 4.20,
                store_unit_name: 'L',
                last_seen: new Date().toISOString(),
                is_active: true
            };
            
            const { error: variantError } = await supabase
                .from('product_variants')
                .insert(variant);
                
            if (variantError) {
                log(COLORS.red, `❌ Failed to create variant for ${store.name}: ${variantError.message}`);
            } else {
                log(COLORS.green, `✅ Created variant for ${store.name}`);
            }
        }
        
        // Create price data for each store
        for (const store of stores) {
            const prices = [
                { price: store.name === 'woolworths' ? 6.99 : store.name === 'newworld' ? 7.20 : 6.80, days_ago: 0 },
                { price: store.name === 'woolworths' ? 7.20 : store.name === 'newworld' ? 7.40 : 7.00, days_ago: 7 },
                { price: store.name === 'woolworths' ? 7.50 : store.name === 'newworld' ? 7.60 : 7.20, days_ago: 14 }
            ];
            
            for (const priceData of prices) {
                const priceDate = new Date();
                priceDate.setDate(priceDate.getDate() - priceData.days_ago);
                
                const priceRecord = {
                    consolidated_product_id: product.id,
                    store_id: store.id,
                    price: priceData.price,
                    recorded_at: priceDate.toISOString(),
                    is_special: priceData.days_ago === 0 && store.name === 'paknsave', // Special deal at PakNSave
                    was_available: true
                };
                
                const { error: priceError } = await supabase
                    .from('consolidated_prices')
                    .insert(priceRecord);
                    
                if (priceError) {
                    log(COLORS.red, `❌ Failed to create price for ${store.name}: ${priceError.message}`);
                }
            }
        }
        
        log(COLORS.green, '✅ Created price history for all stores');
        
        // Create size variants
        const sizeVariants = [
            { size_name: '1L', size_volume_ml: 1000 },
            { size_name: '2L', size_volume_ml: 2000, is_primary_size: true },
            { size_name: '3L', size_volume_ml: 3000 }
        ];
        
        for (const sizeVariant of sizeVariants) {
            const variant = {
                consolidated_product_id: product.id,
                ...sizeVariant
            };
            
            const { error: sizeError } = await supabase
                .from('product_size_variants')
                .insert(variant);
                
            if (sizeError) {
                log(COLORS.red, `❌ Failed to create size variant ${sizeVariant.size_name}: ${sizeError.message}`);
            }
        }
        
        log(COLORS.green, '✅ Created size variants');
        
        return product.id;
        
    } catch (error) {
        log(COLORS.red, `❌ Error creating sample data: ${error.message}`);
        return null;
    }
}

async function testConsolidatedView(productId) {
    log(COLORS.cyan, '\n=== Testing Consolidated Product View ===');
    
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;
    
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    try {
        const { data, error } = await supabase
            .from('consolidated_product_details')
            .select('*')
            .eq('id', productId)
            .single();
            
        if (error) {
            log(COLORS.red, `❌ Failed to fetch consolidated view: ${error.message}`);
            return;
        }
        
        log(COLORS.green, `✅ Product: ${data.display_name}`);
        log(COLORS.blue, `   Category: ${data.main_category_name} > ${data.category_name}`);
        log(COLORS.blue, `   Primary Size: ${data.primary_size}`);
        log(COLORS.blue, `   Available Sizes: ${data.available_sizes?.join(', ') || 'N/A'}`);
        
        if (data.store_prices && data.store_prices.length > 0) {
            log(COLORS.blue, '   Store Prices:');
            data.store_prices.forEach(store => {
                const specialText = store.is_special ? ' (SPECIAL!)' : '';
                const availableText = store.is_available ? '' : ' (Unavailable)';
                log(COLORS.blue, `     ${store.store_name}: $${store.price}${specialText}${availableText}`);
            });
            
            // Find best price
            const availablePrices = data.store_prices.filter(s => s.is_available);
            if (availablePrices.length > 0) {
                const bestPrice = Math.min(...availablePrices.map(s => s.price));
                const bestStore = availablePrices.find(s => s.price === bestPrice);
                log(COLORS.green, `   🏆 Best Price: $${bestPrice} at ${bestStore.store_name}`);
            }
        }
        
    } catch (error) {
        log(COLORS.red, `❌ Error testing view: ${error.message}`);
    }
}

async function demonstrateAPI() {
    log(COLORS.cyan, '\n=== API Query Examples ===');
    
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;
    
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    try {
        // Search products
        log(COLORS.yellow, '1. Search for "milk" products:');
        const { data: searchResults } = await supabase
            .from('consolidated_product_details')
            .select('display_name, store_prices')
            .textSearch('display_name', 'milk')
            .limit(3);
            
        if (searchResults) {
            searchResults.forEach(product => {
                const priceCount = product.store_prices?.length || 0;
                log(COLORS.blue, `   ${product.display_name} (${priceCount} stores)`);
            });
        }
        
        // Get products by category
        log(COLORS.yellow, '\n2. Products in Fresh Foods category:');
        const { data: categoryResults } = await supabase
            .from('consolidated_product_details')
            .select('display_name, category_name')
            .eq('main_category_name', 'Fresh Foods')
            .limit(3);
            
        if (categoryResults) {
            categoryResults.forEach(product => {
                log(COLORS.blue, `   ${product.display_name} (${product.category_name})`);
            });
        }
        
        // Get latest prices
        log(COLORS.yellow, '\n3. Recent price updates:');
        const { data: recentPrices } = await supabase
            .from('consolidated_prices')
            .select(`
                price,
                recorded_at,
                consolidated_products(display_name),
                stores(name)
            `)
            .order('recorded_at', { ascending: false })
            .limit(5);
            
        if (recentPrices) {
            recentPrices.forEach(price => {
                const date = new Date(price.recorded_at).toLocaleDateString();
                log(COLORS.blue, `   ${price.consolidated_products.display_name}: $${price.price} at ${price.stores.name} (${date})`);
            });
        }
        
    } catch (error) {
        log(COLORS.red, `❌ Error with API queries: ${error.message}`);
    }
}

async function cleanup() {
    log(COLORS.cyan, '\n=== Cleanup ===');
    
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;
    
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    try {
        // Delete test data
        const { error } = await supabase
            .from('consolidated_products')
            .delete()
            .eq('normalized_name', 'anchor milk 2l');
            
        if (error) {
            log(COLORS.red, `❌ Cleanup failed: ${error.message}`);
        } else {
            log(COLORS.green, '✅ Test data cleaned up');
        }
        
    } catch (error) {
        log(COLORS.red, `❌ Cleanup error: ${error.message}`);
    }
}

async function main() {
    log(COLORS.cyan, '🧪 Testing Consolidated Products System');
    
    // Create sample data
    const productId = await createSampleData();
    
    if (productId) {
        // Test the consolidated view
        await testConsolidatedView(productId);
        
        // Demonstrate API queries
        await demonstrateAPI();
        
        // Ask user if they want to keep the data
        log(COLORS.yellow, '\n⚠️  Test data created. It will be automatically cleaned up in 5 seconds...');
        log(COLORS.blue, 'Press Ctrl+C now if you want to keep the test data for inspection.');
        
        // Wait 5 seconds then cleanup
        await new Promise(resolve => setTimeout(resolve, 5000));
        await cleanup();
    }
    
    log(COLORS.green, '\n🎉 Consolidated products system test complete!');
    log(COLORS.blue, '\nThe system is ready for:');
    log(COLORS.blue, '✓ Product matching across stores');
    log(COLORS.blue, '✓ Price comparison functionality');
    log(COLORS.blue, '✓ Unified product cards with multiple store prices');
    log(COLORS.blue, '✓ Size variants and category organization');
}

main().catch(console.error);