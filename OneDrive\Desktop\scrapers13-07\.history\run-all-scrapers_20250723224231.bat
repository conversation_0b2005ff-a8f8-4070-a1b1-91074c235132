@echo off
echo Running all scrapers in %1 mode...
echo.

if "%1"=="" (
    echo Usage: run-all-scrapers.bat [dev^|db^|db-images]
    echo.
    echo   dev       - Dry run mode ^(console output only^)
    echo   db        - Save to database
    echo   db-images - Save to database with image upload
    echo.
    pause
    exit /b 1
)

echo Starting MongoDB check...
docker ps | findstr mongodb >nul
if %ERRORLEVEL% NEQ 0 (
    echo MongoDB container not running. Starting it...
    docker run -d -p 27017:27017 --name mongodb mongo:latest
    timeout /t 5 /nobreak >nul
)

echo.
echo ========================================
echo Starting Woolworths Scraper
echo ========================================
cd /d "%~dp0Woolworths"
if "%1"=="dev" (
    start /B cmd /c "npm run dev > ..\logs\woolworths.log 2>&1"
) else if "%1"=="db" (
    start /B cmd /c "npm run db > ..\logs\woolworths.log 2>&1"
) else if "%1"=="db-images" (
    start /B cmd /c "npm run db-images > ..\logs\woolworths.log 2>&1"
)

timeout /t 2 /nobreak >nul

echo.
echo ========================================
echo Starting New World Scraper
echo ========================================
cd /d "%~dp0new-world\src"
if "%1"=="dev" (
    start /B cmd /c "dotnet run > ..\..\logs\newworld.log 2>&1"
) else if "%1"=="db" (
    start /B cmd /c "dotnet run db > ..\..\logs\newworld.log 2>&1"
) else if "%1"=="db-images" (
    start /B cmd /c "dotnet run db images > ..\..\logs\newworld.log 2>&1"
)

timeout /t 2 /nobreak >nul

echo.
echo ========================================
echo Starting PakNSave Scraper
echo ========================================
cd /d "%~dp0paknsave\src"
if "%1"=="dev" (
    start /B cmd /c "dotnet run > ..\..\logs\paknsave.log 2>&1"
) else if "%1"=="db" (
    start /B cmd /c "dotnet run db > ..\..\logs\paknsave.log 2>&1"
) else if "%1"=="db-images" (
    start /B cmd /c "dotnet run db images > ..\..\logs\paknsave.log 2>&1"
)

echo.
echo All scrapers started!
echo.
echo Log files:
echo   Woolworths: logs\woolworths.log
echo   New World:  logs\newworld.log  
echo   PakNSave:   logs\paknsave.log
echo.
echo Press any key to exit...
pause >nul