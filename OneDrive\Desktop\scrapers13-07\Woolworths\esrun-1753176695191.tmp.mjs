process.argv = [
  process.
    argv[0],
  ...process.argv.slice(3)
];

		import __esrun_url from 'url';

		import { createRequire as __esrun_createRequire } from "module";

		const __esrun_fileUrl = __esrun_url.pathToFileURL("C:\Users\<USER>\OneDrive\Desktop\scrapers13-07\Woolworths\esrun-1753176695191.tmp.mjs");

		const require = __esrun_createRequire(__esrun_fileUrl);
// src/index.ts
import * as dotenv2 from "./node_modules/dotenv/lib/main.js";
import playwright from "./node_modules/playwright/index.mjs";
import * as cheerio from "./node_modules/cheerio/dist/esm/index.js";
import _ from "./node_modules/lodash/lodash.js";
import { setTimeout } from "timers/promises";

// src/supabase.ts
import { createClient } from "./node_modules/@supabase/supabase-js/dist/main/index.js";
import * as dotenv from "./node_modules/dotenv/lib/main.js";

// src/utilities.ts
import { readFileSync } from "fs";
var tableIDWidth = 6;
var tableNameWidth = 60;
var tableSizeWidth = 17;
var colour = {
  red: "\x1B[31m",
  green: "\x1B[32m",
  yellow: "\x1B[33m",
  blue: "\x1B[38;5;117m",
  magenta: "\x1B[35m",
  cyan: "\x1B[36m",
  white: "\x1B[37m",
  crimson: "\x1B[38m",
  grey: "\x1B[90m",
  orange: "\x1B[38;5;214m",
  sky: "\x1B[38;5;153m"
};
function log(colour2, text) {
  const clear = "\x1B[0m";
  console.log(`${colour2}%s${clear}`, text);
}
function logError(text) {
  log(colour.red, text);
}
function logProductRow(product) {
  const unitPriceString = product.unitPrice ? `$${product.unitPrice.toFixed(2)} /${product.unitName}` : ``;
  log(
    getAlternatingRowColour(colour.sky, colour.white),
    `${product.id.padStart(tableIDWidth)} | ${product.name.slice(0, tableNameWidth).padEnd(tableNameWidth)} | ${product.size?.slice(0, tableSizeWidth).padEnd(tableSizeWidth)} | $ ${product.currentPrice.toFixed(2).padStart(4).padEnd(5)} | ` + unitPriceString
  );
}
function logTableHeader() {
  log(
    colour.yellow,
    `${"ID".padStart(tableIDWidth)} | ${"Name".padEnd(tableNameWidth)} | ${"Size".padEnd(tableSizeWidth)} | ${"Price".padEnd(7)} | Unit Price`
  );
  let headerLine = "";
  for (let i = 0; i < 111; i++) {
    headerLine += "-";
  }
  log(colour.yellow, headerLine);
}
var alternatingRowColour = false;
function getAlternatingRowColour(colourA, colourB) {
  alternatingRowColour = alternatingRowColour ? false : true;
  return alternatingRowColour ? colourA : colourB;
}
function readLinesFromTextFile(filename) {
  try {
    const file = readFileSync(filename, "utf-8");
    const result = file.split(/\r?\n/).filter((line) => {
      if (line.trim().length > 0) return true;
      else return false;
    });
    return result;
  } catch (error) {
    throw "Error reading " + filename;
  }
}
function getTimeElapsedSince(startTime2) {
  let elapsedTimeSeconds = (Date.now() - startTime2) / 1e3;
  let elapsedTimeString = Math.floor(elapsedTimeSeconds).toString();
  if (elapsedTimeSeconds >= 60) {
    return Math.floor(elapsedTimeSeconds / 60) + ":" + Math.floor(elapsedTimeSeconds % 60).toString().padStart(2, "0");
  } else return elapsedTimeString + "s";
}
function toTitleCase(str) {
  return str.replace(/\w\S*/g, function(txt) {
    return txt.charAt(0).toUpperCase() + txt.substring(1).toLowerCase();
  });
}

// src/supabase.ts
dotenv.config();
dotenv.config({ path: `.env.local`, override: true });
var supabase;
var storeId;
function establishSupabase() {
  const url = process.env.SUPABASE_URL;
  const key = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;
  if (!url || !key) {
    throw Error("SUPABASE_URL or SUPABASE_ANON_KEY not set in env");
  }
  supabase = createClient(url, key, { auth: { persistSession: false } });
}
async function ensureStoreRow() {
  if (storeId !== void 0) return storeId;
  const { data, error } = await supabase.from("stores").select("id").eq("name", "woolworths").single();
  if (error && error.code !== "PGRST116") {
    throw error;
  }
  if (data?.id) {
    storeId = data.id;
    return storeId;
  }
  const { data: insertData, error: insertErr } = await supabase.from("stores").insert({ name: "woolworths" }).select("id").single();
  if (insertErr) throw insertErr;
  storeId = insertData.id;
  return storeId;
}
async function upsertProductToSupabase(scraped) {
  if (!supabase) throw Error("Supabase client not initialised");
  const sId = await ensureStoreRow();
  const productPayload = {
    id: scraped.id,
    name: scraped.name,
    size: scraped.size,
    unit_price: scraped.unitPrice,
    unit_name: scraped.unitName,
    original_unit_qty: scraped.originalUnitQuantity,
    source_site: scraped.sourceSite,
    last_checked: scraped.lastChecked.toISOString(),
    last_updated: scraped.lastUpdated.toISOString()
  };
  const { error: prodErr } = await supabase.from("products").upsert(productPayload, { onConflict: "id" });
  if (prodErr) {
    logError("Supabase upsert product failed: " + prodErr.message);
    return 4 /* Failed */;
  }
  const pricePayload = {
    product_id: scraped.id,
    store_id: sId,
    price: scraped.currentPrice,
    recorded_at: scraped.lastUpdated.toISOString()
  };
  const { error: priceErr } = await supabase.from("prices").insert(pricePayload);
  if (priceErr) {
    logError("Supabase insert price failed: " + priceErr.message);
    return 4 /* Failed */;
  }
  log(colour.green, `  Upserted: ${scraped.name.slice(0, 45).padEnd(45)} | $${scraped.currentPrice}`);
  return 1 /* PriceChanged */;
}
async function uploadImageToSupabase(imageUrl, product) {
  if (!supabase) throw Error("Supabase client not initialised");
  try {
    const imageResponse = await fetch(imageUrl);
    if (!imageResponse.ok) {
      logError(`Failed to download image from ${imageUrl}: ${imageResponse.statusText}`);
      return false;
    }
    const imageBuffer = await imageResponse.arrayBuffer();
    const imageBlob = new Blob([imageBuffer], { type: "image/jpeg" });
    const filePath = `products/${product.id}.jpg`;
    const { data, error } = await supabase.storage.from("product-images").upload(filePath, imageBlob, {
      contentType: "image/jpeg",
      upsert: true
      // Overwrite if exists
    });
    if (error) {
      logError(`Supabase storage upload failed: ${error.message}`);
      return false;
    }
    const { data: publicUrlData } = supabase.storage.from("product-images").getPublicUrl(filePath);
    log(colour.blue, `  Image uploaded: ${product.id} -> ${publicUrlData.publicUrl}`);
    return true;
  } catch (err) {
    logError(`Image upload error: ${err.message}`);
    return false;
  }
}

// src/product-overrides.ts
var productOverrides = [
  { id: "206889", size: "180g" },
  { id: "196996", size: "300g" },
  { id: "137967", size: "420g" },
  { id: "125856", size: "450g" },
  { id: "189268", size: "1.13kg" },
  { id: "189150", size: "1.2kg" },
  { id: "190454", size: "2.1kg" },
  { id: "189078", size: "1.3kg" },
  { id: "189136", size: "1.2kg" },
  { id: "755237", size: "931g" },
  { id: "755304", size: "1.1kg" },
  { id: "755246", size: "1020g" },
  { id: "755245", size: "1.2kg" },
  { id: "112273", size: "865ml" },
  { id: "269514", size: "584ml" },
  { id: "269515", size: "584ml" },
  { id: "116518", size: "440ml" },
  { id: "151191", size: "570ml" },
  { id: "279904", size: "575ml" },
  { id: "146149", size: "1000ml" },
  { id: "791925", size: "525g" },
  { id: "774216", size: "525g" },
  { id: "784406", size: "525g" },
  { id: "791916", size: "525g" },
  { id: "306624", size: "185g" },
  { id: "156824", size: "180g" },
  { id: "9023", size: "375g" },
  { id: "266962", category: "sweets-lollies" },
  { id: "171524", size: "230ml", category: "baking" },
  { id: "170021", category: "ice-blocks" },
  { id: "71164", category: "sausages" },
  { id: "71174", category: "sausages" },
  { id: "71168", category: "sausages" },
  { id: "71165", category: "sausages" },
  { id: "331560", category: "specialty-bread" },
  { id: "679412", category: "herbal-tea" },
  { id: "790129", category: "herbal-tea" },
  { id: "267492", category: "herbal-tea" },
  { id: "267485", category: "herbal-tea" },
  { id: "413302", category: "herbal-tea" },
  { id: "267488", category: "herbal-tea" },
  { id: "760872", category: "herbal-tea" },
  { id: "681177", category: "herbal-tea" },
  { id: "95091", category: "herbal-tea" },
  { id: "761093", category: "black-tea" },
  { id: "721661", category: "green-tea" },
  { id: "790129", category: "herbal-tea" },
  { id: "267492", category: "herbal-tea" },
  { id: "267485", category: "herbal-tea" },
  { id: "721034", category: "herbal-tea" },
  { id: "413302", category: "herbal-tea" },
  { id: "267488", category: "herbal-tea" },
  { id: "760872", category: "herbal-tea" },
  { id: "681177", category: "herbal-tea" },
  { id: "95091.", category: "herbal-tea" },
  { id: "184090", category: "herbal-tea" },
  { id: "761093", category: "black-tea" },
  { id: "721661", category: "green-tea" },
  { id: "690093", category: "green-tea" },
  { id: "780922", category: "sauces" },
  { id: "780921", category: "sauces" },
  { id: "72618", category: "black-tea" },
  { id: "6053", category: "black-tea" },
  { id: "72617", category: "black-tea" },
  { id: "168068", category: "black-tea" },
  { id: "6052", category: "black-tea" },
  { id: "761436", category: "black-tea" }
];

// src/index.ts
dotenv2.config();
dotenv2.config({ path: `.env.local`, override: true });
var pageLoadDelaySeconds = 7;
var productLogDelayMilliSeconds = 20;
var startTime = Date.now();
var categorisedUrls = loadUrlsFile();
var databaseMode = false;
var uploadImagesMode = false;
var headlessMode = true;
categorisedUrls = await handleArguments(categorisedUrls);
if (databaseMode) establishSupabase();
var browser;
var page;
browser = await establishPlaywrightPage(headlessMode);
await selectStoreByLocationName();
await scrapeAllPageURLs();
browser.close();
log(
  colour.sky,
  `
All Pages Completed = Total Time Elapsed ${getTimeElapsedSince(startTime)} 
`
);
function loadUrlsFile(filePath = "src/urls.txt") {
  const rawLinesFromFile = readLinesFromTextFile(filePath);
  let categorisedUrls2 = [];
  rawLinesFromFile.map((line) => {
    let parsedUrls = parseAndCategoriseURL(line);
    if (parsedUrls !== void 0) {
      categorisedUrls2 = [...categorisedUrls2, ...parsedUrls];
    }
  });
  return categorisedUrls2;
}
async function scrapeAllPageURLs() {
  log(
    colour.yellow,
    `${categorisedUrls.length} pages to be scraped`.padEnd(35) + `${pageLoadDelaySeconds}s delay between scrapes`.padEnd(35) + (databaseMode ? "(Database Mode)" : "(Dry Run Mode)")
  );
  for (let i = 0; i < categorisedUrls.length; i++) {
    const categorisedUrl = categorisedUrls[i];
    let url = categorisedUrls[i].url;
    const shortUrl = url.replace("https://", "");
    log(
      colour.yellow,
      `
[${i + 1}/${categorisedUrls.length}] ${shortUrl}`
    );
    try {
      await page.goto(url);
      for (let pageDown = 0; pageDown < 5; pageDown++) {
        const timeBetweenPgDowns = Math.random() * 1e3 + 500;
        await page.waitForTimeout(timeBetweenPgDowns);
        await page.keyboard.press("PageDown");
      }
      await page.setDefaultTimeout(3e4);
      await page.waitForSelector("product-price h3");
      const html = await page.innerHTML("product-grid");
      const $ = cheerio.load(html);
      const allProductEntries = $("cdx-card product-stamp-grid div.product-entry");
      const advertisementEntries = $("div.carousel-track div cdx-card product-stamp-grid div.product-entry");
      const adHrefs = advertisementEntries.map((index, element) => {
        return $(element).find("a").first().attr("href");
      }).toArray();
      const productEntries = allProductEntries.filter((index, element) => {
        const productHref = $(element).find("a").first().attr("href");
        return !adHrefs.includes(productHref);
      });
      log(
        colour.yellow,
        `${productEntries.length} product entries found`.padEnd(35) + `Time Elapsed: ${getTimeElapsedSince(startTime)}`.padEnd(35) + `Category: ${_.startCase(categorisedUrl.categories.join(" - "))}`
      );
      if (!databaseMode) logTableHeader();
      let perPageLogStats = {
        newProducts: 0,
        priceChanged: 0,
        infoUpdated: 0,
        alreadyUpToDate: 0
      };
      perPageLogStats = await processFoundProductEntries(categorisedUrl, productEntries, perPageLogStats);
      if (databaseMode) {
        log(
          colour.blue,
          `Supabase: ${perPageLogStats.newProducts} new products, ${perPageLogStats.priceChanged} updated prices, ${perPageLogStats.infoUpdated} updated info, ${perPageLogStats.alreadyUpToDate} already up-to-date`
        );
      }
      await setTimeout(pageLoadDelaySeconds * 1e3);
    } catch (error) {
      if (typeof error === "string") {
        if (error.includes("NS_ERROR_CONNECTION_REFUSED")) {
          logError("Connection Failed - Check Firewall\n" + error);
          return;
        }
      }
      logError(
        "Page Timeout after 30 seconds - Skipping this page\n" + error
      );
    }
  }
}
async function processFoundProductEntries(categorisedUrl, productEntries, perPageLogStats) {
  for (let i = 0; i < productEntries.length; i++) {
    const productEntryElement = productEntries[i];
    const product = playwrightElementToProduct(
      productEntryElement,
      categorisedUrl.categories
    );
    if (databaseMode && product !== void 0) {
      const response = await upsertProductToSupabase(product);
      switch (response) {
        case 3 /* AlreadyUpToDate */:
          perPageLogStats.alreadyUpToDate++;
          break;
        case 2 /* InfoChanged */:
          perPageLogStats.infoUpdated++;
          break;
        case 0 /* NewProduct */:
          perPageLogStats.newProducts++;
          break;
        case 1 /* PriceChanged */:
          perPageLogStats.priceChanged++;
          break;
        default:
          break;
      }
      if (uploadImagesMode) {
        const imageUrlBase = "https://assets.woolworths.com.au/images/2010/";
        const imageUrlExtensionAndQueryParams = ".jpg?impolicy=wowcdxwbjbx&w=900&h=900";
        const imageUrl = imageUrlBase + product.id + imageUrlExtensionAndQueryParams;
        await uploadImageToSupabase(imageUrl, product);
      }
    } else if (!databaseMode && product !== void 0) {
      logProductRow(product);
    }
    await setTimeout(productLogDelayMilliSeconds);
  }
  return perPageLogStats;
}
function handleArguments(categorisedUrls2) {
  if (process.argv.length > 2) {
    const userArgs = process.argv.slice(2, process.argv.length);
    let potentialUrl = "";
    userArgs.forEach(async (arg) => {
      if (arg === "db") databaseMode = true;
      else if (arg === "images") uploadImagesMode = true;
      else if (arg === "headless") headlessMode = true;
      else if (arg === "headed") headlessMode = false;
      else if (arg.includes(".co.nz")) potentialUrl += arg;
      else if (arg === "reverse") categorisedUrls2 = categorisedUrls2.reverse();
    });
    const parsedUrl = parseAndCategoriseURL(potentialUrl);
    if (parsedUrl !== void 0) categorisedUrls2 = [parsedUrl];
  }
  return categorisedUrls2;
}
async function establishPlaywrightPage(headless = true) {
  log(
    colour.yellow,
    "Launching Browser.. " + (process.argv.length > 2 ? "(" + (process.argv.length - 2) + " arguments found)" : "")
  );
  browser = await playwright.firefox.launch({
    headless
  });
  page = await browser.newPage();
  await routePlaywrightExclusions();
  return browser;
}
async function selectStoreByLocationName(locationName = "") {
  if (locationName === "") {
    if (process.env.STORE_NAME) locationName = process.env.STORE_NAME;
    else return;
  }
  log(colour.yellow, "Selecting Store Location..");
  try {
    await page.setDefaultTimeout(12e3);
    await page.goto("https://www.woolworths.co.nz/bookatimeslot", {
      waitUntil: "domcontentloaded"
    });
    await page.waitForSelector("fieldset div div p button");
  } catch (error) {
    logError("Location selection page timed out - Using default location instead");
    return;
  }
  const oldLocation = await page.locator("fieldset div div p strong").innerText();
  await page.locator("fieldset div div p button").click();
  await page.waitForSelector("form-suburb-autocomplete form-input input");
  try {
    await page.locator("form-suburb-autocomplete form-input input").type(locationName);
    await page.waitForTimeout(1500);
    await page.keyboard.press("ArrowDown");
    await page.waitForTimeout(300);
    await page.keyboard.press("Enter");
    await page.waitForTimeout(1e3);
    await page.getByText("Save and Continue Shopping").click();
    log(
      colour.yellow,
      "Changed Location from " + oldLocation + " to " + locationName + "\n"
    );
    await page.waitForTimeout(2e3);
  } catch {
    logError(
      `Store Location:${locationName} not found. Using default instead.`
    );
  }
}
function playwrightElementToProduct(element, categories) {
  const $ = cheerio.load(element);
  let idNameSizeH3 = $(element).find("h3").filter((i, element2) => {
    if ($(element2).attr("id")?.includes("-title")) {
      return true;
    } else return false;
  });
  let product = {
    // ID
    // -------
    // Extract product ID from h3 id attribute, and remove non-numbers
    id: idNameSizeH3.attr("id")?.replace(/\D/g, ""),
    // Source Site - set where the source of information came from
    sourceSite: "countdown.co.nz",
    // use countdown for consistency with old data
    // Categories
    category: categories,
    // already obtained from url/text file
    // Store today's date
    lastChecked: /* @__PURE__ */ new Date(),
    lastUpdated: /* @__PURE__ */ new Date(),
    // These values will later be overwritten
    name: "",
    priceHistory: [],
    currentPrice: 0
  };
  let rawNameAndSize = idNameSizeH3.text().trim();
  rawNameAndSize = rawNameAndSize.toLowerCase().replace("  ", " ").replace("fresh fruit", "").replace("fresh vegetable", "").trim();
  let tryMatchSize = rawNameAndSize.match(/(tray\s\d+)|(\d+(\.\d+)?(\-\d+\.\d+)?\s?(g|kg|l|ml|pack))\b/g);
  if (!tryMatchSize) {
    product.name = toTitleCase(rawNameAndSize);
    product.size = "";
  } else {
    let indexOfSizeSection = rawNameAndSize.indexOf(tryMatchSize[0]);
    product.name = toTitleCase(rawNameAndSize.slice(0, indexOfSizeSection)).trim();
    let cleanedSize = rawNameAndSize.slice(indexOfSizeSection).trim();
    if (cleanedSize.match(/\d+l\b/)) {
      cleanedSize = cleanedSize.replace("l", "L");
    }
    cleanedSize.replace("tray", "Tray");
    product.size = cleanedSize;
  }
  const dollarString = $(element).find("product-price div h3 em").text().trim();
  let centString = $(element).find("product-price div h3 span").text().trim();
  if (centString.includes("kg")) product.size = "per kg";
  centString = centString.replace(/\D/g, "");
  product.currentPrice = Number(dollarString + "." + centString);
  const today = /* @__PURE__ */ new Date();
  today.setMinutes(0);
  today.setSeconds(0);
  const todaysDatedPrice = {
    date: today,
    price: product.currentPrice
  };
  product.priceHistory = [todaysDatedPrice];
  const rawUnitPrice = $(element).find("span.cupPrice").text().trim();
  if (rawUnitPrice) {
    const unitPriceString = rawUnitPrice.split("/")[0].replace("$", "").trim();
    let unitPrice = Number.parseFloat(unitPriceString);
    const amountAndUnit = rawUnitPrice.split("/")[1].trim();
    let amount = Number.parseInt(amountAndUnit.match(/\d+/g)?.[0] || "");
    let unit = amountAndUnit.match(/\w+/g)?.[0] || "";
    if (amountAndUnit == "100g") {
      amount = amount * 10;
      unitPrice = unitPrice * 10;
      unit = "kg";
    } else if (amountAndUnit == "100mL") {
      amount = amount * 10;
      unitPrice = unitPrice * 10;
      unit = "L";
    }
    unit = unit.replace("1kg", "kg");
    unit = unit.replace("1L", "L");
    product.unitPrice = unitPrice;
    product.unitName = unit;
  }
  productOverrides.forEach((override) => {
    if (override.id === product.id) {
      if (override.size !== void 0) {
        product.size = override.size;
      }
      if (override.category !== void 0) {
        product.category = [override.category];
      }
    }
  });
  if (validateProduct(product)) return product;
  else {
    try {
      logError(
        `  Unable to Scrape: ${product.id.padStart(6)} | ${product.name} | $${product.currentPrice}`
      );
    } catch {
      logError("  Unable to Scrape ID from product");
    }
    return void 0;
  }
}
function validateProduct(product) {
  try {
    if (product.name.match(/\$\s\d+/)) return false;
    if (product.name.length < 4 || product.name.length > 100) return false;
    if (product.id.length < 2 || product.id.length > 20) return false;
    if (product.currentPrice <= 0 || product.currentPrice === null || product.currentPrice === void 0 || Number.isNaN(product.currentPrice) || product.currentPrice > 999) {
      return false;
    }
    return true;
  } catch (error) {
    return false;
  }
}
function parseAndCategoriseURL(line) {
  let baseCategorisedURL = { url: "", categories: [] };
  let parsedUrls = [];
  let numPagesPerURL = 1;
  if (!line.includes("woolworths.co.nz")) {
    return void 0;
  } else if (line.includes("?search=")) {
    parsedUrls.push({ url: line, categories: [] });
  } else {
    line.split(" ").forEach((section) => {
      if (section.includes("woolworths.co.nz")) {
        baseCategorisedURL.url = section;
        if (!baseCategorisedURL.url.startsWith("http"))
          baseCategorisedURL.url = "https://" + section;
        if (section.includes("?")) {
          baseCategorisedURL.url = line.substring(0, line.indexOf("?"));
        }
        baseCategorisedURL.url += "?page=1&inStockProductsOnly=true";
      } else if (section.startsWith("categories=")) {
        let splitCategories = [section.replace("categories=", "")];
        if (section.includes(","))
          splitCategories = section.replace("categories=", "").split(",");
        baseCategorisedURL.categories = splitCategories;
      } else if (section.startsWith("pages=")) {
        numPagesPerURL = Number.parseInt(section.split("=")[1]);
      }
      if (baseCategorisedURL.categories.length === 0) {
        const baseUrl = baseCategorisedURL.url.split("?")[0];
        let slashSections = baseUrl.split("/");
        baseCategorisedURL.categories = [slashSections[slashSections.length - 1]];
      }
    });
    for (let i = 1; i <= numPagesPerURL; i++) {
      let pagedUrl = {
        url: baseCategorisedURL.url.replace("page=1", "page=" + i),
        categories: baseCategorisedURL.categories
      };
      parsedUrls.push(pagedUrl);
    }
  }
  return parsedUrls;
}
async function routePlaywrightExclusions() {
  let typeExclusions = ["image", "media", "font"];
  let urlExclusions = [
    "googleoptimize.com",
    "gtm.js",
    "visitoridentification.js",
    "js-agent.newrelic.com",
    "cquotient.com",
    "googletagmanager.com",
    "cloudflareinsights.com",
    "dwanalytics",
    "facebook.net",
    "chatWidget",
    "edge.adobedc.net",
    "\u200B/Content/Banners/",
    "go-mpulse.net"
  ];
  await page.route("**/*", async (route) => {
    const req = route.request();
    let excludeThisRequest = false;
    urlExclusions.forEach((excludedURL) => {
      if (req.url().includes(excludedURL)) excludeThisRequest = true;
    });
    typeExclusions.forEach((excludedType) => {
      if (req.resourceType() === excludedType) excludeThisRequest = true;
    });
    if (excludeThisRequest) {
      await route.abort();
    } else {
      await route.continue();
    }
  });
  return;
}
export {
  databaseMode,
  parseAndCategoriseURL,
  playwrightElementToProduct,
  uploadImagesMode
};
//# sourceMappingURL=data:application/json;base64,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

	