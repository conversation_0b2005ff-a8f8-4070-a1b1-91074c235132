#!/usr/bin/env node

/**
 * MongoDB Migration Utility
 * 
 * This script helps migrate data from Supabase PostgreSQL to MongoDB
 * for the NZ Supermarket Price Scraper system.
 */

import { MongoClient, ObjectId } from 'mongodb';
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
import { promises as fs } from 'fs';
import path from 'path';

dotenv.config();

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;
const MONGODB_CONNECTION_STRING = process.env.MONGODB_CONNECTION_STRING || 'mongodb://localhost:27017';
const MONGODB_DATABASE_NAME = process.env.MONGODB_DATABASE_NAME || 'nz-supermarket-scraper';

// Batch size for processing large datasets
const BATCH_SIZE = 1000;

class MongoDBMigration {
    constructor() {
        this.supabase = null;
        this.mongoClient = null;
        this.db = null;
        this.collections = {};
    }

    async initialize() {
        // Initialize Supabase client
        if (!SUPABASE_URL || !SUPABASE_KEY) {
            throw new Error('Supabase credentials not configured. Set SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY in .env');
        }
        this.supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

        // Initialize MongoDB client
        this.mongoClient = new MongoClient(MONGODB_CONNECTION_STRING);
        await this.mongoClient.connect();
        this.db = this.mongoClient.db(MONGODB_DATABASE_NAME);

        // Get collections
        this.collections = {
            stores: this.db.collection('stores'),
            brands: this.db.collection('brands'),
            categories: this.db.collection('categoryHierarchy'),
            consolidatedProducts: this.db.collection('consolidatedProducts'),
            priceHistory: this.db.collection('priceHistory')
        };

        console.log('✅ Migration utility initialized');
    }

    async close() {
        if (this.mongoClient) {
            await this.mongoClient.close();
        }
    }

    /**
     * Export data from Supabase to JSON files
     */
    async exportFromSupabase(outputDir = './migration-data') {
        console.log('📤 Exporting data from Supabase...');

        // Create output directory
        await fs.mkdir(outputDir, { recursive: true });

        // Export stores
        console.log('  Exporting stores...');
        const { data: stores } = await this.supabase
            .from('stores')
            .select('*');
        await fs.writeFile(path.join(outputDir, 'stores.json'), JSON.stringify(stores, null, 2));

        // Export brands
        console.log('  Exporting brands...');
        const { data: brands } = await this.supabase
            .from('brands')
            .select('*');
        await fs.writeFile(path.join(outputDir, 'brands.json'), JSON.stringify(brands, null, 2));

        // Export categories
        console.log('  Exporting categories...');
        const { data: categories } = await this.supabase
            .from('categories')
            .select('*');
        await fs.writeFile(path.join(outputDir, 'categories.json'), JSON.stringify(categories, null, 2));

        // Export products (in batches)
        console.log('  Exporting products...');
        let allProducts = [];
        let from = 0;
        let hasMore = true;

        while (hasMore) {
            const { data: products, error } = await this.supabase
                .from('products')
                .select('*')
                .range(from, from + BATCH_SIZE - 1);

            if (error) throw error;

            if (products && products.length > 0) {
                allProducts = allProducts.concat(products);
                from += BATCH_SIZE;
                console.log(`    Exported ${allProducts.length} products...`);
            } else {
                hasMore = false;
            }
        }
        await fs.writeFile(path.join(outputDir, 'products.json'), JSON.stringify(allProducts, null, 2));

        // Export prices (in batches)
        console.log('  Exporting prices...');
        let allPrices = [];
        from = 0;
        hasMore = true;

        while (hasMore) {
            const { data: prices, error } = await this.supabase
                .from('prices')
                .select('*')
                .range(from, from + BATCH_SIZE - 1);

            if (error) throw error;

            if (prices && prices.length > 0) {
                allPrices = allPrices.concat(prices);
                from += BATCH_SIZE;
                console.log(`    Exported ${allPrices.length} prices...`);
            } else {
                hasMore = false;
            }
        }
        await fs.writeFile(path.join(outputDir, 'prices.json'), JSON.stringify(allPrices, null, 2));

        console.log(`✅ Data exported to ${outputDir}`);
    }

    /**
     * Import data from JSON files to MongoDB
     */
    async importToMongoDB(inputDir = './migration-data') {
        console.log('📥 Importing data to MongoDB...');

        // Import stores
        console.log('  Importing stores...');
        const storesData = JSON.parse(await fs.readFile(path.join(inputDir, 'stores.json'), 'utf8'));
        const storeIdMap = new Map(); // Map old IDs to new ObjectIds

        for (const store of storesData) {
            const mongoStore = {
                storeId: store.name.toLowerCase(),
                name: store.name,
                logoUrl: store.logo_url,
                createdAt: new Date(),
                updatedAt: new Date()
            };
            const result = await this.collections.stores.insertOne(mongoStore);
            storeIdMap.set(store.id, result.insertedId);
        }

        // Import brands
        console.log('  Importing brands...');
        const brandsData = JSON.parse(await fs.readFile(path.join(inputDir, 'brands.json'), 'utf8'));
        const brandIdMap = new Map();

        for (const brand of brandsData) {
            const mongoBrand = {
                name: brand.name,
                normalizedName: brand.name.toLowerCase().replace(/[^a-z0-9]/g, '_'),
                createdAt: new Date()
            };
            const result = await this.collections.brands.insertOne(mongoBrand);
            brandIdMap.set(brand.id, result.insertedId);
        }

        // Import categories as hierarchy
        console.log('  Importing categories...');
        const categoriesData = JSON.parse(await fs.readFile(path.join(inputDir, 'categories.json'), 'utf8'));
        const categoryIdMap = new Map();

        // First pass: create basic categories
        for (const category of categoriesData) {
            const mongoCategory = {
                name: category.name,
                parentId: null, // Will be set in second pass if needed
                level: 0, // Default to main category
                sortOrder: category.id,
                createdAt: new Date()
            };
            const result = await this.collections.categories.insertOne(mongoCategory);
            categoryIdMap.set(category.id, result.insertedId);
        }

        // Import products and create consolidated products
        console.log('  Importing products and creating consolidated products...');
        const productsData = JSON.parse(await fs.readFile(path.join(inputDir, 'products.json'), 'utf8'));
        const productIdMap = new Map();

        // Group products by normalized name for consolidation
        const productGroups = new Map();

        for (const product of productsData) {
            const normalizedName = this.normalizeProductName(product.name, product.size);
            
            if (!productGroups.has(normalizedName)) {
                productGroups.set(normalizedName, []);
            }
            productGroups.get(normalizedName).push(product);
        }

        let consolidatedCount = 0;
        for (const [normalizedName, products] of productGroups) {
            const firstProduct = products[0];
            
            // Create consolidated product
            const consolidatedProduct = {
                normalizedName,
                displayName: firstProduct.name,
                primarySize: firstProduct.size,
                categoryId: categoryIdMap.get(firstProduct.category_id) || null,
                brandId: brandIdMap.get(firstProduct.brand_id) || null,
                matchConfidence: 100,
                manualMatch: false,
                variants: [],
                sizeVariants: firstProduct.size ? [{
                    sizeName: firstProduct.size,
                    sizeWeightGrams: null,
                    sizeVolumeMl: null,
                    isPrimarySize: true
                }] : [],
                currentPrices: [],
                createdAt: new Date(firstProduct.created_at || new Date()),
                updatedAt: new Date(firstProduct.last_updated || new Date())
            };

            // Add variants for each product
            for (const product of products) {
                const storeId = this.getStoreIdFromProductId(product.id);
                const mongoStoreId = storeIdMap.get(storeId);

                if (mongoStoreId) {
                    consolidatedProduct.variants.push({
                        storeProductId: product.id,
                        storeId: mongoStoreId,
                        storeName: product.name,
                        storeSize: product.size,
                        storeUnitPrice: product.unit_price,
                        storeUnitName: product.unit_name,
                        lastSeen: new Date(product.last_checked || new Date()),
                        isActive: true,
                        imageUrl: null
                    });
                }
            }

            const result = await this.collections.consolidatedProducts.insertOne(consolidatedProduct);
            
            // Map all product IDs to this consolidated product
            for (const product of products) {
                productIdMap.set(product.id, result.insertedId);
            }

            consolidatedCount++;
            if (consolidatedCount % 100 === 0) {
                console.log(`    Created ${consolidatedCount} consolidated products...`);
            }
        }

        // Import price history
        console.log('  Importing price history...');
        const pricesData = JSON.parse(await fs.readFile(path.join(inputDir, 'prices.json'), 'utf8'));
        const priceHistoryBatch = [];

        for (const price of pricesData) {
            const consolidatedProductId = productIdMap.get(price.product_id);
            const storeId = storeIdMap.get(price.store_id);
            const recordedAt = new Date(price.recorded_at);

            if (consolidatedProductId && storeId) {
                priceHistoryBatch.push({
                    consolidatedProductId,
                    storeId,
                    price: price.price,
                    isSpecial: false,
                    wasAvailable: true,
                    recordedAt,
                    year: recordedAt.getFullYear(),
                    month: recordedAt.getMonth() + 1
                });

                // Insert in batches
                if (priceHistoryBatch.length >= BATCH_SIZE) {
                    await this.collections.priceHistory.insertMany(priceHistoryBatch);
                    console.log(`    Imported ${priceHistoryBatch.length} price records...`);
                    priceHistoryBatch.length = 0;
                }
            }
        }

        // Insert remaining price history
        if (priceHistoryBatch.length > 0) {
            await this.collections.priceHistory.insertMany(priceHistoryBatch);
        }

        console.log('✅ Data imported to MongoDB');
    }

    /**
     * Create indexes for optimal performance
     */
    async createIndexes() {
        console.log('🔍 Creating database indexes...');

        // Consolidated products indexes
        await this.collections.consolidatedProducts.createIndex({
            "displayName": "text",
            "normalizedName": "text",
            "variants.storeName": "text"
        });
        await this.collections.consolidatedProducts.createIndex({ "categoryId": 1 });
        await this.collections.consolidatedProducts.createIndex({ "brandId": 1 });
        await this.collections.consolidatedProducts.createIndex({ 
            "variants.storeProductId": 1, 
            "variants.storeId": 1 
        });

        // Price history indexes
        await this.collections.priceHistory.createIndex({ 
            "consolidatedProductId": 1, 
            "recordedAt": -1 
        });
        await this.collections.priceHistory.createIndex({ "year": 1, "month": 1 });

        // Category hierarchy indexes
        await this.collections.categories.createIndex({ "parentId": 1, "sortOrder": 1 });

        console.log('✅ Database indexes created');
    }

    /**
     * Helper methods
     */
    normalizeProductName(name, size) {
        let normalized = name.toLowerCase()
            .replace(/[^a-z0-9\s]/g, ' ')
            .replace(/\s+/g, '_')
            .trim();
        
        if (size) {
            const normalizedSize = size.toLowerCase()
                .replace(/[^a-z0-9]/g, '')
                .trim();
            normalized += '_' + normalizedSize;
        }
        
        return normalized;
    }

    getStoreIdFromProductId(productId) {
        // Extract store ID from product ID prefix
        if (productId.startsWith('W')) return 1; // Woolworths
        if (productId.startsWith('N')) return 2; // New World  
        if (productId.startsWith('P')) return 3; // PakNSave
        return 1; // Default to Woolworths
    }

    /**
     * Validate migration results
     */
    async validateMigration() {
        console.log('✅ Validating migration...');

        const storeCount = await this.collections.stores.countDocuments();
        const brandCount = await this.collections.brands.countDocuments();
        const categoryCount = await this.collections.categories.countDocuments();
        const productCount = await this.collections.consolidatedProducts.countDocuments();
        const priceCount = await this.collections.priceHistory.countDocuments();

        console.log(`  Stores: ${storeCount}`);
        console.log(`  Brands: ${brandCount}`);
        console.log(`  Categories: ${categoryCount}`);
        console.log(`  Consolidated Products: ${productCount}`);
        console.log(`  Price History Records: ${priceCount}`);

        // Check if consolidation worked properly
        const sampleProduct = await this.collections.consolidatedProducts.findOne({});
        if (sampleProduct) {
            console.log(`  Sample consolidated product has ${sampleProduct.variants.length} variants`);
        }

        console.log('✅ Migration validation complete');
    }
}

// CLI interface
async function main() {
    const args = process.argv.slice(2);
    const command = args[0];

    const migration = new MongoDBMigration();

    try {
        await migration.initialize();

        switch (command) {
            case 'export':
                const exportDir = args[1] || './migration-data';
                await migration.exportFromSupabase(exportDir);
                break;

            case 'import':
                const importDir = args[1] || './migration-data';
                await migration.importToMongoDB(importDir);
                await migration.createIndexes();
                await migration.validateMigration();
                break;

            case 'full':
                const dataDir = args[1] || './migration-data';
                await migration.exportFromSupabase(dataDir);
                await migration.importToMongoDB(dataDir);
                await migration.createIndexes();
                await migration.validateMigration();
                break;

            case 'indexes':
                await migration.createIndexes();
                break;

            case 'validate':
                await migration.validateMigration();
                break;

            default:
                console.log(`
MongoDB Migration Utility

Usage:
  node mongodb-migration.js <command> [options]

Commands:
  export [dir]    Export data from Supabase to JSON files
  import [dir]    Import data from JSON files to MongoDB
  full [dir]      Export from Supabase and import to MongoDB
  indexes         Create database indexes
  validate        Validate migration results

Examples:
  node mongodb-migration.js export ./backup
  node mongodb-migration.js import ./backup
  node mongodb-migration.js full
                `);
                process.exit(1);
        }

    } catch (error) {
        console.error('❌ Migration failed:', error);
        process.exit(1);
    } finally {
        await migration.close();
    }
}

if (import.meta.url === `file://${process.argv[1]}`) {
    main();
}