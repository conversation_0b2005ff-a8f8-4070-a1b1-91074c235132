@echo off
echo 🔧 Comprehensive Scraper Fix Script
echo ===================================

echo.
echo 📦 Step 1: Restoring PakNSave dependencies
cd paknsave\src
dotnet restore
if %ERRORLEVEL% NEQ 0 (
    echo ❌ PakNSave restore failed
) else (
    echo ✅ PakNSave restore successful
)

echo.
echo 🔨 Step 2: Building PakNSave
dotnet build
if %ERRORLEVEL% NEQ 0 (
    echo ❌ PakNSave build failed
) else (
    echo ✅ PakNSave build successful
)

echo.
echo 📦 Step 3: Restoring New World dependencies
cd ..\..\new-world\src
dotnet restore
if %ERRORLEVEL% NEQ 0 (
    echo ❌ New World restore failed
) else (
    echo ✅ New World restore successful
)

echo.
echo 🔨 Step 4: Building New World
dotnet build
if %ERRORLEVEL% NEQ 0 (
    echo ❌ New World build failed
) else (
    echo ✅ New World build successful
)

echo.
echo 📦 Step 5: Installing Woolworths dependencies
cd ..\..\Woolworths
npm install
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Woolworths npm install failed
) else (
    echo ✅ Woolworths npm install successful
)

echo.
echo 🎭 Step 6: Installing Playwright browsers
echo Installing Playwright for Woolworths...
npx playwright install

echo Installing Playwright for PakNSave...
cd ..\paknsave\src
if exist "bin\Debug\net6.0\playwright.ps1" (
    pwsh bin\Debug\net6.0\playwright.ps1 install chromium
) else (
    echo ⚠️ PakNSave needs to be built first to install Playwright
)

echo Installing Playwright for New World...
cd ..\..\new-world\src
if exist "bin\Debug\net6.0\playwright.ps1" (
    pwsh bin\Debug\net6.0\playwright.ps1 install chromium
) else (
    echo ⚠️ New World needs to be built first to install Playwright
)

cd ..\..
echo.
echo 🎉 All scrapers have been fixed!
echo.
echo 🚀 You can now run:
echo   node start-scrapers.js db-images
echo.
pause
