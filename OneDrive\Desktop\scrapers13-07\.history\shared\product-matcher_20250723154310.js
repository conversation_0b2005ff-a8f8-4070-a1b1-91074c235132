/**
 * Product Matching Algorithm for Cross-Store Product Identification
 * 
 * This module provides functions to match products across different supermarket stores
 * by comparing product names, sizes, brands, and other attributes.
 */

import { createClient } from '@supabase/supabase-js';

// Supabase client (should be initialized with your credentials)
let supabase = null;

export function initializeSupabase(url, key) {
    supabase = createClient(url, key);
}

/**
 * Normalize product name for matching
 * Removes brand names, common descriptors, and standardizes format
 */
export function normalizeProductName(name, brandName = '') {
    let normalized = name.toLowerCase().trim();
    
    // Remove brand name if provided
    if (brandName) {
        const brandRegex = new RegExp(brandName.toLowerCase(), 'gi');
        normalized = normalized.replace(brandRegex, '').trim();
    }
    
    // Remove common store-specific descriptors
    const descriptorsToRemove = [
        'countdown', 'woolworths', 'new world', 'paknsave', 'pak n save',
        'select', 'premium', 'value', 'budget', 'signature', 'essentials',
        'organic', 'free range', 'grass fed', 'natural', 'home brand'
    ];
    
    for (const descriptor of descriptorsToRemove) {
        const regex = new RegExp(`\\b${descriptor}\\b`, 'gi');
        normalized = normalized.replace(regex, '').trim();
    }
    
    // Remove extra whitespace and special characters
    normalized = normalized
        .replace(/[^\w\s]/g, ' ')          // Replace special chars with spaces
        .replace(/\s+/g, ' ')             // Collapse multiple spaces
        .replace(/\b(the|and|or|with|in|of|for)\b/g, '') // Remove common words
        .trim();
    
    return normalized;
}

/**
 * Normalize size format for comparison
 */
export function normalizeSize(size) {
    if (!size) return '';
    
    let normalized = size.toLowerCase().trim();
    
    // Standardize units
    normalized = normalized
        .replace(/\bmls?\b/g, 'ml')
        .replace(/\blitres?\b/g, 'l')
        .replace(/\bliters?\b/g, 'l')
        .replace(/\bgrams?\b/g, 'g')
        .replace(/\bkilos?\b/g, 'kg')
        .replace(/\bkilograms?\b/g, 'kg')
        .replace(/\bpacks?\b/g, 'pk')
        .replace(/\beach\b/g, 'ea')
        .replace(/\bper\s+kg\b/g, 'per kg')
        .replace(/\s+/g, '');
        
    return normalized;
}

/**
 * Convert size to comparable numeric value for sorting/grouping
 */
export function sizeToComparableValue(size) {
    if (!size) return 0;
    
    const normalized = normalizeSize(size);
    const match = normalized.match(/^(\d+(?:\.\d+)?)(ml|l|g|kg|pk|ea)?/);
    
    if (!match) return 0;
    
    const value = parseFloat(match[1]);
    const unit = match[2] || '';
    
    // Convert to base units for comparison
    switch (unit) {
        case 'l': return value * 1000; // Convert to ml
        case 'kg': return value * 1000; // Convert to g
        case 'pk':
        case 'ea': return value;
        case 'ml':
        case 'g':
        default: return value;
    }
}

/**
 * Calculate similarity score between two product names (0-100)
 */
export function calculateNameSimilarity(name1, name2) {
    const normalized1 = normalizeProductName(name1);
    const normalized2 = normalizeProductName(name2);
    
    if (normalized1 === normalized2) return 100;
    
    // Use Levenshtein distance
    const distance = levenshteinDistance(normalized1, normalized2);
    const maxLength = Math.max(normalized1.length, normalized2.length);
    
    if (maxLength === 0) return 100;
    
    const similarity = Math.max(0, (1 - distance / maxLength) * 100);
    
    // Boost score for exact word matches
    const words1 = normalized1.split(' ').filter(w => w.length > 2);
    const words2 = normalized2.split(' ').filter(w => w.length > 2);
    const commonWords = words1.filter(w => words2.includes(w));
    
    if (commonWords.length > 0) {
        const wordBoost = (commonWords.length / Math.max(words1.length, words2.length)) * 20;
        return Math.min(100, similarity + wordBoost);
    }
    
    return similarity;
}

/**
 * Calculate Levenshtein distance between two strings
 */
function levenshteinDistance(str1, str2) {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
        matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
        matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
        for (let j = 1; j <= str1.length; j++) {
            if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                matrix[i][j] = matrix[i - 1][j - 1];
            } else {
                matrix[i][j] = Math.min(
                    matrix[i - 1][j - 1] + 1,
                    matrix[i][j - 1] + 1,
                    matrix[i - 1][j] + 1
                );
            }
        }
    }
    
    return matrix[str2.length][str1.length];
}

/**
 * Calculate overall product match score
 */
export function calculateProductMatchScore(product1, product2) {
    const nameScore = calculateNameSimilarity(product1.name, product2.name);
    
    // Size similarity (exact match = 30 points, similar = 15 points)
    let sizeScore = 0;
    if (product1.size && product2.size) {
        const normalizedSize1 = normalizeSize(product1.size);
        const normalizedSize2 = normalizeSize(product2.size);
        
        if (normalizedSize1 === normalizedSize2) {
            sizeScore = 30;
        } else {
            const value1 = sizeToComparableValue(product1.size);
            const value2 = sizeToComparableValue(product2.size);
            const sizeDiff = Math.abs(value1 - value2) / Math.max(value1, value2);
            if (sizeDiff < 0.1) sizeScore = 20; // Very similar sizes
            else if (sizeDiff < 0.3) sizeScore = 10; // Somewhat similar sizes
        }
    }
    
    // Brand similarity (if available)
    let brandScore = 0;
    if (product1.brand && product2.brand) {
        if (product1.brand.toLowerCase() === product2.brand.toLowerCase()) {
            brandScore = 20;
        }
    }
    
    // Calculate weighted total (name is most important)
    const totalScore = (nameScore * 0.5) + (sizeScore * 0.3) + (brandScore * 0.2);
    
    return Math.round(totalScore);
}

/**
 * Find potential matches for a product in the database
 */
export async function findPotentialMatches(product, minScore = 70) {
    if (!supabase) {
        throw new Error('Supabase client not initialized');
    }
    
    // Get normalized name for initial filtering
    const normalizedName = normalizeProductName(product.name);
    const nameWords = normalizedName.split(' ').filter(w => w.length > 2);
    
    // Search for products with similar names using full-text search
    const { data: candidates, error } = await supabase
        .from('consolidated_products')
        .select(`
            id, 
            display_name, 
            normalized_name,
            primary_size,
            brand_id,
            brands(name)
        `)
        .textSearch('normalized_name', nameWords.join(' | '));
    
    if (error) {
        console.error('Error searching for product matches:', error);
        return [];
    }
    
    // Score each candidate
    const matches = [];
    for (const candidate of candidates || []) {
        const candidateProduct = {
            name: candidate.display_name,
            size: candidate.primary_size,
            brand: candidate.brands?.name
        };
        
        const score = calculateProductMatchScore(product, candidateProduct);
        
        if (score >= minScore) {
            matches.push({
                consolidatedProductId: candidate.id,
                displayName: candidate.display_name,
                score: score,
                confidence: score >= 90 ? 'high' : score >= 80 ? 'medium' : 'low'
            });
        }
    }
    
    // Sort by score descending
    return matches.sort((a, b) => b.score - a.score);
}

/**
 * Create or link a product to consolidated products
 */
export async function consolidateProduct(storeProduct, storeId) {
    if (!supabase) {
        throw new Error('Supabase client not initialized');
    }
    
    // Find potential matches
    const matches = await findPotentialMatches(storeProduct, 70);
    
    let consolidatedProductId;
    
    if (matches.length > 0 && matches[0].score >= 85) {
        // High confidence match - use existing consolidated product
        consolidatedProductId = matches[0].consolidatedProductId;
        console.log(`Linked ${storeProduct.name} to existing consolidated product (score: ${matches[0].score})`);
    } else {
        // Create new consolidated product
        const normalizedName = normalizeProductName(storeProduct.name);
        
        // Determine category from store product category
        const categoryId = await mapStoreCategory(storeProduct.category);
        
        const { data: newProduct, error } = await supabase
            .from('consolidated_products')
            .insert({
                normalized_name: normalizedName,
                display_name: storeProduct.name,
                primary_size: storeProduct.size,
                category_id: categoryId,
                match_confidence: matches.length > 0 ? matches[0].score : 100
            })
            .select()
            .single();
            
        if (error) {
            console.error('Error creating consolidated product:', error);
            return null;
        }
        
        consolidatedProductId = newProduct.id;
        console.log(`Created new consolidated product for ${storeProduct.name}`);
    }
    
    // Link store product to consolidated product
    await linkStoreProduct(storeProduct, storeId, consolidatedProductId);
    
    return consolidatedProductId;
}

/**
 * Link store-specific product to consolidated product
 */
async function linkStoreProduct(storeProduct, storeId, consolidatedProductId) {
    const { error } = await supabase
        .from('product_variants')
        .upsert({
            consolidated_product_id: consolidatedProductId,
            store_product_id: storeProduct.id,
            store_id: storeId,
            store_name: storeProduct.name,
            store_size: storeProduct.size,
            store_unit_price: storeProduct.unitPrice,
            store_unit_name: storeProduct.unitName,
            last_seen: new Date().toISOString(),
            is_active: true
        }, {
            onConflict: 'store_product_id,store_id'
        });

    if (error) {
        console.error('Error linking store product:', error);
    }
}

/**
 * Map store categories to unified category hierarchy
 */
async function mapStoreCategory(storeCategories) {
    if (!storeCategories || storeCategories.length === 0) return null;
    
    const categoryMappings = {
        'fruit-veg': 'Fruit & Vegetables',
        'fresh-foods-and-bakery': 'Fresh Foods',
        'meat-poultry': 'Meat & Poultry', 
        'fish-seafood': 'Fish & Seafood',
        'bakery': 'Bakery',
        'fridge-deli': 'Dairy & Deli',
        'chilled-frozen-and-desserts': 'Chilled & Frozen',
        'frozen': 'Frozen Foods',
        'pantry': 'Pantry & Dry Goods',
        'drinks': 'Cold Drinks',
        'hot-and-cold-drinks': 'Beverages',
        'beer-wine': 'Beer, Wine & Cider',
        'beer-wine-and-cider': 'Beer, Wine & Cider',
        'health-body': 'Health & Body',
        'health-and-body': 'Health & Body',
        'household': 'Household & Cleaning',
        'household-and-cleaning': 'Household & Cleaning',
        'baby-child': 'Baby & Toddler',
        'baby-and-toddler': 'Baby & Toddler',
        'pet': 'Pet Supplies',
        'pets': 'Pet Supplies'
    };
    
    // Try to map first category
    const firstCategory = storeCategories[0].toLowerCase().replace(/\s+/g, '-');
    const mappedCategory = categoryMappings[firstCategory];
    
    if (mappedCategory) {
        const { data } = await supabase
            .from('category_hierarchy')
            .select('id')
            .eq('name', mappedCategory)
            .single();
            
        return data?.id;
    }
    
    return null;
}

/**
 * Store consolidated price data
 */
export async function storeConsolidatedPrice(consolidatedProductId, storeId, price, isSpecial = false) {
    if (!supabase) {
        throw new Error('Supabase client not initialized');
    }
    
    const { error } = await supabase
        .from('consolidated_prices')
        .insert({
            consolidated_product_id: consolidatedProductId,
            store_id: storeId,
            price: price,
            is_special: isSpecial,
            was_available: true,
            recorded_at: new Date().toISOString()
        });
        
    if (error) {
        console.error('Error storing consolidated price:', error);
    }
}