C:\Users\<USER>\OneDrive\Desktop\scrapers13-07\new-world\src\NewWorldScraper.csproj : warning NU1603: NewWorldScraper depends on <PERSON><PERSON> (>= 2.1.45) but Dapper 2.1.45 was not found. Dapper 2.1.66 was resolved instead.
C:\Users\<USER>\OneDrive\Desktop\scrapers13-07\new-world\src\NewWorldScraper.csproj : warning NU1603: NewWorldScraper depends on Npgsql (>= 7.0.5) but Npgsql 7.0.5 was not found. Npgsql 7.0.6 was resolved instead.
C:\Users\<USER>\OneDrive\Desktop\scrapers13-07\new-world\src\NewWorldScraper.csproj : warning NU1903: Package 'Npgsql' 7.0.6 has a known high severity vulnerability, https://github.com/advisories/GHSA-x9vc-6hfv-hg8c
C:\Users\<USER>\.nuget\packages\microsoft.bcl.asyncinterfaces\9.0.1\buildTransitive\netcoreapp2.0\Microsoft.Bcl.AsyncInterfaces.targets(4,5): warning : Microsoft.Bcl.AsyncInterfaces 9.0.1 doesn't support net6.0 and has not been tested with it. Consider upgrading your TargetFramework to net8.0 or later. You may also set <SuppressTfmSupportBuildWarnings>true</SuppressTfmSupportBuildWarnings> in the project file to ignore this warning and attempt to run in this unsupported configuration at your own risk. [C:\Users\<USER>\OneDrive\Desktop\scrapers13-07\new-world\src\NewWorldScraper.csproj]
C:\Users\<USER>\OneDrive\Desktop\scrapers13-07\new-world\src\NewWorldScraper.csproj : warning NU1603: NewWorldScraper depends on Dapper (>= 2.1.45) but Dapper 2.1.45 was not found. Dapper 2.1.66 was resolved instead.
C:\Users\<USER>\OneDrive\Desktop\scrapers13-07\new-world\src\NewWorldScraper.csproj : warning NU1603: NewWorldScraper depends on Npgsql (>= 7.0.5) but Npgsql 7.0.5 was not found. Npgsql 7.0.6 was resolved instead.
C:\Users\<USER>\OneDrive\Desktop\scrapers13-07\new-world\src\NewWorldScraper.csproj : warning NU1903: Package 'Npgsql' 7.0.6 has a known high severity vulnerability, https://github.com/advisories/GHSA-x9vc-6hfv-hg8c
__________________________________________________
Project "C:\Users\<USER>\OneDrive\Desktop\scrapers13-07\new-world\src\NewWorldScraper.csproj" (ComputeRunArguments target(s)):

C:\Users\<USER>\.nuget\packages\microsoft.bcl.asyncinterfaces\9.0.1\buildTransitive\netcoreapp2.0\Microsoft.Bcl.AsyncInterfaces.targets(4,5): warning : Microsoft.Bcl.AsyncInterfaces 9.0.1 doesn't support net6.0 and has not been tested with it. Consider upgrading your TargetFramework to net8.0 or later. You may also set <SuppressTfmSupportBuildWarnings>true</SuppressTfmSupportBuildWarnings> in the project file to ignore this warning and attempt to run in this unsupported configuration at your own risk.
Done building project "NewWorldScraper.csproj".
Failed to create indexes: A timeout occurred after 30000ms selecting a server using CompositeServerSelector{ Selectors = WritableServerSelector, LatencyLimitingServerSelector{ AllowedLatencyRange = 00:00:00.0150000 }, OperationsCountServerSelector }. Client view of cluster state is { ClusterId : "1", Type : "Unknown", State : "Disconnected", Servers : [{ ServerId: "{ ClusterId : 1, EndPoint : "Unspecified/localhost:27017" }", EndPoint: "Unspecified/localhost:27017", ReasonChanged: "Heartbeat", State: "Disconnected", ServerVersion: , TopologyVersion: , Type: "Unknown", HeartbeatException: "MongoDB.Driver.MongoConnectionException: An exception occurred while opening a connection to the server.
 ---> System.Net.Internals.SocketExceptionFactory+ExtendedSocketException (10061): No connection could be made because the target machine actively refused it. [::1]:27017
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at MongoDB.Driver.Core.Connections.TcpStreamFactory.Connect(Socket socket, EndPoint endPoint, CancellationToken cancellationToken)
   at MongoDB.Driver.Core.Connections.TcpStreamFactory.CreateStream(EndPoint endPoint, CancellationToken cancellationToken)
   at MongoDB.Driver.Core.Connections.BinaryConnection.OpenHelper(CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at MongoDB.Driver.Core.Connections.BinaryConnection.OpenHelper(CancellationToken cancellationToken)
   at MongoDB.Driver.Core.Connections.BinaryConnection.Open(CancellationToken cancellationToken)
   at MongoDB.Driver.Core.Servers.ServerMonitor.InitializeConnection(CancellationToken cancellationToken)
   at MongoDB.Driver.Core.Servers.ServerMonitor.Heartbeat(CancellationToken cancellationToken)", LastHeartbeatTimestamp: "2025-07-23T22:55:59.5604769Z", LastUpdateTimestamp: "2025-07-23T22:55:59.5604770Z" }] }.
? MongoDB connection established
Invalid number of pages: 20
Invalid number of pages: 20
Invalid number of pages: 20
86 pages to be scraped, with 11s delay between each page scrape.
Using default location
Selected Store: New World Metro Auckland

[1/86] newworld.co.nz/quick-and-easy-meals?
50 Products Found 	Total Time Elapsed: 0:12	Category: quick-and-easy-meals
Failed to ensure store row: A timeout occurred after 30000ms selecting a server using CompositeServerSelector{ Selectors = ReadPreferenceServerSelector{ ReadPreference = { Mode : Primary } }, LatencyLimitingServerSelector{ AllowedLatencyRange = 00:00:00.0150000 }, OperationsCountServerSelector }. Client view of cluster state is { ClusterId : "1", Type : "Unknown", State : "Disconnected", Servers : [{ ServerId: "{ ClusterId : 1, EndPoint : "Unspecified/localhost:27017" }", EndPoint: "Unspecified/localhost:27017", ReasonChanged: "Heartbeat", State: "Disconnected", ServerVersion: , TopologyVersion: , Type: "Unknown", HeartbeatException: "MongoDB.Driver.MongoConnectionException: An exception occurred while opening a connection to the server.
 ---> System.Net.Internals.SocketExceptionFactory+ExtendedSocketException (10061): No connection could be made because the target machine actively refused it. [::1]:27017
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at MongoDB.Driver.Core.Connections.TcpStreamFactory.Connect(Socket socket, EndPoint endPoint, CancellationToken cancellationToken)
   at MongoDB.Driver.Core.Connections.TcpStreamFactory.CreateStream(EndPoint endPoint, CancellationToken cancellationToken)
   at MongoDB.Driver.Core.Connections.BinaryConnection.OpenHelper(CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at MongoDB.Driver.Core.Connections.BinaryConnection.OpenHelper(CancellationToken cancellationToken)
   at MongoDB.Driver.Core.Connections.BinaryConnection.Open(CancellationToken cancellationToken)
   at MongoDB.Driver.Core.Servers.ServerMonitor.InitializeConnection(CancellationToken cancellationToken)
   at MongoDB.Driver.Core.Servers.ServerMonitor.Heartbeat(CancellationToken cancellationToken)", LastHeartbeatTimestamp: "2025-07-23T22:56:44.0174088Z", LastUpdateTimestamp: "2025-07-23T22:56:44.0174089Z" }] }.
Unable to Load Web Page - timed out after 30 seconds

[2/86] newworld.co.nz/quick-and-easy-meals?pg=2
39 Products Found 	Total Time Elapsed: 0:59	Category: quick-and-easy-meals
