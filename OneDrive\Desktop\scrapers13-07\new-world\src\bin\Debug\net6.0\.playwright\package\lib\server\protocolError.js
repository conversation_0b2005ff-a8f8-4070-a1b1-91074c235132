"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ProtocolError = void 0;
exports.isSessionClosedError = isSessionClosedError;
/**
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

class ProtocolError extends Error {
  constructor(sessionClosed, message) {
    super(message);
    this.sessionClosed = void 0;
    this.sessionClosed = sessionClosed || false;
  }
}
exports.ProtocolError = ProtocolError;
function isSessionClosedError(e) {
  return e instanceof ProtocolError && e.sessionClosed;
}