{"format": 1, "restore": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\scrapers13-07\\new-world\\src\\NewWorldScraper.csproj": {}}, "projects": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\scrapers13-07\\new-world\\src\\NewWorldScraper.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\scrapers13-07\\new-world\\src\\NewWorldScraper.csproj", "projectName": "NewWorldScraper", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\scrapers13-07\\new-world\\src\\NewWorldScraper.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\scrapers13-07\\new-world\\src\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\scrapers13-07\\new-world\\src\\NuGet.Config", "C:\\Users\\<USER>\\OneDrive\\Desktop\\scrapers13-07\\new-world\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Dapper": {"target": "Package", "version": "[2.1.45, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[7.0.1, )"}, "Microsoft.Playwright": {"target": "Package", "version": "[1.31.1, )"}, "MongoDB.Driver": {"target": "Package", "version": "[2.23.1, )"}, "MongoDB.Driver.GridFS": {"target": "Package", "version": "[2.23.1, )"}, "Npgsql": {"target": "Package", "version": "[7.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.303\\RuntimeIdentifierGraph.json"}}}}}