# Woolworths New Zealand Scraper

A TypeScript/Node.js web scraper for collecting product data from Woolworths New Zealand (woolworths.co.nz) with advanced cross-store product matching and MongoDB integration.

## 🎯 **Overview**

This scraper collects comprehensive product information including:
- **Product Data**: Names, prices, sizes, unit pricing (per kg, per L, etc.)
- **Categories**: Product categories and subcategories
- **Images**: Product images stored in MongoDB GridFS
- **Cross-Store Matching**: Advanced fuzzy matching with 152 NZ brand mappings
- **Price History**: Historical price tracking for trend analysis

## 🚀 **Quick Start**

### **Prerequisites**
- **Node.js** (v16 or higher) - [Download](https://nodejs.org/)
- **MongoDB** (running on localhost:27017) - Use Docker: `docker run -d -p 27017:27017 --name mongodb mongo:latest`
- **Internet connection** for accessing Woolworths website

### **Installation**

1. **Navigate to Woolworths directory:**
   ```bash
   cd Woolworths
   ```

2. **Install dependencies:**
   ```bash
   npm install
   npx playwright install  # Install browser automation
   ```

3. **Configure environment** (optional):
   ```bash
   # Create .env file with your settings
   echo "STORE_NAME=Auckland Central" > .env
   echo "MONGODB_CONNECTION_STRING=mongodb://localhost:27017/nz-supermarket-scraper" >> .env
   ```

### **Running the Scraper**

#### **Development Mode (Dry Run)**
```bash
npm run dev

# With database storage
npm run db

# With database + image upload
npm run "db images"

# Run tests
npm test
```

## ⚙️ Configuration

### Environment Variables
Create a `.env` file in the Woolworths directory:

```env
# Supabase Configuration (Required for database mode)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Optional Configuration
STORE_NAME=Auckland Central          # Store location name
```

**Setting up Supabase:**
1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project → Settings → API
3. Copy the Project URL and API keys
4. **For image uploads**: Go to Storage → Create bucket → Name: `product-images` → Public bucket: Yes

### URL Configuration
Edit `src/urls.txt` to specify which Woolworths pages to scrape:
```
https://www.woolworths.co.nz/shop/browse/fresh-foods-and-bakery
https://www.woolworths.co.nz/shop/browse/pantry
https://www.woolworths.co.nz/shop/browse/frozen
```

## 🛠️ Commands

| Command | Description |
|---------|-------------|
| `npm run dev` | Dry run mode - displays results in console only |
| `npm run db` | Scrape with database storage |
| `npm run "db images"` | Scrape with database storage + image upload |
| `npm test` | Run Jest test suite |

### Advanced Usage
```bash
# Scrape a single URL instead of urls.txt
npm run db "https://specific-woolworths-url"

# Run with custom arguments
npm run dev headed        # Show browser window
npm run db images headed  # Database + images + visible browser
```

## 📊 Output Examples

### Console Output (Dry Run Mode)
```
    ID | Name                              | Size           | Price  | Unit Price
----------------------------------------------------------------------------------
762844 | Ocean Blue Smoked Salmon Slices   | 100g           | $    9 | $90 /kg
697201 | Clearly Premium Smoked Salmon     | 200g           | $ 13.5 | $67.5 /kg
830035 | Ocean Blue Smoked Salmon Slices   | 180g           | $   12 | $67.7 /kg
```

### Database Storage
Products are stored in Supabase with automatic price history tracking:

```json
{
  "id": "762844",
  "name": "Ocean Blue Smoked Salmon Slices",
  "size": "100g",
  "currentPrice": 9.00,
  "unitPrice": 90.00,
  "unitName": "kg",
  "sourceSite": "woolworths.co.nz",
  "lastUpdated": "2025-01-22T09:00:00Z",
  "priceHistory": [
    {
      "date": "2025-01-15T00:00:00Z",
      "price": 8.50
    },
    {
      "date": "2025-01-22T00:00:00Z", 
      "price": 9.00
    }
  ]
}
```

## 🔧 Features

- **Rate Limited**: 7-second delay between pages to respect website policies
- **Error Handling**: Robust retry mechanisms and error logging
- **Product Overrides**: Manual data corrections via `product-overrides.ts`
- **Price History**: Automatic tracking of price changes over time
- **Unit Price Calculation**: Automatic per-unit pricing calculations
- **Supabase Storage**: Product images saved directly to Supabase Storage
- **Testing**: Comprehensive Jest test suite

## 🏗️ Architecture

```
Woolworths/
├── src/
│   ├── index.ts           # Main scraper entry point
│   ├── supabase.ts        # Database operations
│   ├── utilities.ts       # Helper functions
│   ├── typings.ts         # TypeScript definitions
│   ├── product-overrides.ts # Manual data corrections
│   └── urls.txt           # URLs to scrape
├── tests/
│   └── utilities.test.ts  # Unit tests
├── package.json           # Dependencies and scripts
├── jest.config.js         # Test configuration
└── README.md             # This file
```

## 🚨 Troubleshooting

### Common Issues

**Error: "Playwright browser not found"**
```bash
npx playwright install
```

**Error: "SUPABASE_URL not set"**
- Create `.env` file with Supabase configuration
- Check environment variables are correctly set

**Error: "Supabase storage upload failed"**
- Ensure you've created a `product-images` bucket in Supabase Storage
- Make sure the bucket is set to public (for generating public URLs)
- Check that your Service Role Key has storage permissions

**Error: "Rate limited or blocked"**
- The scraper uses 7-second delays by default
- Avoid running multiple instances simultaneously
- Check your IP isn't blocked by Woolworths

**Network/Timeout Issues**
- Check internet connection
- Verify Woolworths website is accessible
- Try running with fewer concurrent requests

### Development & Debugging

**View Browser Window:**
```bash
npm run dev headed
```

**Test Single Product:**
```bash
npm run dev "https://www.woolworths.co.nz/shop/productdetails/123456"
```

**Check Logs:**
- Console output shows detailed progress
- Error messages include specific failure reasons
- Use `headed` mode to watch scraper in action

## 📝 Contributing

1. Run tests before submitting changes: `npm test`
2. Follow existing code style and patterns
3. Update documentation for new features
4. Test both dry-run and database modes

## ⚖️ Legal & Ethics

- Respects robots.txt and rate limits
- For educational/research purposes
- Users responsible for compliance with terms of service
- No warranty or guarantee provided

## 🔗 Related

This scraper is part of a multi-store New Zealand supermarket price tracking system:
- **New World Scraper** (C#/.NET)
- **PakNSave Scraper** (C#/.NET)
- **Unified Runner**: Use `node ../start-scrapers.js` to run all scrapers

---

*Last updated: January 2025*