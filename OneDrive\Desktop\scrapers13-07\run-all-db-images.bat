@echo off
echo Starting all scrapers in database + images mode...
echo.

echo Checking MongoDB...
docker ps | findstr mongodb >nul
if %ERRORLEVEL% NEQ 0 (
    echo Starting MongoDB container...
    docker run -d -p 27017:27017 --name mongodb mongo:latest
    timeout /t 3 >nul
)

echo.
echo Launching scrapers in separate windows...
echo.

start "Woolworths Scraper" cmd /k "cd /d C:\Users\<USER>\OneDrive\Desktop\scrapers13-07\Woolworths && npm run \"db images\""

timeout /t 2 >nul

start "New World Scraper" cmd /k "cd /d C:\Users\<USER>\OneDrive\Desktop\scrapers13-07\new-world\src && dotnet run db images"

timeout /t 2 >nul

start "PakNSave Scraper" cmd /k "cd /d C:\Users\<USER>\OneDrive\Desktop\scrapers13-07\paknsave\src && dotnet run db images"

echo.
echo All scrapers started!
echo Each scraper is running in its own command prompt window.
echo Close those windows to stop the scrapers.
echo.